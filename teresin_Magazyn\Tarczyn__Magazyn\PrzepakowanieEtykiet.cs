﻿using System;
using System.Linq;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using MySql.Data.MySqlClient;
using System.Text.RegularExpressions;
using System.Threading;
//using System.Data.SqlServerCe;
using System.Collections;



namespace Tarczyn__Magazyn
{
    public partial class PrzepakowanieEtykiet : Form
    {
        ActionMenu myParent = null;

        private Thread Skanowanie;
        Dictionary<string, string> rec = new Dictionary<string, string>();

        bool czy_buffor = false;

        string doc_date = "";
        string magazyn_global = "";

        public string prev1 = "";
        public string prev2 = "";

        string operac_id_global = "";

        int numer_dokumentu = 0;
        int miejsce_id = 0;

        int zmiana_palet = 0;
        int max_docin = 0;
        int max_docout = 0;
        string zam_prod_head = "";
        string kontrah_wew = "";
        string last_docin = "";
        string last_docout = "";

        DataTable GG;


        public PrzepakowanieEtykiet(ActionMenu MyParent)
        {
            InitializeComponent();
            this.myParent = MyParent;
            wms_wybrany.Text = Wlasciwosci.system_id;
            imie_nazwisk_label.Text = Wlasciwosci.imie_nazwisko;

            // jeśli jest połączenie to niech sprawdzi opisy hal i ewentualnie zsynchronizuje
            if (BazaDanychExternal.SprawdzCzyIstniejePolaczenie())
            {
                //    Sprawdz_zgodnosc_opisow();

            }


            string zapytanie = "SELECT last FROM docnumber d WHERE d.name='operacja_id';";

            operac_id_global = (string)BazaDanychExternal.Wyczytaj_Jedna_Wartosc(zapytanie);

            string zapyt = "update docnumber d set last=last+1 WHERE d.name='operacja_id'; ";
            BazaDanychExternal.Wyczytaj_Jedna_Wartosc(zapyt);
            

            this.ZacznijSkanowanie();
        }














        public static string Ile_dokument(string nr_dok)
        {
            string zapytanie = "SELECT count(1) FROM etykiety e left join docout d on e.docout_id=d.id where d.docout_nr='" + nr_dok + "'  and e.system_id='" + Wlasciwosci.system_id_id + "' and d.docout_type='PP-';";
            string aa = (string)BazaDanychExternal.Wyczytaj_Jedna_Wartosc(zapytanie);
            return aa;
        }



        public static string CzyAktywna(string etykieta)
        {
            string zapytanie = "SELECT count(1) FROM etykiety e where active=1 and e.id='" + etykieta + "'   ;";
            string aa = (string)BazaDanychExternal.Wyczytaj_Jedna_Wartosc(zapytanie);
            return aa;
        }



        // sprawdza czy hala regal i poziom są takie jak w bazie









        // do przerobienia na zmianym
        public void Realizuj(string etykieta, string login)
        {


            //--------------------------

                object Tabela = BazaDanychExternal.Wyczytaj_Tabele("select system_id,magazyn,active,miejscep,kod_id,status_id,status_id2,stat,paleta_id,kartony,DATE_FORMAT(dataprod,'%d-%m-%Y'),data_waznosci,ilosc,ts,status,blloc,akcja_id,status_prism,lot,sscc,gtin,przeznaczenie_id,nretykiety,docin_id,docout_id,delivery_id,listcontrol_id from etykiety where id = " + etykieta);
                if (Tabela != null)
                {
                    GG = (DataTable)Tabela;



                    if (GG.Rows[0]["active"].ToString() == "0")
                    {
                        MessageBox.Show("Etykieta : " + etykieta + " jest nieaktywna");
                        return;
                    }



                    

                    long nowa_etykieta1 = 0;
  

                    //BazaDanychExternal.DokonajUpdate("insert into etykiety (magazyn,active,miejscep,kod,stat,paleta,kartony,dataprod,jm,ilosc,doc_internal,doc_type,doc_nr,doc_date,doc_ts,doc_set,doc_ip,doc_kontrah,doc_ref,status, blloc,lot,nretykiety) values ('" + tabela.Rows[0]["magazyn"].ToString() + "',1,'" + tabela.Rows[0]["miejscep"].ToString() + "','" + tabela.Rows[0]["kod"].ToString() + "','" + tabela.Rows[0]["stat"].ToString() + "','0','0','" + tabela.Rows[0]["dataprod"].ToString() + "','" + tabela.Rows[0]["jm"].ToString() + "','" + textBox4.Text + "','zamkniety','PP','" + numer_dokumentu.ToString() + "','" + doc_date + "',NOW()+0,'" + login + "','','5','','" + tabela.Rows[0]["status"].ToString() + "','" + tabela.Rows[0]["blloc"].ToString() + "','" + tabela.Rows[0]["lot"].ToString() + "','" + tabela.Rows[0]["nretykiety"].ToString() + "')");
                    BazaDanychExternal.DokonajUpdate("insert into etykiety(system_id,magazyn,active,miejscep,kod_id,status_id,status_id2,stat,paleta_id,kartony,dataprod,data_waznosci,ilosc,ts,status,blloc,akcja_id,status_prism,lot,sscc,gtin,przeznaczenie_id,nretykiety,docin_id)" +
                                " values(" +
                                sprawdz_czy_null(GG.Rows[0][0].ToString()) + "," +
                                sprawdz_czy_null(GG.Rows[0][1].ToString()) + "," +
                                sprawdz_czy_null(GG.Rows[0][2].ToString()) + "," +
                                sprawdz_czy_null(GG.Rows[0][3].ToString()) + "," +
                                sprawdz_czy_null(GG.Rows[0][4].ToString()) + "," +
                                sprawdz_czy_null(GG.Rows[0][5].ToString()) + "," +
                                sprawdz_czy_null(GG.Rows[0][6].ToString()) + "," +
                                sprawdz_czy_null(GG.Rows[0][7].ToString()) + "," +
                                sprawdz_czy_null(GG.Rows[0][8].ToString()) + "," +
                                sprawdz_czy_null(GG.Rows[0][9].ToString()) + "," +
                                DajDate(sprawdz_czy_null(GG.Rows[0][10].ToString())) + "," + //DATA
                                sprawdz_czy_null(GG.Rows[0][11].ToString()) + "," +
                                sprawdz_czy_null(textBox4.Text.ToString()) + "," +
                                "NOW()," +//NOW()
                                sprawdz_czy_null(GG.Rows[0][14].ToString()) + "," +
                                sprawdz_czy_null(GG.Rows[0][15].ToString()) + "," +
                                sprawdz_czy_null(GG.Rows[0][16].ToString()) + "," +
                                sprawdz_czy_null(GG.Rows[0][17].ToString()) + "," +
                                sprawdz_czy_null(GG.Rows[0][18].ToString()) + "," +
                                sprawdz_czy_null(GG.Rows[0][19].ToString()) + "," +
                                sprawdz_czy_null(GG.Rows[0][20].ToString()) + "," +
                        //sprawdz_czy_null(GG.Rows[0][21].ToString()) + "," +
                                sprawdz_czy_null(GG.Rows[0][21].ToString()) + "," +
                                sprawdz_czy_null(GG.Rows[0]["nretykiety"].ToString()) + "," +
                                last_docin + ")");

                    nowa_etykieta1 = BazaDanychExternal.Command.LastInsertedId;



                    BazaDanychExternal.DokonajUpdate("insert into etykiety(system_id,magazyn,active,miejscep,kod_id,status_id,status_id2,stat,paleta_id,kartony,dataprod,data_waznosci,ilosc,ts,status,blloc,akcja_id,status_prism,lot,sscc,gtin,przeznaczenie_id,nretykiety,docin_id)" +
                                " values(" +
                                sprawdz_czy_null(GG.Rows[0][0].ToString()) + "," +
                                sprawdz_czy_null(GG.Rows[0][1].ToString()) + "," +
                                sprawdz_czy_null(GG.Rows[0][2].ToString()) + "," +
                                sprawdz_czy_null(GG.Rows[0][3].ToString()) + "," +
                                sprawdz_czy_null(GG.Rows[0][4].ToString()) + "," +
                                sprawdz_czy_null(GG.Rows[0][5].ToString()) + "," +
                                sprawdz_czy_null(GG.Rows[0][6].ToString()) + "," +
                                sprawdz_czy_null(GG.Rows[0][7].ToString()) + "," +
                                sprawdz_czy_null(GG.Rows[0][8].ToString()) + "," +
                                sprawdz_czy_null(GG.Rows[0][9].ToString()) + "," +
                                DajDate(sprawdz_czy_null(GG.Rows[0][10].ToString())) + "," + //DATA
                                sprawdz_czy_null(GG.Rows[0][11].ToString()) + "," +
                                sprawdz_czy_null(textBox1.Text.ToString()) + "," +
                                "NOW()," +//NOW()
                                sprawdz_czy_null(GG.Rows[0][14].ToString()) + "," +
                                sprawdz_czy_null(GG.Rows[0][15].ToString()) + "," +
                                sprawdz_czy_null(GG.Rows[0][16].ToString()) + "," +
                                sprawdz_czy_null(GG.Rows[0][17].ToString()) + "," +
                                sprawdz_czy_null(GG.Rows[0][18].ToString()) + "," +
                                sprawdz_czy_null(GG.Rows[0][19].ToString()) + "," +
                                sprawdz_czy_null(GG.Rows[0][20].ToString()) + "," +
                        //sprawdz_czy_null(GG.Rows[0][21].ToString()) + "," +
                                sprawdz_czy_null(GG.Rows[0][21].ToString()) + "," +
                                sprawdz_czy_null(GG.Rows[0]["nretykiety"].ToString()) + "," +
                                last_docin + ")");


                    nowa_etykieta1 = BazaDanychExternal.Command.LastInsertedId;

                    BazaDanychExternal.DokonajUpdate("insert into operacje(etykieta_id, doc_type, doc_nr, imie_nazwisko, typ_operacji,wozek,operac_id,ilosc,system_id) values('" + etykieta + "','PP-','" + numer_dokumentu.ToString() + "','" + Wlasciwosci.imie_nazwisko + "','PP_ET','" + Wlasciwosci.wozek + "','" + operac_id_global + "','1','" + Wlasciwosci.system_id_id + "');");



                    

                    BazaDanychExternal.DokonajUpdate("update etykiety set active=0,docout_id=" + last_docout + " where id=" + etykieta);


                    last_result_text.Text = " " + textBox2.Text + " ilość : " + textBox3.Text + Environment.NewLine + " na " + textBox4.Text + " i " + textBox1.Text;

                    czysc();
                }
        }
        //ok



















            private string DajDate(string gg)
        {

            gg = gg.Replace("'", "");
            //MessageBox.Show(gg);
            if (gg == "null")
            {
                return "null";
            }

            DateTime gh = DateTime.ParseExact(gg,"dd-MM-yyyy",null);
            return "'"+gh.ToString("yyyy-MM-dd")+"'";
                
            
        }




        private string sprawdz_czy_null(string bb)
        {
            if (bb == "")
            {

                return "null";
            }
            else
            {

                try
                {
                    Convert.ToDouble(bb);
                    return bb;
                }
                catch
                {
                    return "'" + bb + "'";
                }
            }

        }








        // zatwierdzanie wczytanej etykiety
        private void zatwierdz_button1_Click(object sender, EventArgs e)
        {
            Skaner.Przewij_Skanowanie();



            if (textBox4.Text != "0" && textBox1.Text != "0" && textBox2.Text != "" && textBox4.Text != "" && textBox1.Text != "")
            {
                if (CzyAktywna(textBox2.Text) != "1")
                {
                    MessageBox.Show("Et " + textBox2.Text + " jest nieaktywna");
                    czysc();
                    ZacznijSkanowanie();
                    return;
                }

                // czy jest z tego dokumentu




                //MessageBox.Show("a ");
                if (czy_buffor != true)
                {
                    kontrah_wew = (string)BazaDanychExternal.Wyczytaj_Jedna_Wartosc("select kontrah_wew_id from systemy where wartosc = " + Wlasciwosci.system_id_id.ToString());
                    DataTable KK;

                    max_docin = Convert.ToInt32(BazaDanychExternal.Wyczytaj_Jedna_Wartosc("select max(doc_nr)  from docin d left join etykiety e on e.docin_id=d.id  where e.system_id = " + Wlasciwosci.system_id_id.ToString()));
                    max_docout = Convert.ToInt32(BazaDanychExternal.Wyczytaj_Jedna_Wartosc("select max(docout_nr)  from docout d left join etykiety e on e.docout_id=d.id  where e.system_id = " + Wlasciwosci.system_id_id.ToString()));


                    if (max_docin > max_docout)
                    {
                        numer_dokumentu = max_docin + 1;
                    }
                    else
                    {
                        numer_dokumentu = max_docout + 1;
                    }



                    BazaDanychExternal.DokonajUpdate("insert into docin(doc_internal,doc_type,doc_nr,doc_date,doc_ts,pracownik_id,kontrah_id,doc_ref,doc_uwagi)" +
                        " values(3,'PP'," + numer_dokumentu.ToString() + ",date(now()),sysdate()," + Wlasciwosci.id_Pracownika + "," + kontrah_wew + ",'','pp_skaner')");
                    last_docin = BazaDanychExternal.Command.LastInsertedId.ToString();

                    BazaDanychExternal.DokonajUpdate("insert into docout(docout_internal,docout_type,docout_nr,docout_date,docout_ts,pracownik_id,kontrah_id,docout_ref,docout_uwagi)" +
                        " values(3,'PP-'," + numer_dokumentu.ToString() + ",date(now()),sysdate()," + Wlasciwosci.id_Pracownika + "," + kontrah_wew + ",'','pp_skaner')");
                    last_docout = BazaDanychExternal.Command.LastInsertedId.ToString();



                    czy_buffor = true;
                }

                //MessageBox.Show("b ");





                if (czy_buffor == true)
                {
                    Realizuj(textBox2.Text, Wlasciwosci.imie_nazwisko);

                    label_head.Text = "PP/" + numer_dokumentu.ToString() + " mag:" + magazyn_global;
                    ilosc_etykiet.Text = "" + Ile_dokument(numer_dokumentu.ToString());

                }



            }
            else
            {
                MessageBox.Show("Pole ilości jest 0");
            }
            czysc();
            this.ZacznijSkanowanie();
        }


        private void Zakoncz_Skanowanie(object sender, EventArgs e)
        {
            this.Skanowanie.Abort();
            Skaner.Przewij_Skanowanie();
        }

        private void powrot_Click(object sender, EventArgs e)
        {

            DialogResult result3 = MessageBox.Show("Czy chcesz zakończyć dokument PP " + numer_dokumentu.ToString() + " ?",
            "Czy zakończyć?",
            MessageBoxButtons.YesNo,
            MessageBoxIcon.Question,
            MessageBoxDefaultButton.Button2);
            if (result3 == DialogResult.Yes)
            {
                this.Skanowanie.Abort();
                Skaner.Przewij_Skanowanie();
                this.myParent.Show();
                this.Hide();;
            }


        }



        private void button1_Anuluj(object sender, EventArgs e)
        {

        }
        private void ZacznijSkanowanie()
        {
            StringBuilder login = new StringBuilder();
            Skaner.UstawTryb_String(login);
            Skaner.Zacznij_Skanowanie();
            this.Skanowanie = new Thread(() => this.CheckString(login));
            this.Skanowanie.IsBackground = true;
            this.Skanowanie.Start();
        }



        private void CheckString(StringBuilder cc)
        {
            EventHandler method = null; //to jest delegat

            while (true)
            {
                if (cc.ToString() != "")
                {
                    if (method == null)
                    {
                        method = delegate
                        {
                            this.dodawanie(cc.ToString());
                        };
                    }
                    this.Invoke(method);
                    return;
                }
                Thread.Sleep(200);
            }
        }









        private void dodawanie(string ops)
        {
            //Skanowanie.Abort();
            Skaner.Przewij_Skanowanie();




            if (ops.Length > 0 && ops.Length < 10)
            {

                string zapytanie = "select ilosc,active,magazyn,system_id from etykiety e where e.id='" + ops + "' limit 1";
                object obj2 = BazaDanychExternal.Wyczytaj_Tabele(zapytanie);
                DataTable tabela = (DataTable)obj2;


                if (tabela.Rows.Count < 1)  //sprawdza czy jest etykieta
                {
                    MessageBox.Show("Brak w bazie et: " + ops);
                    ZacznijSkanowanie(); 
                    return;
                }

                if (czy_buffor == false)
                {
                    magazyn_global = tabela.Rows[0]["magazyn"].ToString();
                }


                if (tabela.Rows[0]["active"].ToString() == "0")
                {
                    MessageBox.Show("Etykieta  " + ops + " jest nieaktywna");
                    ZacznijSkanowanie(); 
                    return;
                }
                else
                    if (magazyn_global != tabela.Rows[0]["magazyn"].ToString())
                    {
                        MessageBox.Show("Etykieta  " + ops + " jest z magazynu " + tabela.Rows[0]["magazyn"].ToString());
                        ZacznijSkanowanie(); 
                        return;
                    }
                    else
                    {

                        Wlasciwosci.system_id_id = tabela.Rows[0]["system_id"].ToString();
                        textBox3.Text = tabela.Rows[0]["ilosc"].ToString();
                        textBox2.Text = ops;
                        textBox4.Focus();
                    }


            }
            else
            {
                MessageBox.Show("To nie jest numer etykiety WMS");
            }





            ZacznijSkanowanie();
        }





        public void przelicz(object sender, EventArgs e)
        {

            try
            {
                textBox1.Text = "" + (Convert.ToInt32(textBox3.Text) - Convert.ToInt32(textBox4.Text));
                //MessageBox.Show("" + Convert.ToInt32(textBox2.Text) + "," + Convert.ToInt32(textBox3.Text));
                if (Convert.ToInt32(textBox4.Text) < 0 || Convert.ToInt32(textBox1.Text) < 0)
                {
                    textBox4.Text = prev1;
                    textBox1.Text = prev2;
                    MessageBox.Show("Etykieta nie może być Ujemna");
                    return;
                }



                prev1 = textBox4.Text;
                prev2 = textBox1.Text;
                //MessageBox.Show(prev);
            }
            catch
            {
                // If there is an error, display the text using the system_id colors.

            }

        }
















        private void czysc()
        {
            textBox2.Text = "";
            textBox3.Text = "0";
            textBox4.Text = "";
            textBox1.Text = "0";
            textBox2.Focus();
        }



        private void timer1_Tick(object sender, EventArgs e)
        {
            this.Close();
            myParent.Show();
        }



        #region fullscreen
        private void fullscreenmode()
        {
            this.Width = Screen.PrimaryScreen.Bounds.Width;
            this.Height = Screen.PrimaryScreen.Bounds.Height;
            this.Left = 0;
            this.Top = 0;
        }
        #endregion

        private void button4_Click(object sender, EventArgs e)
        {
            dodawanie(textBox2.Text);
        }



    }
}