﻿using System;
using System.Linq;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using System.Threading;

namespace Tarczyn__Magazyn
{
    public partial class ProdukcjaMenu : Form
    {

        ActionMenu myParent;
        public ProdukcjaMenu(ActionMenu parent)
        {
            FullScreenMode.OknoOFF(this);
            InitializeComponent();
            myParent = parent;
            wms_wybrany.Text = Wlasciwosci.system_id;
            imie_nazwisk_label.Text = Wlasciwosci.wozek + ":" + Wlasciwosci.imie_nazwisko;
        }

        private void Wyjscie_Click(object sender, EventArgs e)
        {
            //BazaDanychExternal.ChangeHost(Wlasciwosci.Varbaza, Wlasciwosci.Varhost, Wlasciwosci.VarLoginMYSQL, Wlasciwosci.VarHasloMYSQL);
            myParent.Show();
            this.Close();
            //Wlasciwosci.id_Pracownika = "0";
        }


        private void button3_Click(object sender, EventArgs e)
        {
            if (myParent.myWlan.get_Signal_int() < 2)
            {
                MessageBox.Show("Trwa łączenie. Spróbuj ponownie...");
                return;
            }
            WczytywanieNaLinie nowy = new WczytywanieNaLinie(this);
            nowy.Show();
            this.Hide();
        }


        private void ActionMenu_Load(object sender, EventArgs e)
        {
            
        }


      
        private void button1_Click(object sender, EventArgs e)
        {
            if (myParent.myWlan.get_Signal_int() < 2)
            {
                MessageBox.Show("Trwa łączenie. Spróbuj ponownie...");
                return;
            }
            DeklaracjaProdukcji nowy = new DeklaracjaProdukcji(this);
            nowy.Show();
            this.Hide();
        }


              
     

    }
}