﻿using System;
using System.Linq;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using System.Threading;
using System.Text.RegularExpressions;
using System.Xml;

namespace Tarczyn__Magazyn
{



    public partial class Zad_ZM_Pobr : Form, IZad_DL
    {
        //MainMenu myParent = null;
        Zad_Main myParent = null;
        TextBox[] TextBoxArray = null;
        XmlNode node = null;
        TextBox AktualnyTextBox = null;


        List<string> _paleta_wybor = new List<string>();
        int[] _paleta = new int[100];


        //string ilosc_w_opakowaniu = "1";
        string ean_jednostki = "";
        string ean = "";
        string ilosc_w_opakowaniu = "";
        string ean_opakowanie_zbiorcze = "";
        string ilosc_szt_w_zbiorczym = "";
        string nosnik_numer = "";




        public Zad_ZM_Pobr(Zad_Main c, XmlNode node2)
        {

            FullScreenMode.OknoOFF(this);
            InitializeComponent();
            TextBoxArray = new TextBox[] { ETYKIETA };
            myParent = c;
            Etykieta.Inicjalizacja();
            wms_wybrany.Text = Wlasciwosci.system_id;
            imie_nazwisk_label.Text = Wlasciwosci.wozek + ":" + Wlasciwosci.imie_nazwisko;
            //Wlasciwosci.system_id_id = "";
            wypelnij_dane(node2);
        }

        void wypelnij_dane(XmlNode node2)
        {
            
            node = node2;
            //label7.Text = node["doc_type_nazwa"].InnerText + " " + node["doc_id"].InnerText;
            label7.Text = node["dokument"].InnerText;
            label12.Text = node["system_id_nazwa"].InnerText+"    DS"+node["paleta_id"].InnerText;
            textBox4.Text = node["stare_m_nazwa"].InnerText;
            licznik_label.Text = "" + node["ile_wszystkich"].InnerText + "/" + node["ile_gotowe"].InnerText + " ; " + node["ile_wszystkich_paleta"].InnerText + "/" + node["ile_gotowe_paleta"].InnerText;
            textBox4.Focus();
            ETYKIETA.Focus();
            message_label.Text = ""; //44265
        }


        private void TworzenieDL_Load(object sender, EventArgs e)
        {
            //ETYKIETA.Focus();
        }



        private void GotFocus_TextBox(object sender, EventArgs e)
        {
            Skaner.Przewij_Skanowanie();
            TextBox Pole_Tekstowe = (TextBox)sender;
            AktualnyTextBox = Pole_Tekstowe;
            ZacznijSkanowanie();
        }

        private void LostFocus_TextBox(object sender, EventArgs e)
        {
            AktualnyTextBox = null;
            Skaner.Przewij_Skanowanie();

            TextBox Pole_Tekstowe = (TextBox)sender;

            if (ETYKIETA == Pole_Tekstowe)
            {


            }
        }







        #region Skanowanie

        Thread Skanowanie = null;



        private void ZacznijSkanowanie()
        {
            StringBuilder login = new StringBuilder();
            Skaner.UstawTryb_String(login);
            Skaner.Zacznij_Skanowanie();
            Skanowanie = new Thread(() => CheckString(login));
            Skanowanie.IsBackground = true;
            Skanowanie.Start();
        }

        private void CheckString(StringBuilder cc)
        {
            while (true)
            {
                if (cc.ToString() != "")
                {
                    this.Invoke(new EventHandler(delegate
                    {
                        dodawanie(cc.ToString());
                    }));
                    return;
                }
                Thread.Sleep(200);
            }
        }


        private void Zakoncz_Skanowanie()
        {
            {
                Skanowanie.Abort();
                Skaner.Przewij_Skanowanie();
            }
        }

        private void dodawanie(string ops)
        {
            //MessageBox.Show("TrybSkanu:" + TrybSkanu + "   ,   ops.Length:" + ops.Length);


            AktualnyTextBox.Text = ops;
            if (ops == "")
            {
                MessageBox.Show("Nie wypełniono etykiety");
                return;
            }


            



            if (AktualnyTextBox == ETYKIETA)
            {

                XmlNode node_etykieta = Uzgodnienie_Zadania(node, null, ops, 2);


                    if (node_etykieta["komunikat"].InnerText != "OK")
                    {
                        //ETYKIETA.Text = "";
                        message_label.BackColor = System.Drawing.Color.Red;
                        textBox4.Focus();
                        ETYKIETA.Focus();

                        if (node_etykieta["komunikat"].InnerText.Length > 40)
                        {
                            MessageBox.Show(node_etykieta["komunikat"].InnerText);
                        }
                        else
                        {
                            message_label.Text = node_etykieta["komunikat"].InnerText;
                        }
                    }                    
                    else
                    {
                        node = node_etykieta;
                        ETYKIETA.Text = "";
                        message_label.BackColor = System.Drawing.Color.Lime;

                        myParent.Show();
                        myParent.ZacznijNasluchiwanie("21", "");
                        this.Close();

                        /*
                        //licznik_label.Text = "" + node_etykieta["ile_wszystkich"].InnerText + " / " + node_etykieta["ile_gotowe"].InnerText;
                        if (node_etykieta["czy_koniec_kompletacji"].InnerText == "NIE")
                        {
                            node_etykieta = myParent.PobieranieZadaniaZserwera("1");
                            this.wypelnij_dane(node_etykieta);

                        }
                        else
                        {
                            myParent.Show();
                            myParent.ZacznijNasluchiwanie("11");
                            this.Close();
                        }
                        */                       
                        
                        
                    }
                }

            
            //this.ZacznijSkanowanie();
            }

        private XmlNode Uzgodnienie_Zadania(XmlNode node_src, XmlNode node_etykieta, string etykieta_scan, int proba)
        {
            XmlDocument doc1_etykieta = WebService.Pobierz_XmlDocument("zmiana_miejsca_realizacja.php?akcja=pobranie_palety&zadanie_dane_id=" + node_src["id"].InnerText + "&imie_nazwisko=" + Uri.EscapeUriString(Wlasciwosci.imie_nazwisko) + "&etykieta_scan=" + etykieta_scan);
            node_etykieta = WebService.Pobierz_XmlNode(doc1_etykieta, "dane");
            return node_etykieta;
        }




        #endregion






        private void QT_KeyDown(object sender, KeyEventArgs e)
        {

        }





        private void powrot_Click(object sender, EventArgs e)
        {
            Skaner.Przewij_Skanowanie();
            myParent.Show();
            this.Close();
        }

        private void odkladanie_Click(object sender, EventArgs e)
        {
            //myParent.delivery_odkladanie("TAK");
            myParent.Show();
            myParent.ZacznijNasluchiwanie("21", "");

            this.Close();
        }

        private void podglad_button_Click(object sender, EventArgs e)
        {
            Zad_DL_Podglad okno_nowy_nosnik = new Zad_DL_Podglad(this, node);
            okno_nowy_nosnik.Show();
            this.Hide();
        }

        private void Etykieta_KeyDown(object sender, KeyEventArgs e)
        {
            if (e.KeyCode == Keys.Enter)
            {
                this.dodawanie(ETYKIETA.Text);
                //odkladanie_Click(this, new EventArgs());
            }
        }






    }
}