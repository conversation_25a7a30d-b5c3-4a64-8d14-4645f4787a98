﻿using System;
using System.Linq;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using System.Threading;

namespace Tarczyn__Magazyn
{
    public partial class MenuDostawy : Form
    {

        ActionMenu myParent;
        public MenuDostawy(ActionMenu parent)
        {
            FullScreenMode.OknoOFF(this);
            InitializeComponent();
            myParent = parent;
        }

        private void Wyjscie_Click(object sender, EventArgs e)
        {
            myParent.Show();
            this.Close();
            
        }

        private void button20_Click(object sender, EventArgs e)
        {
            WyborBazy XA = new WyborBazy();
            if (XA.ShowDialog() == DialogResult.OK)
            {
                //ActionMenu v = new ActionMenu(myParent);
                //v.Show();
                //this.Close();
                przelacz_system_id();
            }
            Wlasciwosci.CurrentOperacja = "0";
        }
        

        

        private void przelacz_system_id()
        {
            switch (Wlasciwosci.TrybActionMenu)
            {
                case 0:
                    {
                        //prod
                        BazaDanychExternal.ChangeHost(Wlasciwosci.Varbaza, Wlasciwosci.Varhost, Wlasciwosci.VarLoginMYSQL, Wlasciwosci.VarHasloMYSQL);
                        
                        break;
                    }
                case 1:
                    {
                        // gg
                        BazaDanychExternal.ChangeHost(Wlasciwosci.GNG, Wlasciwosci.Varhost, Wlasciwosci.VarLoginMYSQL, Wlasciwosci.VarHasloMYSQL);
                        break;
                    }
            }
        }

        private void ActionMenu_Load(object sender, EventArgs e)
        {
            przelacz_system_id();
        }




 

        private void button1_Click_1(object sender, EventArgs e)
        {

        }

        private void button2_Click(object sender, EventArgs e)
        {
            if (myParent.myWlan.get_Signal_int() < 2)
            {
                MessageBox.Show("Trwa łączenie. Spróbuj ponownie...");
                return;
            }
            //UpdateEtykietaMenu tt = new UpdateEtykietaMenu(this);
            //tt.Show();
            //this.Hide();
        }

        private void button3_Click(object sender, EventArgs e)
        {
            

        }

        
              
     

    }
}