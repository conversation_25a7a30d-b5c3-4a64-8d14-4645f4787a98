# Schemat Bazy Danych - System Inwentaryzacji

## Przegląd

System inwentaryzacji wykorzystuje relacyjną bazę danych MySQL do przechowywania danych o inwentaryzacjach, etykietach, lokalizacjach i operacjach magazynowych.

## Główne Tabele

### Tabela `inwentaryzacja`

Główna tabela przechowująca dane inwentaryzacji.

```sql
CREATE TABLE `inwentaryzacja` (
  `id` int unsigned NOT NULL AUTO_INCREMENT,
  `data` date DEFAULT NULL,
  `opis` varchar(45) DEFAULT NULL,
  `etykieta_id` varchar(23) DEFAULT NULL,
  `paleta_id` int NOT NULL DEFAULT '0',
  `kod` varchar(60) DEFAULT NULL,
  `ilosc` decimal(10,3) DEFAULT NULL,
  `jm` varchar(5) NOT NULL DEFAULT '',
  `miejscep` int unsigned DEFAULT NULL,
  `ilosc_spisana` decimal(10,3) DEFAULT NULL,
  `pracownik` varchar(45) DEFAULT NULL,
  `ts` timestamp NOT NULL DEFAULT '0000-00-00 00:00:00',
  `inwentaryzacja_id` int unsigned DEFAULT NULL,
  `active` int unsigned NOT NULL DEFAULT '1',
  `hala` int unsigned DEFAULT NULL,
  `regal` char(5) DEFAULT NULL,
  `miejsce` int unsigned DEFAULT NULL,
  `poziom` varchar(4) DEFAULT NULL,
  `stat` varchar(4) DEFAULT NULL,
  `nrsap` varchar(45) DEFAULT NULL,
  `podkod` varchar(45) DEFAULT NULL,
  `skan` varchar(65) DEFAULT NULL,
  `uwaga` varchar(45) DEFAULT NULL,
  `nadwyzka` varchar(1) NOT NULL DEFAULT '1',
  `system_id` int unsigned NOT NULL DEFAULT '0',
  `proba` int unsigned NOT NULL DEFAULT '1',
  `nr_wspolny` int unsigned DEFAULT NULL,
  `magazyn` varchar(45) DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `etykieta_id` (`etykieta_id`),
  KEY `miejscep` (`miejscep`),
  KEY `inwentaryzacja_id` (`inwentaryzacja_id`),
  KEY `Index_5` (`nrsap`),
  KEY `system_id` (`system_id`) /*!80000 INVISIBLE */,
  KEY `active` (`active`)
) ENGINE=MyISAM AUTO_INCREMENT=720814 DEFAULT CHARSET=utf8mb3;

```

**Opis pól:**
- `id` - Klucz główny, auto-increment
- `data` - Data inwentaryzacji
- `opis` - Opis sesji inwentaryzacji
- `ilosc` - Ilość teoretyczna (z systemu)
- `ilosc_spisana` - Ilość rzeczywista (spisana przez pracownika)
- `kod` - Kod produktu
- `inwentaryzacja_id` - Identyfikator sesji inwentaryzacji
- `hala`, `regal`, `miejsce`, `poziom` - Lokalizacja magazynowa
- `pracownik` - Imię i nazwisko pracownika wykonującego
- `ts` - Timestamp operacji
- `podkod` - Dodatkowy kod produktu
- `skan` - Zeskanowany kod/etykieta
- `stat` - Status pozycji
- `nrsap` - Numer SAP
- `proba` - Identyfikator próby
- `nr_wspolny` - Numer wspólny dla grupowania
- `system_id` - Identyfikator systemu klienta
- `etykieta_id` - Powiązanie z tabelą etykiety
- `paleta_id` - Powiązanie z paletą
- `active` - Flaga aktywności (1=aktywny, 0=nieaktywny)
- `uwaga` - Uwagi (np. 'brak_w_inw', 'byla_juz')
- `miejscep` - ID miejsca z tabeli miejsca

### Tabela `etykiety`

Przechowuje informacje o etykietach produktów.

```sql
CREATE TABLE `etykiety` (
  `id` int NOT NULL AUTO_INCREMENT,
  `system_id` int NOT NULL DEFAULT '0',
  `etykieta_klient` varchar(45) DEFAULT NULL,
  `magazyn` int unsigned NOT NULL DEFAULT '0',
  `active` int unsigned DEFAULT NULL,
  `miejscep` int NOT NULL DEFAULT '0',
  `kod_id` int unsigned DEFAULT NULL,
  `status_id_old` int unsigned NOT NULL DEFAULT '1',
  `status_id` int NOT NULL DEFAULT '1',
  `stat` varchar(17) DEFAULT NULL,
  `paleta_id` int unsigned DEFAULT NULL,
  `kartony` int unsigned DEFAULT NULL,
  `dataprod` date DEFAULT NULL,
  `data_waznosci` date DEFAULT NULL,
  `ilosc` decimal(10,3) DEFAULT NULL,
  `ts` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `status` varchar(30) DEFAULT NULL,
  `blloc` varchar(45) DEFAULT NULL,
  `akcja_id` int DEFAULT NULL,
  `status_prism` varchar(45) DEFAULT NULL,
  `lot` varchar(300) DEFAULT NULL,
  `sscc` varchar(35) DEFAULT NULL,
  `gtin` varchar(24) DEFAULT NULL,
  `przeznaczenie_id` int NOT NULL DEFAULT '1',
  `nretykiety` int NOT NULL DEFAULT '0',
  `docin_id` int DEFAULT NULL,
  `docout_id` int DEFAULT NULL,
  `delivery_id` int unsigned DEFAULT NULL,
  `listcontrol_id` int unsigned DEFAULT NULL,
  `status_id2` int unsigned DEFAULT NULL,
  `edycja_et` int NOT NULL DEFAULT '1',
  PRIMARY KEY (`id`,`system_id`) USING BTREE,
  UNIQUE KEY `id` (`id`) USING BTREE,
  KEY `paleta` (`paleta_id`),
  KEY `magazyn` (`magazyn`) USING BTREE,
  KEY `active` (`active`) USING BTREE,
  KEY `miejsce` (`miejscep`),
  KEY `docin_id` (`docin_id`),
  KEY `docout_id` (`docout_id`),
  KEY `delivery_id` (`delivery_id`),
  KEY `kod_id` (`kod_id`),
  KEY `system_id` (`system_id`),
  KEY `listcontrol_id` (`listcontrol_id`),
  KEY `nretykiety` (`nretykiety`),
  KEY `status_id` (`status_id`) USING BTREE,
  KEY `etykieta_klient` (`etykieta_klient`),
  KEY `active_system_id` (`system_id`,`active`),
  KEY `status_id2` (`status_id2`),
  KEY `idx_etykiety_docout_system` (`docout_id`,`system_id`),
  CONSTRAINT `chk_paleta_id` CHECK (((`paleta_id` <> 0) and (`paleta_id` is not null)))
) ENGINE=InnoDB AUTO_INCREMENT=7624131 DEFAULT CHARSET=utf8mb3 PACK_KEYS=1 ROW_FORMAT=DYNAMIC;

```

### Tabela `miejsca`

Definiuje strukturę lokalizacji magazynowych.

```sql
CREATE TABLE `miejsca` (
  `id` int NOT NULL AUTO_INCREMENT,
  `hala` int NOT NULL DEFAULT '0',
  `regal` char(10) NOT NULL DEFAULT '',
  `miejsce` int NOT NULL DEFAULT '0',
  `poziom` varchar(4) DEFAULT NULL,
  `wysokosc` varchar(3) NOT NULL DEFAULT '',
  `widoczne` int unsigned NOT NULL DEFAULT '1',
  `zbiorka` int unsigned NOT NULL DEFAULT '0',
  `max_pojemnosc` int unsigned NOT NULL DEFAULT '0',
  `stanowisko_pracy_id` int unsigned NOT NULL DEFAULT '0',
  `picking_fakturowany` int unsigned NOT NULL DEFAULT '0',
  `producent` varchar(45) NOT NULL DEFAULT '0',
  `max_udzwig_kg` int unsigned NOT NULL DEFAULT '0',
  PRIMARY KEY (`id`),
  KEY `regal` (`regal`),
  KEY `miejsce` (`miejsce`),
  KEY `poziom` (`poziom`),
  KEY `hala` (`hala`),
  KEY `zbiorka` (`zbiorka`),
  KEY `widoczne` (`widoczne`)
) ENGINE=MyISAM AUTO_INCREMENT=84906 DEFAULT CHARSET=utf8mb3;

```

### Tabela `kody`

Kartoteka produktów z kodami EAN i informacjami o produktach.

```sql
CREATE TABLE `kody` (
  `id` int NOT NULL AUTO_INCREMENT,
  `kod` varchar(100) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL DEFAULT '',
  `kod2` varchar(100) DEFAULT NULL,
  `kod_nazwa` varchar(200) DEFAULT '',
  `system_id` int NOT NULL DEFAULT '0',
  `jm` varchar(8) DEFAULT NULL,
  `ean_jednostki` varchar(240) NOT NULL DEFAULT '',
  `waga_szt_kg` decimal(9,3) NOT NULL DEFAULT '0.000',
  `objetosc_szt_cm` int unsigned NOT NULL DEFAULT '0',
  `ean` varchar(100) DEFAULT NULL,
  `opakowanie_jm` varchar(8) DEFAULT NULL,
  `ilosc_w_opakowaniu` int unsigned DEFAULT '0',
  `ean_opakowanie_zbiorcze` varchar(45) NOT NULL,
  `ilosc_szt_w_zbiorczym` int NOT NULL,
  `kod_producent` varchar(75) DEFAULT NULL,
  `ilosc_opak_w_zborczym` int unsigned NOT NULL DEFAULT '1',
  `kod_kategoria` varchar(45) DEFAULT NULL,
  `kod_plec` varchar(45) DEFAULT NULL,
  `active` int unsigned NOT NULL DEFAULT '1',
  `gln` varchar(45) NOT NULL DEFAULT '',
  `wlasciciel` varchar(45) NOT NULL DEFAULT '',
  `brand_id` varchar(45) NOT NULL DEFAULT '1',
  `kody_grupy_id` int unsigned DEFAULT NULL,
  `wysokosc` varchar(25) DEFAULT NULL,
  `jednostka_wagi` varchar(10) NOT NULL DEFAULT '',
  `ilosc_szt_palecie` int unsigned NOT NULL DEFAULT '0',
  `ilosc_dni_przydatnosci` int unsigned NOT NULL DEFAULT '0',
  `wymagana_partia` int unsigned NOT NULL DEFAULT '0',
  `wymagana_data_waznosci` int unsigned NOT NULL DEFAULT '0',
  `wymagana_dataprod` int unsigned NOT NULL DEFAULT '0',
  `status_jakosci_domyslny` int unsigned DEFAULT NULL,
  `dlugosc_szt_mm` int unsigned NOT NULL DEFAULT '0',
  `szerokosc_szt_mm` int unsigned NOT NULL DEFAULT '0',
  `wysokosc_szt_mm` int unsigned NOT NULL DEFAULT '0',
  `jednostka_skladowania_domyslna` int unsigned NOT NULL DEFAULT '0',
  `kraj_pochodzenia_id` int unsigned NOT NULL DEFAULT '0',
  `nowy_kod_mail` int unsigned NOT NULL DEFAULT '0',
  `ts_created` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `adr_towary` int DEFAULT '0',
  PRIMARY KEY (`id`),
  UNIQUE KEY `kod_system_id` (`kod`,`system_id`),
  KEY `kod` (`kod`) USING BTREE,
  KEY `system_id` (`system_id`),
  KEY `active` (`active`),
  KEY `ilosc_w_opakowaniu` (`ilosc_w_opakowaniu`)
) ENGINE=InnoDB AUTO_INCREMENT=78453 DEFAULT CHARSET=utf8mb3 ROW_FORMAT=DYNAMIC;

```

### Tabela `operacje`

Rejestr wszystkich operacji wykonywanych w systemie.

```sql
CREATE TABLE `operacje` (
  `id` int unsigned NOT NULL AUTO_INCREMENT,
  `etykieta_id` int unsigned NOT NULL DEFAULT '0',
  `doc_type` varchar(5) NOT NULL DEFAULT '',
  `doc_nr` varchar(25) NOT NULL DEFAULT '0',
  `imie_nazwisko` varchar(25) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '',
  `typ_operacji` varchar(60) NOT NULL DEFAULT '',
  `ts_ins` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `wozek` int unsigned NOT NULL DEFAULT '0',
  `system_id` int unsigned NOT NULL DEFAULT '0',
  `operac_id` varchar(10) NOT NULL DEFAULT '0',
  `ilosc` int unsigned NOT NULL DEFAULT '0',
  `jm` varchar(5) NOT NULL DEFAULT 'PLT',
  PRIMARY KEY (`id`),
  KEY `etyieta_id` (`etykieta_id`),
  KEY `doc_nr` (`doc_nr`),
  KEY `ts_ins` (`ts_ins`),
  KEY `operac_id` (`operac_id`) /*!80000 INVISIBLE */,
  KEY `typ_operacji` (`typ_operacji`) /*!80000 INVISIBLE */
) ENGINE=MyISAM AUTO_INCREMENT=6225666 DEFAULT CHARSET=latin1;

```

### Tabela `zmianym`

Rejestr zmian lokalizacji etykiet podczas inwentaryzacji.

```sql
CREATE TABLE `zmianym` (
  `id` int unsigned NOT NULL AUTO_INCREMENT,
  `typ` varchar(5) NOT NULL DEFAULT '',
  `doc_nr` int NOT NULL DEFAULT '0',
  `pracownik_id` int unsigned DEFAULT NULL,
  `data` date NOT NULL DEFAULT '0000-00-00',
  `etykieta` int unsigned NOT NULL DEFAULT '0',
  `system_id` int DEFAULT NULL,
  `stare_m` int unsigned NOT NULL DEFAULT '0',
  `nowe_m` int unsigned NOT NULL DEFAULT '0',
  `doc_internal` varchar(2) CHARACTER SET latin1 COLLATE latin1_swedish_ci DEFAULT NULL,
  `stat` int unsigned DEFAULT '0',
  `tszm` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `uwagi` varchar(45) NOT NULL DEFAULT '',
  PRIMARY KEY (`id`),
  KEY `doc_nr` (`doc_nr`),
  KEY `etykieta` (`etykieta`),
  KEY `nowe_m` (`nowe_m`),
  KEY `data` (`data`),
  KEY `stare_m` (`stare_m`),
  KEY `system_id` (`system_id`),
  KEY `pracownik_id` (`pracownik_id`) /*!80000 INVISIBLE */,
  KEY `pracownik_id_docinternal` (`pracownik_id`,`doc_internal`)
) ENGINE=InnoDB AUTO_INCREMENT=5894639 DEFAULT CHARSET=utf8mb3 ROW_FORMAT=DYNAMIC;

```

## Relacje Między Tabelami

### Główne Relacje

```
inwentaryzacja
├── etykieta_id → etykiety.id
├── miejscep → miejsca.id (przez etykiety.miejscep)
├── kod → kody.kod (+ system_id)
└── paleta_id → palety.id

etykiety
├── miejscep → miejsca.id
├── kod_id → kody.id
├── paleta_id → palety.id
└── system_id → systemy.wartosc

operacje
├── etykieta_id → etykiety.id
└── system_id → systemy.wartosc

zmianym
├── etykieta → etykiety.id
├── stare_m → miejsca.id
├── nowe_m → miejsca.id
└── pracownik_id → pracownicy.id
```

### Diagram Relacji

```
┌─────────────────┐       ┌─────────────────┐       ┌─────────────────┐
│   inwentaryzacja│──────▶│    etykiety     │──────▶│     miejsca     │
│                 │       │                 │       │                 │
│ - id            │       │ - id            │       │ - id            │
│ - etykieta_id   │       │ - miejscep      │       │ - hala          │
│ - kod           │       │ - kod_id        │       │ - regal         │
│ - hala          │       │ - paleta_id     │       │ - miejsce       │
│ - regal         │       │ - ilosc         │       │ - poziom        │
│ - miejsce       │       │ - lot           │       └─────────────────┘
│ - poziom        │       │ - dataprod      │
│ - ilosc_spisana │       │ - data_waznosci │
└─────────────────┘       └─────────────────┘
         │                         │
         │                         │
         ▼                         ▼
┌─────────────────┐       ┌─────────────────┐
│    operacje     │       │      kody       │
│                 │       │                 │
│ - etykieta_id   │       │ - id            │
│ - typ_operacji  │       │ - kod           │
│ - doc_type      │       │ - ean           │
│ - imie_nazwisko │       │ - kod_nazwa     │
│ - ts            │       │ - system_id     │
└─────────────────┘       └─────────────────┘
```

## Indeksy i Optymalizacja

### Kluczowe Indeksy

1. **inwentaryzacja:**
   - `idx_inwentaryzacja_id` - dla grupowania sesji
   - `idx_etykieta_id` - dla wyszukiwania etykiet
   - `idx_active` - dla filtrowania aktywnych rekordów

2. **etykiety:**
   - `idx_miejscep` - dla lokalizacji
   - `idx_system_id` - dla filtrowania systemów

3. **miejsca:**
   - `idx_hala_regal_miejsce_poziom` - dla wyszukiwania lokalizacji

### Zapytania Optymalizacyjne

```sql
-- Najczęściej używane zapytanie - stan inwentaryzacji
SELECT 
    sum(if(ilosc_spisana is null,0,1)) as ilosc_zliczona, 
    count(1) as stan 
FROM inwentaryzacja i 
WHERE i.inwentaryzacja_id = ? 
    AND kod != '10101'
    AND active = 1;

-- Wyszukiwanie etykiety w inwentaryzacji
SELECT 
    i.kod, i.podkod, i.ilosc, i.paleta_id,
    m.hala, m.regal, m.miejsce, m.poziom,
    i.hala as hala_i, i.regal as regal_i, 
    i.miejsce as miejsce_i, i.poziom as poziom_i,
    i.etykieta_id
FROM inwentaryzacja i 
LEFT JOIN etykiety e ON e.id = i.etykieta_id 
LEFT JOIN miejsca m ON m.id = e.miejscep  
WHERE i.etykieta_id = ? 
    AND i.inwentaryzacja_id = ?
    AND i.active = 1
ORDER BY i.ts ASC 
LIMIT 1;
```
