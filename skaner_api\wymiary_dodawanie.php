<?php

//include_once 'db_pdo.php';
include_once 'Db.class.php';
include_once 'funkcje.inc';
//error_reporting(E_ALL);
//ini_set('display_errors', 1);

$db = new Db();
//
//if (empty($argv)) {
//    //$argv = array();
//    $argv[1] = $_GET['db'];
//    $argv[2] = $_GET['akcja'];
//}
//
$baza_danych = $_GET['db'];
$akcja = $_GET['akcja'];




$komunikat = "OK";

if(empty($_GET['miejsc_paletowych']))
{
    $_GET['miejsc_paletowych']="1";
}




if ($akcja == "dodaj") {
    $sql = 'insert into ' . $baza_danych . '.delivery_wymiary (delivery_id, wysokosc, szerokosc, dlugosc, waga, nazwa_nosnika, ile, miejsc_paletowych,pracownik_id)
values ("' . $_GET['delivery_id'] . '","' . $_GET['wysokosc'] . '","' . $_GET['szerokosc'] . '","' . $_GET['dlugosc'] . '","' . $_GET['waga'] . '","' . $_GET['nazwa_nosnika'] . '","' . $_GET['ile'] . '","' . $_GET['miejsc_paletowych'] . '","' . $_GET['pracownik_id'] . '")   ';
    //echo $sql;
    $result = $db->mGetResultAsXML($sql);


    return xml_from_indexed_array(array('komunikat' => $komunikat));
}



