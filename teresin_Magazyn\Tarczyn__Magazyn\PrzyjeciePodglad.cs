﻿using System;
using System.Linq;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using System.Threading;

namespace Tarczyn__Magazyn
{
    public partial class PrzyjeciePodglad : Form
    {
        UpdateEtykietaMenu myParent = null;

        string listcontrol_id = "0";


        public PrzyjeciePodglad(UpdateEtykietaMenu MyParent)
        {
            this.myParent = MyParent;
            listcontrol_id = myParent.listcontrol_id;
            
            FullScreenMode.OknoOFF(this);
            InitializeComponent();
            pokaz_podglad_wg_palet();
            //BazaDanychExternal.Inicjalizacja(Wlasciwosci.Varbaza, Wlasciwosci.Varhost, Wlasciwosci.VarLoginMYSQL, Wlasciwosci.VarHasloMYSQL, this);  
        }

        Thread Skanowanie = null;


        private void ZacznijSkanowanie()
        {
            StringBuilder login = new StringBuilder();
            Skaner.UstawTryb_String(login);
            Skaner.Zacznij_Skanowanie();
            Skanowanie = new Thread(() => CheckString(login));
            Skanowanie.IsBackground = true;
            Skanowanie.Start();
        }
        private void CheckString(StringBuilder cc)
        {
            while (true)
            {
                if (cc.ToString() != "")
                {
                    this.Invoke(new EventHandler(delegate
                    {

                        dodawanie(cc.ToString());
                    }));
                    return;
                }
                Thread.Sleep(200);
            }
        }
        private void Zakoncz_Skanowanie()
        {
            try
            {
                Skanowanie.Abort();
                Skaner.Przewij_Skanowanie();
            }
            catch
            {
            }
        }

        private void Exit_Click_1(object sender, EventArgs e)
        {
            //Zakoncz_Skanowanie();
            //BazaDanychExternal.ChangeHost(Wlasciwosci.Varbaza, Wlasciwosci.Varhost, Wlasciwosci.VarLoginMYSQL, Wlasciwosci.VarHasloMYSQL);
            myParent.Show();
            this.Close();
        }


        private void button2_Click(object sender, EventArgs e)
        {


        }

        
        private void dodawanie(string gg)
        {

            
            Zakoncz_Skanowanie();
            
        }




        void pokaz_podglad_wg_kodow()
        {
            DataTable AktualneSztuki =
            (DataTable)BazaDanychExternal.Wyczytaj_Tabele("SELECT k.kod, cast(TRIM(TRAILING '.' FROM TRIM(TRAILING '0' from sum(e.ilosc))) as char) as ilosc,count(1) as et FROM list_control l left join listcontrol_palety lp on lp.listcontrol_id=l.id left join etykiety e on e.paleta_id=lp.paleta_id left join kody k on e.kod_id=k.id WHERE l.id='" + listcontrol_id + "' and e.kod_id is not null group by e.kod_id order by k.kod ");
            if (AktualneSztuki.Rows.Count == 0)
            {
                MessageBox.Show("Brak wczytanego towaru.");
                return;
            }


            dataGrid1.DataSource = AktualneSztuki;
            dataGrid1.TableStyles.Clear();
            DataGridTableStyle tableStyle = new DataGridTableStyle();
            tableStyle.MappingName = AktualneSztuki.TableName;

            DataGridTextBoxColumn tbcName = new DataGridTextBoxColumn();
            tbcName.Width = 150;
            tbcName.MappingName = AktualneSztuki.Columns[0].ColumnName;
            tbcName.HeaderText = AktualneSztuki.Columns[0].ColumnName;
            tableStyle.GridColumnStyles.Add(tbcName);
            DataGridTextBoxColumn tbcName1 = new DataGridTextBoxColumn();
            tbcName1.Width = 50;
            tbcName1.MappingName = AktualneSztuki.Columns[1].ColumnName;
            tbcName1.HeaderText = AktualneSztuki.Columns[1].ColumnName;
            tableStyle.GridColumnStyles.Add(tbcName1);
            DataGridTextBoxColumn tbcName2 = new DataGridTextBoxColumn();
            tbcName2.Width = 50;
            tbcName2.MappingName = AktualneSztuki.Columns[2].ColumnName;
            tbcName2.HeaderText = AktualneSztuki.Columns[2].ColumnName;
            tableStyle.GridColumnStyles.Add(tbcName2);




            dataGrid1.TableStyles.Add(tableStyle);
        }

        void pokaz_podglad_wg_palet()
        {
            DataTable AktualneSztuki =
            (DataTable)BazaDanychExternal.Wyczytaj_Tabele("SELECT concat('DS',e.paleta_id) as paleta_id,k.kod, cast(TRIM(TRAILING '.' FROM TRIM(TRAILING '0' from sum(e.ilosc))) as char) as ilosc,count(1) as et FROM list_control l left join listcontrol_palety lp on lp.listcontrol_id=l.id left join etykiety e on e.paleta_id=lp.paleta_id left join kody k on e.kod_id=k.id WHERE l.id='" + listcontrol_id + "' and e.kod_id is not null group by e.paleta_id,e.id order by e.paleta_id,k.kod ");
            if (AktualneSztuki.Rows.Count == 0)
            {
                MessageBox.Show("Brak wczytanego towaru.");
                return;
            }


            dataGrid1.DataSource = AktualneSztuki;
            dataGrid1.TableStyles.Clear();
            DataGridTableStyle tableStyle = new DataGridTableStyle();
            tableStyle.MappingName = AktualneSztuki.TableName;

            DataGridTextBoxColumn tbcName = new DataGridTextBoxColumn();
            tbcName.Width = 70;
            tbcName.MappingName = AktualneSztuki.Columns[0].ColumnName;
            tbcName.HeaderText = AktualneSztuki.Columns[0].ColumnName;
            tableStyle.GridColumnStyles.Add(tbcName);
            DataGridTextBoxColumn tbcName1 = new DataGridTextBoxColumn();
            tbcName1.Width = 150;
            tbcName1.MappingName = AktualneSztuki.Columns[1].ColumnName;
            tbcName1.HeaderText = AktualneSztuki.Columns[1].ColumnName;
            tableStyle.GridColumnStyles.Add(tbcName1);
            DataGridTextBoxColumn tbcName2 = new DataGridTextBoxColumn();
            tbcName2.Width = 50;
            tbcName2.MappingName = AktualneSztuki.Columns[2].ColumnName;
            tbcName2.HeaderText = AktualneSztuki.Columns[2].ColumnName;
            tableStyle.GridColumnStyles.Add(tbcName2);





            dataGrid1.TableStyles.Add(tableStyle);
        }

        void pokaz_podglad_wg_awizacji()
        {
            DataTable AktualneSztuki =
            (DataTable)BazaDanychExternal.Wyczytaj_Tabele(" select b.*,if(ilosc_przyj=ilosc_awizo,'OK','NOOK') as wynik from ( select a.kod,sum(a.ilosc_przyj) as ilosc_przyj, sum(a.ilosc_awizo) as ilosc_awizo from ( SELECT k.kod, 0 as ilosc_awizo, cast(TRIM(TRAILING '.' FROM TRIM(TRAILING '0' from sum(e.ilosc))) as char) as ilosc_przyj FROM list_control l left join listcontrol_palety lp on lp.listcontrol_id=l.id left join etykiety e on e.paleta_id=lp.paleta_id left join kody k on e.kod_id=k.id WHERE l.id= '" + listcontrol_id + "' and e.kod_id is not null group by e.kod_id union SELECT a.kod, cast(TRIM(TRAILING '.' FROM TRIM(TRAILING '0' from sum(a.ilosc))) as char) as ilosc_awizo, 0 as ilosc_przyj FROM list_control l left join awizacje_dostaw_head h on l.id=h.listcontrol_id left join awizacje_dostaw_dane a on a.awizacje_dostaw_id=h.id WHERE l.id= '" + listcontrol_id + "'  group by a.kod) as a group by a.kod ) as b order by kod  ");
            if (AktualneSztuki.Rows.Count == 0)
            {
                MessageBox.Show("Brak wczytanego towaru.");
                return;
            }


            dataGrid1.DataSource = AktualneSztuki;
            dataGrid1.TableStyles.Clear();
            DataGridTableStyle tableStyle = new DataGridTableStyle();
            tableStyle.MappingName = AktualneSztuki.TableName;

            DataGridTextBoxColumn tbcName = new DataGridTextBoxColumn();
            tbcName.Width = 150;
            tbcName.MappingName = AktualneSztuki.Columns[0].ColumnName;
            tbcName.HeaderText = AktualneSztuki.Columns[0].ColumnName;
            tableStyle.GridColumnStyles.Add(tbcName);
            DataGridTextBoxColumn tbcName1 = new DataGridTextBoxColumn();
            tbcName1.Width = 50;
            tbcName1.MappingName = AktualneSztuki.Columns[1].ColumnName;
            tbcName1.HeaderText = AktualneSztuki.Columns[1].ColumnName;
            tableStyle.GridColumnStyles.Add(tbcName1);
            DataGridTextBoxColumn tbcName2 = new DataGridTextBoxColumn();
            tbcName2.Width = 50;
            tbcName2.MappingName = AktualneSztuki.Columns[2].ColumnName;
            tbcName2.HeaderText = AktualneSztuki.Columns[2].ColumnName;
            tableStyle.GridColumnStyles.Add(tbcName2);

            DataGridTextBoxColumn tbcName3 = new DataGridTextBoxColumn();
            tbcName3.Width = 50;
            tbcName3.MappingName = AktualneSztuki.Columns[3].ColumnName;
            tbcName3.HeaderText = AktualneSztuki.Columns[3].ColumnName;
            tableStyle.GridColumnStyles.Add(tbcName3);




            dataGrid1.TableStyles.Add(tableStyle);
        }


        private void WczytywanieNaLinie_Load_1(object sender, EventArgs e)
        {
            //ZacznijSkanowanie();
        }

        private void button2_Click_1(object sender, EventArgs e)
        {
            pokaz_podglad_wg_palet();
        }

        private void button3_Click(object sender, EventArgs e)
        {
            pokaz_podglad_wg_kodow();
        }

        private void button1_Click(object sender, EventArgs e)
        {
            pokaz_podglad_wg_awizacji();
        }

        

  





    }
}