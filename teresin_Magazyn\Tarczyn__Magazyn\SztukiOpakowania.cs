﻿using System;
using System.Linq;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using System.Threading;

namespace Tarczyn__Magazyn
{
    public partial class SztukiOpakowania : Form
    {
        double minimum = 0;

        double maximum = 0;
        public string komunikat = "";
        public double ilosc_wpisana = 0;
        public double ilosc_planowana = 0;
        public double ilosc_maksymalna = 0;
        public int ilosc_w_opakowaniu = 0;
        public string paleta_ds = "";
        TextBox[] TextBoxArray = null;
        TextBox AktualnyTextBox = null;
        private Thread Skanowanie;




        public SztukiOpakowania(string ilosc, string ilosc_plan, string ilosc_w_opak,string kod_local,string kod_nazwa_local)
        {
            InitializeComponent();
            //textBox1.Text = ilosc.ToString();
            
            maximum = Convert.ToDouble(ilosc);
            textBox1.Text = ilosc_plan;
            kod.Text = kod_local;
            kod_nazwa.Text = kod_nazwa_local;

            minimum = 0;

            try
            {
                ilosc_planowana = Convert.ToDouble(ilosc_plan);
                ilosc_w_opakowaniu = Convert.ToInt32(ilosc_w_opak);
                textBox2.Text = (ilosc_planowana / ilosc_w_opakowaniu).ToString();
                //MessageBox.Show("6");
            }
            catch (Exception e)
            {
                MessageBox.Show(e.Message);
            }


            

            TextBoxArray = new TextBox[] { sztuki_real, opak_real };
            paleta_id.Focus();
            //label1.Text = "Ilość pobierana z ilości " + ilosc;
            //opak_real.Focus();
        }

        private void button1_Click(object sender, EventArgs e)
        {
            //this.DialogResult = DialogResult.Cancel;
            //return "przerwij";
        }

        private void Dalej_Click(object sender, EventArgs e)
        {
            //if (Convert.ToDouble(sztuki_real.Text) <= minimum || Convert.ToDouble(sztuki_real.Text) >= maximum)
            //{                
            //    komunikat = "Błędna ilość";
            //}
            if (paleta_id.Text=="")
            {
                MessageBox.Show("Wczytaj paletę na którą należy przepakować !!!");
                return;
            }


            if (Convert.ToDouble(sztuki_real.Text) <= minimum || Convert.ToDouble(sztuki_real.Text) >= maximum)
            {
                MessageBox.Show("Błędna ilość !!!");
                return;
            }


            //if (Convert.ToDouble(sztuki_real.Text) >= maximum)
            //{
            //    komunikat = "Nie można przepakować na maksymalną ilość etykiety lub więcej";
            //}

            if (Convert.ToDouble(sztuki_real.Text) >= maximum)
            {
                MessageBox.Show("Nie można przepakować na maksymalną ilość etykiety lub więcej !!!");
                return;
            }

            if (textBox1.Text != sztuki_real.Text)
            {
                MessageBox.Show("Ilość pobierana jest różna niż zamawiana !!!");
                return;
            }

            
            
            ilosc_wpisana = Convert.ToDouble(sztuki_real.Text);
            paleta_ds = paleta_id.Text.Replace("DS", "");

            this.DialogResult = DialogResult.OK;
        }

        private void WyborBazy_Load(object sender, EventArgs e)
        {
            
            
        }

        private void tylko_numery(object sender, EventArgs e)
        {
            //if (System.Text.RegularExpressions.Regex.IsMatch(((TextBox)sender).Text, "[^0-9]"))
            if (System.Text.RegularExpressions.Regex.IsMatch(((TextBox)sender).Text, "[^0-9].[^0-9]"))
            {
                MessageBox.Show("Tylko liczby.");
                ((TextBox)sender).Text = ((TextBox)sender).Text.Remove(((TextBox)sender).Text.Length - 1, 1);
            }
            TextBox Pole_Tekstowe = (TextBox)sender;

           
            if (opak_real == Pole_Tekstowe)
            {
                if (opak_real.Text == "" || opak_real.Text == "0")
                {
                    return;
                }
                sztuki_real.Text = (ilosc_w_opakowaniu * Convert.ToInt32(opak_real.Text)).ToString();
                
            }
        }
        private void GotFocus_TextBox(object sender, EventArgs e)
        {
            TextBox Pole_Tekstowe = (TextBox)sender;

            AktualnyTextBox = Pole_Tekstowe;

            ZacznijSkanowanie();
        }

        private void LostFocus_TextBox(object sender, EventArgs e)
        {
            AktualnyTextBox = null;
            Skaner.Przewij_Skanowanie();
        }

        private void ZacznijSkanowanie()
        {
            StringBuilder login = new StringBuilder();
            Skaner.UstawTryb_String(login);
            Skaner.Zacznij_Skanowanie();
            this.Skanowanie = new Thread(() => this.CheckString(login));
            this.Skanowanie.IsBackground = true;
            this.Skanowanie.Start();
        }

        private void CheckString(StringBuilder cc)
        {
            EventHandler method = null;
            while (true)
            {
                if (cc.ToString() != "")
                {
                    if (method == null)
                    {
                        method = delegate
                        {
                            //this.vvv();                            
                            this.dodawanie(cc.ToString());
                        };
                        //method = () => this.dodawanie(cc.ToString());
                    }
                    this.Invoke(method);
                    return;
                }
                Thread.Sleep(200);
            }
        }


        private void dodawanie(string ops)
        {

            Skaner.Przewij_Skanowanie();
            //MessageBox.Show("1");



            if (AktualnyTextBox == null)
            {
                MessageBox.Show("Wybierz wczytanie");
                return;
            }
            AktualnyTextBox.Text = ops;
            opak_real.Focus();
        }
    }

    
}