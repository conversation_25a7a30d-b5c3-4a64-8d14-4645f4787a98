# Dokumentacja funkcjonalności prz<PERSON><PERSON><PERSON><PERSON> magazy<PERSON>wych (WMS) - C<PERSON><PERSON>ść 4

## 6. LOGIKA BIZNESOWA

### Reguły walidacji

1. **Etykieta musi istnieć w systemie**
   - Zapytanie sprawdzające istnienie etykiety w tabeli etykiety
   - Warunki: id = etykieta OR etykieta_klient = etykieta OR paleta_id = etykieta.Replace("DS", "")

2. **Status etykiety nie może blokować wydania**
   - Sprawdzenie czy status_system.funkcja_stat != "blokada_wyd" lub regal = "PROD"

3. **Etykieta nie może być przygotowana na wysyłkę**
   - Sprawdzenie czy delivery_nr jest puste i dlcollect jest puste

4. **Miejsce docelowe musi istnieć w systemie**
   - Sprawdzenie istnienia miejsca w tabeli miejsca
   - Warunki: hala, regal, mi<PERSON><PERSON><PERSON>, poziom odpowiadają miejscu i miejsca.widoczne = 1

5. **Miejsce docelowe nie może być zajęte**
   - Opcjonalne sprawdzenie czy miejsce nie jest zajęte przez inną etykietę
   - Warunki: Brak etykiet z active=1 przypisanych do danego miejsca

### Algorytmy

1. **Podpowiadanie miejsc**
   - Na podstawie grupy produktu (grupa_id) system sugeruje odpowiednie miejsca
   - Preferowane są miejsca w tym samym regale (regal_ostatnio)
   - Sortowanie ASC/DESC zależne od numeru miejsca (miejsce <= 70 -> ASC, miejsce > 70 -> ASC)

2. **Obsługa różnych formatów etykiet**
   - Etykiety zaczynające się od "DS" - obsługa przez paleta_id
   - Pozostałe etykiety - obsługa przez id lub etykieta_klient

### Automatyczne akcje

1. **Zapis do historii skanowania**
   - Przy każdym skanowaniu zapis do tabeli historia_skanowania

2. **Zapis do tabeli zmiany_miejsca_niezrealizowane**
   - Tymczasowy zapis etykiety w procesie zmiany miejsca

## 7. INTEGRACJE

### Integracja z API

- **Endpoint**: zmiana_miejsca_regal_manual.php
- **Parametry**:
  - akcja=realizacja_zmiany
  - pracownik_id
  - imie_nazwisko
  - operac_id
  - wozek
  - system_id
  - miejsce_id
  - zm_nr
  - etykieta_scan
  - id_niezrealizowane
- **Format odpowiedzi**: XML z węzłami:
  - komunikat (OK lub komunikat błędu)
  - licznik
  - last_result_text
  - zm_nr
  - operac_id
  - ile_dokument
  - ile_wstawione

### Komunikacja z serwerem

```csharp
public XmlNode KomunikacjaSerwerem(string sciezka)
{
    XmlNodeList xmlnode_local = null;
    XmlNode node_local = null;
    XmlDocument doc1 = WebService.Pobierz_XmlDocument(sciezka);
    xmlnode_local = doc1.GetElementsByTagName("dane");

    foreach (XmlNode wynik in xmlnode_local)
    {
        node_local = wynik;
    }
    return node_local;
}
```

## 8. WZORCE I KONWENCJE

### Konwencje nazewnictwa

- **Zmienne lokalne**: snake_case z sufiksem _local (np. hala_local)
- **Zmienne globalne**: snake_case z sufiksem _global (np. operac_id_global)
- **Zmienne statyczne**: PascalCase lub snake_case (np. etykieta_ostatnia)
- **Metody**: PascalCase (np. KomunikacjaSerwerem)
- **Parametry**: snake_case (np. etykieta, miejsce_id)

### Struktura projektu

- **Formularze**: Klasy dziedziczące po System.Windows.Forms.Form
- **Usługi zewnętrzne**: Klasy statyczne (np. BazaDanychExternal, WebService)
- **Komunikacja z bazą**: Dedykowane metody (np. Wyczytaj_Jedna_Wartosc, Wyczytaj_Tabele, DokonajUpdate)

### Używane biblioteki i frameworki

- **System.Windows.Forms** - interfejs użytkownika
- **System.Xml** - obsługa XML
- **MySql.Data.MySqlClient** - komunikacja z bazą MySQL
- **System.Text.RegularExpressions** - obsługa wyrażeń regularnych
- **System.Net** - komunikacja sieciowa (WebRequest)

### Wzorce projektowe

- **Singleton** - klasy zarządzające połączeniem z bazą danych
- **Command** - operacje na bazie danych
- **Observer** - obsługa zdarzeń skanowania

## 9. PRZYKŁADY KODU

### Przykład starej metody (SQL)

```csharp
// Pobranie aktualnego miejsca etykiety
private static string[] miejsce_aktualne(string etykieta)
{
    string zapytanie = "select m.hala,m.regal,m.miejsce,m.poziom from etykiety e left join miejsca m on e.miejscep=m.id where (e.id='" + etykieta + "' OR e.etykieta_klient='" + etykieta + "' ) and (e.active=1 or e.active is null) order by e.id desc limit 1";
    if (etykieta.Substring(0, 2) == "DS")
    {
        zapytanie = "select m.hala,m.regal,m.miejsce,m.poziom from etykiety e left join miejsca m on e.miejscep=m.id where (e.paleta_id='" + etykieta.Replace("DS", "") + "') and (e.active=1 or e.active is null) order by e.id desc limit 1";
    }
    
    object obj2 = BazaDanychExternal.Wyczytaj_Tabele(zapytanie);
    DataTable tabela = (DataTable)obj2;
    string[] bb = new string[4];
    bb[0] = tabela.Rows[0]["hala"].ToString();
    bb[1] = tabela.Rows[0]["regal"].ToString();
    bb[2] = tabela.Rows[0]["miejsce"].ToString();
    bb[3] = tabela.Rows[0]["poziom"].ToString();
    return bb;
}
```

### Przykład nowej metody (API/XML)

```csharp
public void Realizuj_Zmiane_Miejsca(string etykieta, string zm_type, string zm_date, string login, string hala, string regal, string miejsce, string poziom, string paleta_id, string miejsce_id)
{
    typ_operacji = "ZM";

    if (regal == "POD") typ_operacji = "ZM_POD";

    XmlNode node_local2 = KomunikacjaSerwerem("zmiana_miejsca_regal_manual.php?akcja=realizacja_zmiany&pracownik_id=" + Wlasciwosci.id_Pracownika + "&imie_nazwisko=" + Uri.EscapeUriString(Wlasciwosci.imie_nazwisko) + "&operac_id=" + operac_id_global + "&wozek=" + Wlasciwosci.wozek + "&system_id=" + Wlasciwosci.system_id_id + "&miejsce_id=" + miejsce_id + "&zm_nr=" + zm_nr_global + "&etykieta_scan=" + etykieta + "&id_niezrealizowane=" + id_zmiany_miejsca_niezrealizowane);
    if (node_local2["komunikat"].InnerText != "OK")
    {
        MessageBox.Show(node_local2["komunikat"].InnerText);
    }
    else
    {
        label4.Text = "";
        etykieta_ostatnia = etykieta;
        etykieta_textbox.Text = "";
        licznik_label.Text = node_local2["licznik"].InnerText;
        last_result_text.Text = "Zmieniono: " + Environment.NewLine + node_local2["last_result_text"].InnerText;
        zm_nr_global = node_local2["zm_nr"].InnerText;
        operac_id_global = node_local2["operac_id"].InnerText;
        ilosc_etykiet.Text = node_local2["ile_dokument"].InnerText + "/" + node_local2["ile_wstawione"].InnerText;
    }
}
```

## 10. PODSUMOWANIE

Moduł przesunięć magazynowych systemu WMS dla skanerów mobilnych implementuje dwa podejścia do komunikacji:

1. **Stare podejście**: Bezpośrednie zapytania SQL do bazy danych
   - Zalety: Szybka realizacja, mniej warstw pośrednich
   - Wady: Ryzyko SQL injection, trudniejsza migracja na inną bazę danych, trudności w utrzymaniu

2. **Nowe podejście**: Komunikacja przez API z wykorzystaniem XML
   - Zalety: Większa separacja warstw, łatwiejsze utrzymanie, większa elastyczność
   - Wady: Dodatkowa warstwa pośrednicząca, większa złożoność

Funkcjonalność przesunięć magazynowych jest kluczowym elementem systemu WMS, umożliwiającym efektywne zarządzanie przestrzenią magazynową i optymalizację procesów logistycznych.
