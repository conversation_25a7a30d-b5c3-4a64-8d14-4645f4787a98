<?xml version="1.0"?>
<doc>
    <assembly>
        <name>Symbol.Fusion</name>
    </assembly>
    <members>
        <member name="T:Symbol.Fusion.WLAN.WLAN">
            <summary>
            Summary description for WLAN.
            </summary>
        </member>
        <member name="F:Symbol.Fusion.WLAN.WLAN.m_StatusThreadFusionHandle">
            <summary>
            Fusion handle used for Status Thread.
            </summary>
        </member>
        <member name="M:Symbol.Fusion.WLAN.WLAN.#ctor">
            <summary>
            WLAN Constructor
            </summary>
            <remarks>To avoid garbage collection delay issues, 
            Dispose the WLAN object when it is no longer needed.</remarks>
        </member>
        <member name="M:Symbol.Fusion.WLAN.WLAN.#ctor(Symbol.Fusion.FusionAccessType)">
            <summary>
            WLAN Constructor
            </summary>
            <remarks>To avoid garbage collection delay issues, Dispose 
            the WLAN object when it is no longer needed.</remarks>
            <param name="accessType">The mode to open fusion API</param>
        </member>
        <member name="M:Symbol.Fusion.WLAN.WLAN.Initialize">
            <summary>
            Initialize object in constructor
            </summary>
        </member>
        <member name="M:Symbol.Fusion.WLAN.WLAN.Finalize">
            <summary>
            
            </summary>
        </member>
        <member name="M:Symbol.Fusion.WLAN.WLAN.Dispose">
            <summary>
            This method must be called when a WLAN object is no longer needed
            </summary>
        </member>
        <member name="M:Symbol.Fusion.WLAN.WLAN.Dispose(System.Boolean)">
            <summary>
            dispose resources
            </summary>
        </member>
        <member name="M:Symbol.Fusion.WLAN.WLAN.CancellConnectionPersistance">
            <summary>
            Allow for profile roaming by cancelling connection persistancy. Available in Command Mode only.
            </summary>
            <returns>Returns an encoding of the API result</returns>
        </member>
        <member name="M:Symbol.Fusion.WLAN.WLAN.GenerateStatusEvents">
            <summary>
            Generate status events for this object
            </summary>
        </member>
        <member name="M:Symbol.Fusion.WLAN.WLAN.GenerateStatusEvents(System.Object)">
            <summary>
            Generate status events for all WLAN adapters
            callback that generate status events
            </summary>
            <param name="status"></param>
        </member>
        <member name="M:Symbol.Fusion.WLAN.WLAN.FireStatusEvents(Symbol.Fusion.WLAN.StatusChangeEventArgs)">
            <summary>
            Other classes should call this function to fire events on their status changes.
            </summary>
            <param name="statusChange">Object containing the Changed data</param>
        </member>
        <member name="M:Symbol.Fusion.WLAN.WLAN.OnStatusChange(System.EventArgs)">
            <summary>
            Called by MessageWindow class to notify UI thread of status change. 
            </summary>
        </member>
        <member name="M:Symbol.Fusion.WLAN.WLAN.InitializeAdapterPowerMonitors">
            <summary>
            Initialize power monitors in adapters.
            </summary>
        </member>
        <member name="M:Symbol.Fusion.WLAN.WLAN.InitializeAccesspointMonitors">
            <summary>
            Initialize accesspoint/BSSID monitors in adapters.
            </summary>
        </member>
        <member name="P:Symbol.Fusion.WLAN.WLAN.Adapters">
            <summary>
            A list of WLAN Adapters that could be enumerated. 
            </summary>
        </member>
        <member name="P:Symbol.Fusion.WLAN.WLAN.Profiles">
            <summary>
            A list of WLAN Profiles that could be enumerated. 
            </summary>
        </member>
        <member name="P:Symbol.Fusion.WLAN.WLAN.SignalQualityEventHandlersAdded">
            <summary>
            This flag is set to true when a SignalQuality event handler is added but not set to false
            when a SignalQuality event handler is removed. Should be set to false when all SignalQuality event 
            handlers are removed - this is costly to check.
            </summary>
        </member>
        <member name="P:Symbol.Fusion.WLAN.WLAN.Config">
            <summary>
            Import and export configuration information
            Not supported
            </summary>
        </member>
        <member name="P:Symbol.Fusion.WLAN.WLAN.EventResolution">
            <summary>
            Define the resolution of SignalQuality Event notification in milli seconds
            </summary>
            <remarks>The default value is 2000 milli seconds. The minimum resolution possible is 250 milli seconds</remarks>
        </member>
        <member name="P:Symbol.Fusion.WLAN.WLAN.StatusEventResolution">
            <summary>
            Define the resolution of Status Event notification in milli seconds
            </summary>
            <remarks>The default value is 1000 milli seconds. The minimum resolution possible is 250 milli seconds</remarks>
        </member>
        <member name="P:Symbol.Fusion.WLAN.WLAN.AccessType">
            <summary>
            Define the mode that the connection with the 
            Fusion driver APIs is established by this object.
            </summary>
        </member>
        <member name="E:Symbol.Fusion.WLAN.WLAN.StatusChanged">
            <summary>
            Attach to this StatusChange event to be notified of status changes in the WLAN. 
            </summary>
        </member>
        <member name="P:Symbol.Fusion.WLAN.WLAN.Monitor">
            <summary>
            Enable/Disable Monitoring of Status Changes on selected parameters.
            Monitoring is Disabled by default.
            </summary>
            <remarks>Status Monitoring must be Enabled for <see cref="E:Symbol.Fusion.WLAN.WLAN.StatusChanged"/>
            event notification on a selected parameter.</remarks>
        </member>
        <member name="T:Symbol.Fusion.WLAN.WLAN.StatusChangeHandler">
            <summary>
            A delegate object used for providing notification to the application when a status change occurs
            </summary>
        </member>
        <member name="T:Symbol.Fusion.WLAN.WLAN.MonitorSettings">
            <summary>
            This class is used to Enable/Disable Monitoring changes in state of selected
            parameters and firing <see cref="E:Symbol.Fusion.WLAN.WLAN.StatusChanged"/> event notifications when
            a changes occur.
            </summary>
        </member>
        <member name="P:Symbol.Fusion.WLAN.WLAN.MonitorSettings.AdapterPower">
            <summary>
            Enable/Disable monitoring of Adapter Power changes.
            </summary>
            <value>Default value is false.</value>
        </member>
        <member name="P:Symbol.Fusion.WLAN.WLAN.MonitorSettings.AccessPoint">
            <summary>
            Enable/Disable monitoring the accesspoint/BSSID.
            </summary>
            <value>Default value is false.</value>
        </member>
        <member name="T:Symbol.Fusion.WLAN.UserCredsOverride">
            <summary>
            The class for overriding the user credentials. 
            The credentials specified will be used on every subsequent credential validation.
            </summary>
        </member>
        <member name="M:Symbol.Fusion.WLAN.UserCredsOverride.Finalize">
            <summary>
            The finalizer for UserCredsOverride.
            </summary>
        </member>
        <member name="M:Symbol.Fusion.WLAN.UserCredsOverride.Override(System.Boolean,System.Boolean)">
            <summary>
            Overrides WLAN credentials. 
            The credentials specified will be used on every subsequent credential validation.
            Available in Command Mode only.
            </summary>
            <param name="bPromptForCredentials">Flag that indicates whether to prompt for user credentials or not. (Future)</param>
            <param name="bProfileCredentialsPrecedence">Flag that indicates whether the provided credential information overrides the existing profile credential information. (Future)</param>
        </member>
        <member name="M:Symbol.Fusion.WLAN.UserCredsOverride.Reset">
            <summary>
            Resets user WLAN credentials. Available in Command Mode only.
            </summary>
        </member>
        <member name="P:Symbol.Fusion.WLAN.UserCredsOverride.UserCreds">
            <summary>
            The overriding credentials.
            </summary>
        </member>
        <member name="T:Symbol.Fusion.FusionMarshal">
            <summary>
            Marshals the Fusion level (Top level) API interface
            </summary>
        </member>
        <member name="M:Symbol.Fusion.FusionMarshal.OpenFusion(Symbol.Fusion.FusionAccessType)">
            <summary>
            Open a connection to fusion driver in the specified access mode.
            </summary>
            <param name="accessType">The mode to open the connection</param>
        </member>
        <member name="M:Symbol.Fusion.FusionMarshal.OpenStatisticsMode">
            <summary>
            Open a connection to fusion drivers in statistics mode
            </summary>
        </member>
        <member name="M:Symbol.Fusion.FusionMarshal.OpenCommandMode">
            <summary>
            Open a connection to fusion drivers in command mode
            </summary>
        </member>
        <member name="M:Symbol.Fusion.FusionMarshal.OpenFusion(Symbol.Fusion.FINTRF_CONTEXTHANDLE@)">
            <summary>
            Open fusion in stastics mode
            </summary>
            <param name="handle">A pointer to the handle that will be opened</param>
        </member>
        <member name="M:Symbol.Fusion.FusionMarshal.CloseFusion(Symbol.Fusion.FusionAccessType)">
            <summary>
            Close the connection to the fusion driver
            </summary>
            <param name="accessType">The mode of the connection to close</param>
        </member>
        <member name="M:Symbol.Fusion.FusionMarshal.CloseStatisticsMode">
            <summary>
            Close a statistics mode connection to the fusion driver
            </summary>
        </member>
        <member name="M:Symbol.Fusion.FusionMarshal.CloseCommandMode">
            <summary>
            Close a command mode connection to the fusion driver
            </summary>
        </member>
        <member name="M:Symbol.Fusion.FusionMarshal.CloseFusion(Symbol.Fusion.FINTRF_CONTEXTHANDLE)">
            <summary>
            Close the given handle.
            </summary>
            <param name="handle"></param>
        </member>
        <member name="M:Symbol.Fusion.FusionMarshal.GetHandle(Symbol.Fusion.FusionAccessType)">
            <summary>
            Get the requested fusion handle
            </summary>
            <returns>Returns the command mode handle or the statistics mode handle</returns>
        </member>
        <member name="M:Symbol.Fusion.FusionMarshal.GetVersion(Symbol.Fusion.FINTRF_CONTEXTHANDLE@,Symbol.Fusion.AccessVersionInfoList)">
            <summary>
            Marshal the Fusion get version call
            </summary>
            <param name="fusionHandle">A handle to fusion API</param>
            <param name="versionInfoList"></param>
        </member>
        <member name="M:Symbol.Fusion.FusionMarshal.GetVersionsEx(Symbol.Fusion.FINTRF_CONTEXTHANDLE@,Symbol.Fusion.AccessVersionExList,Symbol.Fusion.VersionExComponentID)">
            <summary>
            Marshal the FusionInterface call related to retrieving the extended version information.
            </summary>
            <param name="fusionHandle">A handle to fusion API</param>
            <param name="versionsExList"></param>
            <param name="nComponentID"></param>
        </member>
        <member name="M:Symbol.Fusion.FusionMarshal.DataExportWLANOptions(Symbol.Fusion.FINTRF_CONTEXTHANDLE@,Symbol.Fusion.FINTRF_ExportImportOptions@)">
            <summary>
            Marshal the FusionInterface DataExportWLANOptions.
            </summary>
        </member>
        <member name="M:Symbol.Fusion.FusionMarshal.DataImportWLANOptions(Symbol.Fusion.FINTRF_CONTEXTHANDLE@,Symbol.Fusion.FINTRF_ExportImportOptions@)">
            <summary>
            Marshal the FusionInterface DataImportWLANOptions.
            </summary>
        </member>
        <member name="M:Symbol.Fusion.FusionMarshal.SaveDataStructModeText(Symbol.Fusion.FINTRF_CONTEXTHANDLE@,System.String)">
            <summary>
            Marshal the FusionInterface SaveDataStructModeText.
            </summary>
        </member>
        <member name="M:Symbol.Fusion.FusionMarshal.AccessControlAll(Symbol.Fusion.FINTRF_CONTEXTHANDLE@,Symbol.Fusion.FINTRF_AccessControl@)">
            <summary>
            Marshal the FusionInterface AccessControlAll.
            </summary>
        </member>
        <member name="M:Symbol.Fusion.FusionMarshal.ImportPAC(Symbol.Fusion.FINTRF_CONTEXTHANDLE@,Symbol.Fusion.FINTRF_ImportPAC@)">
            <summary>
            Marshal the FusionInterface ImportPAC.
            </summary>
        </member>
        <member name="M:Symbol.Fusion.FusionMarshal.ResetOptions(Symbol.Fusion.FINTRF_CONTEXTHANDLE@)">
            <summary>
            Reset options to factory settings (default values). 
            This feature is supported on Fusion versions X_2.00 and newer. 
            Supported only in the command mode.
            </summary>
        </member>
        <member name="M:Symbol.Fusion.FusionMarshal.ResetDataStore(Symbol.Fusion.FINTRF_CONTEXTHANDLE@)">
            <summary>
            Reset Fusion datastore to factory settings (default values).
            Fusion datastore includes profiles, options, PACs and certificates. 
            This feature is supported on Fusion versions X_2.00 and newer. 
            Supported only in the command mode.
            </summary>
        </member>
        <member name="M:Symbol.Fusion.FusionMarshal.SaveWirelessLog(Symbol.Fusion.FINTRF_CONTEXTHANDLE@,System.String)">
            <summary>
            Save diagnostics information to the file
            </summary>
            <param name="fusionHandle">The handle to fusion API</param>
            <param name="fileName">Name of the file to write</param>
            <returns>Returns an encoding of the API result</returns>
        </member>
        <member name="M:Symbol.Fusion.FusionMarshal.ClearWirelessLog(Symbol.Fusion.FINTRF_CONTEXTHANDLE@)">
            <summary>
            Clear the wireless log buffer
            </summary>
            <param name="fusionHandle">The handle to fusion API</param>
            <returns>Returns an encoding of the API result</returns>
        </member>
        <member name="M:Symbol.Fusion.FusionMarshal.SetWirelessLogSize(Symbol.Fusion.FINTRF_CONTEXTHANDLE@,System.Int32)">
            <summary>
            Set the file size of the log file
            </summary>
            <param name="fusionHandle">The handle to fusion API</param>
            <param name="fileSize">The size of the file to write</param>
        </member>
        <member name="M:Symbol.Fusion.FusionMarshal.CheckResults(Symbol.Fusion.FINTRF_CONTEXTHANDLE@,Symbol.Fusion.FusionResults,System.String)">
            <summary>
            This function checks the error state and throws exceptions if needed
            </summary>
            <param name="result">the result to check</param>
            <param name="fusionHandle">The handle to fusion API</param>
            <param name="nativeFunction">the native function that caused the result</param>
        </member>
        <member name="M:Symbol.Fusion.AccessVersionInfoList.getRadionDriver">
            <summary>
            Returns the radio driver version
            </summary>
            <returns></returns>
        </member>
        <member name="M:Symbol.Fusion.AccessVersionInfoList.AllocMarshalPtr">
            <summary>
            Creates a native buffer for use. 
            The native buffer size is based on the MarshalSize property.
            </summary>
            <remarks>
            If the allocation fails the IntPtr is still returned, but the value it contains
            will be zero. (i.e. null)
            </remarks>
            <returns>An IntPtr that points to the new native buffer.</returns>
        </member>
        <member name="M:Symbol.Fusion.AccessVersionInfoList.FreeMarshalPtr(System.IntPtr)">
            <summary>
            Frees the native buffer of the marshal object.
            </summary>
            <param name="pPtr">An IntPtr that points to the native buffer.</param>
        </member>
        <member name="M:Symbol.Fusion.AccessVersionInfoList.UnmarshalFrom(System.IntPtr,System.Int32)">
            <summary>
            Unmarshals the data from the marshal object.
            </summary>
            <param name="pPtr">An IntPtr that points to the native buffer.</param>
            <param name="nOffset">Tbe offset of the data to be unmarshalled.</param>
        </member>
        <member name="M:Symbol.Fusion.AccessVersionExList.AllocMarshalPtr">
            <summary>
            Creates a native buffer for use. 
            The native buffer size is based on the MarshalSize property.
            </summary>
            <remarks>
            If the allocation fails the IntPtr is still returned, but the value it contains
            will be zero. (i.e. null)
            </remarks>
            <returns>An IntPtr that points to the new native buffer.</returns>
        </member>
        <member name="M:Symbol.Fusion.AccessVersionExList.FreeMarshalPtr(System.IntPtr)">
            <summary>
            Frees the native buffer of the marshal object.
            </summary>
            <param name="pPtr">An IntPtr that points to the native buffer.</param>
        </member>
        <member name="M:Symbol.Fusion.AccessVersionExList.UnmarshalFrom(System.IntPtr,System.Int32)">
            <summary>
            Unmarshals the data from the marshal object.
            </summary>
            <param name="pPtr">An IntPtr that points to the native buffer.</param>
            <param name="nOffset">Tbe offset of the data to be unmarshalled.</param>
        </member>
        <member name="T:Symbol.Fusion.WLAN.Config">
            <summary>
            Import and export configuration information.
            Not supported.
            </summary>
        </member>
        <member name="M:Symbol.Fusion.WLAN.Config.Export(System.String,System.String,Symbol.Fusion.WLAN.Config.FileFormat)">
            <summary>
            Export config information to a file
            </summary>
            <param name="filePath">The destination directory</param>
            <param name="fileName">The name of the file to export config data to</param>
            <param name="fileFormat">The format to use in writing the file</param>
        </member>
        <member name="M:Symbol.Fusion.WLAN.Config.Import(System.String,System.String,Symbol.Fusion.WLAN.Config.FileFormat)">
            <summary>
            Import config information from a file
            </summary>
            <param name="filePath">The source directory</param>
            <param name="fileName">The name of the file to import config data from</param>
            <param name="fileFormat">The format to use in reading the file</param>
        </member>
        <member name="T:Symbol.Fusion.WLAN.Config.FileFormat">
            <summary>
            This enumeration is used to specify the file format to use in Export and
            Import operations of config information to and from a file.
            </summary>
        </member>
        <member name="F:Symbol.Fusion.WLAN.Config.FileFormat.Text">
            <summary>
            In Text mode
            </summary>
        </member>
        <member name="F:Symbol.Fusion.WLAN.Config.FileFormat.XML">
            <summary>
            As XML format
            </summary>
        </member>
        <member name="T:Symbol.Fusion.WLAN.Profiles">
            <summary>
            Summary description for Profiles.
            </summary>
        </member>
        <member name="M:Symbol.Fusion.WLAN.Profiles.Dispose">
            <summary>
            Called during WLAN dispose. Used to make the cached data not accessible
            </summary>
        </member>
        <member name="M:Symbol.Fusion.WLAN.Profiles.Refresh">
            <summary>
            Refresh the profiles list. Call this function to read profile information from
            the device and to update profiles list.
            </summary>
            <remarks>This function is called by the following functions. <see cref="M:Symbol.Fusion.WLAN.Profiles.DeleteProfile(Symbol.Fusion.WLAN.Profile)"/>,
            <see cref="M:Symbol.Fusion.WLAN.Profiles.DeleteAll"/>,  
            <see cref="M:Symbol.Fusion.WLAN.Profiles.CreateInfrastructureProfile(Symbol.Fusion.WLAN.InfrastructureProfileData)"/>, <see cref="M:Symbol.Fusion.WLAN.Profiles.CreateAdhocProfile(Symbol.Fusion.WLAN.AdhocProfileData)"/>,
            </remarks>
        </member>
        <member name="M:Symbol.Fusion.WLAN.Profiles.CreateAdhocProfile(Symbol.Fusion.WLAN.AdhocProfileData)">
            <summary>
            Create an adhoc profile. Available in Command Mode only.
            </summary>
            <param name="adhocProfileData">Define data used to create the profile</param>
            <returns>Returns the created profile object</returns>
        </member>
        <member name="M:Symbol.Fusion.WLAN.Profiles.CreateInfrastructureProfile(Symbol.Fusion.WLAN.InfrastructureProfileData)">
            <summary>
            Create an infrastructure profile. Available in Command Mode only.
            </summary>
            <param name="infrastructureProfileData">Define data used to create the profile</param>
            <returns>Returns the created profile object</returns>
        </member>
        <member name="M:Symbol.Fusion.WLAN.Profiles.DeleteProfile(Symbol.Fusion.WLAN.Profile)">
            <summary>
            Delete the specified profile. Available in Command Mode only.
            </summary>
            <param name="profile">Profile to delete</param>
            <remarks>This function calls <see cref="M:Symbol.Fusion.WLAN.Profiles.Refresh"/> method on successful deletion 
            of a profile.</remarks>
            <returns>Returns an encoding of the API result</returns>
        </member>
        <member name="M:Symbol.Fusion.WLAN.Profiles.DeleteAll">
            <summary>
            Delete all profiles. Available in Command Mode only.
            </summary>
            <remarks>This function calls <see cref="M:Symbol.Fusion.WLAN.Profiles.Refresh"/> method on successful deletion 
            of a profile.</remarks>
            <returns>Returns an encoding of the API result</returns>
        </member>
        <member name="M:Symbol.Fusion.WLAN.Profiles.SetPriority(Symbol.Fusion.WLAN.Profile,Symbol.Fusion.WLAN.Profiles.SSIDPriority)">
            <summary>
            Change profile priority. Available in Command Mode only.
            </summary>
            <param name="priorityChange">The change to be done</param>
            <param name="profile">The profile to change priority</param>		
            <remarks><see cref="M:Symbol.Fusion.WLAN.Profiles.Refresh"/> is called by this function
            to get the new priority values using <see cref="P:Symbol.Fusion.WLAN.Profile.Priority"/></remarks>
        </member>
        <member name="M:Symbol.Fusion.WLAN.Profiles.ExportAll(Symbol.Fusion.FileFormat,Symbol.Fusion.WLAN.Profiles.ExportImportOpMode,System.String,System.String)">
            <summary>
            Exports all the profiles.
            </summary>
        </member>
        <member name="M:Symbol.Fusion.WLAN.Profiles.Import(Symbol.Fusion.FileFormat,Symbol.Fusion.WLAN.Profiles.ExportImportOpMode,System.String,System.String)">
            <summary>
            Import profiles. Available in Command Mode only.
            </summary>
        </member>
        <member name="P:Symbol.Fusion.WLAN.Profiles.Length">
            <summary>
            The length of the Profiles list
            </summary>
        </member>
        <member name="P:Symbol.Fusion.WLAN.Profiles.Item(System.Int32)">
            <summary>
            The indexer provided to enumerate Profile objects in the list.
            </summary>
        </member>
        <member name="P:Symbol.Fusion.WLAN.Profiles.Item(System.String)">
            <summary>
            Get the profile with the given profile ID
            </summary>
        </member>
        <member name="T:Symbol.Fusion.WLAN.Profiles.SSIDPriority">
            <summary>
            Defines Priority changes
            </summary>
        </member>
        <member name="F:Symbol.Fusion.WLAN.Profiles.SSIDPriority.NO_CHANGE">
            <summary>
            Priority kept unchanged
            </summary>
        </member>
        <member name="F:Symbol.Fusion.WLAN.Profiles.SSIDPriority.HIGHEST">
            <summary>
            Set priority to the highest
            </summary>
        </member>
        <member name="F:Symbol.Fusion.WLAN.Profiles.SSIDPriority.LOWEST">
            <summary>
            Set priority to the lowest
            </summary>
        </member>
        <member name="F:Symbol.Fusion.WLAN.Profiles.SSIDPriority.MOVEUP">
            <summary>
            Increase priority by one level
            </summary>
        </member>
        <member name="F:Symbol.Fusion.WLAN.Profiles.SSIDPriority.MOVEDOWN">
            <summary>
            Decrease priority by one level
            </summary>
        </member>
        <!-- Badly formed XML comment ignored for member "T:Symbol.Fusion.WLAN.Profiles.ExportImportOpMode" -->
        <member name="F:Symbol.Fusion.WLAN.Profiles.ExportImportOpMode.SINGLE_PROFILE">
            <summary>
            Import a profile from a single file. Unlike the other 2 members in this enumeration, this is not a 
            valid option in importing multiple profiles. Only valid in case of importing a single profile 
            (i.e. one previously exported via Symbol.Fusion.WLAN.Profile.Export(...)).
            </summary>
        </member>
        <member name="F:Symbol.Fusion.WLAN.Profiles.ExportImportOpMode.MULTIPLE_PROFILE">
            <summary>
            Export/Import profiles to/from multiple files (one for each profile).
            </summary>
        </member>
        <member name="F:Symbol.Fusion.WLAN.Profiles.ExportImportOpMode.MULTIPLE_PROFILE_CMRULES">
            <summary>
            Export/Import profiles to/from a single file with CM rules.
            </summary>
        </member>
        <member name="T:Symbol.Fusion.WLAN.Profile">
            <summary>
            Define a WLAN Profile
            </summary>
        </member>
        <member name="M:Symbol.Fusion.WLAN.Profile.Dispose">
            <summary>
            Called during WLAN dispose, and Profiles.Refresh() Used to make the cached data not accessible
            </summary>
        </member>
        <member name="M:Symbol.Fusion.WLAN.Profile.Connect(System.Boolean)">
            <summary>
            Make a connection using this profile. Available in Command Mode only.
            </summary>
            <param name="persistant">make the connection persistant if set to true</param>
            <returns>Returns an encoding of the API result</returns>
        </member>
        <member name="M:Symbol.Fusion.WLAN.Profile.Export(Symbol.Fusion.FileFormat,System.String,System.String)">
            <summary>
            Exports a single profile.
            </summary>
        </member>
        <member name="P:Symbol.Fusion.WLAN.Profile.Name">
            <summary>
            Name of the profile
            </summary>
        </member>
        <member name="P:Symbol.Fusion.WLAN.Profile.ProfileID">
            <summary>
            The unique ID of the profile
            </summary>
        </member>
        <member name="P:Symbol.Fusion.WLAN.Profile.ProfileType">
            <summary>
            Type of the profile
            </summary>
        </member>
        <member name="P:Symbol.Fusion.WLAN.Profile.Priority">
            <summary>
            The priority of the profile.
            </summary>
        </member>
        <member name="P:Symbol.Fusion.WLAN.Profile.Enabled">
            <summary>
            Whether the profile is enabled
            </summary>
        </member>
        <member name="P:Symbol.Fusion.WLAN.Profile.CurrentAdapter">
            <summary>
            Adapter attached to this profile
            </summary>
        </member>
        <member name="T:Symbol.Fusion.WLAN.Profile.OperationalStatus">
            <summary>
            Set Enabled or disabled state of the profile
            </summary>
        </member>
        <member name="F:Symbol.Fusion.WLAN.Profile.OperationalStatus.SET_NO_CHANGE">
            <summary>
            Used for setting Operational Status  from the API
            </summary>
        </member>
        <member name="F:Symbol.Fusion.WLAN.Profile.OperationalStatus.SET_DISABLED">
            <summary>
            Used for setting Operational Status from the API 
            </summary>
        </member>
        <member name="F:Symbol.Fusion.WLAN.Profile.OperationalStatus.SET_ENABLED">
            <summary>
            Used for setting Operational Status from the API
            </summary>
        </member>
        <member name="F:Symbol.Fusion.WLAN.Profile.OperationalStatus.GET_DISABLED">
            <summary>
            Used for getting Operational Status from the API
            </summary>
        </member>
        <member name="F:Symbol.Fusion.WLAN.Profile.OperationalStatus.GET_ENABLED">
            <summary>
            Used for getting Operational Status from the API
            </summary>
        </member>
        <member name="T:Symbol.Fusion.WLAN.Authentication">
            <summary>
            WLAN.Authentication class is provided to define the authentication type 
            used and data required for installation of credentials related to that 
            particular authentication type.
            </summary>
        </member>
        <member name="F:Symbol.Fusion.WLAN.Authentication.MAX_USERNAME_LENGTH">
            <summary>
            Maximum Length of a User Name - Typically Used as an Identity
            </summary>
        </member>
        <member name="F:Symbol.Fusion.WLAN.Authentication.FAPI_MAX_PASSWORD_LENGTH">
            <summary>
             Maximum Length of a Password
            </summary>
        </member>
        <member name="F:Symbol.Fusion.WLAN.Authentication.FAPI_MAX_DOMAINNAME_LENGTH">
            <summary>
             Maximum Length of a Network Domain Name
            </summary>
        </member>
        <member name="F:Symbol.Fusion.WLAN.Authentication.MAX_CERT_FNAME_LENGTH">
            <summary>
            Maximum certificate friendly name length
            </summary>
        </member>
        <member name="M:Symbol.Fusion.WLAN.Authentication.#ctor">
            <summary>
            Authentication constructor
            </summary>
        </member>
        <member name="M:Symbol.Fusion.WLAN.Authentication.IsValid">
            <summary>
            Check whether data in this class is complete and coherent
            </summary>
            <returns>Returns true if the data is valid. If the data is not valid an exception will be thrown with a proper text message.</returns>
            <remarks>This function is called by the assembly before creating a profile. In addition
            this function can be used by developers to verify the data after filling an Authentication
            object.</remarks>
        </member>
        <member name="M:Symbol.Fusion.WLAN.Authentication.setFlags">
            <summary>
            This function sets the flags in CredentialFlags
            </summary>
        </member>
        <member name="P:Symbol.Fusion.WLAN.Authentication.AuthenticationType">
            <summary>
            Defines the authentication type used
            </summary>
        </member>
        <member name="P:Symbol.Fusion.WLAN.Authentication.CredentialFlags">
            <summary>
            Set time options for credential checks.
            Possible options are dependant on the profile type that will be created
            using this authentication object.  
            </summary>
        </member>
        <member name="P:Symbol.Fusion.WLAN.Authentication.UserCertificate">
            <summary>
            Define user certificate
            </summary>
        </member>
        <member name="P:Symbol.Fusion.WLAN.Authentication.ServerCertificate">
            <summary>
            Define server certificate
            </summary>
        </member>
        <member name="P:Symbol.Fusion.WLAN.Authentication.IEE8021xCredentials">
            <summary>
            Tunneling credentials. Used with PEAP and TTLS Authentication types only.
            </summary>
        </member>
        <member name="P:Symbol.Fusion.WLAN.Authentication.UserCredentials">
            <summary>
            Define user credentials
            </summary>
        </member>
        <member name="P:Symbol.Fusion.WLAN.Authentication.CredentialPrompt">
            <summary>
            Define credential prompt. This should be defined if <see cref="P:Symbol.Fusion.WLAN.Authentication.LoginOperation"/>
            is set as <see cref="F:Symbol.Fusion.WLAN.Authentication.ProfileLoginOperation.MANUAL"/>.
            </summary>
        </member>
        <member name="P:Symbol.Fusion.WLAN.Authentication.LoginOperation">
            <summary>
            Define Automatic or Manual login  
            </summary>
        </member>
        <member name="P:Symbol.Fusion.WLAN.Authentication.UseTokenpasswd">
            <summary>
            Define whether the usage of token password is required along with GTC Tunneling.
            </summary>
        </member>
        <member name="T:Symbol.Fusion.WLAN.Authentication.UserCert">
            <summary>
            Define user certificates
            </summary>
        </member>
        <member name="F:Symbol.Fusion.WLAN.Authentication.UserCert.MAX_CERT_FNAME_LENGTH">
            <summary>
            Maximum certificate friendly name length
            </summary>
        </member>
        <member name="F:Symbol.Fusion.WLAN.Authentication.UserCert.MAX_REMOTE_USER_NAME_LEN">
            <summary>
            Maximum User Name Length for Certificate Installation From Remote Machine
            </summary>
        </member>
        <member name="F:Symbol.Fusion.WLAN.Authentication.UserCert.MAX_REMOTE_PASSWORD_LEN">
            <summary>
            Maximum Password Length 
            </summary>
        </member>
        <member name="F:Symbol.Fusion.WLAN.Authentication.UserCert.MAX_REMOTE_SERVER_NAME_LEN">
            <summary>
             Maximum Server Name Length
            </summary>
        </member>
        <member name="M:Symbol.Fusion.WLAN.Authentication.UserCert.IsValid">
            <summary>
            This function will determine whether the UserCertificate object is
            properly defined.
            </summary>
        </member>
        <member name="M:Symbol.Fusion.WLAN.Authentication.UserCert.IsSet">
            <summary>
            This function will determine whether the UserCertificate object has values set.
            </summary>
        </member>
        <member name="P:Symbol.Fusion.WLAN.Authentication.UserCert.CertificateType">
            <summary>
            Define whether using local or remote certificates to use. 
            </summary>
        </member>
        <member name="P:Symbol.Fusion.WLAN.Authentication.UserCert.Name">
            <summary>
            Defines a friendly name for the User Certificate
            </summary>
        </member>
        <member name="P:Symbol.Fusion.WLAN.Authentication.UserCert.RemoteUserName">
            <summary>
            Defines remote user name for installing user certificate from 
            remote server.
            </summary>
        </member>
        <member name="P:Symbol.Fusion.WLAN.Authentication.UserCert.RemotePassWord">
            <summary>
            Defines remote password for installing user certificate from 
            remote server.
            </summary>
        </member>
        <member name="P:Symbol.Fusion.WLAN.Authentication.UserCert.ServerName">
            <summary>
            Defines the server name from where to retrieve the certificates.
            </summary>
        </member>
        <member name="T:Symbol.Fusion.WLAN.Authentication.ServerCert">
            <summary>
            Define Server certificates
            </summary>
        </member>
        <member name="F:Symbol.Fusion.WLAN.Authentication.ServerCert.MAX_CERT_FNAME_LENGTH">
            <summary>
            Maximum certificate friendly name length
            </summary>
        </member>
        <member name="F:Symbol.Fusion.WLAN.Authentication.ServerCert.MAX_SERVER_CERT_PATH_LEN">
            <summary>
            Maximum server certificate path length
            </summary>
        </member>
        <member name="M:Symbol.Fusion.WLAN.Authentication.ServerCert.#ctor">
            <summary>
            Create a Server Certificate
            </summary>
        </member>
        <member name="M:Symbol.Fusion.WLAN.Authentication.ServerCert.IsValid">
            <summary>
            This function will determine whether the ServerCertificate object is
            properly defined.
            </summary>
        </member>
        <member name="M:Symbol.Fusion.WLAN.Authentication.ServerCert.IsSet">
            <summary>
            This function will determine whether the ServerCertificate object has values set.
            </summary>
        </member>
        <member name="P:Symbol.Fusion.WLAN.Authentication.ServerCert.CertificateType">
            <summary>
            Define whether using local or remote certificates. 
            </summary>
        </member>
        <member name="P:Symbol.Fusion.WLAN.Authentication.ServerCert.Name">
            <summary>
            Defines a friendly name for the Srever certificate
            </summary>
        </member>
        <member name="P:Symbol.Fusion.WLAN.Authentication.ServerCert.FilePath">
            <summary>
            Defines the path on machine where the server certificate file 
            is present.
            </summary>
        </member>
        <member name="P:Symbol.Fusion.WLAN.Authentication.ServerCert.Validate">
            <summary>
            Define whether ServerCertificate requires certificate 
            validation (if true) or not.
            </summary>
        </member>
        <member name="T:Symbol.Fusion.WLAN.Authentication.IEEE8021xCred">
            <summary>
            Defines Secure Tunnel Credentials. 
            </summary>
        </member>
        <member name="M:Symbol.Fusion.WLAN.Authentication.IEEE8021xCred.#ctor">
            <summary>
            IEEE8021xCred constructor
            </summary>
        </member>
        <member name="M:Symbol.Fusion.WLAN.Authentication.IEEE8021xCred.IsSet">
            <summary>
            Check whether values are set for this class
            </summary>
            <returns></returns>
        </member>
        <member name="P:Symbol.Fusion.WLAN.Authentication.IEEE8021xCred.UserID">
            <summary>
            Defines the 802.1x user identity for PEAP and TTLS authentication.
            </summary>
        </member>
        <member name="P:Symbol.Fusion.WLAN.Authentication.IEEE8021xCred.Domain">
            <summary>
            Defines the 802.1x domain for PEAP and TTLS authentication.
            </summary>
        </member>
        <member name="T:Symbol.Fusion.WLAN.Authentication.UserCred">
            <summary>
            Define User Credentials
            </summary>
        </member>
        <member name="M:Symbol.Fusion.WLAN.Authentication.UserCred.#ctor">
            <summary>
            User credential constructor
            </summary>
        </member>
        <member name="P:Symbol.Fusion.WLAN.Authentication.UserCred.UserName">
            <summary>
            Defines the user name of the credential.
            </summary>
        </member>
        <member name="P:Symbol.Fusion.WLAN.Authentication.UserCred.Domain">
            <summary>
            Defines the domain name of the credential.
            </summary>
        </member>
        <member name="P:Symbol.Fusion.WLAN.Authentication.UserCred.PassWord">
            <summary>
            Defines the password of the credential.
            </summary>
        </member>
        <member name="P:Symbol.Fusion.WLAN.Authentication.UserCred.UserCertHash">
            <summary>
            Defines the user certificate hash.
            Please note that setting this field by using Symbol.Fusion.WLAN.InfrastructureProfileData.Authentication.UserCredentials at the time of creating the infrastructure profiles is not expected.
            If this field UserCertHash is specified in that scenario, it would be ignored.
            This field UserCertHash is supposed to be set only when overriding the credentials by using Symbol.Fusion.WLAN.UserCredentials.UserCreds.
            Supported in Fusion 3.00.2.0.007R and later. Not supported in Fusion 3.20.
            An OperationFailureException would be thrown with its Result set to ERROR_NOT_SUPPORTED on the Fusion versions older than 3.0 and on Fusion 3.20.
            </summary>
        </member>
        <member name="T:Symbol.Fusion.WLAN.Authentication.CredentialPrmpt">
            <summary>
            Define Credential prompt
            </summary>
        </member>
        <member name="F:Symbol.Fusion.WLAN.Authentication.CredentialPrmpt.MAX_TIME_STR_LEN">
            <summary>
            Maximum Size of a Time String inclusive of NULL Terminator
            A Time Field is Given in hh:mm format
            </summary>
        </member>
        <member name="M:Symbol.Fusion.WLAN.Authentication.CredentialPrmpt.#ctor">
            <summary>
            Credential prompt constructor
            </summary>
        </member>
        <member name="M:Symbol.Fusion.WLAN.Authentication.CredentialPrmpt.IsSet">
            <summary>
            Returns true if valid data is set for this reference
            </summary>
            <returns></returns>
        </member>
        <member name="P:Symbol.Fusion.WLAN.Authentication.CredentialPrmpt.CacheOptions">
            <summary>
            Defines options that specify the times at which User Credentials 
            would be checked. 
            </summary>
        </member>
        <member name="P:Symbol.Fusion.WLAN.Authentication.CredentialPrmpt.TimeCacheOptions">
            <summary>
            Defines login settings for time triggered credential verifications
            </summary>
        </member>
        <member name="P:Symbol.Fusion.WLAN.Authentication.CredentialPrmpt.TimeInterval">
            <summary>
            defines the time interval in minutes that effects login for 
            credential cache. Once this interval expires the user will be 
            given a prompt to log back into the system.
            </summary>
        </member>
        <member name="P:Symbol.Fusion.WLAN.Authentication.CredentialPrmpt.FirstLoginPromptTime">
            <summary>
            Defines a specific time that the user will be prompted for a login. 
            The time is provided in a “hh:mm” string format (where hh is hour and 
            mm is minutes). (1 0f 4)
            </summary>
        </member>
        <member name="P:Symbol.Fusion.WLAN.Authentication.CredentialPrmpt.SecondLoginPromptTime">
            <summary>
            Defines a specific time that the user will be prompted for a login. 
            The time is provided in a “hh:mm” string format (where hh is hour and 
            mm is minutes). (2 of 4)
            </summary>
        </member>
        <member name="P:Symbol.Fusion.WLAN.Authentication.CredentialPrmpt.ThirdLoginPromptTime">
            <summary>
            Defines a specific time that the user will be prompted for a login. 
            The time is provided in a “hh:mm” string format (where hh is hour and 
            mm is minutes). (3 of 4)
            </summary>
        </member>
        <member name="P:Symbol.Fusion.WLAN.Authentication.CredentialPrmpt.FourthLoginPromptTime">
            <summary>
            Defines a specific time that the user will be prompted for a login. 
            The time is provided in a “hh:mm” string format (where hh is hour and 
            mm is minutes). (4 of 4)
            </summary>
        </member>
        <member name="T:Symbol.Fusion.WLAN.Authentication.CredentialPrmpt.CacheOpts">
            <summary>
            Credential verification times
            </summary>
        </member>
        <member name="F:Symbol.Fusion.WLAN.Authentication.CredentialPrmpt.CacheOpts.AtConnect">
            <summary>
            Credential verification will be during connection if set to true.
            </summary>
        </member>
        <member name="F:Symbol.Fusion.WLAN.Authentication.CredentialPrmpt.CacheOpts.AtResume">
            <summary>
            Credential verification will be done or resume after suspension
            </summary>
        </member>
        <member name="F:Symbol.Fusion.WLAN.Authentication.CredentialPrmpt.CacheOpts.AtGivenTime">
            <summary>
            Credential verification will be done at a predefined time
            </summary>
        </member>
        <member name="M:Symbol.Fusion.WLAN.Authentication.CredentialPrmpt.CacheOpts.ToInt">
            <summary>
            Returns an int representation of this class
            </summary>
            <returns></returns>
        </member>
        <member name="T:Symbol.Fusion.WLAN.Authentication.CredentialPrmpt.TimeCacheOpts">
            <summary>
            Time for login prompt for user credential varification
            </summary>
        </member>
        <member name="F:Symbol.Fusion.WLAN.Authentication.CredentialPrmpt.TimeCacheOpts.TIME_INTERVAL">
            <summary>
            verification done at specifide time intervals
            </summary>
        </member>
        <member name="F:Symbol.Fusion.WLAN.Authentication.CredentialPrmpt.TimeCacheOpts.ABSOLUTE_TIME">
            <summary>
            verification done at an absolute time
            </summary>
        </member>
        <member name="T:Symbol.Fusion.WLAN.Authentication.CredentialFlgs">
            <summary>
            Credential verification times
            </summary>
        </member>
        <member name="M:Symbol.Fusion.WLAN.Authentication.CredentialFlgs.Setbit(System.Boolean,System.UInt32)">
            <summary>
            Set or unset the bit specified
            </summary>
            <param name="setOrUnset">true to set</param>
            <param name="bit"></param>
        </member>
        <member name="M:Symbol.Fusion.WLAN.Authentication.CredentialFlgs.ToInt">
            <summary>
            Returns an int representation of this class
            </summary>
            <returns></returns>
        </member>
        <member name="P:Symbol.Fusion.WLAN.Authentication.CredentialFlgs.UseUserCertificate">
            <summary>
            If UseUserCertificate is set the USER CERTIFICATE will be 
            used in Authentication.
            </summary>
        </member>
        <member name="P:Symbol.Fusion.WLAN.Authentication.CredentialFlgs.SpecifyUserCertificateLocal">
            <summary>
            SpecifyUserCertificateLocal is set when the FRIENDLY NAME of 
            a local USER CERTIFICATE is specified.
            If it is not set then the FRIENDLY NAME of a remote USER CERTIFICATE 
            has to be specified with remote server credentials.
            </summary>
        </member>
        <member name="P:Symbol.Fusion.WLAN.Authentication.CredentialFlgs.SpecifyServerCertificateLocal">
            <summary>
            When SpecifyServerCertificateLocal is set the API is
            specifying the FRIENDLY NAME of a local SERVER CERTIFICATE
            conversely if it is not set then the API is specifying the 
            FRIENDLY NAME of the SERVER CERTIFICATE with the associated 
            SERVER CERTIFICATE file path.
            </summary>
        </member>
        <member name="P:Symbol.Fusion.WLAN.Authentication.CredentialFlgs.ValidateServerCertificate">
            <summary>
            When ValidateServerCertificate is set the API is 
            specifying that the SERVER CERTIFICATE requires certificate 
            validation.
            </summary>
        </member>
        <member name="T:Symbol.Fusion.WLAN.Authentication.AuthenticationTypes">
            <summary>
            Defines the authentication public used
            </summary>
        </member>
        <member name="F:Symbol.Fusion.WLAN.Authentication.AuthenticationTypes.NONE">
            <summary>
            No Authentication.
            </summary>
        </member>
        <member name="F:Symbol.Fusion.WLAN.Authentication.AuthenticationTypes.EAP_TLS">
            <summary>
            EAP TLS.
            </summary>
        </member>
        <member name="F:Symbol.Fusion.WLAN.Authentication.AuthenticationTypes.PEAP_MSCHAPV2">
            <summary>
            PEAP with MSCHAPV2 Tunneling.
            </summary>
        </member>
        <member name="F:Symbol.Fusion.WLAN.Authentication.AuthenticationTypes.PEAP_TLS">
            <summary>
            PEAP with TLS Tunneling.
            </summary>
        </member>
        <member name="F:Symbol.Fusion.WLAN.Authentication.AuthenticationTypes.LEAP">
            <summary>
            LEAP Authentication.
            </summary>
        </member>
        <member name="F:Symbol.Fusion.WLAN.Authentication.AuthenticationTypes.EAP_TTLS_CHAP">
            <summary>
            EAP TTLS with CHAP Tunneling.
            </summary>
        </member>
        <member name="F:Symbol.Fusion.WLAN.Authentication.AuthenticationTypes.EAP_TTLS_MSCHAP">
            <summary>
            EAP TTLS with MSCHAP Tunneling.
            </summary>
        </member>
        <member name="F:Symbol.Fusion.WLAN.Authentication.AuthenticationTypes.EAP_TTLS_MSCHAPV2">
            <summary>
            EAP TTLS with MSCHAPV2 Tunneling.
            </summary>
        </member>
        <member name="F:Symbol.Fusion.WLAN.Authentication.AuthenticationTypes.EAP_TTLS_PAP">
             <summary>
            EAP TTLS with PAP Tunneling.  
             </summary>
        </member>
        <member name="F:Symbol.Fusion.WLAN.Authentication.AuthenticationTypes.EAP_TTLS_MD5">
            <summary>
            EAP TTLS with MD5 Tunneling.
            </summary>
        </member>
        <member name="F:Symbol.Fusion.WLAN.Authentication.AuthenticationTypes.PEAP_GTC">
            <summary>
            PEAP with GTC tunneling.
            </summary>
        </member>
        <member name="F:Symbol.Fusion.WLAN.Authentication.AuthenticationTypes.EAP_FAST_MSCHAPV2">
            <summary>
            EAP FAST with MSCHAPV2 tunneling.
            </summary>
        </member>
        <member name="F:Symbol.Fusion.WLAN.Authentication.AuthenticationTypes.EAP_FAST_TLS">
            <summary>
            EAP FAST with TLS tunneling.
            </summary>
        </member>
        <member name="F:Symbol.Fusion.WLAN.Authentication.AuthenticationTypes.EAP_FAST_GTC">
            <summary>
            EAP FAST with GTC tunneling.
            </summary>
        </member>
        <member name="F:Symbol.Fusion.WLAN.Authentication.AuthenticationTypes.WAPI_CERTIFICATE">
            <summary>
            Certificate for WAPI.
            WAPI is China’s standard for wireless LANs. WAPI is a security mechanism which works alongside the 802.11 framework and is an alternative to the 802.11i standard.
            Supported in the Fusion versions 3.30 and above.
            </summary>
        </member>
        <member name="T:Symbol.Fusion.WLAN.Authentication.CertificateTypes">
            <summary>
            Enumerates possible certificate types
            </summary>
        </member>
        <member name="F:Symbol.Fusion.WLAN.Authentication.CertificateTypes.LOCAL">
            <summary>
            Use local certificates
            </summary>
        </member>
        <member name="F:Symbol.Fusion.WLAN.Authentication.CertificateTypes.REMOTE">
            <summary>
            Use remote certificates
            </summary>
        </member>
        <member name="T:Symbol.Fusion.WLAN.Authentication.ProfileLoginOperation">
            <summary>
            Enumerate Automatic or Manual login 
            </summary>
        </member>
        <member name="F:Symbol.Fusion.WLAN.Authentication.ProfileLoginOperation.AUTO">
            <summary>
            The device logs into the network without end-user intervention.
            Credentials should be specified using 
            <see cref="P:Symbol.Fusion.WLAN.Authentication.UserCredentials"/>.
            Profiles with credential specified are also known as a DEVICE PROFILES.
            </summary>
        </member>
        <member name="F:Symbol.Fusion.WLAN.Authentication.ProfileLoginOperation.MANUAL">
            <summary>
            The profile being specified using this Authentication will prompt 
            the end-user for credentials when required. This type of profile 
            is known as a USER PROFILE.
            </summary>
        </member>
        <member name="F:Symbol.Fusion.WLAN.Authentication.ProfileLoginOperation.TOKEN_PASSWORD">
            <summary>
            When This is set and the Profile's authentication
            protocol uses GTC Tunneling, then a constantly changing password is used to
            validate the end-user's credentials.  The end-user will always be prompted
            for the password from the token, and no password is cached.
            If this flag is not set for GTC Tunneling, the expected password
            is static (never-changing) and this profile's credential validation
            behavior is the same as any other DEVICE or USER PROFILE.
            </summary>
        </member>
        <member name="T:Symbol.Fusion.WLAN.ProfileData">
            <summary>
            The classes ProfileData and AdHocProfileData are derived from this class. This class can be
            used to define simple infrastructure profile data 
            (i.e. A profile without Authentication)
            </summary>
        </member>
        <member name="F:Symbol.Fusion.WLAN.ProfileData.MAX_PROFILE_NAME_LENGTH">
            <summary>
            The maximum length of a profile name
            </summary>
        </member>
        <member name="F:Symbol.Fusion.WLAN.ProfileData.MAX_SSID_LENGTH">
            <summary>
            The maximum length of an SSID
            </summary>
        </member>
        <member name="F:Symbol.Fusion.WLAN.ProfileData.MAX_COUNTRY_CODE_LENGTH">
            <summary>
            Maximum length of CountryCode
            </summary>
        </member>
        <member name="M:Symbol.Fusion.WLAN.ProfileData.#ctor(System.String,System.String)">
            <summary>
            ProfileData constructor
            </summary>
            <param name="profileName">The name given to the profile</param>
            <param name="ssID">The SSID given to the profile</param>
        </member>
        <member name="M:Symbol.Fusion.WLAN.ProfileData.IsValid">
            <summary>
            Check whether the data defined in the AdhocProfileData object is complete
            coherent and sizes are acceptable by the Fusion API.
            </summary>
            <returns>Returns true if data is valid. This function will throw exceptions if data is 
            not valid.</returns>
            <remarks>This function descends to along the object hierarchy by calling corresponding 
            IsValid() functions. This function is called by the assembly before creating a profile. 
            In addition this function can be used by developers to verify the data after filling a
            ProfileData object.</remarks>
        </member>
        <member name="P:Symbol.Fusion.WLAN.ProfileData.ProfileName">
            <summary>
            The name of the profile. The maximum length of a profile name is defined by MAX_PROFILE_NAME_LENGTH.
            </summary>
        </member>
        <member name="P:Symbol.Fusion.WLAN.ProfileData.Encryption">
            <summary>
            Encryption settings to use with this profile
            </summary>
        </member>
        <member name="P:Symbol.Fusion.WLAN.ProfileData.PowerMode">
            <summary>
            Defines the power mode in which the device operates.
            </summary>
        </member>
        <member name="P:Symbol.Fusion.WLAN.ProfileData.SSID">
            <summary>
            Define the SSID associated with the profile
            </summary>
            <remarks>The maximum allowed SSID length is defined by MAX_SSID_LENGTH</remarks>
        </member>
        <member name="P:Symbol.Fusion.WLAN.ProfileData.CountryCode">
            <summary>
            Country that will use the profile.
            /// </summary>
            <remarks>The allowed length of CountryCode is defined by 
            MAX_COUNTRY_CODE_LENGTH</remarks>
        </member>
        <member name="P:Symbol.Fusion.WLAN.ProfileData.IPSettings">
            <summary>
            IP settings to use with this profile
            </summary>
        </member>
        <member name="P:Symbol.Fusion.WLAN.ProfileData.PerformanceSetting">
            <summary>
            The performance setting for the device.
            Supported in Fusion versions X_2.0 and above.
            </summary>
        </member>
        <member name="T:Symbol.Fusion.WLAN.InfrastructureProfileData">
            <summary>
            This class can be used to define profile data for all profile types except 
            Adhoc profiles. 
            </summary>
        </member>
        <member name="M:Symbol.Fusion.WLAN.InfrastructureProfileData.#ctor(System.String,System.String)">
            <summary>
            Constructor
            </summary>
            <param name="profileName">The name given to the profile</param>
            <param name="ssID">The SSID given to the profile</param>
        </member>
        <member name="M:Symbol.Fusion.WLAN.InfrastructureProfileData.IsValid">
            <summary>
            Check whether the data defined in the AdhocProfileData object is complete
            coherent and sizes are acceptable by the Fusion API.
            </summary>
            <returns>Returns true if data is valid</returns>
            <remarks>This function descends to along the object hierarchy by calling corresponding 
            IsValid() functions.</remarks>
        </member>
        <member name="P:Symbol.Fusion.WLAN.InfrastructureProfileData.Authentication">
            <summary>
            Define Authentication settings
            </summary>
        </member>
        <member name="P:Symbol.Fusion.WLAN.InfrastructureProfileData.TransmissionPower">
            <summary>
            Defines the current transmit power level.
            </summary>
        </member>
        <member name="P:Symbol.Fusion.WLAN.InfrastructureProfileData.SecurityType">
            <summary>
            Defines the security type used. Supported in Fusion versions 2.60 and above.
            </summary>
        </member>
        <member name="P:Symbol.Fusion.WLAN.InfrastructureProfileData.CCXFlags">
            <summary>
            This will specify the CCX options. Supported in Fusion versions 3.00 and above.
            </summary>
        </member>
        <member name="P:Symbol.Fusion.WLAN.InfrastructureProfileData.FastRoaming">
            <summary>
            Provides access to the fast roaming settings.
            Supported in Fusion versions H_3.40 and above.
            </summary>
        </member>
        <member name="T:Symbol.Fusion.WLAN.InfrastructureProfileData.TransmitPowerLevel">
            <summary>
            Transmit power level for Infrastructure Mode
            </summary>
        </member>
        <member name="F:Symbol.Fusion.WLAN.InfrastructureProfileData.TransmitPowerLevel.BSS_AUTO">
            <summary>
            Set the radio to automatically adjust the transmit power level.
            </summary>
        </member>
        <member name="F:Symbol.Fusion.WLAN.InfrastructureProfileData.TransmitPowerLevel.BSS_PLUS">
            <summary>
            Set the radio to transmit at an additional power level.
            This is not supported in the Fusion versions 3.20 and 3.30.
            </summary>
        </member>
        <member name="T:Symbol.Fusion.WLAN.InfrastructureProfileData.EnabledDisabled">
            <summary>
            Denotes whether the option has been enabled or disabled.
            Undefined means that the option has not been specified.
            </summary>
        </member>
        <member name="F:Symbol.Fusion.WLAN.InfrastructureProfileData.EnabledDisabled.UNDEFINED">
            <summary>
            The option has not been specified.
            </summary>
        </member>
        <member name="F:Symbol.Fusion.WLAN.InfrastructureProfileData.EnabledDisabled.DISABLED">
            <summary>
            Disabled.
            </summary>
        </member>
        <member name="F:Symbol.Fusion.WLAN.InfrastructureProfileData.EnabledDisabled.ENABLED">
            <summary>
            Enabled.
            </summary>
        </member>
        <member name="T:Symbol.Fusion.WLAN.InfrastructureProfileData.FastRoamingOptions">
            <summary>
            The fast roaming settings.
            Supported in Fusion versions H_3.40 and above.
            </summary>
        </member>
        <!-- Badly formed XML comment ignored for member "F:Symbol.Fusion.WLAN.InfrastructureProfileData.FastRoamingOptions.UNDEFINED" -->
        <member name="F:Symbol.Fusion.WLAN.InfrastructureProfileData.FastRoamingOptions.ALLOW_CISCO_CCKM">
            <summary>
            Allow Cisco CCKM fast roaming.
            </summary>
        </member>
        <member name="F:Symbol.Fusion.WLAN.InfrastructureProfileData.FastRoamingOptions.ALLOW_MOTOROLA_HFSR">
            <summary>
            Allow Motorola HFSR fast roaming.
            </summary>
        </member>
        <member name="T:Symbol.Fusion.WLAN.InfrastructureProfileData.FastRoamingSettings">
            <summary>
            Provides access to the fast roaming settings.
            Supported in Fusion versions H_3.40 and above.
            </summary>
        </member>
        <member name="P:Symbol.Fusion.WLAN.InfrastructureProfileData.FastRoamingSettings.CiscoCCKM">
            <summary>
            Denotes whether the fast roaming option CiscoCCKM has been enabled or disabled.
            Undefined means that the option has not been specified.
            Supported in Fusion versions H_3.40 and above.
            </summary>
        </member>
        <member name="P:Symbol.Fusion.WLAN.InfrastructureProfileData.FastRoamingSettings.MotorolaHFSR">
            <summary>
            Denotes whether the fast roaming option MotorolaHFSR has been enabled or disabled.
            Undefined means that the option has not been specified.
            Supported in Fusion versions H_3.40 and above.
            </summary>
        </member>
        <member name="T:Symbol.Fusion.WLAN.AdhocProfileData">
            <summary>
            Summary description for AdhocProfileData.
            </summary>
        </member>
        <member name="M:Symbol.Fusion.WLAN.AdhocProfileData.#ctor(System.String,System.String)">
            <summary>
            Constructor
            </summary>
            <param name="profileName">The name given to the profile</param>
            <param name="ssID">The SSID given to the profile</param>
        </member>
        <member name="P:Symbol.Fusion.WLAN.AdhocProfileData.Channel">
            <summary>
            When operating mode is set for Adhoc, this member defines channel 
            number to use.
            </summary>
        </member>
        <member name="P:Symbol.Fusion.WLAN.AdhocProfileData.TransmissionPower">
            <summary>
            Defines the current transmit power level.
            </summary>
        </member>
        <member name="T:Symbol.Fusion.WLAN.AdhocProfileData.TransmitPowerLevel">
            <summary>
            Transmit power level for Adhoc Mode
            </summary>
        </member>
        <member name="F:Symbol.Fusion.WLAN.AdhocProfileData.TransmitPowerLevel.IBSS_FULL">
            <summary>
            set the radio to transmit at full power.
            </summary>
        </member>
        <member name="F:Symbol.Fusion.WLAN.AdhocProfileData.TransmitPowerLevel.IBSS_30MW">
            <summary>
            set the radio to transmit at 30mW power.
            </summary>
        </member>
        <member name="F:Symbol.Fusion.WLAN.AdhocProfileData.TransmitPowerLevel.IBSS_15MW">
            <summary>
            set the radio to transmit at 15mW power.
            </summary>
        </member>
        <member name="F:Symbol.Fusion.WLAN.AdhocProfileData.TransmitPowerLevel.IBSS_05MW">
            <summary>
            set the radio to transmit at 5mW power.
            </summary>
        </member>
        <member name="F:Symbol.Fusion.WLAN.AdhocProfileData.TransmitPowerLevel.IBSS_01MW">
            <summary>
            set the radio to transmit at 1mW power.
            </summary>
        </member>
        <member name="T:Symbol.Fusion.WLAN.AdhocProfileData.CommunicationChannel">
            <summary>
            Enumerate possible communication channels.  
            </summary>
        </member>
        <member name="F:Symbol.Fusion.WLAN.AdhocProfileData.CommunicationChannel.FREQUENCY_2412_MHZ">
            <summary>
            Channel 1
            </summary>
        </member>
        <member name="F:Symbol.Fusion.WLAN.AdhocProfileData.CommunicationChannel.FREQUENCY_2417_MHZ">
            <summary>
            Channel 2
            </summary>
        </member>
        <member name="F:Symbol.Fusion.WLAN.AdhocProfileData.CommunicationChannel.FREQUENCY_2422_MHZ">
            <summary>
            Channel 3
            </summary>
        </member>
        <member name="F:Symbol.Fusion.WLAN.AdhocProfileData.CommunicationChannel.FREQUENCY_2427_MHZ">
            <summary>
            Channel 4
            </summary>
        </member>
        <member name="F:Symbol.Fusion.WLAN.AdhocProfileData.CommunicationChannel.FREQUENCY_2432_MHZ">
            <summary>
            Channel 5
            </summary>
        </member>
        <member name="F:Symbol.Fusion.WLAN.AdhocProfileData.CommunicationChannel.FREQUENCY_2437_MHZ">
            <summary>
            Channel 6
            </summary>
        </member>
        <member name="F:Symbol.Fusion.WLAN.AdhocProfileData.CommunicationChannel.FREQUENCY_2442_MHZ">
            <summary>
            Channel 7
            </summary>
        </member>
        <member name="F:Symbol.Fusion.WLAN.AdhocProfileData.CommunicationChannel.FREQUENCY_2447_MHZ">
            <summary>
            Channel 8
            </summary>
        </member>
        <member name="F:Symbol.Fusion.WLAN.AdhocProfileData.CommunicationChannel.FREQUENCY_2452_MHZ">
            <summary>
            Channel 9
            </summary>
        </member>
        <member name="F:Symbol.Fusion.WLAN.AdhocProfileData.CommunicationChannel.FREQUENCY_2457_MHZ">
            <summary>
            Channel 10
            </summary>
        </member>
        <member name="F:Symbol.Fusion.WLAN.AdhocProfileData.CommunicationChannel.FREQUENCY_2462_MHZ">
            <summary>
            Channel 11 
            </summary>
        </member>
        <member name="F:Symbol.Fusion.WLAN.AdhocProfileData.CommunicationChannel.FREQUENCY_2467_MHZ">
            <summary>
            Channel 12 
            </summary>
        </member>
        <member name="F:Symbol.Fusion.WLAN.AdhocProfileData.CommunicationChannel.FREQUENCY_2472_MHZ">
            <summary>
            Channel 13
            </summary>
        </member>
        <member name="F:Symbol.Fusion.WLAN.AdhocProfileData.CommunicationChannel.FREQUENCY_2484_MHZ">
            <summary>
            Channel 14 
            </summary>
        </member>
        <member name="T:Symbol.Fusion.WLAN.WLAN_PERFORMANCE_SETTING">
            <summary>
            The performance setting for the device.
            Supported in Fusion versions X_2.0 and above.
            </summary>
        </member>
        <member name="F:Symbol.Fusion.WLAN.WLAN_PERFORMANCE_SETTING.UNDEFINED">
            <summary>
            The performance setting undefined. This means that the option has not been specified.
            </summary>
        </member>
        <member name="F:Symbol.Fusion.WLAN.WLAN_PERFORMANCE_SETTING.OPTIMIZE_FOR_DATA">
            <summary>
            Optimize for Data.
            </summary>
        </member>
        <member name="F:Symbol.Fusion.WLAN.WLAN_PERFORMANCE_SETTING.OPTIMIZE_FOR_VOICE">
            <summary>
            Optimize for Voice.
            </summary>
        </member>
        <member name="T:Symbol.Fusion.WLAN.PowerMode">
            <summary>
            WLAN device power consumption settings
            </summary>
        </member>
        <member name="F:Symbol.Fusion.WLAN.PowerMode.CAM">
            <summary>
            CAM mode
            </summary>
        </member>
        <member name="F:Symbol.Fusion.WLAN.PowerMode.FAST_POWER_SAVE">
            <summary>
            Fast power save mode
            </summary>
        </member>
        <member name="F:Symbol.Fusion.WLAN.PowerMode.MAX_POWER_SAVE">
            <summary>
            Max power save mode
            </summary>
        </member>
        <member name="T:Symbol.Fusion.WLAN.WLANSecurityType">
            <summary>
            Enumerates the types of Security Used.
            Supported in Fusion versions 2.60 and above. 
            </summary>
        </member>
        <member name="F:Symbol.Fusion.WLAN.WLANSecurityType.UNDEFINED">
            <summary>
            The security type is undefined.
            </summary>
        </member>
        <member name="F:Symbol.Fusion.WLAN.WLANSecurityType.SECURITY_LEGACY">
            <summary>
            Legacy covers all authentication mechanisms involving WEP (WEP-40 Bit and WEP-104/WEP-128 Bit) Encryption techniques.
            </summary>
        </member>
        <member name="F:Symbol.Fusion.WLAN.WLANSecurityType.SECURITY_WPA_PERSONAL">
            <summary>
            WPA Personal signifies that the Authentication is done using WPA Pre-shared Key.
            No infrastructure is required. 
            The encryption type should be set to TKIP hex/pass-phrase.
            Hexadecimal key is not supported in Fusion 2.57.
            </summary>
        </member>
        <member name="F:Symbol.Fusion.WLAN.WLANSecurityType.SECURITY_WPA2_PERSONAL">
            <summary>
            WPA2 Personal signifies that the Authentication is done using WPA2 Pre-shared Key.
            No infrastructure is required.  
            The encryption type can be set to AES hex/pass-phrase on all Fusion versions.
            It can be set to TKIP hex/pass-phrase on all Fusion versions except 2.6x.
            Hexadecimal key is not supported in Fusion 2.57.
            </summary>
        </member>
        <member name="F:Symbol.Fusion.WLAN.WLANSecurityType.SECURITY_WPA_ENTERPRISE">
            <summary>
            WPA Enterprise signifies that the Authentication is done using IEEE 802.1X with WPA encryption.
            Generally, this requires the security infrastructure like RADIUS server and Directories.
            The encryption type should be set to TKIP hex/pass-phrase.
            Hexadecimal key is not supported in Fusion 2.57.
            </summary>
        </member>
        <member name="F:Symbol.Fusion.WLAN.WLANSecurityType.SECURITY_WPA2_ENTERPRISE">
            <summary>
            WPA2 Enterprise signifies that the Authentication is done using IEEE 802.1X with WPA2 encryption. 
            Generally, this requires the security infrastructure like RADIUS server and Directories.
            The encryption type can be set to AES hex/pass-phrase on all Fusion versions.
            It can be set to TKIP hex/pass-phrase on all Fusion versions except 2.6x.
            Hexadecimal key is not supported in Fusion 2.57.
            </summary>
        </member>
        <member name="F:Symbol.Fusion.WLAN.WLANSecurityType.SECURITY_WPA_CCKM">
            <summary>
            CCX devices and infrastructure use CCKM fast AP to AP roaming.  
            This is an Enterprise type and the Authentication is done using IEEE 802.1X with WPA encryption. 
            Generally, this requires the security infrastructure like RADIUS server and Directories.
            The encryption type should be set to TKIP hex/pass-phrase. 
            This security mode is only supported in Fusion 2.57.
            This security mode will not be supported in Fusion versions 3.00 and above. 
            </summary>
        </member>
        <member name="F:Symbol.Fusion.WLAN.WLANSecurityType.SECURITY_WAPI">
            <summary>
            WAPI is China’s standard for wireless LANs. WAPI is a security mechanism which works alongside the 802.11 framework and is an alternative to the 802.11i standard.
            The authentication type can be either NONE or WAPI_CERTIFICATE.
            The encryption type should be set to SMS4.
            Supported in the Fusion versions 3.30 and above.
            </summary>
        </member>
        <member name="T:Symbol.Fusion.WLAN.WLANCCXFlags">
            <summary>
            Enumerates the CCXFlags options to be used. 
            Supported in Fusion versions 3.00 and above. 
            </summary>
        </member>
        <member name="F:Symbol.Fusion.WLAN.WLANCCXFlags.UNDEFINED">
            <summary>
            The CCXFlags undefined.
            </summary>
        </member>
        <member name="F:Symbol.Fusion.WLAN.WLANCCXFlags.ENABLE_CCKM">
            <summary>
            When ENABLE_CCKM is set then the API is specifying that 
            CCKM fast AP to AP roaming will be specified with this profile.
            (This is valid with SECURITY_WPA_ENTERPRISE security mode only.)
            </summary>
        </member>
        <member name="T:Symbol.Fusion.WLAN.IEEE802_11_OIDs">
            <summary>
            Enumerates the Object IDs available in NDIS.
            </summary>
        </member>
        <member name="F:Symbol.Fusion.WLAN.IEEE802_11_OIDs.BSSID">
            <summary>
            The media access control (MAC) address of the access point to associate with.
            After this OID is set, the device can only associate with an access point (AP) with the desired BSSID.
            </summary>
        </member>
        <member name="F:Symbol.Fusion.WLAN.IEEE802_11_OIDs.BSSID_LIST_SCAN">
            <summary>
            Set only.
            When set, this requests to perform a network scan of BSSIDs and SSIDs. 
            No data is associated with this OID. The driver will then be queried for the scan results with OID_802_11_BSSID_LIST.
            </summary>
        </member>
        <member name="F:Symbol.Fusion.WLAN.IEEE802_11_OIDs.CONFIGURATION">
            <summary>
            The underlying NIC's radio configuration parameters.
            </summary>
        </member>
        <member name="F:Symbol.Fusion.WLAN.IEEE802_11_OIDs.FRAGMENTATION_THRESHOLD">
            <summary>
            The packet fragmentation threshold of the underlying NIC. 
            </summary>
        </member>
        <member name="F:Symbol.Fusion.WLAN.IEEE802_11_OIDs.INFRASTRUCTURE_MODE">
            <summary>
            The type of 802.11 network the device uses to associate with.
            </summary>
        </member>
        <member name="F:Symbol.Fusion.WLAN.IEEE802_11_OIDs.NETWORK_TYPE_IN_USE">
            <summary>
            The network type that the underlying NIC's physical layer (PHY) will use.
            </summary>
        </member>
        <member name="F:Symbol.Fusion.WLAN.IEEE802_11_OIDs.NUMBER_OF_ANTENNAS">
            <summary>
            The number of antennas on the underlying NIC's radio. 
            </summary>
        </member>
        <member name="F:Symbol.Fusion.WLAN.IEEE802_11_OIDs.POWER_MODE">
            <summary>
            The power mode of the underlying NIC.
            </summary>
        </member>
        <member name="F:Symbol.Fusion.WLAN.IEEE802_11_OIDs.BSSID_LIST">
            <summary>
            The list containing all of the detected BSSIDs and their attributes. 
            </summary>
        </member>
        <member name="F:Symbol.Fusion.WLAN.IEEE802_11_OIDs.RSSI">
            <summary>
            The current value of the received signal strength indication (RSSI).
            The RSSI is measured in dBm.
            </summary>
        </member>
        <member name="F:Symbol.Fusion.WLAN.IEEE802_11_OIDs.RTS_THRESHOLD">
            <summary>
            The request to send (RTS) threshold of the underlying NIC.
            </summary>
        </member>
        <member name="F:Symbol.Fusion.WLAN.IEEE802_11_OIDs.SSID">
            <summary>
            The service set identifier (SSID) of the BSS with which the device can associate.
            </summary>
        </member>
        <member name="F:Symbol.Fusion.WLAN.IEEE802_11_OIDs.STATISTICS">
            <summary>
            The current statistics for the IEEE 802.11 interface.
            </summary>
        </member>
        <member name="F:Symbol.Fusion.WLAN.IEEE802_11_OIDs.TX_POWER_LEVEL">
            <summary>
            The transmit power level of the 802.11 NIC. The value is specified in milliwatts (mW).
            </summary>
        </member>
        <member name="T:Symbol.Fusion.WLAN.WLAN_MANAGEMENT_STATE">
            <summary>
            Enumerates the state of the WLAN mode (WZC / Fusion) option.
            </summary>
        </member>
        <member name="F:Symbol.Fusion.WLAN.WLAN_MANAGEMENT_STATE.WLAN_MANAGEMENT_FUSION_STATE">
            <summary>
            Fusion.
            </summary>
        </member>
        <member name="F:Symbol.Fusion.WLAN.WLAN_MANAGEMENT_STATE.WLAN_MANAGEMENT_WZC_STATE">
            <summary>
            Wireless Zero Config (WZC).
            </summary>
        </member>
        <member name="F:Symbol.Fusion.WLAN.WLAN_MANAGEMENT_STATE.WLAN_MANAGEMENT_FUSION_STATE_REBOOT_IN_WZC_STATE">
            <summary>
            WLAN Management setting is in an intermediate state.
            The current state is Fusion; but after a reboot, the state will be set to WZC.
            This value is only for reading the state, cannot be used for setting the state.
            </summary>
        </member>
        <member name="F:Symbol.Fusion.WLAN.WLAN_MANAGEMENT_STATE.WLAN_MANAGEMENT_WZC_STATE_REBOOT_IN_FUSION_STATE">
            <summary>
            WLAN Management setting is in an intermediate state.
            The current state is WZC; but after a reboot, the state will be set to Fusion.
            This value is only for reading the state, cannot be used for setting the state.
            </summary>
        </member>
        <member name="T:Symbol.Fusion.WLAN.WLAN_OPTIMIZE_RADIO">
            <summary>
            Enumerates the options for Radio Optimization.
            Supported in Fusion versions H_3.40 and above.
            Not supported in Fusion version X_2.0.
            </summary>
        </member>
        <member name="F:Symbol.Fusion.WLAN.WLAN_OPTIMIZE_RADIO.OPTIMIZE_RADIO_FOR_CISCO">
            <summary>
            Cisco.
            </summary>
        </member>
        <member name="F:Symbol.Fusion.WLAN.WLAN_OPTIMIZE_RADIO.OPTIMIZE_RADIO_FOR_MOTOROLA">
            <summary>
            Motorola.
            </summary>
        </member>
        <member name="F:Symbol.Fusion.WLAN.WLAN_OPTIMIZE_RADIO.OPTIMIZE_RADIO_REBOOT_FOR_CISCO_MODE">
            <summary>
            This is in an intermediate state.Current mode is Motorola, 
            it has been changed and reboot or radio disable-enable is pending to change state to Cisco mode.
            This value cannot be used for setting Radio Optimization mode.
            </summary>
        </member>
        <member name="F:Symbol.Fusion.WLAN.WLAN_OPTIMIZE_RADIO.OPTIMIZE_RADIO_REBOOT_FOR_MOTOROLA_MODE">
            <summary>
            This is in an intermediate state.Current mode is Cisco, 
            it has been changed and reboot or radio disable-enable is pending to change state to Motorola mode.
            This value cannot be used for setting Radio Optimization mode.
            </summary>
        </member>
        <member name="T:Symbol.Fusion.WLAN.WLAN_FIPS_MODE">
            <summary>
            Enumerates the options for FIPS mode.
            Supported in Fusion versions H_3.40 and above.
            Not supported in Fusion version X_2.0.
            </summary>
        </member>
        <member name="F:Symbol.Fusion.WLAN.WLAN_FIPS_MODE.FIPS_MODE_DISABLED">
            <summary>
            Disabled.
            </summary>
        </member>
        <member name="F:Symbol.Fusion.WLAN.WLAN_FIPS_MODE.FIPS_MODE_ENABLED">
            <summary>
            Enabled.
            </summary>
        </member>
        <member name="F:Symbol.Fusion.WLAN.WLAN_FIPS_MODE.FIPS_MODE_REBOOT_TO_ENABLE">
            <summary>
            This is in an intermediate state. Currently it is Disabled, 
            it has been changed and reboot is pending to change state to Enabled.
            This value cannot be used for setting FIPS mode.
            </summary>
        </member>
        <member name="F:Symbol.Fusion.WLAN.WLAN_FIPS_MODE.FIPS_MODE_REBOOT_TO_DISABLE">
            <summary>
            This is in an intermediate state. Currently it is Enabled, 
            it has been changed and reboot is pending to change state to Disabled.
            This value cannot be used for setting FIPS mode.
            </summary>
        </member>
        <member name="T:Symbol.Fusion.WLAN.Adapters">
            <summary>
            WLAN.Adapters class provides a list of WLAN Adapters that could be 
            enumerated. 
            </summary>
        </member>
        <member name="F:Symbol.Fusion.WLAN.Adapters.m_Adapters">
            <summary>
            This is the actual list of Adapters
            </summary>
        </member>
        <member name="F:Symbol.Fusion.WLAN.Adapters.m_clsWlan">
            <summary>
            This keeps track of the current position in m_Adapters list.
            </summary>
        </member>
        <member name="M:Symbol.Fusion.WLAN.Adapters.#ctor(Symbol.Fusion.WLAN.WLAN)">
            <summary>
            Constructor,
            Creates the Adapters list by getting adapter information from the API
            </summary>
            <param name="wLan">The parent WLAN object</param>
        </member>
        <member name="M:Symbol.Fusion.WLAN.Adapters.Dispose">
            <summary>
            Called during WLAN dispose. Used to make the cached data not accessible
            </summary>
        </member>
        <member name="M:Symbol.Fusion.WLAN.Adapters.GetAdapter(System.UInt32)">
            <summary>
            Get the adapter object that matches with the passed handle
            </summary>
            <param name="adapterHandle">the handle to seak</param>
            <returns>An adapter with the same handle</returns>
        </member>
        <member name="M:Symbol.Fusion.WLAN.Adapters.InitializePowerMonitoring">
            <summary>
            Initialize power monitoring for all adapters.
            </summary>
        </member>
        <member name="M:Symbol.Fusion.WLAN.Adapters.InitializeAccesspointMonitoring">
            <summary>
            Initialize accesspoint/BSSID monitoring for all adapters.
            </summary>
        </member>
        <member name="P:Symbol.Fusion.WLAN.Adapters.Length">
            <summary>
            The number of Adapters in the list
            </summary>
        </member>
        <member name="P:Symbol.Fusion.WLAN.Adapters.Item(System.Int32)">
            <summary>
            The indexer provided to enumerate Adapter objects in the list.
            </summary>
        </member>
        <member name="T:Symbol.Fusion.WLAN.Adapter">
            <summary>
            WLAN.Adapter class provides information related to a particular
            Wireless adapter and its settings. 
            All wireless adapters available on a mobile device will be included in
            a WLAN.Adapters list which could be iterated to access each WLAN.Adapter 
            object. 
            </summary>
        </member>
        <member name="F:Symbol.Fusion.WLAN.Adapter.m_AdapterHandle">
            <summary>
            Handle to the adapter context
            </summary>
        </member>
        <member name="F:Symbol.Fusion.WLAN.Adapter.m_clsSignalQualityMW">
            <summary>
            SymbolMessageWindow object used by for UI notifications 
            </summary>
        </member>
        <member name="M:Symbol.Fusion.WLAN.Adapter.RenewDHCP">
            <summary>
            Renew IP address of the specified adapter
            </summary>
            <remarks>The active profile should be in DHCP addressing mode</remarks>
            <returns>Returns an encoding of the API result</returns>
        </member>
        <member name="M:Symbol.Fusion.WLAN.Adapter.Dispose">
            <summary>
            Called during WLAN dispose. Used to make the cached data not accessible
            </summary>
        </member>
        <member name="M:Symbol.Fusion.WLAN.Adapter.ProcessSignalQualityEvents">
            <summary>
            Call the SignalQuality event handlers on event changes
            </summary>
            <remarks>This function should be called by a thread on predefined time intervals</remarks>
        </member>
        <member name="M:Symbol.Fusion.WLAN.Adapter.ResetSignalQualityEventMonitors">
            <summary>
            This function resets the monitors used in SignalQuality Event processing
            This is done to make sure that SignalQuality events are fired soon after 
            removing all SignalQuality event handlers and adding a new SignalQuality event handler.
            </summary>
        </member>
        <member name="M:Symbol.Fusion.WLAN.Adapter.OnSignalQualityChange(System.EventArgs)">
            <summary>
            Called by MessageWindow class to notify UI thread of signal quality change. 
            </summary>
        </member>
        <member name="M:Symbol.Fusion.WLAN.Adapter.ProcessStatusEvents">
            <summary>
            Call the Status event handlers on event changes
            </summary>
            <remarks>This function should be called by a thread on predefined time intervals</remarks>
        </member>
        <member name="M:Symbol.Fusion.WLAN.Adapter.InitializeStatusMonitoring">
            <summary>
            Initialize variables used for status monitoring
            </summary>
        </member>
        <member name="M:Symbol.Fusion.WLAN.Adapter.InitializePowerMonitoring">
            <summary>
            Initialize power monitoring
            </summary>
        </member>
        <member name="M:Symbol.Fusion.WLAN.Adapter.InitializeAccesspointMonitoring">
            <summary>
            Initialize accesspoint/BSSID monitoring
            </summary>
        </member>
        <member name="M:Symbol.Fusion.WLAN.Adapter.Initialize">
            <summary>
            Initialize Adapter data using AdapterInfo from the API
            </summary>
        </member>
        <member name="M:Symbol.Fusion.WLAN.Adapter.#ctor(Symbol.Fusion.WLAN.WLAN,System.UInt32)">
            <summary>
            Constructor
            </summary>
            <param name="wLan">The WLAN object</param>
            <param name="adapterID">The ID of the adapter to get AdapterInfo</param>
            <remarks>This function should be called after getting the adapterID list 
            <see cref="T:Symbol.Fusion.WLAN.Adapters"/></remarks>
        </member>
        <member name="M:Symbol.Fusion.WLAN.Adapter.GetSSIDs">
            <summary>
            Get SSIDs. Returns an array list of SSIDInfo objects containing the information
            regarding the available SSIDs in the environment.
            </summary>
        </member>
        <member name="M:Symbol.Fusion.WLAN.Adapter.GetAccessPoints(System.String)">
            <summary>
            Get access points. Returns an array list of SSIDInfo objects containing the information
            regarding the available access points in the environment, for a given SSID.
            </summary>
        </member>
        <member name="M:Symbol.Fusion.WLAN.Adapter.GetPeers(System.String)">
            <summary>
            Get peers. Returns an array list of SSIDInfo objects containing the information
            regarding the available peer (adhoc) devices in the environment, for a given SSID.
            </summary>
        </member>
        <member name="P:Symbol.Fusion.WLAN.Adapter.MacAddressRaw">
            <summary>
            MAC address of the adapter in Raw format
            </summary>
        </member>
        <member name="P:Symbol.Fusion.WLAN.Adapter.MacAddress">
            <summary>
            MAC Address of the Adapter in string format
            </summary>
        </member>
        <member name="P:Symbol.Fusion.WLAN.Adapter.ESSID">
            <summary>
            The ESSID currently used by the adapter as a string
            </summary>
        </member>
        <member name="P:Symbol.Fusion.WLAN.Adapter.PowerState">
            <summary>
            The power state of the adapter. Settable in Command Mode only.
            </summary>
        </member>
        <member name="P:Symbol.Fusion.WLAN.Adapter.SignalQuality">
            <summary>
            The quality of the signal
            </summary>
        </member>
        <member name="P:Symbol.Fusion.WLAN.Adapter.SignalStrength">
            <summary>
            The RF signal strength of the WLAN adapter
            </summary>
        </member>
        <member name="P:Symbol.Fusion.WLAN.Adapter.PartNumber">
            <summary>
            The part number of the adapter.  This is for future use.
            </summary>
        </member>
        <member name="P:Symbol.Fusion.WLAN.Adapter.ModelNumber">
            <summary>
            The modle number of the adapter.  This is for future use.
            </summary>
        </member>
        <member name="P:Symbol.Fusion.WLAN.Adapter.ManufactureDate">
            <summary>
            Date of manufacture.  This is for future use.
            </summary>
        </member>
        <member name="P:Symbol.Fusion.WLAN.Adapter.HardwareRevision">
            <summary>
            Hardware revision.  This is for future use.
            </summary>
        </member>
        <member name="P:Symbol.Fusion.WLAN.Adapter.CardInfoVersionRaw">
            <summary>
            The CardInfoVersion in Raw Mode. This is for future use.
            </summary>
        </member>
        <member name="P:Symbol.Fusion.WLAN.Adapter.CardInfoVersion">
            <summary>
            The CardInfoVersion as a string. This is for future use.
            </summary>
        </member>
        <member name="P:Symbol.Fusion.WLAN.Adapter.ActiveProfile">
            <summary>
            Currently active profile associated with this Adapter.
            </summary>
            <returns>A profile if a profile is active else returns null</returns>
        </member>
        <member name="P:Symbol.Fusion.WLAN.Adapter.ConnectionStatus">
            <summary>
            Waits on the connection status of the WLAN radio.
            This will block until the WLAN radio has a valid connection.
            FusionResults.SUCCESS indicates success, any other value indicates an error.
            </summary>
        </member>
        <member name="P:Symbol.Fusion.WLAN.Adapter.NDIS">
            <summary>
            Provides access to the NDIS information.
            </summary>
        </member>
        <member name="P:Symbol.Fusion.WLAN.Adapter.PAC">
            <summary>
            Provides access to the settings related to PAC.
            </summary>
        </member>
        <member name="P:Symbol.Fusion.WLAN.Adapter.IPv6">
            <summary>
            Provides access to the settings related to IPv6.
            </summary>
        </member>
        <member name="E:Symbol.Fusion.WLAN.Adapter.SignalQualityChanged">
            <summary>
            Attach to this SignalQuality event to be notified of signal quality changes in the WLAN. 
            </summary>
            <value>
            A generic System.EventHandler object.
            </value>
        </member>
        <member name="P:Symbol.Fusion.WLAN.Adapter.RFBand">
            <summary>
            The RF bandwidth of the adapter operates on.
            </summary>
        </member>
        <member name="P:Symbol.Fusion.WLAN.Adapter.IEEE80211dEnabled">
            <summary>
            The IEEE80211d enabled/disabled status.
            </summary>
        </member>
        <member name="P:Symbol.Fusion.WLAN.Adapter.IPManagementEnabled">
            <summary>
            The IP Management enabled/disabled status
            </summary>
        </member>
        <member name="P:Symbol.Fusion.WLAN.Adapter.AutoTimeConfigEnabled">
            <summary>
            The enabled/disabled status of the Automatic Time Configuration.
            </summary>
        </member>
        <member name="P:Symbol.Fusion.WLAN.Adapter.CountryCode">
            <summary>
            The country code of the adapter.
            </summary>
        </member>
        <member name="P:Symbol.Fusion.WLAN.Adapter.WLANManagement">
            <summary>
            The state of the WLAN mode (WZC / Fusion) option. 
            Setting the state is only allowed in command mode and is only supported on the Fusion versions 3.20 and later.
            WLANManagement is not supported in Fusion X_1.00.
            </summary>
        </member>
        <member name="P:Symbol.Fusion.WLAN.Adapter.PreAuthentication">
            <summary>
            The enabled/disabled status of the PreAuthentication.
            Supported on the Fusion version 3.30 and above.
            An OperationFailureException would be thrown with its Result set to ERROR_NOT_SUPPORTED on the Fusion versions older than 3.30.
            PreAuthentication is not supported in Fusion X_1.00.
            Setting PreAuthentication is only allowed in command mode. 
            </summary>
        </member>
        <member name="P:Symbol.Fusion.WLAN.Adapter.OptimizeRadio">
            <summary>
            The current settings for Radio Optimization option.
            Supported in Fusion versions H_3.40 and above.
            Not supported in Fusion version X_2.0.
            Setting OptimizeRadio is only allowed in command mode.
            </summary>
        </member>
        <member name="P:Symbol.Fusion.WLAN.Adapter.FIPSMode">
            <summary>
            The current state (enable / disable) of the FIPS mode Option.
            Supported in Fusion versions H_3.40 and above.
            Not supported in Fusion version X_2.0.
            Setting FIPSMode is only allowed in command mode.
            </summary>
        </member>
        <member name="P:Symbol.Fusion.WLAN.Adapter.RFBandsAndChannels">
            <summary>
            The radio frequency bands and the relevant channels.
            Supported in Fusion versions X_2.0 and above.
            </summary>
        </member>
        <member name="T:Symbol.Fusion.WLAN.Adapter.SignalQualityHandler">
            <summary>
            A delegate object used for providing notification to the application when Signal Quality changes
            </summary>
        </member>
        <member name="T:Symbol.Fusion.WLAN.Adapter.RFBandsAndChannelsSettings">
            <summary>
            Provides the access to the radio frequency bands and the relevant channels the adapter operates on.
            Supported in Fusion versions X_2.0 and above.
            </summary>
        </member>
        <member name="M:Symbol.Fusion.WLAN.Adapter.RFBandsAndChannelsSettings.Get">
            <summary>
            Gets the radio frequency bands and the relevant channels the adapter operates on.
            Supported in Fusion versions X_2.0 and above.
            </summary>
        </member>
        <member name="M:Symbol.Fusion.WLAN.Adapter.RFBandsAndChannelsSettings.Set(Symbol.Fusion.WLAN.Adapter.RFBandsAndChannelsSettings.RFBandsAndChannels)">
            <summary>
            Sets the radio frequency bands and the relevant channels the adapter operates on.
            Supported in Fusion versions X_2.0 and above.
            Only allowed in command mode.
            </summary>
        </member>
        <member name="T:Symbol.Fusion.WLAN.Adapter.RFBandsAndChannelsSettings.RFBandsAndChannels">
            <summary>
            The class representing the radio frequency bands and the relevant channels the adapter operates on.
            Supported in Fusion versions X_2.0 and above.
            </summary>
        </member>
        <member name="M:Symbol.Fusion.WLAN.Adapter.RFBandsAndChannelsSettings.RFBandsAndChannels.#ctor(System.UInt32,System.UInt32,System.UInt32)">
            <summary>
            The constructor which accepts the values for RFBand, ChannelList_2_4 and ChannelList_5.
            </summary>
        </member>
        <member name="M:Symbol.Fusion.WLAN.Adapter.RFBandsAndChannelsSettings.RFBandsAndChannels.#ctor">
            <summary>
            The constructor which doesn't accept any parameters.
            The values for RFBand, ChannelList_2_4 and ChannelList_5 will be initialized to zeros. 
            They have to be set separately.
            </summary>
        </member>
        <member name="P:Symbol.Fusion.WLAN.Adapter.RFBandsAndChannelsSettings.RFBandsAndChannels.RFBand">
            <summary>
            The bit field for the radio frequency band(s). 
            This can contain the ORed values of Adapter.RFBandwidth (Example: 2.4GHz and/or 5GHz).
            </summary>
        </member>
        <member name="P:Symbol.Fusion.WLAN.Adapter.RFBandsAndChannelsSettings.RFBandsAndChannels.ChannelList_2_4">
            <summary>
            The bitfield for the channel settings in 2.4GHz band.
            </summary>
        </member>
        <member name="P:Symbol.Fusion.WLAN.Adapter.RFBandsAndChannelsSettings.RFBandsAndChannels.ChannelList_5">
            <summary>
            The bitfield for the channel settings in 5GHz band.
            </summary>
        </member>
        <member name="T:Symbol.Fusion.WLAN.Adapter.SSIDInfo">
            <summary>
            The class for SSID information.
            </summary>
        </member>
        <member name="P:Symbol.Fusion.WLAN.Adapter.SSIDInfo.MacAddressRaw">
            <summary>
            The MAC Address for the network entity in raw format.
            </summary>
        </member>
        <member name="P:Symbol.Fusion.WLAN.Adapter.SSIDInfo.MacAddress">
            <summary>
            The MAC Address for the network entity in string format.
            </summary>
        </member>
        <member name="P:Symbol.Fusion.WLAN.Adapter.SSIDInfo.SSID">
            <summary>
            SSID of the accesspoint.
            </summary>
        </member>
        <member name="P:Symbol.Fusion.WLAN.Adapter.SSIDInfo.SignalQuality">
            <summary>
            The signal quality of the network entity in relation to the local WLAN radio.
            </summary>
        </member>
        <member name="P:Symbol.Fusion.WLAN.Adapter.SSIDInfo.SignalStrength">
            <summary>
            The signal strength of the network entity (RSSI in dBm) in relation to the local WLAN radio.
            </summary>
        </member>
        <member name="P:Symbol.Fusion.WLAN.Adapter.SSIDInfo.OpMode">
            <summary>
            The operating mode of the network (Adhoc or Infrastructure).
            </summary>
        </member>
        <member name="P:Symbol.Fusion.WLAN.Adapter.SSIDInfo.Frequency">
            <summary>
            The signal frequency in MHz.
            </summary>
        </member>
        <member name="P:Symbol.Fusion.WLAN.Adapter.SSIDInfo.Channel">
            <summary>
            The signal channel.
            </summary>
        </member>
        <member name="P:Symbol.Fusion.WLAN.Adapter.SSIDInfo.IEEE80211Type">
            <summary>
            The network band being used by the radio (802.11 a/b/g).
            </summary>
        </member>
        <member name="P:Symbol.Fusion.WLAN.Adapter.SSIDInfo.SecurityEnabled">
            <summary>
            Whether the security is enabled or not.
            </summary>
        </member>
        <member name="T:Symbol.Fusion.WLAN.Adapter.PowerStates">
            <summary>
            Enumerates possible power states of an Adapter
            </summary>
        </member>
        <member name="F:Symbol.Fusion.WLAN.Adapter.PowerStates.OFF">
            <summary>
            The adapter is powered off
            </summary>
        </member>
        <member name="F:Symbol.Fusion.WLAN.Adapter.PowerStates.ON">
            <summary>
            The adapter is powerd on
            </summary>
        </member>
        <member name="T:Symbol.Fusion.WLAN.Adapter.SignalQualityRange">
            <summary>
            Enumerates possible value ranges
            </summary>
        </member>
        <member name="F:Symbol.Fusion.WLAN.Adapter.SignalQualityRange.NONE">
            <summary>
            No Signal
            </summary>
        </member>
        <member name="F:Symbol.Fusion.WLAN.Adapter.SignalQualityRange.POOR">
            <summary>
            Poor Signal Quality
            </summary>
        </member>
        <member name="F:Symbol.Fusion.WLAN.Adapter.SignalQualityRange.FAIR">
            <summary>
            Fair Signal Quality
            </summary>
        </member>
        <member name="F:Symbol.Fusion.WLAN.Adapter.SignalQualityRange.GOOD">
            <summary>
            Good Signal Quality
            </summary>
        </member>
        <member name="F:Symbol.Fusion.WLAN.Adapter.SignalQualityRange.VERYGOOD">
            <summary>
            Very Good Signal Quality
            </summary>
        </member>
        <member name="F:Symbol.Fusion.WLAN.Adapter.SignalQualityRange.EXCELLENT">
            <summary>
            Excellent Signal Quality
            </summary>
        </member>
        <member name="T:Symbol.Fusion.WLAN.Adapter.WLANNetworkType">
            <summary>
            Enumerates WLAN network types.
            </summary>
        </member>
        <member name="F:Symbol.Fusion.WLAN.Adapter.WLANNetworkType.NDIS802_11IBSS">
            <summary>
            Adhoc Mode.
            </summary> 
        </member>
        <member name="F:Symbol.Fusion.WLAN.Adapter.WLANNetworkType.NDIS802_11INFRASTRUCTURE">
            <summary>
            Infrastructure Mode.
            </summary> 
        </member>
        <member name="F:Symbol.Fusion.WLAN.Adapter.WLANNetworkType.NDIS802_11AUTOUNKNOWN">
            <summary>
            Auto unknown mode of operation.
            </summary>
        </member>
        <member name="T:Symbol.Fusion.WLAN.Adapter.WLANPhysicalLayer">
            <summary>
            Enumerates the network physical layers that a WLAN radio can support.
            </summary>
        </member>
        <member name="F:Symbol.Fusion.WLAN.Adapter.WLANPhysicalLayer.IEEE802_11_RESERVED">
            <summary>
            Reserved 802.11 entry.
            </summary>
        </member>
        <member name="F:Symbol.Fusion.WLAN.Adapter.WLANPhysicalLayer.IEEE802_11b">
            <summary>
            Specifies 802.11b.
            </summary>
        </member>
        <member name="F:Symbol.Fusion.WLAN.Adapter.WLANPhysicalLayer.IEEE802_11a">
            <summary>
            Specifies 802.11a
            </summary>
        </member>
        <member name="F:Symbol.Fusion.WLAN.Adapter.WLANPhysicalLayer.IEEE802_11g">
            <summary>
            Specifies 802.11g
            </summary>
        </member>
        <member name="F:Symbol.Fusion.WLAN.Adapter.WLANPhysicalLayer.IEEE802_11n">
            <summary>
            Specifies 802.11n
            </summary>
        </member>
        <member name="F:Symbol.Fusion.WLAN.Adapter.WLANPhysicalLayer.IEEE802_11_UNKNOWN">
            <summary>
            Unknown radio band.
            </summary>
        </member>
        <member name="T:Symbol.Fusion.WLAN.Adapter.RFBandwidth">
            <summary>
            Enumerates the network bands that a WLAN radio can support.
            </summary>
        </member>
        <member name="F:Symbol.Fusion.WLAN.Adapter.RFBandwidth.RF_2_4GHZ">
            <summary>
            Specifies 2.4 GHz band.
            </summary>
        </member>
        <member name="F:Symbol.Fusion.WLAN.Adapter.RFBandwidth.RF_5GHZ">
            <summary>
            Specifies 5 GHz band
            </summary>
        </member>
        <member name="T:Symbol.Fusion.WLAN.Adapter.PACManager">
            <summary>
            The class representing the settings for PAC.
            </summary>
        </member>
        <member name="P:Symbol.Fusion.WLAN.Adapter.PACManager.AutoRefreshing">
            <summary>
            Get/Set current state of the PAC Auto Refreshing Option.
            </summary>
        </member>
        <member name="P:Symbol.Fusion.WLAN.Adapter.PACManager.AutoProvisioning">
            <summary>
            Get/Set current state of the PAC Auto Provisioning Option.
            </summary>
        </member>
        <member name="T:Symbol.Fusion.WLAN.Adapter.IPv6Manager">
            <summary>
            The class representing the settings for IPv6.
            </summary>
        </member>
        <member name="M:Symbol.Fusion.WLAN.Adapter.IPv6Manager.Reset">
            <summary>
            Resets the IPv6 interface of the WLAN radio.
            </summary>
        </member>
        <member name="P:Symbol.Fusion.WLAN.Adapter.IPv6Manager.Enabled">
            <summary>
            Get/Set the state of the IPv6 (Enable / Disable) option. 
            The support for Setting the option is available in Command Mode Only.
            </summary>
        </member>
        <member name="T:Symbol.Fusion.WLAN.NDISInfo">
            <summary>
            The class representing the Network Driver Interface Specification (NDIS) Information.
            </summary>
        </member>
        <member name="M:Symbol.Fusion.WLAN.NDISInfo.Get">
            <summary>
            Re-populates the NDIS information for all the OIDs represented by the classes NDISInfoOID, OID = BSSID(OID_802_11_BSSID), BSSIDList(OID_802_11_BSSID_LIST), Configuration(OID_802_11_CONFIGURATION), etc..
            Supported when accessed via Symbol.Fusion.WLAN.Adapter.NDIS.Get().
            </summary>
        </member>
        <member name="P:Symbol.Fusion.WLAN.NDISInfo.BSSID">
            <summary>
            Includes the media access control (MAC) address of the access point to associate with.
            After this OID is set, the device can only associate with an access point (AP) with the desired BSSID.
            </summary>
        </member>
        <member name="P:Symbol.Fusion.WLAN.NDISInfo.BSSIDList">
            <summary>
            Includes the list containing all of the detected BSSIDs and their attributes. 
            </summary>
        </member>
        <member name="P:Symbol.Fusion.WLAN.NDISInfo.Configuration">
            <summary>
            The underlying NIC's radio configuration parameters.
            </summary>
        </member>
        <member name="P:Symbol.Fusion.WLAN.NDISInfo.FragmentationThreshold">
            <summary>
            Includes the packet fragmentation threshold of the underlying NIC. 
            </summary>
        </member>
        <member name="P:Symbol.Fusion.WLAN.NDISInfo.InfrastructureMode">
            <summary>
            Includes the type of 802.11 network the device uses to associate with.
            </summary>
        </member>
        <member name="P:Symbol.Fusion.WLAN.NDISInfo.NetworkTypeInUse">
            <summary>
            Includes the network type that the underlying NIC's physical layer (PHY) will use.
            </summary>
        </member>
        <member name="P:Symbol.Fusion.WLAN.NDISInfo.NumberOfAntennas">
            <summary>
            Includes the number of antennas on the underlying NIC's radio. 
            </summary>
        </member>
        <member name="P:Symbol.Fusion.WLAN.NDISInfo.PowerMode">
            <summary>
            Includes the power mode of the underlying NIC.
            </summary>
        </member>
        <member name="P:Symbol.Fusion.WLAN.NDISInfo.RSSI">
            <summary>
            Includes the current value of the received signal strength indication (RSSI).
            The RSSI is measured in dBm.
            </summary>
        </member>
        <member name="P:Symbol.Fusion.WLAN.NDISInfo.RTSThreshold">
            <summary>
            Includes the request to send (RTS) threshold of the underlying NIC.
            </summary>
        </member>
        <member name="P:Symbol.Fusion.WLAN.NDISInfo.SSID">
            <summary>
            Includes the service set identifier (SSID) of the BSS with which the device can associate.
            </summary>
        </member>
        <member name="P:Symbol.Fusion.WLAN.NDISInfo.Statistics">
            <summary>
            Includes the current statistics for the IEEE 802.11 interface
            </summary>
        </member>
        <member name="P:Symbol.Fusion.WLAN.NDISInfo.TXPowerlevel">
            <summary>
            Includes the transmit power level of the 802.11 NIC. The value is specified in milliwatts (mW).
            </summary>
        </member>
        <member name="M:Symbol.Fusion.WLAN.NDISInfoBSSID.Get">
            <summary>
            Re-populates data for this particlular OID only.
            </summary>
        </member>
        <member name="M:Symbol.Fusion.WLAN.NDISInfoBSSIDList.Get">
            <summary>
            Re-populates data for this particlular OID only.
            </summary>
        </member>
        <member name="M:Symbol.Fusion.WLAN.NDISInfoBSSIDList.Scan">
            <summary>
            Requests to perform a network scan of BSSIDs and SSIDs. 
            No data is associated with this.
            This provides the support for the OID BSSID_LIST_SCAN (OID_802_11_BSSID_LIST_SCAN).
            The scanned data will be re-populated into BSSIDList once the user calls Get() after calling Scan().
            </summary>
        </member>
        <member name="T:Symbol.Fusion.WLAN.NDIS_WLAN_BSSID">
            <summary>
             
            </summary>
        </member>
        <member name="P:Symbol.Fusion.WLAN.NDIS_WLAN_BSSID.MACAddress">
            <summary>
             
            </summary>
        </member>
        <member name="P:Symbol.Fusion.WLAN.NDIS_WLAN_BSSID.SSID">
            <summary>
             
            </summary>
        </member>
        <member name="P:Symbol.Fusion.WLAN.NDIS_WLAN_BSSID.Privacy">
            <summary>
             
            </summary>
        </member>
        <member name="P:Symbol.Fusion.WLAN.NDIS_WLAN_BSSID.RSSI">
            <summary>
             
            </summary>
        </member>
        <member name="P:Symbol.Fusion.WLAN.NDIS_WLAN_BSSID.NetworkTypeInUse">
            <summary>
             
            </summary>
        </member>
        <member name="P:Symbol.Fusion.WLAN.NDIS_WLAN_BSSID.Configuration">
            <summary>
             
            </summary>
        </member>
        <member name="P:Symbol.Fusion.WLAN.NDIS_WLAN_BSSID.NetworkInfrastructure">
            <summary>
             
            </summary>
        </member>
        <member name="P:Symbol.Fusion.WLAN.NDIS_WLAN_BSSID.SupportedRates">
            <summary>
             
            </summary>
        </member>
        <member name="P:Symbol.Fusion.WLAN.NDIS_WLAN_BSSID.IEs">
            <summary>
             
            </summary>
        </member>
        <member name="T:Symbol.Fusion.WLAN.NDIS_802_11_Configuration">
            <summary>
             
            </summary>
        </member>
        <member name="M:Symbol.Fusion.WLAN.NDIS_802_11_Configuration.Get">
            <summary>
            Re-populates the NDIS information for the particular OID OID_802_11_CONFIGURATION.
            Only supported when accessed via Symbol.Fusion.WLAN.Adapter.NDIS.Configuration.Get().
            </summary>
        </member>
        <member name="P:Symbol.Fusion.WLAN.NDIS_802_11_Configuration.Length">
            <summary>
             
            </summary>
        </member>
        <member name="P:Symbol.Fusion.WLAN.NDIS_802_11_Configuration.BeaconPeriod">
            <summary>
             
            </summary>
        </member>
        <member name="P:Symbol.Fusion.WLAN.NDIS_802_11_Configuration.ATIMWindow">
            <summary>
             
            </summary>
        </member>
        <member name="P:Symbol.Fusion.WLAN.NDIS_802_11_Configuration.DSConfig">
            <summary>
             
            </summary>
        </member>
        <member name="P:Symbol.Fusion.WLAN.NDIS_802_11_Configuration.ConfigFH">
            <summary>
             
            </summary>
        </member>
        <member name="T:Symbol.Fusion.WLAN.NDIS_802_11_Configuration_FH">
            <summary>
             
            </summary>
        </member>
        <member name="P:Symbol.Fusion.WLAN.NDIS_802_11_Configuration_FH.Length">
            <summary>
             
            </summary>
        </member>
        <member name="P:Symbol.Fusion.WLAN.NDIS_802_11_Configuration_FH.HopPattern">
            <summary>
             
            </summary>
        </member>
        <member name="P:Symbol.Fusion.WLAN.NDIS_802_11_Configuration_FH.HopSet">
            <summary>
             
            </summary>
        </member>
        <member name="P:Symbol.Fusion.WLAN.NDIS_802_11_Configuration_FH.DwellTime">
            <summary>
             
            </summary>
        </member>
        <member name="M:Symbol.Fusion.WLAN.NDISInfoFragmentationThreshold.Get">
            <summary>
            Re-populates data for this particlular OID only.
            </summary>
        </member>
        <member name="M:Symbol.Fusion.WLAN.NDISInfoInfrastructureMode.Get">
            <summary>
            Re-populates data for this particlular OID only.
            </summary>
        </member>
        <member name="M:Symbol.Fusion.WLAN.NDISInfoNetworkTypeInUse.Get">
            <summary>
            Re-populates data for this particlular OID only.
            </summary>
        </member>
        <member name="M:Symbol.Fusion.WLAN.NDISInfoNumberOfAntennas.Get">
            <summary>
            Re-populates data for this particlular OID only.
            </summary>
        </member>
        <member name="M:Symbol.Fusion.WLAN.NDISInfoPowerMode.Get">
            <summary>
            Re-populates data for this particlular OID only.
            </summary>
        </member>
        <member name="M:Symbol.Fusion.WLAN.NDISInfoRSSI.Get">
            <summary>
            Re-populates data for this particlular OID only.
            </summary>
        </member>
        <member name="M:Symbol.Fusion.WLAN.NDISInfoRTSThreshold.Get">
            <summary>
            Re-populates data for this particlular OID only.
            </summary>
        </member>
        <member name="M:Symbol.Fusion.WLAN.NDISInfoSSID.Get">
            <summary>
            Re-populates data for this particlular OID only.
            </summary>
        </member>
        <member name="T:Symbol.Fusion.WLAN.NDIS_802_11_Statistics">
            <summary>
             
            </summary>
        </member>
        <member name="M:Symbol.Fusion.WLAN.NDIS_802_11_Statistics.Get">
            <summary>
            Re-populates the NDIS information for the particular OID OID_802_11_STATISTICS.
            Supported when accessed via Symbol.Fusion.WLAN.Adapter.NDIS.Statistics.Get().
            </summary>
        </member>
        <member name="P:Symbol.Fusion.WLAN.NDIS_802_11_Statistics.TransmittedFragmentCount">
            <summary>
             
            </summary>
        </member>
        <member name="P:Symbol.Fusion.WLAN.NDIS_802_11_Statistics.MulticastTransmittedFrameCount">
            <summary>
             
            </summary>
        </member>
        <member name="P:Symbol.Fusion.WLAN.NDIS_802_11_Statistics.FailedCount">
            <summary>
             
            </summary>
        </member>
        <member name="P:Symbol.Fusion.WLAN.NDIS_802_11_Statistics.RetryCount">
            <summary>
             
            </summary>
        </member>
        <member name="P:Symbol.Fusion.WLAN.NDIS_802_11_Statistics.MultipleRetryCount">
            <summary>
             
            </summary>
        </member>
        <member name="P:Symbol.Fusion.WLAN.NDIS_802_11_Statistics.RTSSuccessCount">
            <summary>
             
            </summary>
        </member>
        <member name="P:Symbol.Fusion.WLAN.NDIS_802_11_Statistics.RTSFailureCount">
            <summary>
             
            </summary>
        </member>
        <member name="P:Symbol.Fusion.WLAN.NDIS_802_11_Statistics.ACKFailureCount">
            <summary>
             
            </summary>
        </member>
        <member name="P:Symbol.Fusion.WLAN.NDIS_802_11_Statistics.FrameDuplicateCount">
            <summary>
             
            </summary>
        </member>
        <member name="P:Symbol.Fusion.WLAN.NDIS_802_11_Statistics.ReceivedFragmentCount">
            <summary>
             
            </summary>
        </member>
        <member name="P:Symbol.Fusion.WLAN.NDIS_802_11_Statistics.MulticastReceivedFrameCount">
            <summary>
             
            </summary>
        </member>
        <member name="P:Symbol.Fusion.WLAN.NDIS_802_11_Statistics.FCSErrorCount">
            <summary>
             
            </summary>
        </member>
        <member name="P:Symbol.Fusion.WLAN.NDIS_802_11_Statistics.TKIPLocalMICFailures">
            <summary>
             
            </summary>
        </member>
        <member name="P:Symbol.Fusion.WLAN.NDIS_802_11_Statistics.TKIPICVErrors">
            <summary>
             
            </summary>
        </member>
        <member name="P:Symbol.Fusion.WLAN.NDIS_802_11_Statistics.TKIPCounterMeasuresInvoked">
            <summary>
             
            </summary>
        </member>
        <member name="P:Symbol.Fusion.WLAN.NDIS_802_11_Statistics.TKIPReplays">
            <summary>
             
            </summary>
        </member>
        <member name="P:Symbol.Fusion.WLAN.NDIS_802_11_Statistics.CCMPFormatErrors">
            <summary>
             
            </summary>
        </member>
        <member name="P:Symbol.Fusion.WLAN.NDIS_802_11_Statistics.CCMPReplays">
            <summary>
             
            </summary>
        </member>
        <member name="P:Symbol.Fusion.WLAN.NDIS_802_11_Statistics.CCMPDecryptErrors">
            <summary>
             
            </summary>
        </member>
        <member name="P:Symbol.Fusion.WLAN.NDIS_802_11_Statistics.FourWayHandshakeFailures">
            <summary>
             
            </summary>
        </member>
        <member name="P:Symbol.Fusion.WLAN.NDIS_802_11_Statistics.WEPUndecryptableCount">
            <summary>
             
            </summary>
        </member>
        <member name="P:Symbol.Fusion.WLAN.NDIS_802_11_Statistics.WEPICVErrorCount">
            <summary>
             
            </summary>
        </member>
        <member name="P:Symbol.Fusion.WLAN.NDIS_802_11_Statistics.DecryptSuccessCount">
            <summary>
             
            </summary>
        </member>
        <member name="P:Symbol.Fusion.WLAN.NDIS_802_11_Statistics.DecryptFailureCount">
            <summary>
             
            </summary>
        </member>
        <member name="M:Symbol.Fusion.WLAN.NDISInfoTXPowerlevel.Get">
            <summary>
            Re-populates data for this particlular OID only.
            </summary>
        </member>
        <member name="T:Symbol.Fusion.WLAN.NDIS_802_11_NetworkType">
            <summary>
            Enumerates the network types that the underlying NIC's physical layer (PHY) will use.
            </summary>
        </member>
        <member name="F:Symbol.Fusion.WLAN.NDIS_802_11_NetworkType.FH">
            <summary>
            Indicates the physical layer for the frequency-hopping spread-spectrum radio. 
            </summary>
        </member>
        <member name="F:Symbol.Fusion.WLAN.NDIS_802_11_NetworkType.DS">
            <summary>
            Indicates the physical layer for the direct-sequence spread-spectrum radio. 
            </summary>
        </member>
        <member name="F:Symbol.Fusion.WLAN.NDIS_802_11_NetworkType.OFDM5">
            <summary>
            Indicates the physical layer for 5-GHz OFDM radios. 
            </summary>
        </member>
        <member name="F:Symbol.Fusion.WLAN.NDIS_802_11_NetworkType.OFDM24">
            <summary>
            Indicates the physical layer for 2.4-GHz OFDM radios. 
            </summary>
        </member>
        <member name="F:Symbol.Fusion.WLAN.NDIS_802_11_NetworkType.Automode">
            <summary>
            Indicates that the NIC will operate on all supported and enabled physical layers.
            </summary>
        </member>
        <member name="T:Symbol.Fusion.WLAN.NDIS_802_11_NetworkInfrastructure">
            <summary>
            Enumerates the types of 802.11 network the device uses to associate with.
            </summary>
        </member>
        <member name="F:Symbol.Fusion.WLAN.NDIS_802_11_NetworkInfrastructure.IBSS">
            <summary>
            Specifies independent basic service set (IBSS) network mode. This mode is also known as ad hoc mode.
            </summary>
        </member>
        <member name="F:Symbol.Fusion.WLAN.NDIS_802_11_NetworkInfrastructure.Infrastructure">
            <summary>
            Specifies infrastructure network mode. This mode is also known as extended service set (ESS) mode. 
            </summary>
        </member>
        <member name="F:Symbol.Fusion.WLAN.NDIS_802_11_NetworkInfrastructure.AutoUnknown">
            <summary>
            Specifies automatic network mode. In this mode, the device can switch between ad hoc and infrastructure networks as required.
             The use of this setting is not recommended.
            </summary>
        </member>
        <member name="T:Symbol.Fusion.WLAN.NDIS_802_11_PowerMode">
            <summary>
            Enumerates the power modes of the underlying NIC.
            </summary>
        </member>
        <member name="F:Symbol.Fusion.WLAN.NDIS_802_11_PowerMode.PowerModeCAM">
            <summary>
            Specifies Constantly Awake Mode (CAM). 
            When the power mode is set to CAM, the device is always on. 
            If it supports power saving modes, it is recommended that the device specify a listen interval when it associates with the access point. Specifying a listen interval allows the power mode to be changed from CAM mode to another power mode without requiring a reassociation to the access point.
            </summary>
        </member>
        <member name="F:Symbol.Fusion.WLAN.NDIS_802_11_PowerMode.PowerModeMAX_PSP">
            <summary>
            Specifies Power Save Polling (PSP) that utilizes the maximum (MAX) power saving.
            A power mode of MAX results in the greatest power savings for the IEEE 802.11 NIC radio. 
            </summary>
        </member>
        <member name="F:Symbol.Fusion.WLAN.NDIS_802_11_PowerMode.PowerModeFast_PSP">
            <summary>
            Specifies Power Save Polling (PSP) that utilizes the fastest power-saving mode. 
            This power mode must provide the best combination of network performance and power usage. 
            </summary>
        </member>
        <member name="T:Symbol.Fusion.WLAN.StatusChangeArgs">
            <summary>
            Status class contains data related to the new status when an event is fired.
            </summary>
        </member>
        <member name="M:Symbol.Fusion.WLAN.StatusChangeArgs.#ctor(Symbol.Fusion.WLAN.Adapter,Symbol.Fusion.WLAN.Adapter.SignalQualityRange,System.Int32)">
            <summary>
            Create a Status object
            </summary>
            <param name="adapter">The Adapter that this information is related to</param>
            <param name="signalQuality">The new signal Quality</param>
            <param name="signalStrength">The new signal Strength</param>
        </member>
        <member name="P:Symbol.Fusion.WLAN.StatusChangeArgs.Change">
            <summary>
            Retrieve the cause for initiating the notification.
            </summary>
            <value>
            <see cref="T:Symbol.Fusion.WLAN.StatusChangeArgs.ChangeType"/> enumeration that defines causes for a
            change in status.
            </value>
        </member>
        <member name="P:Symbol.Fusion.WLAN.StatusChangeArgs.Adapter">
            <summary>
            Defines the adapter in which the status change occured
            </summary>
        </member>
        <member name="P:Symbol.Fusion.WLAN.StatusChangeArgs.SignalQuality">
            <summary>
            Defines the new signal quality after the status change
            </summary>
        </member>
        <member name="P:Symbol.Fusion.WLAN.StatusChangeArgs.SignalStrength">
            <summary>
            Defines the new signal strength after the status change
            </summary>
        </member>
        <member name="T:Symbol.Fusion.WLAN.StatusChangeArgs.ChangeType">
            <summary>
            Changes in the following cause events to fire
            </summary>
        </member>
        <member name="F:Symbol.Fusion.WLAN.StatusChangeArgs.ChangeType.SIGNAL">
            <summary>
            The signal strength or association status has changed.
            </summary>
        </member>
        <member name="T:Symbol.Fusion.WLAN.StatusChangeEventArgs">
            <summary>
            This class contains data related to a status change. 
            </summary>
        </member>
        <member name="M:Symbol.Fusion.WLAN.StatusChangeEventArgs.#ctor(Symbol.Fusion.WLAN.StatusChangeEventArgs.StatusChanges,System.Object)">
            <summary>
            Status change event args constructor
            </summary>
            <param name="statusChange">The change that occurred.</param>
            <param name="changeData">Data associated with the change</param>
        </member>
        <member name="P:Symbol.Fusion.WLAN.StatusChangeEventArgs.StatusChange">
            <summary>
            The Status Change that fired the event
            </summary>
            <value>
            <see cref="T:Symbol.Fusion.WLAN.StatusChangeEventArgs.StatusChanges"/> enumeration that defines causes for a
            change in status.
            </value>
        </member>
        <member name="P:Symbol.Fusion.WLAN.StatusChangeEventArgs.StatusChangeData">
            <summary>
            Provides data associated with the Status Change
            </summary>
            <remarks>The data in StatusChangeData property depends on the value of StatusChange. If StatusChange is
            AdadapterPowerON or AdapterPowerOFF then StatusChange property holds the Adapter in which the Power
            change occurred.</remarks>
        </member>
        <member name="T:Symbol.Fusion.WLAN.StatusChangeEventArgs.StatusChanges">
            <summary>
            Changes that cause events to fire
            </summary>
        </member>
        <member name="F:Symbol.Fusion.WLAN.StatusChangeEventArgs.StatusChanges.AdapterPowerON">
            <summary>
            Adapter Powered ON
            </summary>
        </member>
        <member name="F:Symbol.Fusion.WLAN.StatusChangeEventArgs.StatusChanges.AdapterPowerOFF">
            <summary>
            Adapter Powered OFF
            </summary>
        </member>
        <member name="F:Symbol.Fusion.WLAN.StatusChangeEventArgs.StatusChanges.AccesspointChanged">
            <summary>
            The Accesspoint/BSSID has changed.
            This means that the adapter has associated with a different accesspoint/BSSID.
            </summary>
        </member>
        <member name="T:Symbol.Fusion.WLAN.StatusChangeEventArgs.APChangedEventData">
            <summary>
            This class contains data related to the change in the accesspoint/BSSID. 
            </summary>
        </member>
        <member name="M:Symbol.Fusion.WLAN.StatusChangeEventArgs.APChangedEventData.#ctor(System.String,Symbol.Fusion.WLAN.Adapter)">
            <summary>
            The constructor.
            </summary>
        </member>
        <member name="P:Symbol.Fusion.WLAN.StatusChangeEventArgs.APChangedEventData.BSSID">
            <summary>
            The public property for BSSID.
            This is the MAC address of the accesspoint with which the adapter is associated.
            </summary>
        </member>
        <member name="P:Symbol.Fusion.WLAN.StatusChangeEventArgs.APChangedEventData.Adapter">
            <summary>
            The public property for Adapter.
            </summary>
        </member>
        <member name="T:Symbol.Fusion.Diagnostics">
            <summary>
            Fusion.Diagnostics class provides methods to save Dignostic
            information related to Wireless Networking in to a file.
            This information could be useful in diagnosing Wireless Networking
            issues affecting the mobile device.
            </summary>
        </member>
        <member name="F:Symbol.Fusion.Diagnostics.m_nLogFileSize">
            <summary>
            Contains the size of the log file. This will be initialized to a 
            predefined value since there is no API function provided to get the
            size.
            </summary>
        </member>
        <member name="F:Symbol.Fusion.Diagnostics.m_sLogFileName">
            <summary>
            Contains the name of the log file. This will be initialized to a 
            default value since there is no API function proviced to get the 
            name.
            </summary>
        </member>
        <member name="M:Symbol.Fusion.Diagnostics.#ctor(Symbol.Fusion.Config)">
            <summary>
            Diagnostics constructor
            </summary>
        </member>
        <member name="M:Symbol.Fusion.Diagnostics.Dispose">
            <summary>
            Called during Config dispose. Used to make the cached data not accessible
            </summary>
        </member>
        <member name="M:Symbol.Fusion.Diagnostics.SaveLog">
            <summary>
            Save diagnostics information to the LogFile.
            </summary>
            <returns>Returns an encoding of the API result</returns>
        </member>
        <member name="M:Symbol.Fusion.Diagnostics.ClearLog">
            <summary>
            Clear memory buffers that hold diagnostics information. Available in Command Mode only.
            </summary>
            <returns>Returns an encoding of the API result</returns>
        </member>
        <member name="P:Symbol.Fusion.Diagnostics.LogFileSize">
            <summary>
            The maximum allowed size of the diagnostics log file. Available in Command Mode only.
            </summary>
        </member>
        <member name="P:Symbol.Fusion.Diagnostics.LogFileName">
            <summary>
            The name of the diagnostics log file.
            </summary>
        </member>
        <member name="T:Symbol.Fusion.WLAN.WLANMarshal">
            <summary>
            This class is used to separate WLAN related API calls from FusionMarshal
            </summary>
        </member>
        <member name="M:Symbol.Fusion.WLAN.WLANMarshal.#ctor(Symbol.Fusion.Action)">
            <summary>
            Create WLANMarshal
            </summary>
            <param name="action">An Action reference</param>
            <remarks>A WLANMarshso object should be created only after creating a FusionMarshal
            object and opening Fusion.</remarks>
        </member>
        <member name="M:Symbol.Fusion.WLAN.WLANMarshal.OpenThreadHandle">
            <summary>
            Open the handle for the event thread, if not already opened.
            </summary>
        </member>
        <member name="M:Symbol.Fusion.WLAN.WLANMarshal.CloseThreadHandle">
            <summary>
            Close the thread handle if all references are removed.
            </summary>
        </member>
        <member name="M:Symbol.Fusion.WLAN.WLANMarshal.GetWLANAdapters(Symbol.Fusion.FINTRF_CONTEXTHANDLE@,Symbol.Fusion.WLAN.AccessWLANAdapterIDList)">
            <summary>
             Marshal the Fusion get Adapters call
            </summary>
            <param name="fusionHandle">The handle to fusion API</param>
            <param name="adapterIDList"></param>
        </member>
        <member name="M:Symbol.Fusion.WLAN.WLANMarshal.GetWLANAdapterInfo(Symbol.Fusion.FINTRF_CONTEXTHANDLE@,Symbol.Fusion.WLAN.FINTRF_AdapterInfo@,System.UInt32)">
            <summary>
            Get AdapterInfo, this function has to be called after getting adapterIDs from the API.
            A call to GetAdapters should be used to get the Adapter IDs.
            </summary>
            <param name="fusionHandle">The handle to fusion API</param>
            <param name="adapterInfo"></param>
            <param name="adapterID">The ID or the Adapter handle obtained from the API</param>
        </member>
        <member name="M:Symbol.Fusion.WLAN.WLANMarshal.GetWLANAdapterPowerState(Symbol.Fusion.FINTRF_CONTEXTHANDLE@,System.UInt32)">
            <summary>
            Get the state of an adapter
            </summary>
            <param name="fusionHandle">The handle to fusion API</param>
            <param name="adapterID">The adapter handle</param>
            <returns>Returns the power state of the adapter</returns>
        </member>
        <member name="M:Symbol.Fusion.WLAN.WLANMarshal.SetWLANAdapterPowerState(Symbol.Fusion.FINTRF_CONTEXTHANDLE@,System.UInt32,Symbol.Fusion.WLAN.Adapter.PowerStates)">
            <summary>
            Set the adapter power state
            </summary>
            <param name="fusionHandle">The handle to fusion API</param>
            <param name="adapterID">The ID of the adapter</param>
            <param name="powerState">The state to set</param>
        </member>
        <member name="M:Symbol.Fusion.WLAN.WLANMarshal.GetWLANSignalStrength(Symbol.Fusion.FINTRF_CONTEXTHANDLE@,System.UInt32)">
            <summary>
            Get the signal strength related with the adapter
            </summary>
            <param name="fusionHandle">The handle to fusion API</param>
            <param name="adapterID">The ID of the adapter</param>
            <returns></returns>
        </member>
        <member name="M:Symbol.Fusion.WLAN.WLANMarshal.GetWLANSignalQuality(Symbol.Fusion.FINTRF_CONTEXTHANDLE@,System.UInt32)">
            <summary>
            Get the signal Quality of the adapter from the API
            </summary>
            <param name="fusionHandle">The handle to fusion API</param>
            <param name="adapterID"></param>
            <returns></returns>
        </member>
        <member name="M:Symbol.Fusion.WLAN.WLANMarshal.GetActiveWLANProfile(Symbol.Fusion.FINTRF_CONTEXTHANDLE@,Symbol.Fusion.WLAN.FINTRF_WLANProfileIdentity@,System.UInt32)">
            <summary>
            This function finds the active profile associated with the adapter specified.
            </summary>
            <param name="fusionHandle">The handle to fusion API</param>
            <param name="profileIdentity">The ID of the profile</param>
            <param name="adapterID">The ID of the adapter associated with the requested profile</param>
        </member>
        <member name="M:Symbol.Fusion.WLAN.WLANMarshal.GetWLANSSID(Symbol.Fusion.FINTRF_CONTEXTHANDLE@,System.UInt32)">
            <summary>
            Get the SSID associated with the given Adapter
            </summary>
            <param name="fusionHandle">The handle to fusion API</param>
            <param name="adapterID">The adapter handle</param>
            <returns>a byte array containing the SSID</returns>
        </member>
        <member name="M:Symbol.Fusion.WLAN.WLANMarshal.GetWLANProfiles(Symbol.Fusion.FINTRF_CONTEXTHANDLE@,Symbol.Fusion.WLAN.AccessWLANProfileList)">
            <summary>
            This function fills the profiles list by getting values from the API
            </summary>
            <param name="fusionHandle">The handle to fusion API</param>
            <param name="profiles">The list of profiles to be filled</param>
        </member>
        <member name="M:Symbol.Fusion.WLAN.WLANMarshal.CreateAdhocProfile(Symbol.Fusion.FINTRF_CONTEXTHANDLE@,Symbol.Fusion.WLAN.AdhocProfileData)">
            <summary>
            Create an Adhoc profile using profile data specified
            </summary>
            <param name="fusionHandle">The handle to fusion API</param>
            <param name="profileData"></param>
            <returns>Rerurns the ProfileID</returns>
        </member>
        <member name="M:Symbol.Fusion.WLAN.WLANMarshal.CreateInfrastructureProfile(Symbol.Fusion.FINTRF_CONTEXTHANDLE@,Symbol.Fusion.WLAN.InfrastructureProfileData)">
            <summary>
            Create an infrastructure profile using profile data specified
            </summary>
            <param name="fusionHandle">The handle to fusion API</param>
            <param name="profileData"></param>
            <returns>Returns the ProfileID</returns>
        </member>
        <member name="M:Symbol.Fusion.WLAN.WLANMarshal.DeleteProfile(Symbol.Fusion.FINTRF_CONTEXTHANDLE@,Symbol.Fusion.WLAN.Profile)">
            <summary>
            Delete the profile specified
            </summary>
            <param name="fusionHandle">The handle to fusion API</param>
            <param name="profile">Profile to delete</param>
            <returns>Returns an encoding of the API result</returns>
        </member>
        <member name="M:Symbol.Fusion.WLAN.WLANMarshal.DeleteAll(Symbol.Fusion.FINTRF_CONTEXTHANDLE@)">
            <summary>
            Delete all profiles
            </summary>
            <param name="fusionHandle">The handle to fusion API</param>
            <returns>Returns an encoding of the API result</returns>
        </member>
        <member name="M:Symbol.Fusion.WLAN.WLANMarshal.RenewDHCP(Symbol.Fusion.FINTRF_CONTEXTHANDLE@,Symbol.Fusion.WLAN.Adapter)">
            <summary>
            Renew DHCP IP address of the dpecified adapter
            </summary>
            <param name="fusionHandle">The handle to fusion API</param>
            <param name="adapter">Adapter to renew DHCP</param>
            <returns>Returns an encoding of the API result</returns>
        </member>
        <member name="M:Symbol.Fusion.WLAN.WLANMarshal.ConnectToProfile(Symbol.Fusion.FINTRF_CONTEXTHANDLE@,Symbol.Fusion.WLAN.Profile,System.Boolean)">
            <summary>
            Make the profile specified the active profile. 
            </summary>
            <param name="fusionHandle">The handle to fusion API</param>
            <param name="profile">profile to connect to</param>
            <param name="persistant">make the connection persistant if set to true</param>
            <returns>Returns an encoding of the API result</returns>
        </member>
        <member name="M:Symbol.Fusion.WLAN.WLANMarshal.CancelConnectionPersistance(Symbol.Fusion.FINTRF_CONTEXTHANDLE@)">
            <summary>
            Allow for profile roaming by cancelling connection persistancy
            </summary>
            <param name="fusionHandle">The handle to fusion API</param>
            <returns>Returns an encoding of the API result</returns>
        </member>
        <member name="M:Symbol.Fusion.WLAN.WLANMarshal.SetProfileAttributes(Symbol.Fusion.FINTRF_CONTEXTHANDLE@,System.String,System.Int32,System.Int32)">
            <summary>
            Set Priority and Operational status of a profile
            </summary>
            <param name="fusionHandle">The handle to fusion API</param>
            <param name="profileID">The ID of the Profile</param>
            <param name="priority">Any Priority Change</param>
            <param name="operationalStatus">Profile is Enabled or Disabled</param>
            <returns></returns>
        </member>
        <member name="M:Symbol.Fusion.WLAN.WLANMarshal.GetSSIDs(Symbol.Fusion.FINTRF_CONTEXTHANDLE@,Symbol.Fusion.WLAN.AccessSSIDList,System.UInt32)">
            <summary>
            Marshal the FusionInterface call related to retrieving the SSID list.
            </summary>
        </member>
        <member name="M:Symbol.Fusion.WLAN.WLANMarshal.GetAccessPoints(Symbol.Fusion.FINTRF_CONTEXTHANDLE@,Symbol.Fusion.WLAN.AccessSSIDList,System.UInt32,System.String)">
            <summary>
            Marshal the FusionInterface call related to retrieving the list of access points for a given SSID.
            </summary>
        </member>
        <member name="M:Symbol.Fusion.WLAN.WLANMarshal.GetPeers(Symbol.Fusion.FINTRF_CONTEXTHANDLE@,Symbol.Fusion.WLAN.AccessSSIDList,System.UInt32,System.String)">
            <summary>
            Marshal the FusionInterface call related to retrieving the list of peers for a given SSID.
            </summary>
        </member>
        <member name="M:Symbol.Fusion.WLAN.WLANMarshal.GetRFBand(Symbol.Fusion.FINTRF_CONTEXTHANDLE@,System.UInt32)">
            <summary>
            Marshal the FusionInterface GetRFBand.
            </summary>
        </member>
        <member name="M:Symbol.Fusion.WLAN.WLANMarshal.SetRFBand(Symbol.Fusion.FINTRF_CONTEXTHANDLE@,System.UInt32,System.UInt32)">
            <summary>
            Marshal the FusionInterface SetRFBand.
            </summary>
        </member>
        <member name="M:Symbol.Fusion.WLAN.WLANMarshal.Get80211dStatus(Symbol.Fusion.FINTRF_CONTEXTHANDLE@,System.UInt32)">
            <summary>
            Marshal the FusionInterface GetWLANAdapter80211dStatus.
            </summary>
        </member>
        <member name="M:Symbol.Fusion.WLAN.WLANMarshal.Set80211dStatus(Symbol.Fusion.FINTRF_CONTEXTHANDLE@,System.UInt32,System.Boolean)">
            <summary>
            Marshal the FusionInterface SetWLANAdapter80211dStatus.
            </summary>
        </member>
        <member name="M:Symbol.Fusion.WLAN.WLANMarshal.GetIPManagementStatus(Symbol.Fusion.FINTRF_CONTEXTHANDLE@,System.UInt32)">
            <summary>
            Marshal the FusionInterface GetIPManagementStatus.
            </summary>
        </member>
        <member name="M:Symbol.Fusion.WLAN.WLANMarshal.SetIPManagementStatus(Symbol.Fusion.FINTRF_CONTEXTHANDLE@,System.UInt32,System.Boolean)">
            <summary>
            Marshal the FusionInterface SetIPManagementStatus.
            </summary>
        </member>
        <member name="M:Symbol.Fusion.WLAN.WLANMarshal.GetAutoTimeConfigStatus(Symbol.Fusion.FINTRF_CONTEXTHANDLE@,System.UInt32)">
            <summary>
            Marshal the FusionInterface GetAutoTimeConfigStatus.
            </summary>
        </member>
        <member name="M:Symbol.Fusion.WLAN.WLANMarshal.SetAutoTimeConfigStatus(Symbol.Fusion.FINTRF_CONTEXTHANDLE@,System.UInt32,System.Boolean)">
            <summary>
            Marshal the FusionInterface SetAutoTimeConfigStatus.
            </summary>
        </member>
        <member name="M:Symbol.Fusion.WLAN.WLANMarshal.GetAdapterCountryCode(Symbol.Fusion.FINTRF_CONTEXTHANDLE@,System.UInt32)">
            <summary>
            Marshal the FusionInterface GetAutoTimeConfigStatus.
            </summary>
        </member>
        <member name="M:Symbol.Fusion.WLAN.WLANMarshal.SetAdapterCountryCode(Symbol.Fusion.FINTRF_CONTEXTHANDLE@,System.UInt32,System.String)">
            <summary>
            Marshal the FusionInterface SetAutoTimeConfigStatus.
            </summary>
        </member>
        <member name="M:Symbol.Fusion.WLAN.WLANMarshal.DataExportWLANProfiles(Symbol.Fusion.FINTRF_CONTEXTHANDLE@,Symbol.Fusion.WLAN.FINTRF_ExportProfile@)">
            <summary>
            Marshal the FusionInterface DataExportWLANProfiles.
            </summary>
        </member>
        <member name="M:Symbol.Fusion.WLAN.WLANMarshal.DataImportWLANProfiles(Symbol.Fusion.FINTRF_CONTEXTHANDLE@,Symbol.Fusion.WLAN.FINTRF_ImportProfile@)">
            <summary>
            Marshal the FusionInterface DataImportWLANProfiles.
            </summary>
        </member>
        <member name="M:Symbol.Fusion.WLAN.WLANMarshal.UserCredentialWLANOverride(Symbol.Fusion.FINTRF_CONTEXTHANDLE@,Symbol.Fusion.WLAN.FINTRF_UserCredential_3@)">
            <summary>
            Marshal the FusionInterface UserCredentialWLANOverride.
            </summary>
        </member>
        <member name="M:Symbol.Fusion.WLAN.WLANMarshal.UserCredentialWLANReset(Symbol.Fusion.FINTRF_CONTEXTHANDLE@)">
            <summary>
            Marshal the FusionInterface UserCredentialWLANReset.
            </summary>
        </member>
        <member name="M:Symbol.Fusion.WLAN.WLANMarshal.GetNDISData(Symbol.Fusion.FINTRF_CONTEXTHANDLE@,System.UInt32,Symbol.Fusion.WLAN.IEEE802_11_OIDs,Symbol.Fusion.WLAN.NDISInfo)">
            <summary>
            Marshal the FusionInterface GetNDISData.
            </summary>
        </member>
        <member name="M:Symbol.Fusion.WLAN.WLANMarshal.SetNDISData(Symbol.Fusion.FINTRF_CONTEXTHANDLE@,System.UInt32,Symbol.Fusion.WLAN.IEEE802_11_OIDs,Symbol.Fusion.WLAN.NDISInfo)">
            <summary>
            Marshal the FusionInterface SetNDISData.
            </summary>
        </member>
        <member name="M:Symbol.Fusion.WLAN.WLANMarshal.GetWLANConnectionStatus(Symbol.Fusion.FINTRF_CONTEXTHANDLE@,System.UInt32)">
            <summary>
            Marshal the FusionInterface GetWLANConnectionStatus.
            </summary>
        </member>
        <member name="M:Symbol.Fusion.WLAN.WLANMarshal.GetIPv6(Symbol.Fusion.FINTRF_CONTEXTHANDLE@,System.UInt32)">
            <summary>
            Marshal the FusionInterface GetIPv6.
            </summary>
        </member>
        <member name="M:Symbol.Fusion.WLAN.WLANMarshal.SetIPv6(Symbol.Fusion.FINTRF_CONTEXTHANDLE@,System.UInt32,System.Boolean)">
            <summary>
            Marshal the FusionInterface SetIPv6.
            </summary>
        </member>
        <member name="M:Symbol.Fusion.WLAN.WLANMarshal.ResetIPv6(Symbol.Fusion.FINTRF_CONTEXTHANDLE@,System.UInt32)">
            <summary>
            Marshal the FusionInterface ResetIPv6.
            </summary>
        </member>
        <member name="M:Symbol.Fusion.WLAN.WLANMarshal.GetPACAutoRefreshingStatus(Symbol.Fusion.FINTRF_CONTEXTHANDLE@,System.UInt32)">
            <summary>
            Marshal the FusionInterface GetPACAutoRefreshingStatus.
            </summary>
        </member>
        <member name="M:Symbol.Fusion.WLAN.WLANMarshal.SetPACAutoRefreshingStatus(Symbol.Fusion.FINTRF_CONTEXTHANDLE@,System.UInt32,System.Boolean)">
            <summary>
            Marshal the FusionInterface SetPACAutoRefreshingStatus.
            </summary>
        </member>
        <member name="M:Symbol.Fusion.WLAN.WLANMarshal.GetPACAutoProvisioningStatus(Symbol.Fusion.FINTRF_CONTEXTHANDLE@,System.UInt32)">
            <summary>
            Marshal the FusionInterface GetPACAutoProvisioningStatus.
            </summary>
        </member>
        <member name="M:Symbol.Fusion.WLAN.WLANMarshal.SetPACAutoProvisioningStatus(Symbol.Fusion.FINTRF_CONTEXTHANDLE@,System.UInt32,System.Boolean)">
            <summary>
            Marshal the FusionInterface SetPACAutoProvisioningStatus.
            </summary>
        </member>
        <member name="M:Symbol.Fusion.WLAN.WLANMarshal.GetWLANManagement(Symbol.Fusion.FINTRF_CONTEXTHANDLE@,System.UInt32)">
            <summary>
            Marshal the FusionInterface GetWLANManagement.
            </summary>
        </member>
        <member name="M:Symbol.Fusion.WLAN.WLANMarshal.SetWLANManagement(Symbol.Fusion.FINTRF_CONTEXTHANDLE@,System.UInt32,Symbol.Fusion.WLAN.WLAN_MANAGEMENT_STATE)">
            <summary>
            Marshal the FusionInterface SetWLANManagement.
            </summary>
        </member>
        <member name="M:Symbol.Fusion.WLAN.WLANMarshal.GetPreAuthentication(Symbol.Fusion.FINTRF_CONTEXTHANDLE@,System.UInt32)">
            <summary>
            Marshal the FusionInterfaceGetPreAuthenticationState.
            </summary>
        </member>
        <member name="M:Symbol.Fusion.WLAN.WLANMarshal.SetPreAuthentication(Symbol.Fusion.FINTRF_CONTEXTHANDLE@,System.UInt32,System.Boolean)">
            <summary>
            Marshal the FusionInterfaceSetPreAuthenticationState.
            </summary>
        </member>
        <member name="M:Symbol.Fusion.WLAN.WLANMarshal.GetOptimizeRadio(Symbol.Fusion.FINTRF_CONTEXTHANDLE@,System.UInt32)">
            <summary>
            Marshal the FusionInterface GetOptimizeRadio.
            </summary>
        </member>
        <member name="M:Symbol.Fusion.WLAN.WLANMarshal.SetOptimizeRadio(Symbol.Fusion.FINTRF_CONTEXTHANDLE@,System.UInt32,Symbol.Fusion.WLAN.WLAN_OPTIMIZE_RADIO)">
            <summary>
            Marshal the FusionInterface SetOptimizeRadio.
            </summary>
        </member>
        <member name="M:Symbol.Fusion.WLAN.WLANMarshal.GetFIPSMode(Symbol.Fusion.FINTRF_CONTEXTHANDLE@,System.UInt32)">
            <summary>
            Marshal the FusionInterface GetFIPSMode.
            </summary>
        </member>
        <member name="M:Symbol.Fusion.WLAN.WLANMarshal.SetFIPSMode(Symbol.Fusion.FINTRF_CONTEXTHANDLE@,System.UInt32,Symbol.Fusion.WLAN.WLAN_FIPS_MODE)">
            <summary>
            Marshal the FusionInterface SetFIPSMode.
            </summary>
        </member>
        <member name="M:Symbol.Fusion.WLAN.WLANMarshal.GetRFBandAndChannels(Symbol.Fusion.FINTRF_CONTEXTHANDLE@,Symbol.Fusion.WLAN.FINTRF_RFBand_Channel_1@)">
            <summary>
            Marshal the FusionInterface GetRFBandAndChannels.
            </summary>
        </member>
        <member name="M:Symbol.Fusion.WLAN.WLANMarshal.SetRFBandAndChannels(Symbol.Fusion.FINTRF_CONTEXTHANDLE@,Symbol.Fusion.WLAN.FINTRF_RFBand_Channel_1)">
            <summary>
            Marshal the FusionInterface SetRFBandAndChannels.
            </summary>
        </member>
        <member name="M:Symbol.Fusion.WLAN.AccessWLANAdapterIDList.AllocMarshalPtr">
            <summary>
            Creates a native buffer for use. 
            The native buffer size is based on the MarshalSize property.
            </summary>
            <remarks>
            If the allocation fails the IntPtr is still returned, but the value it contains
            will be zero. (i.e. null)
            </remarks>
            <returns>An IntPtr that points to the new native buffer.</returns>
        </member>
        <member name="M:Symbol.Fusion.WLAN.AccessWLANAdapterIDList.FreeMarshalPtr(System.IntPtr)">
            <summary>
            Frees the native buffer of the marshal object.
            </summary>
            <param name="pPtr">An IntPtr that points to the native buffer.</param>
        </member>
        <member name="M:Symbol.Fusion.WLAN.AccessWLANAdapterIDList.UnmarshalFrom(System.IntPtr,System.Int32)">
            <summary>
            Unmarshals the data from the marshal object.
            </summary>
            <param name="pPtr">An IntPtr that points to the native buffer.</param>
            <param name="nOffset">Tbe offset of the data to be unmarshalled.</param>
        </member>
        <member name="M:Symbol.Fusion.WLAN.AccessSSIDList.AllocMarshalPtr">
            <summary>
            Creates a native buffer for use. 
            The native buffer size is based on the MarshalSize property.
            </summary>
            <remarks>
            If the allocation fails the IntPtr is still returned, but the value it contains
            will be zero. (i.e. null)
            </remarks>
            <returns>An IntPtr that points to the new native buffer.</returns>
        </member>
        <member name="M:Symbol.Fusion.WLAN.AccessSSIDList.FreeMarshalPtr(System.IntPtr)">
            <summary>
            Frees the native buffer of the marshal object.
            </summary>
            <param name="pPtr">An IntPtr that points to the native buffer.</param>
        </member>
        <member name="M:Symbol.Fusion.WLAN.AccessSSIDList.UnmarshalFrom(System.IntPtr,System.Int32)">
            <summary>
            Unmarshals the data from the marshal object.
            </summary>
            <param name="pPtr">An IntPtr that points to the native buffer.</param>
            <param name="nOffset">Tbe offset of the data to be unmarshalled.</param>
        </member>
        <member name="M:Symbol.Fusion.WLAN.AccessWLANProfileList.AllocMarshalPtr">
            <summary>
            Creates a native buffer for use. 
            The native buffer size is based on the MarshalSize property.
            </summary>
            <remarks>
            If the allocation fails the IntPtr is still returned, but the value it contains
            will be zero. (i.e. null)
            </remarks>
            <returns>An IntPtr that points to the new native buffer.</returns>
        </member>
        <member name="M:Symbol.Fusion.WLAN.AccessWLANProfileList.FreeMarshalPtr(System.IntPtr)">
            <summary>
            Frees the native buffer of the marshal object.
            </summary>
            <param name="pPtr">An IntPtr that points to the native buffer.</param>
        </member>
        <member name="M:Symbol.Fusion.WLAN.AccessWLANProfileList.UnmarshalFrom(System.IntPtr,System.Int32)">
            <summary>
            Unmarshals the data from the marshal object.
            </summary>
            <param name="pPtr">An IntPtr that points to the native buffer.</param>
            <param name="nOffset">Tbe offset of the data to be unmarshalled.</param>
        </member>
        <member name="M:Symbol.Fusion.WLAN.FINTRF_EncryptionAlgorithm.SetValues(Symbol.Fusion.WLAN.Encryption)">
            <summary>
            This function sets values from the passed Encryption object to FINTRF_EncryptionAlgorithm
            </summary>
            <param name="encryption">The object to read values from</param>
        </member>
        <member name="M:Symbol.Fusion.WLAN.FINTRF_StaticIPSettings_Fusion_v3_0.SetValues(Symbol.Fusion.WLAN.IPSettings)">
            <summary>
             This function sets values from the passed IPSettings object to FINTRF_StaticIPSettings_Fusion_v3_0
             </summary>
             <param name="ipSettings">The object to read values from</param>
        </member>
        <member name="M:Symbol.Fusion.WLAN.FINTRF_UserCertificate.SetValues(Symbol.Fusion.WLAN.Authentication.UserCert)">
            <summary>
            This function sets values from the passed UserCert object to  
            FINTRF_UserCertificate
            </summary>
            <param name="userCert">The object to read values from</param>
        </member>
        <member name="M:Symbol.Fusion.WLAN.FINTRF_ServerCertificate.SetValues(Symbol.Fusion.WLAN.Authentication.ServerCert)">
            <summary>
            This function sets values from the passed ServerCert object to FINTRF_ServerCertificate
            </summary>
            <param name="cert">The object to read values from</param>
        </member>
        <member name="M:Symbol.Fusion.WLAN.FINTRF_UserCredentials.SetValues(Symbol.Fusion.WLAN.Authentication.UserCred)">
            <summary>
            This function sets values from the passed UserCredentials object to  
            FINTRF_UserCredentials
            </summary>
            <param name="userCred">The object to read values from</param>
        </member>
        <member name="M:Symbol.Fusion.WLAN.FINTRF_CredentialPromptOption.SetValues(Symbol.Fusion.WLAN.Authentication.CredentialPrmpt)">
            <summary>
             This function sets values from the passed CredentialPrompt object to  
             FINTRF_CredentialPromptOption
             </summary>
             <param name="prompt">The object to read values from</param>
        </member>
        <member name="M:Symbol.Fusion.WLAN.FINTRF_IEEE8021X_CredSettings.SetValues(Symbol.Fusion.WLAN.Authentication.IEEE8021xCred)">
            <summary>
            This function sets values from the passed IEEE8021xCred object to  
            FINTRF_IEEE8021X_CredSettings
            </summary>
            <param name="ieeeCred">The object to read values from</param>
        </member>
        <member name="M:Symbol.Fusion.WLAN.FINTRF_AuthenticationSettings.SetValues(Symbol.Fusion.WLAN.Authentication)">
            <summary>
             This function sets values from the passed Authentication object to  
             FINTRF_AuthenticationSettings
             </summary>
             <param name="authentication">The object to read values from</param>
        </member>
        <member name="T:Symbol.Fusion.WLAN.Access_NDIS_802_11_BSSID_LIST_EX">
            <summary>
             Marshal NDIS_802_11_BSSID_LIST_EX structure
            </summary>
        </member>
        <member name="M:Symbol.Fusion.WLAN.Access_NDIS_802_11_BSSID_LIST_EX.AllocMarshalPtr">
            <summary>
            Creates a native buffer for use. 
            The native buffer size is based on the MarshalSize property.
            </summary>
            <remarks>
            If the allocation fails the IntPtr is still returned, but the value it contains
            will be zero. (i.e. null)
            </remarks>
            <returns>An IntPtr that points to the new native buffer.</returns>
        </member>
        <member name="M:Symbol.Fusion.WLAN.Access_NDIS_802_11_BSSID_LIST_EX.FreeMarshalPtr(System.IntPtr)">
            <summary>
            Frees the native buffer of the marshal object.
            </summary>
            <param name="pPtr">An IntPtr that points to the native buffer.</param>
        </member>
        <member name="M:Symbol.Fusion.WLAN.Access_NDIS_802_11_BSSID_LIST_EX.UnmarshalFrom(System.IntPtr,System.Int32)">
            <summary>
            Unmarshals the data from the marshal object.
            </summary>
            <param name="pPtr">An IntPtr that points to the native buffer.</param>
            <param name="nOffset">Tbe offset of the data to be unmarshalled.</param>
        </member>
        <member name="T:Symbol.Fusion.Config">
            <summary>
            Config class is used internally to initiate links with the Wireless Networking hardware 
            of a mobile computer. <see cref="T:Symbol.Fusion.Version"/> and 
            <see cref="T:Symbol.Fusion.Diagnostics"/> information should be 
            accessed througn Config class.
            </summary>
        </member>
        <member name="M:Symbol.Fusion.Config.#ctor">
            <summary>
            Config Constructor
            </summary>
        </member>
        <member name="M:Symbol.Fusion.Config.#ctor(Symbol.Fusion.FusionAccessType)">
            <summary>
            Config Constructor
            </summary>
        </member>
        <member name="M:Symbol.Fusion.Config.AccessControl(Symbol.Fusion.LockOperation,System.String)">
            <summary>
            Enables or disables the password protection of Fusion API’s. Available in Command mode only.
            </summary>
            <param name="lockOperation">Specifies the lock or unlock operation</param>
            <param name="password">Password for the desired operation.</param>
            <returns>The API result code</returns>
        </member>
        <member name="M:Symbol.Fusion.Config.Initalize">
            <summary>
            Initialize members during construction
            </summary>
        </member>
        <member name="M:Symbol.Fusion.Config.Finalize">
            <summary>
            
            </summary>
        </member>
        <member name="M:Symbol.Fusion.Config.Dispose">
            <summary>
            Close Connection to Fusion API and release resources
            </summary>
        </member>
        <member name="M:Symbol.Fusion.Config.Dispose(System.Boolean)">
            <summary>
            dispose resources
            </summary>
        </member>
        <member name="P:Symbol.Fusion.Config.Diagnostics">
            <summary>
            Defines diagnostics information
            </summary>
        </member>
        <member name="P:Symbol.Fusion.Config.Version">
            <summary>
            Defines the version information
            </summary>
        </member>
        <member name="P:Symbol.Fusion.Config.VersionEx">
            <summary>
            Defines the extended version information
            </summary>
        </member>
        <member name="P:Symbol.Fusion.Config.Action">
            <summary>
            Takes care of device control
            </summary>
        </member>
        <member name="P:Symbol.Fusion.Config.AccessType">
            <summary>
            Define the mode that the connection with the 
            Fusion driver APIs is established by this object.
            </summary>
        </member>
        <member name="P:Symbol.Fusion.Config.WLAN">
            <summary>
            Provides access to the WLAN specific configurations.
            </summary>
        </member>
        <member name="T:Symbol.Fusion.Config.WLANConfig">
            <summary>
            Holds the WLAN specific configuration details.
            </summary>
        </member>
        <member name="M:Symbol.Fusion.Config.WLANConfig.Finalize">
            <summary>
            The finalizer for WLANConfig.
            </summary>
        </member>
        <member name="M:Symbol.Fusion.Config.WLANConfig.ResetDataStore">
            <summary>
            Reset Fusion datastore to factory settings (default values).
            Fusion datastore includes profiles, options, PACs and certificates. 
            This feature is supported on Fusion versions X_2.00 and newer. 
            Supported only in the command mode.
            </summary>
        </member>
        <member name="P:Symbol.Fusion.Config.WLANConfig.Options">
            <summary>
            Provides access to the Fusion WLAN options.
            </summary>
        </member>
        <member name="P:Symbol.Fusion.Config.WLANConfig.PAC">
            <summary>
            Provides access to PAC.
            </summary>
        </member>
        <member name="T:Symbol.Fusion.Config.WLANConfig.WLANOptions">
            <summary>
            The class for WLAN options.
            </summary>
        </member>
        <member name="M:Symbol.Fusion.Config.WLANConfig.WLANOptions.Finalize">
            <summary>
            The finalizer for WLANOptions.
            </summary>
        </member>
        <member name="M:Symbol.Fusion.Config.WLANConfig.WLANOptions.Export(Symbol.Fusion.FileFormat,System.String,System.String)">
            <summary>
            Exports the Fusion WLAN options to the file specified.
            </summary>
        </member>
        <member name="M:Symbol.Fusion.Config.WLANConfig.WLANOptions.Import(Symbol.Fusion.FileFormat,System.String,System.String)">
            <summary>
            Imports the Fusion WLAN options from the file specified. Available in Command Mode only.
            </summary>
        </member>
        <member name="M:Symbol.Fusion.Config.WLANConfig.WLANOptions.Reset">
            <summary>
            Resets options to factory settings (default values). 
            This feature is supported on Fusion versions X_2.00 and newer. 
            Supported only in the command mode.
            </summary>
        </member>
        <member name="T:Symbol.Fusion.Config.WLANConfig.WLANPAC">
            <summary>
            The class representing PAC.
            </summary>
        </member>
        <member name="M:Symbol.Fusion.Config.WLANConfig.WLANPAC.Finalize">
            <summary>
            The finalizer for WLANPAC.
            </summary>
        </member>
        <member name="M:Symbol.Fusion.Config.WLANConfig.WLANPAC.Import(System.String,System.String,System.Boolean)">
            <summary>
            Imports a PAC from a given manually provisioned PAC file
            and adds it to the device PAC store. Available in Command Mode only.
            </summary>
            <param name="fileName">File name to be imported containing the manually provisioned PAC.</param>
            <param name="password">Password for decrypting the PAC file. If file uses the Cisco default password, set to 0.</param>
            <param name="overwriteExisting">Flag allowing the import to overwrite PAC in the list with same A-ID and I-ID.</param>
        </member>
        <member name="T:Symbol.Fusion.AdapterTypes">
            <summary>
            An enumeration of supported adapter types
            </summary>
        </member>
        <member name="F:Symbol.Fusion.AdapterTypes.WLAN">
            <summary>
            All adapters used with WLAN
            </summary>
        </member>
        <member name="T:Symbol.Fusion.FusionResults">
            <summary>
            Defines the possible set of return codes from FudionInterface.dll function calls
            </summary>
        </member>
        <member name="F:Symbol.Fusion.FusionResults.SUCCESS">
            <summary>
            The operation was successful
            </summary>
        </member>
        <member name="F:Symbol.Fusion.FusionResults.LOWER_FINTRF_ERROR_LIMIT">
            <summary>
            Datum for the enumeration
            </summary>
        </member>
        <member name="F:Symbol.Fusion.FusionResults.ERROR_HANDLE_INVALID">
            <summary>
            Handle passed to the API is invalid
            </summary>
        </member>
        <member name="F:Symbol.Fusion.FusionResults.ERROR_COMM_MODE_IN_USE">
            <summary>
            Another application is running in Fusion Command mode
            </summary>
        </member>
        <member name="F:Symbol.Fusion.FusionResults.ERROR_STAT_MODE_LIMIT">
            <summary>
            Too many applications are running in Fusion Statistics Mode. Max limit is 5 if users create WLAN objects in their applications. If they create Config objects only, this can go up to 10 applications.
            </summary>
        </member>
        <member name="F:Symbol.Fusion.FusionResults.ERROR_MEMORY_ALLOCATION">
            <summary>
            Memory allocation failed
            </summary>
        </member>
        <member name="F:Symbol.Fusion.FusionResults.ERROR_INVALID_ACCESS_TYPE">
            <summary>
            Invalid access type
            </summary>
        </member>
        <member name="F:Symbol.Fusion.FusionResults.FAPI_FAILURE">
            <summary>
            Fusion Public API failed in the operation
            </summary>
        </member>
        <member name="F:Symbol.Fusion.FusionResults.ERROR_TEXT_LEN">
            <summary>
            Invalid text length
            </summary>
        </member>
        <member name="F:Symbol.Fusion.FusionResults.ERROR_FUSION_PUBLIC_DLL_LOAD_FAILED">
            <summary>
            Unable to load Fusion Public API
            </summary>
        </member>
        <member name="F:Symbol.Fusion.FusionResults.ERROR_FUSION_PUBLIC_DLL_UNLOAD_FAILED">
            <summary>
            Unable to unload Fusion Public API
            </summary>
        </member>
        <member name="F:Symbol.Fusion.FusionResults.ERROR_PTR_OPENFUSIONAPI_NULL">
            <summary>
            Fusion Interface failed in obtaining a function pointer to OpenFusionAPI in Fusion public API
            </summary>
        </member>
        <member name="F:Symbol.Fusion.FusionResults.ERROR_PTR_CLOSEFUSIONAPI_NULL">
            <summary>
            Fusion Interface failed in obtaining a function pointer to CloseFusionAPI in Fusion public API
            </summary>
        </member>
        <member name="F:Symbol.Fusion.FusionResults.ERROR_PTR_COMMANDFUSIONAPI_NULL">
            <summary>
            Fusion Interface failed in obtaining a function pointer to CommandFusionAPI in Fusion public API
            </summary>
        </member>
        <member name="F:Symbol.Fusion.FusionResults.ERROR_VERSION_NEWER">
            <summary>
            The Interface DLL is a newer version that is not 
            compatible with the old EMDK 
            </summary>
        </member>
        <member name="F:Symbol.Fusion.FusionResults.ERROR_VERSION_OLDER">
            <summary>
            The Interface DLL is not compatible and older than the EMDK.
            </summary>
        </member>
        <member name="F:Symbol.Fusion.FusionResults.ERROR_VERSION_FORMAT">
            <summary>
            Error in Format of the Version passed to FusionInterface API 
            </summary>
        </member>
        <member name="F:Symbol.Fusion.FusionResults.ERROR_INVAILD_AUTHENTICATION_SUBTYPE">
            <summary>
            Unsupported Authentication type used
            </summary>
        </member>
        <member name="F:Symbol.Fusion.FusionResults.ERROR_INVAILD_ENCRYPTION_TYPE">
            <summary>
            Unsupported encryption type used
            </summary>
        </member>
        <member name="F:Symbol.Fusion.FusionResults.ERROR_INVAILD_SERVER_CERTIFICATE_TYPE">
            <summary>
            Unsupported server certificate type used
            </summary>
        </member>
        <member name="F:Symbol.Fusion.FusionResults.ERROR_INVAILD_USER_CERTIFICATE_TYPE">
            <summary>
            Unsupported user certificate type used
            </summary>
        </member>
        <member name="F:Symbol.Fusion.FusionResults.ERROR_NULL_ARGUMENT_NOT_ALLOWED">
            <summary>
            Null argument passed to an API function that does not accept null arguments
            </summary>
        </member>
        <member name="F:Symbol.Fusion.FusionResults.ERROR_INVALID_POWER_STATE">
            <summary>
            Unsupported Power state used
            </summary>
        </member>
        <member name="F:Symbol.Fusion.FusionResults.ERROR_TEXT_LEN_INSUFFICIENT">
            <summary>
            The length of the string passed to the API is too short
            </summary>
        </member>
        <member name="F:Symbol.Fusion.FusionResults.ERROR_PROFILEID_NULL">
            <summary>
            The profile ID given, is NULL.
            </summary>
        </member>
        <member name="F:Symbol.Fusion.FusionResults.ERROR_PROFILEID_LENGTH_EXCEEDED">
            <summary>
            The length of the profile ID given, exceeds the maximum allowed.  
            </summary>
        </member>
        <member name="F:Symbol.Fusion.FusionResults.ERROR_FILENAME_NULL">
            <summary>
            The file name given, is NULL.
            </summary>
        </member>
        <member name="F:Symbol.Fusion.FusionResults.ERROR_PROFILE_LIST_REF_NULL">
            <summary>
            The reference to the profle list given, is NULL.
            </summary>
        </member>
        <member name="F:Symbol.Fusion.FusionResults.ERROR_ADAPTER_LIST_REF_NULL">
            <summary>
             The reference to the adapter list given, is NULL.
            </summary>
        </member>
        <member name="F:Symbol.Fusion.FusionResults.ERROR_VERSION_LIST_REF_NULL">
            <summary>
            The reference to the version list given, is NULL.
            </summary>
        </member>
        <member name="F:Symbol.Fusion.FusionResults.ERROR_DWORD_REF_NULL">
            <summary>
            The reference to the DWORD given, is NULL.
            </summary>
        </member>
        <member name="F:Symbol.Fusion.FusionResults.ERROR_STRUCT_REF_NULL">
            <summary>
            The reference to the structure given, is NULL.
            </summary>
        </member>
        <member name="F:Symbol.Fusion.FusionResults.ERROR_TCHAR_REF_NULL">
            <summary>
            The reference to the TCHAR given, is NULL.
            </summary>
        </member>
        <member name="F:Symbol.Fusion.FusionResults.ERROR_VERSION_STRING_NOT_FOUND">
            <summary>
            The Fusion build version string not found in the list of versions.
            </summary>
        </member>
        <member name="F:Symbol.Fusion.FusionResults.ERROR_OLDER_FUSION_BULID_VERSION">
            <summary>
            The device has an older Fusion build version (older than 2.4).
            </summary>
        </member>
        <member name="F:Symbol.Fusion.FusionResults.ERROR_IEEE8021X_CREDSETTINGS_DOMAIN_LENGTH_HIGHER_THAN_64">
            <summary>
            The error returned due to the corresponding length being shortedned in Fusion 2.55 from 160 to 64.
            </summary>
        </member>
        <member name="F:Symbol.Fusion.FusionResults.ERROR_IEEE8021X_CREDSETTINGS_IDENTITY_LENGTH_HIGHER_THAN_64">
            <summary>
            The error returned due to the corresponding length being shortedned in Fusion 2.55 from 160 to 64.
            </summary>
        </member>
        <member name="F:Symbol.Fusion.FusionResults.ERROR_USERCREDS_DOMAIN_LENGTH_HIGHER_THAN_64">
            <summary>
            The error returned due to the corresponding length being shortedned in Fusion 2.55 from 160 to 64.
            </summary>
        </member>
        <member name="F:Symbol.Fusion.FusionResults.ERROR_USERCREDS_IDENTITY_LENGTH_HIGHER_THAN_64">
            <summary>
            The error returned due to the corresponding length being shortedned in Fusion 2.55 from 160 to 64.
            </summary>
        </member>
        <member name="F:Symbol.Fusion.FusionResults.ERROR_USERCREDS_PASSWORD_LENGTH_HIGHER_THAN_64">
            <summary>
            The error returned due to the corresponding length being shortedned in Fusion 2.55 from 160 to 64.
            </summary>
        </member>
        <member name="F:Symbol.Fusion.FusionResults.ERROR_WEP_PASSPHRASE_LENGTH_HIGHER_THAN_32">
            <summary>
            The error returned due to the length of the WEP passphrase being higher than 32. It has been shortedned in Fusion 2.60 from 63 to 32.
            </summary>
        </member>
        <member name="F:Symbol.Fusion.FusionResults.ERROR_NPCS_RADIO_DISABLE">
            <summary>
            NPCS (Network Policy Configuration Service) has disabled the radio.
            The Fusion Public API operations except the one for closing the API will return this error.
            </summary>
        </member>
        <member name="F:Symbol.Fusion.FusionResults.ERROR_MISSING_SECURITY_TYPE">
            <summary>
            The security type information has not been specified.
            </summary>
        </member>
        <member name="F:Symbol.Fusion.FusionResults.ERROR_INVALID_USAGE_CCKM_WITH_CCXFLAGS">
            <summary>
            Invalid Usage. The security type WPA_CCKM has been set along with CCXFlags option specified.
            </summary>
        </member>
        <member name="F:Symbol.Fusion.FusionResults.FINTRF_ERROR_BUFFER_EXCEED">
            <summary>
            The size of the data buffer provided is too large. Cannot be copied to the lower layer.
            </summary>
        </member>
        <member name="F:Symbol.Fusion.FusionResults.FINTRF_ERROR_AC_PASSWORD_LENGTH_HIGHER_THAN_64">
            <summary>
            The access control password is longer than the maximum allowed length 64. (Fusion 2.55 onwards).
            </summary>
        </member>
        <member name="F:Symbol.Fusion.FusionResults.FINTRF_ERROR_REF_NULL">
            <summary>
            The reference given is NULL.
            </summary>
        </member>
        <member name="F:Symbol.Fusion.FusionResults.FINTRF_ERROR_USERCERTIFICATE_HASH_LENGTH_HIGHER_THAN_65">
            <summary>
            The user certificate hash length exceeded. 
            </summary>
        </member>
        <member name="F:Symbol.Fusion.FusionResults.ERROR_NOT_SUPPORTED_FAST_ROAMING_WITH_PERFORMANCE_SETTING">
            <summary>
            Setting both FastRoaming and PerformanceSetting is not supported. 
            </summary>
        </member>
        <member name="F:Symbol.Fusion.FusionResults.ERROR_NOT_SUPPORTED_CCXFLAGS_WITH_FAST_ROAMING">
            <summary>
            Setting both CCXFlags and FastRoaming is not supported. 
            </summary>
        </member>
        <member name="F:Symbol.Fusion.FusionResults.ERROR_NOT_SUPPORTED_AESALLOWMIXEDMODE_WITH_PERFORMANCE_SETTING">
            <summary>
            Setting both AESAllowMixedMode and PerformanceSetting is not supported.
            </summary>
        </member>
        <member name="F:Symbol.Fusion.FusionResults.ERROR_NOT_SUPPORTED_AESALLOWMIXEDMODE_WITH_FAST_ROAMING">
            <summary>
            Setting both AESAllowMixedMode and FastRoaming is not supported.
            </summary>
        </member>
        <member name="F:Symbol.Fusion.FusionResults.ERROR_NOT_SUPPORTED_AESALLOWMIXEDMODE_WITH_PERFORMANCE_SETTING_FAST_ROAMING">
            <summary>
            Setting all AESAllowMixedMode, PerformanceSetting and FastRoaming is not supported.
            </summary>
        </member>
        <member name="F:Symbol.Fusion.FusionResults.ERROR_NOT_SUPPORTED_CCXFLAGS_WITH_PERFORMANCE_SETTING_FAST_ROAMING">
            <summary>
            Setting all CCXFlags, PerformanceSetting and FastRoaming is not supported.
            </summary>
        </member>
        <member name="F:Symbol.Fusion.FusionResults.ERROR_NOT_SUPPORTED">
            <summary>
            A property/feature which is currently not supported, has been specified.
            </summary>
        </member>
        <member name="F:Symbol.Fusion.FusionResults.UNKNOWN_ERROR">
            <summary>
            An unknown/unhandled error in the API
            </summary>
        </member>
        <member name="T:Symbol.Fusion.FusionAccessType">
            <summary>
            Enumerate possible modes of operation of t he Fusion API
            </summary>
        </member>
        <member name="F:Symbol.Fusion.FusionAccessType.COMMAND_MODE">
            <summary>
            Open Fusion interface for editing profiles
            Only one application is allowed to open Fusion in Command mode at a time.
            </summary>
        </member>
        <member name="F:Symbol.Fusion.FusionAccessType.STATISTICS_MODE">
            <summary>
            Open Fusion interface for viewing statistics
            </summary>
        </member>
        <member name="T:Symbol.Fusion.FileFormat">
            <summary>
            The file format used with export and import operations.
            </summary>
        </member>
        <member name="F:Symbol.Fusion.FileFormat.REG_FILE">
            <summary>
            Registry file.
            </summary>
        </member>
        <member name="F:Symbol.Fusion.FileFormat.XML_FILE">
            <summary>
            XML file. Reserved for future use.
            </summary>
        </member>
        <member name="T:Symbol.Fusion.LockOperation">
            <summary>
            Defines the access control to the fusion database.
            </summary>
        </member>
        <member name="F:Symbol.Fusion.LockOperation.LOCK">
            <summary>
            Lock the Fusion database.
            </summary>
        </member>
        <member name="F:Symbol.Fusion.LockOperation.UNLOCK">
            <summary>
            Unlock the Fusion database.
            </summary>
        </member>
        <member name="T:Symbol.Fusion.Version">
            <summary>
            Fusion.Version class provides versioning information related to the Fusion
            modules.
            </summary>
        </member>
        <member name="M:Symbol.Fusion.Version.Dispose">
            <summary>
            Called during Config dispose. Used to make the cached data not accessible
            </summary>
        </member>
        <member name="M:Symbol.Fusion.Version.ToString">
            <summary>
            Get all version strings
            </summary>
        </member>
        <member name="P:Symbol.Fusion.Version.Fusion">
            <summary>
            Get the Fusion version string
            </summary>
        </member>
        <member name="P:Symbol.Fusion.Version.ConfigurationEditor">
            <summary>
            Get the ConfigurationEditor version string
            </summary>
        </member>
        <member name="P:Symbol.Fusion.Version.LoginService">
            <summary>
            Get the LoginService version string
            </summary>
        </member>
        <member name="P:Symbol.Fusion.Version.PhotonDriver">
            <summary>
            Get the PhotonDriver version string
            </summary>
        </member>
        <member name="P:Symbol.Fusion.Version.WCDiag">
            <summary>
            Get the WCDiag version string
            </summary>
        </member>
        <member name="P:Symbol.Fusion.Version.WCLaunch">
            <summary>
            Get the WCLaunch version string
            </summary>
        </member>
        <member name="P:Symbol.Fusion.Version.WCSAPI">
            <summary>
            Get the WCSAPI version string
            </summary>
        </member>
        <member name="P:Symbol.Fusion.Version.WCSRV">
            <summary>
            Get the WCSRV version string
            </summary>
        </member>
        <member name="P:Symbol.Fusion.Version.WCStatus">
            <summary>
            Get the WCStatus version string
            </summary>
        </member>
        <member name="P:Symbol.Fusion.Version.FusionPublicAPI">
            <summary>
            Get the FusionPublicAPI version string
            </summary>
        </member>
        <member name="P:Symbol.Fusion.Version.RadioDriver">
            <summary>
            Get the Fusion Radio Driver name and version
            </summary>
        </member>
        <member name="T:Symbol.Fusion.VersionsEx">
            <summary>
            The class for the extended version information.
            </summary>
        </member>
        <member name="M:Symbol.Fusion.VersionsEx.Dispose">
            <summary>
            Called during Config dispose. Used to make the cached data not accessible
            </summary>
        </member>
        <member name="M:Symbol.Fusion.VersionsEx.GetExtendedInfo(Symbol.Fusion.VersionExComponentID)">
            <summary>
            Get extended version information
            </summary>
        </member>
        <member name="M:Symbol.Fusion.VersionsEx.ToString">
            <summary>
            The overriden ToString() implementation for the VersionEx class. 
            Gives the extended version string. No any component specific versions,
             only the generic information will be provided. 
            </summary>
        </member>
        <member name="P:Symbol.Fusion.VersionsEx.MajorVersion">
            <summary>
            The major version number of the complete Fusion Package.
            </summary>
        </member>
        <member name="P:Symbol.Fusion.VersionsEx.MinorVersion">
            <summary>
            The minor version number of the complete Fusion Package. 
            </summary>
        </member>
        <member name="P:Symbol.Fusion.VersionsEx.MaintenanceNum">
            <summary>
            The maintenance number of the complete Fusion Package. 
            </summary>
        </member>
        <member name="P:Symbol.Fusion.VersionsEx.ServiceReleaseNum">
            <summary>
            The service release number of the complete Fusion Package.
            </summary>
        </member>
        <member name="P:Symbol.Fusion.VersionsEx.BuildNum">
            <summary>
            The build number of the complete Fusion Package. 
            </summary>
        </member>
        <member name="P:Symbol.Fusion.VersionsEx.BuildType">
            <summary>
            The enumerated build type of the complete Fusion Package.
            </summary>
        </member>
        <member name="T:Symbol.Fusion.VersionsEx.VersionExInfo">
            <summary>
            The class for the extended version information (for a particular component ID).
            </summary>
        </member>
        <member name="M:Symbol.Fusion.VersionsEx.VersionExInfo.ToString">
            <summary>
            The overriden ToString() implementation for the VersionExInfo class. Gives the extended component specific version string. 
            </summary>
        </member>
        <member name="P:Symbol.Fusion.VersionsEx.VersionExInfo.ComponentID">
            <summary>
            The unique ID of the version ex component.
            </summary>
        </member>
        <member name="P:Symbol.Fusion.VersionsEx.VersionExInfo.VersionLevel">
            <summary>
            The version level. Reserved for future use.
            </summary>
        </member>
        <member name="P:Symbol.Fusion.VersionsEx.VersionExInfo.FriendlyCompName">
            <summary>
            The friendly name of the Fusion component.
            </summary>
        </member>
        <member name="P:Symbol.Fusion.VersionsEx.VersionExInfo.VersionStr">
            <summary>
            The version string of the Fusion component.
            </summary>
        </member>
        <member name="T:Symbol.Fusion.VersionExComponentID">
            <summary>
            Enumerates the Component IDs.
            </summary>
        </member>
        <member name="F:Symbol.Fusion.VersionExComponentID.GET_HEADER_ONLY_COMPONENT_ID">
             <summary>
            
             </summary>
        </member>
        <member name="F:Symbol.Fusion.VersionExComponentID.GET_ALL_COMPONENT_ID">
             <summary>
            
             </summary>
        </member>
        <member name="F:Symbol.Fusion.VersionExComponentID.MAIN_COMPONENT_ID">
             <summary>
            
             </summary>
        </member>
        <member name="F:Symbol.Fusion.VersionExComponentID.MAIN_COPYRIGHT_COMPONENT_ID">
             <summary>
            
             </summary>
        </member>
        <member name="F:Symbol.Fusion.VersionExComponentID.MAIN_PARTNO_COMPONENT_ID">
             <summary>
            
             </summary>
        </member>
        <member name="F:Symbol.Fusion.VersionExComponentID.MAIN_DEVICE_COMPONENT_ID">
             <summary>
            
             </summary>
        </member>
        <member name="F:Symbol.Fusion.VersionExComponentID.MAIN_CUSTOMER_COMPONENT_ID">
             <summary>
            
             </summary>
        </member>
        <member name="F:Symbol.Fusion.VersionExComponentID.WCLAUNCH_COMPONENT_ID">
             <summary>
            
             </summary>
        </member>
        <member name="F:Symbol.Fusion.VersionExComponentID.WCSTATUS_COMPONENT_ID">
             <summary>
            
             </summary>
        </member>
        <member name="F:Symbol.Fusion.VersionExComponentID.WCDIAG_COMPONENT_ID">
             <summary>
            
             </summary>
        </member>
        <member name="F:Symbol.Fusion.VersionExComponentID.WCCONFIGED_COMPONENT_ID">
             <summary>
            
             </summary>
        </member>
        <member name="F:Symbol.Fusion.VersionExComponentID.WCSAPI_COMPONENT_ID">
             <summary>
            
             </summary>
        </member>
        <member name="F:Symbol.Fusion.VersionExComponentID.WCSRV_COMPONENT_ID">
             <summary>
            
             </summary>
        </member>
        <member name="F:Symbol.Fusion.VersionExComponentID.LOGIN_SRV_COMPONENT_ID">
             <summary>
            
             </summary>
        </member>
        <member name="F:Symbol.Fusion.VersionExComponentID.PUBLIC_API_COMPONENT_ID">
             <summary>
            
             </summary>
        </member>
        <member name="F:Symbol.Fusion.VersionExComponentID.WLAN1_MODEL_COMPONENT_ID">
             <summary>
            
             </summary>
        </member>
        <member name="F:Symbol.Fusion.VersionExComponentID.WLAN1_INFO_COMPONENT_ID">
             <summary>
            
             </summary>
        </member>
        <member name="F:Symbol.Fusion.VersionExComponentID.WLAN1_PARTNO_COMPONENT_ID">
             <summary>
            
             </summary>
        </member>
        <member name="F:Symbol.Fusion.VersionExComponentID.WLAN1_DRIVER_COMPONENT_ID">
             <summary>
            
             </summary>
        </member>
        <member name="F:Symbol.Fusion.VersionExComponentID.WLAN1_MAC_COMPONENT_ID">
             <summary>
            
             </summary>
        </member>
        <member name="F:Symbol.Fusion.VersionExComponentID.DEVICE_MODEL_COMPONENT_ID">
             <summary>
            
             </summary>
        </member>
        <member name="F:Symbol.Fusion.VersionExComponentID.DEVICE_PARTNO_COMPONENT_ID">
             <summary>
            
             </summary>
        </member>
        <member name="F:Symbol.Fusion.VersionExComponentID.DEVICE_SERIAL_COMPONENT_ID">
             <summary>
            
             </summary>
        </member>
        <member name="F:Symbol.Fusion.VersionExComponentID.OS_TYPE_COMPONENT_ID">
             <summary>
            
             </summary>
        </member>
        <member name="F:Symbol.Fusion.VersionExComponentID.OS_BASE_COMPONENT_ID">
             <summary>
            
             </summary>
        </member>
        <member name="F:Symbol.Fusion.VersionExComponentID.OS_OEM_COMPONENT_ID">
             <summary>
            
             </summary>
        </member>
        <member name="F:Symbol.Fusion.VersionExComponentID.INTERFACE_COMPONENT_ID">
             <summary>
            
             </summary>
        </member>
        <member name="F:Symbol.Fusion.VersionExComponentID.INTERFACE_INFO_COMPONENT_ID">
             <summary>
            
             </summary>
        </member>
        <member name="F:Symbol.Fusion.VersionExComponentID.WLAN1_FW_COMPONENT_ID">
             <summary>
            
             </summary>
        </member>
        <member name="F:Symbol.Fusion.VersionExComponentID.PACSTORESER_SRV_COMPONENT_ID">
             <summary>
            
             </summary>
        </member>
        <member name="F:Symbol.Fusion.VersionExComponentID.CERTMANAGE_COMPONENT_ID">
             <summary>
            
             </summary>
        </member>
        <member name="F:Symbol.Fusion.VersionExComponentID.PBAPIPLUGIN_COMPONENT_ID">
             <summary>
            
             </summary>
        </member>
        <member name="T:Symbol.Fusion.BuildType">
            <summary>
            Enumerates Build types.
            </summary>
        </member>
        <member name="F:Symbol.Fusion.BuildType.TEST">
            <summary>
            Specifies an internal build.
            </summary>
        </member>
        <member name="F:Symbol.Fusion.BuildType.RELEASE">
            <summary>
            Specifies an external build.
            </summary>
        </member>
        <member name="T:Symbol.Fusion.Resources">
            <summary>
            This class is used to access string table resources. Mainly used for localization.
            </summary>
        </member>
        <member name="F:Symbol.Fusion.Resources.m_rmNameValues">
            <summary>
            ResourceManager object to access the string table
            </summary>
        </member>
        <member name="M:Symbol.Fusion.Resources.#cctor">
            <summary>
            Static constructor, initialize access to the string table
            </summary>
        </member>
        <member name="M:Symbol.Fusion.Resources.GetString(System.String)">
            <summary>
            Gets a string from the string table
            </summary>
            <param name="name"></param>
            <returns></returns>
        </member>
        <member name="T:Symbol.Fusion.WLAN.IPSettings">
            <summary>
            Define IP Settings of a Profile
            </summary>
        </member>
        <member name="F:Symbol.Fusion.WLAN.IPSettings.MAX_IP_ADDRESS_LENGTH">
            <summary>
            The maximum length of an IP address string
            </summary>
        </member>
        <member name="M:Symbol.Fusion.WLAN.IPSettings.#ctor">
            <summary>
            IPSettings constructor
            </summary>
        </member>
        <member name="M:Symbol.Fusion.WLAN.IPSettings.IsValid">
            <summary>
            Check whether the settings are correct in size
            </summary>
            <returns>Returns true if IP addresses are of correct size</returns>
            <remarks>This function is called by the assembly before creating a profile. In addition
            this function can be used by developers to verify the data after filling an IPSettings
            object.</remarks>
        </member>
        <member name="M:Symbol.Fusion.WLAN.IPSettings.IPv4formatCheck(System.String)">
            <summary>
            Check whether the given string is in correct IPV4 format 
            </summary>
            <param name="address">The IP address or mask</param>
            <returns>true if the address is empty or properly formatted</returns>
        </member>
        <member name="P:Symbol.Fusion.WLAN.IPSettings.AddressingMode">
            <summary>
            Defines the networking addressing mode. Static or DHCP.
            </summary>
        </member>
        <member name="P:Symbol.Fusion.WLAN.IPSettings.IPAddress">
            <summary>
            A string that contains the static IP Address to be 
            used for the Static Network Addressing Mode (Mandatory).
            </summary>
        </member>
        <member name="P:Symbol.Fusion.WLAN.IPSettings.SubnetMask">
            <summary>
            A string that contains the subnet mask address to be
            used for Static Network Addressing Mode (Mandatory).
            </summary>
        </member>
        <member name="P:Symbol.Fusion.WLAN.IPSettings.GateWay">
            <summary>
            A string that contains the (primary) Gateway Address to be 
            used For Static Network Addressing Mode.
            </summary>
        </member>
        <member name="P:Symbol.Fusion.WLAN.IPSettings.GateWay2">
            <summary>
            A string that contains the secondary Gateway Address to be 
            used For Static Network Addressing Mode.
            Supported in Fusion versions 3.00 and above. 
            </summary>
        </member>
        <member name="P:Symbol.Fusion.WLAN.IPSettings.DNS1">
            <summary>
            A string that contains the primary DNS address to be 
            used for Static Network Addressing Mode.
            </summary>
        </member>
        <member name="P:Symbol.Fusion.WLAN.IPSettings.DNS2">
            <summary>
            A string that contains the secondary DNS address to be used 
            for Static Network Addressing Mode. Supported on the Fusion versions 3.0 and later.
            </summary>
        </member>
        <member name="P:Symbol.Fusion.WLAN.IPSettings.WINS1">
            <summary>
            A string that contains the primary WINS address to be used 
            for Static Network Addressing Mode.
            </summary>
        </member>
        <member name="P:Symbol.Fusion.WLAN.IPSettings.WINS2">
            <summary>
            A string that contains the secondary WINS address to be used 
            for Static Network Addressing Mode. Supported on the Fusion versions 3.0 and later.
            </summary>
        </member>
        <member name="T:Symbol.Fusion.WLAN.IPSettings.AddressingModes">
            <summary>
            IP addressing mode
            </summary>
        </member>
        <member name="F:Symbol.Fusion.WLAN.IPSettings.AddressingModes.STATIC">
            <summary>
            Static addressing
            </summary>
        </member>
        <member name="F:Symbol.Fusion.WLAN.IPSettings.AddressingModes.DHCP">
            <summary>
            Dynamic addressing
            </summary>
        </member>
        <member name="T:Symbol.Fusion.Action">
            <summary>
            Fusion.Action class is provided to pass control commands to the Wireless 
            Networking hardware components of a mobile computer. The commands are 
            passed from .Net to FusionInterface.dll which intern processes the 
            command and pass it to FusionPublic dll which calls appropriate 
            driver functions.
            </summary>
        </member>
        <member name="M:Symbol.Fusion.Action.Open(Symbol.Fusion.FusionAccessType)">
            <summary>
            Open a connection with fusion hardware context and get a handle.
            </summary>
        </member>
        <member name="M:Symbol.Fusion.Action.Open(Symbol.Fusion.FINTRF_CONTEXTHANDLE@)">
            <summary>
            Open a connection with fusion hardware context and get a handle.
            </summary>
            <param name="handle">A pointer to the handle to be opened</param>
        </member>
        <member name="M:Symbol.Fusion.Action.Close(Symbol.Fusion.FusionAccessType)">
            <summary>
            Close the established connection with fusion hardware.
            </summary>
        </member>
        <member name="M:Symbol.Fusion.Action.Close(Symbol.Fusion.FINTRF_CONTEXTHANDLE)">
            <summary>
            Close the established connection with fusion hardware.
            </summary>
            <param name="handle">A pointer to the handle to be opened</param>
        </member>
        <member name="T:Symbol.Fusion.WLAN.Encryption">
            <summary>
            Summary description for Encryption.
            </summary>
        </member>
        <member name="F:Symbol.Fusion.WLAN.Encryption.MAX_WEP_KEYS">
            <summary>
            Maximum number of WEP keys used
            </summary>
        </member>
        <member name="F:Symbol.Fusion.WLAN.Encryption.MAX_STRING_LENGTH_104_BIT">
            <summary>
            KeyLength for 104 bit encryption
            </summary>
        </member>
        <member name="F:Symbol.Fusion.WLAN.Encryption.MAX_WEP_STRING_LENGTH_104_BIT">
            <summary>
            KeyLength for 104 bit WEP encryption
            </summary>
        </member>
        <member name="F:Symbol.Fusion.WLAN.Encryption.MAX_STRING_LENGTH_40_BIT">
            <summary>
            Keylength for 40 bit encryption
            </summary>
        </member>
        <member name="F:Symbol.Fusion.WLAN.Encryption.MAX_WPA_HEX_LENGTH">
            <summary>
            Hexadecimal key length for WPA including the NULL terminator.
            </summary>
        </member>
        <member name="F:Symbol.Fusion.WLAN.Encryption.MAX_PASSPHRASE_LENGTH">
            <summary>
            Maximum allowed length of pass phrase
            </summary>
        </member>
        <member name="M:Symbol.Fusion.WLAN.Encryption.#ctor">
            <summary>
            Create an Encryption object
            </summary>
        </member>
        <member name="M:Symbol.Fusion.WLAN.Encryption.IsValid">
            <summary>
            Checks the integrity of data provided in this class.
            </summary>
            <returns>Returns true if data provided is coherent.</returns>
            <remarks>This function is called by the assembly before creating a profile. In addition
            this function can be used by developers to verify the data after filling an Encryption
            object.</remarks>
            <exception cref="T:Symbol.Exceptions.InvalidRequestException">Throws exceptions if WepIndex
            is out of range or if WepKey lengths are too large or if PassPhrase length is too large</exception>
        </member>
        <member name="P:Symbol.Fusion.WLAN.Encryption.EncryptionType">
            <summary>
            Define the type of Encryption used
            </summary>
        </member>
        <member name="P:Symbol.Fusion.WLAN.Encryption.WepIndex">
            <summary>
            Defines the current WEP key index that is being used in hex key mode.
            </summary>
        </member>
        <member name="P:Symbol.Fusion.WLAN.Encryption.WepKeys">
            <summary>
            Defines the current WEP key that is being used in hex key mode. 
            </summary>
        </member>
        <member name="P:Symbol.Fusion.WLAN.Encryption.PassPhrase">
            <summary>
            Defines the pass phrase that is being used.
            </summary>
        </member>
        <member name="P:Symbol.Fusion.WLAN.Encryption.HexKey">
            <summary>
            Defines the hex key that is being used. This is the one to be used for the encrypition types TKIP_HEX, AES_HEX and SMS4_HEX.
            </summary>
        </member>
        <member name="P:Symbol.Fusion.WLAN.Encryption.AESAllowMixedMode">
            <summary>
            Defines whether the WLAN device can operate in an environment which supports devices using both
            TKIP and AES encryption. This is supported only when the profile is using the AES encryption (with passphrase or hex key).
            The Groupwise key for encrypting multicast packets will be encrypted using TKIP method rather the stronger
            AES method.  This option is available if the AP's are configured in this
            manner.
            This was introduced in Fusion version 2.57.
            Also supported in Fusion versions 3.00 and above. 
            But not supported on the Fusion versions 2.60 and 2.61. 
            On the Fusion versions 3.20 or later, the usage of this has been deprecated and the usage of AllowMixedMode is recommended.
            </summary>
        </member>
        <member name="P:Symbol.Fusion.WLAN.Encryption.AllowMixedMode">
            <summary>
            Defines whether the WLAN device can operate in an environment which supports devices using both
            TKIP and AES encryption. This is supported only when the profile is using either the AES encryption (with passphrase or hex key) or the TKIP encryption (with passphrase or hex key). 
            The Groupwise key for encrypting multicast packets will be encrypted using TKIP method rather the stronger
            AES method.  This option is available if the AP's are configured in this
            manner.
            This was introduced in Fusion version 3.20.
            On the Fusion versions 3.20 or later, the usage of this is recommended over the usage of AESAllowMixedMode.
            Not supported on the Fusion versions older than 3.20. 
            </summary>
        </member>
        <member name="T:Symbol.Fusion.WLAN.Encryption.PassKeys">
            <summary>
            This class is used to set WEPKeys. It holds a list of strings, one per WEPKey. 
            </summary>
        </member>
        <member name="M:Symbol.Fusion.WLAN.Encryption.PassKeys.#ctor(System.Int32)">
            <summary>
            PassKeys constructor
            </summary>
            <param name="maxKeys">The maximum number of keys</param>
        </member>
        <member name="M:Symbol.Fusion.WLAN.Encryption.PassKeys.Validate(System.Int32)">
            <summary>
            Checks whether all strings are smaller than the given size
            </summary>
            <param name="maxSize">The maximum allowable size of a string</param>
            <returns></returns>
            <remarks>This function is called by the assembly before creating a profile. In addition
            this function can be used by developers to verify the data after filling a PassKeys
            object.</remarks>
        </member>
        <member name="P:Symbol.Fusion.WLAN.Encryption.PassKeys.Item(System.Int32)">
            <summary>
            Access a selected pass key
            </summary>
        </member>
        <member name="T:Symbol.Fusion.WLAN.Encryption.EncryptionTypes">
            <summary>
            Define the type of Encryption used.
            </summary>
        </member>
        <member name="F:Symbol.Fusion.WLAN.Encryption.EncryptionTypes.NONE">
            <summary>
            No Encryption.
            </summary>
        </member>
        <member name="F:Symbol.Fusion.WLAN.Encryption.EncryptionTypes.HEX_40BIT">
            <summary>
            40 bit WEP Encryption with Hex Key.
            Use Symbol.Fusion.WLAN.Encryption.WepKeys[i] in order to provide the WEP key along with the correct key index (i) set in Symbol.Fusion.WLAN.Encryption.WepIndex.
            </summary>
        </member>
        <member name="F:Symbol.Fusion.WLAN.Encryption.EncryptionTypes.PASSPH_40BIT">
            <summary>
            40 bit WEP Encryption with pass Phrase.
            Use Symbol.Fusion.WLAN.Encryption.PassPhrase in order to provide the pass phrase.
            </summary>
        </member>
        <member name="F:Symbol.Fusion.WLAN.Encryption.EncryptionTypes.HEX_128BIT">
            <summary>
            128 bit WEP Encryption with Hex Key. Legacy. Will be deprecated soon.
            Use Symbol.Fusion.WLAN.Encryption.WepKeys[i] in order to provide the WEP key along with the correct key index (i) set in Symbol.Fusion.WLAN.Encryption.WepIndex.
            </summary>
        </member>
        <member name="F:Symbol.Fusion.WLAN.Encryption.EncryptionTypes.HEX_104BIT">
            <summary>
            128 bit WEP Encryption with Hex Key (104 -> the length without taking the 24-bit initialization vector).
            Use Symbol.Fusion.WLAN.Encryption.WepKeys[i] in order to provide the WEP key along with the correct key index (i) set in Symbol.Fusion.WLAN.Encryption.WepIndex.
            </summary>
        </member>
        <member name="F:Symbol.Fusion.WLAN.Encryption.EncryptionTypes.PASSPH_128BIT">
            <summary>
            128 bit WEP Encryption with Pass phrase. Legacy. Will be deprecated soon.
            Use Symbol.Fusion.WLAN.Encryption.PassPhrase in order to provide the pass phrase.
            </summary>
        </member>
        <member name="F:Symbol.Fusion.WLAN.Encryption.EncryptionTypes.PASSPH_104BIT">
            <summary>
            128 bit WEP Encryption with Pass phrase (104 -> the length without taking the 24-bit initialization vector).
            Use Symbol.Fusion.WLAN.Encryption.PassPhrase in order to provide the pass phrase.
            </summary>
        </member>
        <member name="F:Symbol.Fusion.WLAN.Encryption.EncryptionTypes.TKIP">
            <summary>
            TKIP Encryption  with pass phrase. Legacy. Will be deprecated soon.
            Use Symbol.Fusion.WLAN.Encryption.PassPhrase in order to provide the pass phrase.
            </summary>
        </member>
        <member name="F:Symbol.Fusion.WLAN.Encryption.EncryptionTypes.TKIP_PASSPH">
            <summary>
            TKIP Encryption with pass phrase.
            Use Symbol.Fusion.WLAN.Encryption.PassPhrase in order to provide the pass phrase.
            </summary>
        </member>
        <member name="F:Symbol.Fusion.WLAN.Encryption.EncryptionTypes.AES">
            <summary>
            AES Encryption with pass phrase. Legacy. Will be deprecated soon.
            Use Symbol.Fusion.WLAN.Encryption.PassPhrase in order to provide the pass phrase.
            </summary>
        </member>
        <member name="F:Symbol.Fusion.WLAN.Encryption.EncryptionTypes.AES_PASSPH">
            <summary>
            AES Encryption with pass phrase.
            Use Symbol.Fusion.WLAN.Encryption.PassPhrase in order to provide the pass phrase.
            </summary>
        </member>
        <member name="F:Symbol.Fusion.WLAN.Encryption.EncryptionTypes.TKIP_HEX">
            <summary>
            TKIP Encryption with Hex key.
            This is supported in Fusion versions 2.60 and above, except Fusion version 2.57.
            Use Symbol.Fusion.WLAN.Encryption.HexKey in order to provide the hex key.
            </summary>
        </member>
        <member name="F:Symbol.Fusion.WLAN.Encryption.EncryptionTypes.AES_HEX">
            <summary>
            AES Encryption with Hex key.
            This is supported in Fusion versions 2.60 and above, except Fusion version 2.57.
            Use Symbol.Fusion.WLAN.Encryption.HexKey in order to provide the hex key.
            </summary>
        </member>
        <member name="F:Symbol.Fusion.WLAN.Encryption.EncryptionTypes.SMS4">
            <summary>
            SMS4 Encryption.
            Mandatory to be specified with WAPI profile.
            This is supported in Fusion versions 3.30 and above.
            </summary>
        </member>
        <member name="F:Symbol.Fusion.WLAN.Encryption.EncryptionTypes.SMS4_PASSPH">
            <summary>
            SMS4  Encryption with pass-phrase.
            This is supported in Fusion versions 3.30 and above.
            </summary>
        </member>
        <member name="F:Symbol.Fusion.WLAN.Encryption.EncryptionTypes.SMS4_HEX">
            <summary>
            SMS4  Encryption with hex key.
            This is supported in Fusion versions 3.30 and above.
            </summary>
        </member>
        <member name="T:Symbol.Fusion.WLAN.Encryption.AES_ALLOW_MIXED_MODE">
            <summary>
            Defines whether the WLAN device can operate in an environment which supports devices using both
            TKIP and AES encryption. This is supported only when the profile is using the AES encryption (with passphrase or hex key). 
            The Groupwise key for encrypting multicast packets will be encrypted using TKIP method rather the stronger
            AES method.  This option is available if the AP's are configured in this
            manner.
            This was introduced in Fusion version 2.57.
            Also supported in Fusion versions 3.00 and above.
            But not supported on the Fusion versions 2.60 and 2.61. 
            </summary>
        </member>
        <member name="F:Symbol.Fusion.WLAN.Encryption.AES_ALLOW_MIXED_MODE.AES_ALLOW_MIXED_MODE_UNDEFINED">
            <summary>
            The mixed mode undefined. This means that the option has not been specified.
            </summary>
        </member>
        <member name="F:Symbol.Fusion.WLAN.Encryption.AES_ALLOW_MIXED_MODE.AES_ALLOW_MIXED_MODE_OFF">
            <summary>
            The mixed mode off.
            </summary>
        </member>
        <member name="F:Symbol.Fusion.WLAN.Encryption.AES_ALLOW_MIXED_MODE.AES_ALLOW_MIXED_MODE_ON">
            <summary>
            The mixed mode on.
            </summary>
        </member>
        <member name="T:Symbol.Fusion.WLAN.Encryption.ALLOW_MIXED_MODE">
            <summary>
            Defines whether the WLAN device can operate in an environment which supports devices using both
            TKIP and AES encryption. This is supported only when the profile is using either the AES encryption (with passphrase or hex key) or the TKIP encryption (with passphrase or hex key). 
            The Groupwise key for encrypting multicast packets will be encrypted using TKIP method rather the stronger
            AES method.  This option is available if the AP's are configured in this
            manner.
            This was introduced in Fusion version 3.20.
            Not supported on the Fusion versions older than 3.20. 
            </summary>
        </member>
        <member name="F:Symbol.Fusion.WLAN.Encryption.ALLOW_MIXED_MODE.ALLOW_MIXED_MODE_UNDEFINED">
            <summary>
            The mixed mode undefined. This means that the option has not been specified.
            </summary>
        </member>
        <member name="F:Symbol.Fusion.WLAN.Encryption.ALLOW_MIXED_MODE.ALLOW_MIXED_MODE_OFF">
            <summary>
            The mixed mode off.
            </summary>
        </member>
        <member name="F:Symbol.Fusion.WLAN.Encryption.ALLOW_MIXED_MODE.ALLOW_MIXED_MODE_ON">
            <summary>
            The mixed mode on.
            </summary>
        </member>
    </members>
</doc>
