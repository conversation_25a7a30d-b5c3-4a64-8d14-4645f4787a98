<?php


// include_once 'DbArtsana.class.php';
// $db = new DbArtsana();

$db_name = 'artsana';
$app_config_file = "/etc/www_pass/" . $db_name . ".env";
if (file_exists($app_config_file)) {
    $CONF = parse_ini_file($app_config_file);
} else {
    die("Brak pliku konfiguracji: $app_config_file");
}

$servername = $CONF['DB_HOST'];
$username = $CONF['DB_USER'];
$password = $CONF['DB_PASSWORD'];

// Create connection
$conn = new mysqli($servername, $username, $password, "artsana");

// Check connection
if ($conn->connect_error) {
    die("Connection failed: " . $conn->connect_error);
}
echo "Connected successfully<br>";

error_reporting(E_ALL);
ini_set('display_errors', 1);

$transport_number = $_GET['transport_number'];






// $result  = $conn->query($query);

// //debug zapytania result
// echo "<pre>";
// print_r($result);
// echo "</pre>";

// $query = "SELECT hu.m_palet as lp,count(distinct Handling_Unit) as box_number, hu.transport_number,
// hu.delivery_number, h.Handling_Unit, hu.type,
// h.Codice_EANUPC, h.Materiale, 
// h.Codice_materiale, h.QT1, h.QT2,
// customer_name,address,postal_code,city
// FROM artsana._hucontent h 
// left join artsana._hu hu on Handling_Unit=hu_sscc_number 
// left join _dhd d on d.transport_number=hu.transport_number and d.delivery_number=hu.delivery_number
// WHERE  hu.transport_number='" . $transport_number . "'
// group by hu.m_palet

// order by  cast(hu.m_palet as unsigned) asc";



// if (!$conn->query($query)) {
//     echo ("Error description: " . $conn->error);
// }

// $result = $conn->query($query);

// // debu error   
// echo "<pre>";



// while ($aRow15 = $result->fetch_assoc()) {
//     print_r($aRow15);
// }



//wyswietla cross + palety jednorodne na stock


$query = "SELECT hu.m_palet as lp,count(distinct Handling_Unit) as box_number, hu.transport_number,
hu.delivery_number, h.Handling_Unit, hu.type,
h.Codice_EANUPC, h.Materiale, 
h.Codice_materiale, h.QT1, h.QT2,
customer_name,address,postal_code,city,round(sum(Volume_M3)/1.8,1) as objetosc_pal
FROM artsana._hucontent h 
left join artsana._hu hu on Handling_Unit=hu_sscc_number 
left join _dhd d on d.transport_number=hu.transport_number and d.delivery_number=hu.delivery_number
WHERE  hu.transport_number='" . $transport_number . "'
group by hu.m_palet

order by  cast(hu.m_palet as unsigned) asc";

echo $query;
$result  = $conn->query($query);
$przerwanie = 0;


//$tablica = mysql_fetch_assoc($result);
//print_r($tablica);


while ($aRow = $result->fetch_assoc()) {

    echo "<br>" . $aRow['lp'];
    //sleep(2);
    wydruk($aRow, "**********");
    //exit();
}

// wydruk(array(
//     'lp' => 'MIX',
//     'lp' => '',
//     'box_number' => '',
//     'delivery_number' => '',
//     'address' => '',
//     'city' => '',
//     'postal_code' => '',
//     'transport_number' => '',
//     'customer_name' => '',
// ), "**********");




// generuje hucontent dla kartonow które maja wiecej niz jeden kod
// $query = "
//     select *,
// (SELECT m_palet  FROM artsana.na_stock ns
// where ns.transport_number=a.transport_number and ns.Materiale=h2.Materiale limit 1) as lp,
// (SELECT LPAD(typ_ulozenia,10,'-')  FROM artsana.na_stock ns
// where ns.transport_number=a.transport_number and ns.Materiale=h2.Materiale limit 1) as customer_name

//  from (SELECT count(distinct h.Materiale) as ile_kodow_karton, 

// TRIM(LEADING '0' FROM hu_sscc_number)  as hu_sscc_number,
// d.transport_number,
// Materiale as delivery_number, '' as address , '' as box_number, '' as city, '' as postal_code,hu.type

// FROM artsana._hucontent h 
// left join artsana._hu hu on Handling_Unit=hu_sscc_number
// left join artsana._dhd d on d.transport_number=hu.transport_number and d.delivery_number=hu.delivery_number
// WHERE  hu.transport_number='" . $transport_number . "'
// and d.delivery_number like '30%' and !(customer_name like '%MK S%' or customer_name like '%S MK%' or customer_name like '%Artsana Poland%')


// group by hu_sscc_number
// having ile_kodow_karton>1 order by h.id) as a left join artsana._hucontent h2 
// on TRIM(LEADING '0' FROM h2.Handling_Unit)=a.hu_sscc_number where a.hu_sscc_number is not null
// order by lp";


$query = "
select *,
(SELECT m_palet  FROM artsana.na_stock ns
where ns.transport_number=a.transport_number and ns.Materiale=h2.Materiale limit 1) as lp,
(SELECT LPAD(typ_ulozenia,10,'-')  FROM artsana.na_stock ns
where ns.transport_number=a.transport_number and ns.Materiale=h2.Materiale limit 1) as customer_name,

(SELECT objetosc_pal  FROM artsana.na_stock ns
where ns.transport_number=a.transport_number and ns.Materiale=h2.Materiale limit 1) as objetosc_pal

 from (SELECT count(distinct h.Materiale) as ile_kodow_karton, 

TRIM(LEADING '0' FROM hu_sscc_number)  as hu_sscc_number,
d.transport_number,
Materiale as delivery_number, '' as address , '' as box_number, '' as city, '' as postal_code,hu.type

FROM artsana._hucontent h 
left join artsana._hu hu on Handling_Unit=hu_sscc_number
left join artsana._dhd d on d.transport_number=hu.transport_number and d.delivery_number=hu.delivery_number
WHERE  hu.transport_number='" . $transport_number . "'
and 

((d.delivery_number LIKE '30%'
        AND !( d.customer_name LIKE '%MK S%'
        OR d.customer_name LIKE '%S MK%'
        OR d.customer_name LIKE '%Artsana Poland%'))
        OR (d.delivery_number LIKE '387%'
        AND customer_name LIKE '%Partners Teresin%')
        or (d.delivery_number LIKE '30%' and city='RAWA MAZOWIECKA')) 

group by hu_sscc_number
having ile_kodow_karton>1 order by h.id) as a left join artsana._hucontent h2 
on h2.Handling_Unit=a.hu_sscc_number where a.hu_sscc_number is not null
order by cast(lp as UNSIGNED)";

$kody_z_mixow = array();
$result = $conn->query($query);

while ($aRow = $result->fetch_assoc()) {

    $kody_z_mixow[$aRow['lp']] = $aRow;
}
// powyższa pętla zwraca tablicę z kodami z mixów które mają więcej niż jeden kod na kartonie 


// poniższa pętla wydrukuje etykiety dla kartonów z mixów które mają więcej niż jeden kod na kartonie 
foreach ($kody_z_mixow as $key => $value) {

    if ($key < 400) {
        echo "<pre>";
        print_r($value);
        echo "</pre>";
        wydruk($value, "**********");
        sleep(2);
    }
}
















function wydruk($params, $adres_ip)
{

    // lp,transport_number,delivery_number,customer_name,address,postal_code,city, box_number+0 as box_number
    $layout = "^XA
^CF0,390
^FO150,100^FD" . $params['lp'] . "^FS
^CF0,60
^FO20,450^FD Total:  " . $params['box_number'] . " ^FS

^CF0,140^FO490,100^FD  " . $params['type'] . " ^FS
   ^CF0,50
^FO20,550^

^FO20,550^FD Nr Delivery:   " . $params['delivery_number'] . "^FS
    ^CF0,50
^FO20,650^FD Firma:^FS
^FO20,730^FD " . $params['customer_name'] . "^FS

^FO20,850^FD Adres: " . $params['address'] . "^FS
^FO20,950^FD          " . $params['postal_code'] . " " . $params['city'] . "^FS
^FO20,1050^FD Nr Transportu:   " . $params['transport_number'] . "^FS
^CF0,60^FO490,1110^FD " . $params['objetosc_pal'] . " mpal ^FS 

^XZ";
    echo $layout;

    $file = fopen("/tmp/etykietakaartsana.zbr", "w");
    fputs($file, $layout);
    fclose($file);
    sleep(1);

    $socket = socket_create(AF_INET, SOCK_STREAM, SOL_TCP);
    $result = socket_connect($socket, $adres_ip, "9100");
    socket_write($socket, $layout, strlen($layout));
    socket_close($socket);
}
