﻿using System;
using System.Linq;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using System.Threading;
using System.Text.RegularExpressions;

namespace Tarczyn__Magazyn
{
    public partial class Info_EAN : Form
    {

        MainMenu parent = null;



        List<string> _regal = new List<string>();


        public Info_EAN(MainMenu myParent)
        {
            if (Wlasciwosci.GNG != "")
            {
                BazaDanychExternal.ChangeHost(Wlasciwosci.GNG, Wlasciwosci.Varhost, Wlasciwosci.VarLoginMYSQL, Wlasciwosci.VarHasloMYSQL);
            }
            FullScreenMode.OknoOFF(this);
            InitializeComponent();
            parent = myParent;

            //ZacznijSkanowanie();

        }
        
        List<string> Etykiety = new List<string>();
        Thread Skanowanie = null;


        private void ZacznijSkanowanie()
        {

            StringBuilder login = new StringBuilder();
            Skaner.UstawTryb_String(login);
            Skaner.Zacznij_Skanowanie();
            Skanowanie = new Thread(() => CheckString(login));
            Skanowanie.IsBackground = true;
            Skanowanie.Start();
        }
        private void CheckString(StringBuilder cc)
        {
            while (true)
            {
                if (cc.ToString() != "")
                {
                    this.Invoke(new EventHandler(delegate
                    {
                        
                        dodawanie(cc.ToString());
                    }));
                    return;
                }
                Thread.Sleep(200);
            }
        }
        private void Zakoncz_Skanowanie()
        {
            try
            {
                Skanowanie.Abort();
                Skaner.Przewij_Skanowanie();
            }
            catch
            {
            }
        }

        private void Exit_Click_1(object sender, EventArgs e)
        {
            Zakoncz_Skanowanie();
            BazaDanychExternal.ChangeHost(Wlasciwosci.Varbaza, Wlasciwosci.Varhost, Wlasciwosci.VarLoginMYSQL, Wlasciwosci.VarHasloMYSQL);
            parent.Show();
            this.Close();
        }


        private void button2_Click(object sender, EventArgs e)
        {
          

        }


        private void dodawanie(string gg)
        {

            Zakoncz_Skanowanie();
            //MessageBox.Show("1");
            
            //object test1 = BazaDanychExternal.Wyczytaj_Tabele("SELECT b.id,b.active FROM wmsgg.etykiety_kartony a,wmsgg.etykiety b where karton_ean like'"+gg+"' and a.idetykiety=b.id order by b.id desc LIMIT 1");
            object test1 = BazaDanychExternal.Wyczytaj_Tabele("SELECT kod,kod_nazwa,k.ean,kk.ilosc_w_opakowaniu,kk.ilosc_dni_przydatnosci,k.ilosc_paleta,ilosc_opak_w_zborczym FROM kody_ean k left join kody kk on kk.id=kod_id WHERE k.ean='" + gg + "' ");
            //MessageBox.Show("2");
            DataTable Wynik = (DataTable)test1;
            if (test1 == null)
            {
                //MessageBox.Show("3");
                label2.Text = "Brak w bazie";
                Magazyn_Label.Text = "";
                label6.Text = "";
                label3.Text = "";
                label7.Text = "";
                label9.Text = "";
                label10.Text = "";


                ZacznijSkanowanie();
                return;
            }
            else if (Wynik.Rows.Count < 1)
            {
                //MessageBox.Show("4");
                label2.Text = "Brak w bazie";
                Magazyn_Label.Text = "";
                label6.Text = "";
                label3.Text = "";
                label7.Text = "";
                label9.Text = "";
                label10.Text = "";
                ZacznijSkanowanie();
                return;
            }
            else
            {
                //MessageBox.Show("5");
                ZacznijSkanowanie();
                
                label2.Text = Wynik.Rows[0]["kod"].ToString();
                Magazyn_Label.Text = Wynik.Rows[0]["kod_nazwa"].ToString();
                label6.Text = Wynik.Rows[0]["ean"].ToString();
                label3.Text = Wynik.Rows[0]["ilosc_w_opakowaniu"].ToString();
                label7.Text = Wynik.Rows[0]["ilosc_dni_przydatnosci"].ToString();
                label9.Text = Wynik.Rows[0]["ilosc_paleta"].ToString();
                label10.Text = "kart: " + Wynik.Rows[0]["ilosc_opak_w_zborczym"].ToString();
                return;


            }
            //MessageBox.Show("5");


            //object test2 = BazaDanychExternal.Wyczytaj_Jedna_Wartosc("select wartosc4 from kolektor where wartosc1='" + gg + "' order by id desc LIMIT 1");


            ZacznijSkanowanie();
        }


        private void Zakoncz_Click(object sender, EventArgs e)
        {
     
        }

        private void WczytywanieNaLinie_Load_1(object sender, EventArgs e)
        {
            ZacznijSkanowanie();
        }

        private void button2_Click_1(object sender, EventArgs e)
        {
           
        }

        private void button1_Click(object sender, EventArgs e)
        {
            dodawanie(textBox1.Text);
            textBox1.Text = "";
        }





    }
}
