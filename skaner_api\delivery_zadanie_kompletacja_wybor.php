<?php

//include_once 'db_pdo.php';
include_once 'Db.class.php';
include_once 'funkcje.inc';
//error_reporting(E_ALL);
//ini_set('display_errors', 1);

$db = new Db();

if (empty($argv)) {
    //$argv = array();
    $argv[1] = $_GET['db'];
    $argv[2] = $_GET['akcja'];
}

$baza_danych = $_GET['db'];
$akcja = $argv[2];
//$system_id = $_GET['system_id'];
$zadanie_head_id = $_GET["zadanie_head_id"];

//http://25.56.91.22/wmsgg/public/skaner_api/delivery_skanowanie_etykiety.php?db=wmsgg&akcja=szukaj&system_id=6&paleta_id=547144


$komunikat = "OK";


if ($akcja == "podglad") {



    $sql = 'SELECT zd.id AS id,
        concat(m1.regal,"-",m1.miejsce,"-",m1.poziom,"; ",k.kod,"; ", TRIM(TRAILING "." FROM TRIM(TRAILING "0" FROM zd.ilosc)) ," ",k.jm,",  ",round(TRIM(TRAILING "." FROM TRIM(TRAILING "0" FROM zd.ilosc/k.ilosc_szt_palecie)*100),0),"%" ) as nazwa_wyswietlana,
concat("H: ",m1.hala," ",m1.regal,"-",m1.miejsce,"-",m1.poziom ) AS stare_m_nazwa,
k.kod AS kod,
TRIM(TRAILING "." FROM TRIM(TRAILING "0" FROM zd.ilosc)) AS ilosc,
TRIM(TRAILING "." FROM TRIM(TRAILING "0" FROM zd.ilosc/k.ilosc_szt_palecie)) AS procent
FROM zadania_dane zd LEFT JOIN zadania_head AS zh ON zh.id=zd.zadanie_head_id LEFT JOIN zadania_doc_type AS zdt ON zh.doc_type=zdt.id
LEFT JOIN zadania_typ AS zt ON zh.typ=zt.id LEFT JOIN ' . $baza_danych . '.kody AS k ON zd.kod_id=k.id LEFT JOIN ' . $baza_danych . '.miejsca AS m1 ON m1.id=zd.stare_m
LEFT JOIN ' . $baza_danych . '.miejsca AS m2 ON m2.id=zd.nowe_m LEFT JOIN ' . $baza_danych . '.pracownicy AS p ON zd.przydzielenie_pracownik_id=p.id
LEFT JOIN ' . $baza_danych . '.pracownicy AS p2 ON zd.realizacja_pracownik_id=p2.id
LEFT JOIN ' . $baza_danych . '.systemy AS s ON s.wartosc=zh.system_id
WHERE zh.id=' . $zadanie_head_id . ' AND zh.typ=4 AND zh.doc_type=2 and zd.status=1
GROUP BY zh.id, zd.id ORDER BY 


zd.pominiecie ASC,
            CASE
              WHEN ((zd.ilosc/ilosc_szt_palecie)*100>70) THEN 2
              ELSE 1
            END DESC,
            CASE
              WHEN (k.waga_szt_kg>30) THEN 2
              ELSE 1
            END desc,
            CASE
              WHEN ((zd.ilosc/ilosc_szt_palecie)*100>70) THEN 3
              WHEN ((zd.ilosc/ilosc_szt_palecie)*100 between 30 and 70) THEN 2
              ELSE 1
            END DESC,

            floor(m1.regal/2) desc,if(((floor(m1.regal/2)%2)="1"),(300-m1.miejsce),m1.miejsce) desc,m1.poziom
';



    $result = $db->mGetResultAsXML($sql);
        //echo $sql;
//           echo "<pre>";
//    echo print_r($result);
//    echo "</pre>";
//    return false;
    //echo "<br>" . $sql;
    if (!empty($db->errors))
        $komunikat = $db->errors;

    if (count($result) == 0) {
        $komunikat = "Brak pozycji do zrealizowania";
        return show_komunikat_xml($komunikat);
    } else {
        return xml_from_indexed_array(array('komunikat' => $komunikat, 'pozycje' => $result));
    }
}
?>