<?php

//include_once 'db_pdo.php';
include_once 'Db.class.php';
include_once 'funkcje.inc';
//error_reporting(E_ALL);
//ini_set('display_errors', 1);

$db = new Db();
//
//if (empty($argv)) {
//    //$argv = array();
//    $argv[1] = $_GET['db'];
//    $argv[2] = $_GET['akcja'];
//}
//
$baza_danych = $_GET['db'];
$akcja = $_GET['akcja'];
$delivery_id = $_GET['delivery_id'];




$komunikat = "OK";

if ($akcja == "wyswietl") {

    function wyswietl($baza_danych, $delivery_id, $db) {
        $sql = "SELECT nazwa_nosnika as nosnik, d.ile, concat(d.wysokosc,'x', d.szerokosc,'x', d.dlugosc) as wymiar,d.miejsc_paletowych as mpal, d.waga as kg  FROM " . $baza_danych . ".delivery_wymiary d where delivery_id=" . $delivery_id." order by id desc";
        //echo $sql;
        $result = $db->mGetResultAsXML($sql);
        return $result;
    }

    $wymiary = wyswietl("wmsgg",$delivery_id, $db);

    return xml_from_indexed_array(array('komunikat' => $komunikat, 'dodatkowa_akcja' => 'wybierz_wymiar', 'wymiary_lista' => $wymiary));
}



