<?php

include_once 'Db.class.php';
include_once 'funkcje.inc';
$db = new Db();



$komunikat = "OK";
$paleta_id = $_GET['paleta_id'];
$baza_danych = $_GET['baza_danych'];



$sql = 'SELECT s.nazwa as skrot,paleta_id
FROM ' . $baza_danych . '.etykiety e
left join ' . $baza_danych . '.systemy s on e.system_id=s.wartosc
WHERE e.paleta_id=' . $paleta_id . '
group by paleta_id';

$result = $db->mGetResultAsXML($sql);
//$ile = mysql_num_rows($result);
//echo "<br>" . $sql . "<br>";


if (count($result) > 0) {
    $ilosc_etykiet = count($result);
    $i = 1;
    foreach ($result as $index => $aRow) {

        if (($i % 3) == 0) { // z<PERSON><PERSON><PERSON>czenie, bo nie drukowało wszystkich etykiet
            sleep(1);
        }

        printlabelsmall_palet($aRow, "***********");



        $i+=1;
        $komunikat.=$db->errors;
    }


//$komunikat=$sql;
} else {
    $komunikat = "Brak zadan";
}

show_komunikat_xml($komunikat);

function printlabelsmall_palet($szukaj_etykiete, $adres_ip) {

    //    ------
    //    |     |
    //    |     |
    //    |     |
    //    |     |
    //    |     |
    //    ------
    //
	    //Instrukcja szablonu etykiety:
    //^FO200,780 ^FB400,,,	 ^A0B,70,50 ^FD'.$blloc.'^FS'
    // ^FO200,780 - 200 od lewej , 780 od góry, licz_c tak jak etykieta wysz_a z etykieciarki (patrz rys. u góry)  
    //  FB400 - format block o d_ugo_ci 400                                                   
    //  A0B,70,50 - 70 wysoko__ czcionki, 50 d_ugo__ czcionki - zw__anie czcionki
    // AOB - pionowe, ADN - poziome


    $layout = '^XA'
            //. '^FO80,80	^ADN,30,20	^FDDATA PRZYJ: '.date("Y-m-d").'^FS  '
            //. '^FO140,180	^A0B,60,30	^FDLISTA KONT:^FS '
            . '^FO140,180	^ADN,60,30	^FD ' . $szukaj_etykiete['skrot'] . '^FS '
            . '^FO120,250^BY3	 ^BCN,100,Y,N,N ^A0,60 ^FDDS' . $szukaj_etykiete['paleta_id'] . '^FS'
            //       .'^FO40,60 ^FB 400,,,C ^A0B,100,100 ^FD '.$params['nr_palety'].'/'.$params['nr_kartonu'].' ^FS' 
            //.'^FO180,145 ^FB 980,,,C ^A0B,120,180 ^FD '.$params['kod'].' ^FS' 
            . ' ^XZ';
    $file = fopen("/tmp/etykietakarton.zbr", "w");
    fputs($file, $layout);
    fclose($file);
    //flush();
    //sleep(1);

    $socket = socket_create(AF_INET, SOCK_STREAM, SOL_TCP);
    $result = socket_connect($socket, $adres_ip, "9100");
    socket_write($socket, $layout, strlen($layout));
    socket_close($socket);

    //sleep(1);
    //flush();
    //return 1;
}

?>