<?php

//include_once 'db_pdo.php';
include_once 'Db.class.php';
include_once 'funkcje.inc';
error_reporting(E_ALL);
ini_set('display_errors', 1);

$db = new Db();
//
//if (empty($argv)) {
//    //$argv = array();
//    $argv[1] = $_GET['db'];
//    $argv[2] = $_GET['akcja'];
//}
//
$baza_danych = $_GET['db'];
$akcja = $_GET['akcja'];

$doc_ref = $_GET['doc_ref'];

$doc_ref = sortuj_doc_ref($doc_ref);



$komunikat = "OK";

if ($akcja == "dodaj") {

    $delivery_wymiary_head_id="";

    $sql = 'SELECT id FROM wmsgg.delivery_wymiary_head
where dokument_dl="' . $doc_ref . '"';
    //echo $sql;
    $result = $db->mGetResultAsXML($sql);
//    print_r($result);
//    exit();
    if (empty($result)) {
        $sql = "insert into delivery_wymiary_head(dokument_dl, status_wyslania,zamkniete,pracownik_id) values("
                . " '" . $doc_ref . "',0,0, '" . $_GET['pracownik_id'] . "'); ";
        //echo $sql;
        $id = $db->mGetResultAsXML($sql);
        //echo $sql;
    } else {
        $id = $result[0]['id'];
    }
        $sql = "INSERT INTO `wmsgg`.`delivery_wymiary_dane`
                (
                `delivery_wymiary_head_id`,
                `wysokosc`,
                `szerokosc`,
                `dlugosc`,
                `waga`,
                `nazwa_nosnika`,
                `ile`,
                `miejsc_paletowych`)
                VALUES
                (
                '" . $id . "',
                '" . $_GET['wysokosc'] . "',
                '" . $_GET['szerokosc'] . "',
                '" . $_GET['dlugosc'] . "',
                '" . $_GET['waga'] . "',
                '" . $_GET['nazwa_nosnika'] . "',
                '" . $_GET['ile'] . "',
                '" . $_GET['miejsc_paletowych'] . "');";
        //echo $sql;
        $delivery_wymiary_head_id = $db->mGetResultAsXML($sql);
        //exit();
    if(!empty($_GET['nr_ds']))
    {
        // dodać wagę palety
    }


//    $sql = 'insert into ' . $baza_danych . '.delivery_wymiary (delivery_id, wysokosc, szerokosc, dlugosc, waga, nazwa_nosnika, ile, miejsc_paletowych,pracownik_id)
//values ("' . $_GET['delivery_id'] . '","' . $_GET['wysokosc'] . '","' . $_GET['szerokosc'] . '","' . $_GET['dlugosc'] . '","' . $_GET['waga'] . '","' . $_GET['nazwa_nosnika'] . '","' . $_GET['ile'] . '","' . $_GET['miejsc_paletowych'] . '","' . $_GET['pracownik_id'] . '")   ';
//    //echo $sql;
//    $result = $db->mGetResultAsXML($sql);


    return xml_from_indexed_array(array('komunikat' => $komunikat, 'delivery_wymiary_head_id' => $id));
}

function sortuj_doc_ref($doc_ref) {
    $dane = "";
    $array = explode(',', $doc_ref);
    //print_r($array);
    sort($array);
    foreach ($array as $value) {
        if ($dane == "") {
            $dane = $value;
        } else {
            $dane .= "," . $value;
        }
    }
    return $dane;
}
