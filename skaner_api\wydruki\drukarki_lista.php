<?php

//require_once("./../../lib/nusoap/lib/nusoap.php");


include_once './../Db.class.php';
include_once './../funkcje.inc';

ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

$db = new Db();
$params = get_params_to_array();

$komunikat = array();

//echo 1;
$komunikat['komunikat']="OK";
$sql = "SELECT adres_ip,opis FROM wmsgg.drukarki
where active=1 order by dp_domyslna desc, porzadek asc";

$drukarki = $db->mGetResultAsXML($sql);

$port = 9100;
$waitTimeoutInSeconds = 1;

foreach ($drukarki as $key => $value) {

    if ($fp = @fsockopen($drukarki[$key]['adres_ip'], $port, $errCode, $errStr, $waitTimeoutInSeconds)) {

        fclose($fp);
    } else {
        unset($drukarki[$key]);
    }
}

if (empty($drukarki)) {
    $komunikat['komunikat'] = " Brak drukarek";
}



if (!empty($db->errors))
    $komunikat['komunikat'] = $db->errors;

$komunikat['drukarki'] = $drukarki;

return xml_from_indexed_array($komunikat);
