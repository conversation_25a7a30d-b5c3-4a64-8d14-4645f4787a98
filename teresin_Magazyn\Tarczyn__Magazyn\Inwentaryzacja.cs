﻿﻿﻿﻿using System;
using System.Linq;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using MySql.Data.MySqlClient;
using System.Text.RegularExpressions;
using System.Threading;
//using System.Data.SqlServerCe;
using System.Collections;
using System.Xml;


namespace Tarczyn__Magazyn
{
    public partial class Inwentaryzacja : Form
    {
        ActionMenu myParent = null;


        List<string> _inw_wybor = new List<string>();
        List<string> _inw_data = new List<string>();
        List<string> _inw_opis = new List<string>();
        List<string> _nr_wspolny = new List<string>();
        List<string> _proba = new List<string>();
        int[] _inw = new int[100];

        List<string> _inw_hala = new List<string>();
        List<string> _inw_regal = new List<string>();
        List<string> _inw_poziom = new List<string>();
        List<string> _inw_status = new List<string>();

        int nr_inw = 0;
        private Thread Skanowanie;
        string etykieta_id = "0";

        string sap_gora_czesc2 = "";
        string kod = "";
        string podkod = "";
        double ilosc = 0;
        string operac_id_global = "";


        Dictionary<string, string> rec = new Dictionary<string, string>();

        int ile_do_inw = 0;
        TextBox AktualnyTextBox = null;


        //public static SqlCeConnection con = new SqlCeConnection(@"Data Source =.\Program Files\moto1\Northwind.sdf");







        public MySqlConnection ok = null;

        public Inwentaryzacja(ActionMenu MyParent)
        {
            InitializeComponent();
            this.myParent = MyParent;
            wms_wybrany.Text = Wlasciwosci.system_id;
            imie_nazwisk_label.Text = Wlasciwosci.wozek + ":" + Wlasciwosci.imie_nazwisko;
            //ok = myParent.conn;
            wyszukaj_inwentaryzacje();
            wypelnij_poziom();
            wypelnij_hale();
            wypelnij_regal();
            wypelnij_status();
            //this.ZacznijSkanowanie();
        }


        // podczas wchodzenia do inwentaryzacji
        private void wyszukaj_inwentaryzacje()
        {
            string zapytanie = "SELECT DATE_FORMAT(i.data,'%Y-%m-%d') as data,opis,inwentaryzacja_id,nr_wspolny,proba FROM inwentaryzacja i  where active=1 group by i.data,opis,inwentaryzacja_id order by inwentaryzacja_id desc limit 40";
            object obj2 = BazaDanychExternal.Wyczytaj_Tabele(zapytanie);
            DataTable tabela = (DataTable)obj2;

            for (byte k = 0; k < tabela.Rows.Count; k++)
            {
                _inw_wybor.Add(tabela.Rows[k]["data"].ToString() + " | " + tabela.Rows[k]["opis"].ToString());
                _inw[k] = Convert.ToInt32(tabela.Rows[k]["inwentaryzacja_id"].ToString());
                _inw_data.Add(tabela.Rows[k]["data"].ToString());
                _inw_opis.Add(tabela.Rows[k]["opis"].ToString());
                _nr_wspolny.Add(tabela.Rows[k]["nr_wspolny"].ToString());
                _proba.Add(tabela.Rows[k]["proba"].ToString());
            }
            if (_inw_wybor.Count == 0)
            {
                MessageBox.Show("Brak Inwentaryzacji do wykonania");
            }
            else
            {
                comboBox1.Visible = true;
                BindingSource bs = new BindingSource();
                bs.DataSource = _inw_wybor;
                comboBox1.DataSource = bs;
            }
        }


        private void wypelnij_hale()
        {
            string zapytanie = "SELECT m.hala FROM miejsca m GROUP BY m.hala;";
            object obj2 = BazaDanychExternal.Wyczytaj_Tabele(zapytanie);
            DataTable tabela = (DataTable)obj2;

            for (byte k = 0; k < tabela.Rows.Count; k++)
            {
                _inw_hala.Add(tabela.Rows[k]["hala"].ToString());
                //_inw[k] = Convert.ToInt32(tabela.Rows[k]["inwentaryzacja_id"]);
            }
            if (_inw_wybor.Count == 0)
            {
                MessageBox.Show("Brak hali");
            }
            else
            {
                //comboBox1.Visible = true;
                BindingSource bs = new BindingSource();
                bs.DataSource = _inw_hala;
                hala_comboBox2.DataSource = bs;
            }
        }

        private void wypelnij_regal()
        {
            //string zapytanie = "SELECT m.regal FROM miejsca m where LENGTH(regal)<=3 GROUP BY m.regal;";
            string zapytanie = "SELECT m.regal FROM miejsca m  GROUP BY m.regal order by regal";
            object obj2 = BazaDanychExternal.Wyczytaj_Tabele(zapytanie);
            DataTable tabela = (DataTable)obj2;

            for (byte k = 0; k < tabela.Rows.Count; k++)
            {
                _inw_regal.Add(tabela.Rows[k]["regal"].ToString());
            }
            if (_inw_regal.Count == 0)
            {
                MessageBox.Show("Brak regalow");
            }
            else
            {
                //comboBox1.Visible = true;
                BindingSource bs = new BindingSource();
                bs.DataSource = _inw_regal;
                comboBox3.DataSource = bs;
            }
        }
        private void wypelnij_poziom()
        {
            //_inw_poziom.Add("A");
           // _inw_poziom.Add("B");
            //_inw_poziom.Add("C");
           // _inw_poziom.Add("D");
           // _inw_poziom.Add("E");
            //_inw_poziom.Add("F");
            //_inw_poziom.Add("G");
            //_inw_poziom.Add("H");

           
            string zapytanie = "SELECT m.poziom FROM miejsca m left join etykiety e on e.miejscep=m.id where active=1 and poziom!='' and poziom!='A' and poziom!='B' and poziom!='C' and poziom!='D' and poziom!='E' and poziom!='F' and poziom!='G'  GROUP BY m.poziom order by poziom;";
            object obj2 = BazaDanychExternal.Wyczytaj_Tabele(zapytanie);
            DataTable tabela = (DataTable)obj2;

            for (byte k = 0; k < tabela.Rows.Count; k++)
            {
                _inw_poziom.Add(tabela.Rows[k]["poziom"].ToString());
                //_inw_poziom(tabela.Rows[k]["poziom"].ToString()) = tabela.Rows[k]["poziom"].ToString();
                //_inw_poziom[k] = tabela.Rows[k]["poziom"].ToString();
                //_inw[k] = Convert.ToInt32(tabela.Rows[k]["inwentaryzacja_id"]);
            }

            

            BindingSource bs = new BindingSource();
            bs.DataSource = _inw_poziom;
            comboBox4.DataSource = bs;
        }
        private void wypelnij_status()
        {
            //_inw_status.Add("ET_OK");
            _inw_status.Add("");
            _inw_status.Add("REPR");
            _inw_status.Add("USZK");
            //_inw_status.Add("REND");


            BindingSource bs = new BindingSource();
            bs.DataSource = _inw_status;
            status_comboBox2.DataSource = bs;
        }






        private void skopiuj_inwentaryzacje(object sender, EventArgs e)
        {

        }


        private void zsynchronizuj_inwentaryzacje(object sender, EventArgs e)
        {
            if (BazaDanychExternal.SprawdzCzyIstniejePolaczenie())
            {

                
            }
            else
            {
                MessageBox.Show("Brak Wifi");
            }
        }



















        // zatwierdzanie wczytanej etykiety
        private void button1_Click(object sender, EventArgs e)
        {
            //powrot.Visible = false;
            //comboBox1.Enabled = false;
            kod_textbox.Focus();
            kod_textbox.SelectionStart = kod_textbox.Text.Length;
            Skaner.Przewij_Skanowanie();
            if (kod_textbox.Text != "")
            {

                XmlNode node_local2 = KomunikacjaSerwerem();
                if (node_local2["komunikat"].InnerText != "OK")
                {
                    MessageBox.Show(node_local2["komunikat"].InnerText);
                    //MessageBox.Show(node_local.InnerXml
                    //return;
                    //this.ZacznijSkanowanie();
                }
                ilosc_etykiet.Text = node_local2["ilosc_zliczona"].InnerText + "/" + node_local2["stan"].InnerText;
                
                status_comboBox2.SelectedIndex = status_comboBox2.Items.IndexOf("");

            }
            else
            {
                MessageBox.Show("Pole kod jest puste.");
            }

            ilosc_textBox.Text = "";
            etykieta_textbox.Text = "";
            kod_textbox.Text = "";
            kod_nazwa.Text = "";
            label2.Text = "";
            podkod = "";
            kod = "";
            nrsap.Text = "";
            ilosc = 0;


            etykieta_textbox.Focus();
        }



        public XmlNode KomunikacjaSerwerem()
        {
            XmlNodeList xmlnode_local = null;
            XmlNode node_local = null;
            XmlDocument doc1 = WebService.Pobierz_XmlDocument("inwentaryzacja_manual.php?akcja=zapisz&pracownik_id=" + Wlasciwosci.id_Pracownika + "&imie_nazwisko=" + Uri.EscapeUriString(Wlasciwosci.imie_nazwisko) + "&operac_id=" + operac_id_global + "&wozek=" + Wlasciwosci.wozek + "&system_id=" + Wlasciwosci.system_id_id + "&inw_data=" + _inw_data[comboBox1.SelectedIndex] + "&inw_opis=" + Uri.EscapeUriString(_inw_opis[comboBox1.SelectedIndex]) + "&ilosc=" + ilosc_textBox.Text + "&kod=" + Uri.EscapeUriString(kod_textbox.Text) + "&inwentaryzacja_id=" + _inw[comboBox1.SelectedIndex] + "&hala=" + _inw_hala[hala_comboBox2.SelectedIndex] + "&regal=" + _inw_regal[comboBox3.SelectedIndex] + "&miejsce=" + textBox1.Text + "&poziom=" + _inw_poziom[comboBox4.SelectedIndex] + "&inw_status=" + _inw_status[status_comboBox2.SelectedIndex] + "&proba=" + _proba[comboBox1.SelectedIndex] + "&nrwspolny=" + _nr_wspolny[comboBox1.SelectedIndex] + "&etykieta_id=" + etykieta_textbox.Text + "&etykieta_klient=" + nrsap.Text );  
            xmlnode_local = doc1.GetElementsByTagName("dane");

            foreach (XmlNode wynik in xmlnode_local)
            {
                node_local = wynik;
            }
            return node_local;
        }




        private void Zakoncz_Skanowanie(object sender, EventArgs e)
        {
            //powrot.Visible = true;
            this.Skanowanie.Abort();
            Skaner.Przewij_Skanowanie();
            //this.button1.Click -= new EventHandler(this.Zakoncz_Skanowanie);
            //this.button1.Click += new EventHandler(this.button1_Click);

            //MessageBox.Show("Zakończyłem tworzenie dokumentu DL:"+nr_inw);
            //this.myParent.Show();
            //base.Close();
        }

        private void powrot_Click(object sender, EventArgs e)
        {
            //this.Skanowanie.Abort();
            Skaner.Przewij_Skanowanie();
            this.myParent.Show();
            this.Hide();

        }



        private void button1_Anuluj(object sender, EventArgs e)
        {

        }
        private void ZacznijSkanowanie()
        {
            StringBuilder login = new StringBuilder();
            Skaner.UstawTryb_String(login);
            Skaner.Zacznij_Skanowanie();
            this.Skanowanie = new Thread(() => this.CheckString(login));
            this.Skanowanie.IsBackground = true;
            this.Skanowanie.Start();
        }



        private void CheckString(StringBuilder cc)
        {
            EventHandler method = null;
            while (true)
            {
                if (cc.ToString() != "")
                {
                    if (method == null)
                    {
                        method = delegate
                        {
                            //this.vvv();
                            
                                this.dodawanie(cc.ToString());
                        };
                        //method = () => this.dodawanie(cc.ToString());
                    }
                    this.Invoke(method);
                    return;
                }
                Thread.Sleep(200);
            }
        }






        






        // 'External' - wyszukuje w bazie etykiete i wypełnia pola
        private void dodawanie(string ops)
        {

            Skaner.Przewij_Skanowanie();

            



            if (!BazaDanychExternal.SprawdzCzyIstniejePolaczenie())
            {
                MessageBox.Show("Brak połączenia z bazą danych , spróbuj ponownie.");
                this.ZacznijSkanowanie();
            }
            else
            {
                if (ops.Substring(0, 2) == "MP")
                {
                    Regex regex = new Regex("-");
                    string[] words = null;
                    words = regex.Split(ops.Substring(3, ops.Length - 3));

                    hala_comboBox2.SelectedIndex = hala_comboBox2.Items.IndexOf(words[0].ToString());
                    comboBox3.SelectedIndex = comboBox3.Items.IndexOf(words[1].ToString());
                    textBox1.Text = words[2].ToString();
                    comboBox4.SelectedIndex = comboBox4.Items.IndexOf(words[3].ToString());
                    this.ZacznijSkanowanie();
                    return;
                    //poziom_comboBox4.SelectedIndex = poziom_comboBox4.Items.IndexOf(words[3].ToString());
                }
                else
                {
                    AktualnyTextBox.Text = ops;

                    if (AktualnyTextBox == kod_textbox)
                    {
                        AktualnyTextBox.Text = "";
                        //MessageBox.Show("1");


                        if (kod_textbox.Text == "")
                        {
                            //MessageBox.Show("2");
                            ops = ops.TrimStart('0');

                            string zapytanie = "SELECT k.id,k.kod,k.kod_nazwa, k.ean, k.ilosc_w_opakowaniu, k.gln, k.ean_jednostki, k.ilosc_dni_przydatnosci,k.wymagana_partia, k.wymagana_data_waznosci, k.wymagana_dataprod FROM kody k WHERE k.system_id=\"" + Wlasciwosci.system_id_id + "\" and (TRIM(LEADING '0' FROM ean)=\"" + ops + "\" or TRIM(LEADING '0' FROM ean_jednostki)=\"" + ops + "\" or TRIM(LEADING '0' FROM kod)=\"" + ops + "\" or TRIM(LEADING '0' FROM ean_opakowanie_zbiorcze)=\"" + ops + "\") and k.active=1 ORDER BY k.id asc limit 1;"; 
                            object obj2 = BazaDanychExternal.Wyczytaj_Tabele(zapytanie);
                            DataTable tabela1 = (DataTable)obj2;

                            if (tabela1.Rows.Count == 0)
                            {
                                MessageBox.Show("Brak kodu w kartotece. Należy dodać lub pzypisać EAN do kodu. ");
                                return;
                            }
                            else
                            {
                                //MessageBox.Show("3");
                                if (tabela1.Rows.Count == 1)
                                {
                                    kod_textbox.Text = tabela1.Rows[0]["kod"].ToString();
                                    kod_nazwa.Text = tabela1.Rows[0]["kod_nazwa"].ToString();
                                    //MessageBox.Show("3");
                                }
                            }
                        }
                    }

                    if (AktualnyTextBox == etykieta_textbox)
                    {

                        
                        if (ops.Length > 24)
                        {


                            MessageBox.Show("Nieprawidłowa etykieta" + Environment.NewLine +
                                            "" + ops.Substring(0, 3));


                            this.ZacznijSkanowanie();
                            return;
                        }
                        else
                        {
                            //MessageBox.Show("A");
                            //ops = Convert.ToInt64(ops).ToString();
                            string zapytanie = "";
                            etykieta_id = "0";

                            if (ops.Substring(0, 2) == "DS")
                            {
                                label2.Text = ops;
                                zapytanie = "select i.kod,kod_nazwa,podkod,  cast(TRIM(TRAILING '.' FROM TRIM(TRAILING '0' from i.ilosc)) as char) as ilosc,m.hala,m.regal,m.miejsce,m.poziom,i.hala as hala_i,i.regal as regal_i,i.miejsce as miejsce_i,i.poziom as poziom_i,i.etykieta_id,i.paleta_id,i.nrsap from inwentaryzacja i left join miejsca m on m.id=i.miejscep left join kody k on k.kod=i.kod and i.system_id=k.system_id  where (paleta_id='" + ops.Replace("DS", "") + "') and inwentaryzacja_id=" + _inw[comboBox1.SelectedIndex] + " ORDER BY i.ts ASC limit 1";

                            }
                            else
                            {

                                zapytanie = "select i.kod,kod_nazwa,podkod,  cast(TRIM(TRAILING '.' FROM TRIM(TRAILING '0' from i.ilosc)) as char) as ilosc,m.hala,m.regal,m.miejsce,m.poziom,i.hala as hala_i,i.regal as regal_i,i.miejsce as miejsce_i,i.poziom as poziom_i,i.etykieta_id,i.paleta_id,i.nrsap from inwentaryzacja i left join miejsca m on m.id=i.miejscep left join kody k on k.kod=i.kod and i.system_id=k.system_id  where (etykieta_id='" + ops + "' or nrsap='" + ops + "' ) and inwentaryzacja_id=" + _inw[comboBox1.SelectedIndex] + " ORDER BY i.ts ASC limit 1";
                            }


                            /*
                            if (etykieta_textbox.Text != "")
                        {
                            DialogResult result = MessageBox.Show("Poprzednia etykieta nie została zatwierdzona. Czy chcesz zeskanować inną etykietę?",
                                "Ostrzeżenie",
                                MessageBoxButtons.YesNo,
                                MessageBoxIcon.Question,
                                MessageBoxDefaultButton.Button2);

                            if (result == DialogResult.No)
                            {
                                etykieta_textbox.Focus();
                                return;
                            }
                        }
                             * 
                             * */
                            

                            //MessageBox.Show("B");

                            object obj2 = BazaDanychExternal.Wyczytaj_Tabele(zapytanie);
                            if (obj2 == null)
                            {
                                this.ZacznijSkanowanie();
                            }
                            DataTable table = (DataTable)obj2;

                            //MessageBox.Show("C");

                            if (table.Rows.Count < 1)
                            {
                                //MessageBox.Show("D");
                                //BazaDanychExternal.DokonajUpdate("Insert into inwentaryzacja(data,opis,ilosc,ilosc_spisana,kod,inwentaryzacja_id,hala,regal,miejsce,poziom,pracownik,ts,podkod,skan,uwaga) values('" + _inw_data[comboBox1.SelectedIndex] + "','" + _inw_opis[comboBox1.SelectedIndex] + "',0,'0','10101','" + _inw[comboBox1.SelectedIndex] + "','" + _inw_hala[hala_comboBox2.SelectedIndex] + "','" + _inw_regal[comboBox3.SelectedIndex] + "','" + textBox1.Text + "','" + _inw_poziom[comboBox4.SelectedIndex] + "','" + Wlasciwosci.imie_nazwisko + "',now(),'" + podkod + "','" + ops + "','brak_w_inw');");
                                MessageBox.Show("Brak w tej inwentaryzacji etykiety: " + ops);
                                etykieta_textbox.Text = "";
                                //import_podpowiedz(ops);
                                

                                //MessageBox.Show("E");
                            }
                            else
                            {


                                etykieta_textbox.Text = table.Rows[0]["etykieta_id"].ToString();

                                if (table.Rows[0]["hala_i"].ToString() != "")
                                {
                                    //MessageBox.Show("F");
                                    //BazaDanychExternal.DokonajUpdate("Insert into inwentaryzacja(data,opis,ilosc,ilosc_spisana,kod,inwentaryzacja_id,hala,regal,miejsce,poziom,pracownik,ts,podkod,skan,uwaga) values('" + _inw_data[comboBox1.SelectedIndex] + "','" + _inw_opis[comboBox1.SelectedIndex] + "',0,'0','10101','" + _inw[comboBox1.SelectedIndex] + "','" + _inw_hala[hala_comboBox2.SelectedIndex] + "','" + _inw_regal[comboBox3.SelectedIndex] + "','" + textBox1.Text + "','" + _inw_poziom[comboBox4.SelectedIndex] + "','" + Wlasciwosci.imie_nazwisko + "',now(),'" + podkod + "','" + ops + "','byla_juz');");
                                    //MessageBox.Show("Był inwentaryzowana w " + table.Rows[0]["hala_i"].ToString() + " " + table.Rows[0]["regal_i"].ToString() + "-" + table.Rows[0]["miejsce_i"].ToString() + "-" + table.Rows[0]["poziom_i"].ToString());

                                    //MessageBox.Show("G");
                                    DialogResult result3 = MessageBox.Show("Był inwentaryzowany w" + table.Rows[0]["hala_i"].ToString() + " " + table.Rows[0]["regal_i"].ToString() + "-" + table.Rows[0]["miejsce_i"].ToString() + "-" + table.Rows[0]["poziom_i"].ToString() + ". Inwentaryzować jeszcze raz?",
                                    "Czy ponownie?",
                                    MessageBoxButtons.YesNo,
                                    MessageBoxIcon.Question,
                                    MessageBoxDefaultButton.Button2);
                                    if (result3 == DialogResult.Yes)
                                    {
                                        etykieta_id = table.Rows[0]["etykieta_id"].ToString();
                                        podkod = table.Rows[0]["podkod"].ToString();
                                        kod_textbox.Text = table.Rows[0]["kod"].ToString();
                                        kod_nazwa.Text = table.Rows[0]["kod_nazwa"].ToString();
                                        ilosc_textBox.Text = table.Rows[0]["ilosc"].ToString(); // table.Rows[0]["ilosc"].ToString()
                                        hala_comboBox2.SelectedIndex = hala_comboBox2.Items.IndexOf(table.Rows[0]["hala"].ToString());
                                        comboBox3.SelectedIndex = comboBox3.Items.IndexOf(table.Rows[0]["regal"].ToString());
                                        comboBox4.SelectedIndex = comboBox4.Items.IndexOf(table.Rows[0]["poziom"].ToString());
                                        label2.Text = "DS"+table.Rows[0]["paleta_id"].ToString();
                                        textBox1.Text = table.Rows[0]["miejsce"].ToString();
                                        nrsap.Text = table.Rows[0]["nrsap"].ToString();
                                        //etykieta_textbox.Text = ops;
                                        button1.Focus();
                                        kod_textbox.SelectionStart = kod_textbox.Text.Length;
                                        this.ZacznijSkanowanie();
                                        return;

                                    }
                                    else
                                    {
                                        //this.ZacznijSkanowanie();
                                        //return;
                                        etykieta_textbox.Focus();
                                        return; 
                                    }
                                }
                                else
                                {

                                    //MessageBox.Show("H");
                                    etykieta_id = table.Rows[0]["etykieta_id"].ToString();
                                    podkod = table.Rows[0]["podkod"].ToString();
                                    kod_textbox.Text = table.Rows[0]["kod"].ToString();
                                    kod_nazwa.Text = table.Rows[0]["kod_nazwa"].ToString();
                                    ilosc_textBox.Text = "" + table.Rows[0]["ilosc"].ToString();
                                    hala_comboBox2.SelectedIndex = hala_comboBox2.Items.IndexOf(table.Rows[0]["hala"].ToString());
                                    comboBox3.SelectedIndex = comboBox3.Items.IndexOf(table.Rows[0]["regal"].ToString());
                                    comboBox4.SelectedIndex = comboBox4.Items.IndexOf(table.Rows[0]["poziom"].ToString());
                                    textBox1.Text = table.Rows[0]["miejsce"].ToString();
                                    label2.Text = "DS" + table.Rows[0]["paleta_id"].ToString();
                                    nrsap.Text = table.Rows[0]["nrsap"].ToString();
                                    //etykieta_textbox.Text = ops;
                                    button1.Focus();
                                    kod_textbox.SelectionStart = kod_textbox.Text.Length;
                                    etykieta_textbox.Focus();
                                    return;
                                }


                            }
                        }
                    }




                }
            }
        }

        // 'External' po każdym dodaniu przelicza ile jest zrobionego
        

        private void import_podpowiedz(string ops)
        {


            string zapytanie = "";
            etykieta_id = "0";
            zapytanie = "SELECT i.kod, cast(TRIM(TRAILING '.' FROM TRIM(TRAILING '0' from i.ilosc)) as char) as ilosc FROM importSap i where ( nrsap='" + ops + "')  limit 1";

            //MessageBox.Show("B");

            object obj2 = BazaDanychExternal.Wyczytaj_Tabele(zapytanie);
            if (obj2 == null)
            {
                this.ZacznijSkanowanie();
            }
            DataTable table = (DataTable)obj2;

            //MessageBox.Show("C");

            if (table.Rows.Count < 1)
            {
                //MessageBox.Show("D");

                MessageBox.Show("Wprowadz ręcznie. Brak: " + ops);

                kod_textbox.Focus();
                kod_textbox.SelectionStart = kod_textbox.Text.Length;
                //MessageBox.Show("E");
            }
            else
            {
                //etykieta_id = table.Rows[0]["etykieta_id"].ToString();
                //podkod = table.Rows[0]["podkod"].ToString();
                kod_textbox.Text = table.Rows[0]["kod"].ToString();
                ilosc_textBox.Text = table.Rows[0]["ilosc"].ToString();
                //hala_comboBox2.SelectedIndex = hala_comboBox2.Items.IndexOf(table.Rows[0]["hala"].ToString());
                //comboBox3.SelectedIndex = comboBox3.Items.IndexOf(table.Rows[0]["regal"].ToString());
                //comboBox4.SelectedIndex = comboBox4.Items.IndexOf(table.Rows[0]["poziom"].ToString());
                //textBox1.Text = table.Rows[0]["miejsce"].ToString();
                etykieta_textbox.Text = ops;
                button1.Focus();
                kod_textbox.SelectionStart = kod_textbox.Text.Length;
                return;
            }

        }
































        private void czysc()
        {

            //skaner = "";
            //sap_gora_czesc2 = "";
            //kod = "";
            //podkod = "";
            kod_textbox.Text = "";

        }



        private void timer1_Tick(object sender, EventArgs e)
        {
            this.Close();
            myParent.Show();
        }



        #region fullscreen
        private void fullscreenmode()
        {
            this.Width = Screen.PrimaryScreen.Bounds.Width;
            this.Height = Screen.PrimaryScreen.Bounds.Height;
            this.Left = 0;
            this.Top = 0;
        }
        #endregion

        private void comboBox1_SelectedIndexChanged(object sender, EventArgs e)
        {
            //nr_inw = _inw[comboBox1.SelectedIndex];

        }

        private void button4_Click(object sender, EventArgs e)
        {
           
            //comboBox1.
            //this.Skanowanie.Abort();
            Skaner.Przewij_Skanowanie();

            //ZacznijSkanowanie();
            etykieta_textbox.Focus();


        }

        private void type_offline_CheckStateChanged(object sender, EventArgs e)
        {
            
        }

        private void button3_Click(object sender, EventArgs e)
        {
            kod_textbox.Text = "IT";
            kod_textbox.Focus();
            kod_textbox.SelectionStart = kod_textbox.Text.Length;
        }

        private void button5_Click(object sender, EventArgs e)
        {
            kod_textbox.Text = "PL";
            kod_textbox.Focus();
            kod_textbox.SelectionStart = kod_textbox.Text.Length;
        }

        private void GotFocus_TextBox(object sender, EventArgs e)
        {
            Skaner.Przewij_Skanowanie();
            TextBox Pole_Tekstowe = (TextBox)sender;
            AktualnyTextBox = Pole_Tekstowe;
            ZacznijSkanowanie();
        }

        private void LostFocus_TextBox(object sender, EventArgs e)
        {
            AktualnyTextBox = null;
            Skaner.Przewij_Skanowanie();

            TextBox Pole_Tekstowe = (TextBox)sender;
        }

        private void button2_Click(object sender, EventArgs e)
        {

        }







    }
}