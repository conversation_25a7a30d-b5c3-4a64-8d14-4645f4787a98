<?php

$dbname = 'artsana';

wydruk($_GET, "***********");

function wydruk($params, $adres_ip) {

    // lp,transport_number,delivery_number,customer_name,address,postal_code,city, box_number+0 as box_number
    $layout = "^XA
    ^FWR
    ^CF0,110
    ^FO680,50 ^FDNumer WZ: ".$params['wz']."^FS
    ^FO560,50 ^FDKod towaru: ".$params['kod']."^FS
    ^FO440,50 ^FDIle zgrz<PERSON>ek: ".$params['qt']."^FS
^BY2.5,5,150
^FO200,140
^BC,,N
^FD".$params['sscc']."
^FS
^FO80,150
^A0N,60,60
^FWR
^FD".$params['sscc']."
^FS
^XZ";
echo $layout;

    $file = fopen("/tmp/etykietsofidel.zbr", "w");
    fputs($file, $layout);
    fclose($file);
    //flush();
    sleep(1);

    $socket = socket_create(AF_INET, SOCK_STREAM, SOL_TCP);
    $result = socket_connect($socket, $adres_ip, "9100");
    socket_write($socket, $layout, strlen($layout));
    socket_close($socket);
}

?>