<?php

//include_once 'db_pdo.php';
include_once 'Db.class.php';
include_once 'funkcje.inc';
//error_reporting(E_ALL);
//ini_set('display_errors', 1);

$db = new Db();

if (empty($argv)) {
//$argv = array();
    //$argv[1] = $_GET['db'];
    $argv[2] = $_GET['akcja'];
}

//$baza_danych = $_GET['db'];
$akcja = $_GET['akcja'];
$system_id = $_GET['system_id'];

$skan = $_GET["skan"];

//http://25.56.91.22/wmsgg/public/skaner_api/delivery_skanowanie_etykiety.php?db=wmsgg&akcja=szukaj&system_id=6&paleta_id=547144


$komunikat = "OK";
if ($akcja == "szukaj") {

    if (substr($skan, 0, 2) == "DS") {
        $sql = 'SELECT e.id,TRIM(TRAILING "." FROM TRIM(TRAILING "0" from e.ilosc)) as ilosc, k.kod,k.kod_nazwa, e.kod_id , dc.nr_et,de.etykieta_id ,e.active,kg.nazwa as kod_grupa_nazwa                       
                        FROM etykiety e left join dlcollect dc on e.id=dc.nr_et left join kody k on e.kod_id=k.id
                            left join delivery_et de on de.etykieta_id=e.id  
                            left join kody_grupy kg on kg.id=k.kody_grupy_id
                        WHERE e.system_id=' . $system_id . ' and e.paleta_id=' . str_replace("DS", "", $skan) . '    ORDER BY e.active DESC, e.id DESC limit 1';
    } else {

        $sql = 'SELECT e.id,TRIM(TRAILING "." FROM TRIM(TRAILING "0" from e.ilosc)) as ilosc, k.kod,k.kod_nazwa, e.kod_id ,dc.nr_et,de.etykieta_id,e.active,kg.nazwa as kod_grupa_nazwa                       
                        FROM etykiety e left join dlcollect dc on e.id=dc.nr_et left join kody k on e.kod_id=k.id
                            left join delivery_et de on de.etykieta_id=e.id
                            left join kody_grupy kg on kg.id=k.kody_grupy_id
                        WHERE e.system_id=' . $system_id . ' and (e.id="' . $skan . '" or e.etykieta_klient="' . $skan . '" )    ORDER BY e.active DESC, e.id DESC   limit 1';
    }
    //echo $sql;
//    if (count($result) == 0) {
//        $komunikat = "$sql";
//        return show_komunikat_xml($komunikat);
//    }
    $result = $db->mGetResultAsXML($sql);

    if (count($result) == 0) {
        $komunikat = "Brak w systemie etykiety";
        return show_komunikat_xml($komunikat);
    }
    $result = $db->mGetResultAsXML($sql);

    if ($result[0]['active'] == "") {
        $komunikat = "Etykieta nie przyjęta";
        return show_komunikat_xml($komunikat);
    }
    if ($result[0]['active'] == "0") {
        $komunikat = "Etykieta NIE AKTYWNA";
        return show_komunikat_xml($komunikat);
    }
    if (!empty($result[0]['nr_et']) || !empty($result[0]['etykieta_id'])) {
        $komunikat = "Etykieta zarezerwowana na DL " . $result[0]['nr_et'] . ";" . $result[0]['etykieta_id'];
        return show_komunikat_xml($komunikat);
    }


    $result[0]['komunikat'] = $komunikat;
    return xml_from_indexed_array($result[0]);
}
?>