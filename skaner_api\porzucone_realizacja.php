<?php

include_once 'Db.class.php';
include_once 'funkcje.inc';
$db = new Db();



$komunikat = "OK";
$akcja = $_GET['akcja'];
$zadanie_dane_id = $_GET['zadanie_dane_id'];
$imie_nazwisko = plznaki($_GET['imie_nazwisko']);
$skan = $_GET["skan"];
$czy_koniec_zadania = "NIE";





//header('Content-type: text/xml');
/* echo '<?xml version="1.0" encoding="utf-8" ?><dane>'; */

if (empty($akcja) || empty($zadanie_dane_id) || empty($imie_nazwisko) || empty($skan)) {
    $komunikat = "Brak wszystkich parametrow wejsciwych. Przerywam operacje";
    //echo '<komunikat>', htmlentities($komunikat), '</komunikat></dane>';
    return show_komunikat_xml($komunikat);
}


if ($akcja == "realizacja_zadania") {

    $result = pobierz_zadanie_global($zadanie_dane_id, "", $db);
    $baza_danych = "";
    $zadanie_head_id = "";
    foreach ($result as $key => $value) {
        $baza_danych = $value["baza_danych"];
        $zadanie_head_id = $value["zadanie_head_id"];
    }

    $nowe_miejsce = 0;

    //$ile = mysql_num_rows($result);
    //echo "<br>" . $sql . "<br>";


    $wymagania_etykieta_skanowana = wymagania_etykieta_skanowana($skan);
    if (!empty($wymagania_etykieta_skanowana)) {
        $komunikat = $wymagania_etykieta_skanowana;
        return show_komunikat_xml($komunikat);
    }


    // sprawdz czy było coś zaczęte
    $sprawdzanie_czy_jest_odlozone = sprawdzanie_czy_jest_odlozone($zadanie_head_id, $db);


    $sql = 'SELECT id FROM ' . $baza_danych . '.pracownicy p WHERE p.imie_nazwisko="' . $imie_nazwisko . '" limit 1; ';
    $result2 = $db->mGetResultAsXML($sql);
    //echo "<br>" . $sql;
    foreach ($result2 as $index => $aRowp) {
        $pracownik_id = $aRowp['id'];
    }
    if (empty($pracownik_id)) {
        $komunikat = "Nie znaleziono pracownika: " . $imie_nazwisko . ". Przerywam operacje";
        return show_komunikat_xml($komunikat);
    }





    if (count($result) > 0) {
        $zadanie_dane_id = 0;
        foreach ($result as $index => $aRowZadanie) {
            $baza_danych = $aRowZadanie['baza_danych'];
            $zadanie_dane_id = $aRowZadanie['id'];

            if ($aRowZadanie['status'] != "8") {
                $komunikat = "Zadanie zostalo zrealizowane . Przerywam operacje";
                return show_komunikat_xml($komunikat);
            }














            // najpotrzebniejsze etykieta ta sama i ilosc realizowana
            //$skan = "MP-1-RMP-1-A";
            $arr = explode("-", $skan);




            if ($arr[0] == "MP") {



                // sprawdz czy było coś zaczęte
                if ($sprawdzanie_czy_jest_odlozone == "TAK") {
                    $komunikat = "Wymagane nosnik skompletowany";
                    return show_komunikat_xml($komunikat);
                }


                if ($aRowZadanie["nowe_hala"] != $arr[1] || $aRowZadanie["nowe_regal"] != $arr[2] || $aRowZadanie["nowe_miejsce"] != $arr[3]) {
                    $komunikat = "Wczytano \nH:" . $arr[1] . " " . " " . $arr[2] . "-" . $arr[3] .
                            " \nEtykieta moze byc skompletowana tylko na:\nH:" . $aRowZadanie["nowe_hala"] . " " . $aRowZadanie["nowe_regal"] . "-" . $aRowZadanie["nowe_miejsce"] . "";
                    return show_komunikat_xml($komunikat);
                }
                $nowe_miejsce = sprawdz_czy_istnieje_miejsce($arr[1], $arr[2], $arr[3], "A", $baza_danych, $db);
                if (empty($nowe_miejsce)) {
                    $komunikat = "Brak w bazie tego miejsca";
                    return show_komunikat_xml($komunikat);
                }

                $sprawdz2 = sprawdz_czy_wszystkie_zadanie_dokumentu_sa_wykonane($zadanie_head_id, $db);


                if ($sprawdz2 == "TAK") {
                    $czy_koniec_zadania = "TAK";
                    $sql = 'update zadania_head  set status_dokumentu=3 where id=' . $zadanie_head_id . ' and status_dokumentu!=3 limit 1';
                    $result5 = $db->mGetResultAsXML($sql);
                }


                // czy miejsce jest dodane w bazie danych
            }


            if (substr($arr[0], 0, 2) == "DS") {

                if ($sprawdzanie_czy_jest_odlozone == "NIE") {
                    $komunikat = "Wymagane miejsce kompletowane";
                    return show_komunikat_xml($komunikat);
                }
                if (str_replace("DS", "", $skan) == $aRowZadanie["paleta_id"]) {
                    $komunikat = "Ta paleta musi byc odlozona przy kompletowanych";
                    return show_komunikat_xml($komunikat);
                }




                $sql = 'select dl.nr_dl,hala,regal,miejsce,poziom
                            from ' . $baza_danych . '.etykiety e
                            left join ' . $baza_danych . '.miejsca m on e.miejscep=m.id
                            left join ' . $baza_danych . '.dlcollect dl  on dl.nr_et=e.id
                            where e.paleta_id=' . str_replace("DS", "", $skan) . " order by nr_dl desc limit 1 ";
                $result2 = $db->mGetResultAsXML($sql);
                //echo "<br>" . $sql;

                if (count($result2) == 0) {
                    $komunikat = "Nie rozpoznano etykiety";
                    return show_komunikat_xml($komunikat);
                }
                foreach ($result2 as $index => $aRowEtWms) {



                    //select m.id from wmsgg.miejsca m where m.hala='3' and regal='RMP' and miejsce='3' and poziom='A' limit 1


                    if ($aRowEtWms['nr_dl'] != $aRowZadanie["doc_id"]) {
                        $komunikat = "Etykieta jest z innej DL bo jest z " . $aRowEtWms['nr_dl'] . ". Przerywam operacje";
                        //echo "<br>" . $komunikat;
                        //return;
                        return show_komunikat_xml($komunikat);
                    }
                }
            } else if (!($arr[0] == "MP" || $arr[0] == "DS")) {
                // gdy etykieta WMS

                $sql = 'select dl.nr_dl,hala,regal,miejsce,poziom
                            from ' . $baza_danych . '.etykiety e
                            left join ' . $baza_danych . '.miejsca m on e.miejscep=m.id
                            left join ' . $baza_danych . '.dlcollect dl  on dl.nr_et=e.id
                            where (e.id=' . $skan . ' or e.etykieta_klient="' . $skan . '") order by nr_dl desc limit 1 ';
                $result2 = $db->mGetResultAsXML($sql);
                //echo "<br>" . $sql;
                if (count($result2) == 0) {
                    $komunikat = "Nie rozpoznano etykiety";
                    return show_komunikat_xml($komunikat);
                }
                foreach ($result2 as $index => $aRowEtWms) {
                    //while ($aRowEtWms = mysql_fetch_assoc($result2)) {
                    //select m.id from wmsgg.miejsca m where m.hala='3' and regal='RMP' and miejsce='3' and poziom='A' limit 1


                    if ($aRowEtWms['nr_dl'] != $aRowZadanie["doc_id"]) {
                        $komunikat = "Etykieta jest z innej DL bo jest z " . $aRowEtWms['nr_dl'] . ". Przerywam operacje";
                        //echo "<br>" . $komunikat;
                        //return;
                        return show_komunikat_xml($komunikat);
                    }
                }
            }

            if ($komunikat == "OK") {

                $sql = 'SELECT e.id,e.miejscep,e.system_id FROM ' . $baza_danych . '.etykiety e WHERE e.active=1 AND e.paleta_id=' . $aRowZadanie['paleta_id'];
                $result7 = $db->mGetResultAsXML($sql);
//                echo "<pre>";
//                print_r($result7);
//                echo "</pre>";
//                return false;
                foreach ($result7 as $index => $aRowEtykietaWMS) {

                    $sql = "insert into $baza_danych.zmianym 
                        (typ, doc_nr, pracownik_id, data, etykieta, system_id, stare_m, nowe_m, doc_internal, stat, tszm)
                        values('ZM',0, '$pracownik_id',CURDATE(), " . $aRowEtykietaWMS['id'] . "," . $aRowEtykietaWMS['system_id'] . "," . $aRowZadanie['stare_m'] . ", " . $aRowZadanie['nowe_m'] . ", 'Z', 1, NOW())";
                    //echo "<br>" . $sql;
                    $result2 = $db->mGetResultAsXML($sql);

                    $sql = "update $baza_danych.etykiety set miejscep=" . $aRowZadanie['nowe_m'] . " where id=" . $aRowEtykietaWMS['id'] . " limit 1";

                    //echo "<br>" . $sql;
                    $result2 = $db->mGetResultAsXML($sql);

                    //zamykanie statusu dokumentu
                }




                $sql = ' update  zadania_dane z set z.realizacja_pracownik_id=' . $pracownik_id . ', z.stop=NOW(),status=3 WHERE z.id=' . $zadanie_dane_id;
                //echo "<br>" . $sql;
                $result = $db->mGetResultAsXML($sql);
            }
            $komunikat.=$db->errors;
        }




        //pozpoczynanie_pelnych po skompletowaniu wszystkich
        $sprawdz = sprawdz_czy_wszystkie_kompletowane_sa_skompletowane($zadanie_head_id, $db);
        if ($sprawdz == "TAK") {
            zmien_status_rozpocznij_pelne_po_kompletacji($zadanie_head_id, $db);
        }

        $sprawdz2 = sprawdz_czy_wszystkie_zadanie_dokumentu_sa_wykonane($zadanie_head_id, $db);
        if ($sprawdz2 == "TAK") {
            $czy_koniec_zadania = "TAK";
            $sql = 'update zadania_head  set status_dokumentu=3 where id=' . $zadanie_head_id . ' and status_dokumentu!=3 limit 1';
            $result5 = $db->mGetResultAsXML($sql);
        }








//$komunikat=$sql;
    } else {
        $komunikat = "Brak zadan";
    }
} else {
    $komunikat = "Brak akcji realizacji!";
}
//show_komunikat_xml($komunikat);
xml_from_indexed_array(array(
    'komunikat' => $komunikat,
    'czy_koniec_zadania' => $czy_koniec_zadania)
);

function sprawdz_czy_wszystkie_zadanie_dokumentu_sa_wykonane($zadanie_head_id, $db) {
    $wynik = "NIE";
    $sql_ins_zad = "SELECT sum(if(zs.finish=1,1,0)) as gotowe, sum(1) as wszystkie FROM zadania_dane z left join zadania_statusy zs on zs.id=z.status
WHERE  zadanie_head_id= " . $zadanie_head_id;
    //echo "<br>" . $sql_ins_zad;
    $result_zadanie4 = $db->mGetResult($sql_ins_zad);
    if (!empty($result_zadanie4)) {
        if ($result_zadanie4[0]['gotowe'] == $result_zadanie4[0]['wszystkie']) {
            $wynik = "TAK";
        }
    }
    //print_r($result_zadanie4);
    return $wynik;
}

function wymagania_etykieta_skanowana($skan) {
    $komunikat = "";

    if (!(substr($skan, 0, 2) == "MP" || substr($skan, 0, 2) == "DS")) {
        $komunikat = "Dozwolone: paleta/miejsce kompletowane";
        return $komunikat;
    }

//    $sprawdzanie_czy_jest_odlozone = sprawdzanie_czy_jest_odlozone($zadanie_head_id, $db);
//    if ($sprawdzanie_czy_jest_odlozone == "NIE" && substr($skan, 0, 2) != "MP") {
//        $komunikat = "Wymagane miejsce kompletowane";
//    }
//    if ($sprawdzanie_czy_jest_odlozone == "TAK" && substr($skan, 0, 2) != "DS") {
//        $komunikat = "Wymagane nosnik skompletowany";
//    }
    return $komunikat;
}

function sprawdzanie_czy_jest_odlozone($zadanie_head_id, $db) {
    $wynik = "NIE";
    $sql = "SELECT 1 as ile FROM zadania_dane z where status=3 and zadanie_head_id=" . $zadanie_head_id;
    $result_zadanie4 = $db->mGetResult($sql);
    if (!empty($result_zadanie4)) {
        $wynik = "TAK";
    }
    return $wynik;
}

function sprawdz_czy_wszystkie_zadania_zrobione($zadanie_head_id, $db) {
    $wynik = "NIE";
    $sql_ins_zad = "SELECT sum(if(z.stop is not null,1,0)) as kompletacja, sum(1) as wszystkie FROM zadania_dane z WHERE  zadanie_head_id=" . $zadanie_head_id;
    //echo "<br>" . $sql_ins_zad;
    $result_zadanie4 = $db->mGetResult($sql_ins_zad);
    if (!empty($result_zadanie4)) {
        if ($result_zadanie4[0]['kompletacja'] == $result_zadanie4[0]['wszystkie']) {
            $wynik = "TAK";
        }
    }
    //print_r($result_zadanie4);
//    $wynik = empty($result_zadanie4) ? 0 : count($result_zadanie4[0]);
    return $wynik;
}

function zmien_status_rozpocznij_pelne_po_kompletacji($zadanie_head_id, $db) {

    $sql_ins_zad = "update zadania_dane z set status=1
where zadanie_head_id=$zadanie_head_id
and z.status=6 and kompletacja=0 ";
    //echo "<br>" . $sql_ins_zad;
    $result_zadanie4 = $db->mGetResult($sql_ins_zad);
}

function sprawdz_czy_wszystkie_kompletowane_sa_skompletowane($zadanie_head_id, $db) {
    $wynik = "NIE";
    $sql_ins_zad = "SELECT sum(if(z.status=3 or z.status=7,1,0)) as kompletacja, sum(1) as wszystkie FROM zadania_dane z WHERE kompletacja=1 and zadanie_head_id=" . $zadanie_head_id;
    //echo "<br>" . $sql_ins_zad;
    $result_zadanie4 = $db->mGetResult($sql_ins_zad);
    if (!empty($result_zadanie4)) {
        if ($result_zadanie4[0]['kompletacja'] == $result_zadanie4[0]['wszystkie']) {
            $wynik = "TAK";
        }
    }
    //print_r($result_zadanie4);
//    $wynik = empty($result_zadanie4) ? 0 : count($result_zadanie4[0]);
    return $wynik;
}

//echo '<komunikat>', $komunikat, '</komunikat></dane>'; //htmlentities(    mb_convert_encoding($komunikat, "UTF-8","ISO-8859-2")
//
//echo $komunikat;
//echo '<ilosc_pozostala>', htmlentities($ilosc_pozostala), '</ilosc_pozostala>';
//echo '<nowa_etykieta>', htmlentities($nowa_etykieta), '</nowa_etykieta>';
//echo '<kod>', htmlentities($kod), '</kod>';
//echo '</dane>';
?>