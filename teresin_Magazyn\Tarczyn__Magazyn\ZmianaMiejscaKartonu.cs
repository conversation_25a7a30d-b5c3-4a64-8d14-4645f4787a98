﻿using System;
using System.Linq;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using System.Threading;

namespace Tarczyn__Magazyn
{



    public partial class ZmianaMiejscaKartonu : Form
    {
        ActionMenu myParent = null;
        int licznik = 0;

        public ZmianaMiejscaKartonu(ActionMenu c)
        {

            if (Wlasciwosci.GNG != "")
            {
                BazaDanychExternal.ChangeHost(Wlasciwosci.GNG, Wlasciwosci.Varhost, Wlasciwosci.VarLoginMYSQL, Wlasciwosci.VarHasloMYSQL);
            }
            FullScreenMode.OknoOFF(this);
            InitializeComponent();
            myParent = c;

            //BazaDanychExternal.Inicjalizacja(Wlasciwosci.Varbaza, Wlasciwosci.Varhost, Wlasciwosci.VarLoginMYSQL, Wlasciwosci.VarHasloMYSQL, this);  
        }


        List<string> Etykiety = new List<string>();
        Thread Skanowanie = null;
        int trybskanowania = 0;

        private void ZacznijSkanowanie()
        {
            StringBuilder login = new StringBuilder();
            Skaner.UstawTryb_String(login);
            Skaner.Zacznij_Skanowanie();
            Skanowanie = new Thread(() => CheckString(login));
            Skanowanie.IsBackground = true;
            Skanowanie.Start();
        }
        private void CheckString(StringBuilder cc)
        {
            while (true)
            {
                if (cc.ToString() != "")
                {
                    this.Invoke(new EventHandler(delegate
                    {

                        dodawanie(cc.ToString());
                    }));
                    return;
                }
                Thread.Sleep(200);
            }
        }
        private void Zakoncz_Skanowanie()
        {
            try
            {
                Skanowanie.Abort();
                Skaner.Przewij_Skanowanie();
            }
            catch
            {
            }
        }

        private void Exit_Click_1(object sender, EventArgs e)
        {
            Zakoncz_Skanowanie();
            myParent.Show();
            this.Close();
        }


        private void button2_Click(object sender, EventArgs e)
        {


        }


        private void dodawanie(string gg)
        {

            typsex.Text = "";
            

            Zakoncz_Skanowanie();
            if (BazaDanychExternal.SprawdzCzyIstniejePolaczenie() == false)
            {
                MessageBox.Show("Brak połączenia z bazą danych , spróbuj ponownie.");
                ZacznijSkanowanie();
                return;
            }

            if (gg.Substring(0, 2) == "DS")
            {
                textBox3.Text = gg;
                licznik=0;
                label3.Text=licznik.ToString();
            }
            else
            {
                //MessageBox.Show("1");
                //MessageBox.Show("select wartosc4 from kole    ktor where wartosc1='" + gg + "' order by id desc LIMIT 1");
                object kk = BazaDanychExternal.Wyczytaj_Tabele("select id from etykiety where etykieta_klient='"+gg+"' and active=1 ");
                if (kk == null)
                {
                    typsex.Text = "Ten karton nie jest przyjety";
                    ZacznijSkanowanie();
                    return;
                }
                //MessageBox.Show("2");
                 
                
                DataTable wynik = (DataTable)kk;
                if(wynik.Rows.Count==0)
                {
                    typsex.Text = "Ten karton nie jest przyjety";
                    ZacznijSkanowanie();
                    return;
                }
                //MessageBox.Show("3");

                //MessageBox.Show("update etykiety set paleta_id='" + textBox3.Text.Replace("DS", "") + "' where id=" + wynik.Rows[0][0].ToString());
                BazaDanychExternal.DokonajUpdate("update etykiety set paleta_id='" + textBox3.Text.Replace("DS", "") + "' where id=" + wynik.Rows[0][0].ToString());
                //BazaDanychExternal.DokonajUpdate("update etykiety e, (SELECT ee.miejscep FROM etykiety ee WHERE ee.paleta_id='" + textBox3.Text.Replace("DS", "") + "' group by ee.miejscep order by count(ee.miejscep) desc limit 1) as bb set e.paleta_id='" + textBox3.Text.Replace("DS", "") + "', e.miejscep=bb.miejscep where e.id=" + wynik.Rows[0][0].ToString());
                typsex.Text = "Zmieniłem karton:" + gg + " na " + textBox3.Text;
                licznik++;
                label3.Text = licznik.ToString();

            
            }
            
            ZacznijSkanowanie();
            //textBox3.Focus();

        }


        private void Zakoncz_Click(object sender, EventArgs e)
        {

        }

        private void WczytywanieNaLinie_Load_1(object sender, EventArgs e)
        {
            //ZacznijSkanowanie();
        }

        private void button2_Click_1(object sender, EventArgs e)
        {

        }

        private void button1_Click(object sender, EventArgs e)
        {
            dodawanie(textBox1.Text);
            textBox1.Text = "";
        }

        private void button2_Click_2(object sender, EventArgs e)
        {

            /*8
            string komand = "select id from list_control where id='" + textBox2.Text + "'";
            object lista = BazaDanychExternal.Wyczytaj_Jedna_Wartosc(komand);
            if (lista == null)
            {
                MessageBox.Show("Nie ma takiej listy kontrolnej w systemie.");
                return;
            }

            string komand1 = "select id from listcontrol_palety where paleta_id='" + textBox3.Text.Replace("DS", "") + "' and listcontrol_id=" + textBox2.Text;
            object lista1 = BazaDanychExternal.Wyczytaj_Jedna_Wartosc(komand1);
            if (lista1 == null)
            {
                MessageBox.Show("Nie ma takiej palety w systemie lub nie należy ona do tej listy kontrolnej.");
                textBox3.Focus();
                return;
            }

            ZacznijSkanowanie();
            textBox1.Visible = true;
            label1.Visible = true;
            button1.Visible = true;
            button2.Enabled = false;
             */
        }

        private void textBox2_GotFocus(object sender, EventArgs e)
        {
            ZacznijSkanowanie();
            trybskanowania = 1;
        }

        private void textBox2_LostFocus(object sender, EventArgs e)
        {
            Zakoncz_Skanowanie();
            trybskanowania = 0;
        }





    }
}