# Mapowanie Funkcjonalności Desktop na Architekturę MAUI

## Przegląd

Dokument opisuje mapowanie funkcjonalności z aplikacji desktop Windows CE na nowoczesną aplikację MAUI, zachowując logikę biznesową przy jednoczesnej modernizacji interfejsu użytkownika.

## Architektura Aplikacji MAUI

### Struktura Projektu

```
WMS.MAUI/
├── Platforms/
│   ├── Android/
│   ├── iOS/
│   └── Windows/
├── Models/
│   ├── Inwentaryzacja/
│   ├── Etykiety/
│   └── Lokalizacje/
├── ViewModels/
│   ├── InwentaryzacjaViewModel.cs
│   ├── SkanowanieViewModel.cs
│   └── BaseViewModel.cs
├── Views/
│   ├── InwentaryzacjaPage.xaml
│   ├── SkanowaniePage.xaml
│   └── SettingsPage.xaml
├── Services/
│   ├── ApiService.cs
│   ├── DatabaseService.cs
│   ├── ScannerService.cs
│   └── SyncService.cs
└── Resources/
    ├── Styles/
    └── Images/
```

## Mapowanie Klas Desktop → MAUI

### 1. <PERSON><PERSON><PERSON>

| Desktop Class | MAUI Equivalent | Opis |
|---------------|-----------------|------|
| `Inwentaryzacja.cs` | `InwentaryzacjaOgolnaViewModel` | Główna inwentaryzacja |
| `InwentaryzacjaProd.cs` | `InwentaryzacjaProduktowaViewModel` | Inwentaryzacja produktowa |
| `InwentaryzacjaGG.cs` | `InwentaryzacjaGGViewModel` | Inwentaryzacja GG |
| `InwentaryzacjaMiejsca.cs` | `InwentaryzacjaMiejscViewModel` | Inwentaryzacja miejsc |

### 2. Klasy Pomocnicze

| Desktop Class | MAUI Equivalent | Opis |
|---------------|-----------------|------|
| `BazaDanychExternal.cs` | `DatabaseService.cs` | Obsługa bazy danych |
| `WebService.cs` | `ApiService.cs` | Komunikacja HTTP/API |
| `Skaner.cs` | `ScannerService.cs` | Obsługa skanera |
| `Etykieta.cs` | `EtykietaModel.cs` | Model etykiety |
| `Wlasciwosci.cs` | `AppSettings.cs` | Ustawienia aplikacji |

## Modele Danych MAUI

### InwentaryzacjaModel.cs

```csharp
public class InwentaryzacjaModel
{
    public int Id { get; set; }
    public DateTime Data { get; set; }
    public string Opis { get; set; }
    public int InwentaryzacjaId { get; set; }
    public string NrWspolny { get; set; }
    public string Proba { get; set; }
    public bool Active { get; set; }
    public InwentaryzacjaTyp Typ { get; set; }
    
    // Stan inwentaryzacji
    public int IloscZliczona { get; set; }
    public int StanCalkowity { get; set; }
    public double ProcentWykonania => StanCalkowity > 0 ? 
        (double)IloscZliczona / StanCalkowity * 100 : 0;
}

public enum InwentaryzacjaTyp
{
    Ogolna,
    Produktowa,
    GG,
    Miejsca
}
```

### EtykietaModel.cs

```csharp
public class EtykietaModel
{
    public int Id { get; set; }
    public string EtykietaKlient { get; set; }
    public string Kod { get; set; }
    public string KodNazwa { get; set; }
    public decimal IloscTeoretyczna { get; set; }
    public decimal? IloscSpisana { get; set; }
    public LokalizacjaModel Lokalizacja { get; set; }
    public LokalizacjaModel LokalizacjaInwentaryzacja { get; set; }
    public int? PaletaId { get; set; }
    public string NrSap { get; set; }
    public DateTime OstatniaAktualizacja { get; set; }
    public string Pracownik { get; set; }
    public string Status { get; set; }
    
    // Dodatkowe pola dla GS1
    public string Lot { get; set; }
    public DateTime? DataProdukcji { get; set; }
    public DateTime? DataWaznosci { get; set; }
    public string SSCC { get; set; }
    public string GTIN { get; set; }
}
```

### LokalizacjaModel.cs

```csharp
public class LokalizacjaModel
{
    public int Id { get; set; }
    public string Hala { get; set; }
    public string Regal { get; set; }
    public string Miejsce { get; set; }
    public string Poziom { get; set; }
    
    public string PelnyAdres => $"{Hala}-{Regal}-{Miejsce}-{Poziom}";
    
    public bool IsValid => !string.IsNullOrEmpty(Hala) && 
                          !string.IsNullOrEmpty(Regal) && 
                          !string.IsNullOrEmpty(Miejsce) && 
                          !string.IsNullOrEmpty(Poziom);
}
```

## ViewModels MAUI

### BaseInwentaryzacjaViewModel.cs

```csharp
public abstract class BaseInwentaryzacjaViewModel : BaseViewModel
{
    protected readonly IApiService _apiService;
    protected readonly IScannerService _scannerService;
    protected readonly IDialogService _dialogService;
    
    // Wspólne właściwości
    public ObservableCollection<InwentaryzacjaModel> AktywneInwentaryzacje { get; set; }
    public InwentaryzacjaModel WybranaInwentaryzacja { get; set; }
    public ObservableCollection<string> Hale { get; set; }
    public ObservableCollection<string> Regaly { get; set; }
    public ObservableCollection<string> Poziomy { get; set; }
    public ObservableCollection<string> Statusy { get; set; }
    
    // Aktualne dane
    public EtykietaModel AktualnaEtykieta { get; set; }
    public string ZeskanowanyKod { get; set; }
    public decimal IloscSpisana { get; set; }
    public LokalizacjaModel WybranaLokalizacja { get; set; }
    
    // Komendy
    public ICommand WyszukajInwentaryzacjeCommand { get; set; }
    public ICommand WybierzInwentaryzacjeCommand { get; set; }
    public ICommand SkanujKodCommand { get; set; }
    public ICommand ZapiszPozycjeCommand { get; set; }
    public ICommand AnulujCommand { get; set; }
    
    // Abstrakcyjne metody do implementacji w klasach pochodnych
    protected abstract Task<bool> WalidujDaneAsync();
    protected abstract Task<bool> ZapiszPozycjeAsync();
    protected abstract string GetTypInwentaryzacji();
}
```

### InwentaryzacjaProduktowaViewModel.cs

```csharp
public class InwentaryzacjaProduktowaViewModel : BaseInwentaryzacjaViewModel
{
    private bool _trybOffline;
    
    public bool TrybOffline
    {
        get => _trybOffline;
        set => SetProperty(ref _trybOffline, value);
    }
    
    protected override string GetTypInwentaryzacji() => "produktowa";
    
    protected override async Task<bool> WalidujDaneAsync()
    {
        // Walidacja specyficzna dla inwentaryzacji produktowej
        if (string.IsNullOrEmpty(AktualnaEtykieta?.Kod))
        {
            await _dialogService.ShowAlertAsync("Błąd", "Pole kod jest wymagane");
            return false;
        }
        
        if (IloscSpisana <= 0)
        {
            await _dialogService.ShowAlertAsync("Błąd", "Ilość musi być większa od zera");
            return false;
        }
        
        return true;
    }
    
    protected override async Task<bool> ZapiszPozycjeAsync()
    {
        try
        {
            if (TrybOffline)
            {
                return await ZapiszOfflineAsync();
            }
            else
            {
                return await ZapiszOnlineAsync();
            }
        }
        catch (Exception ex)
        {
            await _dialogService.ShowAlertAsync("Błąd", $"Nie udało się zapisać: {ex.Message}");
            return false;
        }
    }
    
    private async Task<bool> ZapiszOfflineAsync()
    {
        // Implementacja zapisu offline do lokalnej bazy SQLite
        var offlineData = new OfflineInwentaryzacjaModel
        {
            InwentaryzacjaId = WybranaInwentaryzacja.InwentaryzacjaId,
            EtykietaId = AktualnaEtykieta?.Id ?? 0,
            Kod = AktualnaEtykieta?.Kod,
            IloscSpisana = IloscSpisana,
            Lokalizacja = WybranaLokalizacja,
            TimestampLocal = DateTime.Now,
            Zsynchronizowane = false
        };
        
        await _databaseService.SaveOfflineDataAsync(offlineData);
        return true;
    }
    
    private async Task<bool> ZapiszOnlineAsync()
    {
        var request = new ZapiszInwentaryzacjeRequest
        {
            Akcja = "zapisz",
            InwentaryzacjaId = WybranaInwentaryzacja.InwentaryzacjaId,
            EtykietaId = AktualnaEtykieta?.Id ?? 0,
            Kod = AktualnaEtykieta?.Kod,
            IloscSpisana = IloscSpisana,
            Hala = WybranaLokalizacja.Hala,
            Regal = WybranaLokalizacja.Regal,
            Miejsce = WybranaLokalizacja.Miejsce,
            Poziom = WybranaLokalizacja.Poziom,
            ImieNazwisko = AppSettings.Current.ImieNazwisko,
            Wozek = AppSettings.Current.Wozek,
            SystemId = AppSettings.Current.SystemId,
            EtykietaScan = ZeskanowanyKod
        };
        
        var response = await _apiService.ZapiszInwentaryzacjeAsync(request);
        
        if (response.Status == "success")
        {
            // Aktualizacja stanu inwentaryzacji
            WybranaInwentaryzacja.IloscZliczona = response.StanInwentaryzacji.IloscZliczona;
            WybranaInwentaryzacja.StanCalkowity = response.StanInwentaryzacji.StanCalkowity;
            
            await _dialogService.ShowAlertAsync("Sukces", "Pozycja została zapisana");
            return true;
        }
        else
        {
            await _dialogService.ShowAlertAsync("Błąd", response.Komunikat);
            return false;
        }
    }
}
```

## Services MAUI

### ApiService.cs

```csharp
public interface IApiService
{
    Task<List<InwentaryzacjaModel>> GetAktywneInwentaryzacjeAsync(string typInwentaryzacji);
    Task<EtykietaModel> SzukajEtykietyAsync(int inwentaryzacjaId, string kodScan);
    Task<ZapiszInwentaryzacjeResponse> ZapiszInwentaryzacjeAsync(ZapiszInwentaryzacjeRequest request);
    Task<StanInwentaryzacjiModel> GetStanInwentaryzacjiAsync(int inwentaryzacjaId);
    Task<List<LokalizacjaModel>> GetLokalizacjeAsync(string typ, string filtr = null);
    Task<List<KodModel>> SzukajKodowAsync(string kodSzukany, int limit = 10);
    Task<SyncResponse> SynchronizujOfflineAsync(List<OfflineInwentaryzacjaModel> daneOffline);
}

public class ApiService : IApiService
{
    private readonly HttpClient _httpClient;
    private readonly ILogger<ApiService> _logger;
    
    public ApiService(HttpClient httpClient, ILogger<ApiService> logger)
    {
        _httpClient = httpClient;
        _logger = logger;
    }
    
    public async Task<List<InwentaryzacjaModel>> GetAktywneInwentaryzacjeAsync(string typInwentaryzacji)
    {
        try
        {
            var url = $"inwentaryzacja_lista.php?system_id={AppSettings.Current.SystemId}&typ_inwentaryzacji={typInwentaryzacji}";
            var response = await _httpClient.GetStringAsync(url);
            
            var xmlDoc = XDocument.Parse(response);
            var inwentaryzacje = new List<InwentaryzacjaModel>();
            
            foreach (var element in xmlDoc.Descendants("inwentaryzacja"))
            {
                inwentaryzacje.Add(new InwentaryzacjaModel
                {
                    InwentaryzacjaId = int.Parse(element.Element("id")?.Value ?? "0"),
                    Data = DateTime.Parse(element.Element("data")?.Value ?? DateTime.Now.ToString()),
                    Opis = element.Element("opis")?.Value ?? "",
                    NrWspolny = element.Element("nr_wspolny")?.Value,
                    Proba = element.Element("proba")?.Value,
                    Typ = ParseTypInwentaryzacji(typInwentaryzacji)
                });
            }
            
            return inwentaryzacje;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Błąd podczas pobierania inwentaryzacji");
            throw;
        }
    }
    
    // Pozostałe implementacje metod...
}
```

### ScannerService.cs

```csharp
public interface IScannerService
{
    Task<bool> IsAvailableAsync();
    Task<string> ScanAsync();
    Task StartContinuousScanAsync(Action<string> onScanResult);
    Task StopContinuousScanAsync();
    bool IsScanningActive { get; }
}

public class ScannerService : IScannerService
{
    private bool _isScanningActive;
    private CancellationTokenSource _scanningCancellationToken;
    
    public bool IsScanningActive => _isScanningActive;
    
    public async Task<bool> IsAvailableAsync()
    {
        // Sprawdzenie dostępności skanera na danej platformie
#if ANDROID
        return await AndroidScannerHelper.IsAvailableAsync();
#elif IOS
        return await iOSScannerHelper.IsAvailableAsync();
#else
        return await Task.FromResult(false);
#endif
    }
    
    public async Task<string> ScanAsync()
    {
        // Implementacja skanowania jednorazowego
#if ANDROID
        return await AndroidScannerHelper.ScanAsync();
#elif IOS
        return await iOSScannerHelper.ScanAsync();
#else
        return await Task.FromResult(string.Empty);
#endif
    }
    
    public async Task StartContinuousScanAsync(Action<string> onScanResult)
    {
        if (_isScanningActive) return;
        
        _isScanningActive = true;
        _scanningCancellationToken = new CancellationTokenSource();
        
        try
        {
            while (!_scanningCancellationToken.Token.IsCancellationRequested)
            {
                var result = await ScanAsync();
                if (!string.IsNullOrEmpty(result))
                {
                    onScanResult?.Invoke(result);
                }
                
                await Task.Delay(200, _scanningCancellationToken.Token);
            }
        }
        catch (OperationCanceledException)
        {
            // Oczekiwane przy zatrzymaniu skanowania
        }
        finally
        {
            _isScanningActive = false;
        }
    }
    
    public async Task StopContinuousScanAsync()
    {
        _scanningCancellationToken?.Cancel();
        _isScanningActive = false;
        await Task.CompletedTask;
    }
}
```

### DatabaseService.cs (SQLite dla trybu offline)

```csharp
public interface IDatabaseService
{
    Task InitializeAsync();
    Task<List<OfflineInwentaryzacjaModel>> GetNiesynchronizowaneDaneAsync();
    Task SaveOfflineDataAsync(OfflineInwentaryzacjaModel data);
    Task MarkAsSynchronizedAsync(int id);
    Task ClearSynchronizedDataAsync();
}

public class DatabaseService : IDatabaseService
{
    private SQLiteAsyncConnection _database;
    
    public async Task InitializeAsync()
    {
        if (_database != null) return;
        
        var databasePath = Path.Combine(FileSystem.AppDataDirectory, "WMS.db3");
        _database = new SQLiteAsyncConnection(databasePath);
        
        await _database.CreateTableAsync<OfflineInwentaryzacjaModel>();
        await _database.CreateTableAsync<CachedInwentaryzacjaModel>();
        await _database.CreateTableAsync<CachedLokalizacjaModel>();
    }
    
    public async Task<List<OfflineInwentaryzacjaModel>> GetNiesynchronizowaneDaneAsync()
    {
        await InitializeAsync();
        return await _database.Table<OfflineInwentaryzacjaModel>()
            .Where(x => !x.Zsynchronizowane)
            .ToListAsync();
    }
    
    public async Task SaveOfflineDataAsync(OfflineInwentaryzacjaModel data)
    {
        await InitializeAsync();
        await _database.InsertAsync(data);
    }
    
    // Pozostałe implementacje...
}
```

## Interfejs Użytkownika MAUI

### InwentaryzacjaPage.xaml

```xml
<?xml version="1.0" encoding="utf-8" ?>
<ContentPage x:Class="WMS.MAUI.Views.InwentaryzacjaPage"
             xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
             xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
             Title="Inwentaryzacja">
    
    <ScrollView>
        <StackLayout Padding="20">
            
            <!-- Wybór inwentaryzacji -->
            <Frame BackgroundColor="LightBlue" Padding="10" Margin="0,0,0,10">
                <StackLayout>
                    <Label Text="Wybór Inwentaryzacji" FontSize="18" FontAttributes="Bold"/>
                    <Picker x:Name="InwentaryzacjaPicker" 
                            ItemsSource="{Binding AktywneInwentaryzacje}"
                            ItemDisplayBinding="{Binding DisplayText}"
                            SelectedItem="{Binding WybranaInwentaryzacja}"/>
                    <Label Text="{Binding WybranaInwentaryzacja.ProcentWykonania, StringFormat='Postęp: {0:F1}%'}"
                           FontSize="14"/>
                </StackLayout>
            </Frame>
            
            <!-- Skanowanie -->
            <Frame BackgroundColor="LightGreen" Padding="10" Margin="0,0,0,10">
                <StackLayout>
                    <Label Text="Skanowanie" FontSize="18" FontAttributes="Bold"/>
                    <Entry x:Name="KodEntry" 
                           Placeholder="Zeskanuj lub wprowadź kod"
                           Text="{Binding ZeskanowanyKod}"/>
                    <Button Text="Skanuj" 
                            Command="{Binding SkanujKodCommand}"
                            BackgroundColor="Green" 
                            TextColor="White"/>
                </StackLayout>
            </Frame>
            
            <!-- Informacje o etykiecie -->
            <Frame BackgroundColor="LightYellow" Padding="10" Margin="0,0,0,10"
                   IsVisible="{Binding AktualnaEtykieta, Converter={StaticResource NotNullConverter}}">
                <StackLayout>
                    <Label Text="Informacje o Etykiecie" FontSize="18" FontAttributes="Bold"/>
                    <Label Text="{Binding AktualnaEtykieta.Kod, StringFormat='Kod: {0}'}"/>
                    <Label Text="{Binding AktualnaEtykieta.KodNazwa, StringFormat='Nazwa: {0}'}"/>
                    <Label Text="{Binding AktualnaEtykieta.IloscTeoretyczna, StringFormat='Ilość teoretyczna: {0}'}"/>
                    <Label Text="{Binding AktualnaEtykieta.Lokalizacja.PelnyAdres, StringFormat='Lokalizacja: {0}'}"/>
                </StackLayout>
            </Frame>
            
            <!-- Wprowadzanie danych -->
            <Frame BackgroundColor="LightCoral" Padding="10" Margin="0,0,0,10">
                <StackLayout>
                    <Label Text="Dane Inwentaryzacji" FontSize="18" FontAttributes="Bold"/>
                    
                    <Label Text="Ilość spisana:"/>
                    <Entry x:Name="IloscEntry" 
                           Keyboard="Numeric"
                           Text="{Binding IloscSpisana}"/>
                    
                    <Label Text="Lokalizacja:"/>
                    <Grid>
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="*"/>
                        </Grid.ColumnDefinitions>
                        
                        <Picker Grid.Column="0" 
                                Title="Hala"
                                ItemsSource="{Binding Hale}"
                                SelectedItem="{Binding WybranaLokalizacja.Hala}"/>
                        <Picker Grid.Column="1" 
                                Title="Regał"
                                ItemsSource="{Binding Regaly}"
                                SelectedItem="{Binding WybranaLokalizacja.Regal}"/>
                        <Entry Grid.Column="2" 
                               Placeholder="Miejsce"
                               Text="{Binding WybranaLokalizacja.Miejsce}"/>
                        <Picker Grid.Column="3" 
                                Title="Poziom"
                                ItemsSource="{Binding Poziomy}"
                                SelectedItem="{Binding WybranaLokalizacja.Poziom}"/>
                    </Grid>
                    
                    <CheckBox x:Name="TrybOfflineCheckBox" 
                              IsChecked="{Binding TrybOffline}"/>
                    <Label Text="Tryb offline"/>
                </StackLayout>
            </Frame>
            
            <!-- Przyciski akcji -->
            <StackLayout Orientation="Horizontal" HorizontalOptions="FillAndExpand">
                <Button Text="Zapisz" 
                        Command="{Binding ZapiszPozycjeCommand}"
                        BackgroundColor="Blue" 
                        TextColor="White"
                        HorizontalOptions="FillAndExpand"/>
                <Button Text="Anuluj" 
                        Command="{Binding AnulujCommand}"
                        BackgroundColor="Gray" 
                        TextColor="White"
                        HorizontalOptions="FillAndExpand"/>
            </StackLayout>
            
        </StackLayout>
    </ScrollView>
    
</ContentPage>
```

## Kluczowe Różnice Desktop vs MAUI

### 1. Architektura

| Aspekt | Desktop (WinCE) | MAUI |
|--------|-----------------|------|
| Pattern | Code-behind | MVVM |
| UI Framework | Windows Forms | XAML |
| Threading | Manual | Async/Await |
| Data Binding | Manual | Automatic |
| Navigation | Form.Show() | Shell Navigation |

### 2. Obsługa Danych

| Aspekt | Desktop | MAUI |
|--------|---------|------|
| Baza danych | Direct MySQL | API + SQLite (offline) |
| Synchronizacja | Real-time | Background sync |
| Cache | Manual | Automatic |
| Offline | Limited | Full support |

### 3. Interfejs Użytkownika

| Aspekt | Desktop | MAUI |
|--------|---------|------|
| Layout | Fixed | Responsive |
| Controls | Basic | Rich |
| Styling | Manual | Styles/Themes |
| Accessibility | Limited | Full support |

### 4. Funkcjonalności Mobilne

| Funkcjonalność | Implementacja MAUI |
|----------------|-------------------|
| Skanowanie | Camera + ML.NET |
| GPS | Microsoft.Maui.Essentials |
| Offline sync | SQLite + Background service |
| Push notifications | Azure Notification Hubs |
| Biometric auth | Microsoft.Maui.Authentication |
