<?php

include_once 'Db.class.php';
include_once 'funkcje.inc';
// add errors to the output
error_reporting(E_ALL);
ini_set('display_errors', '1');
$db = new Db();
$komunikat = "OK";
$zadanie_dane_id = $_GET['zadanie_dane_id'];

if (!empty($zadanie_dane_id)) {
    $sql = "update zadania_dane z set pominiecie=NOW(),start=null,przydzielenie_pracownik_id=0 where z.id=$zadanie_dane_id limit 1";
    //echo $sql;
    $result = $db->mGetResult($sql);
    show_komunikat_xml($komunikat);
} else {
    show_komunikat_xml("Nie udało się pominąć");
}
