﻿using System;
using System.Linq;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using System.Threading;

namespace Tarczyn__Magazyn
{
    public partial class WczytywanieNaLinie : Form
    {

        ProdukcjaMenu parent = null;
        public WczytywanieNaLinie(ProdukcjaMenu myParent)
        {
            FullScreenMode.OknoOFF(this);
            InitializeComponent();
            parent = myParent;

            string zapytanie = "SELECT last FROM docnumber d WHERE d.name='operacja_id';";

            operac_id_global = (string)BazaDanychExternal.Wyczytaj_Jedna_Wartosc(zapytanie);

            string zapyt = "update docnumber d set last=last+1 WHERE d.name='operacja_id'; ";
            BazaDanychExternal.Wyczytaj_Jedna_Wartosc(zapyt);
            Wlasciwosci.CurrentOperacja = "18";

        }
        
        string CurrentSystem = "";
        int CurrentSystemID = 0;
        string operac_id_global = "";
        int numer_dokumentu = 0;
        int miejsce_id = 0;

        int zmiana_palet = 0;
        int max_docin = 0;
        int max_docout = 0;
        string zam_prod_head = "";
        string kontrah_wew = "";
        string last_docin = "";
        string last_docout = "";
        

        DataTable GG;
        List<string> Etykiety = new List<string>();
        List<string> Etykiety_linie = new List<string>();
        Thread Skanowanie = null;


        private void ZacznijSkanowanie()
        {
            StringBuilder login = new StringBuilder();
            Skaner.UstawTryb_String(login);
            Skaner.Zacznij_Skanowanie();
            Skanowanie = new Thread(() => CheckString(login));
            Skanowanie.IsBackground = true;
            Skanowanie.Start();
        }
        private void CheckString(StringBuilder cc)
        {
            while (true)
            {
                if (cc.ToString() != "")
                {
                    this.Invoke(new EventHandler(delegate
                    {
                        
                        dodawanie(cc.ToString());
                    }));
                    return;
                }
                Thread.Sleep(200);
            }
        }
        private void Zakoncz_Skanowanie()
        {
            try
            {
                Skanowanie.Abort();
                Skaner.Przewij_Skanowanie();
            }
            catch
            {
            }
        }

        private void Exit_Click_1(object sender, EventArgs e)
        {
            Wlasciwosci.CurrentOperacja = "0";
            Zakoncz_Skanowanie();
            parent.Show();
            this.Close();
        }


        private void button2_Click(object sender, EventArgs e)
        {
            object Tabela = BazaDanychExternal.Wyczytaj_Tabele("select id,system_id,magazyn from etykiety where active = 1 AND id = 59935");
            if (Tabela != null)
            {

                GG = (DataTable)Tabela;
                if (GG.Rows.Count == 0)
                {
                    MessageBox.Show("Etykieta jest nie aktywna lub niepoprawna.");
                    return;
                }
                Wlasciwosci.system_id_id = GG.Rows[0][1].ToString();
                if (CurrentSystemID == 0)
                {
                    CurrentSystem = (string)BazaDanychExternal.Wyczytaj_Jedna_Wartosc("select nazwa from systemy where wartosc =" + GG.Rows[0][1].ToString());
                    SystemLabel.Text = CurrentSystem;
                    
                    CurrentSystemID = Convert.ToInt32(GG.Rows[0][1].ToString());
                    Etykiety.Add(GG.Rows[0][0].ToString());
                    Etykiety_linie.Add(textBox1.Text);
                    Zakoncz.Visible = true;
                }
                else
                {
                    if (CurrentSystemID != Convert.ToInt32(GG.Rows[0][1].ToString()))
                    {
                        MessageBox.Show("Etykieta nie należy do systemu na którym pracujesz.");
                        return;
                    }
                    if (Convert.ToInt32(GG.Rows[0][2].ToString()) == 2)
                    {
                        MessageBox.Show("Etykieta Wczytana na Linię");
                        ZacznijSkanowanie();
                        return;
                    }
                    if (Etykiety.Contains(GG.Rows[0][0].ToString()))
                    {
                        MessageBox.Show("Etykieta została już zeskanowana.");
                        return;
                    }
                    
                    Etykiety.Add(GG.Rows[0][0].ToString());
                    Etykiety_linie.Add(textBox1.Text);
                }
            }
            else
            {
                //BazaDanychExternal.DokonajUpdate("repalce into jmp_nieaktywne_na_lini(etykieta) values('" + gg + "');");
                    
                MessageBox.Show("Etykieta jest nie aktywna lub niepoprawna.");

            }

        }

        private int pobierz_id_miejsca(string hala_local, string regal_local, string miejsce_local, string poziom_local) //pobiera
        {
            //if(nr_etykiety)
            string zapytanie = "";
            zapytanie = "select id from miejsca m where hala='" + hala_local + "' and regal='" + regal_local + "' and miejsce='" + miejsce_local + "' and poziom='" + poziom_local + "' limit 1;";

            object obj2 = BazaDanychExternal.Wyczytaj_Tabele(zapytanie);

            DataTable tabela = (DataTable)obj2;
            if (tabela.Rows.Count < 1)
            {  //sprawdza 
                return 0;
            }
            else
            {
                return Convert.ToInt32(tabela.Rows[0]["id"]);
            }


        }

        private void dodawanie(string gg)
        {
            Zakoncz_Skanowanie();


            if (BazaDanychExternal.SprawdzCzyIstniejePolaczenie() == false)
            {
                MessageBox.Show("Brak połączenia z bazą danych , spróbuj ponownie.");
                ZacznijSkanowanie();
                return;
            }

            if (!(textBox1.Text == "1" || textBox1.Text == "2" || textBox1.Text == "3" || textBox1.Text == "4" || textBox1.Text == "4" || textBox1.Text == "5" || textBox1.Text == "6" || textBox1.Text == "7" || textBox1.Text == "8" || textBox1.Text == "9")) 
            {
                MessageBox.Show("Błędny numer lini");
                ZacznijSkanowanie();
                return;
            }

            
            miejsce_id = pobierz_id_miejsca("1", "PROD", textBox1.Text, "A");
            
            if(miejsce_id==0)
                {
                    MessageBox.Show("Brak lini w bazie");
                    ZacznijSkanowanie();
                    return;
                }

            if (gg.Substring(0, 2) == "DS")
            {
                object Tabela = BazaDanychExternal.Wyczytaj_Tabele("select e.id,e.system_id,lot,data_waznosci,e.magazyn,s.nazwa as status_nazwa,s.funkcja_stat from etykiety e left join status_system s on e.status_id=s.id where e.active = 1 and  e.paleta_id = " + gg.Replace("DS", ""));
                if (Tabela != null)
                {

                    GG = (DataTable)Tabela;
                    if (GG.Rows.Count == 0)
                    {
                        MessageBox.Show("Etykieta jest nie aktywna lub niepoprawna.");
                        BazaDanychExternal.DokonajUpdate("replace into jmp_nieaktywne_na_lini(etykieta) values('" + gg + "');");
                 
                        ZacznijSkanowanie();
                        return;
                    }

                    Wlasciwosci.system_id_id = GG.Rows[0][1].ToString();
                    if (CurrentSystemID == 0)
                    {
                        CurrentSystem = (string)BazaDanychExternal.Wyczytaj_Jedna_Wartosc("select nazwa from systemy where wartosc =" + GG.Rows[0][1].ToString());
                        SystemLabel.Text = CurrentSystem;
                        CurrentSystemID = Convert.ToInt32(GG.Rows[0][1].ToString());
                        //Etykiety.Add(GG.Rows[0][0].ToString());
                        //Etykiety_linie.Add(textBox1.Text);
                        Zakoncz.Visible = true;
                    }
                    else
                    {
                        if (CurrentSystemID != Convert.ToInt32(GG.Rows[0][1].ToString()))
                        {
                            MessageBox.Show("Etykieta nie należy do systemu na którym pracujesz.");
                            ZacznijSkanowanie();
                            return;
                        }
                        if (Etykiety.Contains(GG.Rows[0][0].ToString()))
                        {
                            MessageBox.Show("Etykieta została już zeskanowana.");
                            ZacznijSkanowanie();
                            return;
                        }
                        if (Convert.ToInt32(GG.Rows[0][4].ToString()) == 2)
                        {
                            MessageBox.Show("Etykieta Wczytana na Linię");
                            ZacznijSkanowanie();
                            return;
                        }

                        if (GG.Rows[0]["funkcja_stat"].ToString() == "blokada_wyd")
                        {
                            MessageBox.Show("Etykieta " + gg + " ma status " + GG.Rows[0]["status_nazwa"].ToString());
                            ZacznijSkanowanie();
                            return; 
                        }
                        /*
                        if (GG.Rows[0][2].ToString() == "" || GG.Rows[0][3].ToString() == "")
                        {
                            string wiadomosc = "Brak Daty waznosci: " + GG.Rows[0][2].ToString() + " lub Lot: " + GG.Rows[0][2].ToString() + ". Mimo to wczytać?";
                            //DialogResult dialogResult = MessageBox.Show(wiadomosc, "", MessageBoxButtons.YesNo,null,null);
                            DialogResult result = MessageBox.Show(wiadomosc, string.Empty, MessageBoxButtons.YesNo, MessageBoxIcon.None, MessageBoxDefaultButton.Button1);

                            if (result == DialogResult.No)
                            {
                                ZacznijSkanowanie();
                                return;
                            }

                        } */


                        //Etykiety.Add(GG.Rows[0][0].ToString());
                        //Etykiety_linie.Add(textBox1.Text);

                    }
                    for (int t = 0; t < GG.Rows.Count; t++)
                    {
                        ZmianaEtykietNaProdukcje(GG.Rows[t][0].ToString(), textBox1.Text);
                        
                    }
                    

                }
                else
                {

                    BazaDanychExternal.DokonajUpdate("replace into jmp_nieaktywne_na_lini(etykieta) values('" + gg + "');");
                 
                    MessageBox.Show("Etykieta jest nie aktywna lub niepoprawna.");
                }


            }
            else
            {



                object Tabela = BazaDanychExternal.Wyczytaj_Tabele("select e.id,e.system_id,e.lot,e.data_waznosci,e.magazyn from etykiety e where e.active = 1  AND (e.id = " + gg + " or e.etykieta_klient='" + gg + "')");
                if (Tabela != null)
                {

                    GG = (DataTable)Tabela;
                    if (GG.Rows.Count == 0)
                    {
                        MessageBox.Show("Etykieta jest nie aktywna lub niepoprawna.");
                        //MessageBox.Show("repalce into jmp_nieaktywne_na_lini(etykieta) values('" + gg + "')");
                        BazaDanychExternal.DokonajUpdate("replace into jmp_nieaktywne_na_lini(etykieta) values('" + gg + "');");
                 
                        ZacznijSkanowanie();
                        return;
                    }
                    Wlasciwosci.system_id_id = GG.Rows[0][1].ToString();
                    if (CurrentSystemID == 0)
                    {
                        CurrentSystem = (string)BazaDanychExternal.Wyczytaj_Jedna_Wartosc("select nazwa from systemy where wartosc =" + GG.Rows[0][1].ToString());
                        SystemLabel.Text = CurrentSystem;
                        CurrentSystemID = Convert.ToInt32(GG.Rows[0][1].ToString());
                        //Etykiety.Add(GG.Rows[0][0].ToString());
                        //Etykiety_linie.Add(textBox1.Text);
                        Zakoncz.Visible = true;
                    }
                    else
                    {
                        if (CurrentSystemID != Convert.ToInt32(GG.Rows[0][1].ToString()))
                        {
                            MessageBox.Show("Etykieta nie należy do systemu na którym pracujesz.");
                            ZacznijSkanowanie();
                            return;
                        }

                        if (Convert.ToInt32(GG.Rows[0][4].ToString())==2)
                        {
                            MessageBox.Show("Etykieta Wczytana na Linię");
                            ZacznijSkanowanie();
                            return;
                        }

                        if (Etykiety.Contains(GG.Rows[0][0].ToString()))
                        {
                            MessageBox.Show("Etykieta została już zeskanowana.");
                            ZacznijSkanowanie();
                            return;
                        }
                        if (GG.Rows[0][2].ToString() == "" || GG.Rows[0][3].ToString() == "")
                        {
                            /*
                            string wiadomosc = "Brak Daty waznosci: " + GG.Rows[0][2].ToString() + " lub Lot: " + GG.Rows[0][2].ToString() + ". Mimo to wczytać?";
                            //DialogResult dialogResult = MessageBox.Show(wiadomosc, "", MessageBoxButtons.YesNo,null,null);
                            DialogResult result = MessageBox.Show(wiadomosc, string.Empty, MessageBoxButtons.YesNo, MessageBoxIcon.None, MessageBoxDefaultButton.Button1);

                            if (result == DialogResult.No)
                            {
                                ZacznijSkanowanie();
                                return;
                            }
                            */

                        }


                        //Etykiety.Add(GG.Rows[0][0].ToString());
                        //Etykiety_linie.Add(textBox1.Text);

                    }
                    ZmianaEtykietNaProdukcje(GG.Rows[0][0].ToString(), textBox1.Text);

                }
                else
                {
                    BazaDanychExternal.DokonajUpdate("replace into jmp_nieaktywne_na_lini(etykieta) values('" + gg + "');");
                 

                    MessageBox.Show("Etykieta jest nie aktywna lub nieprzyjęta/niepoprawna.");
                }

            }


            

            ZacznijSkanowanie();
        }




        private string DajDate(string gg)
        {

            gg = gg.Replace("'", "");
            //MessageBox.Show(gg);
            if (gg == "null")
            {
                return "null";
            }

            DateTime gh = DateTime.ParseExact(gg,"dd-MM-yyyy",null);
            return "'"+gh.ToString("yyyy-MM-dd")+"'";
                
            
        }


        private void ZmianaEtykietNaProdukcje(string etykieta_id,string miejsce_local)
        {
            
            if (numer_dokumentu == 0)
            {

                BazaDanychExternal.DokonajUpdate("insert into zam_prod_head(system_id_zm,data,pracownik_id,status) values (" + CurrentSystemID.ToString() + ",sysdate()," + Wlasciwosci.id_Pracownika + ",2)");
                zam_prod_head = BazaDanychExternal.Command.LastInsertedId.ToString();
                kontrah_wew = (string)BazaDanychExternal.Wyczytaj_Jedna_Wartosc("select kontrah_wew_id from systemy where wartosc = " + CurrentSystemID.ToString());
                DataTable KK;

                max_docin = Convert.ToInt32(BazaDanychExternal.Wyczytaj_Jedna_Wartosc("select max(doc_nr) from docin where kontrah_id = " + kontrah_wew));
                max_docout = Convert.ToInt32(BazaDanychExternal.Wyczytaj_Jedna_Wartosc("select max(docout_nr) from docout where kontrah_id = " + kontrah_wew));


                if (max_docin > max_docout)
                {
                    numer_dokumentu = max_docin + 1;
                }
                else
                {
                    numer_dokumentu = max_docout + 1;
                }

               zmiana_palet = Convert.ToInt32(BazaDanychExternal.Wyczytaj_Jedna_Wartosc("select (max(doc_nr)+1) from zmianym where system_id = " + CurrentSystemID.ToString()));

            
            BazaDanychExternal.DokonajUpdate("insert into docin(doc_internal,doc_type,doc_nr,doc_date,doc_ts,pracownik_id,kontrah_id,doc_ref,doc_uwagi)" +
                " values(3,'MM'," + numer_dokumentu.ToString() + ",date(now()),sysdate()," + Wlasciwosci.id_Pracownika + "," + kontrah_wew + ",'" + zam_prod_head + " " + SystemLabel.Text + "','')");
            last_docin = BazaDanychExternal.Command.LastInsertedId.ToString();

            BazaDanychExternal.DokonajUpdate("insert into docout(docout_internal,docout_type,docout_nr,docout_date,docout_ts,pracownik_id,kontrah_id,docout_ref,docout_uwagi)" +
                " values(3,'MM-'," + numer_dokumentu.ToString() + ",date(now()),sysdate()," + Wlasciwosci.id_Pracownika + "," + kontrah_wew + ",'" + zam_prod_head + " " + SystemLabel.Text + "','')");
            last_docout = BazaDanychExternal.Command.LastInsertedId.ToString();
            }



            object Tabela = BazaDanychExternal.Wyczytaj_Tabele("select system_id,magazyn,active,miejscep,kod_id,status_id,status_id2,stat,paleta_id,kartony,DATE_FORMAT(dataprod,'%d-%m-%Y'),DATE_FORMAT(data_waznosci,'%d-%m-%Y'),ilosc,ts,status,blloc,akcja_id,status_prism,lot,sscc,gtin,przeznaczenie_id,nretykiety,docin_id,docout_id,delivery_id,listcontrol_id,etykieta_klient from etykiety where id = " + etykieta_id);
                if (Tabela != null)
                {


                    
                BazaDanychExternal.Inicjuj_Transakcje();
                                BazaDanychExternal.Zacznij_Transakcje();

                try
                {


                    BazaDanychExternal.DokonajUpdate("insert into zam_prod(etykieta_id,system_id,ts,zamowienie_id) values(" + etykieta_id + "," + CurrentSystemID.ToString() + ",sysdate()," + zam_prod_head + ")");
                    GG = (DataTable)Tabela;
                    BazaDanychExternal.DokonajUpdate("insert into zmianym(typ,doc_nr,pracownik_id,data,etykieta,system_id,stare_m,nowe_m,doc_internal,stat,tszm,uwagi) VALUES (" +
                       "'ZMP'," +
                       zmiana_palet.ToString() + "," +
                       Wlasciwosci.id_Pracownika + "," +
                       "date(sysdate())," +
                       etykieta_id + "," +
                       CurrentSystemID.ToString() + "," +
                       GG.Rows[0][3].ToString() + "," +
                       miejsce_id+"," +
                       "3," +
                       "1," +
                       "sysdate(),'')");
                    //string ko = (string)BazaDanychExternal.Wyczytaj_Jedna_Wartosc("SELECT last FROM docnumber d WHERE d.name='nretykiety'");
                    //BazaDanychExternal.Wyczytaj_Jedna_Wartosc("update docnumber set last=(last+1)  WHERE name='nretykiety'");
                    BazaDanychExternal.DokonajUpdate("insert into etykiety(system_id,magazyn,active,miejscep,kod_id,status_id,status_id2,stat,paleta_id,kartony,dataprod,data_waznosci,ilosc,ts,status,blloc,akcja_id,status_prism,lot,sscc,gtin,przeznaczenie_id,nretykiety,docin_id,etykieta_klient)" +
                        " values(" +
                        sprawdz_czy_null(GG.Rows[0][0].ToString()) + "," +
                        "2," +
                        sprawdz_czy_null(GG.Rows[0][2].ToString()) + "," +
                        miejsce_id + "," +
                        sprawdz_czy_null(GG.Rows[0][4].ToString()) + "," +
                        sprawdz_czy_null(GG.Rows[0][5].ToString()) + "," +
                        sprawdz_czy_null(GG.Rows[0][6].ToString()) + "," +
                        sprawdz_czy_null(GG.Rows[0][7].ToString()) + "," +
                        sprawdz_czy_null(GG.Rows[0][8].ToString()) + "," +
                        sprawdz_czy_null(GG.Rows[0][9].ToString()) + "," +
                        DajDate(sprawdz_czy_null(GG.Rows[0][10].ToString())) + "," + //DATA
                        DajDate(sprawdz_czy_null(GG.Rows[0][11].ToString())) + "," + //DATA
                        sprawdz_czy_null(GG.Rows[0][12].ToString()) + "," +
                        "NOW()," +//NOW()
                        sprawdz_czy_null(GG.Rows[0][14].ToString()) + "," + 
                        sprawdz_czy_null(GG.Rows[0][15].ToString()) + "," +
                        sprawdz_czy_null(GG.Rows[0][16].ToString()) + "," +
                        sprawdz_czy_null(GG.Rows[0][17].ToString()) + "," +
                        sprawdz_czy_null(GG.Rows[0][18].ToString()) + "," +
                        sprawdz_czy_null(GG.Rows[0][19].ToString()) + "," +
                        sprawdz_czy_null(GG.Rows[0][20].ToString()) + "," +
                        //sprawdz_czy_null(GG.Rows[0][21].ToString()) + "," +
                          "2," +
                        sprawdz_czy_null(GG.Rows[0]["nretykiety"].ToString()) + "," +
                        last_docin + "," +

                        sprawdz_czy_null(GG.Rows[0]["etykieta_klient"].ToString()) + ")");
                    
                    BazaDanychExternal.DokonajUpdate("update etykiety set active=0,docout_id=" + last_docout + " where id=" + etykieta_id);
                    BazaDanychExternal.DokonajUpdate("insert into operacje(etykieta_id, doc_type, doc_nr,  imie_nazwisko, typ_operacji,system_id,wozek,operac_id,ilosc) values('" + etykieta_id + "','ZMP','" + zmiana_palet.ToString() + "','" + Wlasciwosci.imie_nazwisko + "','ZM','" + Wlasciwosci.system_id_id + "','" + Wlasciwosci.wozek + "','" + operac_id_global + "','1');");
                    BazaDanychExternal.Zakoncz_Transakcje();
                }
                catch (MySql.Data.MySqlClient.MySqlException t)
                {
                    BazaDanychExternal.Przerwij_Transakcje();
                    MessageBox.Show("Nie udało się. Proszę o ponowny skan.");
                    return;
                }

                    label9.Text = (string)BazaDanychExternal.Wyczytaj_Jedna_Wartosc("select count(1)  from etykiety e left join docin d on e.docin_id=d.id where d.id=" + last_docin);
                    Etykieta.Text = etykieta_id;


                }
            

            //Wlasciwosci.ZapisXMLNaLinie("", "", "");
            //MessageBox.Show("Zakończyłem przenoszenie etykiet.");
            //
            
        }

        private string sprawdz_czy_null(string bb)
        {
            if (bb == "")
            {

                return "null";
            }
            else
            {

                try
                {
                    Convert.ToDouble(bb);
                    return bb;
                }
                catch
                {
                    return "'" + bb + "'";
                }
            }

        }

        private void Zakoncz_Click(object sender, EventArgs e)
        {
            parent.Show();

            this.Close();


            Zakoncz_Skanowanie();
            /*
            string EtykietyAll = "";
            for (int v = 0; v < Etykiety.Count; v++)
            {
                if (v == 0)
                {
                    EtykietyAll += Etykiety[v];
                }
                else
                {
                    EtykietyAll += "," + Etykiety[v];
                }
            }
            //Wlasciwosci.ZapisXMLNaLinie(EtykietyAll, CurrentSystemID.ToString(), Wlasciwosci.id_Pracownika);
            ZmianaEtykietNaProdukcje();
            */
        }

        private void WczytywanieNaLinie_Load_1(object sender, EventArgs e)
        {
            string[] old = Wlasciwosci.wczytanieEtykietNaLinie();
            if (old[2] == Wlasciwosci.id_Pracownika)
            {
                MessageBox.Show("Znalazłem nie zakończoną operację, wczytuje poprzednie etykiety.");

                foreach (string hh in old[0].Split(','))
                {
                    Etykiety.Add(hh);
                }
                CurrentSystemID = Convert.ToInt32(old[1]);
                CurrentSystem = (string)BazaDanychExternal.Wyczytaj_Jedna_Wartosc("select nazwa from systemy where wartosc =" + old[1]);
                SystemLabel.Text = CurrentSystem;
                Zakoncz.Visible = true;
            }
            ZacznijSkanowanie();
        }

        private void button1_Click(object sender, EventArgs e)
        {
            if (textBox2.Text == "" || textBox1.Text=="")
            {
                MessageBox.Show("Wpisz nr etykiety lub linię");
                return;
            }
            dodawanie(textBox2.Text);
            textBox2.Text = "";
            //ZmianaEtykietNaProdukcje(textBox2.Text, textBox1.Text);
            
        }






    }
}
