<?php

include_once 'Db.class.php';
include_once 'funkcje.inc';
//error_reporting(E_ALL);
//ini_set('display_errors', 1);

$db = new Db();

// todo do zablokowania statusy






$komunikat = "OK";
$akcja = $_GET['akcja'];
$delivery_id = $_GET['delivery_id'];
$pracownik_id = $_GET['pracownik_id'];
$imie_nazwisko = $_GET['imie_nazwisko'];
$paleta_id = str_replace("DS", "", $_GET['paleta_id']);
$baza_danych = "wmsgg";
$operac_id = $_GET['operac_id'];
$wozek = $_GET['wozek'];
$system_id = $_GET['system_id'];
$etykieta_scan = $_GET['etykieta_scan'];

$start_time = microtime(true);

//header('Content-type: text/xml');
//echo '<dane>';
//http://25.56.91.22/wmsgg/public/skaner_api/delivery_realizacja_kompletacji.php?akcja=realizacja_zadania&zadanie_dane_id=1760&baza_danych=wmsgg&etykieta_id_realizowana=9969437&imie_nazwisko=Lukasz%20Domanski&ilosc_pobierana=3
// pobierz nagłówki z dok delivery
//sprawdzanie czy jakas pozycja z palety DS znajduje sie na DL
//Jezeli tak to czy ilosc aktualna etykiety jest taka sama jak zamawiana na delivery
//    Jeśli jest taka sama to zrealizuj DL,
//        Jezeli jest inna to zwróć dane do skanera by można było wyświetlić komunikat że nastąpi przepakowanie
//            
//            
//            
//            Po otrzyamniu danych od skanera sprawdź aktualnie ilość na tej pozycji palety i dokonaj przepakowania
//            Wstaw dlcollect 
//            pomniejsz etykietę o pobieraną ilość
//            stworz nową etykietę z ilością pomniejszoną
//            zapisz w logach i operacjach.
//    echo "<pre>";
//print_r(delivery_etykiety($delivery_id, $system_id, $db). "/". wczytane_licznik($delivery_id, $system_id, $db));
//    echo "</pre>";
//    return false;


if (!empty($paleta_id)) {
    if (substr($paleta_id, 0, 2) == "DL") {
        $komunikat = " Wczytane DL zamiast DS. Przerywam operację ";
        return show_komunikat_xml($komunikat);
    }
}

if (substr($etykieta_scan, 0, 2) == "DS" && $system_id == "17") {
    $komunikat = " Na tym kliencie jest wysyłka kartonowa. Przerywam operację ";
    return show_komunikat_xml($komunikat);
}


if ($akcja == "realizacja_zadania") {


    $tmp_arr = sprawdz_szukana_etykiete_WMS_DL($etykieta_scan, $delivery_id, $system_id, $komunikat, $baza_danych, $db);

    if (empty($tmp_arr['aRowEtWms'])) {
        $komunikat = "Etykieta " . $etykieta_scan . " nie jest potrzebna lub została zrealizowana ";
        return show_komunikat_xml($komunikat);
    }


    $aRowEtWms = $tmp_arr['aRowEtWms'];
    $komunikat = $tmp_arr['komunikat'];
    $etykieta_id_realizowana = $aRowEtWms['id'];

    //$komunikat .= //mysql_error();
    if ($komunikat == "OK") {

        $result2 = $aRowEtWms;
        //        







        $ilosc_pobierana = $aRowEtWms['ilosc_zamawiana'];

        //return;
        $etykieta_id_realizowana = $aRowEtWms['id'];
        if ($aRowEtWms['ilosc'] == $aRowEtWms['ilosc_zamawiana']) { // czy ilość na etykiecie jest równa czy będzie przepakowanie
            $sql = "insert into $baza_danych.dlcollect(nr_dl,nr_et,system_id) values('" . $delivery_id . "','" . $etykieta_id_realizowana . "','" . $system_id . "') ";
            $result3 = $db->mGetResultAsXML($sql);
            //                $sql = "update $baza_danych.etykiety e set e.paleta_id=$paleta_id WHERE id=" . $etykieta_id_realizowana . "  ";
            //                $result = $db->mGetResultAsXML($sql);
            $sql = "insert into operacje(etykieta_id, doc_type, doc_nr, imie_nazwisko, typ_operacji,system_id,wozek,operac_id,ilosc) values('" . $etykieta_id_realizowana . "','DL','" . $delivery_id . "','" . $imie_nazwisko . "','DL_SZ','" . $system_id . "', '" . $wozek . "','" . $operac_id . "','1');";
            $result8 = $db->mGetResultAsXML($sql);

            if (!empty($aRowEtWms['miejsce_kompletacji'])) {


                $sql = "insert into $baza_danych.zmianym 
                        (typ, doc_nr, pracownik_id, data, etykieta, system_id, stare_m, nowe_m, doc_internal, stat, tszm)
                        values('ZM',0, '$pracownik_id',CURDATE(), " . $etykieta_id_realizowana . "," . $system_id . "," . $aRowEtWms['miejscep'] . ", " . $aRowEtWms['miejsce_kompletacji'] . ", 'Z', 1, NOW())";
                //echo "<br>" . $sql;
                $result2 = $db->mGetResultAsXML($sql);

                $sql = "update $baza_danych.etykiety set miejscep=" . $aRowEtWms['miejsce_kompletacji'] . " where id=" . $etykieta_id_realizowana . " limit 1";

                //echo "<br>" . $sql;
                $result2 = $db->mGetResultAsXML($sql);
            }


            //echo "<br>" . $sql;
        } else {  //gdy jest mniej
            // skopjuj etykietę i nową ilość na DL
            if (empty($paleta_id)) {
                return xml_from_indexed_array(
                    array(
                        'komunikat' => $komunikat,
                        'akcja' => "przepakowanie",
                        'kod' => $aRowEtWms['kod'],
                        'ilosc' => $aRowEtWms['ilosc'],
                        'ilosc_zamawiana' => $aRowEtWms['ilosc_zamawiana'],
                        'ilosc_w_opakowaniu' => $aRowEtWms['ilosc_w_opakowaniu'],
                        'kod_nazwa' => preg_replace('/[^A-Za-z0-9]/', ' ', $aRowEtWms['kod_nazwa']),
                    )
                );
            }
            $docout_id = $aRowEtWms["dl_docout_id_wew"];
            $docin_id = $aRowEtWms["dl_docin_id_wew"];

            if ((empty($docout_id) || empty($docin_id))) {
                $kontrah_wew_id = get_kontrah_wew($baza_danych, $system_id, $db);
                $pracownik_id = get_pracownik($baza_danych, $imie_nazwisko, $db);
                $numer = docnumber_increment($baza_danych, "PP", $db);
                $docout_id = tworz_dokument_docout($baza_danych, "PP-", $numer, $pracownik_id, $kontrah_wew_id, $db);
                $docin_id = tworz_dokument_docin($baza_danych, "PP", $numer, $pracownik_id, $kontrah_wew_id, $db);
                $sql = "update delivery set dl_docin_id_wew=" . $docin_id . ",dl_docout_id_wew=" . $docout_id . " where id=" . $delivery_id;
                //echo $sql;
                $result = $db->mGetResultAsXML($sql);
            }


            $ilosc_etykiety = $aRowEtWms['ilosc']; //get_ilosc_etykiety($baza_danych, $etykieta_id_realizowana, $system_id, $db);
            $ilosc_zostawiana = ($ilosc_etykiety + 0) - ($ilosc_pobierana + 0);

            $id_oryginalna = przepakowanie_etykiety_step1_oryginalna($baza_danych, $etykieta_id_realizowana, $docout_id, $db);
            $id_pobierana = przepakowanie_etykiety_step2_ilosc_pobierana($baza_danych, $etykieta_id_realizowana, $docin_id, $ilosc_pobierana, $db);
            $id_zostawiana = przepakowanie_etykiety_step3_ilosc_zostawiana($baza_danych, $etykieta_id_realizowana, $docin_id, $ilosc_zostawiana, $db);

            $sql = "insert into logi(etykieta_id, system_id, typ_tabeli, opis, ts) values('" . $id_pobierana . "','" . $system_id . "','etykiety','Z:" . $etykieta_id_realizowana . ";Przed:" . $aRowEtWms['ilosc'] . ";Po:" . $ilosc_zostawiana . "',NOW()); ";
            $result8 = $db->mGetResultAsXML($sql);

            $sql = "update $baza_danych.etykiety e set e.paleta_id=$paleta_id WHERE id=" . $id_pobierana . "  ";
            $result6 = $db->mGetResultAsXML($sql);
            //echo "<br>" . $sql;

            $sql = "insert into $baza_danych.dlcollect(nr_dl,nr_et,system_id) values('" . $delivery_id . "','" . $id_pobierana . "','" . $system_id . "') ";
            $result3 = $db->mGetResultAsXML($sql);
            //echo "<br>" . $sql;

            $sql = "insert into operacje(etykieta_id, doc_type, doc_nr, imie_nazwisko, typ_operacji,system_id,wozek,operac_id,ilosc) values('" . $id_pobierana . "','DL','" . $delivery_id . "','" . $imie_nazwisko . "','DL_SZ','" . $system_id . "', '" . $wozek . "','" . $operac_id . "','1');";
            $result8 = $db->mGetResultAsXML($sql);

            $sql = "update $baza_danych.delivery_et d set d.etykieta_id=$id_pobierana WHERE d.delivery_id=" . $delivery_id . " AND d.etykieta_id=" . $etykieta_id_realizowana . " and ilosc_zamawiana=" . $aRowEtWms['ilosc_zamawiana'] . " limit 1";
            $result7 = $db->mGetResultAsXML($sql);

            if (!empty($aRowEtWms['miejsce_kompletacji'])) {


                $sql = "insert into $baza_danych.zmianym 
                        (typ, doc_nr, pracownik_id, data, etykieta, system_id, stare_m, nowe_m, doc_internal, stat, tszm)
                        values('ZM',0, '$pracownik_id',CURDATE(), " . $id_pobierana . "," . $system_id . "," . $aRowEtWms['miejscep'] . ", " . $aRowEtWms['miejsce_kompletacji'] . ", 'Z', 1, NOW())";
                //echo "<br>" . $sql;
                $result2 = $db->mGetResultAsXML($sql);

                $sql = "update $baza_danych.etykiety set miejscep=" . $aRowEtWms['miejsce_kompletacji'] . " where id=" . $id_pobierana . " limit 1";

                //echo "<br>" . $sql;
                $result2 = $db->mGetResultAsXML($sql);
            }



            //$start_time = microtime(true);
            $end_time = microtime(true);
            $execution_time = intval($end_time - $start_time);
            if ($execution_time > 0) {
                $sql = "INSERT INTO czas_wykonywania(nazwa_funkcji, ts, sekund)
                        values
                        ('delivery_manual_skaner_przepakowanie',NOW(),'" . $execution_time . "');";
                $result7 = $db->mGetResultAsXML($sql);
            }
        }
    }




    return xml_from_indexed_array(
        array(
            'komunikat' => $komunikat,
            'akcja' => "",
            'result_label' => "Wczytano: " . $etykieta_scan . "\nKod: " . $aRowEtWms["kod"] . ",  ilość: " . $aRowEtWms["ilosc_zamawiana"],
            'licznik' => delivery_etykiety($delivery_id, $system_id, $db) . "/" . wczytane_licznik($delivery_id, $system_id, $db)
        )
    );
}

function sprawdz_szukana_etykiete_WMS_DL($etykieta_scan, $delivery_id, $system_id, $komunikat, $baza_danych, $db)
{

    if (substr($etykieta_scan, 0, 2) == "DS") {
        $sql = 'select e.id,e.etykieta_klient,k.kod,k.ilosc_w_opakowaniu,k.kod_nazwa,e.lot,e.active,TRIM(TRAILING "." FROM TRIM(TRAILING "0" from e.ilosc)) as ilosc,dl.nr_dl,e.paleta_id,ss.nazwa as status_system_nazwa, ss.funkcja_stat , TRIM(TRAILING "." FROM TRIM(TRAILING "0" from de.ilosc_zamawiana))  as ilosc_zamawiana, d.dl_docin_id_wew, d.dl_docout_id_wew, d.miejsce_kompletacji,e.miejscep from ' . $baza_danych . '.etykiety e left join ' . $baza_danych . '.kody k on e.kod_id=k.id left join ' . $baza_danych . '.dlcollect dl ' . ' on dl.nr_et=e.id left join ' . $baza_danych . '.status_system ss  on e.status_id=ss.id left join ' . $baza_danych . '.delivery_et de ' . ' on de.etykieta_id=e.id left join ' . $baza_danych . '.delivery d ' . ' on de.delivery_id=d.id  where e.active=1 and de.delivery_id=' . $delivery_id . ' and e.paleta_id=' . str_replace("DS", "", $etykieta_scan) . ' and e.system_id=' . $system_id . '  order by nr_dl asc,e.ilosc asc limit 1'; //having nr_dl is null
    } else {
        $sql = 'select e.id,e.etykieta_klient,k.kod,k.ilosc_w_opakowaniu,k.kod_nazwa,e.lot,e.active,TRIM(TRAILING "." FROM TRIM(TRAILING "0" from e.ilosc)) as ilosc,dl.nr_dl,e.paleta_id,ss.nazwa as status_system_nazwa, ss.funkcja_stat , TRIM(TRAILING "." FROM TRIM(TRAILING "0" from de.ilosc_zamawiana))  as ilosc_zamawiana , d.dl_docin_id_wew, d.dl_docout_id_wew,d.miejsce_kompletacji,e.miejscep from ' . $baza_danych . '.etykiety e left join ' . $baza_danych . '.kody k on e.kod_id=k.id left join ' . $baza_danych . '.dlcollect dl ' . ' on dl.nr_et=e.id left join ' . $baza_danych . '.status_system ss  on e.status_id=ss.id left join ' . $baza_danych . '.delivery_et de ' . ' on de.etykieta_id=e.id left join ' . $baza_danych . '.delivery d ' . ' on de.delivery_id=d.id  where e.active=1 and de.delivery_id=' . $delivery_id . ' and  (e.id="' . $etykieta_scan . '" or e.etykieta_klient="' . $etykieta_scan . '")  and e.system_id=' . $system_id . '  order by nr_dl asc,e.ilosc asc limit 1'; //having nr_dl is null
    }
    //echo $sql;
    $result2 = $db->mGetResultAsXML($sql);
    $aRowEtWms = array();

    //    if (count($result2) > 1) {
    //        return array('komunikat' => $komunikat, 'aRowEtWms' => $result2, 'ilosc_pozycji_etykiety' => count($result2));
    //    }
    //echo "<br>" . $sql;
    foreach ($result2 as $index => $aRowEtWms) {
        if ($aRowEtWms['active'] != "1") {
            $komunikat = "Etykieta jest nieaktywna. Przerywam operacje";
            //echo "<br>" . $komunikat;
            //return show_komunikat_xml($komunikat);
        }

        if (!empty($aRowEtWms['nr_dl'])) {
            $komunikat = "Etykieta jest wczytana na DL" . $aRowEtWms['nr_dl'] . "";
            //echo "<br>" . $komunikat;
            //return show_komunikat_xml($komunikat);
        }
        if (!empty($aRowEtWms['funkcja_stat'])) {
            $komunikat = "Etykieta jest w statusie: " . $aRowEtWms['status_system_nazwa'] . ". Przerywam operacje";
            //echo "<br>" . $komunikat;
            //return show_komunikat_xml($komunikat);
        }

        if ($aRowEtWms['ilosc'] < $aRowEtWms['ilosc_zamawiana']) {  //czy jest odpowiednia ilość na etykiecie
            $komunikat = "Ilosc na etykiecie " . $aRowEtWms['ilosc'] . " jest niewystarczajaca. Przerywam operacje";
            //echo "<br>" . $komunikat;
            //return show_komunikat_xml($komunikat);
        }
    }
    return array('komunikat' => $komunikat, 'aRowEtWms' => $aRowEtWms);
}

function wczytane_licznik($nr_dl, $system_id, $db)
{
    $sql = "select count(1) as ile from dlcollect d where nr_dl='" . $nr_dl . "' and d.system_id='" . $system_id . "';";
    $result8 = $db->mGetResultAsXML($sql);
    return $result8[0]['ile'];
}

function delivery_etykiety($nr_dl, $system_id, $db)
{
    $sql = "select count(1) as ile from etykiety e left join delivery_et de on de.etykieta_id=e.id left join delivery d on de.delivery_id=d.id where d.id='" . $nr_dl . "' and e.system_id='" . $system_id . "' ";
    $result8 = $db->mGetResultAsXML($sql);
    return $result8[0]['ile'];
}
