﻿<Project DefaultTargets="Build" xmlns="http://schemas.microsoft.com/developer/msbuild/2003" ToolsVersion="3.5">
  <PropertyGroup>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">AnyCPU</Platform>
    <ProductVersion>9.0.21022</ProductVersion>
    <SchemaVersion>2.0</SchemaVersion>
    <ProjectGuid>{6395E3BB-6DF4-44C2-9A5D-BE79D5F798AF}</ProjectGuid>
    <OutputType>WinExe</OutputType>
    <AppDesignerFolder>Properties</AppDesignerFolder>
    <RootNamespace>WMS</RootNamespace>
    <AssemblyName>WMS</AssemblyName>
    <ProjectTypeGuids>{4D628B5B-2FBC-4AA6-8C16-197242AEB884};{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}</ProjectTypeGuids>
    <PlatformFamilyName>WindowsCE</PlatformFamilyName>
    <PlatformID>E2BECB1F-8C8C-41ba-B736-9BE7D946A398</PlatformID>
    <OSVersion>5.0</OSVersion>
    <DeployDirSuffix>Tarczyn__Magazyn</DeployDirSuffix>
    <TargetFrameworkVersion>v3.5</TargetFrameworkVersion>
    <NativePlatformName>Windows CE</NativePlatformName>
    <FormFactorID>
    </FormFactorID>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|AnyCPU' ">
    <DebugSymbols>true</DebugSymbols>
    <DebugType>full</DebugType>
    <Optimize>false</Optimize>
    <OutputPath>bin\Debug\</OutputPath>
    <DefineConstants>DEBUG;TRACE;$(PlatformFamilyName)</DefineConstants>
    <NoStdLib>true</NoStdLib>
    <NoConfig>true</NoConfig>
    <ErrorReport>prompt</ErrorReport>
    <FileAlignment>512</FileAlignment>
    <WarningLevel>4</WarningLevel>
    <GenerateSerializationAssemblies>Off</GenerateSerializationAssemblies>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|AnyCPU' ">
    <DebugType>pdbonly</DebugType>
    <Optimize>true</Optimize>
    <OutputPath>bin\Release\</OutputPath>
    <DefineConstants>TRACE;$(PlatformFamilyName)</DefineConstants>
    <NoStdLib>true</NoStdLib>
    <NoConfig>true</NoConfig>
    <ErrorReport>prompt</ErrorReport>
    <FileAlignment>512</FileAlignment>
    <WarningLevel>4</WarningLevel>
    <GenerateSerializationAssemblies>Off</GenerateSerializationAssemblies>
  </PropertyGroup>
  <ItemGroup>
    <Reference Include="Microsoft.WindowsCE.Forms" />
    <Reference Include="mscorlib" />
    <Reference Include="MySql.Data.CF, Version=6.9.7.0, Culture=neutral, PublicKeyToken=c5687fc88969c44d, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\MySql.Data.CF.dll</HintPath>
    </Reference>
    <Reference Include="Symbol, Version=2.8.0.0, Culture=neutral, PublicKeyToken=68ec8db391f150ca, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>bin\Debug\Symbol.dll</HintPath>
    </Reference>
    <Reference Include="Symbol.Barcode, Version=2.8.0.0, Culture=neutral, PublicKeyToken=68ec8db391f150ca, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>bin\Debug\Symbol.Barcode.dll</HintPath>
    </Reference>
    <Reference Include="Symbol.Barcode2, Version=2.8.0.0, Culture=neutral, PublicKeyToken=68ec8db391f150ca, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>bin\Debug\Symbol.Barcode2.dll</HintPath>
    </Reference>
    <Reference Include="Symbol.Fusion, Version=2.8.0.0, Culture=neutral, PublicKeyToken=68ec8db391f150ca, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>bin\Debug\Symbol.Fusion.dll</HintPath>
    </Reference>
    <Reference Include="Symbol.ResourceCoordination, Version=2.9.0.0, Culture=neutral, PublicKeyToken=68ec8db391f150ca, processorArchitecture=MSIL" />
    <Reference Include="System" />
    <Reference Include="System.Core" />
    <Reference Include="System.Web.Services" />
    <Reference Include="System.Xml.Linq" />
    <Reference Include="System.Data.DataSetExtensions" />
    <Reference Include="System.Data" />
    <Reference Include="System.Drawing" />
    <Reference Include="System.Windows.Forms" />
    <Reference Include="System.Xml" />
  </ItemGroup>
  <ItemGroup>
    <Compile Include="ActionMenu1.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="ActionMenu1.Designer.cs">
      <DependentUpon>ActionMenu1.cs</DependentUpon>
    </Compile>
    <Compile Include="ArtsanaWyszukiwanieEtykiety.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="ArtsanaWyszukiwanieEtykiety.designer.cs">
      <DependentUpon>ArtsanaWyszukiwanieEtykiety.cs</DependentUpon>
    </Compile>
    <Compile Include="AANEW_FORM.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="AANEW_FORM.designer.cs">
      <DependentUpon>AANEW_FORM.cs</DependentUpon>
    </Compile>
    <Compile Include="Klasy\BazaDanychExternal.cs" />
    <Compile Include="PIKR.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="PIKR.designer.cs">
      <DependentUpon>PIKR.cs</DependentUpon>
    </Compile>
    <Compile Include="Delivery_kontrola.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Delivery_kontrola.designer.cs">
      <DependentUpon>Delivery_kontrola.cs</DependentUpon>
    </Compile>
    <Compile Include="KontrolaPaletNew.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="KontrolaPaletNew.designer.cs">
      <DependentUpon>KontrolaPaletNew.cs</DependentUpon>
    </Compile>
    <Compile Include="EtykietyLaczenie.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="EtykietyLaczenie.designer.cs">
      <DependentUpon>EtykietyLaczenie.cs</DependentUpon>
    </Compile>
    <Compile Include="KontrolaPalet.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="KontrolaPalet.designer.cs">
      <DependentUpon>KontrolaPalet.cs</DependentUpon>
    </Compile>
    <Compile Include="Wozek.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Wozek.Designer.cs">
      <DependentUpon>Wozek.cs</DependentUpon>
    </Compile>
    <Compile Include="WydrukMiejsce_Form.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="WydrukMiejsce_Form.designer.cs">
      <DependentUpon>WydrukMiejsce_Form.cs</DependentUpon>
    </Compile>
    <Compile Include="ZmianaMiejscRegalNoActiveApiOne.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="ZmianaMiejscRegalNoActiveApiOne.designer.cs">
      <DependentUpon>ZmianaMiejscRegalNoActiveApiOne.cs</DependentUpon>
    </Compile>
    <Compile Include="KurierDrukowanie.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="KurierDrukowanie.designer.cs">
      <DependentUpon>KurierDrukowanie.cs</DependentUpon>
    </Compile>
    <Compile Include="DeklaracjaProdukcji.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="DeklaracjaProdukcji.Designer.cs">
      <DependentUpon>DeklaracjaProdukcji.cs</DependentUpon>
    </Compile>
    <Compile Include="SortowanieGNG.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="SortowanieGNG.designer.cs">
      <DependentUpon>SortowanieGNG.cs</DependentUpon>
    </Compile>
    <Compile Include="ZmianaMiejscRegalNoActiveApiNew.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="ZmianaMiejscRegalNoActiveApiNew.designer.cs">
      <DependentUpon>ZmianaMiejscRegalNoActiveApiNew.cs</DependentUpon>
    </Compile>
    <Compile Include="Delivery_WymiaryNew.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Delivery_WymiaryNew.designer.cs">
      <DependentUpon>Delivery_WymiaryNew.cs</DependentUpon>
    </Compile>
    <Compile Include="Kwit_paletowy.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Kwit_paletowy.designer.cs">
      <DependentUpon>Kwit_paletowy.cs</DependentUpon>
    </Compile>
    <Compile Include="GS1_Test.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="GS1_Test.designer.cs">
      <DependentUpon>GS1_Test.cs</DependentUpon>
    </Compile>
    <Compile Include="Klasy\Etykieta_test.cs" />
    <Compile Include="Sofidel_wydruk.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Sofidel_wydruk.designer.cs">
      <DependentUpon>Sofidel_wydruk.cs</DependentUpon>
    </Compile>
    <Compile Include="PoleComboboxXml.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="PoleComboboxXml.Designer.cs">
      <DependentUpon>PoleComboboxXml.cs</DependentUpon>
    </Compile>
    <Compile Include="Info_Miejsce.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Info_Miejsce.Designer.cs">
      <DependentUpon>Info_Miejsce.cs</DependentUpon>
    </Compile>
    <Compile Include="Delivery_Szykowanie.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Delivery_Szykowanie.designer.cs">
      <DependentUpon>Delivery_Szykowanie.cs</DependentUpon>
    </Compile>
    <Compile Include="IGlobal.cs" />
    <Compile Include="AwizacjaDostawRozkladanie.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="AwizacjaDostawRozkladanie.designer.cs">
      <DependentUpon>AwizacjaDostawRozkladanie.cs</DependentUpon>
    </Compile>
    <Compile Include="KonsolidacjaEtykiet.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="KonsolidacjaEtykiet.designer.cs">
      <DependentUpon>KonsolidacjaEtykiet.cs</DependentUpon>
    </Compile>
    <Compile Include="SzukanieKodu.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="SzukanieKodu.designer.cs">
      <DependentUpon>SzukanieKodu.cs</DependentUpon>
    </Compile>
    <Compile Include="Zad_DL_Podglad_oldGrid.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Zad_DL_Podglad_oldGrid.Designer.cs">
      <DependentUpon>Zad_DL_Podglad_oldGrid.cs</DependentUpon>
    </Compile>
    <Compile Include="Wybierz_Poziom_ZMNoActive.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Wybierz_Poziom_ZMNoActive.Designer.cs">
      <DependentUpon>Wybierz_Poziom_ZMNoActive.cs</DependentUpon>
    </Compile>
    <Compile Include="ZmianaMiejscRegalNoActive.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="ZmianaMiejscRegalNoActive.Designer.cs">
      <DependentUpon>ZmianaMiejscRegalNoActive.cs</DependentUpon>
    </Compile>
    <Compile Include="Delivery_Wymiary.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Delivery_Wymiary.designer.cs">
      <DependentUpon>Delivery_Wymiary.cs</DependentUpon>
    </Compile>
    <Compile Include="Delivery_kontrola_new.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Delivery_kontrola_new.designer.cs">
      <DependentUpon>Delivery_kontrola_new.cs</DependentUpon>
    </Compile>
    <Compile Include="Inwentaryzacja.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Inwentaryzacja.designer.cs">
      <DependentUpon>Inwentaryzacja.cs</DependentUpon>
    </Compile>
    <Compile Include="KolektorWysylka.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="KolektorWysylka.designer.cs">
      <DependentUpon>KolektorWysylka.cs</DependentUpon>
    </Compile>
    <Compile Include="PrzeklejanieKartonow.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="PrzeklejanieKartonow.designer.cs">
      <DependentUpon>PrzeklejanieKartonow.cs</DependentUpon>
    </Compile>
    <Compile Include="Zad_DL_PorzuconeRealizacja.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Zad_DL_PorzuconeRealizacja.designer.cs">
      <DependentUpon>Zad_DL_PorzuconeRealizacja.cs</DependentUpon>
    </Compile>
    <Compile Include="PoleTekstowe.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="PoleTekstowe.Designer.cs">
      <DependentUpon>PoleTekstowe.cs</DependentUpon>
    </Compile>
    <Compile Include="TworzenieDL_najnowszeV2.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="TworzenieDL_najnowszeV2.designer.cs">
      <DependentUpon>TworzenieDL_najnowszeV2.cs</DependentUpon>
    </Compile>
    <Compile Include="IZad_DL.cs" />
    <Compile Include="IZad_Main.cs" />
    <Compile Include="IZad_Parent.cs" />
    <Compile Include="Klasy\WebService.cs" />
    <Compile Include="Operacje.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Operacje.designer.cs">
      <DependentUpon>Operacje.cs</DependentUpon>
    </Compile>
    <Compile Include="PoleWyborListBox.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="PoleWyborListBox.designer.cs">
      <DependentUpon>PoleWyborListBox.cs</DependentUpon>
    </Compile>
    <Compile Include="ZadaniaKT1.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="ZadaniaKT1.Designer.cs">
      <DependentUpon>ZadaniaKT1.cs</DependentUpon>
    </Compile>
    <Compile Include="InwentaryzacjaMiejsca.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="InwentaryzacjaMiejsca.designer.cs">
      <DependentUpon>InwentaryzacjaMiejsca.cs</DependentUpon>
    </Compile>
    <Compile Include="ZadaniaZM2.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="ZadaniaZM2.Designer.cs">
      <DependentUpon>ZadaniaZM2.cs</DependentUpon>
    </Compile>
    <Compile Include="ZadaniaZM1.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="ZadaniaZM1.Designer.cs">
      <DependentUpon>ZadaniaZM1.cs</DependentUpon>
    </Compile>
    <Compile Include="ZadaniaMain.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="ZadaniaMain.Designer.cs">
      <DependentUpon>ZadaniaMain.cs</DependentUpon>
    </Compile>
    <Compile Include="PoleCombobox.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="PoleCombobox.Designer.cs">
      <DependentUpon>PoleCombobox.cs</DependentUpon>
    </Compile>
    <Compile Include="Kolektor.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Kolektor.designer.cs">
      <DependentUpon>Kolektor.cs</DependentUpon>
    </Compile>
    <Compile Include="PickingReprintDS.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="PickingReprintDS.Designer.cs">
      <DependentUpon>PickingReprintDS.cs</DependentUpon>
    </Compile>
    <Compile Include="Info_Paleta.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Info_Paleta.Designer.cs">
      <DependentUpon>Info_Paleta.cs</DependentUpon>
    </Compile>
    <Compile Include="Info_EAN_Uszk.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Info_EAN_Uszk.Designer.cs">
      <DependentUpon>Info_EAN_Uszk.cs</DependentUpon>
    </Compile>
    <Compile Include="Info_EAN.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Info_EAN.Designer.cs">
      <DependentUpon>Info_EAN.cs</DependentUpon>
    </Compile>
    <Compile Include="KodyAktualizacja.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="KodyAktualizacja.designer.cs">
      <DependentUpon>KodyAktualizacja.cs</DependentUpon>
    </Compile>
    <Compile Include="PoleWybor.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="PoleWybor.designer.cs">
      <DependentUpon>PoleWybor.cs</DependentUpon>
    </Compile>
    <Compile Include="ProdukcjaMenu.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="ProdukcjaMenu.Designer.cs">
      <DependentUpon>ProdukcjaMenu.cs</DependentUpon>
    </Compile>
    <Compile Include="MenuDostawy.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="MenuDostawy.Designer.cs">
      <DependentUpon>MenuDostawy.cs</DependentUpon>
    </Compile>
    <Compile Include="InwentaryzacjaGG.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="InwentaryzacjaGG.designer.cs">
      <DependentUpon>InwentaryzacjaGG.cs</DependentUpon>
    </Compile>
    <Compile Include="InwentaryzacjaProd.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="InwentaryzacjaProd.designer.cs">
      <DependentUpon>InwentaryzacjaProd.cs</DependentUpon>
    </Compile>
    <Compile Include="PoleLiczbowe.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="PoleLiczbowe.Designer.cs">
      <DependentUpon>PoleLiczbowe.cs</DependentUpon>
    </Compile>
    <Compile Include="Klasy\Etykieta.cs" />
    <Compile Include="PrzepakowanieEtykiet.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="PrzepakowanieEtykiet.designer.cs">
      <DependentUpon>PrzepakowanieEtykiet.cs</DependentUpon>
    </Compile>
    <Compile Include="PrzyjeciePodglad.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="PrzyjeciePodglad.designer.cs">
      <DependentUpon>PrzyjeciePodglad.cs</DependentUpon>
    </Compile>
    <Compile Include="SztukiOpakowania.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="SztukiOpakowania.designer.cs">
      <DependentUpon>SztukiOpakowania.cs</DependentUpon>
    </Compile>
    <Compile Include="TworzenieDLnew.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="TworzenieDLnew.designer.cs">
      <DependentUpon>TworzenieDLnew.cs</DependentUpon>
    </Compile>
    <Compile Include="UpdateEtykietaMenu.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="UpdateEtykietaMenu.designer.cs">
      <DependentUpon>UpdateEtykietaMenu.cs</DependentUpon>
    </Compile>
    <Compile Include="Wolne_Miejsca.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Wolne_Miejsca.designer.cs">
      <DependentUpon>Wolne_Miejsca.cs</DependentUpon>
    </Compile>
    <Compile Include="Info_Dostawa.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Info_Dostawa.Designer.cs">
      <DependentUpon>Info_Dostawa.cs</DependentUpon>
    </Compile>
    <Compile Include="GnGNew.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="GnGNew.Designer.cs">
      <DependentUpon>GnGNew.cs</DependentUpon>
    </Compile>
    <Compile Include="WczytywanieNaLinie.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="WczytywanieNaLinie.Designer.cs">
      <DependentUpon>WczytywanieNaLinie.cs</DependentUpon>
    </Compile>
    <Compile Include="Klasy\Zobele.cs" />
    <Compile Include="HasloPrompt.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="HasloPrompt.Designer.cs">
      <DependentUpon>HasloPrompt.cs</DependentUpon>
    </Compile>
    <Compile Include="Wolne_Poziomy.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Wolne_Poziomy.Designer.cs">
      <DependentUpon>Wolne_Poziomy.cs</DependentUpon>
    </Compile>
    <Compile Include="Wybierz_Poziom_ZM.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Wybierz_Poziom_ZM.Designer.cs">
      <DependentUpon>Wybierz_Poziom_ZM.cs</DependentUpon>
    </Compile>
    <Compile Include="WyborBazy.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="WyborBazy.Designer.cs">
      <DependentUpon>WyborBazy.cs</DependentUpon>
    </Compile>
    <Compile Include="Zad_DL_Komp.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Zad_DL_Komp.Designer.cs">
      <DependentUpon>Zad_DL_Komp.cs</DependentUpon>
    </Compile>
    <Compile Include="Zad_DL_Komp_Etykiet.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Zad_DL_Komp_Etykiet.Designer.cs">
      <DependentUpon>Zad_DL_Komp_Etykiet.cs</DependentUpon>
    </Compile>
    <Compile Include="Zad_DL_Odkl.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Zad_DL_Odkl.designer.cs">
      <DependentUpon>Zad_DL_Odkl.cs</DependentUpon>
    </Compile>
    <Compile Include="Zad_DL_Pelna_Odkl.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Zad_DL_Pelna_Odkl.designer.cs">
      <DependentUpon>Zad_DL_Pelna_Odkl.cs</DependentUpon>
    </Compile>
    <Compile Include="Zad_DL_Pelna_Pobr.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Zad_DL_Pelna_Pobr.Designer.cs">
      <DependentUpon>Zad_DL_Pelna_Pobr.cs</DependentUpon>
    </Compile>
    <Compile Include="Zad_DL_Podglad.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Zad_DL_Podglad.Designer.cs">
      <DependentUpon>Zad_DL_Podglad.cs</DependentUpon>
    </Compile>
    <Compile Include="Zad_DL_PoleWybor.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Zad_DL_PoleWybor.Designer.cs">
      <DependentUpon>Zad_DL_PoleWybor.cs</DependentUpon>
    </Compile>
    <Compile Include="Zad_DL_RoczpNos.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Zad_DL_RoczpNos.designer.cs">
      <DependentUpon>Zad_DL_RoczpNos.cs</DependentUpon>
    </Compile>
    <Compile Include="Zad_DL_Zdejmowanie.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Zad_DL_Zdejmowanie.designer.cs">
      <DependentUpon>Zad_DL_Zdejmowanie.cs</DependentUpon>
    </Compile>
    <Compile Include="Zad_Main.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Zad_Main.designer.cs">
      <DependentUpon>Zad_Main.cs</DependentUpon>
    </Compile>
    <Compile Include="Zad_ZM_Odkl.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Zad_ZM_Odkl.designer.cs">
      <DependentUpon>Zad_ZM_Odkl.cs</DependentUpon>
    </Compile>
    <Compile Include="Zad_ZM_Pobr.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Zad_ZM_Pobr.Designer.cs">
      <DependentUpon>Zad_ZM_Pobr.cs</DependentUpon>
    </Compile>
    <Compile Include="ZmianaMiejscaKartonu.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="ZmianaMiejscaKartonu.designer.cs">
      <DependentUpon>ZmianaMiejscaKartonu.cs</DependentUpon>
    </Compile>
    <Compile Include="ZmianaMiejscaPaletowego.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="ZmianaMiejscaPaletowego.Designer.cs">
      <DependentUpon>ZmianaMiejscaPaletowego.cs</DependentUpon>
    </Compile>
    <Compile Include="ZmianaMiejscRegal.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="ZmianaMiejscRegal.Designer.cs">
      <DependentUpon>ZmianaMiejscRegal.cs</DependentUpon>
    </Compile>
    <Compile Include="ZmianaMiejscRegalNoActiveApi.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="ZmianaMiejscRegalNoActiveApi.designer.cs">
      <DependentUpon>ZmianaMiejscRegalNoActiveApi.cs</DependentUpon>
    </Compile>
    <Compile Include="ZmianaMiejscWysokie.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="ZmianaMiejscWysokie.designer.cs">
      <DependentUpon>ZmianaMiejscWysokie.cs</DependentUpon>
    </Compile>
    <Compile Include="Ustawienia.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Ustawienia.Designer.cs">
      <DependentUpon>Ustawienia.cs</DependentUpon>
    </Compile>
    <Compile Include="Klasy\BazaDanychExternal_new.cs" />
    <Compile Include="Klasy\Skaner.cs" />
    <Compile Include="Klasy\WLAN_Status.cs" />
    <Compile Include="Klasy\Wlasciwosci.cs" />
    <Compile Include="MainMenu.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="MainMenu.Designer.cs">
      <DependentUpon>MainMenu.cs</DependentUpon>
    </Compile>
    <Compile Include="Klasy\FullScreenMode.cs" />
    <Compile Include="Klasy\ProcessCE.cs" />
    <Compile Include="Panel.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="Panel.Designer.cs">
      <DependentUpon>Panel.cs</DependentUpon>
    </Compile>
    <Compile Include="Program.cs" />
    <Compile Include="Properties\AssemblyInfo.cs" />
    <EmbeddedResource Include="ActionMenu1.resx">
      <DependentUpon>ActionMenu1.cs</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="ArtsanaWyszukiwanieEtykiety.resx">
      <DependentUpon>ArtsanaWyszukiwanieEtykiety.cs</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="AwizacjaDostawRozkladanie.resx">
      <DependentUpon>AwizacjaDostawRozkladanie.cs</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="AANEW_FORM.resx">
      <DependentUpon>AANEW_FORM.cs</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="PIKR.resx">
      <DependentUpon>PIKR.cs</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="Delivery_kontrola.resx">
      <DependentUpon>Delivery_kontrola.cs</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="KontrolaPaletNew.resx">
      <DependentUpon>KontrolaPaletNew.cs</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="EtykietyLaczenie.resx">
      <DependentUpon>EtykietyLaczenie.cs</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="KontrolaPalet.resx">
      <DependentUpon>KontrolaPalet.cs</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="Wozek.resx">
      <DependentUpon>Wozek.cs</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="WydrukMiejsce_Form.resx">
      <DependentUpon>WydrukMiejsce_Form.cs</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="ZmianaMiejscRegalNoActiveApiOne.resx">
      <DependentUpon>ZmianaMiejscRegalNoActiveApiOne.cs</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="KurierDrukowanie.resx">
      <DependentUpon>KurierDrukowanie.cs</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="DeklaracjaProdukcji.resx">
      <DependentUpon>DeklaracjaProdukcji.cs</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="SortowanieGNG.resx">
      <DependentUpon>SortowanieGNG.cs</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="ZmianaMiejscRegalNoActiveApiNew.resx">
      <DependentUpon>ZmianaMiejscRegalNoActiveApiNew.cs</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="Delivery_WymiaryNew.resx">
      <DependentUpon>Delivery_WymiaryNew.cs</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="Kwit_paletowy.resx">
      <DependentUpon>Kwit_paletowy.cs</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="GS1_Test.resx">
      <DependentUpon>GS1_Test.cs</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="Sofidel_wydruk.resx">
      <DependentUpon>Sofidel_wydruk.cs</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="PoleComboboxXml.resx">
      <DependentUpon>PoleComboboxXml.cs</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="Info_Miejsce.resx">
      <DependentUpon>Info_Miejsce.cs</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="Delivery_Szykowanie.resx">
      <DependentUpon>Delivery_Szykowanie.cs</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="KonsolidacjaEtykiet.resx">
      <DependentUpon>KonsolidacjaEtykiet.cs</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="SzukanieKodu.resx">
      <DependentUpon>SzukanieKodu.cs</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="Zad_DL_Podglad_oldGrid.resx">
      <DependentUpon>Zad_DL_Podglad_oldGrid.cs</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="Wybierz_Poziom_ZMNoActive.resx">
      <DependentUpon>Wybierz_Poziom_ZMNoActive.cs</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="ZmianaMiejscRegalNoActive.resx">
      <DependentUpon>ZmianaMiejscRegalNoActive.cs</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="Delivery_Wymiary.resx">
      <DependentUpon>Delivery_Wymiary.cs</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="Delivery_kontrola_new.resx">
      <DependentUpon>Delivery_kontrola_new.cs</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="Inwentaryzacja.resx">
      <DependentUpon>Inwentaryzacja.cs</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="KolektorWysylka.resx">
      <DependentUpon>KolektorWysylka.cs</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="PrzeklejanieKartonow.resx">
      <DependentUpon>PrzeklejanieKartonow.cs</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="Zad_DL_PorzuconeRealizacja.resx">
      <DependentUpon>Zad_DL_PorzuconeRealizacja.cs</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="PoleTekstowe.resx">
      <DependentUpon>PoleTekstowe.cs</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="TworzenieDL_najnowszeV2.resx">
      <DependentUpon>TworzenieDL_najnowszeV2.cs</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="Operacje.resx">
      <DependentUpon>Operacje.cs</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="PoleWyborListBox.resx">
      <DependentUpon>PoleWyborListBox.cs</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="ZadaniaKT1.resx">
      <DependentUpon>ZadaniaKT1.cs</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="InwentaryzacjaMiejsca.resx">
      <DependentUpon>InwentaryzacjaMiejsca.cs</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="ZadaniaZM2.resx">
      <DependentUpon>ZadaniaZM2.cs</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="ZadaniaZM1.resx">
      <DependentUpon>ZadaniaZM1.cs</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="ZadaniaMain.resx">
      <DependentUpon>ZadaniaMain.cs</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="PoleCombobox.resx">
      <DependentUpon>PoleCombobox.cs</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="Kolektor.resx">
      <DependentUpon>Kolektor.cs</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="PickingReprintDS.resx">
      <DependentUpon>PickingReprintDS.cs</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="Info_Paleta.resx">
      <DependentUpon>Info_Paleta.cs</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="Info_EAN_Uszk.resx">
      <DependentUpon>Info_EAN_Uszk.cs</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="Info_EAN.resx">
      <DependentUpon>Info_EAN.cs</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="KodyAktualizacja.resx">
      <DependentUpon>KodyAktualizacja.cs</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="PoleWybor.resx">
      <DependentUpon>PoleWybor.cs</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="ProdukcjaMenu.resx">
      <DependentUpon>ProdukcjaMenu.cs</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="MenuDostawy.resx">
      <DependentUpon>MenuDostawy.cs</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="InwentaryzacjaGG.resx">
      <DependentUpon>InwentaryzacjaGG.cs</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="InwentaryzacjaProd.resx">
      <DependentUpon>InwentaryzacjaProd.cs</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="PoleLiczbowe.resx">
      <DependentUpon>PoleLiczbowe.cs</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="PrzepakowanieEtykiet.resx">
      <DependentUpon>PrzepakowanieEtykiet.cs</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="PrzyjeciePodglad.resx">
      <DependentUpon>PrzyjeciePodglad.cs</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="SztukiOpakowania.resx">
      <DependentUpon>SztukiOpakowania.cs</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="TworzenieDLnew.resx">
      <DependentUpon>TworzenieDLnew.cs</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="UpdateEtykietaMenu.resx">
      <DependentUpon>UpdateEtykietaMenu.cs</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="Wolne_Miejsca.resx">
      <DependentUpon>Wolne_Miejsca.cs</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="Info_Dostawa.resx">
      <DependentUpon>Info_Dostawa.cs</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="GnGNew.resx">
      <DependentUpon>GnGNew.cs</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="WczytywanieNaLinie.resx">
      <DependentUpon>WczytywanieNaLinie.cs</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="HasloPrompt.resx">
      <DependentUpon>HasloPrompt.cs</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="Wolne_Poziomy.resx">
      <DependentUpon>Wolne_Poziomy.cs</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="Wybierz_Poziom_ZM.resx">
      <DependentUpon>Wybierz_Poziom_ZM.cs</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="WyborBazy.resx">
      <DependentUpon>WyborBazy.cs</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="Zad_DL_Komp.resx">
      <DependentUpon>Zad_DL_Komp.cs</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="Zad_DL_Komp_Etykiet.resx">
      <DependentUpon>Zad_DL_Komp_Etykiet.cs</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="Zad_DL_Odkl.resx">
      <DependentUpon>Zad_DL_Odkl.cs</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="Zad_DL_Pelna_Odkl.resx">
      <DependentUpon>Zad_DL_Pelna_Odkl.cs</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="Zad_DL_Pelna_Pobr.resx">
      <DependentUpon>Zad_DL_Pelna_Pobr.cs</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="Zad_DL_Podglad.resx">
      <DependentUpon>Zad_DL_Podglad.cs</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="Zad_DL_PoleWybor.resx">
      <DependentUpon>Zad_DL_PoleWybor.cs</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="Zad_DL_RoczpNos.resx">
      <DependentUpon>Zad_DL_RoczpNos.cs</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="Zad_DL_Zdejmowanie.resx">
      <DependentUpon>Zad_DL_Zdejmowanie.cs</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="Zad_Main.resx">
      <DependentUpon>Zad_Main.cs</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="Zad_ZM_Odkl.resx">
      <DependentUpon>Zad_ZM_Odkl.cs</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="Zad_ZM_Pobr.resx">
      <DependentUpon>Zad_ZM_Pobr.cs</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="ZmianaMiejscaKartonu.resx">
      <DependentUpon>ZmianaMiejscaKartonu.cs</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="ZmianaMiejscaPaletowego.resx">
      <DependentUpon>ZmianaMiejscaPaletowego.cs</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="ZmianaMiejscRegal.resx">
      <DependentUpon>ZmianaMiejscRegal.cs</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="ZmianaMiejscRegalNoActiveApi.resx">
      <DependentUpon>ZmianaMiejscRegalNoActiveApi.cs</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="ZmianaMiejscWysokie.resx">
      <DependentUpon>ZmianaMiejscWysokie.cs</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="Ustawienia.resx">
      <DependentUpon>Ustawienia.cs</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="MainMenu.resx">
      <DependentUpon>MainMenu.cs</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="Panel.resx">
      <DependentUpon>Panel.cs</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="Properties\Resources.resx">
      <Generator>ResXFileCodeGenerator</Generator>
      <LastGenOutput>Resources.Designer.cs</LastGenOutput>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <Compile Include="Properties\Resources.Designer.cs">
      <AutoGen>True</AutoGen>
      <DependentUpon>Resources.resx</DependentUpon>
      <DesignTime>True</DesignTime>
    </Compile>
    <Compile Include="Klasy\Wyjscie.cs" />
  </ItemGroup>
  <ItemGroup>
    <Content Include="settings.xml">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </Content>
  </ItemGroup>
  <ItemGroup>
    <WebReferences Include="Web References\" />
  </ItemGroup>
  <ItemGroup>
    <Folder Include="CustomControls\" />
  </ItemGroup>
  <Import Project="$(MSBuildBinPath)\Microsoft.CompactFramework.CSharp.targets" />
  <ProjectExtensions>
    <VisualStudio>
      <FlavorProperties GUID="{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}">
        <HostingProcess disable="1" />
      </FlavorProperties>
    </VisualStudio>
  </ProjectExtensions>
  <!-- To modify your build process, add your task inside one of the targets below and uncomment it. 
       Other similar extension points exist, see Microsoft.Common.targets.
  <Target Name="BeforeBuild">
  </Target>
  <Target Name="AfterBuild">
  </Target>
  
  -->
</Project>