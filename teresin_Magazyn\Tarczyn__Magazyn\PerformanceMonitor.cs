using System;
using System.Collections.Generic;

namespace Tarczyn__Magazyn
{
    public static class PerformanceMonitor
    {
        private static readonly Dictionary<string, DateTime> _operations = 
            new Dictionary<string, DateTime>();
            
        private static string _lastPerformanceResult = string.Empty;
        
        public static string LastResult 
        { 
            get { return _lastPerformanceResult; } 
        }

        public static void StartOperation(string name)
        {
            _operations[name] = DateTime.Now;
        }

        public static TimeSpan EndOperation(string name)
        {
            if (_operations.ContainsKey(name))
            {
                TimeSpan duration = DateTime.Now - _operations[name];
                _operations.Remove(name);
                
                _lastPerformanceResult = string.Format("Operation {0} took {1} ms", 
                    name, duration.TotalMilliseconds);
                    
                return duration;
            }
            return TimeSpan.Zero;
        }
        
        public static void ClearLastResult()
        {
            _lastPerformanceResult = string.Empty;
        }
    }
}