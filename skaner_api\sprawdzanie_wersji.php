<?php

include_once 'Db.class.php';
include_once 'funkcje.inc';

$db = new Db();
$akcja = $_GET['akcja'];
$komunikat = "OK";

if ($akcja == "pobierz") 
{

    $wersja = wyswietl_wersje("wmsgg", $db);

    return xml_from_indexed_array(array('komunikat' => $komunikat, 'wersja' => $wersja));
}

function wyswietl_wersje($baza_danych, $db) 
{
    $sql = "Select Wersja from wersjamobile group by wersja order by wersja desc limit 1";
    $result = $db->mGetResultAsXML($sql);
    
    return $result;
}