<?php

//include_once 'db_pdo.php';
include_once 'Db.class.php';
include_once 'funkcje.inc';
//error_reporting(E_ALL);
//ini_set('display_errors', 1);

$db = new Db();
//
//if (empty($argv)) {
//    //$argv = array();
//    $argv[1] = $_GET['db'];
//    $argv[2] = $_GET['akcja'];
//}
//
$baza_danych = $baza_danych = $_GET['db'];
$akcja = $_GET['akcja'];
$system_id = $_GET['system_id'];
$data = date("Y-m-d");

$id = $_GET['id'];
$nr_kwitu = $_GET['nr_kwitu'];
$params['sscc'] = $_GET['sscc'];


//exit();




$komunikat = "OK";
if ($akcja == "skan") {
    $kontrah = pobierz_karton($params);
//    echo "<pre>";
//    print_r($kontrah);
//    echo "</pre>";
    $kontrah= array_merge(array('komunikat' => $komunikat, 'id' => $id),$kontrah);



    return xml_from_indexed_array($kontrah);
}

function pobierz_karton($params) {
    $dbname = 'artsana';
    $app_config_file = "/etc/www_pass/" . $db_name . ".env";

    if (file_exists($app_config_file)) {
        $CONF = parse_ini_file($app_config_file);
    } else {
        die("Brak pliku konfiguracji: $app_config_file");
    }

    $conn = mysql_connect($CONF['DB_HOST'], $CONF['DB_USER'], $CONF['DB_PASSWORD']) or die("Couldn't make connection.");
    $db = mysql_select_db($CONF['DB_NAME'], $conn) or die("Couldn't select database");
    $sscc = $_GET['sscc'];

//zdejmowanie towaru
    $query = "SELECT 
        d.delivery_number,
customer_name,address,postal_code,city,region
FROM artsana._hucontent h 
left join artsana._hu hu on Handling_Unit=hu_sscc_number
left join _dhd d on d.transport_number=hu.transport_number 
and d.delivery_number=hu.delivery_number
WHERE  hu.hu_sscc_number like '%" . $sscc . "%' limit 1";

    //echo $query;
    $result = mysql_query($query, $conn);

    while ($aRow = mysql_fetch_array($result, MYSQL_ASSOC)) {
        return $aRow;
    }
    return null;
}
