<?php

//include_once 'db_pdo.php';
include_once 'Db.class.php';
include_once 'funkcje.inc';
//error_reporting(E_ALL);
//ini_set('display_errors', 1);

$db = new Db();
//
//if (empty($argv)) {
//    //$argv = array();
//    $argv[1] = $_GET['db'];
//    $argv[2] = $_GET['akcja'];
//}
//
$baza_danych = $_GET['db'];
$akcja = $_GET['akcja'];




$komunikat = "OK";

if ($akcja == "sprawdz_palete") {

    $paleta_id = $_GET['paleta_id'];
    $delivery_id = $_GET['delivery_id'];




    $sql = "SELECT p.id as paleta_id,de.delivery_id FROM ".$baza_danych.".palety p left join ".$baza_danych.".etykiety e on e.paleta_id=p.id 
        left join ".$baza_danych.".delivery_et de on de.etykieta_id=e.id
        where p.id=" . str_replace("DS", "", $paleta_id) . " limit 1";


    //echo $sql;
//    if (count($result) == 0) {
//        $komunikat = "$sql";
//        return show_komunikat_xml($komunikat);
//    }
    $result = $db->mGetResultAsXML($sql);

    if (count($result) == 0) {
        $komunikat = "Brak w bazie takiej palety ";
        return show_komunikat_xml($komunikat);
    }
    if (!empty($result[0]['delivery_id'])) {
        if ($result[0]['delivery_id'] != $delivery_id) {
            $komunikat = "Paleta nie należy do dej DL,  jest z DL" . $result[0]['delivery_id'];
            return show_komunikat_xml($komunikat);
        }
    }
    $result[0]['komunikat'] = $komunikat;
    return xml_from_indexed_array($result[0]);
}



$paleta_id = 0;
if ($akcja == "insert") {

    //http://***********/wmsgg/public/skaner_api/delivery_tworzenie_nosnika.php?db=wmsgg&akcja=insert
    $nosnik = 0;
    $paleta_id = 0;

    //$nosnik = get_nowy_sscc("wmsgg", $db);
    $paleta_id = docnumber_increment($baza_danych, 'nrpalety', $db);

    insert_nosnika($baza_danych, $nosnik, $paleta_id, $db);

    $sql = "SELECT id,opis FROM $baza_danych.typypalet t order by kolejnosc_pal";

    $result = $db->mGetResultAsXML($sql);

    if (!empty($db->errors))
        $komunikat = $db->errors;

    header('Content-type: text/xml');
    echo '<dane>';
    echo '<komunikat>', htmlentities($komunikat), '</komunikat>';
    echo '<sscc>', htmlentities($nosnik), '</sscc>';
    echo '<paleta_id>', htmlentities($paleta_id), '</paleta_id>';



    foreach ($result as $index => $row) {
        echo "<palety_rodzaje>";
        echo "<typypalet_id>" . $row['id'] . "</typypalet_id>";
        echo "<opis>" . $row['opis'] . "</opis>";
        echo "</palety_rodzaje>";
    }



    echo '</dane>';

    //$dc_prac_id = "999";
}
if ($akcja == "update") {
    //http://***********/wmsgg/public/skaner_api/delivery_tworzenie_nosnika.php?db=wmsgg&akcja=update&paleta_id=551326&typypalet_id=2
    $paleta_id = $_GET['paleta_id'];
    $typypalet_id = $_GET['typypalet_id'];
    $system_id_nazwa = $_GET['system_id_nazwa'];
    $delivery_id = $_GET['delivery_id'];
    $paleta_nazwa = $_GET['paleta_nazwa'];
    $adres_ip = str_replace("IP", "", $_GET['adres_ip']);

    $j_skladowania_id = "1";
    if ($typypalet_id == "2") {
        $j_skladowania_id = 2;
    }
    //echo "aa";
    if (!empty($paleta_id)) {
        update_nosnika($baza_danych, $typypalet_id, $j_skladowania_id, $paleta_id, $db);
        if (!empty($db->errors))
            $komunikat = $db->errors;

        $dane = array(
            'delivery_id' => $delivery_id,
            'skrot' => $system_id_nazwa,
            'paleta_id' => $paleta_id,
            'paleta_nazwa' => $paleta_nazwa
        );

        //$adres_ip = "***********";

        $sprawdzanie_polaczenia_drukaka = sprawdzanie_polaczenia_drukaka($adres_ip);
        if ($sprawdzanie_polaczenia_drukaka == "TAK") {
            printlabelsmall_palet($dane, $adres_ip);
        } else {
            $komunikat = "Brak polaczenia z drukarka: " . $adres_ip;
        }

        //

        header('Content-type: text/xml');
        echo '<dane>';
        echo '<komunikat>', htmlentities($komunikat), '</komunikat>';
        //echo '<komunikat>', $komunikat, '</komunikat>';
        echo '</dane>';
    }
}


if ($akcja == "pokaz_typy_palet") {
    
    $paleta_id = $_GET['paleta_id'];

    //http://***********/wmsgg/public/skaner_api/delivery_tworzenie_nosnika.php?db=wmsgg&akcja=insert
    $nosnik = 0;
    $typ_aktualny="";
    
    $sql = "SELECT opis FROM $baza_danych.palety p
left join $baza_danych.typypalet tp on tp.id=typypalet_id
where p.id=$paleta_id
ORDER BY p.id desc limit 1 ";

    $result3 = $db->mGetResultAsXML($sql);
    foreach ($result3 as $index => $row) {
        $typ_aktualny = $row['opis'];
    }
    
    


    $sql = "SELECT id,opis FROM $baza_danych.typypalet t order by kolejnosc_pal";

    $result = $db->mGetResultAsXML($sql);
    
    
    

    if (!empty($db->errors))
        $komunikat = $db->errors;

    header('Content-type: text/xml');
    echo '<dane>';
    echo '<komunikat>', htmlentities($komunikat), '</komunikat>';
    echo '<sscc>', htmlentities($nosnik), '</sscc>';
    echo '<paleta_id>', htmlentities($paleta_id), '</paleta_id>';
    
    echo '<typ_aktualny>', htmlentities($typ_aktualny), '</typ_aktualny>';



    foreach ($result as $index => $row) {
        echo "<palety_rodzaje>";
        echo "<typypalet_id>" . $row['id'] . "</typypalet_id>";
        echo "<opis>" . $row['opis'] . "</opis>";
        echo "</palety_rodzaje>";
    }



    echo '</dane>';

    //$dc_prac_id = "999";
}



function sprawdzanie_polaczenia_drukaka($adres_ip) {
    $port = 9100;
    $waitTimeoutInSeconds = 1;

    if ($fp = fsockopen($adres_ip, $port, $errCode, $errStr, $waitTimeoutInSeconds)) {
        fclose($fp);
        return "TAK";
    } else {
        fclose($fp);
        return "NIE";
    }
}

function printlabelsmall_palet($szukaj_etykiete, $adres_ip) {

    //    ------
    //    |     |
    //    |     |
    //    |     |
    //    |     |
    //    |     |
    //    ------
    //
	    //Instrukcja szablonu etykiety:
    //^FO200,780 ^FB400,,,	 ^A0B,70,50 ^FD'.$blloc.'^FS'
    // ^FO200,780 - 200 od lewej , 780 od góry, licz_c tak jak etykieta wysz_a z etykieciarki (patrz rys. u góry)  
    //  FB400 - format block o d_ugo_ci 400                                                   
    //  A0B,70,50 - 70 wysoko__ czcionki, 50 d_ugo__ czcionki - zw__anie czcionki
    // AOB - pionowe, ADN - poziome


    $layout = '^XA'
            //. '^FO80,80	^ADN,30,20	^FDDATA PRZYJ: '.date("Y-m-d").'^FS  '
            . '^FO140,10	^ADN,60,30	^FD ' . $szukaj_etykiete['skrot'] . '^FS '
            . '^FO80,100	^ADN,60,30	^FDDL:' . $szukaj_etykiete['delivery_id'] . '^FS'
            //. '^FO80,180	^ADN,60,30	^FD' . $szukaj_etykiete['paleta_nazwa'] . '^FS'
            . '^FO120,250^BY3	 ^BCN,100,Y,N,N ^A0,60 ^FDDS' . $szukaj_etykiete['paleta_id'] . '^FS'
            //       .'^FO40,60 ^FB 400,,,C ^A0B,100,100 ^FD '.$params['nr_palety'].'/'.$params['nr_kartonu'].' ^FS' 
            //.'^FO180,145 ^FB 980,,,C ^A0B,120,180 ^FD '.$params['kod'].' ^FS' 
            . ' ^XZ';
    //echo $layout;
    $file = fopen("/tmp/etykietakarton.zbr", "w");
    fputs($file, $layout);
    fclose($file);
    //flush();
    //sleep(1);

    $socket = socket_create(AF_INET, SOCK_STREAM, SOL_TCP);
    $result = socket_connect($socket, $adres_ip, "9100");
    socket_write($socket, $layout, strlen($layout));
    socket_close($socket);
    //sleep(1);
    //flush();
    //return 1;
}

function insert_nosnika($baza_danych, $nosnik, $paleta_id, $db) {
    $sql = "insert into $baza_danych.palety(id, pal_klient, ts_utworzenia)
values(  '" . $paleta_id . "' ," .
            "'" . $nosnik . "',"
            . "NOW());"
            . "  ";
    $result = $db->mGetResultAsXML($sql);
    return $result;
}

function update_nosnika($baza_danych, $typypalet_id, $j_skladowania_id, $paleta_id, $db) {
    $sql = "update $baza_danych.palety set typypalet_id=$typypalet_id, j_skladowania_id=$j_skladowania_id, ilosc=1  where id=$paleta_id";
    $result = $db->mGetResultAsXML($sql);
    return $result;
}



function docnumber_get($baza_danych, $name, $db) {
    $sql = "SELECT last FROM $baza_danych.docnumber d
WHERE d.name='$name' limit 1 "; //,dc_prac_id
    //echo "<br>" . $sql;
    $result = $db->mGetResultAsXML($sql);

    foreach ($result as $index => $aRow) {
        $wynik = $aRow['last'];
    }
    return $wynik;
}

function get_nowy_sscc($baza_danych, $db) {
    $sscc_serial_num = docnumber_increment($baza_danych, 'sscc_serial_num', $db);
    $sscc_company_nu = docnumber_get($baza_danych, 'sscc_company_nu', $db);
    $sscc_prefix = docnumber_get($baza_danych, 'sscc_prefix', $db);
    $sscc = generate_checkdigit($sscc_prefix . $sscc_company_nu . str_pad($sscc_serial_num, 6, '0', STR_PAD_LEFT));
    return $sscc;
}

function generate_checkdigit($upc_code) {
    $odd_total = 0;
    $even_total = 0;

    for ($i = 0; $i < 17; $i++) {
        if ((($i + 1) % 2) == 0) {
            /* Sum even digits */
            $even_total += $upc_code[$i];
        } else {
            /* Sum odd digits */
            $odd_total += $upc_code[$i];
        }
    }

    $sum = (3 * $odd_total) + $even_total;

    /* Get the remainder MOD 10 */
    $check_digit = $sum % 10;

    /* If the result is not zero, subtract the result from ten. */
    $aa = ($check_digit > 0) ? 10 - $check_digit : $check_digit;
    return $upc_code . $aa;
}

?>