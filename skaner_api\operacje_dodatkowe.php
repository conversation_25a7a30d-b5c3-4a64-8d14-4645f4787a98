<?php

include_once 'db.php';

$doc_nr = $_GET['doc_nr'];
$imie_nazwisko = $_GET['imie_nazwisko'];
$typ_operacji = $_GET['typ_operacji'];
$system_id = $_GET['system_id'];
$wozek = $_GET['wozek'];
$operac_id = $_GET['operac_id'];
$ilosc = $_GET['ilosc'];
$jm = $_GET['jm'];
$ostatni_wpis = $_GET['ostatni_wpis'];
$akcja = $_GET['akcja'];
$baza_danych = $_GET['baza_danych'];

$komunikat = "OK";

if (empty($baza_danych)) {
    $baza_danych = "wmsgg";
}


if ($akcja == "insert") {
    if (empty($ilosc)) {
        $ilosc = "0";
    }

    $sql = "insert into $baza_danych.operacje(etykieta_id, doc_type, doc_nr, imie_nazwisko, typ_operacji,system_id,wozek,operac_id,ilosc,jm) "
            . "values('0','','" . $doc_nr . "','" . $imie_nazwisko . "','" . $typ_operacji . "','" . $system_id . "',"
            . "'" . $wozek . "','" . $operac_id . "','" . $ilosc . "','" . $jm . "');";
    $result = mysql_query($sql, $conn);

    //echo "<br>" . $sql;
    //if(!empty($ostatni_wpis)) {

    $sql2 = "update $baza_danych.operacje set doc_nr='" . $doc_nr . "',jm='" . $jm . "' where id='" . $ostatni_wpis . "' limit 1;";
    //echo "<br>" . $sql2;
    $result = mysql_query($sql2, $conn);

    // BazaDanychExternal.DokonajUpdate("update operacje set doc_nr='" + textBox1.Text + "',jm='" + _jm[jm_comboBox2.SelectedIndex] + "' where id='" + ostatni_wpis + "' limit 1;");
    //}
}

//
//$komunikat.=//mysql_error();
//echo $komunikat;
header('Content-type: text/xml');
echo '<dane>';
echo '<komunikat>', htmlentities($komunikat), '</komunikat>';
//echo '<ilosc_pozostala>', htmlentities($ilosc_pozostala), '</ilosc_pozostala>';
//echo '<nowa_etykieta>', htmlentities($nowa_etykieta), '</nowa_etykieta>';
//echo '<kod>', htmlentities($kod), '</kod>';
echo '</dane>';
?>