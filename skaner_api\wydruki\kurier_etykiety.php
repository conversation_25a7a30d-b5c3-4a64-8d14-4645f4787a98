<?php



error_reporting(E_ALL);
ini_set('display_errors', 1);
//include_once 'db_pdo.php';
include_once '../ArtsanaDb.class.php';
include_once '../Db.class.php';
include_once '../funkcje.inc';

$artsanaDb = new ArtsanaDb();
$db = new Db();

//pola
$scan = $_GET['scan'];
$adres_ip = $_GET['drukarka'];
$pracownik_id = $_GET['pracownik_id'];
$komunikat = "OK";
$doc_ref = "";

// ostrzeżenie jesli scan jest pusty

//walidacja
if (empty($adres_ip)) {
    return xml_from_indexed_array(array('komunikat' => "Nie podano drukarki!!!"));
}
if (empty($pracownik_id)) {
    return xml_from_indexed_array(array('komunikat' => "Nie podano pracownika!!!"));
}
if (empty($scan)) {
    return xml_from_indexed_array(array('komunikat' => "Nie podano scanu!!!"));
}

$sql = 'select * from pracownicy where id=' . $pracownik_id . ' limit 1;';
$result = $db->mGetResultAsXML($sql);
if (empty($result)) {
    return xml_from_indexed_array(array('komunikat' => "Nie znaleziono pracownika o id=" . $pracownik_id));
}


$sql = 'select * from drukarki where adres_ip="' . $adres_ip . '" limit 1;';
$result = $db->mGetResultAsXML($sql);
if (empty($result)) {
    return xml_from_indexed_array(array('komunikat' => "Nie znaleziono drukarki o adresie ip=" . $adres_ip));
}

if (substr($scan, 0, 2) == "DS") {
    return xml_from_indexed_array(array('komunikat' => "Wymagany jest dokument, nie paleta!!!"));
}




//logika 

if (substr($scan, 0, 2) == "DL") {
    $result = szukaj_dl(str_replace("DL", "", $scan), $db);
    if (empty($result)) {
        $xml = xml_from_indexed_array(array('komunikat' => "Nie znaleziono " . $scan));
    } else {


        foreach ($result as $key => $value) {
            $result3 = szukaj_kurier_head($value['dl_doc_ref'], $value['dl_doc_ref_klient'], $db);
            if (empty($result3)) {
                return xml_from_indexed_array(array('komunikat' => "Nie znaleziono etykiet dla '" . $value['dl_doc_ref'] . "' lub '" . $value['dl_doc_ref_klient'] . "' "));
            } else {
                foreach ($result3 as $key => $value3) {
                    print_kurier($value3['labelData'], $adres_ip);
                    $sql = 'update kurier_head set pracownik_id_wydruk=' . $pracownik_id . ',wydrukowano_etykiety=NOW() where id=' . $value3['id'] . ' limit 1;';
                    $result5 = $db->mGetResultAsXML($sql);
                }
            }
        }
    }
} else {
    $result5 = szukaj_artsana($scan, $artsanaDb);
    if (empty($result5)) {
        return xml_from_indexed_array(array('komunikat' => "Nie znaleziono dokumentu dla " . $scan));
    } else {

        foreach ($result as $key => $value) {
            $result3 = szukaj_kurier_head($result5[0]['delivery_number'], $result5[0]['delivery_number'], $db);
            if (empty($result3)) {
                return xml_from_indexed_array(array('komunikat' => "Nie znaleziono etykiety dla " . $$result5[0]['delivery_number']));
            } else {
                foreach ($result3 as $key => $value3) {
                    print_kurier($value3['labelData'], $adres_ip);
                    $sql = 'update kurier_head set pracownik_id_wydruk=' . $pracownik_id . ',wydrukowano_etykiety=NOW() where id=' . $value3['id'] . ' limit 1;';
                    $result5 = $db->mGetResultAsXML($sql);
                }
            }
        }
    }
}


return xml_from_indexed_array(array('komunikat' => $komunikat, 'doc_ref' => $doc_ref));

function szukaj_kurier_head($dl_doc_ref, $dl_doc_ref_klient, $db)
{
    $sql = 'SELECT id, ts, comment,labelData FROM wmsgg.kurier_head where comment like "%' . $dl_doc_ref . '%" or comment like "%' . $dl_doc_ref_klient . '%"  and  ts>DATE_SUB(NOW(), INTERVAL 3 DAY) ;';
    $result = $db->mGetResultAsXML($sql);
    return $result;
}



function szukaj_dl($scan, $db)
{
    $sql = 'SELECT dl_doc_ref, dl_doc_ref_klient 
              FROM wmsgg.delivery  
             WHERE id="' . $scan . '"    
             LIMIT 1;';
    return $db->mGetResultAsXML($sql);
}





function print_kurier($layout, $adres_ip)
{
    echo $layout;
    // $file = fopen("/tmp/etykietakarton3.zbr", "w");
    // fputs($file, $layout);
    // fclose($file);
    //flush();
    //sleep(1);

    // $socket = socket_create(AF_INET, SOCK_STREAM, SOL_TCP);
    // $result = socket_connect($socket, $adres_ip, "9100");
    // socket_write($socket, $layout, strlen($layout));
    // socket_close($socket);
}



function szukaj_kurier_artsana($delivery_numer, $db)
{
    $sql = 'SELECT * FROM kurier_artsana ka
left join kurier_head kh on kh.id=ka.kurier_head_id
where ka.delivery_number="' . $delivery_numer . '"  limit 1;';
    $result = $db->mGetResultAsXML($sql);
    return $result;
}

function szukaj_artsana($scan, $artsanaDb)
{
    $sql = "SELECT delivery_number FROM _hu
where _hu.hu_sscc_number='" . $scan . "' limit 1";
    //echo $sql;
    $result = $artsanaDb->mGetResultAsXML($sql);

    return $result;
}

function sortuj_doc_ref($doc_ref)
{
    $array = explode(',', $doc_ref);
    sort($array);
    $dane = implode(',', $array);
    return $dane;
}
