<?php

error_reporting(E_ALL);
ini_set('display_errors', 1);

include_once '../Db_multi.class.php';

include_once '../funkcje.inc';

$db = new Db("localhost", "wmsgg");

$dbPikr = new Db("***********", "pikr");

$numer_karty = $_GET["numer_karty"];

$sql = 'SELECT * FROM wmsgg.pracownicy
where numer_Karty="' . $numer_karty . '" limit 1;   ';
$result = $db->mGetResultAsXML($sql);
//

if (empty($result)) {
    $sql = 'SELECT * FROM pikr.osoby
where numer_karty="' . $numer_karty . '" limit 1;
       ';
    $result2 = $dbPikr->mGetResultAsXML($sql);

    if (empty($result2)) {
        xml_from_indexed_array(array(
            'komunikat' => "NOOK",
                )
        );
    } else {

        //print_r($result2[0]['numer_karty']);
        $sql = "insert into wmsgg.pracownicy(login,haslo,imie_nazwisko,stanowisko,numer_Karty,pin)
values ('" . $result2[0]['nazwisko'] . $result2[0]['imie'] . "','" . $result2[0]['nazwisko'] . $result2[0]['imie'] . "','" . $result2[0]['imie'] . " " . $result2[0]['nazwisko'] . "','magazynier','" . $result2[0]['numer_karty'] . "','1234')";
        //echo $sql;
        $result = $db->mGetResultAsXML($sql);
        if (!empty($result)) {
            xml_from_indexed_array(array(
                'komunikat' => "OK",
                    )
            );
        } else {
            xml_from_indexed_array(array(
                'komunikat' => "NOOK",
                    )
            );
        }
    }
} else {
    xml_from_indexed_array(array(
        'komunikat' => "OK",
            )
    );
}



// koniec funkcji printlabel
?>