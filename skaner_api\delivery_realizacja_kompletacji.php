<?php

include_once 'Db.class.php';
include_once 'funkcje.inc';
//error_reporting(E_ALL);
//ini_set('display_errors', 1);

$db = new Db();

// todo do zablokowania statusy



$imie_nazwisko = plznaki($_GET['imie_nazwisko']);
//$typ_operacji = $_GET['typ_operacji'];

$akcja = $_GET['akcja'];
$zadanie_dane_id = $_GET['zadanie_dane_id'];

$komunikat = "OK";
$ilosc_pobierana = $_GET['ilosc_pobierana'];
$etykieta_id_realizowana = $_GET['etykieta_id_realizowana'];
$paleta_id = $_GET['paleta_id'];

$czy_koniec_kompletacji = "NIE";
$start_time = microtime(true);


//header('Content-type: text/xml');
//echo '<dane>';
//http://25.56.91.22/wmsgg/public/skaner_api/delivery_realizacja_kompletacji.php?akcja=realizacja_zadania&zadanie_dane_id=1760&baza_danych=wmsgg&etykieta_id_realizowana=9969437&imie_nazwisko=Lukasz%20Domanski&ilosc_pobierana=3


if ($akcja == "realizacja_zadania") {

    if (empty($ilosc)) {
        $ilosc = "0";
    }


    $result = pobierz_zadanie_global($zadanie_dane_id, "", $db);

    if (count($result) == 0) {
        $komunikat = "Brak informacji o zadaniu";
        return show_komunikat_xml($komunikat);
    }

    $aRowZadanie = $result[0];
    $baza_danych = $aRowZadanie['baza_danych'];
    $kod = $aRowZadanie['kod'];
    $lot = $aRowZadanie['lot'];
    $ilosc = $aRowZadanie['ilosc'];
    $zadanie_head_id = $aRowZadanie['zadanie_head_id'];
    $system_id = $aRowZadanie['system_id'];
    $docout_id = $aRowZadanie['docout_id_wew'];
    $docin_id = $aRowZadanie['docin_id_wew'];

// najpotrzebniejsze etykieta ta sama i ilość realizowana



    if ($aRowZadanie['status'] != "1") {
        $komunikat = "Zadanie zostalo zrealizowane . Przerywam operacje";
//echo $komunikat;
        return show_komunikat_xml($komunikat);
    }



// najpotrzebniejsze etykieta ta sama i ilość realizowana
//    echo $ilosc_pobierana;
//            echo "<pre>";
//            print_r($aRowZadanie);
//            echo "</pre>";
//            return false;

    if ($aRowZadanie['ilosc'] != $ilosc_pobierana) {  //czy jest odpowiednia ilość na etykiecie
        $komunikat = "Ilosc realizowana jest rozna niz planowana. Przerywam operacje";
//echo "<br>" . $komunikat;
        return show_komunikat_xml($komunikat);
    }

    if ($aRowZadanie['etykieta_id'] != $etykieta_id_realizowana) {  //czy jest odpowiednia ilość na etykiecie
        $komunikat = "Etykieta realizowana jest rozna niz planowana. Przerywam operacje";
//echo "<br>" . $komunikat;
        return show_komunikat_xml($komunikat);
    }




    if ($aRowZadanie['status'] != "1") {
        $komunikat = "Zadanie zostalo zrealizowane . Przerywam operacje";
//echo $komunikat;
        return show_komunikat_xml($komunikat);
    }


    $sql = 'SELECT id FROM ' . $baza_danych . '.pracownicy p WHERE p.imie_nazwisko="' . $imie_nazwisko . '" limit 1; ';
    $result2 = $db->mGetResult($sql);
//echo "<br>" . $sql;
    foreach ($result2 as $index => $aRowp) {
        $pracownik_id = $aRowp['id'];
    }
    if (empty($pracownik_id)) {
        $komunikat = "Nie znaleziono pracownika: " . $imie_nazwisko . ". Przerywam operacje";
////echo "<br>" . $komunikat;
        return show_komunikat_xml($komunikat);
    }



    $sql = 'select kod,lot,e.active,TRIM(TRAILING "." FROM TRIM(TRAILING "0" from ilosc)) as ilosc,dl.nr_dl,ss.nazwa as status_system_nazwa, ss.funkcja_stat from ' . $baza_danych . '.etykiety e left join ' . $baza_danych . '.kody k on e.kod_id=k.id left join ' . $baza_danych . '.dlcollect dl '
            . ' on dl.nr_et=e.id left join ' . $baza_danych . '.status_system ss  on e.status_id=ss.id where e.id=' . $etykieta_id_realizowana . ' limit  1';
    $result2 = $db->mGetResultAsXML($sql);
//echo "<br>" . $sql;
    foreach ($result2 as $index => $aRowEtWms) {
        if ($aRowEtWms['active'] != "1") {
            $komunikat = "Etykieta jest nieaktywna. Przerywam operacje";
//echo "<br>" . $komunikat;
            return show_komunikat_xml($komunikat);
        }

        if (!empty($aRowEtWms['nr_dl'])) {
            $komunikat = "Etykieta jest wczytana na DL" . $aRowEtWms['nr_dl'] . "";
//echo "<br>" . $komunikat;
            return show_komunikat_xml($komunikat);
        }

        if (!empty($aRowEtWms['funkcja_stat'])) {
            $komunikat = "Etykieta jest w statusie: " . $aRowEtWms['status_system_nazwa'] . ". Przerywam operacje";
//echo "<br>" . $komunikat;
            return show_komunikat_xml($komunikat);
        }

        if ($aRowEtWms['ilosc'] < $ilosc_pobierana) {  //czy jest odpowiednia ilość na etykiecie
            $komunikat = "Ilosc na etykiecie " . $aRowEtWms['ilosc'] . " jest niewystarczajaca. Przerywam operacje";
//echo "<br>" . $komunikat;
            return show_komunikat_xml($komunikat);
        }
//                echo "<br>".$aRowZadanie['etykieta_id'];
//                $komunikat = "-----PRZERYWNIK-------";
//                //echo "<br>" . $komunikat;
//                return;
//return;

        if ($aRowZadanie['ilosc'] == $aRowEtWms['ilosc']) { // czy ilość na etykiecie jest równa czy będzie przepakowanie
            $sql = "insert into $baza_danych.dlcollect(nr_dl,nr_et,system_id) values('" . $aRowZadanie['doc_id'] . "','" . $etykieta_id_realizowana . "','" . $aRowZadanie['system_id'] . "') ";
            $result3 = $db->mGetResultAsXML($sql);
            $sql = "update $baza_danych.etykiety e set e.paleta_id=$paleta_id WHERE id=" . $etykieta_id_realizowana . "  ";
            $result = $db->mGetResultAsXML($sql);
//echo "<br>" . $sql;
        } else {  //gdy jest mniej
// skopjuj etykietę i nową ilość na DL
            $ilosc_etykiety = $aRowEtWms['ilosc']; //get_ilosc_etykiety($baza_danych, $etykieta_id_realizowana, $system_id, $db);
            $ilosc_zostawiana = ($ilosc_etykiety + 0) - ($ilosc_pobierana + 0);


            $id_oryginalna = przepakowanie_etykiety_step1_oryginalna($baza_danych, $etykieta_id_realizowana, $docout_id, $db);
            $id_pobierana = przepakowanie_etykiety_step2_ilosc_pobierana($baza_danych, $etykieta_id_realizowana, $docin_id, $ilosc_pobierana, $db);
            $id_zostawiana = przepakowanie_etykiety_step3_ilosc_zostawiana($baza_danych, $etykieta_id_realizowana, $docin_id, $ilosc_zostawiana, $db);


            $sql = "update $baza_danych.etykiety e set e.paleta_id=$paleta_id WHERE id=" . $id_pobierana . "  ";
            $result6 = $db->mGetResultAsXML($sql);
//echo "<br>" . $sql;

            $sql = "insert into $baza_danych.dlcollect(nr_dl,nr_et,system_id) values('" . $aRowZadanie['doc_id'] . "','" . $id_pobierana . "','" . $aRowZadanie['system_id'] . "') ";
            $result3 = $db->mGetResultAsXML($sql);
//echo "<br>" . $sql;


            $sql = "update $baza_danych.delivery_et d set d.etykieta_id=$id_pobierana WHERE d.delivery_id=" . $aRowZadanie['doc_id'] . " AND d.etykieta_id=" . $aRowZadanie['etykieta_id'] . " and ilosc_zamawiana=" . $aRowZadanie['ilosc'] . " limit 1";
            $result7 = $db->mGetResultAsXML($sql);

            $sql = 'update  zadania_dane z set z.etykieta_id=' . $id_pobierana . ' where z.id=' . $zadanie_dane_id . ' limit 1';
//echo "<br>" . $sql;
            $result8 = $db->mGetResultAsXML($sql);
//echo "<br>" . $sql;
        }
    }



    //$komunikat .= //mysql_error();
    if ($komunikat == "OK") {
        $sql = ' update  zadania_dane z set z.realizacja_pracownik_id=' . $pracownik_id . ', status=2,stop=NOW(),kompletowana_paleta_id=' . $paleta_id . ' WHERE z.id=' . $zadanie_dane_id;
//echo "<br>" . $sql;
        $result8 = $db->mGetResultAsXML($sql);

        $sql = 'SELECT count(1) as ile_do_kompeltacji FROM zadania_dane z WHERE z.zadanie_head_id=' . $zadanie_head_id . ' and z.kompletacja=1 and z.status=1 ';
        $result4 = $db->mGetResultAsXML($sql);

        foreach ($result4 as $index => $aRowZadanie4) {
            if ($aRowZadanie4["ile_do_kompeltacji"] == "0") { //to jest koniec kompletacji
                //rozpocznij_pelne_po_kompletacji($zadanie_head_id, $db);
                $czy_koniec_kompletacji = "TAK";
            }
        }
    }

    //$start_time = microtime(true);
    $end_time = microtime(true);
    $execution_time = intval($end_time - $start_time);
    if ($execution_time >= 0) {
        $sql = "INSERT INTO czas_wykonywania(nazwa_funkcji, ts, sekund)
                        values
                        ('delivery_realizacja_kompletacji',NOW(),'" . $execution_time . "');";
        $result7 = $db->mGetResultAsXML($sql);
    }



    return xml_from_indexed_array(array(
        'komunikat' => $komunikat,
        'czy_koniec_kompletacji' => $czy_koniec_kompletacji)
    );
}

//
//
//function rozpocznij_pelne_po_kompletacji($zadanie_head_id, $db) {
//    $sql_ins_zad = "update zadania_dane z set z.status=1 WHERE status=6 and zadanie_head_id=" . $zadanie_head_id;
//    //echo "<br>" . $sql_ins_zad;
//    $result_zadanie4 = $db->mGetResultAsXML($sql_ins_zad);
//}
?>