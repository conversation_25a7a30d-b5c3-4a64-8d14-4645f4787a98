﻿using System;
using System.Linq;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using System.Threading;
using System.Text.RegularExpressions;

namespace Tarczyn__Magazyn
{



    public partial class GS1_Test : Form
    {
        //MainMenu myParent = null;
        ActionMenu myParent = null;
        TextBox[] TextBoxArray = null;

        TextBox AktualnyTextBox = null;

        string operac_id_global = "";

        string prev_lot = "";
        string prev_kod = "";
        string prev_kod_id = "";
        string prev_data_waznosci = "";
        string prev_sztuk = "";
        string prev_opak = "";

        List<string> _paleta_wybor = new List<string>();
        int[] _paleta = new int[100];


        string ilosc_w_opakowaniu = "1";
        string ilosc_dni_przydatnosci = "0";

        public string listcontrol_id = "";

        string wymagaj_lot = "0";
        string wymagaj_dataprod = "0";
        string wymagaj_data_waznosci = "0";



        string nrpalety_poprzedni = "";
        string nrpalety_nastepny = "";


        int licznik_local = 1;

        string decode_lot_rok = "0,0";
        string decode_lot_miesiac = "0,0";
        string decode_lot_dzien = "0,0";
        string decode_prefix_lot = "";
        string typpalety_nazwa_poprzedni = "";
        string awizacje_id = "0";



        public GS1_Test(ActionMenu c)
        {
            FullScreenMode.OknoOFF(this);
            InitializeComponent();
            TextBoxArray = new TextBox[] { LISTA_KONTROLNA_testbox, SSC };
            myParent = c;
            Etykieta_test.Inicjalizacja();

            string zapytanie = "SELECT last FROM docnumber d WHERE d.name='operacja_id';";

            operac_id_global = (string)BazaDanychExternal.Wyczytaj_Jedna_Wartosc(zapytanie);

            string zapyt = "update docnumber d set last=last+1 WHERE d.name='operacja_id'; ";
            BazaDanychExternal.Wyczytaj_Jedna_Wartosc(zapyt);

            wms_wybrany.Text = Wlasciwosci.system_id;
            imie_nazwisk_label.Text = Wlasciwosci.wozek + ":" + Wlasciwosci.imie_nazwisko;
            //Wlasciwosci.system_id_id = "";

            //paleta_id_textbox.Text = "DS" + increment_docnumber("nrpalety");

            //wyszukaj_palety();

            string rok_tmp = (string)BazaDanychExternal.Wyczytaj_Jedna_Wartosc("SELECT s.rok FROM skaner_lot_dataprod s WHERE s.system_id='" + Wlasciwosci.system_id_id + "' union select '0,0' as wartosc ");
            if (rok_tmp != "0,0") decode_lot_rok = rok_tmp;
            string miesiac_tmp = (string)BazaDanychExternal.Wyczytaj_Jedna_Wartosc("SELECT s.miesiac FROM skaner_lot_dataprod s WHERE s.system_id='" + Wlasciwosci.system_id_id + "'  union select '0,0' as wartosc");
            if (miesiac_tmp != "0,0") decode_lot_miesiac = miesiac_tmp;
            string dzien_tmp = (string)BazaDanychExternal.Wyczytaj_Jedna_Wartosc("SELECT s.dzien FROM skaner_lot_dataprod s WHERE s.system_id='" + Wlasciwosci.system_id_id + "'  union select '0,0' as wartosc");
            if (dzien_tmp != "0,0") decode_lot_dzien = dzien_tmp;

            string prefix_lot_tmp = (string)BazaDanychExternal.Wyczytaj_Jedna_Wartosc("SELECT s.prefix_lot FROM skaner_lot_dataprod s WHERE s.system_id='" + Wlasciwosci.system_id_id + "' union select 'brak'  as wartosc");
            if (prefix_lot_tmp != "brak") decode_prefix_lot = prefix_lot_tmp;



            //DW.GotFocus += GotFocus.EventHandle(RemoveText);
            //DW.LostFocus += LostFocus.EventHandle(AddText);

            //this.DW.GotFocus += new EventHandler(RemoveText);
            //this.DW.LostFocus += new EventHandler(AddText);

            //this.dataprod.GotFocus += new EventHandler(RemoveText);
            //this.dataprod.LostFocus += new EventHandler(AddText);

        }





        public void RemoveText(object sender, EventArgs e)
        {
            TextBox Pole_Tekstowe = (TextBox)sender;
            if (Pole_Tekstowe.Text == "RRMMDD")
            {
                Pole_Tekstowe.Text = "";
            }

        }

        public void AddText(object sender, EventArgs e)
        {
            if (DW.Text == "")
                DW.Text = "RRMMDD";
            if (dataprod.Text == "")
                dataprod.Text = "RRMMDD";
        }

        private void button1_Click(object sender, EventArgs e)
        {
            
            Skaner.Przewij_Skanowanie();
            myParent.Show();
            this.Close();
        }


        private void liczniki()
        {
            if (listcontrol_id != "")
            {
                //string etykiet = (string)BazaDanychExternal.Wyczytaj_Jedna_Wartosc("SELECT count(1) as licznik FROM list_control l left join listcontrol_palety lp on lp.listcontrol_id=l.id left join etykiety e on e.paleta_id=lp.paleta_id  WHERE l.id=" + listcontrol_id + " ");
                //string przyjetych = (string)BazaDanychExternal.Wyczytaj_Jedna_Wartosc("SELECT sum(if(e.ilosc is not null,1,0)) as licznik  FROM list_control l left join listcontrol_palety lp on lp.listcontrol_id=l.id left join etykiety e on e.paleta_id=lp.paleta_id  WHERE l.id=" + listcontrol_id + " ");
                //string przyjetych = (string)BazaDanychExternal.Wyczytaj_Jedna_Wartosc("SELECT cast(concat(count(1),' / ', sum(if(e.ilosc is not null,1,0))) as char) as licznik FROM list_control l left join listcontrol_palety lp on lp.listcontrol_id=l.id left join etykiety e on e.paleta_id=lp.paleta_id  WHERE l.id=" + listcontrol_id + " ");

                //licznik_label.Text = etykiet + " / " + przyjetych;
            }
            
        
        }
        int pole_tekstowe = 0;

        



        int TrybSkanu = 0;
        // 0 = Etykieta
        // 1 = GTIN 
        // 2 = SSC
        // 3 = LOT
        // 4 = DW
        // 5 = QT



        private void TworzenieDL_Load(object sender, EventArgs e)
        {

            LISTA_KONTROLNA_testbox.Focus();
        }



        private void GotFocus_TextBox(object sender, EventArgs e)
        {
            Skaner.Przewij_Skanowanie();
            TextBox Pole_Tekstowe = (TextBox)sender;

            AktualnyTextBox = Pole_Tekstowe;

            //if (DW == Pole_Tekstowe)
            //{

            //    if (!(dataprod.Text == "" || dataprod.Text == "RRMMDD"))
            //    {
            //        // sprawdź poprawność daty

            //        try
            //        {
            //            DateTime gg = DateTime.ParseExact("20" + dataprod.Text, "yyyyMMdd", null);
            //            if (ilosc_dni_przydatnosci != "0")
            //            {
            //                DW.Text = gg.AddDays(Convert.ToDouble(ilosc_dni_przydatnosci)).ToString("yyMMdd"); 
            //                //MessageBox.Show("DW:" + DW.Text);
            //                opak_real.Focus();
            //            }
            //        }
            //        catch (Exception ex)
            //        {
            //            MessageBox.Show("Błędna data produkcji.");
            //            dataprod.Text = "";
            //        }
            //    }

            //}
            if (Pole_Tekstowe.Text == "RRMMDD")
            {
                Pole_Tekstowe.Text = "";
            }


            ZacznijSkanowanie();
        }

        private void LostFocus_TextBox(object sender, EventArgs e)
        {
            AktualnyTextBox = null;
            Skaner.Przewij_Skanowanie();

            TextBox Pole_Tekstowe = (TextBox)sender;

            if (LISTA_KONTROLNA_testbox == Pole_Tekstowe)
            {
                if (LISTA_KONTROLNA_testbox.Text != "")
                {

                    string zapytanie = "SELECT l.id,l.listcontrol_system_id,l.awizacje_id FROM list_control l WHERE l.id='" + LISTA_KONTROLNA_testbox.Text + "' limit 1;";
                    object obj2 = BazaDanychExternal.Wyczytaj_Tabele(zapytanie);
                    DataTable tabela4 = (DataTable)obj2;

                    if (tabela4.Rows.Count == 0)
                    {
                        MessageBox.Show("Nie ma LK " + LISTA_KONTROLNA_testbox.Text);
                        LISTA_KONTROLNA_testbox.Text = "";
                        return;
                    }
                    else
                    {
                        listcontrol_id = tabela4.Rows[0]["id"].ToString();
                        awizacje_id = tabela4.Rows[0]["awizacje_id"].ToString();
                        liczniki();
                    }
                    if (Wlasciwosci.system_id_id == "")
                    {
                        Wlasciwosci.system_id_id = tabela4.Rows[0]["listcontrol_system_id"].ToString();
                    }
                }
            }

            

            if (kod == Pole_Tekstowe && kod.Text != "")
            {
                string zapytanie = "SELECT k.id,k.kod, k.ean, k.ilosc_w_opakowaniu, k.gln, k.ean_jednostki,k.ean_opakowanie_zbiorcze, k.ilosc_dni_przydatnosci,k.wymagana_partia, k.wymagana_data_waznosci, k.wymagana_dataprod FROM kody k WHERE k.system_id=\"" + Wlasciwosci.system_id_id + "\" and (TRIM(LEADING '0' FROM ean)=\"" + kod.Text.TrimStart('0') + "\"  or TRIM(LEADING '0' FROM ean_opakowanie_zbiorcze)=\"" + kod.Text.TrimStart('0') + "\" or TRIM(LEADING '0' FROM ean_jednostki)=\"" + kod.Text.TrimStart('0') + "\" or TRIM(LEADING '0' FROM kod)=\"" + kod.Text.TrimStart('0') + "\") and k.active=1 ORDER BY k.id asc ;";
                object obj2 = BazaDanychExternal.Wyczytaj_Tabele(zapytanie);
                DataTable tabela1 = (DataTable)obj2;

                if (tabela1.Rows.Count == 0)
                {
                    MessageBox.Show("Brak kodu w kartotece. Należy dodać lub pzypisać EAN do kodu. ");
                    return;
                }
                else
                {
                    if (tabela1.Rows.Count == 1)
                    {
                        kod.Text = tabela1.Rows[0]["kod"].ToString();
                        opak_real.Focus();
                    }
                    else
                    {

                        PoleWybor XA = new PoleWybor(this, tabela1);

                        if (XA.ShowDialog() == DialogResult.OK)
                        {
                            if (XA.wartosc_wybrana != "")
                            {
                                kod.Text = XA.wartosc_wybrana;
                                opak_real.Focus();
                            }
                        }
                    }

                    zapytanie = "SELECT k.id,k.kod, k.ean, k.ilosc_w_opakowaniu, k.gln, k.ean_jednostki,k.ean_opakowanie_zbiorcze, k.ilosc_dni_przydatnosci,k.wymagana_partia, k.wymagana_data_waznosci, k.wymagana_dataprod,ifnull(s.nazwa,'') as status_jakosci_domyslny_nazwa FROM kody k left join status_system s on k.status_jakosci_domyslny=s.id WHERE k.system_id=\"" + Wlasciwosci.system_id_id + "\" and (TRIM(LEADING '0' FROM ean)=\"" + kod.Text.TrimStart('0') + "\" or TRIM(LEADING '0' FROM ean_jednostki)=\"" + kod.Text.TrimStart('0') + "\" or TRIM(LEADING '0' FROM ean_opakowanie_zbiorcze)=\"" + kod.Text.TrimStart('0') + "\" or TRIM(LEADING '0' FROM kod)=\"" + kod.Text.TrimStart('0') + "\") and k.active=1 ORDER BY k.id desc limit 1;";
                    object obj3 = BazaDanychExternal.Wyczytaj_Tabele(zapytanie);
                    DataTable tabela = (DataTable)obj3;

                    if (tabela.Rows.Count == 0)
                    {
                        MessageBox.Show("Brak kodu w kartotece. Należy dodać lub pzypisać EAN do kodu. ");
                        return;
                    }
                    else
                    {
                        if (!(tabela.Rows[0]["status_jakosci_domyslny_nazwa"].ToString() == "" || tabela.Rows[0]["status_jakosci_domyslny_nazwa"].ToString() == "OK"))
                        {
                            MessageBox.Show("Domyślny status:" + tabela.Rows[0]["status_jakosci_domyslny_nazwa"].ToString());
                        }
                        if (!(tabela.Rows[0]["ilosc_w_opakowaniu"].ToString() == "" || tabela.Rows[0]["ilosc_w_opakowaniu"].ToString() == "0"))
                        {
                            ilosc_w_opakowaniu = tabela.Rows[0]["ilosc_w_opakowaniu"].ToString();
                        }
                        ilosc_dni_przydatnosci = tabela.Rows[0]["ilosc_dni_przydatnosci"].ToString();
                        wymagaj_data_waznosci = tabela.Rows[0]["wymagana_data_waznosci"].ToString();
                        wymagaj_dataprod = tabela.Rows[0]["wymagana_dataprod"].ToString();
                        wymagaj_lot = tabela.Rows[0]["wymagana_partia"].ToString();
                        kod.Text = tabela.Rows[0]["kod"].ToString();

                    }
                }
            }

            if (dataprod == Pole_Tekstowe)
            {
                if (!(dataprod.Text == "" || dataprod.Text == "RRMMDD") || dataprod.Text == "000000")
                {
                    // sprawdź poprawność daty
                    dataprod.Text = checkLastDay(dataprod.Text);
                    try
                    {
                        DateTime gg = DateTime.ParseExact("20" + dataprod.Text, "yyyyMMdd", null);
                        if (ilosc_dni_przydatnosci != "0")
                        {
                            if ((DW.Text == "" || DW.Text == "RRMMDD") || DW.Text == "000000")
                            {
                                DW.Text = gg.AddDays(Convert.ToDouble(ilosc_dni_przydatnosci)).ToString("yyMMdd");
                            }
                            
                        
                        }
                    }
                    catch (Exception ex)
                    {
                        MessageBox.Show("Błędna data produkcji.");
                        dataprod.Text = "";
                    }
                }
                else
                {
                    dataprod.Text = "RRMMDD";
                }

            }
            if (DW == Pole_Tekstowe && !(DW.Text == "" || DW.Text == "RRMMDD" || DW.Text == "0" || DW.Text == "000000"))
            {
                if (DW.Text.Length == 4)
                {
                    DW.Text += DW.Text + "00";
                }
                DW.Text = checkLastDay(DW.Text);

                // sprawdź poprawność daty
                try
                {
                    DateTime gg = DateTime.ParseExact("20" + DW.Text, "yyyyMMdd", null);
                }
                catch (Exception ex)
                {
                    MessageBox.Show("Błędna data ważności.");
                    DW.Text = "RRMMDD";
                }
            }
            /*
        else
        {
            DW.Text = "RRMMDD";
        } */

            if (LOT == Pole_Tekstowe)
            {
                // sprawdź poprawność daty


                if (!(decode_lot_rok == "0,0" || decode_lot_miesiac == "0,0" || LOT.Text == "" || LOT.Text == "0"))
                {
                    //MessageBox.Show("decode_lot_rok:" + decode_lot_rok);
                    //MessageBox.Show("LOT.Text:" + LOT.Text);
                    string dataprod_lokal = "";
                    try
                    {
                        string[] rok_params = decode_lot_rok.Split(',');
                        string[] miesiac_params = decode_lot_miesiac.Split(',');
                        //dataprod_lokal = "" + LOT.Text.Substring(Convert.ToInt32(rok_params[0]), Convert.ToInt32(rok_params[1])) + LOT.Text.Substring(Convert.ToInt32(miesiac_params[0]), Convert.ToInt32(miesiac_params[1])) + "01";
                        dataprod_lokal = "" + LOT.Text.Substring(Convert.ToInt32(rok_params[0]), Convert.ToInt32(rok_params[1])) + LOT.Text.Substring(Convert.ToInt32(miesiac_params[0]), Convert.ToInt32(miesiac_params[1]));
                        string[] dzien_params = decode_lot_dzien.Split(',');
                        if (decode_lot_dzien == "0,0") { dataprod_lokal += "01"; } else { dataprod_lokal += LOT.Text.Substring(Convert.ToInt32(dzien_params[0]), Convert.ToInt32(dzien_params[1])); }

                        DateTime gg = DateTime.ParseExact("20" + dataprod_lokal, "yyyyMMdd", null);
                        dataprod.Text = dataprod_lokal;
                        if (ilosc_dni_przydatnosci != "0")
                        {
                            DW.Text = gg.AddDays(Convert.ToDouble(ilosc_dni_przydatnosci)).ToString("yyMMdd");
                        }
                    }
                    catch (Exception ex)
                    {
                        MessageBox.Show("Błędna data produkcji z lotu");
                        dataprod.Text = "";
                    }

                }


            }
        }



        string pobierz_palete(CheckBox paleta_operacja, string paleta_id_local, string typypalet_id_local)
        {
            string j_skladowania_id = "1";
            if (typypalet_id_local == "2") j_skladowania_id = "2";

            if (paleta_operacja.Checked == true)
            {
                return paleta_id_local;
            }
            else
            {
                BazaDanychExternal.DokonajUpdate("replace into palety(id,typypalet_id, ilosc, j_skladowania_id) values('" + paleta_id_local + "','1','" + typypalet_id_local + "','" + j_skladowania_id + "')");
            }

            return paleta_id_local;
        }



        string increment_docnumber(string name)
        {
            string paleta_id = (string)BazaDanychExternal.Wyczytaj_Jedna_Wartosc("SELECT d.last FROM docnumber d WHERE d.name='" + name + "';");
            BazaDanychExternal.Wyczytaj_Jedna_Wartosc("update docnumber d set last=last+1 WHERE d.name='" + name + "'; ");
            return paleta_id;
        }




        /*
        private void wyszukaj_palety()
        {
            string zapytanie = "SELECT t.id, substr(t.opis,1,5) as paleta FROM typypalet t WHERE t.id<=3";
            object obj2 = BazaDanychExternal.Wyczytaj_Tabele(zapytanie);
            DataTable tabela = (DataTable)obj2;

            for (byte k = 0; k < tabela.Rows.Count; k++)
            {
                _paleta_wybor.Add(tabela.Rows[k]["paleta"].ToString());
                _paleta[k] = Convert.ToInt32(tabela.Rows[k]["id"]);
            }
            if (_paleta_wybor.Count == 0)
            {
                MessageBox.Show("Brak palet");
            }
            else
            {
                BindingSource bs = new BindingSource();
                bs.DataSource = _paleta_wybor;
                paleta_comboBox1.DataSource = bs;
            }
        }
        */


        string wyszukaj_kod()
        {
            string kod_local = "";
            if (kod.Text != "")
            {
                string zapytanie = "SELECT k.id,k.kod FROM kody k WHERE k.system_id='" + Wlasciwosci.system_id_id + "' and (ean='" + kod.Text + "' or ean_jednostki='" + kod.Text + "' or ean_opakowanie_zbiorcze='" + kod.Text + "' or kod='" + kod.Text + "') and k.active=1 ORDER BY k.id desc limit 1;";
                object obj2 = BazaDanychExternal.Wyczytaj_Tabele(zapytanie);
                DataTable tabela = (DataTable)obj2;

                if (tabela.Rows.Count == 0)
                {


                    if (Wlasciwosci.system_id_id == "")
                    {
                        MessageBox.Show("Proszę wczytać pierwszą etykietę");
                        return kod_local;
                    }

                    MessageBox.Show("Brak kodów w kartotece. Należy przypisać EAN do kodu");

                    KodyAktualizacja nowy = new KodyAktualizacja(this);
                    nowy.Show();
                    this.Hide();
                    Zakoncz_Skanowanie();
                    return kod_local;
                }
                else
                {
                    kod_local = tabela.Rows[0]["id"].ToString();
                    wymagaj_lot = tabela.Rows[0]["id"].ToString();
                    wymagaj_dataprod = tabela.Rows[0]["id"].ToString();
                    wymagaj_data_waznosci = tabela.Rows[0]["id"].ToString();


                }
            }
            return kod_local;

        }

        private void button2_Click(object sender, EventArgs e)
        {

            if (BazaDanychExternal.SprawdzCzyIstniejePolaczenie() == false)
            {
                MessageBox.Show("Brak połączenia. Podejdź do strefy Wifi i spróbuj ponownie");
                return;

            }

            


           




            if (QT.Text == "0" || QT.Text == "")
            {
                MessageBox.Show("Brak ilości!!");
                return;
            }

            if (wymagaj_data_waznosci == "1" && (DW.Text == "" || DW.Text == "RRMMDD"))
            {
                MessageBox.Show("Dla tego kodu wymagana data waznosci!");
                return;
            }
            if (wymagaj_dataprod == "1" && (dataprod.Text == "" || dataprod.Text == "RRMMDD"))
            {
                MessageBox.Show("Dla tego kodu wymagana data produkcji!");
                return;
            }
            if (wymagaj_lot == "1" && LOT.Text == "")
            {
                MessageBox.Show("Dla tego kodu wymagany jest: LOT lub Partia lub Batch!");
                return;
            }

            if (SSC.Text != "")
            {
                
            }





            string kod_id = "";
            kod_id = wyszukaj_kod();

            if (kod_id == "")
            {
                return;
            }

            

            DataTable sprawdzenie_ilosci_palety_opakowania = (DataTable)BazaDanychExternal.Wyczytaj_Tabele("SELECT * FROM kody k WHERE k.id=" + kod_id + " AND k.system_id="+Wlasciwosci.system_id_id+" and (ilosc_w_opakowaniu=0 or ilosc_szt_palecie=0) limit 1");
            if (sprawdzenie_ilosci_palety_opakowania.Rows.Count == 1)
            {
                MessageBox.Show("Brak ilości szt w opakowaniu lub w palecie");
                //ZacznijSkanowanie();
                return;

            }






            if (SSC.Text == "" && Wlasciwosci.system_id_id == "23")
            {
                MessageBox.Show("Proszę uzupełnić pole 'SSC'");
                return;
            }
            if (LOT.Text == "" && Wlasciwosci.system_id_id == "9")
            {
                MessageBox.Show("Proszę uzupełnić pole 'LOT'");
                return;
            }
            if (DW.Text == "" && Wlasciwosci.system_id_id == "9")
            {
                MessageBox.Show("Proszę uzupełnić pole 'Data ważnosci'");
                return;
            }

            if (paleta_id_textbox.Text.Replace("DS", "") == "")
            {
                MessageBox.Show("Proszę uzupełnić palete DS");
                return;
            }







            string zapytanie = "";

            string lot_local = null;
            if (LOT.Text != "")
            {
                lot_local += "'" + LOT.Text + "'";
            }
            else
            {
                lot_local = "null";
            }


            label7.Text = LISTA_KONTROLNA_testbox.Text;
            //string listcontrol_id = "0";
            //listcontrol_id = (string)BazaDanychExternal.Wyczytaj_Jedna_Wartosc("select ee.listcontrol_id from etykiety ee where ee.id=" + LISTA_KONTROLNA_testbox.Text + " ");
            //MessageBox.Show("ee");
            //listcontrol_id = LISTA_KONTROLNA_testbox.Text;

            //licznik.Text = (string)BazaDanychExternal.Wyczytaj_Jedna_Wartosc("SELECT cast(concat(count(1),' / ', (SELECT count(1) FROM etykiety eee WHERE eee.listcontrol_id=" + listcontrol_id + " and !(eee.etykieta_klient is null  AND eee.data_waznosci is null AND eee.sscc is null AND eee.lot is null))) as char) as aa FROM etykiety e WHERE e.listcontrol_id=" + listcontrol_id + " ");
            
            //licznik_label.Text = (string)BazaDanychExternal.Wyczytaj_Jedna_Wartosc(" SELECT cast(concat(count(1)) as char) as licznik FROM etykiety e WHERE e.listcontrol_id=" + listcontrol_id + " ");
            //licznik.Text = licznik_local.ToString();
            
            //MessageBox.Show("ff");

            //if (poprzednia_pal_checkBox1.Checked == false)
            //{
            //    paleta_id_textbox.Text = "DS" + increment_docnumber("nrpalety");
            //}



            prev_kod = kod.Text;
            prev_lot = LOT.Text;
            prev_data_waznosci = DW.Text;
            prev_sztuk = QT.Text;
            prev_opak = opak_real.Text;


            string pusty = "";
            //ETYKIETA.Text = pusty;
            SSC.Text = pusty;
            LOT.Text = pusty;
            SKAN_GS1.Text = pusty;
            DW.Text = pusty;
            dataprod.Text = pusty;
            QT.Text = pusty;
            kod.Text = pusty;
            ilosc_w_opakowaniu = "1";
            opak_real.Text = pusty;
            ilosc_dni_przydatnosci = "0";
            kod_id = pusty;
            wymagaj_lot = "0";
            wymagaj_dataprod = "0";
            wymagaj_data_waznosci = "0";
            //MessageBox.Show("Dokonałem operację zapisu.");
            SKAN_GS1.Focus();




        }



        #region Skanowanie

        Thread Skanowanie = null;

        private void UstawSieNaNowePole()
        {
            if (TrybSkanu == 2)
            {
                TrybSkanu = 0;
            }
            else
            {
                TrybSkanu++;
            }
            TextBoxArray[TrybSkanu].Focus();
        }
        private string sprawdz_czy_null(string bb)
        {
            if (bb == "")
            {

                return "null";
            }
            else
            {
                

                try
                {
                    Convert.ToDouble(bb);
                    return bb;
                }
                catch
                {
                    return "'" + bb + "'";
                }
            }

        }

        private string DajDate(string gg)
        {

            gg = gg.Replace("'", "");
            //MessageBox.Show(gg);
            if (gg.Contains("null") == true)
            {
                return "null";
            }
            if (gg == "00-00-0000" || gg == "RRMMDD" || gg == "000000" || gg == "")
            {
                return "null";
            }
            DateTime gh = new DateTime();
            try
            {

                gh = DateTime.ParseExact("20" + gg, "yyyyMMdd", null);
                //MessageBox.Show(gg);
                //update += ",dataprod='" + gh.ToString("yyyy-MM-dd") + "'";



                //DateTime gh = DateTime.ParseExact(gg, "dd-MM-yyyy", null);
                if (gh.ToString("yyyy-MM-dd") == "0000-00-00")
                {
                    return "null";
                }
                gh = DateTime.ParseExact("20" + gg, "yyyyMMdd", null);
            }
            catch (Exception ex)
            {
                MessageBox.Show("" + ex.ToString());
            }




            return "'" + gh.ToString("yyyy-MM-dd") + "'";
        }


        private void ZacznijSkanowanie()
        {
            StringBuilder login = new StringBuilder();
            Skaner.UstawTryb_String(login);
            Skaner.Zacznij_Skanowanie();
            Skanowanie = new Thread(() => CheckString(login));
            Skanowanie.IsBackground = true;
            Skanowanie.Start();
        }

        private void CheckString(StringBuilder cc)
        {
            while (true)
            {
                if (cc.ToString() != "")
                {
                    this.Invoke(new EventHandler(delegate
                    {
                        dodawanie(cc.ToString());
                    }));
                    return;
                }
                Thread.Sleep(200);
            }
        }


        private void Zakoncz_Skanowanie()
        {
            {
                Skanowanie.Abort();
                Skaner.Przewij_Skanowanie();
            }
        }

        private void dodawanie(string ops)
        {
            //MessageBox.Show("TrybSkanu:" + TrybSkanu + "   ,   ops.Length:" + ops.Length);

            if (ops[0] != 'D' && ops[1] != 'S')
            {
                AktualnyTextBox.Text = ops;
            }
            else
            {
                paleta_id_textbox.Text = ops;
            }

            if (AktualnyTextBox == LISTA_KONTROLNA_testbox)
            {
                LISTA_KONTROLNA_testbox.Text.Replace("LK", "");
                paleta_id_textbox.Focus();
            }

            if (AktualnyTextBox == SKAN_GS1)
            {

                if (kod.Text == "")
                {

                    string zapytanie = "SELECT  ad.etykieta_klient, ad.kod as kod, (TRIM(TRAILING '.' FROM TRIM(TRAILING '0' from ad.ilosc))) as ilosc ,k.id as kod_id, ad.lot as lot, DATE_FORMAT(ad.dataprod,'%y%m%d') as dataprod, DATE_FORMAT(ad.data_waznosci,'%y%m%d') as data_waznosci FROM awizacje_dostaw_dane ad left join awizacje_dostaw_head ah on ah.id=ad.awizacje_dostaw_id left join kody k on ad.kod=k.kod and k.system_id=ah.system_id where TRIM(LEADING '0' FROM etykieta_klient) = TRIM(LEADING '0' FROM '" + ops.TrimStart('0') + "')  and ah.system_id='" + Wlasciwosci.system_id_id + "' order by ad.id desc limit 1;";
                    //MessageBox.Show(zapytanie);
                    object obj2 = BazaDanychExternal.Wyczytaj_Tabele(zapytanie);
                    DataTable tabela4 = (DataTable)obj2;

                    if (tabela4.Rows.Count > 0)
                    {
                        if (SSC.Text == "") SSC.Text = tabela4.Rows[0]["etykieta_klient"].ToString();
                        if (kod.Text == "") kod.Text = tabela4.Rows[0]["kod"].ToString();
                        if (QT.Text == "") QT.Text = tabela4.Rows[0]["ilosc"].ToString();
                        if (LOT.Text == "") LOT.Text = tabela4.Rows[0]["lot"].ToString();
                        if (DW.Text == "" || DW.Text == "RRMMDD") dataprod.Text = tabela4.Rows[0]["dataprod"].ToString();
                        if (DW.Text == "" || DW.Text == "RRMMDD") DW.Text = tabela4.Rows[0]["data_waznosci"].ToString();

                        
                            
                    }
                }

            }



            



            if (AktualnyTextBox == SKAN_GS1 && AktualnyTextBox.Text.Length>9)
            {

                //string cleanString = Regex.Replace(ops, @"[^a-zA-Z0-9\-\.\,]", "");
                ops = ops.Replace("\r\n", string.Empty);
                Dictionary<Etykieta_test.AII, string> ff = Etykieta_test.Parse(ops, false);

                foreach (Etykieta_test.AII tt in ff.Keys)
                {
                    //MessageBox.Show("Dla kodu:" + tt.AI + "   ,   Otrzymujemy wartość:" + ff[tt].ToString());
                    //Console.WriteLine("Dla kodu:" + tt.AI + "   ,   Otrzymujemy wartość:" + ff[tt].ToString());
                    if (tt.AI == "00") { SSC.Focus(); SSC.Text = ff[tt].ToString(); }
                    if (tt.AI == "91") { kod.Focus(); kod.Text = Regex.Replace(ff[tt].ToString(), @"[^a-zA-Z0-9\-\.\,\\\/]", ""); }
                    if (tt.AI == "02")
                    {
                        if (Regex.Replace(ff[tt].ToString(), @"[^a-zA-Z0-9\-\.\,\\\/]", "") != "00000000000000")
                        {
                            //MessageBox.Show("ddd:" + Regex.Replace(ff[tt].ToString(), @"[^a-zA-Z0-9\-\.\,\\\/]", ""));
                            kod.Focus(); kod.Text = Regex.Replace(ff[tt].ToString(), @"[^a-zA-Z0-9\-\.\,\\\/]", "");
                        }
                    }

                    if (tt.AI == "240" || tt.AI == "241")
                    {
                        //MessageBox.Show("cc:" + Regex.Replace(ff[tt].ToString(), @"[^a-zA-Z0-9\-\.\,\\\/]", ""));
                        //kod.Focus(); 
                        kod.Text = Regex.Replace(ff[tt].ToString(), @"[^a-zA-Z0-9\-\.\,\\\/]", "");
                    }

                    if (tt.AI == "11") { dataprod.Focus(); dataprod.Text = ff[tt].ToString(); }
                    if (tt.AI == "15" || tt.AI == "17")
                    {
                        //MessageBox.Show("Wykryłem datę ważnosci:"+ff[tt].ToString());
                        DW.Focus(); DW.Text = ff[tt].ToString();
                        //MessageBox.Show("Wykryłem datę ważnosci:" + DW.Text);
                    }
                    //if (tt.AI == "10" && AktualnyTextBox!=LOT) { LOT.Focus(); LOT.Text = ff[tt].ToString(); opak_real.Focus(); }

                    if (tt.AI == "10") { LOT.Focus(); LOT.Text = decode_prefix_lot + Regex.Replace(ff[tt].ToString(), @"[^a-zA-Z0-9\-\.\,\\\/]", ""); }



                    if ((tt.AI == "37" || tt.AI == "330d" || tt.AI == "3302") && opak_real.Text == "")
                    {
                        opak_real.Focus(); opak_real.Text = ff[tt].ToString();
                        if (!(ilosc_w_opakowaniu == "" || ilosc_w_opakowaniu == "0"))
                        {
                            try
                            {
                                QT.Text = (Convert.ToDouble(ff[tt].ToString()) * Convert.ToDouble(ilosc_w_opakowaniu)).ToString();
                            }
                            catch (Exception ex)
                            {
                                MessageBox.Show("Błędna ilość opakowań");
                                opak_real.Text = "";
                            }
                        }
                    }

                }
                SKAN_GS1.Text = "";
                SKAN_GS1.Focus();
            }

            

            if (AktualnyTextBox == kod)
            {
                AktualnyTextBox.Text = "";



                if (kod.Text == "")
                {

                    ops = ops.TrimStart('0');

                    string zapytanie = "SELECT k.id,k.kod, k.ean, k.ilosc_w_opakowaniu, k.gln, k.ean_jednostki, k.ilosc_dni_przydatnosci,k.wymagana_partia, k.wymagana_data_waznosci, k.wymagana_dataprod FROM kody k WHERE k.system_id=\"" + Wlasciwosci.system_id_id + "\" and (TRIM(LEADING '0' FROM ean)=\"" + ops + "\" or TRIM(LEADING '0' FROM ean_jednostki)=\"" + ops + "\" or TRIM(LEADING '0' FROM ean_opakowanie_zbiorcze)=\"" + ops + "\" or TRIM(LEADING '0' FROM kod)=\"" + ops + "\") and k.active=1 ORDER BY k.id asc ;";
                    object obj2 = BazaDanychExternal.Wyczytaj_Tabele(zapytanie);
                    DataTable tabela1 = (DataTable)obj2;

                    if (tabela1.Rows.Count == 0)
                    {
                        MessageBox.Show("Brak kodu w kartotece. Należy dodać lub pzypisać EAN do kodu. " + ops);
                        return;
                    }
                    else
                    {
                        if (tabela1.Rows.Count == 1)
                        {
                            kod.Text = tabela1.Rows[0]["kod"].ToString();
                            opak_real.Focus();
                        }
                        else
                        {

                            PoleWybor XA = new PoleWybor(this, tabela1);

                            if (XA.ShowDialog() == DialogResult.OK)
                            {
                                if (XA.wartosc_wybrana != "")
                                {
                                    kod.Text = XA.wartosc_wybrana;
                                    opak_real.Focus();
                                }
                            }
                        }

                        zapytanie = "SELECT k.id,k.kod, k.ean, k.ilosc_w_opakowaniu, k.gln, k.ean_jednostki, k.ilosc_dni_przydatnosci,k.wymagana_partia, k.wymagana_data_waznosci, k.wymagana_dataprod FROM kody k WHERE k.system_id=\"" + Wlasciwosci.system_id_id + "\" and (TRIM(LEADING '0' FROM ean)=\"" + kod.Text.TrimStart('0') + "\" or TRIM(LEADING '0' FROM ean_jednostki)=\"" + kod.Text.TrimStart('0') + "\" or TRIM(LEADING '0' FROM kod)=\"" + kod.Text.TrimStart('0') + "\") and k.active=1 ORDER BY k.id desc limit 1;";
                        object obj3 = BazaDanychExternal.Wyczytaj_Tabele(zapytanie);
                        DataTable tabela = (DataTable)obj3;

                        if (tabela.Rows.Count == 0)
                        {
                            MessageBox.Show("Brak kodu w kartotece. Należy dodać lub pzypisać EAN do kodu. ");
                            return;
                        }
                        else
                        {
                            if (!(tabela.Rows[0]["ilosc_w_opakowaniu"].ToString() == "" || tabela.Rows[0]["ilosc_w_opakowaniu"].ToString() == "0"))
                            {
                                ilosc_w_opakowaniu = tabela.Rows[0]["ilosc_w_opakowaniu"].ToString();
                            }
                            ilosc_dni_przydatnosci = tabela.Rows[0]["ilosc_dni_przydatnosci"].ToString();
                            wymagaj_data_waznosci = tabela.Rows[0]["wymagana_data_waznosci"].ToString();
                            wymagaj_dataprod = tabela.Rows[0]["wymagana_dataprod"].ToString();
                            wymagaj_lot = tabela.Rows[0]["wymagana_partia"].ToString();
                            kod.Text = tabela.Rows[0]["kod"].ToString();

                        }
                    }
                }
                else
                {
                    MessageBox.Show("By zeskanować usuń kod");
                }



            }

            if (ops[0] == 'D' && ops[1] == 'S')
            {
                //paleta_id_textbox.Text.Replace("DS", "");

                //string zapyt_nazwa_palety = "select opis from palety p left join typypalet t on p.typypalet_id=t.id where p.id = '" + PAL_ETYKIETA_SYS.Text.Replace("DS", "") + "'";
                string zapytanie_rodzaje = "SELECT t.id, t.opis as nazwa FROM typypalet t WHERE t.id!=0 ORDER BY t.kolejnosc_pal";
                //string ff = (string)BazaDanychExternal.Wyczytaj_Jedna_Wartosc(zapyt_nazwa_palety);


                PoleCombobox XC = new PoleCombobox(zapytanie_rodzaje, "Wybierz paletę", typpalety_nazwa_poprzedni);
                if (XC.ShowDialog() == DialogResult.OK)
                {
                    if (XC.wybierana_nazwa == "")
                    {
                        MessageBox.Show("Nie dokonano wyboru");
                        return;
                    }
                    typpalety_nazwa_poprzedni = XC.wybierana_nazwa;

                    BazaDanychExternal.DokonajUpdate("update palety p  set typypalet_id='" + XC.wybierane_id + "' where p.id = '" + paleta_id_textbox.Text.Replace("DS", "") + "'");
                }
                else
                {
                    return;
                }


                SKAN_GS1.Focus();
            }



        }

        #endregion

        private void button3_Click(object sender, EventArgs e)
        {
            if (Wlasciwosci.system_id_id == "")
            {
                MessageBox.Show("Proszę wczytać pierwszą etykietę");
                return;
            }
            KodyAktualizacja XA = new KodyAktualizacja(this);

            if (XA.ShowDialog() == DialogResult.OK)
            {
                kod.Text = XA.wybrany_kod;
                //kod.Focus();
                //LOT.Focus();
                //if (XA.komunikat != "")
                //{
                //    MessageBox.Show(XA.komunikat);
                //    ZacznijSkanowanie();
                //    return;
                //}
            }



            //starre
            /*
KodyAktualizacja nowy = new KodyAktualizacja(this);
nowy.Show();
this.Hide();
Zakoncz_Skanowanie();
            */
        }



        private void button6_Click(object sender, EventArgs e)
        {
            kod.Text = prev_kod;
            LOT.Focus();
        }

        private void button5_Click(object sender, EventArgs e)
        {
            LOT.Text = prev_lot;
        }

        private void button4_Click(object sender, EventArgs e)
        {
            DW.Text = prev_data_waznosci;
        }

        private void button7_Click(object sender, EventArgs e)
        {
            QT.Text = prev_sztuk;
        }

        private void button8_Click(object sender, EventArgs e)
        {
            opak_real.Text = prev_opak;
        }

        private void przelicz_numery(object sender, EventArgs e)
        {
            //@"^[0-9]*(?:\.[0-9]*)?$"


            try
            {
                if (System.Text.RegularExpressions.Regex.IsMatch(((TextBox)sender).Text, "[^0-9].[^0-9]"))
                {
                    MessageBox.Show("Tylko liczby.");
                    ((TextBox)sender).Text = ((TextBox)sender).Text.Remove(((TextBox)sender).Text.Length - 1, 1);
                }
                TextBox Pole_Tekstowe = (TextBox)sender;


                if (opak_real == Pole_Tekstowe)
                {
                    if (opak_real.Text == "" || opak_real.Text == "0" || ilosc_w_opakowaniu == "" || ilosc_w_opakowaniu == "0")
                    {
                        return;
                    }
                    QT.Text = (Convert.ToDouble(ilosc_w_opakowaniu) * Convert.ToDouble(opak_real.Text)).ToString();
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show("Błędna liczba");
                dataprod.Text = "";
            }
        }

        private void poprzednia_paleta_checkBox1(object sender, EventArgs e)
        {

        }

        private void QT_KeyDown(object sender, KeyEventArgs e)
        {
            if (e.KeyCode == Keys.Enter)
            {
                button2_Click(this, new EventArgs());
            }
        }

        private void opak_real_KeyDown(object sender, KeyEventArgs e)
        {
            if (e.KeyCode == Keys.Enter)
            {
                button2_Click(this, new EventArgs());
            }
        }

        private void button9_Click(object sender, EventArgs e)
        {
            


        }

        private static string checkLastDay(string pdate)
        {
            if (pdate.Substring(4, 2) == "00")
            {
                pdate = pdate.Substring(0, 4) + DateTime.DaysInMonth(Convert.ToInt32("20" + pdate.Substring(0, 2)), Convert.ToInt32(pdate.Substring(2, 2)));
            }
            return pdate;
        }

        private void button10_Click(object sender, EventArgs e)
        {
            if (paleta_id_textbox.Text.Length==0)
            {
                MessageBox.Show("Brak palety");
                return;
            }
            DialogResult result3 = MessageBox.Show("Czy usunąć zawartość " + paleta_id_textbox.Text ,
                                            "Czy usunąć?",
                                            MessageBoxButtons.YesNo,
                                            MessageBoxIcon.Question,
                                            MessageBoxDefaultButton.Button2);
            if (result3 == DialogResult.Yes)
            {
                BazaDanychExternal.DokonajUpdate("update etykiety  set paleta_id=null where active is null and system_id=" + Wlasciwosci.system_id_id + " and paleta_id=" + paleta_id_textbox.Text.Replace("DS", "") + " limit 5;");
            }
        }





    }
}