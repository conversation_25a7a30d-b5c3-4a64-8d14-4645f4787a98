﻿using System;
using System.Linq;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using System.Text.RegularExpressions;

namespace Tarczyn__Magazyn
{
    public partial class PoleLiczbowe : Form
    {
        double minimum = 0;

        double maximum = 0;
        public string komunikat = "";
        public double ilosc_wpisana = 0;
        public PoleLiczbowe(double ilosc, string komunikat)
        {
            InitializeComponent();
            //textBox1.Text = ilosc.ToString();
            textBox1.Focus();
            maximum = ilosc;
            minimum = 0;
            label1.Text = komunikat;
        }

        private void button1_Click(object sender, EventArgs e)
        {
            //this.DialogResult = DialogResult.Cancel;
            //return "przerwij";
        }

        private void Dalej_Click(object sender, EventArgs e)
        {
            //if (!Regex.IsMatch(textBox1.Text, @"\d"))
            //{
            //    MessageBox.Show("Pole ilość nie jest liczbą.");
            //    textBox1.Focus();
            //    return;
            //}
            textBox1.Text = textBox1.Text.Replace(",", ".");

            if (maximum != -1) // jak nie jest wymagana ilość maximum
            {
                if (Convert.ToDouble(textBox1.Text) <= minimum || Convert.ToDouble(textBox1.Text) > maximum)
                {
                    komunikat = "Błędna ilość";
                }               
            }
            ilosc_wpisana = Convert.ToDouble(textBox1.Text);
            
        }

        private void WyborBazy_Load(object sender, EventArgs e)
        {
            
            
        }

        private void tylko_numery(object sender, EventArgs e)
        {
            //if (System.Text.RegularExpressions.Regex.IsMatch(((TextBox)sender).Text, "[^0-9]"))
            if (System.Text.RegularExpressions.Regex.IsMatch(((TextBox)sender).Text,"[^0-9].[^0-9]"))
            {
                MessageBox.Show("Tylko liczby.");
                ((TextBox)sender).Text = ((TextBox)sender).Text.Remove(((TextBox)sender).Text.Length - 1, 1);
            }
        }
    }
}