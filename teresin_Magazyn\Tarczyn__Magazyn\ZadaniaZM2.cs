﻿using System;
using System.Linq;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using MySql.Data.MySqlClient;
using System.Text.RegularExpressions;
using System.Threading;
using System.Net;


using System.Collections;

using System.IO;


namespace Tarczyn__Magazyn
{
    public partial class ZadaniaZM2 : Form
    {
        public ZadaniaMain myParent = null;
        List<string> _hala = new List<string>();
        List<string> _regal = new List<string>();
        List<string> _poziom = new List<string>();
        List<string> Etykiety_dodane = new List<string>();
        public string poziom = "";
        public string prawa = "";
        public string lewa = "";

        public string hala = "";
        public string regal = "";
        public string miejsce = "";
        public string paleta_id = "";

        public string regal_ostatnio = "";

        public string wysokosc_opis = "";
        public string grupa_id = "";

        int nowe_m = 0;
        public string ostatnio_hala = "";
        public string ostatnio_regal = "";
        public string ostatnio_miejsce = "";
        public string id_zmiany_miejsca_niezrealizowane = "0";



        private Thread Skanowanie;
        private Thread Nasluchiwanie;

        StringBuilder next = null;


        Dictionary<string, string> rec = new Dictionary<string, string>();


        public ZadaniaZM2(ZadaniaMain MyParent)
        {
            //
            InitializeComponent();
            //this.etykieta_textbox.KeyPress += new KeyPressEventHandler(etykieta_textbox_KeyPress);
            this.myParent = MyParent;
            wms_wybrany.Text = Wlasciwosci.system_id;
            imie_nazwisk_label.Text = Wlasciwosci.wozek + ":" + Wlasciwosci.imie_nazwisko;
            //FERRERO_Synchronizacja.SprawdzWolnaNazwePliku();
            timer1.Enabled = false;


            Lokalizacja_label.Text = "H:" + myParent.hala + " " + myParent.regal + "-" + myParent.miejsce + "-" + myParent.poziom;
            //nosnik_text.Text = "DS"+myParent.paleta_id;
            zawartosc_label.Text = myParent.kod + " ; " + myParent.lot + " ; " + myParent.ilosc;


           
            
            nosnik_text.Text = myParent.paleta_id;
            textBox1.Focus();

            


            this.ZacznijSkanowanie();
        }

        private void Nasluchowanie_metoda()
        {
            EventHandler method = null; //to jest delegat

            while (true)
            {

                if (method == null)
                {
                    method = delegate
                    {
                        //bb += 1;
                        this.Nasluchowanie_wykonawca();
                    };
                }
                this.Invoke(method);
                //return;
                Thread.Sleep(10000);
            }

        }

        private void Nasluchowanie_wykonawca()
        {


            


        }






        private void etykieta_textbox_KeyPress(object sender, KeyPressEventArgs e)
        {
            if (e.KeyChar == (char)Keys.Back)
            {
                e.Handled = true;
                //etykieta_textbox.Text = "";
            }
            else
            {
                e.Handled = false;
                return;
            }
        }



       







        public static string Czy_miejsce_zajete(string hala_local, string regal_local, string miejsce_local, string poziom_local)
        {
            string zapytanie = "SELECT count(1) FROM etykiety e left join miejsca m on e.miejscep=m.id where (e.active=1) and hala='" + hala_local + "' and regal='" + regal_local + "' and miejsce='" + miejsce_local + "' and poziom='" + poziom_local + "'  and poziom!='A' ;";
            string aa = (string)BazaDanychExternal.Wyczytaj_Jedna_Wartosc(zapytanie);
            return aa;
        }
 

        



        //ok
        

         





        private void Zakoncz_Skanowanie(object sender, EventArgs e)
        {
            this.Skanowanie.Abort();
            Skaner.Przewij_Skanowanie();
        }

        private void powrot_Click(object sender, EventArgs e)
        {
            string zapyt = "update zadania z set pracownik_id=0,czas_pobrania='0000-00-00 00:00:00' WHERE z.id=" + myParent.zadanie_id + " limit 1";
            BazaDanychExternal.Wyczytaj_Jedna_Wartosc(zapyt);

            this.myParent.tryb_pracy = "pobieranie_pauza";


            //this.myParent.tryb_pracy = "pobieranie";
            this.myParent.ZacznijNasluchiwanie();
            this.Skanowanie.Abort();
            Skaner.Przewij_Skanowanie();
            Wlasciwosci.CurrentOperacja = "0";
            //myParent.wczytwanieEtykiet = false;
            this.myParent.Show();
            timer1.Enabled = false;
            this.Close();
                
            

        }



        private void button1_Anuluj(object sender, EventArgs e)
        {

        }

        public void ZacznijNasluchiwanie()
        {
            StringBuilder login = new StringBuilder();
            this.Nasluchiwanie = new Thread(() => this.Nasluchowanie_metoda());
            this.Nasluchiwanie.IsBackground = true;
            this.Nasluchiwanie.Start();
        }

        private void Zakoncz_Nasluchiwanie(object sender, EventArgs e)
        {
            this.Nasluchiwanie.Abort();
        }


        public void ZacznijSkanowanie()
        {
            StringBuilder login = new StringBuilder();
            Skaner.UstawTryb_String(login);
            Skaner.Zacznij_Skanowanie();
            this.Skanowanie = new Thread(() => this.CheckString(login));
            this.Skanowanie.IsBackground = true;
            this.Skanowanie.Start();
        }



        private void CheckString(StringBuilder cc)
        {
            EventHandler method = null; //to jest delegat

            while (true)
            {
                if (cc.ToString() != "")
                {
                    if (method == null)
                    {
                        method = delegate
                        {
                            this.dodawanie(cc.ToString());
                        };
                    }
                    this.Invoke(method);
                    return;
                }
                Thread.Sleep(200);
            }
        }


        private static int pobierz_id_miejsca(string hala_local, string regal_local, string miejsce_local, string poziom_local) //pobiera
        {
            //if(nr_etykiety) 
            string zapytanie = "";
            zapytanie = "select id, widoczne from miejsca m where hala='" + hala_local + "' and regal='" + regal_local + "' and miejsce='" + miejsce_local + "' and poziom='" + poziom_local + "' and widoczne=1 limit 1;";

            object obj2 = BazaDanychExternal.Wyczytaj_Tabele(zapytanie);

            DataTable tabela = (DataTable)obj2;


            if (tabela.Rows.Count < 1)
            {  //sprawdza 
                return 0;
            }
            else
            {
                if (tabela.Rows[0]["widoczne"] == "0") return -1;
                return Convert.ToInt32(tabela.Rows[0]["id"]);
            }
        }

        



        // 'Internal' - wyszukuje w bazie etykiete i wypełnia pola
        private void dodawanie(string ops)
        {
            Skaner.Przewij_Skanowanie();

            if (!(ops.Substring(0, 2) == "MP" || ops.Substring(0, 2) == "DS"))
            {
                MessageBox.Show("To nie jest miejsce ");
                ZacznijSkanowanie();
                return;
            }



            if (ops.Substring(0, 2) == "MP")
            {
                Regex regex = new Regex("-");
                string[] words = null;
                words = regex.Split(ops.Substring(3, ops.Length - 3));
                hala = words[0].ToString();
                if (words[1].ToString() != "0")// wyjątek jak miejsce jest 0
                {
                    regal = words[1].ToString().TrimStart(new Char[] { '0' });
                }
                else
                {
                    regal = words[1].ToString();
                }
                if (words[2].ToString() != "0")// wyjątek jak miejsce jest 0
                {
                    miejsce = words[2].ToString().TrimStart(new Char[] { '0' });
                }
                else
                {
                    miejsce = words[2].ToString();
                }

                   

                string zapytanie = "";
                zapytanie = "SELECT m.poziom FROM miejsca m where  poziom!='' and hala='" + hala + "' and regal='" + regal + "'  and miejsce='" + miejsce + "' and m.widoczne=1  GROUP BY m.poziom order by poziom DESC;";
                object obj2 = BazaDanychExternal.Wyczytaj_Tabele(zapytanie);
                DataTable tabela = (DataTable)obj2;

                if (tabela.Rows.Count < 1)
                {
                    MessageBox.Show("Brak takiego miejsca w bazie. Spróbuj ponownie.");
                    ZacznijSkanowanie();
                    return;

                }
                else if (tabela.Rows.Count == 1)
                {
                    this.ustaw_poziom(tabela.Rows[0]["poziom"].ToString());
                }
                else
                {
                    Wybierz_Poziom_ZM qw = new Wybierz_Poziom_ZM(this, hala, regal, miejsce);
                    qw.ShowDialog();
                    this.Hide();
                }
            }

            if (ops.Substring(0, 2) == "DS")
            {
                string zapyt = "select miejscep,hala,regal,miejsce,poziom from etykiety e left join miejsca m on e.miejscep=m.id WHERE e.paleta_id=" + ops.Replace("DS", "") + " order by e.active desc limit 1";
                
                 DataTable temp = (DataTable)BazaDanychExternal.Wyczytaj_Tabele(zapyt); //or active is null

                 if (temp.Rows.Count < 1)
                 {
                     MessageBox.Show("Brak zadania związanego danym nośnikiem");
                     ZacznijSkanowanie();
                     return;

                 }
                 else
                 {
                     hala = temp.Rows[0]["hala"].ToString();
                     regal = temp.Rows[0]["regal"].ToString();
                     miejsce = temp.Rows[0]["miejsce"].ToString();
                     poziom = temp.Rows[0]["poziom"].ToString();
                     this.ustaw_poziom(poziom);
                 }



                
            }          
        }




        public void ustaw_poziom(string aa)
        {
            //MessageBox.Show("FUN ustaw_poziom:"+aa);
            poziom = aa;
            //MessageBox.Show("Wybrane miejsce: Hala" + hala+" "+regal+"-"+miejsce+"-"+poziom);

                //if (BazaDanychExternal.SprawdzCzyIstniejePolaczenie())
                {



                    string zapytanie = "insert into historia_skanowania(wartosc,stamp,Serial,prac_id,operacja) values('" + poziom + "',sysdate(),'" + Wlasciwosci.SerialNumber + "','" + Wlasciwosci.id_Pracownika + "','" + Wlasciwosci.CurrentOperacja + "')";


                    if (hala.Replace(" ", "") == "" || regal.Replace(" ", "") == "" || miejsce.Replace(" ", "") == "" || poziom.Replace(" ", "") == "")
                    {
                        MessageBox.Show("Błąd formatowania miejsca paletowego. Proszę powtórzyć operację. [" + hala + " " + regal + " " + miejsce + " " + poziom);
                        return;
                    }

                    nowe_m = pobierz_id_miejsca(hala, regal, miejsce, poziom);
                    //MessageBox.Show(hala + "," + regal + "," + miejsce + "," + poziom);

                    if(myParent.zgodnosc_miejsca=="1"){
                        if (myParent.nowe_m != nowe_m)
                        {
                            MessageBox.Show("Tą paletę można odłożyć tylko w "+ Environment.NewLine+"H:" + hala + " " + regal + "-" + miejsce + "-" + poziom);
                            this.myParent.ZacznijNasluchiwanie();
                        }
                    }

                    if (nowe_m == 0)
                    {
                        MessageBox.Show("Brak takiego miejsca w bazie. Spróbuj ponownie." + nowe_m);
                        this.myParent.ZacznijNasluchiwanie();
                        //this.ZacznijSkanowanie();
                        //this.Show();
                        //return;
                    } else
                        if (Czy_miejsce_zajete(hala, regal, miejsce, poziom) != "0")
                        {
                            MessageBox.Show("Miejsce w systemie zajęte. Paletę wstaw w inne");
                            this.myParent.ZacznijNasluchiwanie();
                            //this.ZacznijSkanowanie();
                            //this.Show();
                            //miejsce_textBox1.Text = "";
                            //return;
                        }
                        else
                        {
                            myParent.Realizuj_Zmiane_Miejsca(myParent.paleta_id, "ZM", "", Wlasciwosci.imie_nazwisko, hala, regal, miejsce, poziom, myParent.paleta_id, nowe_m);

                            this.myParent.Show();                            
                            this.Close();
                            

                        }



                }
                //else
                //{
                //    MessageBox.Show("Brak zasiągu WIFI!!!");
                //}
            
        }






        



        private void timer1_Tick(object sender, EventArgs e)
        {
            this.Skanowanie.Abort();
            Skaner.Przewij_Skanowanie();

            //myParent.wczytwanieEtykiet = false;
            this.myParent.Show();
            timer1.Enabled = false;
            this.Close();


        }



        #region fullscreen
        private void fullscreenmode()
        {
            this.Width = Screen.PrimaryScreen.Bounds.Width;
            this.Height = Screen.PrimaryScreen.Bounds.Height;
            this.Left = 0;
            this.Top = 0;
        }
        #endregion

      


       






    }
}