<?php

include_once 'Db.class.php';
include_once 'funkcje.inc';
//error_reporting(E_ALL);
//ini_set('display_errors', 1);

$db = new Db();


$komunikat = "OK";

$listcontrol_id = $_GET['listcontrol_id'];
$scan = ltrim($_GET['scan'], '0');
$system_id = ltrim($_GET['system_id'], '0');

function szukaj_awizacje($listcontrol_id, $system_id, $db) {
    $sql = 'SELECT l.awizacje_id, l.listcontrol_system_id FROM list_control l WHERE l.id=' . $listcontrol_id; //having nr_dl is null
    //echo $sql;
    $result = $db->mGetResultAsXML($sql);
    $awizacje_id = 0;
    if (empty($result)) {
        $komunikat = "Brak takiej LK";
        return xml_from_indexed_array(array('komunikat' => $komunikat));
    }
    foreach ($result as $index => $aRow) {
        $awizacje_id = $aRow['awizacje_id'];
        if ($aRow['listcontrol_system_id'] != $system_id) {
            $komunikat = "LK jest innego kienta niż zalogowano";
            return xml_from_indexed_array(array('komunikat' => $komunikat));
        }
    }

    return $awizacje_id;
}

$awizacja_id = szukaj_awizacje($listcontrol_id, $system_id, $db);
if (empty($awizacja_id)) {
    $komunikat = "LK nie ma przypisanej Awizacji dostawy";
    return xml_from_indexed_array(array('komunikat' => $komunikat));
}


$kod = ltrim(szukaj_kod($scan, $system_id, $db),"0");

szukaj_paletyzacje($awizacja_id, $kod, $system_id, $db);

function szukaj_kod($scan, $system_id, $db) {

    $sql = "SELECT k.kod FROM kody k where  (TRIM(LEADING '0' FROM ean)='" . $scan . "' or TRIM(LEADING '0' FROM ean_jednostki)='" . $scan . "' or TRIM(LEADING '0' FROM k.ean_opakowanie_zbiorcze)='" . $scan . "' or TRIM(LEADING '0' FROM k.kod)='" . $scan . "' ) and k.active=1  and k.system_id=" . $system_id . " limit 1";
    //echo $sql;
    $result = $db->mGetResultAsXML($sql);
    $kod = "";
    if (empty($result)) {
        $komunikat = "Brak kodu lub nie przypisany EAN";
        return xml_from_indexed_array(array('komunikat' => $komunikat));
    }
    foreach ($result as $index => $aRow) {
        $kod = $aRow['kod'];
    }
    return $kod;
}

function szukaj_paletyzacje($awizacja_id, $kod, $system_id, $db) {

    $sql = "SELECT paletyzacja,ad.kod,k.kod_nazwa FROM awizacje_dostaw_dane ad left join awizacje_dostaw_head ah on ah.id=ad.awizacje_dostaw_id left join kody k on ah.system_id=k.system_id and  ad.kod=k.kod where awizacje_dostaw_id='" . $awizacja_id . "'  and (TRIM(LEADING '0' FROM ean)='" . $kod . "' or TRIM(LEADING '0' FROM ean_jednostki)='" . $kod . "' or TRIM(LEADING '0' FROM k.ean_opakowanie_zbiorcze)='" . $kod . "' or TRIM(LEADING '0' FROM k.kod)='" . $kod . "'  or TRIM(LEADING '0' FROM ad.kod)='" . $kod . "') and k.active=1  and k.system_id=" . $system_id . " limit 1";
    //echo $sql;
    $result = $db->mGetResultAsXML($sql);
    if (empty($result)) {
        $komunikat = "Brak kodu w Awizacji dostawy";
        return xml_from_indexed_array(array('komunikat' => $komunikat));
    }
    foreach ($result as $index => $aRow) {
        return xml_from_indexed_array(array('komunikat' => "OK", 'paletyzacja' => $aRow['paletyzacja'], 'kod' => $aRow['kod'], 'kod_nazwa' => $aRow['kod_nazwa']));
    }
}

return;



if (!empty($hu_sscc_number)) {


    $sql = "SELECT id,l.awizacje_id FROM list_control l WHERE l.id=14324";




    //$query = "select tt.id,tt.m_palet as lp,tt.transport_number,tt.delivery_number,CAST(hu_sscc_number as unsigned) as hu_sscc_number, (select count(*) from _hu ww where ww.delivery_number=tt.delivery_number and ww.transport_number=tt.transport_number) as ilosc_hu_dostawa, (select count(*) from _hu ww where ww.transport_number=tt.transport_number) as ilosc_tr_dostawa,(CASE WHEN tt.scan is null then 0 else 1 END) as zeskanowany, (CASE WHEN tt.scanout is null then 0 else 1 END) as zeskanowanyout, (select count(*) from _hu ww where ww.delivery_number=tt.delivery_number and ww.transport_number=tt.transport_number and ww.scan is not null) as ilosc_hu_dostawa_scan,(select count(*) from _hu ww where ww.transport_number=tt.transport_number and ww.scan is not null) as ilosc_tr_dostawa_scan from _hu tt,_dhd dh where TRIM(LEADING '0' FROM hu_sscc_number) ='" . $hu_sscc_number . "' and dh.delivery_number=tt.delivery_number";
    $query = "";
    //echo $query;
    $result = mysql_query($query, $conn);  //wy_wietla jakie kody b_dziemy zdejmowa_
    $przerwanie = 0;

    $wierszedok = mysql_num_rows($result);
    if ($wierszedok == 0) {
        $komunikat = "Brak HU o numerze : " . $hu_sscc_number;
    } else {
        while ($aRow = mysql_fetch_array($result)) {
            if ($aRow['zeskanowany'] == "1") {
                $komunikat = "Ten karton zostal juz zeskanowany.  ";
                $nr_palety = $aRow['lp'];
                $transport_number = $aRow['transport_number'];
                $delivery_number = $aRow['delivery_number'];
                $hu_sscc_number = $aRow['hu_sscc_number'];
                $ilosc_hu_dostawa = $aRow['ilosc_hu_dostawa'];
                $ilosc_tr_dostawa = $aRow['ilosc_tr_dostawa'];
                $ilosc_hu_dostawa_scan = $aRow['ilosc_hu_dostawa_scan'];
                $ilosc_tr_dostawa_scan = $aRow['ilosc_tr_dostawa_scan'];
            } else {
                $sql2 = 'update _hu set scan=now() where scan is null and id="' . $aRow['id'] . '";   ';
                //$komunikat.="" . $sql2;
                $result2 = mysql_query($sql2, $conn);

                $query3 = "select tt.id,tt.m_palet as lp,tt.transport_number,tt.delivery_number,CAST(hu_sscc_number as unsigned) as hu_sscc_number, (select count(*) from _hu ww where ww.delivery_number=tt.delivery_number and ww.transport_number=tt.transport_number) as ilosc_hu_dostawa, (select count(*) from _hu ww where ww.transport_number=tt.transport_number) as ilosc_tr_dostawa,(CASE WHEN tt.scan is null then 0 else 1 END) as zeskanowany, (CASE WHEN tt.scanout is null then 0 else 1 END) as zeskanowanyout, (select count(*) from _hu ww where ww.delivery_number=tt.delivery_number and ww.transport_number=tt.transport_number and ww.scan is not null) as ilosc_hu_dostawa_scan,(select count(*) from _hu ww where ww.transport_number=tt.transport_number and ww.scan is not null) as ilosc_tr_dostawa_scan from _hu tt,_dhd dh where TRIM(LEADING '0' FROM hu_sscc_number)='" . $hu_sscc_number . "' and dh.delivery_number=tt.delivery_number";

                //echo $query;
                $result3 = mysql_query($query3, $conn);
                while ($aRow3 = mysql_fetch_array($result3)) {
                    $nr_palety = $aRow3['lp'];
                    $transport_number = $aRow3['transport_number'];
                    $delivery_number = $aRow3['delivery_number'];
                    $hu_sscc_number = $aRow3['hu_sscc_number'];
                    $ilosc_hu_dostawa = $aRow3['ilosc_hu_dostawa'];
                    $ilosc_tr_dostawa = $aRow3['ilosc_tr_dostawa'];
                    $ilosc_hu_dostawa_scan = $aRow3['ilosc_hu_dostawa_scan'];
                    $ilosc_tr_dostawa_scan = $aRow3['ilosc_tr_dostawa_scan'];
                }
            }
        }
    }
} else {
    $komunikat = " Podaj nr HU!";
}
$komunikat .= //mysql_error();
//echo $komunikat;
header('Content-type: text/xml');
echo '<dane>';
echo '<komunikat>', htmlentities($komunikat), '</komunikat>';
echo '<nr_palety>', htmlentities($nr_palety), '</nr_palety>';
echo '<transport_number>', htmlentities($transport_number), '</transport_number>';
echo '<delivery_number>', htmlentities($delivery_number), '</delivery_number>';
echo '<hu_sscc_number>', htmlentities($hu_sscc_number), '</hu_sscc_number>';
echo '<ilosc_hu_dostawa>', htmlentities($ilosc_hu_dostawa), '</ilosc_hu_dostawa>';
echo '<ilosc_tr_dostawa>', htmlentities($ilosc_tr_dostawa), '</ilosc_tr_dostawa>';
echo '<ilosc_hu_dostawa_scan>', htmlentities($ilosc_hu_dostawa_scan), '</ilosc_hu_dostawa_scan>';
echo '<ilosc_tr_dostawa_scan>', htmlentities($ilosc_tr_dostawa_scan), '</ilosc_tr_dostawa_scan>';
echo '</dane>';

//zdejmowanie towaru
?>