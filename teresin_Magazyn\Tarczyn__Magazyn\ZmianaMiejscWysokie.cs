﻿using System;
using System.Linq;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using MySql.Data.MySqlClient;
using System.Text.RegularExpressions;
using System.Threading;
//using System.Data.SqlServerCe;
using System.Collections;


namespace Tarczyn__Magazyn
{
    public partial class ZmianaMiejscWysokie : Form
    {
        ActionMenu myParent = null;
        List<string> _hala = new List<string>();
        List<string> _regal = new List<string>();
        List<string> _poziom = new List<string>();
        private Thread Skanowanie;
        Dictionary<string, string> rec = new Dictionary<string, string>();

        bool czy_buffor = false;
        string doc_type_global = "";
        string doc_nr_global = "";
        string listcontrol_id = "";
        string zm_nr_global = "";
        string zm_data_global = "";
        string first_regal = "";

        string operac_id_global = "";
        int nowe_m = 0;

        int licznik = 0;
        int licz = 0;


        public ZmianaMiejscWysokie(ActionMenu MyParent)
        {
            InitializeComponent();
            this.myParent = MyParent;
            wms_wybrany.Text = Wlasciwosci.system_id;
            imie_nazwisk_label.Text = Wlasciwosci.wozek + ":" + Wlasciwosci.imie_nazwisko;
            Wlasciwosci.CurrentOperacja = "12";

            // jeśli jest połączenie to niech sprawdzi opisy hal i ewentualnie zsynchronizuje
            if (BazaDanychExternal.SprawdzCzyIstniejePolaczenie())
            {
                //    Sprawdz_zgodnosc_opisow();
                wypelnij_hala();
                wypelnij_regal("2"); //wypelnij_regal("1");
                wypelnij_poziom("1", first_regal);
                //wypelnij_poziom();
                string zapytanie = "SELECT last FROM docnumber d WHERE d.name='operacja_id';";

                operac_id_global = (string)BazaDanychExternal.Wyczytaj_Jedna_Wartosc(zapytanie);

                string zapyt = "update docnumber d set last=last+1 WHERE d.name='operacja_id'; ";
                BazaDanychExternal.Wyczytaj_Jedna_Wartosc(zapyt);
            }



            this.ZacznijSkanowanie();
        }


        private void wypelnij_hala()
        {
            _hala.Clear();
            string zapytanie = "";
            zapytanie = "SELECT m.hala FROM miejsca m where widoczne=1 GROUP BY m.hala ;";
            object obj2 = BazaDanychExternal.Wyczytaj_Tabele(zapytanie);
            DataTable tabela = (DataTable)obj2;

            for (byte k = 0; k < tabela.Rows.Count; k++)
            {
                _hala.Add(tabela.Rows[k]["hala"].ToString());
            }
            BindingSource bs = new BindingSource();
            bs.DataSource = _hala;
            hala_comboBox2.DataSource = bs;
        }

        private void wypelnij_regal(string hala_local)
        {
            _regal.Clear();
            string zapytanie = "";

            
            zapytanie = "SELECT m.regal FROM miejsca m where widoczne=1  and hala='"+hala_local+"' GROUP BY m.regal;"; //and LENGTH(regal)<=3
            object obj2 = BazaDanychExternal.Wyczytaj_Tabele(zapytanie);
            DataTable tabela = (DataTable)obj2;

            for (byte k = 0; k < tabela.Rows.Count; k++)
            {
                _regal.Add(tabela.Rows[k]["regal"].ToString());
                if (k == 0) first_regal = tabela.Rows[k]["regal"].ToString();
            }

            BindingSource bs = new BindingSource();
            bs.DataSource = _regal;
            DYST.DataSource = bs;
        }



        private void wypelnij_poziom(string hala_local,string regal_local)
        {
            _poziom.Clear();

            /*
            _poziom.Add("A");
            _poziom.Add("B");
            _poziom.Add("C");
            _poziom.Add("D");
            _poziom.Add("E");
            _poziom.Add("F");
            _poziom.Add("G");
            _poziom.Add("H");
            */

            string zapytanie = "";
            zapytanie = "SELECT m.poziom FROM miejsca m where  poziom!='' and hala='" + hala_local + "' and regal='" + regal_local + "'   and widoczne=1 GROUP BY m.poziom order by poziom;";
            object obj2 = BazaDanychExternal.Wyczytaj_Tabele(zapytanie);
            DataTable tabela = (DataTable)obj2;
            
            for (byte k = 0; k < tabela.Rows.Count; k++)
            {
                _poziom.Add(tabela.Rows[k]["poziom"].ToString());
            }
            

            BindingSource bs = new BindingSource();
            bs.DataSource = _poziom;
            poziom_comboBox4.DataSource = bs;
        }


        public static string Sprawdz_Czy_Strefa_Buffor(string etykieta)
        {
            string zapytanie = "";
            zapytanie = "SELECT count(1) FROM etykiety e left join miejsca m on e.miejscep=m.id where (e.etykieta_klient='" + etykieta + "' or e.id='" + etykieta + "') and poziom='BUFF' limit 1 ;";
            string aa = (string)BazaDanychExternal.Wyczytaj_Jedna_Wartosc(zapytanie);
            return aa;
        }

        private static string[] get_dokument(string etykieta) //pobiera
        {
            string zapytanie = "select doc_nr,doc_type,listcontrol_id from etykiety e where (etykieta_klient='" + etykieta + "' or id='" + etykieta + "') limit 1";
            object obj2 = BazaDanychExternal.Wyczytaj_Tabele(zapytanie);
            DataTable tabela = (DataTable)obj2;
            string[] bb = new string[3];
            bb[0] = "" + tabela.Rows[0]["doc_type"].ToString();
            bb[1] = tabela.Rows[0]["doc_nr"].ToString();
            bb[2] = tabela.Rows[0]["listcontrol_id"].ToString();
            return bb;
        }


        private static string[] get_max_zmianym_date() //pobiera
        {
            string zapytanie = "select max(doc_nr)+1 as max_doc_nr, DATE_FORMAT(now(),'%Y-%m-%d') as data_zm from zmianym;";
            object obj2 = BazaDanychExternal.Wyczytaj_Tabele(zapytanie);
            DataTable tabela = (DataTable)obj2;
            string[] bb = new string[2];
            bb[0] = "" + tabela.Rows[0]["max_doc_nr"].ToString();
            bb[1] = tabela.Rows[0]["data_zm"].ToString();
            return bb;
        }

        public static string Ile_Buffor(string doc_type, string doc_nr, string listcontrol_id)
        {
            string zapytanie = "SELECT count(1) FROM etykiety e left join miejsca m on e.miejscep=m.id where (e.listcontrol_id='" + listcontrol_id + "' or (doc_type='" + doc_type + "' and doc_nr='" + doc_nr + "')) and poziom='BUFF';";
            string aa = (string)BazaDanychExternal.Wyczytaj_Jedna_Wartosc(zapytanie);
            return aa;
        }

        public static string Ile_Dokument_Etykiet(string doc_type, string doc_nr, string listcontrol_id)
        {
            string zapytanie = "SELECT count(1) FROM etykiety e left join miejsca m on e.miejscep=m.id where (e.listcontrol_id='" + listcontrol_id + "' or (doc_type='" + doc_type + "' and doc_nr='" + doc_nr + "')) ;";
            string aa = (string)BazaDanychExternal.Wyczytaj_Jedna_Wartosc(zapytanie);
            return aa;
        }
        public static string Sprawdz_dokument_etykieta(string etykieta, string doc_type, string doc_nr, string listcontrol_id)
        {
            string zapytanie = "SELECT count(1) FROM etykiety e where (e.listcontrol_id='" + listcontrol_id + "' or (doc_type='" + doc_type + "' and doc_nr='" + doc_nr + "')) and (e.etykieta_klient='" + etykieta + "' or e.id='" + etykieta + "')  ;";
            string aa = (string)BazaDanychExternal.Wyczytaj_Jedna_Wartosc(zapytanie);
            return aa;
        }

        public static string Count_zm(string doc_nr)
        {
            //distinct paleta_id
            string zapytanie = "SELECT count(1) FROM zmianym z where z.doc_nr='" + doc_nr + "'   ;";
            string aa = (string)BazaDanychExternal.Wyczytaj_Jedna_Wartosc(zapytanie);
            return aa;
        }











        // do przerobienia na zmianym
        public void Realizuj_Zmiane_Miejsca(string etykieta, string zm_nr, string zm_type, string zm_date, string login, string hala, string regal, string miejsce, string poziom)
        {
            //MessageBox.Show(etykieta[0] + ";" + etykieta[1]);
            


            if (etykieta.Substring(0, 2)=="DS")
            {
                DataTable temp = (DataTable)BazaDanychExternal.Wyczytaj_Tabele("select e.id,e.system_id,miejscep,paleta_id,regal,miejsce,poziom,ifnull(e.active,3) as active  from etykiety e left join miejsca m on e.miejscep=m.id where e.paleta_id=" + etykieta.Replace("DS", "") + " and (active=1 or active is null )"); //


                if (zm_nr == "") { zm_nr = (string)BazaDanychExternal.Wyczytaj_Jedna_Wartosc("select max(doc_nr)+1 FROM zmianym"); }
                if (temp.Rows.Count > 0)
                {
                    Wlasciwosci.system_id_id = temp.Rows[0][1].ToString();
                    //string nowemiejsce = miejscepaletowe2();
                    nowe_m = 0;
                    nowe_m = pobierz_id_miejsca(hala, regal, miejsce, poziom);
                    licz = 0;
                    if (nowe_m != 0)
                    {
                        for (int t = 0; t < temp.Rows.Count; t++)
                        {
                           
                            BazaDanychExternal.DokonajUpdate("insert into zmianym(typ,doc_nr,pracownik_id,data,etykieta,system_id,stare_m,nowe_m,doc_internal,stat,tszm) values(" +
                                "'ZM'," + zm_nr + "," + Wlasciwosci.id_Pracownika + ",sysdate()," + temp.Rows[t][0].ToString() + "," + temp.Rows[t][1].ToString() + "," + temp.Rows[t][2].ToString() + "," + nowe_m + ",3,1,sysdate())");

                            //BazaDanychExternal.DokonajUpdate("insert into miejsca_fizyczne(etykieta_id, miejscep, miejsce_id,paleta_id) values ('" + temp.Rows[t][0].ToString() + "','" + nowe_m + "','" + nowe_m + "','" + temp.Rows[t][3].ToString() + "'  );");
                            //if (lewej.Checked == true) BazaDanychExternal.DokonajUpdate("insert into miejsca_fizyczne(etykieta_id, miejscep, miejsce_id,paleta_id) values ('" + temp.Rows[t][0].ToString() + "','" + nowe_m + "','" + nowe_m_lewa + "','" + temp.Rows[t][3].ToString() + "');");
                            //if (prawej.Checked == true) BazaDanychExternal.DokonajUpdate("insert into miejsca_fizyczne(etykieta_id, miejscep, miejsce_id,paleta_id) values ('" + temp.Rows[t][0].ToString() + "','" + nowe_m + "','" + nowe_m_prawa + "','" + temp.Rows[t][3].ToString() + "');");


                            if (t == 0)
                            {
                                
                                BazaDanychExternal.DokonajUpdate("insert into operacje(etykieta_id, doc_type, doc_nr, imie_nazwisko, typ_operacji,system_id,wozek,operac_id,ilosc) values('" + temp.Rows[t][0].ToString() + "','ZM','" + zm_nr + "','" + Wlasciwosci.imie_nazwisko + "','ZM','" + Wlasciwosci.system_id_id + "','" + Wlasciwosci.wozek + "','" + operac_id_global + "','1');");

                            }

                            if (temp.Rows[t]["active"].ToString()=="1"){
                                    licz += 1;
                            }
                            
                        }
                        label3.Text = licz.ToString();
                        
                        BazaDanychExternal.DokonajUpdate("update etykiety set miejscep = " + nowe_m + " where paleta_id=" + etykieta.Replace("DS", "") + " and (active=1 or active is null) ");
                        //licznik++;
                        last_result_text.Text = "Zmieniono: " + temp.Rows[0]["regal"] + "-" + temp.Rows[0]["miejsce"] + "-" + temp.Rows[0]["poziom"] + " -> " + regal + "-" + miejsce + "-" + poziom;
                        //lewej.Checked = false;
                        //lewej.Text = "";
                        //prawej.Checked = false;
                        //prawej.Text = "";

                    }
                    else
                    {
                        MessageBox.Show("Brak takiego miejsca w bazie. Spróbuj ponownie.");
                        return;
                    }


                }

                else
                {
                    MessageBox.Show("Brak takiej palety w systemie lub jest nieaktywna , spróbuj ponownie.");
                }
            } 
            else if (etykieta.Substring(0, 2)=="DL"){
                nowe_m = pobierz_id_miejsca(hala, regal, miejsce, poziom);
                string zapytanie = "update delivery set miejsce_kompletacji = " + nowe_m + " where id=" + etykieta.Replace("DL", "")+" limit 1";
                BazaDanychExternal.DokonajUpdate(zapytanie);
                last_result_text.Text = "Zmieniono:  " + regal + "-" + miejsce + "-" + poziom+ "dla " + etykieta;
            }
            else    
            {

                string zapytanie = "";
                zapytanie = "select IFNULL(e.miejscep,3) as miejscep ,IFNULL(e.active,3) as active,docout_type,docout_nr,e.id,m.hala,m.regal,m.miejsce,m.poziom,drobnicowe_miejsca from etykiety e left join docout d on e.docout_id=d.id left join miejsca m on e.miejscep=m.id left join systemy s on e.system_id=s.wartosc where (etykieta_klient='" + etykieta + "' or e.id='" + etykieta + "')  order by e.id desc limit 1";

                object obj2 = BazaDanychExternal.Wyczytaj_Tabele(zapytanie);
                if (obj2 == null)
                {
                    this.ZacznijSkanowanie();
                }
                DataTable table = (DataTable)obj2;
                nowe_m = pobierz_id_miejsca(hala, regal, miejsce, poziom);
                DateTime ts = DateTime.Now;

                if (table.Rows.Count < 1)  //sprawdza czy jest etykieta
                {
                    MessageBox.Show("Brak w bazie et: " + etykieta + ".");

                    /*
                    BazaDanychExternal.DokonajUpdate(
                            "insert into bledy_synchronizacja(tabela,data,bledy_synchronizacja_typy_id,kolumna1,kolumna2,kolumna3,kolumna4,kolumna5," +
                            "kolumna6,kolumna7,kolumna8,kolumna9,kolumna10,kolumna11,kolumna12,uwagi,hala_s ,regal_s, miejsce_s, poziom_s) VALUES ('zmianym',sysdate(),1," +
                            "'" + zm_type + "','" + zm_nr + "','" + Wlasciwosci.id_Pracownika + "','" + zm_date + "','" + etykieta + "','" + Wlasciwosci.system_id_id + "','0','" + nowe_m + "','1','1','" + ts.ToString("yyyy-MM-dd H:mm:ss") + "','','Brak_w_bazie','" + hala + "','" + regal + "','" + miejsce + "','" + poziom + "')");
                    */
                    //this.ZacznijSkanowanie();
                }
                else                     // czy jest wydana z systemu
                    if (table.Rows[0]["active"].ToString() == "0")
                    {
                        MessageBox.Show("Et" + etykieta + " wydana : " + table.Rows[0]["docout_type"].ToString() + " - " + table.Rows[0]["docout_nr"].ToString() + ". Dodana do offline");

                        /*
                        BazaDanychExternal.DokonajUpdate(
                            "insert into bledy_synchronizacja(tabela,data,bledy_synchronizacja_typy_id,kolumna1,kolumna2,kolumna3,kolumna4,kolumna5," +
                            "kolumna6,kolumna7,kolumna8,kolumna9,kolumna10,kolumna11,kolumna12,uwagi,hala_s ,regal_s, miejsce_s, poziom_s) VALUES ('zmianym',sysdate(),1," +
                            "'" + zm_type + "','" + zm_nr + "','" + Wlasciwosci.id_Pracownika + "','" + zm_date + "','" + etykieta + "','" + Wlasciwosci.system_id_id + "','0','" + nowe_m + "','1','1','" + ts.ToString("yyyy-MM-dd H:mm:ss") + "','','Nieaktywna','" + hala + "','" + regal + "','" + miejsce + "','" + poziom + "')");
                        */
                    }
                    else
                    {


                        //MessageBox.Show("pobierz_id_miejsca: " + nowe_m);
                        if (nowe_m == 0)
                        {
                            //nowe_m = dodaj_miejsce(hala, regal, miejsce, poziom);
                            //MessageBox.Show("Dodano nowe miejsce: Hala: " + hala + " " + regal + "-" + miejsce + "-" + poziom);
                            MessageBox.Show("Brak w bazie miejsca: Hala: " + hala + " " + regal + "-" + miejsce + "-" + poziom + ". Spróbuj jeszze raz.");
                            //this.ZacznijSkanowanie();
                            //return;
                        }
                        else
                        {

                            if (nowe_m.ToString() != table.Rows[0]["miejscep"].ToString())
                            {
                                if (table.Rows[0]["drobnicowe_miejsca"].ToString() == "1")
                                {
                                    //MessageBox.Show("drobnicowe_miejsca");
                                    string zap2 = "";
                                    zap2 = "SELECT ifnull(cast(max(paleta_id) as char),'brak') as paleta_id FROM etykiety e  where active=1 and system_id=" + Wlasciwosci.system_id_id + " and miejscep=" + nowe_m + ";";
                                    string pal_id_loc = (string)BazaDanychExternal.Wyczytaj_Jedna_Wartosc(zap2);
                                    //MessageBox.Show(pal_id_loc);
                                    if (pal_id_loc != "brak")
                                    {
                                        //MessageBox.Show("update etykiety e set e.paleta_id=" + pal_id_loc + " where e.id=" + table.Rows[0]["id"] + ";");
                                        BazaDanychExternal.DokonajUpdate("update etykiety e set e.paleta_id=" + pal_id_loc + " where e.id=" + table.Rows[0]["id"] + ";");
                                    }

                                }

                                BazaDanychExternal.DokonajUpdate("insert into operacje(etykieta_id, doc_type, doc_nr, imie_nazwisko, typ_operacji,system_id,wozek,operac_id,ilosc) values('" + table.Rows[0]["id"] + "','" + zm_type + "','" + zm_nr + "','" + Wlasciwosci.imie_nazwisko + "','ZM','" + Wlasciwosci.system_id_id + "','" + Wlasciwosci.wozek + "','" + operac_id_global + "','1');");

                                BazaDanychExternal.DokonajUpdate("update etykiety e set e.miejscep=" + nowe_m + " where e.id=" + table.Rows[0]["id"] + ";");
                                //if (table.Rows[0]["miejscep"].ToString() != "0")
                                //{
                                BazaDanychExternal.DokonajUpdate("insert into zmianym(typ,doc_nr,pracownik_id,data,etykieta,stare_m,nowe_m,doc_internal,stat,system_id) values ('" + zm_type + "','" + zm_nr + "','" + Wlasciwosci.id_Pracownika + "','" + zm_date + "','" + table.Rows[0]["id"] + "','" + table.Rows[0]["miejscep"].ToString() + "','" + nowe_m + "','Z','1'," + Wlasciwosci.system_id_id + ");");
                                //}


                                label2.Text = "Zmieniono: " + table.Rows[0]["regal"] + "-" + table.Rows[0]["miejsce"] + "-" + table.Rows[0]["poziom"] + " -> " + regal + "-" + miejsce + "-" + poziom;
                            }
                            else
                            {
                                label2.Text = "Jest:" + table.Rows[0]["regal"] + " - " + table.Rows[0]["miejsce"] + " - " + table.Rows[0]["poziom"];
                            }

                        }


                    }

            }



        }
        //ok
        private int pobierz_id_miejsca(string hala_local, string regal_local, string miejsce_local, string poziom_local) //pobiera
        {
            //if(nr_etykiety)
            string zapytanie = "";
            zapytanie = "select id from miejsca m where hala='" + hala_local + "' and regal='" + regal_local + "' and miejsce='" + miejsce_local + "' and poziom='" + poziom_local + "' limit 1;";
            
            object obj2 = BazaDanychExternal.Wyczytaj_Tabele(zapytanie);
            
            DataTable tabela = (DataTable)obj2;
            if (tabela.Rows.Count < 1)
            {  //sprawdza 
                return 0;
            }
            else
            {
                return Convert.ToInt32(tabela.Rows[0]["id"]);
            }


        }

        //private int dodaj_miejsce(string hala_local, string regal_local, string miejsce_local, string poziom_local) //pobiera
        //{
        //   return BazaDanychExternal.DokonajInsertLastId("insert into miejsca(hala,regal,miejsce,poziom,widoczne) values ('" + hala_local + "','" + regal_local + "','" + miejsce_local + "','" + poziom_local + "','1');");
        //}


























        private void Zakoncz_Skanowanie(object sender, EventArgs e)
        {
            this.Skanowanie.Abort();
            Skaner.Przewij_Skanowanie();
        }

        private void powrot_Click(object sender, EventArgs e)
        {
            Wlasciwosci.CurrentOperacja = "0";
            this.Skanowanie.Abort();
            Skaner.Przewij_Skanowanie();
            this.myParent.Show();
            this.Hide();
        }



        private void button1_Anuluj(object sender, EventArgs e)
        {

        }
        public void ZacznijSkanowanie()
        {
            StringBuilder login = new StringBuilder();
            Skaner.UstawTryb_String(login);
            Skaner.Zacznij_Skanowanie();
            this.Skanowanie = new Thread(() => this.CheckString(login));
            this.Skanowanie.IsBackground = true;
            this.Skanowanie.Start();
        }



        private void CheckString(StringBuilder cc)
        {
            EventHandler method = null; //to jest delegat

            while (true)
            {
                if (cc.ToString() != "")
                {
                    if (method == null)
                    {
                        method = delegate
                        {
                            this.dodawanie(cc.ToString());
                        };
                    }
                    this.Invoke(method);
                    return;
                }
                Thread.Sleep(200);
            }
        }









        private void dodawanie(string ops)
        {
            //Skanowanie.Abort();
            Skaner.Przewij_Skanowanie();



            if (ops.Substring(0, 2) == "MP")
            {
                Regex regex = new Regex("-");
                string[] words = null;
                words = regex.Split(ops.Substring(3, ops.Length - 3));

                if (_hala[hala_comboBox2.SelectedIndex].ToString() != words[0].ToString())
                {
                    wypelnij_regal(words[0].ToString());
                    wypelnij_poziom(words[0].ToString(), first_regal);
                }

                hala_comboBox2.SelectedIndex = hala_comboBox2.Items.IndexOf(words[0].ToString());
                DYST.SelectedIndex = DYST.Items.IndexOf(words[1].ToString());
                miejsce_textBox1.Text = words[2].ToString();
                poziom_comboBox4.SelectedIndex = poziom_comboBox4.Items.IndexOf("1");


            }
            else
                if (ops.Length < 23)
                {

                    //if (ops.Length <= 3)
                    //{
                    //    MessageBox.Show("To nie jest numer etykiety");
                    //    ZacznijSkanowanie();
                    //    return;
                    //}


                    //if (ops.Length == 20 || ops.Length == 21)
                    //{
                    //    ops = ops.Remove(0, 2);
                    //}
                    //if (ops.Length <= 3)
                    //{
                    //    MessageBox.Show("To nie jest numer etykiety");
                    //    ZacznijSkanowanie();
                    //    return;
                    //}

                    //if (((ops[0] == '1' && ops[1] == '5' && ops[2] == '9') || ((ops.Length <= 10) && ops.Length >= 5)) ||
                    //(ops[0] == '3' && ops[1] == '4' && ops[2] == '0') || (ops[0] == '1' && ops[1] == '8' && ops[2] == '0') ||
                    //    (ops[0] == '1' && ops[1] == '5' && ops[2] == '4'))
                    //{
                        //if (ops.Length < 23 && jestLiczba == true)  //bool isNumeric = int.TryParse("123", out n);

                    etykieta_textbox.Text = ops;
                    /*
                            try
                            {
                                //etykieta_textbox.Text = Convert.ToInt64(ops).ToString();
                            }
                            catch
                            {
                                ZacznijSkanowanie();
                                return;
                            }
                    */


                            Skaner.Przewij_Skanowanie();
                            if (etykieta_textbox.Text != "" && miejsce_textBox1.Text != "")
                            {
                                if (czy_buffor == false && zm_nr_global == "")
                                {
                                    string[] bb = new string[2];
                                    bb = get_max_zmianym_date();
                                    zm_nr_global = bb[0];
                                    zm_data_global = bb[1];
                                }



                                Realizuj_Zmiane_Miejsca(etykieta_textbox.Text, zm_nr_global, "ZM", zm_data_global, Wlasciwosci.imie_nazwisko, _hala[hala_comboBox2.SelectedIndex].ToString(), _regal[DYST.SelectedIndex].ToString(), miejsce_textBox1.Text, _poziom[poziom_comboBox4.SelectedIndex].ToString());


                                if (czy_buffor == false)
                                {
                                    label_head.Text = " ZM " + zm_nr_global + " , " + zm_data_global;
                                    licznik +=1;
                                    ilosc_etykiet.Text = "" + licznik; //Count_zm(zm_nr_global);
                                }

                                
                            }
                            else
                            {
                                MessageBox.Show("Pole etykiety lub miejsca jest puste.");
                            }
                            czysc();
                            //this.ZacznijSkanowanie();

                        

                    //}
                    //else
                    //{
                    //    MessageBox.Show("To nie jest numer etykiety" + ops + "    ,      " + ops.Length);
                    //}






                }
                else
                {
                    MessageBox.Show("To nie jest numer etykiety");
                }
            ZacznijSkanowanie();
        }














        private void hala_comboBox2_SelectedIndexChanged(object sender, EventArgs e)
        {
            wypelnij_regal(_hala[hala_comboBox2.SelectedIndex].ToString());
            wypelnij_poziom(_hala[hala_comboBox2.SelectedIndex].ToString(), first_regal);

            /*
            if (_hala[hala_comboBox2.SelectedIndex].ToString() == "1")
            {
                wypelnij_regal("1");
            }
            if (_hala[hala_comboBox2.SelectedIndex].ToString() == "2")
            {
                wypelnij_regal("2");
            }
            if (_hala[hala_comboBox2.SelectedIndex].ToString() == "3")
            {
                wypelnij_regal("3");
            }

            */

            if (_hala[hala_comboBox2.SelectedIndex].ToString() == "7")
            {
                DYST.SelectedIndex = DYST.Items.IndexOf("REJA");
                poziom_comboBox4.SelectedIndex = poziom_comboBox4.Items.IndexOf("1");
            }
            
        }





        private void czysc()
        {
            etykieta_textbox.Text = "";
        }



        private void timer1_Tick(object sender, EventArgs e)
        {
            this.Close();
            myParent.Show();
        }



        #region fullscreen
        private void fullscreenmode()
        {
            this.Width = Screen.PrimaryScreen.Bounds.Width;
            this.Height = Screen.PrimaryScreen.Bounds.Height;
            this.Left = 0;
            this.Top = 0;
        }
        #endregion

        private void button4_Click(object sender, EventArgs e)
        {
            dodawanie(etykieta_textbox.Text);
        }

        private void button1_Click(object sender, EventArgs e)
        {
            if (zm_nr_global != "")
            {

                string zapytanie = "";
                zapytanie = "select id,etykieta,stare_m from zmianym z where typ='ZM' and doc_nr='" + zm_nr_global + "'   order by id desc limit 1;";

                object obj2 = BazaDanychExternal.Wyczytaj_Tabele(zapytanie);

                DataTable table = (DataTable)obj2;
                if (table.Rows.Count < 1)  //sprawdza czy jest etykieta
                {
                    return;
                }
                else
                {
                    // czy jest wydana z systemu
                    //table.Rows[0]["active"].ToString() == "0";

                    BazaDanychExternal.DokonajUpdate("update etykiety e set miejscep='" + table.Rows[0]["stare_m"].ToString() + "'  where id='" + table.Rows[0]["etykieta"].ToString() + "' limit 1;");
                    BazaDanychExternal.DokonajUpdate("delete from zmianym where id='" + table.Rows[0]["id"].ToString() + "' limit 1;");

                }
                ilosc_etykiet.Text = "" + Count_zm(zm_nr_global);

            }


        }

        private void regal_comboBox3_SelectedIndexChanged(object sender, EventArgs e)
        {

            wypelnij_poziom(_hala[hala_comboBox2.SelectedIndex].ToString(), _regal[DYST.SelectedIndex].ToString());

        }

    } 
} 