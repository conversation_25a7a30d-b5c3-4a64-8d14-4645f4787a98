﻿using System;
using System.Linq;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using System.Threading;
using System.Text.RegularExpressions;
using System.Net;
using System.IO;

namespace Tarczyn__Magazyn
{
    public partial class PickingReprintDS : Form
    {

        
        ActionMenu parent = null;



        List<string> _regal = new List<string>();

        public PickingReprintDS(ActionMenu myParent)
        {
            if (Wlasciwosci.GNG != "")
            {
                BazaDanychExternal.ChangeHost(Wlasciwosci.GNG, Wlasciwosci.Varhost, Wlasciwosci.VarLoginMYSQL, Wlasciwosci.VarHasloMYSQL);
            }
            FullScreenMode.OknoOFF(this);
            InitializeComponent();
            parent = myParent;

            //ZacznijSkanowanie();

        }
        
        List<string> Etykiety = new List<string>();
        Thread Skanowanie = null;


        private void ZacznijSkanowanie()
        {

            StringBuilder login = new StringBuilder();
            Skaner.UstawTryb_String(login);
            Skaner.Zacznij_Skanowanie();
            Skanowanie = new Thread(() => CheckString(login));
            Skanowanie.IsBackground = true;
            Skanowanie.Start();
        }
        private void CheckString(StringBuilder cc)
        {
            while (true)
            {
                if (cc.ToString() != "")
                {
                    this.Invoke(new EventHandler(delegate
                    {
                        
                        dodawanie(cc.ToString());
                    }));
                    return;
                }
                Thread.Sleep(200);
            }
        }
        private void Zakoncz_Skanowanie()
        {
            try
            {
                Skanowanie.Abort();
                Skaner.Przewij_Skanowanie();
            }
            catch
            {
            }
        }

        private void Exit_Click_1(object sender, EventArgs e)
        {
            Zakoncz_Skanowanie();
            BazaDanychExternal.ChangeHost(Wlasciwosci.Varbaza, Wlasciwosci.Varhost, Wlasciwosci.VarLoginMYSQL, Wlasciwosci.VarHasloMYSQL);
            parent.Show();
            this.Close();
        }


        private void button2_Click(object sender, EventArgs e)
        {
          

        }


        private void dodawanie(string gg)
        {

            //Zakoncz_Skanowanie();
            

            //object test2 = BazaDanychExternal.Wyczytaj_Jedna_Wartosc("select wartosc4 from kolektor where wartosc1='" + gg + "' order by id desc LIMIT 1");


            //ZacznijSkanowanie();
            gg = gg.Substring(2, 18);
            //MessageBox.Show("" + gg);
            string url = "http://***********/wmsgg/public/reprint_sscc.php?sscc=" + gg;
            WebRequest request = WebRequest.Create(url);
            WebResponse response = request.GetResponse();
            Stream data = response.GetResponseStream();
            string html = String.Empty;
            using (StreamReader sr = new StreamReader(data))
            {
                html = sr.ReadToEnd();
            }

            //this.Close();
            ZacznijSkanowanie();
        }


        private void Zakoncz_Click(object sender, EventArgs e)
        {
     
        }

        private void WczytywanieNaLinie_Load_1(object sender, EventArgs e)
        {
            ZacznijSkanowanie();
        }

        private void button2_Click_1(object sender, EventArgs e)
        {
           
        }

        private void button1_Click(object sender, EventArgs e)
        {
            dodawanie(textBox1.Text);
            textBox1.Text = "";
        }





    }
}
