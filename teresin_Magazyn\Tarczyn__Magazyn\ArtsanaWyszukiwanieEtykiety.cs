﻿using System;
using System.Linq;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using MySql.Data.MySqlClient;
using System.Threading;
using System.Net;
using System.IO;
using System.Xml;

namespace Tarczyn__Magazyn
{
    public partial class ArtsanaWyszukiwanieEtykiety : Form
    {
        ActionMenu myParent = null;
        string nr_etykiety = null;
        string aktywna = null;
        private Thread Skanowanie;
        string operac_id_global = "";

        public ArtsanaWyszukiwanieEtykiety(ActionMenu MyParent)
        {

            InitializeComponent();
            this.myParent = MyParent;
            wms_wybrany.Text = Wlasciwosci.system_id;
            imie_nazwisk_label.Text = Wlasciwosci.wozek + ":" + Wlasciwosci.imie_nazwisko;
            Wlasciwosci.CurrentOperacja = "3";
            this.ZacznijSkanowanie();

            string zapytanie = "SELECT last FROM docnumber d WHERE d.name='operacja_id';";

            operac_id_global = (string)BazaDanychExternal.Wyczytaj_Jedna_Wartosc(zapytanie);

            string zapyt = "update docnumber d set last=last+1 WHERE d.name='operacja_id'; ";
            BazaDanychExternal.Wyczytaj_Jedna_Wartosc(zapyt);
            BazaDanychExternal.DokonajUpdate("insert into operacje(etykieta_id, doc_type, doc_nr, imie_nazwisko, typ_operacji,system_id,wozek,operac_id,ilosc) values('0','','','" + Wlasciwosci.imie_nazwisko + "','PR_AR','" + Wlasciwosci.system_id_id + "','" + Wlasciwosci.wozek + "','" + operac_id_global + "','0');");



        }

        private void czysc()
        {
            nr_etykiety_opis.Text = "";
            label4.Text = "";
            kod.Text = "";
            ilosc.Text = "";
            adres.Text = "";
            partia.Text = "";
        }

        private void CheckString(StringBuilder cc)
        {
            EventHandler method = null;
            while (true)
            {
                if (cc.ToString() != "")
                {
                    if (method == null)
                    {
                        method = delegate
                        {
                            this.wyszukaj_etykiete(cc.ToString());
                        };
                    }
                    this.Invoke(method);
                    return;
                }
                Thread.Sleep(200);
            }
        }

        private void wyszukaj_etykiete(string ops)
        {

            string URL = "http://172.6.1.249/wmsgg/public/skaner_api/artsana_corssdocking_in.php?hu_sscc_number=" + ops;
            const string data = @"{""object"":{""name"":""Title""}}";

            HttpWebRequest request = (HttpWebRequest)WebRequest.Create(URL);
            request.Method = "POST";
            request.ContentType = "application/json";
            request.ContentLength = data.Length;
            StreamWriter requestWriter = new StreamWriter(request.GetRequestStream(), System.Text.Encoding.ASCII);
            requestWriter.Write(data);
            requestWriter.Close();

            try
            {
                // get the response
                WebResponse webResponse = request.GetResponse();
                Stream webStream = webResponse.GetResponseStream();
                StreamReader responseReader = new StreamReader(webStream);
                string response = responseReader.ReadToEnd();
                responseReader.Close();

                XmlDocument doc1 = new XmlDocument();
                doc1.Load(new StringReader(response));
                //XmlNode node = doc1.SelectSingleNode("/dane/komunikat/");
                XmlNodeList xmlnode;
                xmlnode = doc1.GetElementsByTagName("dane");
                foreach (XmlNode node in xmlnode)
                {
                    if (node["komunikat"].InnerText != "OK")
                    {
                        MessageBox.Show(node["komunikat"].InnerText);

                    }

                    label4.Text = node["nr_palety"].InnerText.ToString();
                    //ilosc.Text = (string)BazaDanychExternal.Wyczytaj_Jedna_Wartosc("SELECT cast(concat(sum(if(scan is not null,1,0)),' / ',count(1) ) as char) as skanowanych FROM artsana._hu h WHERE  h.delivery_number='" + (tabela.Rows[0]["delivery_number"]).ToString() + "';");
                    kod.Text = node["delivery_number"].InnerText;
                    adres.Text = node["transport_number"].InnerText;
                    //partia.Text = (string)BazaDanychExternal.Wyczytaj_Jedna_Wartosc("SELECT cast(concat(sum(if(scan is not null,1,0)),' / ',count(1) ) as char) as skanowanych FROM artsana._hu h WHERE h.transport_number='" + (tabela.Rows[0]["transport_number"]).ToString() + "' ;");
                    ilosc.Text = node["ilosc_hu_dostawa_scan"].InnerText + " / " + node["ilosc_hu_dostawa"].InnerText;
                    partia.Text = node["ilosc_tr_dostawa_scan"].InnerText + " / " + node["ilosc_tr_dostawa"].InnerText; //(tabela.Rows[0]["ilosc_tr_dostawa_scan"]).ToString() + " / " + (tabela.Rows[0]["ilosc_tr_dostawa"]).ToString();

                    if (node["ilosc_hu_dostawa_scan"].InnerText == node["ilosc_hu_dostawa"].InnerText && !(node["ilosc_hu_dostawa_scan"].InnerText == "0" || node["ilosc_hu_dostawa_scan"].InnerText == ""))
                    {
                        MessageBox.Show("Uwaga ! Zeskanowałeś wszystkie kartony z number delivery '" + node["delivery_number"].InnerText + "'");
                    }
                    this.ZacznijSkanowanie();

                }

            }
            catch (WebException we)
            {
                string webExceptionMessage = we.Message;
            }
            catch (Exception ex)
            {
                // no need to do anything special here....
            }



        }



        private void powrot_Click(object sender, EventArgs e)
        {
            this.Skanowanie.Abort();
            Skaner.Przewij_Skanowanie();
            //BazaDanychExternal.DokonajUpdate("insert into operacje(etykieta_id, doc_type, doc_nr, imie_nazwisko, typ_operacji,system_id,wozek,operac_id,ilosc) values('0','','','" + Wlasciwosci.imie_nazwisko + "','PR_AR','" + Wlasciwosci.system_id_id + "','" + Wlasciwosci.wozek + "','" + operac_id_global + "','0');");

            this.myParent.Show();
            this.Close();
            //BazaDanychExternal.ChangeHost(Wlasciwosci.Varbaza, Wlasciwosci.Varhost, Wlasciwosci.VarLoginMYSQL, Wlasciwosci.VarHasloMYSQL);
        }




        private void button1_Click(object sender, EventArgs e)
        {
            this.button1.Click -= new EventHandler(this.button1_Click);
            this.button1.Click += new EventHandler(this.Zakoncz_Skanowanie);
            this.button1.Text = "Zakończ";
            this.ZacznijSkanowanie();
        }
        private void ZacznijSkanowanie()
        {
            StringBuilder login = new StringBuilder();
            Skaner.UstawTryb_String(login);
            Skaner.Zacznij_Skanowanie();
            this.Skanowanie = new Thread(() => this.CheckString(login));
            this.Skanowanie.IsBackground = true;
            this.Skanowanie.Start();
        }

        private void Zakoncz_Skanowanie(object sender, EventArgs e)
        {
            this.Skanowanie.Abort();
            Skaner.Przewij_Skanowanie();
            this.button1.Click -= new EventHandler(this.Zakoncz_Skanowanie);
            this.button1.Click += new EventHandler(this.button1_Click);
            this.myParent.Show();
            base.Close();
        }

        private void button2_Click(object sender, EventArgs e)
        {
            if (adres.Text != "")
            {
                DialogResult result3 = MessageBox.Show("Czy chcesz wydrukować transport:" + adres.Text + "?",
                                    "Czy wydrukować?",
                                    MessageBoxButtons.YesNo,
                                    MessageBoxIcon.Question,
                                    MessageBoxDefaultButton.Button2);
                if (result3 == DialogResult.Yes)
                { 
                    string URL = "http://172.6.1.249/wmsgg/public/skaner_api/artsana_wydruki.php?transport_number=" + adres.Text;
                    const string data = @"{""object"":{""name"":""Title""}}";

                    HttpWebRequest request = (HttpWebRequest)WebRequest.Create(URL);
                    request.Method = "POST";
                    request.ContentType = "application/json";
                    request.ContentLength = data.Length;
                    StreamWriter requestWriter = new StreamWriter(request.GetRequestStream(), System.Text.Encoding.ASCII);
                    requestWriter.Write(data);
                    requestWriter.Close();

                    try
                    {
                        // get the response
                        WebResponse webResponse = request.GetResponse();
                        Stream webStream = webResponse.GetResponseStream();
                        StreamReader responseReader = new StreamReader(webStream);
                        string response = responseReader.ReadToEnd();
                        responseReader.Close();


                    }
                    catch (WebException we)
                    {
                        string webExceptionMessage = we.Message;
                    }
                    catch (Exception ex)
                    {
                        // no need to do anything special here....
                    }
                }
            }
            else
            {
                MessageBox.Show("Uwaga");
            }


        }





    }
}