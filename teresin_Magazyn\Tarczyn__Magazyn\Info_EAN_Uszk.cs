﻿using System;
using System.Linq;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using System.Threading;
using System.Text.RegularExpressions;

namespace Tarczyn__Magazyn
{
    public partial class Info_EAN_Uszk : Form
    {

        MainMenu parent = null;



        List<string> _regal = new List<string>();


        public Info_EAN_Uszk(MainMenu myParent)
        {
            if (Wlasciwosci.GNG != "")
            {
                BazaDanychExternal.ChangeHost(Wlasciwosci.GNG, Wlasciwosci.Varhost, Wlasciwosci.VarLoginMYSQL, Wlasciwosci.VarHasloMYSQL);
            }
            FullScreenMode.OknoOFF(this);
            InitializeComponent();
            parent = myParent;

            //ZacznijSkanowanie();

        }
        
        List<string> Etykiety = new List<string>();
        Thread Skanowanie = null;


        private void ZacznijSkanowanie()
        {

            StringBuilder login = new StringBuilder();
            Skaner.UstawTryb_String(login);
            Skaner.Zacznij_Skanowanie();
            Skanowanie = new Thread(() => CheckString(login));
            Skanowanie.IsBackground = true;
            Skanowanie.Start();
        }
        private void CheckString(StringBuilder cc)
        {
            while (true)
            {
                if (cc.ToString() != "")
                {
                    this.Invoke(new EventHandler(delegate
                    {
                        
                        dodawanie(cc.ToString());
                    }));
                    return;
                }
                Thread.Sleep(200);
            }
        }
        private void Zakoncz_Skanowanie()
        {
            try
            {
                Skanowanie.Abort();
                Skaner.Przewij_Skanowanie();
            }
            catch
            {
            }
        }

        private void Exit_Click_1(object sender, EventArgs e)
        {
            Zakoncz_Skanowanie();
            BazaDanychExternal.ChangeHost(Wlasciwosci.Varbaza, Wlasciwosci.Varhost, Wlasciwosci.VarLoginMYSQL, Wlasciwosci.VarHasloMYSQL);
            parent.Show();
            this.Close();
        }


        private void button2_Click(object sender, EventArgs e)
        {
          

        }


        private void dodawanie(string gg)
        {

            Zakoncz_Skanowanie();
            //MessageBox.Show("1");
            if (textBox2.Text == "")
            {
                MessageBox.Show("Proszę podać nr DC");
                ZacznijSkanowanie();
                return;
            }
            if (paleta_idd.Text == "" && gg.Substring(0, 2) != "DS")
            {
                MessageBox.Show("Proszę podać nr DS");
                ZacznijSkanowanie();
                return;
            }
            

            if (gg.Substring(0, 2) == "DS")
            {
                paleta_idd.Text = gg.Replace("DS", "");
            }           
            else
            {
                BazaDanychExternal.DokonajUpdate("insert into jmp_ean_produkcja_uszk(ean, nr_dc, ilosc,ds) values('" + gg + "','" + textBox2.Text + "','1','" + paleta_idd.Text + "');");

                object test2 = BazaDanychExternal.Wyczytaj_Jedna_Wartosc("SELECT count(1) FROM jmp_ean_produkcja_uszk j WHERE j.nr_dc='" + textBox2.Text + "' and ds='" + paleta_idd.Text + "';");
                label4.Text = test2.ToString();
            }


            

            ZacznijSkanowanie();
        }


        private void Zakoncz_Click(object sender, EventArgs e)
        {
     
        }

        private void WczytywanieNaLinie_Load_1(object sender, EventArgs e)
        {
            ZacznijSkanowanie();
        }

        private void button2_Click_1(object sender, EventArgs e)
        {
           
        }

        private void button1_Click(object sender, EventArgs e)
        {
            dodawanie(textBox1.Text);
            textBox1.Text = "";
        }





    }
}
