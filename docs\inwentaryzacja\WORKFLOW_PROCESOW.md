# Workflow Procesów Inwentaryzacji

## Przegląd

System inwentaryzacji obsługuje cztery główne typy inwentaryzacji, każdy z własną specyfiką i przepływem procesów.

## Typy Inwentaryzacji

### 1. Inwentary<PERSON>ja Ogólna (`Inwentaryzacja.cs`)
- **Cel:** Kompleksowa inwentaryzacja z pełną kontrolą
- **Operacja:** `CurrentOperacja = "19"`
- **Specyfika:** Obsługuje pola `nr_wspolny` i `proba`, może obsługiwać palety

### 2. Inwentaryzacja Produktowa (`InwentaryzacjaProd.cs`)
- **Cel:** Inwentaryzacja skupiona na produktach/kodach
- **Operacja:** `CurrentOperacja = "21"`
- **Specyfika:** Automatyczne dodawanie nowych rekordów, tryb offline

### 3. Inwentaryzacja GG (`InwentaryzacjaGG.cs`)
- **Cel:** Inwentaryzacja według grup/kategorii
- **Operacja:** `CurrentOperacja = "20"`
- **Specyfika:** Brak wymogu kodu produktu, skupienie na etykietach

### 4. Inwentaryzacja Miejsc (`InwentaryzacjaMiejsca.cs`)
- **Cel:** Inwentaryzacja według lokalizacji magazynowych
- **Specyfika:** Możliwość tworzenia nowej inwentaryzacji, prostszy interfejs

## Główny Przepływ Procesu

### Faza 1: Inicjalizacja

```mermaid
graph TD
    A[Start Aplikacji] --> B[Logowanie Pracownika]
    B --> C[Wybór Typu Inwentaryzacji]
    C --> D[Wyszukanie Aktywnych Inwentaryzacji]
    D --> E{Czy istnieją aktywne?}
    E -->|Tak| F[Wybór z Listy]
    E -->|Nie| G[Komunikat: Brak Inwentaryzacji]
    F --> H[Inicjalizacja Formularza]
    G --> I[Koniec]
    H --> J[Wypełnienie Combo Boxów]
    J --> K[Start Skanowania]
```

**Szczegóły:**
1. **Logowanie:** Weryfikacja pracownika i wózka
2. **Wybór typu:** Określenie rodzaju inwentaryzacji
3. **Wyszukanie:** Query: `SELECT * FROM inwentaryzacja WHERE active=1`
4. **Inicjalizacja:** Przygotowanie interfejsu użytkownika

### Faza 2: Skanowanie i Wprowadzanie Danych

```mermaid
graph TD
    A[Skanowanie Kodu] --> B{Sprawdzenie Połączenia}
    B -->|Brak| C[Komunikat: Brak Połączenia]
    B -->|OK| D[Dekodowanie Kodu]
    D --> E{Typ Kodu?}
    E -->|Etykieta| F[Wyszukanie w Inwentaryzacji]
    E -->|Paleta DS| G[Wyszukanie Palety]
    E -->|Kod Produktu| H[Wyszukanie w Kartotece]
    F --> I{Znaleziono?}
    G --> I
    H --> I
    I -->|Tak| J[Wypełnienie Pól]
    I -->|Nie| K[Komunikat: Brak w Inwentaryzacji]
    J --> L[Wprowadzenie Ilości]
    K --> M[Opcja Dodania Nowego]
    L --> N[Wybór Lokalizacji]
    M --> N
    N --> O[Zatwierdzenie]
```

**Szczegóły dekodowania:**
- **Etykiety:** Wyszukiwanie po `etykieta_id` lub `nrsap`
- **Palety:** Prefix "DS" + ID palety
- **Kody produktów:** Wyszukiwanie w tabeli `kody` po EAN/kodzie

### Faza 3: Walidacja i Zapis

```mermaid
graph TD
    A[Zatwierdzenie Danych] --> B{Walidacja Pól}
    B -->|Błąd| C[Komunikat Błędu]
    B -->|OK| D{Etykieta Istnieje?}
    D -->|Tak| E[Aktualizacja Rekordu]
    D -->|Nie| F[Dodanie Nowego Rekordu]
    E --> G{Zmiana Lokalizacji?}
    F --> H[Rejestracja w Operacjach]
    G -->|Tak| I[Zapis Zmiany Miejsca]
    G -->|Nie| H
    I --> H
    H --> J[Aktualizacja Stanu]
    J --> K[Komunikat Sukcesu]
    K --> L[Reset Formularza]
    L --> M[Powrót do Skanowania]
```

**Walidacja obejmuje:**
- Sprawdzenie wypełnienia wymaganych pól
- Weryfikacja poprawności ilości
- Kontrola istnienia lokalizacji
- Sprawdzenie uprawnień pracownika

## Szczegółowe Zapytania SQL

### Wyszukiwanie Aktywnych Inwentaryzacji

```sql
-- Inwentaryzacja ogólna
SELECT DATE_FORMAT(i.data,'%Y-%m-%d') as data, opis, inwentaryzacja_id, nr_wspolny, proba 
FROM inwentaryzacja i  
WHERE active=1 
GROUP BY i.data, opis, inwentaryzacja_id 
ORDER BY inwentaryzacja_id DESC 
LIMIT 40;

-- Inwentaryzacja miejsc (z opcją nowej)
SELECT DATE_FORMAT(i.data,'%Y-%m-%d') as data, opis, inwentaryzacja_id 
FROM inwentaryzacja i  
WHERE active=1 
GROUP BY i.data, opis, inwentaryzacja_id 
ORDER BY inwentaryzacja_id DESC 
LIMIT 40;
```

### Wyszukiwanie Etykiety w Inwentaryzacji

```sql
-- Wyszukiwanie standardowe
SELECT i.kod, kod_nazwa, podkod, 
       CAST(TRIM(TRAILING '.' FROM TRIM(TRAILING '0' FROM i.ilosc)) AS CHAR) as ilosc,
       m.hala, m.regal, m.miejsce, m.poziom,
       i.hala as hala_i, i.regal as regal_i, i.miejsce as miejsce_i, i.poziom as poziom_i,
       i.etykieta_id, i.paleta_id, i.nrsap
FROM inwentaryzacja i 
LEFT JOIN miejsca m ON m.id = i.miejscep 
LEFT JOIN kody k ON k.kod = i.kod AND i.system_id = k.system_id  
WHERE (etykieta_id = ? OR nrsap = ?) 
  AND inwentaryzacja_id = ? 
ORDER BY i.ts ASC 
LIMIT 1;

-- Wyszukiwanie palety
SELECT i.kod, kod_nazwa, podkod, 
       CAST(TRIM(TRAILING '.' FROM TRIM(TRAILING '0' FROM i.ilosc)) AS CHAR) as ilosc,
       m.hala, m.regal, m.miejsce, m.poziom,
       i.hala as hala_i, i.regal as regal_i, i.miejsce as miejsce_i, i.poziom as poziom_i,
       i.etykieta_id, i.paleta_id, i.nrsap
FROM inwentaryzacja i 
LEFT JOIN miejsca m ON m.id = i.miejscep 
LEFT JOIN kody k ON k.kod = i.kod AND i.system_id = k.system_id  
WHERE paleta_id = ? 
  AND inwentaryzacja_id = ? 
ORDER BY i.ts ASC 
LIMIT 1;
```

### Dodawanie Nowego Rekordu

```sql
-- Inwentaryzacja produktowa
INSERT INTO inwentaryzacja(
    data, opis, ilosc, ilosc_spisana, kod, inwentaryzacja_id, 
    hala, regal, miejsce, poziom, pracownik, ts, podkod, skan, stat, nrsap
) VALUES (
    ?, ?, 0, ?, ?, ?, ?, ?, ?, ?, ?, NOW(), ?, ?, ?, ?
);

-- Inwentaryzacja miejsc
INSERT INTO inwentaryzacja(
    data, ilosc_spisana, kod, inwentaryzacja_id, 
    hala, regal, miejsce, poziom, pracownik, ts, jm, system_id
) VALUES (
    CURDATE(), ?, ?, ?, ?, ?, ?, ?, ?, NOW(), ?, ?
);
```

### Aktualizacja Istniejącego Rekordu

```sql
UPDATE inwentaryzacja 
SET ilosc_spisana = ?, 
    pracownik = ?, 
    ts = NOW(),
    hala = ?, 
    regal = ?, 
    miejsce = ?, 
    poziom = ?, 
    stat = ?, 
    podkod = ?, 
    skan = ?
WHERE active = 1 
  AND inwentaryzacja_id = ? 
  AND (etykieta_id = ? OR nrsap = ?)
LIMIT 1;
```

## Obsługa Zmian Lokalizacji

### Wykrywanie Zmiany Miejsca

```sql
-- Sprawdzenie czy lokalizacja się zmieniła
IF (hala_old != hala_new OR regal_old != regal_new OR 
    miejsce_old != miejsce_new OR poziom_old != poziom_new) THEN
    -- Rejestracja zmiany miejsca
END IF;
```

### Zapis Zmiany Miejsca

```sql
-- Dodanie rekordu zmiany miejsca
INSERT INTO zmianym(
    typ, doc_nr, pracownik_id, data, etykieta, system_id, 
    stare_m, nowe_m, doc_internal, stat, tszm
) VALUES (
    'ZM', ?, ?, CURDATE(), ?, ?, ?, ?, 3, 1, NOW()
);

-- Aktualizacja lokalizacji etykiety
UPDATE etykiety 
SET miejscep = ? 
WHERE id = ? 
LIMIT 1;
```

## Rejestracja Operacji

### Zapis w Tabeli Operacje

```sql
INSERT INTO operacje(
    etykieta_id, doc_type, doc_nr, imie_nazwisko, 
    typ_operacji, system_id, wozek, operac_id, ilosc
) VALUES (
    ?, 'INW', ?, ?, 'INW', ?, ?, ?, '1'
);
```

## Monitorowanie Postępu

### Obliczanie Stanu Inwentaryzacji

```sql
SELECT 
    SUM(IF(ilosc_spisana IS NULL, 0, 1)) as ilosc_zliczona, 
    COUNT(1) as stan 
FROM inwentaryzacja i 
WHERE i.inwentaryzacja_id = ? 
  AND kod != '10101'
  AND active = 1;
```

**Wynik:** Format "zliczone/wszystkie" (np. "150/200")

## Obsługa Błędów

### Standardowe Komunikaty

1. **"Brak w tej inwentaryzacji etykiety: [kod]"**
   - Etykieta nie została znaleziona w bieżącej inwentaryzacji
   - Opcja dodania nowego rekordu z kodem '10101' i uwagą 'brak_w_inw'

2. **"Brak połączenia z bazą danych"**
   - Problem z połączeniem WiFi/sieciowym
   - Automatyczne ponowienie próby

3. **"Brak kodu w kartotece"**
   - Kod produktu nie istnieje w tabeli `kody`
   - Wymagane dodanie kodu do kartoteki

### Mechanizmy Odzyskiwania

1. **Tryb Offline** (tylko InwentaryzacjaProd):
   - Checkbox `type_offline` pozwala na pracę bez połączenia
   - Dane zapisywane lokalnie do późniejszej synchronizacji

2. **Automatyczne Ponowne Łączenie:**
   - Klasa `WLAN_Status` monitoruje połączenie
   - Automatyczne przywracanie połączenia z bazą danych

3. **Walidacja Przed Zapisem:**
   - Sprawdzenie wszystkich wymaganych pól
   - Weryfikacja poprawności danych przed commitem
