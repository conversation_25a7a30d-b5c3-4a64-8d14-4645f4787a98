﻿using System;
using System.Linq;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using System.Threading;
using System.Xml;

namespace Tarczyn__Magazyn
{
    public partial class WydrukMiejsce_Form : Form
    {
        ActionMenu myParent = null;

        public WydrukMiejsce_Form(ActionMenu MyParent)
        {
            this.myParent = MyParent;

            FullScreenMode.OknoOFF(this);
            InitializeComponent();
            //ZacznijSkanowanie();
            //Wlasciwosci.CurrentOperacja = "28";
            //pokaz_podglad();
            //BazaDanychExternal.Inicjalizacja(Wlasciwosci.Varbaza, Wlasciwosci.Varhost, Wlasciwosci.VarLoginMYSQL, Wlasciwosci.VarHasloMYSQL, this);  
            //pobranie_wymiarow_zdefiniowanych();


        }

        Thread Skanowanie = null;


        private void ZacznijSkanowanie()
        {
            StringBuilder login = new StringBuilder();
            Skaner.UstawTryb_String(login);
            Skaner.Zacznij_Skanowanie();
            Skanowanie = new Thread(() => CheckString(login));
            Skanowanie.IsBackground = true;
            Skanowanie.Start();
        }

        private void CheckString(StringBuilder cc)
        {
            while (true)
            {
                if (cc.ToString() != "")
                {
                    this.Invoke(new EventHandler(delegate
                    {
                        dodawanie(cc.ToString());
                    }));
                    return;
                }
                Thread.Sleep(200);
            }
        }
        private void Zakoncz_Skanowanie()
        {
            try
            {
                Skanowanie.Abort();
                Skaner.Przewij_Skanowanie();
            }
            catch
            {
            }
        }






        private void dodawanie(string gg)
        {
            Skanowanie.Abort();
            Skaner.Przewij_Skanowanie();


            if (gg.StartsWith("IP"))
            {
                
                IP_drukarka.Text = gg;
            }
            else if (gg.StartsWith("MP"))
            {
                Scan_miejsce.Text = gg;
                if (IP_drukarka.Text != "")
                {
                    // Wysyłanie na serwer
                    string adresIP = IP_drukarka.Text;
                    string url = "wydruki/wydruk_etykiet_miejsca.php?adres_ip=" + adresIP + "&miejsce=" + Scan_miejsce.Text + "";
                    XmlDocument response = WebService.Pobierz_XmlDocument(url);

                    XmlNode node_etykieta = WebService.Pobierz_XmlNode(response, "dane");

                    // Tutaj możesz dodać obsługę odpowiedzi z serwera jeśli potrzebna

                    if (node_etykieta["komunikat"].InnerText != "OK")
                    {
                        MessageBox.Show(node_etykieta["komunikat"].InnerText);
                    }
                    else
                    {
                        Scan_miejsce.Text = "";
                        Scan_miejsce.Focus();
                    }
                }
                else
                {
                    MessageBox.Show("Nie podano adresu IP drukarki");
                }
            }

            ZacznijSkanowanie();
        }



        private void wyjscie()
        {
            this.Skanowanie.Abort();
            Skaner.Przewij_Skanowanie();
            this.myParent.Show();
            this.Close();
        }
        private void button1_Click(object sender, EventArgs e)
        {
            wyjscie();
        }

        private void button2_Click_1(object sender, EventArgs e)
        {
            //akceptacja_manualna();
            //pokaz_podglad(delivery_id_global);
            //edycja = false;
        }


        private void GotFocus_TextBox(object sender, EventArgs e)
        {
            Skaner.Przewij_Skanowanie();
            //TextBox Pole_Tekstowe = (TextBox)sender;

            //AktualnyTextBox = Pole_Tekstowe;

            ZacznijSkanowanie();
        }

        private void LostFocus_TextBox(object sender, EventArgs e)
        {
            //AktualnyTextBox = null;
            Skaner.Przewij_Skanowanie();

        }

        private void textBox2_TextChanged(object sender, EventArgs e)
        {

        }

        private void textBox1_TextChanged(object sender, EventArgs e)
        {

        }



        //private void button6_Click(object sender, EventArgs e)
        //{
        //    etykietyDoUzupelnienia[currentIndex - 1].Status_Prism = textBox2.Text;
        //}

       

    }
}