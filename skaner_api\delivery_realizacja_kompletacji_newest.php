<?php

include_once 'Db.class.php';
include_once 'funkcje.inc';
//error_reporting(E_ALL);
//ini_set('display_errors', 1);

$db = new Db();

// todo do zablokowania statusy



$imie_nazwisko = plznaki($_GET['imie_nazwisko']);
//$typ_operacji = $_GET['typ_operacji'];

$akcja = $_GET['akcja'];
$zadanie_dane_id = $_GET['zadanie_dane_id'];

$komunikat = "OK";
$komunikat_do_informacji = "";
$ilosc_pobierana = $_GET['ilosc_pobierana'];
$etykieta_id_realizowana = $_GET['etykieta_id_realizowana'];
$paleta_id = $_GET['paleta_id'];

$czy_koniec_kompletacji = "NIE";

//header('Content-type: text/xml');
////echo '<dane>';
//http://25.56.91.22/wmsgg/public/skaner_api/delivery_realizacja_kompletacji.php?akcja=realizacja_zadania&zadanie_dane_id=1760&baza_danych=wmsgg&etykieta_id_realizowana=9969437&imie_nazwisko=Lukasz%20Domanski&ilosc_pobierana=3


if ($akcja == "realizacja_zadania") {

    if (empty($ilosc)) {
        $ilosc = "0";
    }


    $result = pobierz_zadanie_global($zadanie_dane_id, "", $db);

    if (count($result) == 0) {
        $komunikat = "Brak informacji o zadaniu";
        return show_komunikat_xml($komunikat);
    }


    $zgodnosc_towaru = "3"; // kod_lot
    $aRowZadanie = $result[0];
    $baza_danych = $aRowZadanie['baza_danych'];
    $kod = $aRowZadanie['kod'];
    $lot = $aRowZadanie['lot'];
    $ilosc = $aRowZadanie['ilosc'];
    $zadanie_head_id = $aRowZadanie['zadanie_head_id'];
    $system_id = $aRowZadanie['system_id'];
    $docout_id = $aRowZadanie['docout_id_wew'];
    $docin_id = $aRowZadanie['docin_id_wew'];
    $zgodnosc_towaru = $aRowZadanie['zgodnosc_towaru'];

    if ($aRowZadanie['zgodnosc_towaru'] == "4") {
        $zgodnosc_towaru = $aRowZadanie['zgodnosc_towaru'];
    }


    $ilosc_pierwotna_kodu = pobieranie_ilosci_kodu_dl(array('kod_id' => $aRowZadanie['kod_id'], 'delivery_id' => $aRowZadanie['doc_id']), $db);

    if ((empty($aRowZadanie["docin_id_wew"]) || empty($aRowZadanie["docout_id_wew"]) ) && $aRowZadanie["typ"] == "4") {
        $kontrah_wew_id = get_kontrah_wew($baza_danych, $system_id, $db);
        $pracownik_id = get_pracownik($baza_danych, $imie_nazwisko, $db);
        $numer = docnumber_increment($baza_danych, "PP", $db);
        $docout_id = tworz_dokument_docout($baza_danych, "PP-", $numer, $pracownik_id, $kontrah_wew_id, $db);
        $docin_id = tworz_dokument_docin($baza_danych, "PP", $numer, $pracownik_id, $kontrah_wew_id, $db);
        $sql = "update zadania_head set docin_id_wew=" . $docin_id . ",docout_id_wew=" . $docout_id . " where id=" . $aRowZadanie["zadanie_head_id"];
        ////echo $sql;
        $result = $db->mGetResultAsXML($sql);
    }

// najpotrzebniejsze etykieta ta sama i ilość realizowana



    if ($aRowZadanie['status'] != "1") {
        $komunikat = "Zadanie zostalo zrealizowane . Przerywam operacje";
////echo $komunikat;
        return show_komunikat_xml($komunikat);
    }




// najpotrzebniejsze etykieta ta sama i ilość realizowana





    if ($aRowZadanie['etykieta_id'] == $etykieta_id_realizowana) {  //czy jest odpowiednia ilość na etykiecie
        //-------------- jeśli etykieta jest ta sama
        $sql = 'SELECT id FROM ' . $baza_danych . '.pracownicy p WHERE p.imie_nazwisko="' . $imie_nazwisko . '" limit 1; ';
        $result2 = $db->mGetResultAsXML($sql);
////echo "<br>" . $sql;
        foreach ($result2 as $index => $aRowp) {
            $pracownik_id = $aRowp['id'];
        }
        if (empty($pracownik_id)) {
            $komunikat = "Nie znaleziono pracownika: " . $imie_nazwisko . ". Przerywam operacje";
//////echo "<br>" . $komunikat;
            return show_komunikat_xml($komunikat);
        }



        $sql = 'select kod,lot,e.active,TRIM(TRAILING "." FROM TRIM(TRAILING "0" from ilosc)) as ilosc,e.miejscep,dl.nr_dl,ss.nazwa as status_system_nazwa, ss.funkcja_stat from ' . $baza_danych . '.etykiety e left join ' . $baza_danych . '.kody k on e.kod_id=k.id left join ' . $baza_danych . '.dlcollect dl '
                . ' on dl.nr_et=e.id left join ' . $baza_danych . '.status_system ss  on e.status_id=ss.id where e.id=' . $etykieta_id_realizowana . ' limit  1';
        $result2 = $db->mGetResultAsXML($sql);
////echo "<br>" . $sql;
        foreach ($result2 as $index => $aRowEtWms) {
            if ($aRowEtWms['active'] != "1") {
                $komunikat = "Etykieta jest nieaktywna. Przerywam operacje";
////echo "<br>" . $komunikat;
                return show_komunikat_xml($komunikat);
            }

            if (!empty($aRowEtWms['nr_dl'])) {
                $komunikat = "Etykieta jest wczytana na DL" . $aRowEtWms['nr_dl'] . "";
////echo "<br>" . $komunikat;
                return show_komunikat_xml($komunikat);
            }

            if (!empty($aRowEtWms['funkcja_stat'])) {
                $komunikat = "Etykieta jest w statusie: " . $aRowEtWms['status_system_nazwa'] . ". Przerywam operacje";
////echo "<br>" . $komunikat;
                return show_komunikat_xml($komunikat);
            }

            if ($ilosc_pobierana == "") {  //czy jest odpowiednia ilość na etykiecie
                $komunikat = "Ilość podano ilości pobieranej. Przerywam operacje";
////echo "<br>" . $komunikat;
                return show_komunikat_xml($komunikat);
            }

            if ($aRowEtWms['ilosc'] < $ilosc_pobierana) {  //czy jest odpowiednia ilość na etykiecie
                $komunikat = "Ilosc na etykiecie " . $aRowEtWms['ilosc'] . " jest niewystarczajaca. Przerywam operacje";
////echo "<br>" . $komunikat;
                return show_komunikat_xml($komunikat);
            }


            /// ponowne sprawdzanie

            $result77 = pobierz_zadanie_global($zadanie_dane_id, "", $db);

            if (count($result77) == 0) {
                $komunikat = "Brak informacji o zadaniu";
                return show_komunikat_xml($komunikat);
            }
            if ($result77[0]['status'] != "1") {
                $komunikat = "Zadanie zostalo zrealizowane . Przerywam operacje";
                $temat = "Raport nieprawidłowości DL" . $aRowZadanie['doc_id'] . "  ";
                $adresaci = array(
                    array('adres' => '<EMAIL>'),
                );
                $content = "" . $komunikat . "Zaniadnie id" . $zadanie_dane_id;
                wyslij_mail_func($adresaci, $temat, $naglowek_html . $content);
                return show_komunikat_xml($komunikat);
            }


            if ($aRowZadanie['ilosc'] == $ilosc_pobierana) {  //czy jest odpowiednia ilość na etykiecie
                // ----------PRZYPADEK JEŚLI ETYKIETA JEST TA SAMA ALE ILOŚĆ REALIZOWANA JEST TAKA SAMA JAK PLANOWANA
                //    -----------PRZERYWNIK----------
//    //    //echo $ilosc_pobierana;
//            //echo "<pre>";
//            //print_r($aRowZadanie);
//            //echo "</pre>";
//            return false;
                //echo "Przypadek: ilosc równa;" . $ilosc_pobierana;
//                //echo "<pre>";
//                //print_r($aRowZadanie);
//                //echo "</pre>";
//                return false;
                if ($aRowZadanie['ilosc'] == $aRowEtWms['ilosc']) { // czy ilość na etykiecie jest równa czy będzie przepakowanie
//                    $sql = "insert into $baza_danych.dlcollect(nr_dl,nr_et,system_id) values('" . $aRowZadanie['doc_id'] . "','" . $etykieta_id_realizowana . "','" . $aRowZadanie['system_id'] . "') ";
//                    $result3 = $db->mGetResultAsXML($sql);
//                    $sql = "update $baza_danych.etykiety e set e.paleta_id=$paleta_id WHERE id=" . $etykieta_id_realizowana . "  ";
//                    $result = $db->mGetResultAsXML($sql);
////echo "<br>" . $sql;
                    $ilosc_etykiety = $aRowEtWms['ilosc']; //get_ilosc_etykiety($baza_danych, $etykieta_id_realizowana, $system_id, $db);


                    $id_oryginalna = przepakowanie_etykiety_step1_oryginalna($baza_danych, $etykieta_id_realizowana, $docout_id, $db);
                    $id_pobierana = przepakowanie_etykiety_step2_ilosc_pobierana($baza_danych, $etykieta_id_realizowana, $docin_id, $ilosc_pobierana, $db);
                    //$id_zostawiana = przepakowanie_etykiety_step3_ilosc_zostawiana($baza_danych, $etykieta_id_realizowana, $docin_id, $ilosc_zostawiana, $db);



                    $sql = "update $baza_danych.etykiety e set e.paleta_id=$paleta_id WHERE id=" . $id_pobierana . "  ";
                    $result6 = $db->mGetResultAsXML($sql);
////echo "<br>" . $sql;

                    $sql = "insert into $baza_danych.dlcollect(nr_dl,nr_et,system_id) values('" . $aRowZadanie['doc_id'] . "','" . $id_pobierana . "','" . $aRowZadanie['system_id'] . "') ";
                    $result3 = $db->mGetResultAsXML($sql);
////echo "<br>" . $sql;


                    $sql = "update $baza_danych.delivery_et d set d.etykieta_id=$id_pobierana WHERE d.delivery_id=" . $aRowZadanie['doc_id'] . " AND d.etykieta_id=" . $aRowZadanie['etykieta_id'] . " and ilosc_zamawiana=" . $aRowZadanie['ilosc'] . " limit 1";
                    $result7 = $db->mGetResultAsXML($sql);

                    $sql = 'update  zadania_dane z set z.etykieta_id=' . $id_pobierana . ' where z.id=' . $zadanie_dane_id . ' limit 1';
////echo "<br>" . $sql;
                    $result8 = $db->mGetResultAsXML($sql);
                } else {  //gdy jest mniej
// skopjuj etykietę i nową ilość na DL
                    $ilosc_etykiety = $aRowEtWms['ilosc']; //get_ilosc_etykiety($baza_danych, $etykieta_id_realizowana, $system_id, $db);
                    $ilosc_zostawiana = ($ilosc_etykiety + 0) - ($ilosc_pobierana + 0);

                    $id_oryginalna = przepakowanie_etykiety_step1_oryginalna($baza_danych, $etykieta_id_realizowana, $docout_id, $db);
                    $id_pobierana = przepakowanie_etykiety_step2_ilosc_pobierana($baza_danych, $etykieta_id_realizowana, $docin_id, $ilosc_pobierana, $db);
                    $id_zostawiana = przepakowanie_etykiety_step3_ilosc_zostawiana($baza_danych, $etykieta_id_realizowana, $docin_id, $ilosc_zostawiana, $db);

                    $sql = "insert into logi(etykieta_id, system_id, typ_tabeli, opis, ts) values('" . $id_pobierana . "','" . $system_id . "','etykiety','Z:" . $etykieta_id_realizowana . ";Przed:" . $aRowEtWms['ilosc'] . ";Po:" . $ilosc_zostawiana . "',NOW()); ";
                    $result8 = $db->mGetResultAsXML($sql);

                    $sql = "update $baza_danych.etykiety e set e.paleta_id=$paleta_id WHERE id=" . $id_pobierana . "  ";
                    $result6 = $db->mGetResultAsXML($sql);
////echo "<br>" . $sql;

                    $sql = "insert into $baza_danych.dlcollect(nr_dl,nr_et,system_id) values('" . $aRowZadanie['doc_id'] . "','" . $id_pobierana . "','" . $aRowZadanie['system_id'] . "') ";
                    $result3 = $db->mGetResultAsXML($sql);
////echo "<br>" . $sql;


                    $sql = "update $baza_danych.delivery_et d set d.etykieta_id=$id_pobierana WHERE d.delivery_id=" . $aRowZadanie['doc_id'] . " AND d.etykieta_id=" . $aRowZadanie['etykieta_id'] . " and ilosc_zamawiana=" . $aRowZadanie['ilosc'] . " limit 1";
                    $result7 = $db->mGetResultAsXML($sql);

                    $sql = 'update  zadania_dane z set z.etykieta_id=' . $id_pobierana . ' where z.id=' . $zadanie_dane_id . ' limit 1';
////echo "<br>" . $sql;
                    $result8 = $db->mGetResultAsXML($sql);
////echo "<br>" . $sql;
                }
            } else {                // ------- przypadek jak ilość pobbierana jest mniejsza niż planowana
                if ($ilosc_pobierana > $aRowZadanie['ilosc']) {
                    $komunikat = "Za duza ilosc pobierana wzgledem planowanej. Przerywam operacje";
                    return show_komunikat_xml($komunikat);
                }


                $sql = "SELECT d.id FROM delivery_et d left join dlcollect dc on d.etykieta_id=dc.nr_et and d.delivery_id=nr_dl WHERE d.delivery_id=" . $aRowZadanie['doc_id'] . " AND d.etykieta_id=" . $aRowZadanie['etykieta_id'] . " AND d.ilosc_zamawiana=" . $aRowZadanie['ilosc'] . " and dc.id is null limit 1;";
                ////echo $sql;
                $result_de = $db->mGetResultAsXML($sql);
                if (empty($result_de)) {
                    $komunikat = "Brak etykiety " . $aRowZadanie['etykieta_id'] . " w bazie delivery_et, by mozna bylo podmienic";
                    return show_komunikat_xml($komunikat);
                }



                /// NOWY SPOSÓB: ILOŚC POBIERANA  JEST RÓWNA ZERO- START
                if ($ilosc_pobierana == "0") {

                    $braki = array(
                        'pracownik_id' => $pracownik_id,
                        'miejsce_id' => $aRowEtWms['miejscep'],
                        'kod_id' => $aRowZadanie['kod_id'],
                        'ilosc_wymagana' => $aRowZadanie['ilosc'],
                        'ilosc_realna' => $ilosc_pobierana,
                        'delivery_id' => $aRowZadanie['doc_id'],
                        'paleta_id' => $aRowZadanie['paleta_id'],
                        'etykieta_id' => $aRowZadanie['etykieta_id'],
                    );
                    insert_delivery_braki($braki, $db);

                    $sql = "update $baza_danych.etykiety e set e.status_id=4 WHERE id=" . $etykieta_id_realizowana . " limit 1; ";
                    $result16 = $db->mGetResultAsXML($sql);
                    //echo "<br>" . $sql;



                    $ilosc_niezrealizowana = $aRowZadanie['ilosc'] - $ilosc_pobierana;
                    if ($ilosc_niezrealizowana > 0) {
                        $aRowZadanie['etykieta_id_przepakowana'] = $etykieta_id_realizowana; // zabezpieczenie, bo jest jeszcze dostepna a nie ma w dlcollect

                        $szukany_kod_dostepny = szukaj_kod_dostepny_OK($aRowZadanie, $db);
                    }
                    $ilosc_dostepna_suma = 0;
                    foreach ($szukany_kod_dostepny as $key => $value) {
                        $ilosc_dostepna_suma += $value['ilosc_dostepna'];
                    }



                    //echo "<br>ilosc_niezrealizowana:".$ilosc_niezrealizowana."<br>";
                    //echo "<br>ilosc_dostepna_suma:".$ilosc_dostepna_suma."<br>";
                    //echo "<br>zadanie ilosc:".$aRowZadanie['ilosc']."<br>";


                    if ($ilosc_dostepna_suma < $ilosc_niezrealizowana) {
                        $komunikat = "Nic nie pobrano, nie można zrealizowac pozycji, brak alternatywnych palet z potrzebna iloscia";
                        return show_komunikat_xml($komunikat);
                    }






                    if ($ilosc_dostepna_suma >= $ilosc_niezrealizowana) {
                        foreach ($szukany_kod_dostepny as $key => $value) {
                            if ($ilosc_niezrealizowana > 0) {



                                if ($value['ilosc_dostepna'] > $ilosc_niezrealizowana && $value['ilosc_dostepna'] > 0) {
                                    //$komunikat.="\nPotrzeba: " . $ilosc_niezrealizowana . ", Dodano et:" . $kod[$key]['id'] . " w ilosci:" . ($ilosc_niezrealizowana);
                                    $record = array('delivery_id' => $aRowZadanie['doc_id'], 'etykieta_id' => $value['id'], 'ilosc_zamawiana' => $ilosc_niezrealizowana);
                                    //print_r($record);
                                    $sql = "insert into delivery_et(delivery_id, etykieta_id, ilosc_zamawiana) values (" . $aRowZadanie['doc_id'] . ", " . $value['id'] . "," . $ilosc_niezrealizowana . ")";
                                    //echo "<br>" . $sql;
                                    $result13 = $db->mGetResultAsXML($sql);
                                    $aRowZadanie['etykieta_id'] = $value['id'];
                                    $aRowZadanie['paleta_id'] = $value['paleta_id'];
                                    $aRowZadanie['stare_m'] = $value['miejscep'];
                                    $aRowZadanie['ilosc'] = $ilosc_niezrealizowana;
                                    $aRowZadanie['przydzielenie_pracownik_id'] = $pracownik_id;
                                    $zadania_dane_insert_id = insert_zadanie_dane($aRowZadanie, $db);

                                    if ($value['zbiorka'] != '1') {
                                        $aRowZadanie['status'] = "10"; //oczekuje_na_zdjecie                                    
                                        $czy_jest_zadanie_palety = sprawdz_czy_juz_jest_takie_polecenie_zdjecia_palety($value['paleta_id'], 5, $db);
                                        if (empty($czy_jest_zadanie_palety)) {
                                            $sql_ins_zad = "insert into wmsgg.zadania_dane(zadanie_head_id,status, stare_m, nowe_m, paleta_id, etykieta_id, kod_id, lot, ilosc,stanowisko_id,kompletacja,wysokie,zadanie_dane_rodzic_id) values "
                                                    . "('" . $zadanie_head_id . "','5','" . $value['miejscep'] . "','" . sugerowane_miejsce($value['kod'], $value['system_id'], $db) . "','" . $value['paleta_id'] . "','" . $value['id'] . "','" . $value['kod_id'] . "','" . $value['lot'] . "','" . $ilosc_niezrealizowana . "','" . $aRowZadanie['stanowisko_id'] . "','0','1','" . $zadania_dane_insert_id . "')";
                                            //echo "<br>" . $sql_ins_zad;
                                            $result_zadanie4 = $db->mGetResultAsXML($sql_ins_zad);

//                                            $sql_ins_zad = "update zadania_head z set priorytet=5 WHERE z.priorytet=0 AND z.id=" . $zadanie_head_id;
//                                            //echo "<br>" . $sql_ins_zad;
//                                            $result_zadanie4 = $db->mGetResultAsXML($sql_ins_zad);
                                        }

                                        $sql_ins_zad = "update zadania_dane z set z.status=10 WHERE  id=" . $zadania_dane_insert_id;
                                        //echo "<br>" . $sql_ins_zad;
                                        $result_zadanie4 = $db->mGetResultAsXML($sql_ins_zad);
                                    }

                                    $ilosc_niezrealizowana = ($ilosc_niezrealizowana - ($value['ilosc_dostepna'] + 0)); // to musi być ostatnie
                                }

                                if ($value['ilosc_dostepna'] <= $ilosc_niezrealizowana && $value['ilosc_dostepna'] > 0) {
                                    //$komunikat.="\nPotrzeba: " . $ilosc_niezrealizowana . ",Dodano et:" . $kod[$key]['id'] . " w ilosci:" . $kod[$key]['ilosc_dostepna'];
                                    $record = array('delivery_id' => $aRowZadanie['doc_id'], 'etykieta_id' => $value['id'], 'ilosc_zamawiana' => $value['ilosc_dostepna']);
                                    //print_r($record);
                                    $sql = "insert into delivery_et(delivery_id, etykieta_id, ilosc_zamawiana) values (" . $aRowZadanie['doc_id'] . ", " . $value['id'] . "," . $value['ilosc_dostepna'] . ")";
                                    $result14 = $db->mGetResultAsXML($sql);
                                    //echo "<br>" . $sql;
                                    $aRowZadanie['etykieta_id'] = $value['id'];
                                    $aRowZadanie['paleta_id'] = $value['paleta_id'];
                                    $aRowZadanie['stare_m'] = $value['miejscep'];
                                    $aRowZadanie['ilosc'] = $value['ilosc_dostepna'];
                                    $aRowZadanie['przydzielenie_pracownik_id'] = $pracownik_id;

                                    $zadania_dane_insert_id = insert_zadanie_dane($aRowZadanie, $db);

                                    if ($value['zbiorka'] != '1') {
                                        $aRowZadanie['status'] = "10"; //oczekuje_na_zdjecie                                    
                                        $czy_jest_zadanie_palety = sprawdz_czy_juz_jest_takie_polecenie_zdjecia_palety($value['paleta_id'], 5, $db);
                                        $komunikat_do_informacji = "Znaleziono paletę, będzie aktywna po zdjęciu z wysokości";
                                        if (empty($czy_jest_zadanie_palety)) {
                                            $sql_ins_zad = "insert into wmsgg.zadania_dane(zadanie_head_id,status, stare_m, nowe_m, paleta_id, etykieta_id, kod_id, lot, ilosc,stanowisko_id,kompletacja,wysokie,zadanie_dane_rodzic_id) values "
                                                    . "('" . $zadanie_head_id . "','5','" . $value['miejscep'] . "','" . sugerowane_miejsce($value['kod'], $value['system_id'], $db) . "','" . $value['paleta_id'] . "','" . $value['id'] . "','" . $value['kod_id'] . "','" . $value['lot'] . "','" . $ilosc_niezrealizowana . "','" . $aRowZadanie['stanowisko_id'] . "','0','1','" . $zadania_dane_insert_id . "')";
                                            //echo "<br>" . $sql_ins_zad;
                                            $result_zadanie4 = $db->mGetResultAsXML($sql_ins_zad);

//                                            $sql_ins_zad = "update zadania_head z set priorytet=5 WHERE z.priorytet=0 AND z.id=" . $zadanie_head_id;
//                                            //echo "<br>" . $sql_ins_zad;
//                                            $result_zadanie4 = $db->mGetResultAsXML($sql_ins_zad);
                                        }

                                        $sql_ins_zad = "update zadania_dane z set z.status=10 WHERE  id=" . $zadania_dane_insert_id;
                                        //echo "<br>" . $sql_ins_zad;
                                        $result_zadanie4 = $db->mGetResultAsXML($sql_ins_zad);
                                    }


                                    $ilosc_niezrealizowana = ($ilosc_niezrealizowana - ($value['ilosc_dostepna'] + 0)); // to musi być ostatnie
                                }
                                //echo "<br>" . "<br>";
                            }
                        }



                        $sql = "delete from delivery_et WHERE id=" . $result_de[0]['id'];
                        $result_zadanie21 = $db->mGetResultAsXML($sql);
                        //echo $sql;
                        $sql = ' delete from zadania_dane  WHERE id=' . $zadanie_dane_id;
                        //echo "<br>" . $sql;
                        $result8 = $db->mGetResultAsXML($sql);
                    }
                }  // NOWY SPOSÓB: ILOŚC POBIERANA  JEST RÓWNA ZERO- END
                else {

                    // NOWY SPOSÓB: ILOŚC POBIERANA  JEST WIEKSZA NIZ ZERO - START







                    $ilosc_etykiety = $aRowEtWms['ilosc']; //get_ilosc_etykiety($baza_danych, $etykieta_id_realizowana, $system_id, $db);
                    $ilosc_zostawiana = ($ilosc_etykiety + 0) - ($ilosc_pobierana + 0);

                    $id_oryginalna = przepakowanie_etykiety_step1_oryginalna($baza_danych, $etykieta_id_realizowana, $docout_id, $db);
                    $id_pobierana = przepakowanie_etykiety_step2_ilosc_pobierana($baza_danych, $etykieta_id_realizowana, $docin_id, $ilosc_pobierana, $db);
                    $id_zostawiana = przepakowanie_etykiety_step3_ilosc_zostawiana($baza_danych, $etykieta_id_realizowana, $docin_id, $ilosc_zostawiana, $db);

                    $sql = "insert into logi(etykieta_id, system_id, typ_tabeli, opis, ts) values('" . $id_pobierana . "','" . $system_id . "','etykiety','Z:" . $etykieta_id_realizowana . ";Przed:" . $aRowEtWms['ilosc'] . ";Po:" . $ilosc_zostawiana . "',NOW()); ";
                    //echo "<br>" . $sql;
                    $result18 = $db->mGetResultAsXML($sql);

                    $sql = "update zadania_dane z set z.ilosc=" . $ilosc_pobierana . ",z.etykieta_id=" . $id_pobierana . " WHERE z.id=" . $zadanie_dane_id . " limit 1;";
                    //echo "<br>" . $sql;
                    $result_zadanie12 = $db->mGetResultAsXML($sql);

                    $sql = "update $baza_danych.etykiety e set e.paleta_id=$paleta_id WHERE id=" . $id_pobierana . " limit 1;  ";
                    //echo "<br>" . $sql;
                    $result6 = $db->mGetResultAsXML($sql);

                    $sql = "insert into $baza_danych.dlcollect(nr_dl,nr_et,system_id) values('" . $aRowZadanie['doc_id'] . "','" . $id_pobierana . "','" . $aRowZadanie['system_id'] . "') ";
                    $result3 = $db->mGetResultAsXML($sql);
                    //echo "<br>" . $sql;


                    $sql = "update delivery_et d set d.ilosc_zamawiana=" . $ilosc_pobierana . ",d.etykieta_id=$id_pobierana WHERE d.id=" . $result_de[0]['id'] . " limit 1;";
                    //echo $sql;
                    $result_zadanie18 = $db->mGetResultAsXML($sql);

                    $ilosc_niezrealizowana = $aRowZadanie['ilosc'] - $ilosc_pobierana;

                    $aRowZadanie['etykieta_id'] = $etykieta_id_realizowana;
                    $aRowZadanie['ilosc'] = $ilosc_niezrealizowana;
                    $aRowZadanie['przydzielenie_pracownik_id'] = $pracownik_id;
                    $zadania_dane_insert_id = insert_zadanie_dane($aRowZadanie, $db);

                    $sql = "insert into delivery_et(delivery_id, etykieta_id, ilosc_zamawiana) values (" . $aRowZadanie['doc_id'] . ", " . $etykieta_id_realizowana . "," . $ilosc_niezrealizowana . ")";
                    //echo "<br>" . $sql;
                    $result13 = $db->mGetResultAsXML($sql);

                    // NOWY SPOSÓB: ILOŚC POBIERANA  JEST WIEKSZA NIZ ZERO - END
                }


                // sprawdzanie poprawności ilości po przepakowaniach i alarmowanie mailem.
                $ilosc_po_zmianach_kodu = pobieranie_ilosci_kodu_dl(array('kod_id' => $aRowZadanie['kod_id'], 'delivery_id' => $aRowZadanie['doc_id']), $db);
                if ($ilosc_pierwotna_kodu != $ilosc_po_zmianach_kodu) {
                    $temat = "Raport nieprawidłowości DL" . $aRowZadanie['doc_id'] . "  ";
                    $adresaci = array(
                        array('adres' => '<EMAIL>'),
                    );
                    $content = "W " . $aRowZadanie['system_id_nazwa'] . " DL" . $aRowZadanie['doc_id'] . ", dla kodu <b>" . $aRowZadanie['kod'] . "</b> ilosc pierwotna była: " . $ilosc_pierwotna_kodu . ", została przy kompletacji zmieniona na:<b>" . $ilosc_po_zmianach_kodu . "</b>. Jest to błąd, należy sprawdzić.";
                    wyslij_mail_func($adresaci, $temat, $naglowek_html . $content);
                }






                //    //    -----------PRZERYWNIK----------
//                if ($zgodnosc_towaru == "4") {
//                    $komunikat = "Nie mozna dobrac z innej etykiety. Zgodnosc towaru: Etykieta";
//                    return show_komunikat_xml($komunikat);
//                }
            }
        }



        //$komunikat .= //mysql_error();
        if ($komunikat == "OK") {

            if ($ilosc_pobierana != "0") {
                $sql = ' update  zadania_dane z set z.realizacja_pracownik_id=' . $pracownik_id . ', status=2,stop=NOW(),kompletowana_paleta_id=' . $paleta_id . ' WHERE z.id=' . $zadanie_dane_id;
////echo "<br>" . $sql;

                $result8 = $db->mGetResultAsXML($sql);

                // dodawanie operaji
                $operac_id = docnumber_increment($baza_danych, "operacja_id", $db);
                $sql = "insert into operacje(ts_ins,etykieta_id, doc_type, doc_nr, imie_nazwisko, typ_operacji,system_id,wozek,operac_id,ilosc) values('" . $aRowZadanie['start'] . "','" . $etykieta_id_realizowana . "','DL','" . $aRowZadanie['doc_id'] . "','" . $imie_nazwisko . "','DL_SZ_ZAD','" . $aRowZadanie['system_id'] . "', '0','" . $operac_id . "','0');";
                $result8 = $db->mGetResultAsXML($sql);

                $sql = "insert into operacje(ts_ins,etykieta_id, doc_type, doc_nr, imie_nazwisko, typ_operacji,system_id,wozek,operac_id,ilosc) values(NOW(),'" . $etykieta_id_realizowana . "','DL','" . $aRowZadanie['doc_id'] . "','" . $imie_nazwisko . "','DL_SZ_ZAD','" . $aRowZadanie['system_id'] . "', '0','" . $operac_id . "','1');";
                $result8 = $db->mGetResultAsXML($sql);
            }

            $sql_ins_zad = "update zadania_head z set rozpoczete=1 WHERE rozpoczete=0 and z.id=" . $zadanie_head_id;
            $result_zadanie14 = $db->mGetResultAsXML($sql_ins_zad);

            $sql = 'SELECT count(1) as ile_do_kompeltacji FROM zadania_dane z WHERE z.zadanie_head_id=' . $zadanie_head_id . ' and z.kompletacja=1 and z.status=1 ';
            $result4 = $db->mGetResultAsXML($sql);

            foreach ($result4 as $index => $aRowZadanie4) {
                if ($aRowZadanie4["ile_do_kompeltacji"] == "0") { //to jest koniec kompletacji
                    //rozpocznij_pelne_po_kompletacji($zadanie_head_id, $db);
                    $czy_koniec_kompletacji = "TAK";
                }
            }
        }

////echo "Przypadek: ilosc mniejsza;" . $ilosc_pobierana;
//                //echo "<pre>";
//                //print_r($aRowZadanie);
//                //echo "</pre>";
//                return falserozpocznij_pelne_po_kompletacji;


        return xml_from_indexed_array(array(
            'komunikat' => $komunikat,
            'komunikat_do_informacji' => $komunikat_do_informacji,
            'czy_koniec_kompletacji' => $czy_koniec_kompletacji)
        );
    } else {
        $komunikat = "Etykieta realizowana jest inna niz planowana. Przerywam operacje";
////echo "<br>" . $komunikat;
        return show_komunikat_xml($komunikat);

//        //  tu będzie jeśli etykieta jest różna
//        if ($aRowZadanie['ilosc'] != $ilosc_pobierana) {  //czy jest odpowiednia ilość na etykiecie
//            $komunikat = "Ilosc realizowana jest rozna niz planowana. Przerywam operacje";
//////echo "<br>" . $komunikat;
//            return show_komunikat_xml($komunikat);
//        }
    }
}

function pobieranie_ilosci_kodu_dl($params, $db) {
    $sql_ins_zad = "SELECT TRIM(TRAILING '.' FROM TRIM(TRAILING '0' from ROUND(sum(de.ilosc_zamawiana),3))) as suma_ilosc_zamawiana
 FROM  delivery_et de
left join etykiety e on de.etykieta_id=e.id where de.delivery_id=" . $params['delivery_id'] . " AND e.kod_id=" . $params['kod_id'] . ";";
    ////echo "<br>" . $sql_ins_zad;
    $result_zadanie4 = $db->mGetResultAsXML($sql_ins_zad);
    $wynik = empty($result_zadanie4) ? 0 : $result_zadanie4[0]['suma_ilosc_zamawiana'];
    return $wynik;
}

function insert_delivery_braki($params, $db) {
    $sql = "insert into delivery_braki_towaru(data, ts, pracownik_id, miejsce_id, kod_id, ilosc_wymagana, ilosc_realna, delivery_id, mail_wyslany, 
        paleta_id, etykieta_id)
values
(CURDATE(),NOW(),'" . $params['pracownik_id'] . "','" . $params['miejsce_id'] . "','" . $params['kod_id'] . "',"
            . "'" . $params['ilosc_wymagana'] . "','" . $params['ilosc_realna'] . "','" . $params['delivery_id'] . "','0',"
            . "'" . $params['paleta_id'] . "',"
            . "'" . $params['etykieta_id'] . "' );";
    $result = $db->mGetResultAsXML($sql);
    ////echo "<br>" . $sql;
}

function insert_zadanie_dane($params, $db) {
    $sql = "insert into zadania_dane(zadanie_head_id, status, przydzielenie_pracownik_id, stare_m, nowe_m, paleta_id, etykieta_id, kod_id, lot, ilosc, "
            . "stanowisko_id, kompletacja, wysokie) "
            . "values('" . $params['zadanie_head_id'] . "','" . $params['status'] . "','" . $params['przydzielenie_pracownik_id'] . "','" . $params['stare_m'] . "',"
            . " '" . $params['nowe_m'] . "','" . $params['paleta_id'] . "','" . $params['etykieta_id'] . "','" . $params['kod_id'] . "','" . $params['lot'] . "'"
            . ",'" . $params['ilosc'] . "','" . $params['stanowisko_id'] . "','" . $params['kompletacja'] . "','" . $params['wysokie'] . "'); ";

    //echo "<br>" . $sql;
    $nowe_zadanie_id = $db->mGetResultAsXML($sql);

    $sql = "update zadania_dane set zadanie_dane_rodzic_id=$nowe_zadanie_id where  id=" . $nowe_zadanie_id . " limit 1";
    $db->mGetResultAsXML($sql);
    return $nowe_zadanie_id;
}

function szukaj_kod_dostepny_OK($params, $db) {
    $sql = 'SELECT
(SELECT group_concat(distinct d.delivery_id) as aa FROM delivery_et d left join delivery dd on d.delivery_id=dd.id WHERE d.etykieta_id=e.id and (dd.dl_status<4)) as delivery_nr_grup,
       e.id AS id,
       e.magazyn AS magazyn,
       e.active AS active,
       k.kod_nazwa AS kod_nazwa,
       k.kod AS kod,
       e.miejscep,
       e.kod_id,
       e.system_id,
       e.dataprod AS dataprod,
       e.data_waznosci AS data_waznosci,
       s.nazwa AS status_nazwa,
       e.status_id AS status_id,
       e.lot AS lot,
       e.paleta_id AS paleta_id,
       m.zbiorka,
if(din2.doc_date is null,din.doc_date,din2.doc_date) as data_przyjecia,
       TRIM(TRAILING "."
            FROM TRIM(TRAILING "0"
                      FROM (e.ilosc - sum(IFNULL(IFNULL(ee.ilosc, de.ilosc_zamawiana), 0))))) AS ilosc_dostepna
FROM etykiety e
LEFT JOIN kody AS k ON k.id=e.kod_id
LEFT JOIN status_system AS s ON e.status_id=s.id
LEFT JOIN delivery AS dl ON e.delivery_id=dl.id
LEFT JOIN delivery_et AS de ON e.id=de.etykieta_id
LEFT JOIN dlcollect AS dlc ON e.id=dlc.nr_et
LEFT JOIN etykiety AS ee ON dlc.nr_et=ee.id
left join docin din on e.docin_id=din.id
left join miejsca m on e.miejscep=m.id
left join docin din2 on e.docin_id=din2.id
WHERE e.magazyn!=2 and e.kod_id=' . $params['kod_id'] . ' and e.active=1 and s.nazwa="OK" and ifnull(e.lot,"")="' . $params['lot'] . '"
  AND e.system_id = ' . $params['system_id'] . ' and e.id!=' . $params['etykieta_id'] . ' and e.id!=' . $params['etykieta_id_przepakowana'] . '
GROUP BY e.id
having ilosc_dostepna>0
order by delivery_nr_grup DESC,
m.zbiorka DESC,
e.ilosc ASC,
data_przyjecia ASC';

    $result = $db->mGetResultAsXML($sql);
    //echo "<br>" . $sql;

    if (empty($result)) {
        $komunikat = "Nie znaleziono pasujacych etykiet";
        return show_komunikat_xml($komunikat);
    }



    return $result;
}

function sprawdz_czy_juz_jest_takie_polecenie_zdjecia_palety($paleta_id, $status, $db) {
    $sql_ins_zad = "SELECT 1 as ile FROM zadania_dane z left join zadania_head zh on z.zadanie_head_id=zh.id
WHERE z.paleta_id=" . $paleta_id . " and z.status=$status and zh.id is not null limit 1;";
    //echo "<br>" . $sql_ins_zad;
    $result_zadanie4 = $db->mGetResultAsXML($sql_ins_zad);
    $wynik = empty($result_zadanie4) ? 0 : count($result_zadanie4[0]);
    return $wynik;
}

function sugerowane_miejsce($kod, $system_id, $db) {
    $sugerowane_miejsce_id = 0;

    return $sugerowane_miejsce_id;
}

//
//
//function rozpocznij_pelne_po_kompletacji($zadanie_head_id, $db) {
//    $sql_ins_zad = "update zadania_dane z set z.status=1 WHERE status=6 and zadanie_head_id=" . $zadanie_head_id;
//    ////echo "<br>" . $sql_ins_zad;
//    $result_zadanie4 = $db->mGetResultAsXML($sql_ins_zad);
//}
?>