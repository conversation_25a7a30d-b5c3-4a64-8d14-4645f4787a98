<?php

//require_once("./../../lib/nusoap/lib/nusoap.php");


include_once './../Db.class.php';
include_once './../funkcje.inc';

ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

$db = new Db();
$params = get_params_to_array();
$baza_danych = "wmsgg";
$komunikat = array();

//echo "<pre>";
//var_dump(validateDate($params['data'], 'Y-m-d'));
//echo "</pre>";
//exit();
//echo 1;

$operacja_id = 0;

if (!empty($params['operacja_id'])) {
    $operacja_id = $params['operacja_id'];
}

if ($params['akcja'] == 'sprawdzanie_deklaracja') {

    sprawdzanie_deklaracja($params['docin_id'], $params['ilosc_deklarowana'], $params['data_waznosci'], $operacja_id, $params['imie_nazwisko'], $params['drukarka_ip'], $params['wozek'], $baza_danych, $db);
//    echo "<pre>";
//    print_r($result2);
//    echo "</pre>";
}

if ($params['akcja'] == 'wyszukaj_dp') {

    wyszukaj_dp($params['system_id'], $db);
}

if ($params['akcja'] == 'drukowanie_etykiety') {
    $komunikat = array();
    $komunikat = drukowanie_etykiety($params['etykieta_id'], $params['drukarka_ip'], $db);
    return xml_from_indexed_array($komunikat);
}





//$ilosc_deklarowana = 1;
//$data_waznosci = "2022-11-29";
//sprawdzanie_deklaracja($docin_id, $ilosc_deklarowana, $data_waznosci, $operacja_id, $baza_danych, $db);

//$drukarka_ip = '**********';

//drukowanie_etykiety(6036185, $drukarka_ip, $db);
//$server->wsdl->addComplexType(
//        'MySoapObject', 'complexType', 'struct', 'all', '', array(
//    'komunikat' => array('name' => 'komunikat', 'type' => 'xsd:string'),
//    'etykieta' => array('name' => 'etykieta', 'type' => 'xsd:string'),
//    'doc_nr' => array('name' => 'doc_nr', 'type' => 'xsd:string'),
//    'system_id' => array('name' => 'system_id', 'type' => 'xsd:string')
//        )
//);
//
//$server->wsdl->addComplexType(
//        'MySoapObjectArray', 'complexType', 'array', '', 'SOAP-ENC:Array', array(), array(array('ref' => 'SOAP-ENC:arrayType', 'wsdl:arrayType' => 'tns:MySoapObject[]')), 'tns:MySoapObject'
//);
//
//
//$server->register(
//        'sprawdzanie_deklaracja', array('docin_id' => 'xsd:string', 'ilosc_deklarowana' => 'xsd:string', 'data_waznosci' => 'xsd:string'), array('return' => 'tns:MySoapObjectArray'), $namespace, false, 'rpc', false, 'Processes an array of MySoapObjects and returns one of them');
//
//$server->register(
//        'drukowanie_etykiety', array('etykieta_id' => 'xsd:string', 'drukarka_ip' => 'xsd:string'), array(), $namespace, false, 'rpc', false, 'Drukowanie etykiety');
//parse_str(parse_url($url, PHP_URL_QUERY), $array);
//print_r($array);
//getTime($_GET['docin_id'],$_GET['ilosc_deklarowana'],$_GET['data_waznosci']);




function wyszukaj_dp($system_id, $db) {
    $komunikat['komunikat'] = "OK";
    $sql = "SELECT d.id,s.nazwa,doc_nr,lot,status_prism FROM etykiety e left join docin d on e.docin_id=d.id left join systemy s on e.system_id=s.wartosc where doc_type='DP' and active=1 and e.system_id='" . $system_id . "' and doc_date=curdate()  group by d.id order by d.id desc limit 52";

    $result = $db->mGetResultAsXML($sql);
    //echo $sql;
    if (!empty($db->errors))
        $komunikat['komunikat'] = $db->errors;
    if (empty($result)) {
        $komunikat['komunikat'] = " Brak dokumentów DP";
    }

    $komunikat['lista_dp'] = $result;

    return xml_from_indexed_array($komunikat);
}

function sprawdzanie_deklaracja($docin_id, $ilosc_deklarowana, $data_waznosci, $operacja_id, $imie_nazwisko, $drukarka_ip, $wozek, $baza_danych, $db) {


//    echo "<pre>";
//    print_r(func_get_args());
//    echo "</pre>";
//    exit();
    if (empty($operacja_id)) {
        $operacja_id = docnumber_increment($baza_danych, "operacja_id", $db);
    }



    $komunikat['komunikat'] = "OK";

    $etykieta_nowa_wp = "0";
    $etykieta_nowa_dp = "0";

    $wykonaj_query = "1";

    $pokaz_echo = "0";

    $start_time = microtime(true);
    $licznik = 0;

// gniazdo dla każdej DP
// 
// 
// kopiuj ostatnią etykietę  z docin z nową ilością i nr palety


    if ($data_waznosci != "") {
        if (validateDate($data_waznosci, 'Y-m-d') == false) {
            $komunikat['komunikat'] = "Nieprawidłowa data ważności";
            return xml_from_indexed_array($komunikat);
        }
    }




    $query = "SELECT wartosc,
if(wartosc=0,'MOZNA',if(TIMESTAMPDIFF(MINUTE, max(ts),NOW())<1,'BLOKADA','MOZNA')) as blokada FROM setup s
where nazwa='deklaracja_dp_blokada';";
    if ($pokaz_echo == "1")
        echo "<br><br>" . $query;
    $setup = $db->mGetResultAsXML($query);

    $deklaracja_dp_blokada = $setup[0]['blokada'];

    if ($deklaracja_dp_blokada == "MOZNA") {
        $query = "update setup s set wartosc='1'
where nazwa='deklaracja_dp_blokada';";
        if ($pokaz_echo == "1")
            echo "<br><br>" . $query;

        $result = $db->mGetResultAsXML($query);

        $query = "  SELECT e.*,(SELECT dd.id FROM docout dd left join etykiety ee on ee.docout_id=dd.id
WHERE dd.docout_type='WP' and dd.docout_ref=concat('DP ',d.doc_nr)  and ee.system_id=e.system_id
ORDER BY dd.id desc limit 1) as docout_id_prod,
(SELECT ee.magazyn FROM docout dd left join etykiety ee on ee.docout_id=dd.id
WHERE dd.docout_type='WP' and dd.docout_ref=concat('DP ',d.doc_nr)  and ee.system_id=e.system_id
ORDER BY dd.id desc limit 1) as magazyn_skladnikow,
p.typypalet_id, p.ilosc as paleta_ilosc, p.j_skladowania_id, p.pal_klient, d.planowanie_sklad_id, m.miejsce, d.doc_nr,
ilosc_szt_palecie
FROM docin d left join etykiety e on e.docin_id=d.id
left join palety p on e.paleta_id=p.id  left join miejsca m on e.miejscep=m.id
left join kody k on e.kod_id=k.id
WHERE d.id=$docin_id order by e.id desc limit 1

";
        if ($pokaz_echo == "1")
            echo "<br><br>" . $query;
        $result9 = $db->mGetResultAsXML($query);

        foreach ($result9 as $key => $aRow) {

            if ($pokaz_echo == "1")
                echo "<br>aRow:<br>";
            if ($pokaz_echo == "1")
                echo print_r($aRow);

            $planowanie_sklad_id = $aRow['planowanie_sklad_id'];
            $magazyn = $aRow['magazyn'];

            $magazyn_skladnikow = $aRow['magazyn_skladnikow'];
            $system_id_id = $aRow['system_id'];

            $etykieta_produkowana = $aRow['id'];

            $docout_id = $aRow['docout_id_prod'];

            $typypalet_id = $aRow['typypalet_id'];
            $paleta_ilosc = $aRow['paleta_ilosc'];
            $j_skladowania_id = $aRow['j_skladowania_id'];
            $pal_klient = $aRow['pal_klient'];

            $miejsce = $aRow['miejsce'];
            $doc_nr = $aRow['doc_nr'];
            $miejsce = 0;

            if ($miejsce != "0") {
                $miejscezap = " AND (m.miejsce=" . $miejsce . " ) ";      //or m.miejsce=0       
            } else {
                $miejscezap = "";
            }


            if ($pokaz_echo == "1")
                echo "<br>miejscezap:" . $miejscezap . "<br>";



            if (empty($aRow['ilosc_szt_palecie'])) {
                $komunikat['komunikat'] .= "Brak paletyzacji wyrobu";
            } else if (($aRow['ilosc_szt_palecie'] - $ilosc_deklarowana) < 0) {
                $komunikat['komunikat'] .= "Ilosc deklarowana przekracza paletyzacje";
            }



            if (empty($aRow['docout_id_prod'])) {
                $komunikat['komunikat'] .= "Brak dokumentu WP";
            }
            //$ilosc_deklarowana=0;
            if (empty($ilosc_deklarowana)) {

                $komunikat['komunikat'] .= "Wprowadz poprawna ilosc deklarowana";
//                return;
            }



            $query2 = "  SELECT p.id AS id, p.planowanie_id AS planowanie_id, p.skladnik_id AS skladnik_id, p.typ_recept_kod AS typ_recept_kod, p.ilosc AS ilosc, p.status_skladnika AS status_skladnika, ph.data AS data, ph.pracownik_id AS pracownik_id, ph.status AS status, ph.system_id AS system_id, pr.imie_nazwisko AS imie_nazwisko, if(p.typ_recept_kod='receptura_id',rk.id,k.id) AS kod_id FROM planowanie_prod_sklad p LEFT JOIN planowanie_produkcji_head AS ph ON p.planowanie_id=ph.id LEFT JOIN pracownicy AS pr ON ph.pracownik_id=pr.id LEFT JOIN kody AS k ON k.id=p.skladnik_id LEFT JOIN receptura AS r ON r.id=p.skladnik_id LEFT JOIN kody AS rk ON rk.id=r.kod_id WHERE p.id = $planowanie_sklad_id ";

            $result2 = $db->mGetResultAsXML($query2);
            if ($pokaz_echo == "1")
                echo "<br>" . $query2;
            $planowanie_skladnik = $db->mGetResultAsXML($query2);

            if ($pokaz_echo == "1")
                echo "<br>$query2<br>planowanie_skladnik:<br>" . print_r($planowanie_skladnik);
            if ($pokaz_echo == "1")
                echo print_r($planowanie_skladnik);


            if ($planowanie_skladnik[0]['typ_recept_kod'] == 'receptura_id') {
                $receptura_id = $planowanie_skladnik[0]['skladnik_id'];

                $queryreceptura = "SELECT r.id AS id, r.system_id AS system_id, r.kod_id AS kod_id, r.ilosc AS ilosc, r.status_receptury AS status_receptury, k.kod AS kod, k.kod_nazwa AS kod_nazwa FROM receptura r LEFT JOIN kody AS k ON r.kod_id=k.id WHERE r.id = $receptura_id ";
                $result3 = $db->mGetResultAsXML($queryreceptura);

                $receptura = $result3[0];

                if ($pokaz_echo == "1")
                    echo "<br><br>$queryreceptura<br>receptura:<br>" . print_r($receptura) . "<br><br>";


                $mnoznik = $ilosc_deklarowana / $receptura['ilosc'];

                //$queryskladreceptura = "SELECT r.id AS id, r.receptura_id AS receptura_id, r.kod_id AS kod_id, r.ilosc AS ilosc, k.kod AS kod, k.kod_nazwa AS kod_nazwa, k.jm AS jm, (select status_receptury from receptura where id=665) AS status_receptury, ( 1*r.ilosc ) AS wymagana_ilosc, IFNULL((select sum(ilosc) from etykiety e where active=1 and e.przeznaczenie_id=2 and e.magazyn=2 and e.system_id=$system_id_id and r.kod_id=e.kod_id group by kod_id),0) AS ilosc_prod, (select round(sum(ilosc)/r.ilosc,0) from etykiety e where active=1 and e.przeznaczenie_id=2 and e.magazyn=2 and e.system_id=$system_id_id and r.kod_id=e.kod_id group by kod_id) AS ilosc_prod_stand, IFNULL((select sum(ilosc) from etykiety e where active=1 and e.przeznaczenie_id=1 and e.magazyn=1 and e.system_id=$system_id_id and r.kod_id=e.kod_id group by kod_id),0) AS ilosc_mag, (select round(sum(ilosc)/r.ilosc,0) from etykiety e where active=1 and e.przeznaczenie_id=1 and e.magazyn=1 and e.system_id=$system_id_id and r.kod_id=e.kod_id group by kod_id) AS ilosc_mag_stand, (select sum(ilosc) from etykiety e where active=1 and e.system_id=$system_id_id and r.kod_id=e.kod_id group by kod_id) AS ilosc_razem, (select round(sum(ilosc)/r.ilosc,0) from etykiety e where active=1 and e.system_id=$system_id_id and r.kod_id=e.kod_id group by kod_id) AS ilosc_razem_stand, (select sum(ilosc) from etykiety e left join docout d on e.docout_id=d.id where docout_type='WP' and active=0 and magazyn=2 and e.system_id=$system_id_id and r.kod_id=e.kod_id group by kod_id) AS ilosc_razem_got, (select round(sum(ilosc)/r.ilosc,0) from etykiety e left join docout d on e.docout_id=d.id where docout_type='WP' and active=0 and magazyn=2 and e.system_id=$system_id_id and r.kod_id=e.kod_id group by kod_id) AS ilosc_razem_got_stand FROM receptura_skladniki r LEFT JOIN kody AS k ON r.kod_id=k.id WHERE receptura_id=$receptura_id ";
                $queryskladreceptura = "SELECT r.id AS id, r.receptura_id AS receptura_id, r.kod_id AS kod_id, r.ilosc AS ilosc, k.kod AS kod, k.kod_nazwa AS kod_nazwa, k.jm AS jm, (select status_receptury from receptura where id=665) AS status_receptury, ( 1*r.ilosc ) AS wymagana_ilosc FROM receptura_skladniki r LEFT JOIN kody AS k ON r.kod_id=k.id WHERE receptura_id=$receptura_id ";

                $result4 = $db->mGetResultAsXML($queryskladreceptura);

                if ($pokaz_echo == "1")
                    echo "<br><br>$queryskladreceptura<br>queryskladreceptura:<br><br><br>";
                foreach ($result4 as $key => $skladnik) {

                    $ilosc_potrzebna = $mnoznik * $skladnik['ilosc'];

                    if ($pokaz_echo == "1")
                        echo "<br>skladniki_receptury";
                    if ($pokaz_echo == "1")
                        echo print_r($skladnik);

                    $querystockprod = "SELECT e.id AS id, e.system_id AS system_id, e.etykieta_klient AS etykieta_klient, e.magazyn AS magazyn, e.active AS active, e.miejscep AS miejscep, e.przeznaczenie_id AS przeznaczenie_id, e.kod_id AS kod_id, e.paleta_id AS paleta_id, e.dataprod AS dataprod, e.data_waznosci AS data_waznosci, e.status AS status, e.blloc AS blloc, e.akcja_id AS akcja_id, e.akcja_id AS akcjanr_id, e.status_prism AS status_prism, e.stat AS stat, e.status_id AS status_id, st.nazwa AS status_nazwa, st.funkcja_stat AS funkcja_stat, e.kartony AS kartony, ed3.kolor AS kolor, ed3.plec AS plec, ed3.rozmiar_nr AS rozmiar_nr, ed3.uszkodzenie AS uszkodzenie, e.lot AS lot, e.sscc AS sscc, e.gtin AS gtin, e.edycja_et AS edycja_et, e.ilosc AS ilosc, e.ts AS ts, e.nretykiety AS nretykiety, e.docin_id AS docin_id, e.docout_id AS docout_id, e.delivery_id AS delivery_id, e.listcontrol_id AS listcontrol_id, concat(m.regal,'-',m.miejsce,'-',m.poziom) AS adres2, k.kod_nazwa AS kod_nazwa, k.jm AS jm, k.kod AS kod, k.kod2 AS kod2, (if(e.ilosc is null,1,e.ilosc)/if(k.ilosc_w_opakowaniu is null,1,ilosc_w_opakowaniu)) AS ilosc_opak, k.opakowanie_jm AS opakowanie_jm, js.nazwa AS j_skladowania_nazwa, concat(p.ilosc, ' ',tp.opis ) AS paletanazwa, p.pal_klient AS pal_klient, tp.kod AS tpkod, pdocin.imie_nazwisko AS docin_imie_nazwisko, pdout.imie_nazwisko AS pdout_imie_nazwisko, s.skrot AS skrot, m.hala AS hala, din.doc_nr AS doc_nr, din.doc_date AS doc_date, din.doc_ts AS doc_ts, sum(e.ilosc) AS iloscsuma FROM etykiety e LEFT JOIN status_system AS st ON e.status_id=st.id LEFT JOIN etykiety_dod3 AS ed3 ON e.id=ed3.id and e.system_id=ed3.system_id LEFT JOIN miejsca AS m ON e.miejscep=m.id LEFT JOIN kody AS k ON k.id=e.kod_id LEFT JOIN palety AS p ON e.paleta_id=p.id LEFT JOIN typypalet AS tp ON p.typypalet_id=tp.id LEFT JOIN jednostka_skladowania AS js ON p.j_skladowania_id=js.id LEFT JOIN docin AS din ON e.docin_id=din.id LEFT JOIN docout AS dout ON e.docout_id=dout.id LEFT JOIN delivery AS dl ON e.delivery_id=dl.id LEFT JOIN kontrah AS kin ON din.kontrah_id=kin.id LEFT JOIN kontrah AS kout ON dout.kontrah_id=kout.id LEFT JOIN pracownicy AS pdocin ON din.pracownik_id=pdocin.id LEFT JOIN pracownicy AS pdout ON dout.pracownik_id=pdout.id LEFT JOIN systemy AS s ON e.system_id=s.wartosc WHERE e.przeznaczenie_id = 2 $miejscezap AND e.system_id =$system_id_id AND e.kod_id=" . $skladnik['kod_id'] . " AND (e.active=1 and docout_id is null) AND e.magazyn=2 GROUP BY e.kod_id";
                    $result5 = $db->mGetResultAsXML($querystockprod);

                    $ilosc_stock = $result5[0];

                    if ($pokaz_echo == "1")
                        echo "<br><br>$querystockprod<br>ilosc_stock:<br>" . print_r($ilosc_stock) . "<br><br>";

                    if (empty($result5)) {
                        $ilosc_stock['iloscsuma'] = 0;
                    }

                    if (empty($result5) || $ilosc_potrzebna > $ilosc_stock['iloscsuma']) {
                        $komunikat['komunikat'] .= 'Brak ' . $skladnik['kod'] . ' - ' . ($ilosc_potrzebna - $ilosc_stock['iloscsuma']) . ' linia: ' . $miejsce . ' {0}';
                    } else {
                        //$komunikat['komunikat'].='JEST kodu ' . $skladnik['kod'] . ' na lini PROD. Jest ' . $ilosc_stock['iloscsuma'] . ' a powinno byc ' . $ilosc_potrzebna . '  <br>';
                    }
                }


                if ($pokaz_echo == "1")
                    echo "//////////////////////////KONIEC SPRAWDZANIA///////////////////////////////////";



                if ($komunikat['komunikat'] == "OK") {


                    if ($pokaz_echo == "1")
                        echo "//////////////////////////TWORZENIE ET DP///////////////////////////////////";

                    $query2 = "  SELECT last FROM docnumber d
WHERE d.name='nrpalety';";
                    if ($pokaz_echo == "1")
                        echo "<br><br>" . $query2;

                    $result2 = $db->mGetResultAsXML($query2);
                    if ($pokaz_echo == "1")
                        echo "<br>" . $query2;
                    $docnumber = $result2[0];
                    $paleta_id = $docnumber['last'];

                    $query = "  update docnumber d set last=" . ($paleta_id + 1) . "
WHERE d.name='nrpalety';";

                    if ($pokaz_echo == "1")
                        echo "<br><br>" . $query;
                    if ($wykonaj_query == "1")
                        $result = $db->mGetResultAsXML($query);


                    $query2 = "  SELECT last FROM docnumber d
WHERE d.name='nretykiety';";
                    if ($pokaz_echo == "1")
                        echo "<br><br>" . $query2;

                    $result2 = $db->mGetResultAsXML($query2);
                    if ($pokaz_echo == "1")
                        echo "<br>" . $query2;
                    $docnumber = $result2[0];
                    $nretykiety = $docnumber['last'];

                    $query = "  update docnumber d set last=" . ($nretykiety + 1) . "
WHERE d.name='nretykiety';";

                    if ($pokaz_echo == "1")
                        echo "<br><br>" . $query;
                    if ($wykonaj_query == "1")
                        $result = $db->mGetResultAsXML($query);




                    $query_ins_pal = "  insert into palety(id,typypalet_id, ilosc, j_skladowania_id, pal_klient)"
                            . "values("
                            . "'" . $paleta_id . "',"
                            . "'" . $typypalet_id . "',"
                            . "'" . $paleta_ilosc . "',"
                            . "'" . $j_skladowania_id . "',"
                            . "'" . $pal_klient . "' "
                            . ");";
                    if ($pokaz_echo == "1")
                        echo "<br><br>" . $query_ins_pal;
                    $result_ins_pal = $db->mGetResultAsXML($query_ins_pal);

//                    echo "<pre>";
//                    print_r($result_ins_pal);
//                    echo "</pre>";
//                    exit();

                    $queryins_prod = "insert into etykiety(system_id, etykieta_klient, magazyn, active, miejscep, kod_id, status_id_old, status_id, stat,
                                paleta_id, kartony, dataprod, data_waznosci, ilosc, ts, status, blloc, akcja_id, status_prism, lot, sscc, gtin, przeznaczenie_id,
                                nretykiety, docin_id) 
                                ( SELECT  e.system_id, e.etykieta_klient, e.magazyn, '1', '4392', e.kod_id, e.status_id_old, e.status_id, e.stat,
                                $paleta_id, e.kartony, e.dataprod,  e.data_waznosci , $ilosc_deklarowana, e.ts , e.status, e.blloc, e.akcja_id, e.status_prism, e.lot, e.sscc, e.gtin, e.przeznaczenie_id,
                                $nretykiety, e.docin_id  FROM etykiety e
                                WHERE e.id=" . $etykieta_produkowana . ");";
                    if ($pokaz_echo == "1")
                        echo "<br><br>" . $queryins_prod;
                    if ($wykonaj_query == "1")
                        $etykieta_nowa_dp = $db->mGetResultAsXML($queryins_prod);



                    $query_ins_pow = "  insert into powiazania (etykieta_id, system_id, tab_id, tab_type, dest_etykieta_id, type)"
                            . "values("
                            . "'" . $etykieta_nowa_dp . "',"
                            . "'" . $system_id_id . "',"
                            . "'" . $docin_id . "',"
                            . "'1',"
                            . "'" . $etykieta_nowa_dp . "', "
                            . "'U'"
                            . ");";
                    if ($pokaz_echo == "1")
                        echo "<br><br>" . $query_ins_pow;
                    if ($wykonaj_query == "1")
                        $result_ins_pow = $db->mGetResultAsXML($query_ins_pow);


                    $sql = "insert into operacje(etykieta_id, doc_type, doc_nr, imie_nazwisko, typ_operacji,system_id,wozek,operac_id,ilosc) "
                            . "values('" . $etykieta_nowa_dp . "','DP','" . $doc_nr . "','" . $imie_nazwisko . "','DP','" . $system_id_id . "','" . $wozek . "','" . $operacja_id . "','1');";
                    //echo "\n" . $sql;
                    $resul3 = $db->mGetResultAsXML($sql);

                    $query2 = "  select count(1) as ile  from etykiety e left join docin d on e.docin_id=d.id where d.id=$docin_id  ;";

                    $result2 = $db->mGetResultAsXML($query2);
                    $licznik = $result2[0]['ile'];

                    if ($pokaz_echo == "1")
                        echo "//////////////////////////ZDEJOWAMIE SKLADNIKOW///////////////////////////////////";











                    //$queryskladreceptura = "SELECT r.id AS id, r.receptura_id AS receptura_id, r.kod_id AS kod_id, r.ilosc AS ilosc, k.kod AS kod, k.kod_nazwa AS kod_nazwa, k.jm AS jm, (select status_receptury from receptura where id=665) AS status_receptury, ( 1*r.ilosc ) AS wymagana_ilosc, IFNULL((select sum(ilosc) from etykiety e where active=1 and e.przeznaczenie_id=2 and e.magazyn=2 and e.system_id=$system_id_id and r.kod_id=e.kod_id group by kod_id),0) AS ilosc_prod, (select round(sum(ilosc)/r.ilosc,0) from etykiety e where active=1 and e.przeznaczenie_id=2 and e.magazyn=2 and e.system_id=$system_id_id and r.kod_id=e.kod_id group by kod_id) AS ilosc_prod_stand, IFNULL((select sum(ilosc) from etykiety e where active=1 and e.przeznaczenie_id=1 and e.magazyn=1 and e.system_id=$system_id_id and r.kod_id=e.kod_id group by kod_id),0) AS ilosc_mag, (select round(sum(ilosc)/r.ilosc,0) from etykiety e where active=1 and e.przeznaczenie_id=1 and e.magazyn=1 and e.system_id=$system_id_id and r.kod_id=e.kod_id group by kod_id) AS ilosc_mag_stand, (select sum(ilosc) from etykiety e where active=1 and e.system_id=$system_id_id and r.kod_id=e.kod_id group by kod_id) AS ilosc_razem, (select round(sum(ilosc)/r.ilosc,0) from etykiety e where active=1 and e.system_id=$system_id_id and r.kod_id=e.kod_id group by kod_id) AS ilosc_razem_stand, (select sum(ilosc) from etykiety e left join docout d on e.docout_id=d.id where docout_type='WP' and active=0 and magazyn=2 and e.system_id=$system_id_id and r.kod_id=e.kod_id group by kod_id) AS ilosc_razem_got, (select round(sum(ilosc)/r.ilosc,0) from etykiety e left join docout d on e.docout_id=d.id where docout_type='WP' and active=0 and magazyn=2 and e.system_id=$system_id_id and r.kod_id=e.kod_id group by kod_id) AS ilosc_razem_got_stand FROM receptura_skladniki r LEFT JOIN kody AS k ON r.kod_id=k.id WHERE receptura_id=$receptura_id ";
                    $queryskladreceptura = "SELECT r.id AS id, r.receptura_id AS receptura_id, r.kod_id AS kod_id, r.ilosc AS ilosc, k.kod AS kod, k.kod_nazwa AS kod_nazwa, k.jm AS jm, (select status_receptury from receptura where id=665) AS status_receptury, ( 1*r.ilosc ) AS wymagana_ilosc FROM receptura_skladniki r LEFT JOIN kody AS k ON r.kod_id=k.id WHERE receptura_id=$receptura_id ";
                    $result4 = $db->mGetResultAsXML($queryskladreceptura);

                    if ($pokaz_echo == "1")
                        echo "<br><br>$queryskladreceptura<br>queryskladreceptura:<br><br><br>";
                    foreach ($result4 as $key => $skladnik) {

                        $skladnik['ilosc_zdejmowana'] = $ilosc_potrzebna = $mnoznik * $skladnik['ilosc'];
                        $skladnik['magazyn'] = $magazyn;
                        $skladnik['magazyn_skladnikow'] = $magazyn_skladnikow;

                        $skladnik['docout_id'] = $docout_id;

                        //////////////////////////
                        $params = $skladnik;
                        if ($pokaz_echo == "1")
                            echo print_r($params);
                        $ilosczdejmowana = $params['ilosc_zdejmowana'];

                        if ($ilosczdejmowana != 0) {


                            // etykiety produkcji
                            $querystockprod_et = "SELECT e.id AS id, e.system_id AS system_id, e.etykieta_klient AS etykieta_klient, e.magazyn AS magazyn, e.active AS active, e.miejscep AS miejscep, e.przeznaczenie_id AS przeznaczenie_id, e.kod_id AS kod_id, e.paleta_id AS paleta_id, e.dataprod AS dataprod, e.data_waznosci AS data_waznosci, e.status AS status, e.blloc AS blloc, e.akcja_id AS akcja_id, e.akcja_id AS akcjanr_id, e.status_prism AS status_prism, e.stat AS stat, e.status_id AS status_id, st.nazwa AS status_nazwa, st.funkcja_stat AS funkcja_stat, e.kartony AS kartony, ed3.kolor AS kolor, ed3.plec AS plec, ed3.rozmiar_nr AS rozmiar_nr, ed3.uszkodzenie AS uszkodzenie, e.lot AS lot, e.sscc AS sscc, e.gtin AS gtin, e.edycja_et AS edycja_et, e.ilosc AS ilosc, e.ts AS ts, e.nretykiety AS nretykiety, e.docin_id AS docin_id, e.docout_id AS docout_id, e.delivery_id AS delivery_id, e.listcontrol_id AS listcontrol_id, concat(m.regal,'-',m.miejsce,'-',m.poziom) AS adres2, k.kod_nazwa AS kod_nazwa, k.jm AS jm, k.kod AS kod, k.kod2 AS kod2, (if(e.ilosc is null,1,e.ilosc)/if(k.ilosc_w_opakowaniu is null,1,ilosc_w_opakowaniu)) AS ilosc_opak, k.opakowanie_jm AS opakowanie_jm, js.nazwa AS j_skladowania_nazwa, concat(p.ilosc, ' ',tp.opis ) AS paletanazwa, p.pal_klient AS pal_klient, tp.kod AS tpkod, pdocin.imie_nazwisko AS docin_imie_nazwisko, pdout.imie_nazwisko AS pdout_imie_nazwisko, s.skrot AS skrot, m.hala AS hala, din.doc_nr AS doc_nr, din.doc_date AS doc_date, din.doc_ts AS doc_ts, sum(e.ilosc) AS iloscsuma FROM etykiety e LEFT JOIN status_system AS st ON e.status_id=st.id LEFT JOIN etykiety_dod3 AS ed3 ON e.id=ed3.id and e.system_id=ed3.system_id LEFT JOIN miejsca AS m ON e.miejscep=m.id LEFT JOIN kody AS k ON k.id=e.kod_id LEFT JOIN palety AS p ON e.paleta_id=p.id LEFT JOIN typypalet AS tp ON p.typypalet_id=tp.id LEFT JOIN jednostka_skladowania AS js ON p.j_skladowania_id=js.id LEFT JOIN docin AS din ON e.docin_id=din.id LEFT JOIN docout AS dout ON e.docout_id=dout.id LEFT JOIN delivery AS dl ON e.delivery_id=dl.id LEFT JOIN kontrah AS kin ON din.kontrah_id=kin.id LEFT JOIN kontrah AS kout ON dout.kontrah_id=kout.id LEFT JOIN pracownicy AS pdocin ON din.pracownik_id=pdocin.id LEFT JOIN pracownicy AS pdout ON dout.pracownik_id=pdout.id LEFT JOIN systemy AS s ON e.system_id=s.wartosc WHERE e.przeznaczenie_id = 2 $miejscezap AND e.system_id =$system_id_id AND e.kod_id=" . $skladnik['kod_id'] . " AND (e.active=1 and docout_id is null) AND e.magazyn=$magazyn_skladnikow GROUP BY e.id
order by e.ts asc ";
                            if ($pokaz_echo == "1")
                                echo "<br><br>" . $querystockprod_et;
                            $result_et = $db->mGetResultAsXML($querystockprod_et);
                            foreach ($result_et as $key => $etykiety_palet) {

                                $iloscetykiety = $etykiety_palet['ilosc'];
//                                if (empty($data_waznosci)) {
//                                    if (!($etykiety_palet['data_waznosci'] == "" || $etykiety_palet['data_waznosci'] == "0000-00-00" || $etykiety_palet['data_waznosci'] == "1970-01-01")) {
//                                        if ($min_data_waznosci == "") {
//                                            $min_data_waznosci = $etykiety_palet['data_waznosci'];
//                                        } else
//                                        if ($min_data_waznosci >= $etykiety_palet['data_waznosci'])
//                                            $min_data_waznosci = $etykiety_palet['data_waznosci'];
//                                    }
//                                } else {
//                                    $min_data_waznosci = $data_waznosci;
//                                }
//
//                                $min_data_waznosci = $etykiety_palet['data_waznosci'];
//
//                                if ($min_data_waznosci == "")
//                                    $min_data_waznosci = $aRow['data_waznosci'];



                                if ($iloscetykiety == $ilosczdejmowana && $ilosczdejmowana != 0) { //gdy ilość na etykiecie jest równa ilości zdejmowanej
                                    $queryupdate = "update etykiety e set active=0,  docout_id=$docout_id where id=" . $etykiety_palet['id'];
                                    if ($pokaz_echo == "1")
                                        echo "<br><br>" . $queryupdate;
                                    if ($wykonaj_query == "1")
                                        $result_up = $db->mGetResultAsXML($queryupdate);

                                    $query_ins_pow = "  insert into powiazania (etykieta_id, system_id, tab_id, tab_type, dest_etykieta_id, type)"
                                            . "values("
                                            . "'" . $etykieta_nowa_dp . "',"
                                            . "'" . $system_id_id . "',"
                                            . "'" . $docin_id . "',"
                                            . "'1',"
                                            . "'" . $etykiety_palet['id'] . "', "
                                            . "'P'"
                                            . ");";
                                    if ($pokaz_echo == "1")
                                        echo "<br><br>" . $query_ins_pow;
                                    if ($wykonaj_query == "1")
                                        $result_ins_pow = $db->mGetResultAsXML($query_ins_pow);

                                    $ilosczdejmowana = 0;
                                }//if end
                                if ($iloscetykiety > $ilosczdejmowana && $ilosczdejmowana != 0) { //zostawia resztę na etykiecie
                                    $queryupdate = "update etykiety e set ilosc=" . ($iloscetykiety - $ilosczdejmowana) . " where id=" . $etykiety_palet['id'];
                                    if ($pokaz_echo == "1")
                                        echo "<br><br>" . $queryupdate;
                                    if ($wykonaj_query == "1")
                                        $result_up = $db->mGetResultAsXML($queryupdate);


                                    $queryupdate = "insert into etykiety(system_id, etykieta_klient, magazyn, active, miejscep, kod_id, status_id_old, status_id, stat,
paleta_id, kartony, dataprod, data_waznosci, ilosc, ts, status, blloc, akcja_id, status_prism, lot, sscc, gtin, przeznaczenie_id,
nretykiety, docin_id, docout_id, delivery_id, listcontrol_id, status_id2, edycja_et) 
( SELECT  e.system_id, e.etykieta_klient, e.magazyn, '0', e.miejscep, e.kod_id, e.status_id_old, e.status_id, e.stat,
e.paleta_id, e.kartony, e.dataprod, e.data_waznosci, $ilosczdejmowana, e.ts , e.status, e.blloc, e.akcja_id, e.status_prism, e.lot, e.sscc, e.gtin, e.przeznaczenie_id,
e.nretykiety, e.docin_id, $docout_id, e.delivery_id, e.listcontrol_id, e.status_id2, e.edycja_et FROM etykiety e
WHERE e.id=" . $etykiety_palet['id'] . ");";
                                    if ($pokaz_echo == "1")
                                        echo "<br><br>" . $queryupdate;
                                    if ($wykonaj_query == "1")
                                        $etykieta_nowa_wp = $db->mGetResultAsXML($queryupdate);


                                    $query_ins_pow = "  insert into powiazania (etykieta_id, system_id, tab_id, tab_type, dest_etykieta_id, type)"
                                            . "values("
                                            . "'" . $etykieta_nowa_dp . "',"
                                            . "'" . $system_id_id . "',"
                                            . "'" . $docin_id . "',"
                                            . "'1',"
                                            . "'" . $etykieta_nowa_wp . "', "
                                            . "'P'"
                                            . ");";
                                    if ($pokaz_echo == "1")
                                        echo "<br><br>" . $query_ins_pow;
                                    if ($wykonaj_query == "1")
                                        $result_ins_pow = $db->mGetResultAsXML($query_ins_pow);



                                    $ilosczdejmowana = 0;
                                }

                                if ($iloscetykiety < $ilosczdejmowana && $ilosczdejmowana != 0) { //gdy ilosc naetykiecie jest większa niż ilość zdejmowana, robi ją nieaktywną i szuka inne etykiety danego kodu by zdjąć resztę
                                    $queryupdate = "update etykiety e set active=0,  docout_id=$docout_id where id=" . $etykiety_palet['id'];
                                    if ($pokaz_echo == "1")
                                        echo "<br><br>" . $queryupdate;
                                    if ($wykonaj_query == "1")
                                        $result_up = $db->mGetResultAsXML($queryupdate);

                                    $query_ins_pow = "  insert into powiazania (etykieta_id, system_id, tab_id, tab_type, dest_etykieta_id, type)"
                                            . "values("
                                            . "'" . $etykieta_nowa_dp . "',"
                                            . "'" . $system_id_id . "',"
                                            . "'" . $docin_id . "',"
                                            . "'1',"
                                            . "'" . $etykiety_palet['id'] . "', "
                                            . "'P'"
                                            . ");";
                                    if ($pokaz_echo == "1")
                                        echo "<br><br>" . $query_ins_pow;
                                    if ($wykonaj_query == "1")
                                        $result_ins_pow = $db->mGetResultAsXML($query_ins_pow);
                                    $ilosczdejmowana = $ilosczdejmowana - $iloscetykiety;
                                } //if end
                                if ($ilosczdejmowana == 0) { //szuka dot_d a_ _ci_gnie wymagan_ ilo__ danego kodu
                                    break;
                                }
                            }
                        }
                    }

                    if ($data_waznosci != "") {
                        $query = "update etykiety e  set e.data_waznosci='" . $data_waznosci . "' where e.id=$etykieta_nowa_dp;";
                        if ($pokaz_echo == "1")
                            echo "<br><br>" . $query;

                        if ($wykonaj_query == "1")
                            $result = $db->mGetResultAsXML($query);
                    }


                    $end_time = microtime(true);
                    $execution_time = intval($end_time - $start_time);
                    if ($execution_time >= 0) {
                        $sql = "INSERT INTO czas_wykonywania(nazwa_funkcji, ts, sekund)
                        values
                        ('deklaracja_wyrobu_skaner',NOW(),'" . $execution_time . "');";
                        $result44 = $db->mGetResultAsXML($sql);
                    }



                    $query = "update setup s set wartosc='0'
where nazwa='deklaracja_dp_blokada';";
                    if ($pokaz_echo == "1")
                        echo "<br><br>" . $query;

                    $result = $db->mGetResultAsXML($query);
                }
            }
        }
    } else {
        $komunikat['komunikat'] .= "Trwa deklarowanie. Proszę czekać na zakończenie lub po 1 min.";
    }

    if($komunikat['komunikat']=="OK"){
        drukowanie_etykiety($etykieta_nowa_dp, $drukarka_ip, $db);
    }

    

    $komunikat['etykieta'] = $etykieta_nowa_dp;
    $komunikat['operacja_id'] = $operacja_id;
    $komunikat['licznik'] = $licznik;

    //return new soapval('komunikat', 'xsd:string', $komunikat['komunikat']); //$komunikat['komunikat']
    //return array("komunikat" => $komunikat['komunikat'] ); //new soapval( //,"etykieta_id" => $etykieta_nowa_dp
//    echo "<pre>";
//    print_r($komunikat);
//    echo "</pre>";
    //return new soapval('komunikat', 'tns:MySoapObjectArray', $komunikat);
    return xml_from_indexed_array($komunikat);
}

function zamianapolskich($tekst) {
    $tabela = Array(
        //WIN
        "\xb9" => "a", "\xa5" => "A", "\xe6" => "c", "\xc6" => "C",
        "\xea" => "e", "\xca" => "E", "\xb3" => "l", "\xa3" => "L",
        "\xf3" => "o", "\xd3" => "O", "\x9c" => "s", "\x8c" => "S",
        "\x9f" => "z", "\xaf" => "Z", "\xbf" => "z", "\xac" => "Z",
        "\xf1" => "n", "\xd1" => "N",
        //UTF
        "\xc4\x85" => "a", "\xc4\x84" => "A", "\xc4\x87" => "c", "\xc4\x86" => "C",
        "\xc4\x99" => "e", "\xc4\x98" => "E", "\xc5\x82" => "l", "\xc5\x81" => "L",
        "\xc3\xb3" => "o", "\xc3\x93" => "O", "\xc5\x9b" => "s", "\xc5\x9a" => "S",
        "\xc5\xbc" => "z", "\xc5\xbb" => "Z", "\xc5\xba" => "z", "\xc5\xb9" => "Z",
        "\xc5\x84" => "n", "\xc5\x83" => "N",
        //ISO
        "\xb1" => "a", "\xa1" => "A", "\xe6" => "c", "\xc6" => "C",
        "\xea" => "e", "\xca" => "E", "\xb3" => "l", "\xa3" => "L",
        "\xf3" => "o", "\xd3" => "O", "\xb6" => "s", "\xa6" => "S",
        "\xbc" => "z", "\xac" => "Z", "\xbf" => "z", "\xaf" => "Z",
        "\xf1" => "n", "\xd1" => "N");

    return strtr($tekst, $tabela);
}

function drukowanie_etykiety($etykieta_id, $drukarka_ip, $db) {






    $komunikat = array();

    $wykonaj_query = "0";

    $pokaz_echo = "0";

    $query = 'SELECT e.id AS id, e.system_id AS system_id, e.etykieta_klient AS etykieta_klient, e.magazyn AS magazyn, e.active AS active, e.miejscep AS miejscep, e.przeznaczenie_id AS przeznaczenie_id, e.kod_id AS kod_id, e.paleta_id AS paleta_id, e.dataprod AS dataprod, e.data_waznosci AS data_waznosci, e.status AS status, e.blloc AS blloc, e.akcja_id AS akcja_id, e.akcja_id AS akcjanr_id, e.status_prism AS status_prism, e.stat AS stat, e.status_id AS status_id, st.nazwa AS status_nazwa, st.funkcja_stat AS funkcja_stat, e.kartony AS kartony, ed3.kolor AS kolor, ed3.plec AS plec, ed3.rozmiar_nr AS rozmiar_nr, ed3.uszkodzenie AS uszkodzenie, e.lot AS lot, e.sscc AS sscc, e.gtin AS gtin, e.edycja_et AS edycja_et, TRIM(TRAILING "." FROM TRIM(TRAILING "0" from e.ilosc)) AS ilosc, e.ts AS ts, e.nretykiety AS nretykiety, e.docin_id AS docin_id, e.docout_id AS docout_id, e.delivery_id AS delivery_id, e.listcontrol_id AS listcontrol_id, concat("Hala ",m.hala," ",m.regal,"-",m.miejsce,"-",m.poziom) AS adres, concat(m.regal,"-",m.miejsce,"-",m.poziom) AS adres2, k.kod_nazwa AS kod_nazwa, k.jm AS jm, k.kod AS kod, k.kod2 AS kod2, (if(e.ilosc is null,1,e.ilosc)/if(k.ilosc_w_opakowaniu is null,1,ilosc_w_opakowaniu)) AS ilosc_opak, k.opakowanie_jm AS opakowanie_jm, js.nazwa AS j_skladowania_nazwa, concat(p.ilosc, " ",tp.opis ) AS paletanazwa, p.pal_klient AS pal_klient, tp.kod AS tpkod, pdocin.imie_nazwisko AS docin_imie_nazwisko, pdout.imie_nazwisko AS pdout_imie_nazwisko, s.skrot AS skrot, m.hala AS hala, din.doc_nr AS doc_nr, din.doc_date AS doc_date, din.doc_ts AS doc_ts, din.doc_internal AS doc_internal, din.doc_type AS doc_type, din.pracownik_id AS pracownik_id_docin, din.kontrah_id AS kontrah_id_docin, din.doc_ref AS doc_ref, din.doc_uwagi AS doc_uwagi, din.dostawa_typ AS dostawa_typ, din1.nr_doc_dost AS nr_doc_dost, din1.data_wystawienia AS data_wystawienia, din1.nr_zam_mpg AS nr_zam_mpg, din1.numeroavviso AS numeroavviso, dout.docout_type AS docout_type, dout.docout_nr AS docout_nr, dout.docout_date AS docout_date, dout.docout_ts AS docout_ts, dout.pracownik_id AS pracownik_id_docout, dout.kontrah_id AS kontrah_id_docout, dout.docout_ref AS docout_ref, dout.docout_uwagi AS docout_uwagi, dout.pracownik_id_kier AS pracownik_id_kier, dout.docout_internal AS docout_internal, dout.docout_date_req AS docout_date_req, dl.delivery_nr AS delivery_nr, dl.delivery_internal AS delivery_internal, dl.delivery_date AS delivery_date, dl.pracownik_id AS pracownik_id_delivery, dl.delivery_ts AS delivery_ts, kin.logo AS kinlogo, kin.dest_code AS dest_code, kout.logo AS koutlogo, a.nazwa AS akcja_nazwa,(select count(1) from etykiety ee where ee.docin_id=e.docin_id and ee.id<=' . $etykieta_id . ' ) as lp FROM etykiety e LEFT JOIN status_system AS st ON e.status_id=st.id LEFT JOIN etykiety_dod3 AS ed3 ON e.id=ed3.id and e.system_id=ed3.system_id LEFT JOIN miejsca AS m ON e.miejscep=m.id LEFT JOIN kody AS k ON k.id=e.kod_id LEFT JOIN palety AS p ON e.paleta_id=p.id LEFT JOIN typypalet AS tp ON p.typypalet_id=tp.id LEFT JOIN jednostka_skladowania AS js ON p.j_skladowania_id=js.id LEFT JOIN docin AS din ON e.docin_id=din.id LEFT JOIN docout AS dout ON e.docout_id=dout.id LEFT JOIN delivery AS dl ON e.delivery_id=dl.id LEFT JOIN kontrah AS kin ON din.kontrah_id=kin.id LEFT JOIN kontrah AS kout ON dout.kontrah_id=kout.id LEFT JOIN pracownicy AS pdocin ON din.pracownik_id=pdocin.id LEFT JOIN pracownicy AS pdout ON dout.pracownik_id=pdout.id LEFT JOIN systemy AS s ON e.system_id=s.wartosc LEFT JOIN docin_dod1 AS din1 ON din.id=din1.docin_id LEFT JOIN akcja AS a ON a.id=e.akcja_id WHERE e.id = ' . $etykieta_id . '  LIMIT  1;';
    if ($pokaz_echo == "1")
        echo "<br><br>" . $query;

    $result = $db->mGetResultAsXML($query);
    $szukaj_etykiete = $result[0];

    if ($szukaj_etykiete['data_waznosci'] == "000-00-00") {
        $szukaj_etykiete['data_waznosci'] = "";
    }


    $layout = '^XA'
            . '^FO140,10	^ADN,60,30	^FD ' . $szukaj_etykiete['skrot'] . '^FS '
            . '^FO470,80	^ADN,30,14	^FDH:' . $szukaj_etykiete['hala'] . ' ' . $szukaj_etykiete['adres2'] . ' ^FS '
            . '^FO470,10	^ADN,30,10	^FD ' . $szukaj_etykiete['ts'] . '^FS '
            . '^FO30,80	^ADN,30,14	^FD';

    $layout .= ' ILOSC: ' . $szukaj_etykiete['ilosc'] . ' ' . $szukaj_etykiete['jm'];
    $layout .= '^FS'
            . '^FO1,140	^ADN,30,14	^FD';

    $layout .= ' ' . substr($szukaj_etykiete['kod'], 0, 25);
    $layout .= '^FS  '
            . '^FO10,190	^ADN,30,14	^FD  ' . zamianapolskich(substr($szukaj_etykiete['kod_nazwa'], 0, 25)) . '^FS  '
            . '^FO80,330^BY2	 ^BCN,100,Y,N,N ^A0,60 ^FDDS' . $szukaj_etykiete['paleta_id'] . '^FS'
            . '^FO570,160	^ADN,30,14	^FD ' . $szukaj_etykiete['doc_type'] . ' ' . $szukaj_etykiete['doc_nr'] . '^FS'
            . '^FO470,280	^ADN,30,14  ^FD Data PP: ' . $szukaj_etykiete['doc_date'] . '^FS'
            . '^FO470,350	^ADN,30,14	^FD LOT: ' . $szukaj_etykiete['lot'] . '^FS'
            . '^FO470,390	^ADN,30,14	^FD D.waz: ' . $szukaj_etykiete['data_waznosci'] . '^FS'
            . '^FO470,440	^ADN,30,14	^FD LP: ' . $szukaj_etykiete['lp'] . '^FS';

    $layout .= ' ^FS  '
            . '^FO470,370	^ADN,30,20	^FD';
//        if (!(empty($szukaj_etykiete['data_waznosci']) || $szukaj_etykiete['data_waznosci'] == "1970-01-01")) {
//            $layout.='DW: ' . date("ymd", strtotime($szukaj_etykiete['data_waznosci']));
//        } 
    $layout .= '^FS  '
            . ' ^XZ';
//    
//    $layout = '^XA
//^MMT
//^PW1181
//^LL1890
//^LS0
//^FT107,105^A0N,92,91^FH\^FD**** ' . $szukaj_etykiete['skrot'] . ' ***^FS
//^FT40,201^A0N,98,68^FH\^FD' . $szukaj_etykiete['kod'] . '^FS
//^FT40,352^A0N,97,47^FH\^FD' . $szukaj_etykiete['kod_nazwa'] . '^FS
//
//^FT40,514^A0N,98,50^FH\^FDILOSC: ' . $szukaj_etykiete['ilosc'] . ' ' . $szukaj_etykiete['jm'] . '^FS
//^FT43,613^A0N,62,50^FH\^FDMAG: ' . $szukaj_etykiete['magazyn'] . '^FS
//^FT40,697^A0N,62,50^FH\^FDMIEJSCE: ' . $szukaj_etykiete['adres2'] . '^FS
//
//^FT40,764^A0N,62,50^FH\^FDDOK: ' . $szukaj_etykiete['doc_type'] . ' - ' . $szukaj_etykiete['doc_nr'] . ' data: ' . $szukaj_etykiete['doc_date'] . '^FS
//
//^FT40,886^A0N,62,50^FH\^FDDATA PRZYD: ' . $szukaj_etykiete['data_waznosci'] . '^FS
//^FT40,955^A0N,62,50^FH\^FDPAL: ' . $szukaj_etykiete['paletanazwa'] . '^FS
//^FT40,1003^A0N,37,38^FH\^FDczas ' . $szukaj_etykiete['ts'] . '^FS
//^BY9,3,202^FT78,1257^BCN,,Y,N
//^FD>;' . $szukaj_etykiete['id'] . '^FS
//^FO39,647^GB705,0,2^FS
//^PQ1,0,1,Y^XZ';
    $file = fopen("/tmp/etykietakartonm.zbr", "w");
    fputs($file, $layout);
    fclose($file);
//flush();
//sleep(1);
    //$drukarka_ip='***********';


    $socket = socket_create(AF_INET, SOCK_STREAM, SOL_TCP);
    $result = socket_connect($socket, $drukarka_ip, "9100");
    socket_write($socket, $layout, strlen($layout));
    socket_close($socket);

    $komunikat['komunikat'] = "OK";
    return $komunikat;

    //return new soapval('komunikat', 'xsd:string', $komunikat['komunikat']); //$komunikat['komunikat']
    //return array("komunikat" => $komunikat['komunikat'] ); //new soapval( //,"etykieta_id" => $etykieta_nowa_dp
    //return new soapval('komunikat', 'tns:MySoapObjectArray', $komunikat);
}
