﻿using System;
using System.Linq;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using System.Threading;

namespace Tarczyn__Magazyn
{
    public partial class ZmianaMiejscaPaletowego : Form
    {
        ActionMenu myParent = null;
        string NR_DOK = "";
        string zapamietane_miejsce = "";
        int licznik = 0;
        string operac_id_global = "";

        string docin_id ="";
        string doc_nr = "";
        string ile = "";
        string listcontrol_id = "";

        string lewa = "";
        string prawa = "";


        bool czy_buffor = false;


        public ZmianaMiejscaPaletowego(ActionMenu c)
        {
            
            if (Wlasciwosci.GNG != "")
            {
                if (Wlasciwosci.TrybActionMenu == 1)
                {
                    Wlasciwosci.bazaDanych = 2;

                    BazaDanychExternal.ChangeHost(Wlasciwosci.GNG, Wlasciwosci.Varhost, Wlasciwosci.VarLoginMYSQL, Wlasciwosci.VarHasloMYSQL);

                }

                if (Wlasciwosci.TrybActionMenu == 0)
                {
                    Wlasciwosci.bazaDanych = 0;
                    BazaDanychExternal.ChangeHost(Wlasciwosci.Varbaza, Wlasciwosci.Varhost, Wlasciwosci.VarLoginMYSQL, Wlasciwosci.VarHasloMYSQL);
                }
            }
            //textBox2.Focus();

            string zapytanie = "SELECT last FROM docnumber d WHERE d.name='operacja_id';";

            operac_id_global = (string)BazaDanychExternal.Wyczytaj_Jedna_Wartosc(zapytanie);

            string zapyt = "update docnumber d set last=last+1 WHERE d.name='operacja_id'; ";
            BazaDanychExternal.Wyczytaj_Jedna_Wartosc(zapyt);

            
            FullScreenMode.OknoOFF(this);
            InitializeComponent();
            myParent = c;
            trybskanowania = 1;
            ZacznijSkanowanie();

            //BazaDanychExternal.Inicjalizacja(Wlasciwosci.Varbaza, Wlasciwosci.Varhost, Wlasciwosci.VarLoginMYSQL, Wlasciwosci.VarHasloMYSQL, this);  
        }


        List<string> Etykiety = new List<string>();
        Thread Skanowanie = null;


        public void ZacznijSkanowanie()
        {
            StringBuilder login = new StringBuilder();
            Skaner.UstawTryb_String(login);
            Skaner.Zacznij_Skanowanie();
            Skanowanie = new Thread(() => CheckString(login));
            Skanowanie.IsBackground = true;
            Skanowanie.Start();
        }
        private void CheckString(StringBuilder cc)
        {
            while (true)
            {
                if (cc.ToString() != "")
                {
                    this.Invoke(new EventHandler(delegate
                    {

                        dodawanie(cc.ToString());
                    }));
                    return;
                }
                Thread.Sleep(200);
            }
        }
        private void Zakoncz_Skanowanie()
        {
            try
            {
                Skanowanie.Abort();
                Skaner.Przewij_Skanowanie();
            }
            catch
            {
            }
        }

        private void Exit_Click_1(object sender, EventArgs e)
        {
            if (Obecna_Lista.Rows.Count > 1)
            {
                MessageBox.Show("Dokonaj najpierw zapisu zmiany miejsca.");
                return;

            }

            Zakoncz_Skanowanie();
            //BazaDanychExternal.ChangeHost(Wlasciwosci.Varbaza, Wlasciwosci.Varhost, Wlasciwosci.VarLoginMYSQL, Wlasciwosci.VarHasloMYSQL);
            myParent.Show();
            this.Close();
        }


        private void button2_Click(object sender, EventArgs e)
        {


        }




        private void wyborPoziomu()
        {
            if (textBox2.Text != "" && textBox1.Text != "")
            {
                //Exit.Enabled = false;
                textBox1.Enabled = false;
                textBox2.Enabled = false;
                lewej.Visible = false;
                prawej.Visible = false;

                string[] dzielenie = textBox1.Text.Split('-');
                if (dzielenie.Length != 5)
                {
                    MessageBox.Show("Zeskanuj ponownie miejsce.");
                    textBox1.Text = "";
                    Exit.Enabled = true;
                    textBox1.Enabled = true;
                    textBox2.Enabled = true;

                    return;
                }


                if (dzielenie[2] == "RMP_A" || dzielenie[2] == "RMP_B"  || dzielenie[2] == "STR")
                {
                    label4.Text = "Rampa/Strefa";
                    textBox3.Visible = true;
                    button1.Visible = true;
                    comboBox3.Visible = false;
                    lewej.Visible = false;
                    prawej.Visible = false;
                    textBox3.Focus();
                }
                else
                {
                    // wyswietla poziomy
                    //MessageBox.Show("select poziom, (select mm.miejsce from miejsca mm where mm.hala=m.hala and mm.regal=m.regal and mm.miejsce=(m.miejsce-1) limit 1) as lewa, (select mm.miejsce from miejsca mm where mm.hala=m.hala and mm.regal=m.regal and mm.miejsce=(m.miejsce+1) limit 1) as prawa from miejsca where hala='" + dzielenie[1] + "' and regal='" + dzielenie[2] + "' and miejsce='" + dzielenie[3] + "'");
                    DataTable bb = (DataTable)BazaDanychExternal.Wyczytaj_Tabele("select poziom, (select mm.miejsce from miejsca mm where mm.hala=m.hala and mm.regal=m.regal and mm.miejsce=(m.miejsce-1) limit 1) as lewa, (select mm.miejsce from miejsca mm where mm.hala=m.hala and mm.regal=m.regal and mm.miejsce=(m.miejsce+1) limit 1) as prawa from miejsca m where hala='" + dzielenie[1] + "' and regal='" + dzielenie[2] + "' and miejsce='" + dzielenie[3] + "'");

                    if (bb.Rows.Count < 1)
                    {
                        MessageBox.Show("Zeskanuj ponownie miejsce.");
                        textBox1.Text = "";
                        Exit.Enabled = true;
                        textBox1.Enabled = true;
                        textBox2.Enabled = true;
                        return;
                    }

                    string lew = "";
                    string praw = "";
                    comboBox3.Items.Clear();
                    comboBox3.Visible = true;
                    label4.Visible = true;
                    for (int b = 0; b < bb.Rows.Count; b++)
                    {
                        comboBox3.Items.Add(bb.Rows[b][0].ToString());
                        if (bb.Rows[b][1].ToString() != "")
                        {
                            lew = "1";
                        }
                        if (bb.Rows[b][2].ToString() != "")
                        {
                            praw = "1";
                        }                        
                    }
                    wlacz = true;


                    if (lew != "")
                    {
                        lewej.Text = bb.Rows[0][1].ToString();
                        lewej.Visible = true;
                    }
                    if (praw != "")
                    {
                        prawej.Text = bb.Rows[0][2].ToString();
                        prawej.Visible = true;
                    }
                    comboBox3.Focus();
                }

                

                /*BindingSource bs = new BindingSource();
                bs.DataSource = _dl_wybor;
                comboBox3.DataSource = bs;
                 */

            }

        }


        private void dodawanie(string gg)
        {
            Zakoncz_Skanowanie();

            if (BazaDanychExternal.SprawdzCzyIstniejePolaczenie() == false)
            {
                MessageBox.Show("Brak połączenia z bazą danych , spróbuj ponownie.");
                ZacznijSkanowanie();
                return;
            }


            if (trybskanowania == 2)
            {
                textBox1.Text = gg;
                
                wyborPoziomu();

                if (czy_buffor == true)
                {
                    
                    label1.Text = Count_PZ(gg) + " / " + Count_PZ_Wstawione(gg);
                }
                
                
            }

            if (trybskanowania == 1)
            {


                //MessageBox.Show("-4");


                
                /*
                if (Count_do_Wstawienia(gg) == "0")
                {
                    czy_buffor = false;
                    //MessageBox.Show("-3");
                }

                */


                /*


                if (czy_buffor == true)
                {
                    //MessageBox.Show("-2");

                    if (Sprawdz_dokument_etykieta(gg, docin_id,listcontrol_id) != "1")
                    {
                        //MessageBox.Show("-7");
                        MessageBox.Show("Et nie jest z " + doc_type + " / " + doc_nr + " / LK " + listcontrol_id);
                        //ZacznijSkanowanie();
                        textBox1.Text = "";
                        return;
                    }


                    if (Czy_Produkcja(gg) == "0")
                    {
                        MessageBox.Show("Etykieta " + gg + " jest już pobrana");
                        textBox1.Text = "";
                        return;
                    }

                }

                //MessageBox.Show("-1");


                if (czy_buffor != true) //Sprawdz_Czy_Mozna(tabela2.Rows[0]["id"].ToString()) == "1"
                {

                    //MessageBox.Show("1");
                    string[] aa = new string[4];
                    aa = get_dokument(gg);
                    

                    if (aa[1] == "DP")
                    {
                    ///MessageBox.Show(aa[0] + ";1a;" + aa[3]);
                    ile = Ile_Pozostalo(aa[0], aa[3]);

                        //MessageBox.Show("2");

                        DialogResult result3 = MessageBox.Show("Czy chcesz rozpocząć  " + Environment.NewLine + "  " + aa[1] + " / " + aa[2] + " LK:" + aa[3] + " /  et: " + ile + ";  ?",
                "Zmiana miejsca",
                MessageBoxButtons.YesNo,
                MessageBoxIcon.Question,
                MessageBoxDefaultButton.Button2);
                        if (result3 == DialogResult.Yes)
                        {
                            czy_buffor = true;
                            docin_id = aa[0];
                            doc_type = aa[1];
                            doc_nr = aa[2];
                            listcontrol_id = aa[3];
                        }
                        else
                        {
                            ZacznijSkanowanie();
                            return;
                        }

                    }
                    
                    
                }

                */


                //*/
                



                 



                /* 
                 * 
                if (czy_buffor == true && textBox1.Text != "" )
                {

                }
                else
                {
                    return;
                }


                 //////////////////////////////*/


                textBox2.Text = gg;
                textBox1.Focus();
                if(checkBox1_zapamietaj.Checked)
                {
                    textBox1.Text = zapamietane_miejsce;
                    funkcja_zapamietaj_miejsce();


                    /*
                    if (czy_buffor == true)
                    {
                        label1.Text = Count_PZ(gg) + " / " + Count_PZ_Wstawione(gg);

                        if (Count_do_Wstawienia(gg) == "0")
                        {
                            czy_buffor = false;
                        }
                    }

                    */
                }                
            }

            
            //////try
            //////{
            //////    if (miejscepaletowe() == "")
            //////    {

            //////        MessageBox.Show("Proszę wybrać prawidłowe miejsce.");
            //////        return;


            //////    }
            //////    textBox2.Text = gg;


            //////    object test = BazaDanychExternal.Wyczytaj_Jedna_Wartosc("select id from etykiety where paleta_id ='" + gg.Replace("DS", "") + "' and active=1");
            //////    if (test == null)
            //////    {
            //////        MessageBox.Show("Brak palety w systemie , lub brak etykiet na danym nośniku.");
            //////        return;
            //////    }

            //////    Obecna_Lista.Rows.Add(gg, comboBox1.Text, comboBox2.Text, comboBox4.Text, comboBox3.Text);
            //////    ZacznijSkanowanie();


            //////}
            //////catch (Exception ex)
            //////{
            //////    MessageBox.Show(ex.ToString());
            //////    ZacznijSkanowanie();
            //////}
        }


        private static string[] get_dokument(string etykieta) //pobiera
        {
            string zapytanie = "select e.docin_id,d.doc_type,d.doc_nr,e.listcontrol_id from etykiety e left join docin d on e.docin_id=d.id   where e.id=" + etykieta + "  limit 1";
            object obj2 = BazaDanychExternal.Wyczytaj_Tabele(zapytanie);
            DataTable tabela = (DataTable)obj2;
            string[] bb = new string[4];
            bb[0] = "" + tabela.Rows[0]["docin_id"].ToString();
            bb[1] = tabela.Rows[0]["doc_type"].ToString();
            bb[2] = tabela.Rows[0]["doc_nr"].ToString();
            bb[3] = tabela.Rows[0]["listcontrol_id"].ToString();
            return bb;
        }



        private void Zakoncz_Click(object sender, EventArgs e)
        {

        }


        private void metoda(DataTable x, ComboBox values, string kolumna, string warunek)
        {


        }


        DataTable MiejscaPaletowe = new DataTable();
        DataTable Obecna_Lista = new DataTable();


        private void WczytywanieNaLinie_Load_1(object sender, EventArgs e)
        {
            //ZacznijSkanowanie();
            ////////DataTable temp = (DataTable)BazaDanychExternal.Wyczytaj_Tabele("SELECT distinct hala FROM miejsca") ;
            ////////Obecna_Lista.Columns.Add("paleta");
            ////////Obecna_Lista.Columns.Add("hala");
            ////////Obecna_Lista.Columns.Add("regal");
            ////////Obecna_Lista.Columns.Add("miejsce");
            ////////Obecna_Lista.Columns.Add("poziom");
            ////////dataGrid1.DataSource = Obecna_Lista;
            ////////for (int q = 0; q < temp.Rows.Count; q++)
            ////////{
            ////////    comboBox1.Items.Add(temp.Rows[q][0].ToString());
            ////////}
        }

        private void button2_Click_1(object sender, EventArgs e)
        {

        }

        private void button1_Click(object sender, EventArgs e)
        {

        }

        DataTable AktualneSztuki = new DataTable();
        DataTable Aktualnedane = new DataTable();


        private int pobierz_id_miejsca(string hala_local, string regal_local, string miejsce_local, string poziom_local) //pobiera
        {
            //if(nr_etykiety) 
            string zapytanie = "";
            zapytanie = "select id, widoczne from miejsca m where hala='" + hala_local + "' and regal='" + regal_local + "' and miejsce='" + miejsce_local + "' and poziom='" + poziom_local + "' limit 1;";

            object obj2 = BazaDanychExternal.Wyczytaj_Tabele(zapytanie);

            DataTable tabela = (DataTable)obj2;


            if (tabela.Rows.Count < 1)
            {  //sprawdza 
                return 0;
            }
            else
            {
                if (tabela.Rows[0]["widoczne"] == "0") return -1;
                return Convert.ToInt32(tabela.Rows[0]["id"]);
            }
        }
       
        
        string miejscepaletowe2()
        {


            string[] dzielenie = textBox1.Text.Split('-');
            object test = null;

            //if (dzielenie[2] == "RMP" || dzielenie[2] == "STR")
            //{
            //    dzielenie[4] = "1";
                //test = BazaDanychExternal.Wyczytaj_Jedna_Wartosc("select id from miejsca where hala='" + dzielenie[1] + "' AND regal='" + dzielenie[2] + "' AND miejsce='" + dzielenie[3] + "' AND poziom='1'");
                //MessageBox.Show("select id from miejsca where hala='" + dzielenie[1] + "' AND regal='" + dzielenie[2] + "' AND miejsce='" + textBox3.Text + "' AND poziom='1'");
            //}
            
                test = BazaDanychExternal.Wyczytaj_Jedna_Wartosc("select id from miejsca where hala='" + dzielenie[1] + "' AND regal='" + dzielenie[2] + "' AND miejsce='" + dzielenie[3] + "' AND poziom='" + dzielenie[4] + "'");
            

            
            //DataRow[] t = MiejscaPaletowe.Select("hala='" + Aktualnedane.Rows[h]["hala"].ToString() + "' AND regal='" + Aktualnedane.Rows[h]["regal"].ToString() + "' AND miejsce='" + Aktualnedane.Rows[h]["miejsce"].ToString() + "' AND poziom='" + Aktualnedane.Rows[h]["poziom"].ToString()+"'");
            //MessageBox.Show("select id from miejsca where hala='" + dzielenie[1] + "' AND regal='" + dzielenie[2] + "' AND miejsce='" + dzielenie[3] + "' AND poziom='" + comboBox3.Text + "'");
            if (test == null)
            {
                return "";
            }
            return (string)test;
        }


        private void dokonajZmiany()
        {


            //  for (int q = 0; q < Obecna_Lista.Rows.Count; q++)

            int nowe_m_prawa = 0;
            int nowe_m_lewa = 0;


            string[] dzielenie = textBox1.Text.Split('-');
            int nowe_m = pobierz_id_miejsca("1", dzielenie[2], dzielenie[3], dzielenie[4]);

            if (lewej.Checked == true)
            {
                //MessageBox.Show("3");
                nowe_m_lewa = pobierz_id_miejsca("1", dzielenie[2], "" + (Convert.ToInt32(dzielenie[3]) - 1), dzielenie[4]);
                //MessageBox.Show("4");
            }
            if (prawej.Checked == true)
            {
                //MessageBox.Show("5");
                nowe_m_prawa = pobierz_id_miejsca("1", dzielenie[2], "" + (Convert.ToInt32(dzielenie[3]) + 1), dzielenie[4]);
            }

            //MessageBox.Show("6");


            if (Wlasciwosci.TrybActionMenu == 1)
            { //wmsgg

                if (textBox2.Text[0] == 'D' && textBox2.Text[1] == 'S')
                {
                    DataTable temp = (DataTable)BazaDanychExternal.Wyczytaj_Tabele("select id,system_id,miejscep,paleta_id  from etykiety where paleta_id=" + textBox2.Text.Replace("DS", "") + " and (active=1 or active is null)");


                    if (NR_DOK == "") { NR_DOK = (string)BazaDanychExternal.Wyczytaj_Jedna_Wartosc("select max(doc_nr)+1 FROM zmianym"); }
                    if (temp.Rows.Count > 0)
                    {
                        Wlasciwosci.system_id_id = temp.Rows[0][1].ToString();
                        string nowemiejsce = miejscepaletowe2();
                        if (nowemiejsce != "")
                        {
                            for (int t = 0; t < temp.Rows.Count; t++)
                            {
                                BazaDanychExternal.DokonajUpdate("insert into zmianym(typ,doc_nr,pracownik_id,data,etykieta,system_id,stare_m,nowe_m,doc_internal,stat,tszm) values(" +
                                    "'ZM'," + NR_DOK + "," + Wlasciwosci.id_Pracownika + ",sysdate()," + temp.Rows[t][0].ToString() + "," + temp.Rows[t][1].ToString() + "," + temp.Rows[t][2].ToString() + "," + nowemiejsce + ",3,1,sysdate())");
                                
                                BazaDanychExternal.DokonajUpdate("insert into miejsca_fizyczne(etykieta_id, miejscep, miejsce_id,paleta_id) values ('" + temp.Rows[t][0].ToString() + "','" + nowe_m + "','" + nowe_m + "','" + temp.Rows[t][3].ToString() + "'  );");
                                if (lewej.Checked == true) BazaDanychExternal.DokonajUpdate("insert into miejsca_fizyczne(etykieta_id, miejscep, miejsce_id,paleta_id) values ('" + temp.Rows[t][0].ToString() + "','" + nowe_m + "','" + nowe_m_lewa + "','" + temp.Rows[t][3].ToString() + "');");
                                if (prawej.Checked == true) BazaDanychExternal.DokonajUpdate("insert into miejsca_fizyczne(etykieta_id, miejscep, miejsce_id,paleta_id) values ('" + temp.Rows[t][0].ToString() + "','" + nowe_m + "','" + nowe_m_prawa + "','" + temp.Rows[t][3].ToString() + "');");


                                if(t==0){
                                    BazaDanychExternal.DokonajUpdate("insert into operacje(etykieta_id, doc_type, doc_nr, imie_nazwisko, typ_operacji,system_id,wozek,operac_id,ilosc) values('" + temp.Rows[t][0].ToString() + "','ZM','" + NR_DOK + "','" + Wlasciwosci.imie_nazwisko + "','ZM','" + Wlasciwosci.system_id_id + "','" + Wlasciwosci.wozek + "','" + operac_id_global + "','1');");

                                }
                                
                            }
                            BazaDanychExternal.DokonajUpdate("update etykiety set miejscep = " + nowemiejsce + " where paleta_id=" + textBox2.Text.Replace("DS", "") + " and (active=1 or active is null) ");
                            licznik++;

                            lewej.Checked = false; 
                            lewej.Text = "";
                            prawej.Checked = false;
                            prawej.Text = "";
                            
                        }
                        else
                        {
                            MessageBox.Show("Brak takiego miejsca w bazie. Spróbuj ponownie.");
                            return;
                        }

                        
                    }

                    else
                    {
                        MessageBox.Show("Brak takiej palety w systemie lub jest nieaktywna , spróbuj ponownie.");
                    }

                }

                else
                {
                    MessageBox.Show("Wczytaj nr DS palety, spróbuj ponownie.");
                }



            }
            else
            {
                if (textBox2.Text[0] != 'D' && textBox2.Text[1] != 'S')
                {
                    DataTable temp = (DataTable)BazaDanychExternal.Wyczytaj_Tabele("select id,system_id,miejscep,paleta_id from etykiety where id=" + textBox2.Text + " and (active=1 or active is null)");



                    if (temp.Rows.Count > 0)
                    {
                        Wlasciwosci.system_id_id = temp.Rows[0][1].ToString();

                        if (NR_DOK == "") { NR_DOK = (string)BazaDanychExternal.Wyczytaj_Jedna_Wartosc("select IFNULL(max(doc_nr)+1,1) as doc_nr FROM zmianym z left join etykiety e on z.etykieta=e.id WHERE e.system_id=" + temp.Rows[0]["system_id"].ToString() + ";"); }
                        string nowemiejsce = miejscepaletowe2();



                        if (nowemiejsce != "")
                        {

                            for (int t = 0; t < temp.Rows.Count; t++)
                            {
                                BazaDanychExternal.DokonajUpdate("insert into zmianym(typ,doc_nr,pracownik_id,data,etykieta,system_id,stare_m,nowe_m,doc_internal,stat,tszm) values(" +
                                    "'ZM'," + NR_DOK + "," + Wlasciwosci.id_Pracownika + ",sysdate()," + temp.Rows[t][0].ToString() + "," + temp.Rows[t][1].ToString() + "," + temp.Rows[t][2].ToString() + "," + nowemiejsce + ",3,1,sysdate())");
                                BazaDanychExternal.DokonajUpdate("insert into miejsca_fizyczne(etykieta_id, miejscep, miejsce_id,paleta_id) values ('" + temp.Rows[t][0].ToString() + "','" + nowe_m + "','" + nowe_m + "','" + temp.Rows[t][3].ToString() + "'  );");
                                if (lewej.Checked == true)  BazaDanychExternal.DokonajUpdate("insert into miejsca_fizyczne(etykieta_id, miejscep, miejsce_id,paleta_id) values ('" + temp.Rows[t][0].ToString() + "','" + nowe_m + "','" + nowe_m_lewa +  "','" + temp.Rows[t][3].ToString() + "');");
                                if (prawej.Checked == true) BazaDanychExternal.DokonajUpdate("insert into miejsca_fizyczne(etykieta_id, miejscep, miejsce_id,paleta_id) values ('" + temp.Rows[t][0].ToString() + "','" + nowe_m + "','" + nowe_m_prawa + "','" + temp.Rows[t][3].ToString() + "');");

                            }
                            BazaDanychExternal.DokonajUpdate("update etykiety set miejscep = " + nowemiejsce + " where id=" + textBox2.Text + " and (active=1 or active is null) ");
                            BazaDanychExternal.DokonajUpdate("insert into operacje(etykieta_id, doc_type, doc_nr, imie_nazwisko, typ_operacji,system_id,wozek,operac_id,ilosc) values('" + textBox2.Text + "','ZM','" + NR_DOK + "','" + Wlasciwosci.imie_nazwisko + "','ZM','" + Wlasciwosci.system_id_id + "','" + Wlasciwosci.wozek + "','" + operac_id_global + "','1');");
                            licznik++;
                            lewej.Checked = false;
                            lewej.Text = "";
                            prawej.Checked = false;
                            prawej.Text = "";
                       }
                        else
                        {
                            MessageBox.Show("Brak takiego miejsca w bazie. Spróbuj ponownie.");
                            return;
                        }


                    }

                    else
                    {
                        MessageBox.Show("Brak takiej etykiety w systemie lub jest nieaktywna , spróbuj ponownie.");
                    }

                }
                else
                {
                    MessageBox.Show("Wczytaj nr etykiety, nie DS palety, spróbuj ponownie.");
                }

            }





        }


        private void button2_Click_2(object sender, EventArgs e)
        {
            
            //ZacznijSkanowanie();
            //trybskanowania = 0;
        }


        int trybskanowania = 0;
        private void textBox2_GotFocus(object sender, EventArgs e)
        {
            Zakoncz_Skanowanie();
            ZacznijSkanowanie();
            if (textBox2 == sender)
                trybskanowania = 1;
            if (textBox1 == sender)
                trybskanowania = 2;

        }

        private void textBox2_LostFocus(object sender, EventArgs e)
        {
            Zakoncz_Skanowanie();
            trybskanowania = 0;
        }

        private void comboBox1_SelectedIndexChanged(object sender, EventArgs e)
        {
            //////comboBox2.Items.Clear();
            //////comboBox3.Items.Clear();
            //////comboBox4.Items.Clear();

            //////DataTable temp = (DataTable)BazaDanychExternal.Wyczytaj_Tabele("SELECT distinct regal FROM miejsca where hala='"+comboBox1.Text+"'");
            //////for (int q = 0; q < temp.Rows.Count; q++)
            //////{
            //////    comboBox2.Items.Add(temp.Rows[q][0].ToString());
            //////}

        }

        private void comboBox2_SelectedIndexChanged(object sender, EventArgs e)
        {

            ////////comboBox3.Items.Clear();
            ////////comboBox4.Items.Clear();

            ////////DataTable temp = (DataTable)BazaDanychExternal.Wyczytaj_Tabele("SELECT distinct poziom FROM miejsca where hala='" + comboBox1.Text + "' and regal='" + comboBox2.Text + "'");
            ////////for (int q = 0; q < temp.Rows.Count; q++)
            ////////{
            ////////    comboBox3.Items.Add(temp.Rows[q][0].ToString());
            ////////}
        }

        bool wlacz = false;
        private void comboBox3_SelectedIndexChanged(object sender, EventArgs e)
        {

            if (comboBox3.SelectedIndex == -1)
                return;

            if (wlacz == false)
                return; 
            ////comboBox4.Items.Clear();

            ////DataTable temp = (DataTable)BazaDanychExternal.Wyczytaj_Tabele("SELECT distinct miejsce FROM miejsca where hala='" + comboBox1.Text + "' and regal='" + comboBox2.Text + "' and poziom='"+comboBox3.Text+"'");
            ////for (int q = 0; q < temp.Rows.Count; q++)
            ////{
            ////    comboBox4.Items.Add(temp.Rows[q][0].ToString());
            ////}
            string[] dzielenie = textBox1.Text.Split('-');
            textBox1.Text = "" + dzielenie[0] + "-" + dzielenie[1] + "-" + dzielenie[2] + "-" + dzielenie[3] + "-" + comboBox3.Text;

            dokonajZmiany();
            textBox1.Enabled = true;
            textBox2.Enabled = true;
            
            zapamietane_miejsce = textBox1.Text;


            label2.Text = "Ostatnio: " + textBox2.Text + " w R:" + dzielenie[2] + " M:" + dzielenie[3] + " P:" + comboBox3.Text;
            
            label1.Text = "Licznik:" + licznik;
            textBox1.Text = "";
            textBox2.Text = "";
            Exit.Enabled = true;
            comboBox3.Visible = false;
            label4.Visible = false;
            wlacz = false;
            trybskanowania = 1;
            textBox2.Focus();
            checkBox1_zapamietaj.Visible = true;

        }

        private void button1_Click_1(object sender, EventArgs e)
        {

            ////comboBox4.Items.Clear();

            ////DataTable temp = (DataTable)BazaDanychExternal.Wyczytaj_Tabele("SELECT distinct miejsce FROM miejsca where hala='" + comboBox1.Text + "' and regal='" + comboBox2.Text + "' and poziom='"+comboBox3.Text+"'");
            ////for (int q = 0; q < temp.Rows.Count; q++)
            ////{
            ////    comboBox4.Items.Add(temp.Rows[q][0].ToString());
            ////}

            string[] dzielenie = textBox1.Text.Split('-');
            textBox1.Text = "" + dzielenie[0] + "-" + dzielenie[1] + "-" + dzielenie[2] + "-" + textBox3.Text + "-1";



            dokonajZmiany();
            textBox1.Enabled = true;
            textBox2.Enabled = true;
            zapamietane_miejsce = textBox1.Text;


            label2.Text = "Ostatnio: " + textBox2.Text + " w R:" + dzielenie[2] + " M:" + textBox3.Text;
            //licznik++;
            label1.Text = "Licznik:" + licznik;


            textBox1.Text = "";
            textBox2.Text = "";
            textBox3.Text = "";
            Exit.Enabled = true;
            comboBox3.Visible = false;
            label4.Visible = false;
            wlacz = false;
            trybskanowania = 1;
            textBox2.Focus();
            checkBox1_zapamietaj.Visible = true;
            textBox3.Visible = false;
            button1.Visible = false;
            label4.Text = "Poziom:";
            

        }
        private void funkcja_zapamietaj_miejsce()
        {
            dokonajZmiany();
            trybskanowania = 1;
            textBox2.Focus();
            textBox2.Text="";
        }

        public static string Ile_Pozostalo(string docin_id, string listcontrol_id_local)
        {

            //"select (SELECT sum(if(regal='WYR',1,0)) as ile FROM etykiety ee left join miejsca m on ee.miejscep=m.id where ee.docin_id=e.docin_id) as PZ from etykiety e left join docout d on e.docout_id=d.id where (e.etykieta_klient='638542' or e.id='638542')  order by e.id desc limit 1";
            //
            string zapytanie = "SELECT sum(if((regal='WYR' or regal like '%RMP%' or regal is null),1,0)) as ile FROM etykiety ee left join miejsca m on ee.miejscep=m.id where ee.listcontrol_id='" + listcontrol_id_local + "' or  ee.docin_id='" + docin_id + "'";
            
            //string zapytanie = "SELECT count(1)-(select count(1) from operacje o  where doc_type='DL' and doc_nr=delivery_nr and typ_operacji='DL_SZYK') as dl_szyk FROM etykiety e left join docout d on e.docout_id=d.id left join delivery dd on e.delivery_id=dd.id where (e.delivery_id='" + delivery_id + "' or (d.docout_type='" + docout_type + "' and d.docout_nr='" + docout_nr + "')) and  e.system_id='" + Wlasciwosci.system_id_id + "' ;";
            string aa = (string)BazaDanychExternal.Wyczytaj_Jedna_Wartosc(zapytanie);
            return aa;
        }

        public static string Count_PZ(string etykieta)
        {

            string zapytanie = "select (SELECT count(1) as ile FROM etykiety ee left join miejsca m on ee.miejscep=m.id where ee.docin_id=e.docin_id or ee.listcontrol_id=e.listcontrol_id) as PZ from etykiety e left join docout d on e.docout_id=d.id where (e.etykieta_klient='" + etykieta + "' or e.id='" + etykieta + "')  order by e.id desc limit 1";
            string aa = (string)BazaDanychExternal.Wyczytaj_Jedna_Wartosc(zapytanie);
            return aa;
        }


        
        public static string Count_PZ_Wstawione(string etykieta)
        {
            string zapytanie = "select (SELECT sum(if(!(regal='WYR' or regal like '%RMP%' or regal is null),1,0)) as ile FROM etykiety ee left join miejsca m on ee.miejscep=m.id where ee.docin_id=e.docin_id or ee.listcontrol_id=e.listcontrol_id) as PZ from etykiety e left join docout d on e.docout_id=d.id where (e.etykieta_klient='" + etykieta + "' or e.id='" + etykieta + "')  order by e.id desc limit 1";
            string aa = (string)BazaDanychExternal.Wyczytaj_Jedna_Wartosc(zapytanie);
            return aa;
        }
        public static string Count_do_Wstawienia(string etykieta)
        {
            string zapytanie = "select (SELECT sum(if((regal='WYR' or regal like '%RMP%' or regal is null),1,0)) as ile FROM etykiety ee left join miejsca m on ee.miejscep=m.id where ee.docin_id=e.docin_id or ee.listcontrol_id=e.listcontrol_id) as PZ from etykiety e left join docout d on e.docout_id=d.id where (e.etykieta_klient='" + etykieta + "' or e.id='" + etykieta + "')  order by e.id desc limit 1";
            string aa = (string)BazaDanychExternal.Wyczytaj_Jedna_Wartosc(zapytanie);
            return aa;
        }


        public static string Sprawdz_dokument_etykieta(string etykieta, string docin_id, string listcontrol_id_local)
        {
            string zapytanie = "SELECT count(1) FROM etykiety e left join docout d on e.docin_id=d.id  where (e.docin_id='" + docin_id + "' or e.listcontrol_id='" + listcontrol_id_local + "' )  and e.id='" + etykieta + "' and e.system_id='" + Wlasciwosci.system_id_id + "'  ;";
            string aa = (string)BazaDanychExternal.Wyczytaj_Jedna_Wartosc(zapytanie);
            return aa;
        }

        public static string Czy_Produkcja(string etykieta)
        {
            string zapytanie = "SELECT count(1) FROM etykiety e left join docout d on e.docin_id=d.id  left join miejsca m on e.miejscep=m.id  where  e.id='" + etykieta + "' and e.system_id='" + Wlasciwosci.system_id_id + "' and (regal='WYR' or regal like '%RMP%' or regal is null)";
            
            //SELECT count(1) FROM etykiety e left join docout d on e.docin_id=d.id  where (e.docin_id='" + docin_id + "' )  and e.id='" + etykieta + "' and e.system_id='" + Wlasciwosci.system_id_id + "'  ;";
            string aa = (string)BazaDanychExternal.Wyczytaj_Jedna_Wartosc(zapytanie);
            return aa;
        }

        private void checkBox1_zapamietaj_CheckStateChanged(object sender, EventArgs e)
        {
            if (!checkBox1_zapamietaj.Checked)
            {
                textBox1.Text = "";
                textBox2.Text = ""; 
                checkBox1_zapamietaj.Visible = false;
            }
            else
            {
                textBox1.Text = zapamietane_miejsce;
            }
            textBox2.Focus();
        }

        private void button3_Click(object sender, EventArgs e)
        {
            this.Skanowanie.Abort();
            Skaner.Przewij_Skanowanie();
            //Wolne_Miejsca nowy = new Wolne_Miejsca(this);
            //nowy.Show();
            this.Hide();
        }

        private void lewej_CheckStateChanged(object sender, EventArgs e)
        {
            comboBox3.Focus();
        }

        private void prawej_CheckStateChanged(object sender, EventArgs e)
        {
            comboBox3.Focus();
        }





    }
}