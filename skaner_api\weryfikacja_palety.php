<?php

//include_once 'db_pdo.php';
include_once 'Db.class.php';
include_once 'funkcje.inc';
error_reporting(E_ALL);
ini_set('display_errors', 1);


// CREATE TABLE `wmsgg`.`palety_weryfikacja` (
//     `id` INT NOT NULL AUTO_INCREMENT,
//     `paleta_id` INT NULL,
//     `date` DATE NULL,
//     `ts` TIMESTAMP NULL,
//     `typypalet_id_old` INT NULL,
//     `typypalet_id_old` INT NULL,
//     PRIMARY KEY (`id`))
//   ENGINE = InnoDB
//   DEFAULT CHARACTER SET = utf8
//   COLLATE = utf8_bin;


// CREATE TABLE `palety` (
//     `id` int unsigned NOT NULL AUTO_INCREMENT,
//     `typypalet_id` int unsigned DEFAULT NULL,
//     `ilosc` int unsigned DEFAULT '0',
//     `j_skladowania_id` int unsigned DEFAULT NULL,
//     `pal_klient` varchar(45) DEFAULT NULL,
//     `ts_inwentaryzacja` datetime DEFAULT NULL,
//     `result_inwentaryzacja` varchar(45) DEFAULT NULL,
//     `ts_utworzenia` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
//     `pal_docin_id` int unsigned DEFAULT '0',
//     PRIMARY KEY (`id`),
//     KEY `Index_2` (`typypalet_id`) USING BTREE,
//     KEY `Index_3` (`j_skladowania_id`) USING BTREE
//   ) ENGINE=MyISAM AUTO_INCREMENT=11572125 DEFAULT CHARSET=latin1 ROW_FORMAT=DYNAMIC;




$db = new Db();

if (empty($argv)) {
    //$argv = array();
    $argv[1] = $_GET['db'];
    $argv[2] = $_GET['akcja'];
}

$baza_danych = "wmsgg";
$akcja = $_GET['akcja'];
$pracownik_id = $_GET['pracownik_id'];
$paleta_id = $_GET["paleta_id"];
$typypalet_id_new = $_GET["typypalet_id_new"];
$pracownik_id = $_GET["pracownik_id"];


//http://25.56.91.22/wmsgg/public/skaner_api/delivery_skanowanie_etykiety.php?db=wmsgg&akcja=szukaj&system_id=6&paleta_id=547144


$komunikat = "OK";
if ($akcja == "weryfikacja") {
    palety_weryfikacja($paleta_id, $typypalet_id_new, $pracownik_id, $db);
    return xml_from_indexed_array(array('komunikat' => $komunikat));
}



// napisz skrypt, który sprawdzi czy paleta jest w bazie danych i czy jest w odpowiednim type i wynik weryfikacj zostanie zapisany w tabeli palety_weryfikacja
function palety_weryfikacja($paleta_id, $typypalet_id_new, $pracownik_id, $db)
{

    $sql = 'SELECT * FROM palety WHERE id=' . $paleta_id . ' ;';
    $result = $db->mGetResultAsArray($sql);
    // jeśli nie ma palety w bazie danych to zwróć komunikat
    if (count($result) == 0) {
        return "Brak palety w bazie danych";
    }

    $typypalet_id_old = $result[0]['typypalet_id'];

    // zapisać weryfikację palety
    $sql = 'INSERT INTO palety_weryfikacja(paleta_id, typypalet_id_old, typypalet_id_new, date, ts,pracownik_id) 
VALUES (' . $paleta_id . ',' . $typypalet_id_old . ',' . $typypalet_id_new . ',NOW(),NOW(),' . $pracownik_id . ');';
    $result = $db->mGetResultAsArray($sql);

    // jeśli różne typy palet  to update
    if ($typypalet_id_old != $typypalet_id_new) {
        $sql = 'UPDATE palety SET typypalet_id=' . $typypalet_id_new . ' WHERE id=' . $paleta_id . ' ;';
        $result = $db->mGetResultAsArray($sql);
    }
}
