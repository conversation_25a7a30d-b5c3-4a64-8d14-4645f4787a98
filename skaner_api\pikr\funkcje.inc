<?php

function get_ilosc_etykiety($baza_danych, $etykieta_id, $system_id_id, $db)
{
    $sql = "SELECT TRIM(TRAILING '.' FROM TRIM(TRAILING '0' FROM e.ilosc)) AS ilosc FROM $baza_danych.etykiety e
WHERE e.id=$etykieta_id and system_id='" . $system_id_id . "'   limit 1 "; //,dc_prac_id
    //echo "<br>" . $sql;
    $result = $db->mGetResultAsXML($sql);
    $ilosc = 0;

    foreach ($result as $index => $aRow) {
        $ilosc = $aRow['ilosc'];
    }
    return $ilosc;
}



function get_kontrah_wew($baza_danych, $system_id_id, $db)
{
    $sql = "select kontrah_wew_id from $baza_danych.systemy where wartosc='" . $system_id_id . "'  limit 1 "; //,dc_prac_id
    //echo "<br>" . $sql;
    $result = $db->mGetResultAsXML($sql);
    $kontrah_wew_id = 0;

    foreach ($result as $index => $aRow) {
        $kontrah_wew_id = $aRow['kontrah_wew_id'];
    }
    return $kontrah_wew_id;
}

function get_pracownik($baza_danych, $imie_nazwisko, $db)
{
    $sql = "SELECT id FROM $baza_danych.pracownicy p
WHERE p.imie_nazwisko='" . $imie_nazwisko . "' limit 1 "; //,dc_prac_id
    //echo "<br>" . $sql;
    $result = $db->mGetResultAsXML($sql);
    $pracownik_id = 0;

    foreach ($result as $index => $aRow) {
        $pracownik_id = $aRow['id'];
    }
    return $pracownik_id;
}

function docnumber_increment($baza_danych, $name, $db)
{
    $sql = "SELECT max(last) as last FROM $baza_danych.docnumber d
WHERE (d.name='$name' or d.name='$name-') "; //,dc_prac_id
    //echo "<br>" . $sql;
    $result = $db->mGetResult($sql);

    foreach ($result as $index => $aRow) {
        $numer = $aRow['last'];
        //$dc_prac_id = $aRow['dc_prac_id'];
        $sql = "update $baza_danych.docnumber d set  last=$numer+1 WHERE (d.name='$name' or d.name='$name-')";
        //echo "<br>" . $sql;
        $result1 = $db->mGetResultAsXML($sql);
    }
    return $numer;
}

// status_id2 jako docin_źródłowy


function przepakowanie_etykiety_step1_oryginalna($baza_danych, $etykieta_id, $docout_id, $db)
{
    //robić kopię etykiety ale docout_id ma być PP-, active=0 i ona będzie stanowiła archiwum dla PZ
    $sql = "insert into $baza_danych.etykiety( system_id, etykieta_klient, magazyn, active, miejscep, kod_id, status_id_old, status_id, stat, paleta_id, kartony, dataprod, "
        . "data_waznosci, ilosc, ts, status, blloc, akcja_id, status_prism, lot, sscc, gtin, przeznaczenie_id, nretykiety, docin_id, docout_id, delivery_id, "
        . "listcontrol_id, status_id2, edycja_et) "
        . "SELECT  e.system_id, e.etykieta_klient, e.magazyn, '0', e.miejscep, e.kod_id, e.status_id_old, e.status_id, e.stat, e.paleta_id, e.kartony, 
                e.dataprod, e.data_waznosci, e.ilosc, e.ts, e.status, e.blloc, e.akcja_id, e.status_prism, e.lot, e.sscc, e.gtin, e.przeznaczenie_id, e.nretykiety, 
                e.docin_id, $docout_id, e.delivery_id, e.listcontrol_id, e.status_id2, e.edycja_et FROM $baza_danych.etykiety e
where id=" . $etykieta_id;
    //echo "<br>" . $sql;
    $result = $db->mGetResultAsXML($sql);
    return $result;
}

function przepakowanie_etykiety_step2_ilosc_pobierana($baza_danych, $etykieta_id, $docin_id, $ilosc_pobierana, $db)
{
    //zrobić kopię aktywną z etykiety z ilością pobieraną i docin_id ma mieć PP, 
    $sql = "insert into $baza_danych.etykiety( system_id, etykieta_klient, magazyn, active, miejscep, kod_id, status_id_old, status_id, stat, paleta_id, kartony, dataprod, "
        . "data_waznosci, ilosc, ts, status, blloc, akcja_id, status_prism, lot, sscc, gtin, przeznaczenie_id, nretykiety, docin_id, docout_id, delivery_id, "
        . "listcontrol_id, status_id2, edycja_et) "
        . "SELECT  e.system_id, e.etykieta_klient, e.magazyn, '1', e.miejscep, e.kod_id, e.status_id_old, e.status_id, e.stat, e.paleta_id, e.kartony, 
                e.dataprod, e.data_waznosci, $ilosc_pobierana , e.ts, e.status, e.blloc, e.akcja_id, e.status_prism, e.lot, e.sscc, e.gtin, e.przeznaczenie_id, e.nretykiety, 
                $docin_id, e.docout_id, e.delivery_id, e.listcontrol_id, e.status_id2, e.edycja_et FROM $baza_danych.etykiety e
where id=" . $etykieta_id;
    //echo "<br>" . $sql;
    $result = $db->mGetResultAsXML($sql);
    return $result;
}

function przepakowanie_etykiety_step3_ilosc_zostawiana($baza_danych, $etykieta_id, $docin_id, $ilosc_zostawiana, $db)
{
    //zaktualizować ilość na etykiecie zostającej, oraz docin_id =PP
    $sql = "update $baza_danych.etykiety e set e.docin_id=$docin_id,ilosc=$ilosc_zostawiana where e.id=$etykieta_id limit 1";
    //echo "<br>" . $sql;
    $result = $db->mGetResultAsXML($sql);
    return $result;
}

function przepakowanie_etykiety_dezaktywowana($baza_danych, $etykieta_id, $docout_id, $db)
{
    //zaktualizować ilość na etykiecie zostającej, oraz docin_id =PP
    $sql = "update $baza_danych.etykiety e set e.docout_id=$docout_id,active=0 where e.id=$etykieta_id limit 1";
    //echo "<br>" . $sql;
    $result = $db->mGetResultAsXML($sql);
}

function tworz_dokument_docout($baza_danych, $docout_type, $docout_nr, $pracownik_id, $kontrah_wew_id, $db)
{
    $sql = "insert into " . $baza_danych . ".docout(docout_internal,docout_type,docout_nr,docout_date,docout_ts,pracownik_id,kontrah_id,docout_ref,docout_uwagi)" .
        " values(1,'" . $docout_type . "'," . $docout_nr . ",date(now()),sysdate(),'" . $pracownik_id . "','$kontrah_wew_id','','')";
    //echo "<br>" . $sql;
    $docout_id = $db->mGetResultAsXML($sql);

    return $docout_id;
}

function tworz_dokument_docin($baza_danych, $doc_type, $doc_nr, $pracownik_id, $kontrah_wew_id, $db)
{
    $sql = "insert into " . $baza_danych . ".docin(doc_internal,doc_type,doc_nr,doc_date,doc_ts,pracownik_id,kontrah_id,doc_ref,doc_uwagi)" .
        " values(1,'" . $doc_type . "'," . $doc_nr . ",date(now()),sysdate(),'" . $pracownik_id . "','$kontrah_wew_id','','')";
    $docin_id = $db->mGetResultAsXML($sql);
    //echo "<br>" . $sql;

    return $docin_id;
}

function plznaki($zmienna)
{
    $zamienniki = array("&#261;", "&#263;", "&#281;", "&#322;", "&#324;", "&#347;", "&#378;", "&#380;", "&#260;", "&#262;", "&#280;", "&#321;", "&#323;", "&#346;", "&#377;", "&#379;");
    $polskie = array("ą", "ć", "ę", "ł", "ń", "ś", "ź", "ż", "Ą", "Ć", "Ę", "Ł", "Ń", "Ś", "Ź", "Ż");

    $zmienna = str_replace($zamienniki, $polskie, $zmienna);
    return $zmienna;
}

function pobierz_zadanie_global_old($zadania_dane_id_local, $baza_danych_local, $db)
{

    $status_local = "1";
    if (empty($baza_danych_local)) {
        $sql = 'SELECT  zh.baza_danych,zd.status FROM zadania_dane zd  LEFT JOIN zadania_head AS zh ON zh.id=zd.zadanie_head_id WHERE zd.id=' . $zadania_dane_id_local . ' LIMIT 1 ';
        //echo $sql;
        $result3 = $db->mGetResultAsXML($sql);
        $baza_danych_local = "";

        foreach ($result3 as $key => $value) {
            $baza_danych_local = $value["baza_danych"];
            //$status_local = $value["status"];
        }
    }





    $sql = 'SELECT zd.id AS id,
       
       TRIM(TRAILING "." FROM TRIM(TRAILING "0" FROM zd.ilosc)) AS ilosc,
       k.kod,               
       k.kod_nazwa,
       TRIM(LEADING "0" FROM k.ean) as ean ,
       TRIM(LEADING "0" FROM k.ean_jednostki) as ean_jednostki,
       k.ilosc_w_opakowaniu,
       k.ilosc_szt_w_zbiorczym,
       TRIM(LEADING "0" FROM k.ean_opakowanie_zbiorcze) as ean_opakowanie_zbiorcze,
       (TRIM(TRAILING "." FROM TRIM(TRAILING "0" FROM zd.ilosc/k.ilosc_w_opakowaniu))) AS ilosc_opakowan,
       zd.zadanie_head_id AS zadanie_head_id,
       zd.status AS status,
       zd.stare_m AS stare_m,
       zd.nowe_m AS nowe_m,
       zd.nowe_m_realizowane AS nowe_m_realizowane,
       zd.paleta_id AS paleta_id,
       zd.etykieta_id AS etykieta_id,
       zd.kod_id AS kod_id,
       zd.lot AS lot,
       zh.baza_danych AS baza_danych,
       zh.system_id AS system_id,
       zh.typ AS typ,
       zh.doc_id AS doc_id,
       zh.doc_type AS doc_type,
       zdt.nazwa AS doc_type_nazwa,
       zh.zgodnosc_miejsca AS zgodnosc_miejsca,
       zh.zgodnosc_towaru AS zgodnosc_towaru,
       zh.docin_id_wew,zh.docout_id_wew,
       zt.nazwa AS zadanie_typ_nazwa,
       if(e.miejscep=0 or miejscep is null, concat("H: ", m1.hala, " ", m1.regal, "-", m1.miejsce, "-", m1.poziom)
       ,concat("H: ", m3.hala, " ", m3.regal, "-", m3.miejsce, "-", m3.poziom))  AS stare_m_nazwa,
       concat("H: ", m2.hala, " ", m2.regal, "-", m2.miejsce, "-", m2.poziom) AS nowe_m_nazwa,
       m2.hala as nowe_hala,
m2.regal as nowe_regal,
m2.miejsce as nowe_miejsce,
m2.poziom as nowe_poziom,
       s.nazwa AS system_id_nazwa,
       e.status_id AS status_id,

       zd.czas_przydzielenia AS czas_przydzielenia,
       zd.przydzielenie_pracownik_id AS przydzielenie_pracownik_id,
       zd.start AS start,
       zd.stop AS stop,
       zd.realizacja_pracownik_id AS realizacja_pracownik_id,
       zd.kompletowana_paleta_id,
       p2.imie_nazwisko AS realizacja_imie_nazwisko,
       zd.stanowisko_id AS stanowisko_id,
       zd.kierunek AS kierunek,
       zd.kompletacja,
       zd.wysokie,
       zh.priorytet AS priorytet,
       p.imie_nazwisko AS przydzielenie_imie_nazwisko,
       
       

(SELECT count(1)  FROM zadania_dane z  WHERE z.zadanie_head_id=zh.id AND z.status!=9) AS ile_wszystkich,
(SELECT count(1)  FROM zadania_dane z  WHERE z.zadanie_head_id=zh.id AND z.status!=9 AND z.status!=' . $status_local . ') AS ile_gotowe,
(SELECT count(1)  FROM zadania_dane z  WHERE z.zadanie_head_id=zh.id AND z.status=' . $status_local . ') AS ile_zostalo_zadan,
(SELECT count(1)  FROM zadania_dane z  WHERE z.zadanie_head_id=zh.id AND z.paleta_id=zd.paleta_id ) AS ile_wszystkich_paleta,
(SELECT count(1)  FROM zadania_dane z  WHERE z.zadanie_head_id=zh.id AND z.paleta_id=zd.paleta_id AND z.status!=' . $status_local . ') AS ile_gotowe_paleta

FROM zadania_dane zd
LEFT JOIN zadania_head AS zh ON zh.id=zd.zadanie_head_id
LEFT JOIN zadania_doc_type AS zdt ON zh.doc_type=zdt.id
LEFT JOIN zadania_typ AS zt ON zh.typ=zt.id
LEFT JOIN ' . $baza_danych_local . '.miejsca AS m1 ON m1.id=zd.stare_m
LEFT JOIN ' . $baza_danych_local . '.miejsca AS m2 ON m2.id=zd.nowe_m
LEFT JOIN ' . $baza_danych_local . '.pracownicy AS p ON zd.przydzielenie_pracownik_id=p.id
LEFT JOIN ' . $baza_danych_local . '.pracownicy AS p2 ON zd.realizacja_pracownik_id=p2.id
LEFT JOIN ' . $baza_danych_local . '.systemy AS s ON s.wartosc=zh.system_id
LEFT JOIN ' . $baza_danych_local . '.kody k ON zd.kod_id=k.id
LEFT JOIN ' . $baza_danych_local . '.etykiety e ON zd.etykieta_id=e.id and e.system_id=zh.system_id
LEFT JOIN ' . $baza_danych_local . '.miejsca AS m3 ON m3.id=e.miejscep
    
WHERE zd.id=' . $zadania_dane_id_local . '
GROUP BY zd.id
LIMIT 1  ';



    //concat(if(zh.baza_danych="rbrama",
    //                   (SELECT w.numer_karty
    //                    FROM rbrama.wjazdy w
    //                    WHERE w.id=zh.doc_id
    //                    LIMIT 1),"")) AS numer_karty,
    //       if(zh.baza_danych="rbrama",
    //            (SELECT w.ilosc_palet
    //             FROM rbrama.wjazdy w
    //             WHERE w.id=zh.doc_id
    //             LIMIT 1),"") AS ilosc_jednostek ,
    //if(zh.baza_danych="rbrama",
    //            (SELECT if(w.rampa IS NULL, "", concat("Rampa ", w.rampa, "; ", w.nr_ciagnik, "/", w.nr_naczepa))
    //             FROM rbrama.wjazdy w
    //             WHERE w.id=zh.doc_id
    //             LIMIT 1),"") AS info,
    //echo $sql;
    $result = $db->mGetResultAsXML($sql);
    return $result;
}

function pobierz_zadanie_global($zadania_dane_id_local, $baza_danych_local, $db)
{

    $status_local = "1";
    if (empty($baza_danych_local)) {
        $sql = 'SELECT  zh.baza_danych,zd.status FROM zadania_dane zd  LEFT JOIN zadania_head AS zh ON zh.id=zd.zadanie_head_id WHERE zd.id=' . $zadania_dane_id_local . ' LIMIT 1 ';
        //echo $sql;
        $result3 = $db->mGetResultAsXML($sql);
        $baza_danych_local = "";

        foreach ($result3 as $key => $value) {
            $baza_danych_local = $value["baza_danych"];
            //$status_local = $value["status"];
        }
    }





    $sql = 'SELECT zd.id AS id,
       
       TRIM(TRAILING "." FROM TRIM(TRAILING "0" FROM zd.ilosc)) AS ilosc,
       k.kod,               
       k.kod_nazwa,
       TRIM(LEADING "0" FROM k.ean) as ean ,
       TRIM(LEADING "0" FROM k.ean_jednostki) as ean_jednostki,
       k.ilosc_w_opakowaniu,
       k.ilosc_szt_w_zbiorczym,
       TRIM(LEADING "0" FROM k.ean_opakowanie_zbiorcze) as ean_opakowanie_zbiorcze,
       (TRIM(TRAILING "." FROM TRIM(TRAILING "0" FROM zd.ilosc/k.ilosc_w_opakowaniu))) AS ilosc_opakowan,
       zd.zadanie_head_id AS zadanie_head_id,
       zd.status AS status,
       zd.stare_m AS stare_m,
       zd.nowe_m AS nowe_m,
       zd.nowe_m_realizowane AS nowe_m_realizowane,
       zd.paleta_id AS paleta_id,
       zd.etykieta_id AS etykieta_id,
       zd.kod_id AS kod_id,
       zd.lot AS lot,
       zh.baza_danych AS baza_danych,
       zh.system_id AS system_id,
       zh.typ AS typ,
       zh.doc_id AS doc_id,
       zh.doc_type AS doc_type,
       zdt.nazwa AS doc_type_nazwa,
       zh.zgodnosc_miejsca AS zgodnosc_miejsca,
       zh.zgodnosc_towaru AS zgodnosc_towaru,
       zh.docin_id_wew,zh.docout_id_wew,
       zt.nazwa AS zadanie_typ_nazwa,
       if(e.miejscep=0 or miejscep is null, concat("H: ", m1.hala, " ", m1.regal, "-", m1.miejsce, "-", m1.poziom)
       ,concat("H: ", m3.hala, " ", m3.regal, "-", m3.miejsce, "-", m3.poziom))  AS stare_m_nazwa,
       concat("H: ", m2.hala, " ", m2.regal, "-", m2.miejsce, "-", m2.poziom) AS nowe_m_nazwa,
       m2.hala as nowe_hala,
m2.regal as nowe_regal,
m2.miejsce as nowe_miejsce,
m2.poziom as nowe_poziom,
       s.nazwa AS system_id_nazwa,
       e.status_id AS status_id,

       zd.czas_przydzielenia AS czas_przydzielenia,
       zd.przydzielenie_pracownik_id AS przydzielenie_pracownik_id,
       zd.start AS start,
       zd.stop AS stop,
       zd.realizacja_pracownik_id AS realizacja_pracownik_id,
       zd.kompletowana_paleta_id,
       p2.imie_nazwisko AS realizacja_imie_nazwisko,
       zd.stanowisko_id AS stanowisko_id,
       zd.kierunek AS kierunek,
       zd.kompletacja,
       zd.wysokie,
       zh.priorytet AS priorytet,
       p.imie_nazwisko AS przydzielenie_imie_nazwisko,
       if(e.ilosc=zd.ilosc,"PEŁNA","KOMPLETOWANA") as przenaczenie_palety,
       
       

(SELECT count(1)  FROM zadania_dane z  WHERE z.zadanie_head_id=zh.id AND z.status!=9) AS ile_wszystkich,
(SELECT count(1)  FROM zadania_dane z  WHERE z.zadanie_head_id=zh.id AND z.status!=9 AND z.status!=' . $status_local . ') AS ile_gotowe,
(SELECT count(1)  FROM zadania_dane z  WHERE z.zadanie_head_id=zh.id AND z.status=' . $status_local . ') AS ile_zostalo_zadan,
(SELECT count(1)  FROM zadania_dane z  WHERE z.zadanie_head_id=zh.id AND z.paleta_id=zd.paleta_id ) AS ile_wszystkich_paleta,
(SELECT count(1)  FROM zadania_dane z  WHERE z.zadanie_head_id=zh.id AND z.paleta_id=zd.paleta_id AND z.status!=' . $status_local . ') AS ile_gotowe_paleta

FROM zadania_dane zd
LEFT JOIN zadania_head AS zh ON zh.id=zd.zadanie_head_id
LEFT JOIN zadania_doc_type AS zdt ON zh.doc_type=zdt.id
LEFT JOIN zadania_typ AS zt ON zh.typ=zt.id
LEFT JOIN ' . $baza_danych_local . '.miejsca AS m1 ON m1.id=zd.stare_m
LEFT JOIN ' . $baza_danych_local . '.miejsca AS m2 ON m2.id=zd.nowe_m
LEFT JOIN ' . $baza_danych_local . '.pracownicy AS p ON zd.przydzielenie_pracownik_id=p.id
LEFT JOIN ' . $baza_danych_local . '.pracownicy AS p2 ON zd.realizacja_pracownik_id=p2.id
LEFT JOIN ' . $baza_danych_local . '.systemy AS s ON s.wartosc=zh.system_id
LEFT JOIN ' . $baza_danych_local . '.kody k ON zd.kod_id=k.id
LEFT JOIN ' . $baza_danych_local . '.etykiety e ON zd.etykieta_id=e.id and e.system_id=zh.system_id
LEFT JOIN ' . $baza_danych_local . '.miejsca AS m3 ON m3.id=e.miejscep
    
WHERE zd.id=' . $zadania_dane_id_local . '
GROUP BY zd.id
LIMIT 1  ';


    //concat(if(zh.baza_danych="rbrama",
    //                   (SELECT w.numer_karty
    //                    FROM rbrama.wjazdy w
    //                    WHERE w.id=zh.doc_id
    //                    LIMIT 1),"")) AS numer_karty,
    //       if(zh.baza_danych="rbrama",
    //            (SELECT w.ilosc_palet
    //             FROM rbrama.wjazdy w
    //             WHERE w.id=zh.doc_id
    //             LIMIT 1),"") AS ilosc_jednostek ,
    //if(zh.baza_danych="rbrama",
    //            (SELECT if(w.rampa IS NULL, "", concat("Rampa ", w.rampa, "; ", w.nr_ciagnik, "/", w.nr_naczepa))
    //             FROM rbrama.wjazdy w
    //             WHERE w.id=zh.doc_id
    //             LIMIT 1),"") AS info,
    //echo $sql;
    $result = $db->mGetResultAsXML($sql);

    if (count($result) > 0) {
        if (!empty($result[0]['typ'])) {
            if ($result[0]['typ'] == "7") {
                $result[0]['baza_danych'] = "wmsgg";
                $sql = 'SELECT concat(doc_type," ",doc_nr) as dokument,doc_nr, doc_type as docin_type FROM ' . $result[0]['baza_danych'] . '.docin d
where d.id=' . $result[0]['doc_id'] . ' limit 1;';
                //echo $sql;
                //            $tmpp = array('komunikat' => $sql);
                //        return xml_from_indexed_array($tmpp);

                $result2 = $db->mGetResultAsXML($sql);

                if (count($result2) > 0) {
                    $result[0] = array_merge($result[0], $result2['0']);
                }
            }
        }
        if ($result[0]['typ'] == "6") {
            $result[0] = array_merge($result[0], array('dokument' => $result[0]['doc_type_nazwa'] . " " . $result[0]['doc_id'], 'doc_nr' => $result[0]['doc_id'], 'docin_type' => $result[0]['doc_type_nazwa']));
        }
    }



    return $result;
}


function sprawdzaj_przydzielaj_zadanie_new($imie_nazwisko, $wysokie, $kompletacja, $stanowisko_id, $db)
{




    //$imie_nazwisko = "Badowski Marcinnn";

    $sql = 'SELECT z.id,
             "tak" AS przydzial,
             kompletacja,
             zadanie_head_id,
             wysokie,
             z.przydzielenie_pracownik_id as pracownik_id
      FROM zadania_dane z
      left join zadania_head zh on z.zadanie_head_id=zh.id
      left join pracownicy p on p.id=z.przydzielenie_pracownik_id
      
      WHERE (((z.status=1 or z.status=5 or z.status=8 ) and z.wysokie=' . $wysokie . ' and z.stanowisko_id=' . $stanowisko_id . ' ) or z.status=2 or z.status=21 ) 
      and (p.imie_nazwisko="' . $imie_nazwisko . '" )
        
         and zh.status_dokumentu>0 
        order by IF(z.status=2 or z.status=21,1,0) desc
      LIMIT 1
';
    //echo "<br><br>" . $sql;

    $result = $db->mGetResultAsXML($sql);
    //print_r($result);

    if (count($result) == 0) {


        $sql = "SELECT z.id,
             'nie' AS przydzial,
             z.status,zh.typ,
             kompletacja,
             zadanie_head_id,
             wysokie,
             stanowisko_id,
             (select pp.id as pracownik_id from pracownicy pp where pp.imie_nazwisko='" . $imie_nazwisko . "' limit 1) as pracownik_id
      FROM zadania_dane z
left join zadania_head zh on z.zadanie_head_id=zh.id and zh.id is not null
left join pracownicy p on p.id=z.przydzielenie_pracownik_id
      WHERE (z.status=1 or z.status=5 or z.status=8 )
        AND (przydzielenie_pracownik_id=0 or przydzielenie_pracownik_id is null)
        AND z.wysokie=$wysokie AND z.stanowisko_id=$stanowisko_id and zh.status_dokumentu>0
        ORDER BY if(z.status=5,1,0) desc,  zh.priorytet DESC,z.priorytet_zadania DESC, z.status desc,
        

        if(zh.planowany_czas_realizacji is null,DATE_ADD(zh.ts, INTERVAL +24 HOUR),
        zh.planowany_czas_realizacji) ASC,
        zh.zadania_head_rodzic_id ASC, 
        z.id ASC   limit 100     
      ";

        if ($stanowisko_id == "7" && $wysokie == "1") {

            $sql = "SELECT z.id,
             'nie' AS przydzial,
             z.status,zh.typ,
             kompletacja,
             zadanie_head_id,
             wysokie,
             stanowisko_id,
             (select pp.id as pracownik_id from pracownicy pp where pp.imie_nazwisko='" . $imie_nazwisko . "' limit 1) as pracownik_id
      FROM zadania_dane z
left join zadania_head zh on z.zadanie_head_id=zh.id and zh.id is not null
left join pracownicy p on p.id=z.przydzielenie_pracownik_id
left join miejsca m on z.stare_m=m.id

      WHERE (z.status=5 )
        AND (przydzielenie_pracownik_id=0 or przydzielenie_pracownik_id is null)
        AND z.wysokie=$wysokie  and zh.status_dokumentu>0
        ORDER BY if(z.status=5,1,0) desc,  m.regal ASC, m.miejsce ASC, m.poziom ASC limit 100
      ";
        }
        echo "<br><br>" . $sql;
        $result2 = $db->mGetResultAsXML($sql);
        echo "<pre>";
        print_r($result2);
        echo "</pre>";
        exit();

        if (count($result2) > 0) {

            foreach ($result2 as $key => $value) {
                //print_r($value);
                if ($value['typ'] == "4" && $value['status'] == "5") {
                    $sql = "UPDATE zadania_dane zd SET przydzielenie_pracownik_id=" . $value['pracownik_id'] . " where (przydzielenie_pracownik_id=0 or przydzielenie_pracownik_id is null)  and zd.id=" . $value['id'] . " ";

                    //echo "<br><br>" . $sql;

                    $result = $db->mGetResultAsXML($sql);
                    if ($db->count > 0) {
                        break;
                    }
                }
                if ($value['typ'] == "4" && $value['status'] != "5") {
                    $sql = "UPDATE zadania_dane zd SET przydzielenie_pracownik_id=" . $value['pracownik_id'] . " where (przydzielenie_pracownik_id=0 or przydzielenie_pracownik_id is null) and zd.status!=5 and zd.zadanie_head_id=" . $value['zadanie_head_id'] . " ";

                    //echo "<br><br>" . $sql;

                    $result = $db->mGetResultAsXML($sql);
                    if ($db->count > 0) {
                        break;
                    }
                }
                if ($value['typ'] == "6" || $value['typ'] == "7") {
                    $sql = "UPDATE zadania_dane zd SET przydzielenie_pracownik_id=" . $value['pracownik_id'] . " where (przydzielenie_pracownik_id=0 or przydzielenie_pracownik_id is null) and zd.id=" . $value['id'] . " ";

                    //echo "<br><br>" . $sql;

                    $result = $db->mGetResultAsXML($sql);
                    if ($db->count > 0) {
                        break;
                    }
                }
            }
        }
    }
}

function sprawdzaj_przydzielaj_zadanie($imie_nazwisko, $wysokie, $kompletacja, $stanowisko_id, $db)
{





    $sql = 'SELECT z.id,
             "tak" AS przydzial,
             kompletacja,
             zadanie_head_id,
             wysokie,
             z.przydzielenie_pracownik_id as pracownik_id
      FROM zadania_dane z
      left join zadania_head zh on z.zadanie_head_id=zh.id
      left join pracownicy p on p.id=z.przydzielenie_pracownik_id
      
      WHERE (((z.status=1 or z.status=5 or z.status=8 ) and z.wysokie=' . $wysokie . ' and z.stanowisko_id=' . $stanowisko_id . ' ) or z.status=2 or z.status=21 ) 
      and (p.imie_nazwisko="' . $imie_nazwisko . '" )
        
         and zh.status_dokumentu>0 
        order by IF(z.status=2 or z.status=21,1,0) desc
      LIMIT 1
';
    //echo "<br><br>" . $sql;

    $result = $db->mGetResultAsXML($sql);
    //print_r($result);

    if (count($result) == 0) {


        $sql = "SELECT z.id,
             'nie' AS przydzial,
             z.status,zh.typ,
             kompletacja,
             zadanie_head_id,
             wysokie,
             stanowisko_id,
             (select pp.id as pracownik_id from pracownicy pp where pp.imie_nazwisko='" . $imie_nazwisko . "' limit 1) as pracownik_id
      FROM zadania_dane z
left join zadania_head zh on z.zadanie_head_id=zh.id and zh.id is not null
left join pracownicy p on p.id=z.przydzielenie_pracownik_id
      WHERE (z.status=1 or z.status=5 or z.status=8 )
        AND (przydzielenie_pracownik_id=0 or przydzielenie_pracownik_id is null)
        AND z.wysokie=$wysokie AND z.stanowisko_id=$stanowisko_id and zh.status_dokumentu>0
        ORDER BY if(z.status=5,1,0) desc,  zh.priorytet DESC,z.priorytet_zadania DESC, z.status desc,
        

        if(zh.planowany_czas_realizacji is null,DATE_ADD(zh.ts, INTERVAL +24 HOUR),
        zh.planowany_czas_realizacji) ASC,
        zh.zadania_head_rodzic_id ASC, 
        z.id ASC   limit 100     
      ";

        if ($stanowisko_id == "7" && $wysokie == "1") {

            $sql = "SELECT z.id,
             'nie' AS przydzial,
             z.status,zh.typ,
             kompletacja,
             zadanie_head_id,
             wysokie,
             stanowisko_id,
             (select pp.id as pracownik_id from pracownicy pp where pp.imie_nazwisko='" . $imie_nazwisko . "' limit 1) as pracownik_id
      FROM zadania_dane z
left join zadania_head zh on z.zadanie_head_id=zh.id and zh.id is not null
left join pracownicy p on p.id=z.przydzielenie_pracownik_id
left join miejsca m on z.stare_m=m.id

      WHERE (z.status=5 )
        AND (przydzielenie_pracownik_id=0 or przydzielenie_pracownik_id is null)
        AND z.wysokie=$wysokie  and zh.status_dokumentu>0
        ORDER BY if(z.status=5,1,0) desc,z.pominiecie asc, zh.priorytet DESC,z.priorytet_zadania DESC,  cast(m.regal as UNSIGNED) ASC, m.miejsce ASC, m.poziom ASC limit 100
      ";
        }

        if ($stanowisko_id == "8" && $wysokie == "1") {

            $sql = "SELECT z.id,
             'nie' AS przydzial,
             z.status,zh.typ,
             kompletacja,
             zadanie_head_id,
             wysokie,
             stanowisko_id,
             (select pp.id as pracownik_id from pracownicy pp where pp.imie_nazwisko='" . $imie_nazwisko . "' limit 1) as pracownik_id
      FROM zadania_dane z
left join zadania_head zh on z.zadanie_head_id=zh.id and zh.id is not null
left join pracownicy p on p.id=z.przydzielenie_pracownik_id
left join miejsca m on z.stare_m=m.id

      WHERE (z.status=5 )
        AND (przydzielenie_pracownik_id=0 or przydzielenie_pracownik_id is null)
        AND z.wysokie=$wysokie  and zh.status_dokumentu>0
        ORDER BY if(z.status=5,1,0) desc,z.pominiecie asc,  cast(m.regal as UNSIGNED) DESC, m.miejsce ASC, m.poziom ASC limit 100
      ";
        }
        //echo "<br><br>".$sql;
        $result2 = $db->mGetResultAsXML($sql);
        //    echo "<pre>";
        //    print_r($result2);
        //    echo "</pre>";

        if (count($result2) > 0) {

            foreach ($result2 as $key => $value) {
                //print_r($value);
                if ($value['typ'] == "4" && $value['status'] == "5") {
                    $sql = "UPDATE zadania_dane zd SET przydzielenie_pracownik_id=" . $value['pracownik_id'] . " where (przydzielenie_pracownik_id=0 or przydzielenie_pracownik_id is null)  and zd.id=" . $value['id'] . " ";

                    //echo "<br><br>" . $sql;

                    $result = $db->mGetResultAsXML($sql);
                    if ($db->count > 0) {
                        break;
                    }
                }
                if ($value['typ'] == "4" && $value['status'] != "5") {
                    $sql = "UPDATE zadania_dane zd SET przydzielenie_pracownik_id=" . $value['pracownik_id'] . " where (przydzielenie_pracownik_id=0 or przydzielenie_pracownik_id is null) and zd.status!=5 and zd.zadanie_head_id=" . $value['zadanie_head_id'] . " ";

                    //echo "<br><br>" . $sql;

                    $result = $db->mGetResultAsXML($sql);
                    if ($db->count > 0) {
                        break;
                    }
                }
                if ($value['typ'] == "6" || $value['typ'] == "7") {
                    $sql = "UPDATE zadania_dane zd SET przydzielenie_pracownik_id=" . $value['pracownik_id'] . " where (przydzielenie_pracownik_id=0 or przydzielenie_pracownik_id is null) and zd.id=" . $value['id'] . " ";

                    //echo "<br><br>" . $sql;

                    $result = $db->mGetResultAsXML($sql);
                    if ($db->count > 0) {
                        break;
                    }
                }
            }
        }
    }
}

function sprawdzaj_przydzielaj_zadanie_old($imie_nazwisko, $wysokie, $kompletacja, $stanowisko_id, $db)
{

    $sql_ins_zad = "UPDATE zadania_dane zd
LEFT JOIN
  (SELECT a.*
   FROM
     (SELECT z.id,
             'tak' AS przydzial,
             kompletacja,
             zadanie_head_id,
             wysokie,
             stanowisko_id,
             z.przydzielenie_pracownik_id as pracownik_id
      FROM zadania_dane z
      left join zadania_head zh on z.zadanie_head_id=zh.id
      left join pracownicy p on p.id=z.przydzielenie_pracownik_id
      
      WHERE (((z.status=1 or z.status=5 or z.status=8 ) and z.wysokie=$wysokie and z.kompletacja=$kompletacja and z.stanowisko_id=$stanowisko_id) or z.status=2 or z.status=21 ) 
      and (p.imie_nazwisko='" . $imie_nazwisko . "' )
        
         and zh.status_dokumentu>0 
        order by IF(z.status=2 or z.status=21,1,0) desc
      LIMIT 1) AS a
   UNION SELECT b.*
   FROM
     (SELECT z.id,
             'nie' AS przydzial,
             kompletacja,
             zadanie_head_id,
             wysokie,
             stanowisko_id,
             (select pp.id as pracownik_id from pracownicy pp where pp.imie_nazwisko='" . $imie_nazwisko . "' limit 1) as pracownik_id
      FROM zadania_dane z
left join zadania_head zh on z.zadanie_head_id=zh.id and zh.id is not null
left join pracownicy p on p.id=z.przydzielenie_pracownik_id
      WHERE (z.status=1 or z.status=5 or z.status=8 )
        AND przydzielenie_pracownik_id=0
        AND z.wysokie=$wysokie AND z.kompletacja=$kompletacja and z.stanowisko_id=$stanowisko_id and zh.status_dokumentu>0
        ORDER BY if(z.status=5,1,0) desc,  zh.priorytet DESC,z.priorytet_zadania DESC, z.status desc,
        

        if(zh.planowany_czas_realizacji is null,DATE_ADD(zh.ts, INTERVAL +24 HOUR),
        zh.planowany_czas_realizacji) ASC,
        zh.zadania_head_rodzic_id ASC, 
        z.id ASC        
      LIMIT 1) AS b
   LIMIT 1) AS b ON (b.id=zd.id      OR zd.zadanie_head_id=b.zadanie_head_id                      AND b.kompletacja=1)
AND b.przydzial='nie'
AND b.wysokie=zd.wysokie
AND b.kompletacja=zd.kompletacja
AND b.stanowisko_id=zd.stanowisko_id
SET przydzielenie_pracownik_id=b.pracownik_id
WHERE (zd.status=1 or zd.status=5 or zd.status=8  )
  AND if(b.kompletacja=0,(zd.id=b.id),(zd.zadanie_head_id=b.zadanie_head_id
                                       AND b.kompletacja=1))
  AND przydzielenie_pracownik_id=0
  AND zd.wysokie=$wysokie   AND zd.kompletacja=$kompletacja and zd.stanowisko_id=$stanowisko_id ";
    //echo "<br>" . $sql_ins_zad;
    // co Autor miał na myśli: najpierw realizuje zadania, które są przydzielone do danej osoby,
    // następnie, jeśli nie ma, to szukaj według wskazanuch parametrów
    // wyszukuje jedno ale sprawdza i robi left join w zależności czy jedno czy wiele

    $result_zadanie4 = $db->mGetResultAsXML($sql_ins_zad);


    $sql_ins_zad = "update  zadania_dane z
left join  zadania_dane zz on zz.zadanie_head_id=z.zadanie_head_id
set z.przydzielenie_pracownik_id=zz.przydzielenie_pracownik_id,z.priorytet_zadania=15
where z.status=6 and zz.status=1 and z.przydzielenie_pracownik_id=0 and zz.przydzielenie_pracownik_id!=0;";
    $result_zadanie15 = $db->mGetResultAsXML($sql_ins_zad);
}

function sprawdz_czy_istnieje_miejsce($hala, $regal, $miejsce, $poziom, $baza_danych, $db)
{

    $sql_ins_zad = "SELECT m.id FROM $baza_danych.miejsca m
WHERE m.hala=$hala AND m.regal='$regal' and miejsce=$miejsce and poziom='" . $poziom . "';";
    //echo "<br>" . $sql_ins_zad;
    $result_zadanie4 = $db->mGetResultAsXML($sql_ins_zad);
    if (!empty($result_zadanie4)) {
        return $result_zadanie4[0]['id'];
    } else {
        return "";
    }
}

function zamianapolskich_zpl($tekst)
{
    $tabela = array(
        //WIN
        "\xb9" => "a", "\xa5" => "A", "\xe6" => "c", "\xc6" => "C",
        "\xea" => "e", "\xca" => "E", "\xb3" => "l", "\xa3" => "L",
        "\xf3" => "o", "\xd3" => "O", "\x9c" => "s", "\x8c" => "S",
        "\x9f" => "z", "\xaf" => "Z", "\xbf" => "z", "\xac" => "Z",
        "\xf1" => "n", "\xd1" => "N",
        //UTF
        "\xc4\x85" => "a", "\xc4\x84" => "A", "\xc4\x87" => "c", "\xc4\x86" => "C",
        "\xc4\x99" => "e", "\xc4\x98" => "E", "\xc5\x82" => "l", "\xc5\x81" => "L",
        "\xc3\xb3" => "o", "\xc3\x93" => "O", "\xc5\x9b" => "s", "\xc5\x9a" => "S",
        "\xc5\xbc" => "z", "\xc5\xbb" => "Z", "\xc5\xba" => "z", "\xc5\xb9" => "Z",
        "\xc5\x84" => "n", "\xc5\x83" => "N",
        //ISO
        "\xb1" => "a", "\xa1" => "A", "\xe6" => "c", "\xc6" => "C",
        "\xea" => "e", "\xca" => "E", "\xb3" => "l", "\xa3" => "L",
        "\xf3" => "o", "\xd3" => "O", "\xb6" => "s", "\xa6" => "S",
        "\xbc" => "z", "\xac" => "Z", "\xbf" => "z", "\xaf" => "Z",
        "\xf1" => "n", "\xd1" => "N"
    );

    return strtr($tekst, $tabela);
}

$naglowek_html = "<html lang='pl-PL'>
<head>
   <meta charset='UTF-8'> 
<style>
table {
  border-collapse: collapse;
  width: 80%;
}

th, td {
  text-align: left;
  padding: 3px;
}
th {
  background-color: lightgreen;
}

tr:nth-child(even) {background-color: #f2f2f2;}
</style>
</head>";

function wyslij_mail_func($adresaci, $temat, $content)
{
    $INCLUDE_DIR = "/var/www/lib/phpmailer/";
    require($INCLUDE_DIR . "PHPMailerAutoload.php");

    $mail = new PHPMailer();
    $mail->IsSMTP();

    $mail->Host       = '***********';
    $mail->Port       = 25;
    $timeout = 3; // sprawdza przez 3 sekundy czy host jes dostępny 
    if (!$sock = @fsockopen($mail->Host, $mail->Port, $errno, $errstr, $timeout)) {
        $mail->Host = "smtp.office365.com";  // specify main and backup server
        $mail->SMTPSecure = "tls";
        $mail->Port = 587;
    }
    $mail->SMTPAuth = true;     // turn on SMTP authentication
    //$mail->SMTPSecure = "tls";
    $mail->CharSet = 'UTF-8';
    $mail->Username = "<EMAIL>";  // SMTP username
    $mail->Password = "biuroP@rtners"; // SMTP password
    $mail->From = "<EMAIL>";
    $mail->FromName = "WMS RAWA";

    foreach ($adresaci as $key => $value) {
        $mail->AddAddress($value['adres']);
    }


    $mail->AddReplyTo("<EMAIL>", "Information");
    $mail->WordWrap = 50;                                 // set word wrap to 50 characters



    $mail->IsHTML(true);                                  // set email format to HTML
    //$mail->Subject = "Raport poprawnosci WMS z dnia " . $data . " godz " . date('H:i') . " ";
    $mail->Subject = $temat;
    $mail->Body = $content;
    $mail->AltBody = "Hello, date " . date('Y-m-d') . " This is automatically generated by the system_id. It may contain errors. If you find any errors, please contact us at the address: <EMAIL>, <EMAIL>";

    if (!$mail->Send()) {

        $komunikat = " Error 991. Zglos do kierownika:" + $content;
        return show_komunikat_xml($komunikat);
    } else {
    }
}

function validateDate($date, $format = 'Y-m-d')
{
    $d = DateTime::createFromFormat($format, $date);
    return $d && $d->format($format) == $date;
}

function show_komunikat_xml($komunikat)
{
    header('Content-type: text/xml');
    echo "<dane>";
    echo "<komunikat>", htmlentities($komunikat), "</komunikat>";
    echo "</dane>";
    exit();
}

function xml_from_indexed_array($array)
{
    header('Content-type: text/xml');
    echo "<dane>\n";
    foreach ($array as $child => $value) {
        if (is_array($value)) {
            foreach ($value as $child1 => $value1) {
                echo "<$child>\n";
                foreach ($value1 as $key2 => $value2) {
                    echo "    <$key2>" . htmlspecialchars($value2, ENT_XML1 | ENT_COMPAT, 'UTF-8') . "</$key2>\n";
                }
                echo "</$child>\n";
            }
        } else {
            echo "    <$child>" . htmlspecialchars($value, ENT_XML1 | ENT_COMPAT, 'UTF-8') . "</$child>\n";
        }
    }
    echo '</dane>';
    exit();
}

function xml_from_indexed_array_test($array)
{
    header('Content-type: text/xml');
    echo "<dane>\n";
    foreach ($array as $child => $value) {
        if (is_array($value)) {
            foreach ($value as $child1 => $value1) {
                echo "<$child>\n";
                foreach ($value1 as $key2 => $value2) {
                    //echo "    <$key2>".htmlspecialchars($value2, ENT_XML1 | ENT_COMPAT, 'UTF-8')."</$key2>\n";
                    echo "    <$key2>" . $value2 . "</$key2>\n";
                }
                echo "</$child>\n";
            }
        } else {
            //echo "    <$child>".htmlspecialchars($value, ENT_XML1 | ENT_COMPAT, 'UTF-8')."</$child>\n";
            echo "    <$child>" . $value . "</$child>\n";
        }
    }
    echo '</dane>';
    exit();
}

function get_params_to_array()
{
    $parametry = array();

    // sprawdzenie, czy dane zostały przesłane metodą GET
    if ($_SERVER['REQUEST_METHOD'] === 'GET') {
        foreach ($_GET as $klucz => $wartosc) {
            $parametry[$klucz] = $wartosc;
        }
    }

    // sprawdzenie, czy dane zostały przesłane metodą POST
    if ($_SERVER['REQUEST_METHOD'] === 'POST') {
        foreach ($_POST as $klucz => $wartosc) {
            $parametry[$klucz] = $wartosc;
        }
    }

    return $parametry;
}
