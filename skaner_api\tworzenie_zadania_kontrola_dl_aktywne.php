<?php

include_once 'Db.class.php';
include_once 'funkcje.inc';
//error_reporting(E_ALL);
//ini_set('display_errors', 1);

$db = new Db();

if (empty($argv)) {
    //$argv = array();
    $argv[1] = $_GET['db'];
    $argv[2] = $_GET['tb'];
    $argv[3] = $_GET['id'];
}

if ($argv[1] == '1') {
    $baza_danych = 'wmsgg';
}
if ($argv[1] == '2') {
    $baza_danych = 'wmsftl';
}
if ($argv[1] == '3') {
    $baza_danych = 'prod';
}
$baza_danych = $argv[1];

$tabela = $argv[2];
$id = $argv[3];


//if ($_GET['db'] == '1') {
//    $baza_danych = 'wmsgg';
//}
//
//$tabela = $_GET['tb'];
//$id = $_GET['id'];

echo "<br>aa,$baza_danych";

echo "<br>bb";
if ($tabela == "delivery") {
    echo "<br>cc";
    $typ = "8";
    $doc_type = "2";
    
    
    $sql = "SELECT dl_system_id from delivery de where id=$doc_id  limit 1";
    echo "<br>" . $sql;
    $result = $db->mGetResult($sql);
    $system_id_id = 
    

    $sql = "SELECT zh.id from wmsgg.zadania_head zh
left join wmsgg.zadania_dane z  on  z.zadanie_head_id=zh.id where zh.baza_danych='$baza_danych' and system_id=$system_id_id and zh.typ='$typ' and doc_id=$doc_id and z.id is not null limit 1";
    echo "<br>" . $sql;
    $result24 = $db->mGetResult($sql);



    $ile = count($result24);
    echo "$ile";
    if ($ile == "0") {
        echo "bbbbbbb";


        //echo "asdasd" . $aRow2;
        $sql_ins_zad = "insert into wmsgg.zadania_head(system_id,baza_danych,typ,doc_id,doc_type,ts,zgodnosc_towaru,realizuj_cale_zadanie,status_dokumentu) values"
                . " ('" . $system_id_id . "','$baza_danych','$typ','" . $id . "','$doc_type',NOW(),'4','1','1')";
        echo "<br>" . $sql_ins_zad;
        $zadanie_head_id = $db->mGetResult($sql_ins_zad);



        $query_zap1 = "UPDATE wmsgg.zadania_head z set zadania_head_rodzic_id=$zadanie_head_id where id='$zadanie_head_id' limit 1";
        echo "<br>" . $query_zap1;
        $result_zadanie2 = $db->mGetResult($query_zap1);


        /// dodawanie pozycji dl

//        $sql4 = "SELECT e.system_id,e.id,e.paleta_id,e.kod_id,ifnull(e.lot,'') as lot,de.ilosc_zamawiana,e.miejscep, k.ilosc_szt_palecie,e.ilosc,k.kod,
//if(de.ilosc_zamawiana=ilosc_szt_palecie and de.ilosc_zamawiana=e.ilosc,0,1) as kompletacja,
//m.zbiorka
//     FROM $baza_danych.delivery d
//left join $baza_danych.delivery_et de on de.delivery_id=d.id
//left join $baza_danych.etykiety e on de.etykieta_id=e.id
//left join $baza_danych.kody k on e.kod_id=k.id
//left join $baza_danych.miejsca m on e.miejscep=m.id
// where d.id=$id and e.id is not null
//order by kompletacja desc";
//        if ($system_id_id == "31") {
//            $sql4 .= " ,k.kod asc,m.hala asc,m.regal asc,m.miejsce asc,m.poziom asc";
//        } else {
//            //$sql4.=" , m.hala asc,m.regal asc,m.miejsce asc,m.poziom asc";
//            $sql4 .= " ,floor(m.regal/2),if(((floor(m.regal/2)%2)='1'),(300-m.miejsce),m.miejsce),m.poziom";
//        }
//
//        echo "<br>" . $sql;
//        $result4 = $db->mGetResult($sql4); //mysql_query($sql4, $conn);
//        $czy_dokument_zawiera_kompletacje = "NIE";
//
//
//
//
//        foreach ($result4 as $index => $aRow4) {
//            
//
//            $wysokie = 0;
//            if ($aRow4['zbiorka'] != "1") {
//                $wysokie = 1;
//            }
//            if ($aRow4['kompletacja'] == "1") {
//                $czy_dokument_zawiera_kompletacje = "TAK";
//            }
            $status = 25;

            $sql_ins_zad = "insert into wmsgg.zadania_dane(zadanie_head_id,status, stare_m, nowe_m, paleta_id, etykieta_id, kod_id, lot, ilosc,kompletacja,wysokie,stanowisko_id) values "
                    . "('" . $zadanie_head_id . "','" . $status . "','" . $aRow4['miejscep'] . "','" . $miejsce_kompletacji . "','" . $aRow4['paleta_id'] . "','" . $aRow4['id'] . "','" . $aRow4['kod_id'] . "','" . $aRow4['lot'] . "','" . $aRow4['ilosc_zamawiana'] . "','" . $aRow4['kompletacja'] . "','" . $wysokie . "','3')";
            echo "<br>" . $sql_ins_zad;
            $zadania_dane_insert_id = $db->mGetResult($sql_ins_zad);
            $sql = "update zadania_dane z set z.zadanie_dane_rodzic_id=$zadania_dane_insert_id WHERE id=" . $zadania_dane_insert_id . " limit 1";
            //echo "<br>" . $sql_ins_zad;
            $result_zadanie4 = $db->mGetResult($sql);

        //}
    }
}

function sugerowane_miejsce($kod, $system_id, $db) {
    $sugerowane_miejsce_id = 0;

    return $sugerowane_miejsce_id;
}

function status_aktywne_dla_pozostalych($zadanie_head_id, $db) {
    $sql_ins_zad = "update zadania_dane z set z.status=1 WHERE status=0 and zadanie_head_id=" . $zadanie_head_id;
    echo "<br>" . $sql_ins_zad;
    $result_zadanie4 = $db->mGetResult($sql_ins_zad);
}

?>