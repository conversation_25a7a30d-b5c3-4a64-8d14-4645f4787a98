<?php

include_once '../Db.class.php';
include_once '../funkcje.inc';

// show errors  
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

$db = new Db();

$baza_danych = "wmsgg";
$komunikat = "OK";

$response = array();

// Pobierz dane z $_GET
$scan = $_GET['scan']; // wymagany
$system_id = $_GET['system_id']; 

if (empty($scan)) {
    $komunikat = "Brak etykiety";
    return show_komunikat_xml($komunikat);
}

$tmp_arr1 = sprawdz_szukana_etykiete_WMS($scan, $system_id, $komunikat, $baza_danych, $db);


if (empty($tmp_arr1['aRowEtWms'])) {
    $komunikat = "Nie znaleziono aktywnej etykietyty $scan !!!! ";
    return show_komunikat_xml($komunikat);
}
$aRowEtWms = $tmp_arr1['aRowEtWms'];

$response['komunikat'] = "OK";
$response['ilosc_opak']=0;

$response['ilosc'] = $aRowEtWms['ilosc'];
if($aRowEtWms['ilosc_w_opakowaniu'] == 0){
    $aRowEtWms['ilosc_w_opakowaniu'] = 1;
    //$response['ilosc_opak'] = 1;
}
$response['ilosc_opak'] = ($aRowEtWms['ilosc'] / $aRowEtWms['ilosc_w_opakowaniu']); //* $aRowEtWms['ilosc_w_opakowaniu'];

//print_r($aRowEtWms);

return xml_from_indexed_array($response);



// echo "<pre>";
// print_r($tmp_arr1);
// echo "</pre>";

// $delivery_nr_wiodace = $aRowEtWms1['delivery_id'];
// $ilosc_etykieta = $aRowEtWms1['ilosc'];
//return show_komunikat_xml(array('komunikat' => $komunikat, 'aRowEtWms' => $aRowEtWms));

//return show_komunikat_xml(array('komunikat' => $komunikat, 'aRowEtWms' => $aRowEtWms));

return xml_from_indexed_array(array('komunikat' => $komunikat, 'aRowEtWms' => $aRowEtWms));


function sprawdz_szukana_etykiete_WMS($etykieta_scan, $system_id, $komunikat, $baza_danych, $db)
{

        if (substr($etykieta_scan, 0, 2) == "DS") {
                $sql = 'select e.id,e.etykieta_klient,k.kod,k.ilosc_w_opakowaniu,k.kod_nazwa,e.lot,e.active,TRIM(TRAILING "." FROM TRIM(TRAILING "0" from e.ilosc)) as ilosc,de.delivery_id,e.paleta_id,ss.nazwa as status_system_nazwa, ss.funkcja_stat , TRIM(TRAILING "." FROM TRIM(TRAILING "0" from de.ilosc_zamawiana))  as ilosc_zamawiana, d.dl_docin_id_wew, d.dl_docout_id_wew, d.miejsce_kompletacji,e.miejscep from ' . $baza_danych . '.etykiety e left join ' . $baza_danych . '.kody k on e.kod_id=k.id left join ' . $baza_danych . '.dlcollect dl ' . ' on dl.nr_et=e.id left join ' . $baza_danych . '.status_system ss  on e.status_id=ss.id left join ' . $baza_danych . '.delivery_et de ' . ' on de.etykieta_id=e.id left join ' . $baza_danych . '.delivery d ' . ' on de.delivery_id=d.id  where e.active=1  and e.paleta_id=' . str_replace("DS", "", $etykieta_scan) . ' and e.system_id=' . $system_id . '  order by nr_dl asc,e.ilosc asc limit 1'; //having nr_dl is null
        } else {
                $sql = 'select e.id,e.etykieta_klient,k.kod,k.ilosc_w_opakowaniu,k.kod_nazwa,e.lot,e.active,TRIM(TRAILING "." FROM TRIM(TRAILING "0" from e.ilosc)) as ilosc,de.delivery_id,e.paleta_id,ss.nazwa as status_system_nazwa, ss.funkcja_stat , TRIM(TRAILING "." FROM TRIM(TRAILING "0" from de.ilosc_zamawiana))  as ilosc_zamawiana , d.dl_docin_id_wew, d.dl_docout_id_wew,d.miejsce_kompletacji,e.miejscep from ' . $baza_danych . '.etykiety e left join ' . $baza_danych . '.kody k on e.kod_id=k.id left join ' . $baza_danych . '.dlcollect dl ' . ' on dl.nr_et=e.id left join ' . $baza_danych . '.status_system ss  on e.status_id=ss.id left join ' . $baza_danych . '.delivery_et de ' . ' on de.etykieta_id=e.id left join ' . $baza_danych . '.delivery d ' . ' on de.delivery_id=d.id  where e.active=1  and  (e.id="' . $etykieta_scan . '" or e.etykieta_klient="' . $etykieta_scan . '")  and e.system_id=' . $system_id . '  order by nr_dl asc,e.ilosc asc limit 1'; //having nr_dl is null
        }
        //echo $sql;
        $result2 = $db->mGetResultAsXML($sql);
        $aRowEtWms = array();

        //    if (count($result2) > 1) {
        //        return array('komunikat' => $komunikat, 'aRowEtWms' => $result2, 'ilosc_pozycji_etykiety' => count($result2));
        //    }
        //echo "<br>" . $sql;
        foreach ($result2 as $index => $aRowEtWms) {
                if ($aRowEtWms['active'] != "1") {
                        $komunikat = "Etykieta jest nieaktywna. Przerywam operacje";
                        //echo "<br>" . $komunikat;
                        return show_komunikat_xml($komunikat);
                }

                if (!empty($aRowEtWms['funkcja_stat'])) {
                        $komunikat = "Etykieta jest w statusie: " . $aRowEtWms['status_system_nazwa'] . ". Przerywam operacje";
                        //echo "<br>" . $komunikat;
                        return show_komunikat_xml($komunikat);
                }

                // jeśli ilosc_zamawiana jest rózna od ilosc
                if ($aRowEtWms['ilosc_zamawiana'] != $aRowEtWms['ilosc']) {
                        $komunikat = "Etykieta zamawiana " . $aRowEtWms['ilosc_zamawiana'] . " jest różna od ilości etykiety: " . $aRowEtWms['ilosc'] . ". Przerywam operacje";
                        //echo "<br>" . $komunikat;
                        return show_komunikat_xml($komunikat);
                }
        }
        return array('komunikat' => $komunikat, 'aRowEtWms' => $aRowEtWms);
}