﻿using System;
using System.Linq;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using MySql.Data.MySqlClient;
using System.Threading;
using System.Text.RegularExpressions;
using System.Xml;

namespace Tarczyn__Magazyn
{ 
    public partial class Wolne_Poziomy : Form
    {

        //public MySqlConnection conn_FERRERO = null;

        List<string> _poziom = new List<string>();
        //string poziom = "";

        private Thread Skanowanie;

        int ilosc_poziomow = 0;

        public string poziom = "";

        public string hala = "";
        public string regal = "";
        public string miejsce = "";
        ActionMenu myParent = null;
        string operac_id_global = "";


        public Wolne_Poziomy(ActionMenu MyParent)
        {
            //this.aa = objekt;
            InitializeComponent();
            this.myParent = MyParent;
            Wlasciwosci.CurrentOperacja = "22";


            this.ZacznijSkanowanie();
        }


        /*
        private void Wybierz_Poziom_Powrot(object sender, EventArgs e)
        {
            if (_poziom[listBox1.SelectedIndex] != "")
            {
                poziom = _poziom[listBox1.SelectedIndex];
                aa.ustaw_poziom(poziom);
                this.Hide();
                aa.Show();
            }
        }
        */


        private void ZacznijSkanowanie()
        {
            StringBuilder login = new StringBuilder();
            Skaner.UstawTryb_String(login);
            Skaner.Zacznij_Skanowanie();
            this.Skanowanie = new Thread(() => this.CheckString(login));
            this.Skanowanie.IsBackground = true;
            this.Skanowanie.Start();
        }



        private void CheckString(StringBuilder cc)
        {
            EventHandler method = null; //to jest delegat

            while (true)
            {
                if (cc.ToString() != "")
                {
                    if (method == null)
                    {
                        method = delegate
                        {
                            this.dodawanie(cc.ToString());
                        };
                    }
                    this.Invoke(method);
                    return;
                }
                Thread.Sleep(200);
                
            }
        }

        private void dodawanie(string ops)
        {
            //Skanowanie.Abort();
            //Skaner.Przewij_Skanowanie();

            //bool jestLiczba = Regex.IsMatch(ops, @"\d");

            if (ops.Substring(0, 2) == "MP")
            {
                //BazaDanychExternal.DokonajUpdate("insert into operacje(etykieta_id, doc_type, doc_nr, imie_nazwisko, typ_operacji,system_id,wozek,operac_id,ilosc) values('0','','0','" + Wlasciwosci.imie_nazwisko + "','WOLN_POZ','0', '" + Wlasciwosci.wozek + "','" + operac_id_global + "','1');");

                

                string zapytanie = "";
                //zapytanie = "SELECT poziom,count(distinct paleta_id) as ile,regal,miejsce FROM etykiety e left join miejsca m on e.miejscep=m.id where regal='" + regal  + "' and miejsce='" + miejsce + "' and active=1 group by poziom union SELECT poziom,count(distinct paleta_id) as ile,regal,miejsce FROM etykiety e left join miejsca m on e.miejscep=m.id where regal='" + regal + "' and miejsce='" + miejsce + "' and active=1 group by poziom desc";
                //zapytanie = "SELECT poziom, sum(if(active is null or active=1,1,0)) as ile, regal,miejsce FROM miejsca m left join etykiety e on e.miejscep=m.id where regal='" + regal + "' and miejsce='" + miejsce + "' and active=1 group by poziom desc";
                //zapytanie = "SELECT poziom,  regal,miejsce, ifnull((select sum(1) from etykiety e where m.id=e.miejscep and (active=1 or active is null)),0) as ile FROM miejsca m where regal='" + regal + "' and miejsce='" + miejsce + "' and widoczne=1 group by poziom ASC";

                XmlDocument doc1_etykieta = WebService.Pobierz_XmlDocument("wolne_poziomy.php?akcja=szukaj&db=wmsgg&scan=" + ops + "&operac_id=" + operac_id_global + "&imie_nazwisko=" + Uri.EscapeUriString(Wlasciwosci.imie_nazwisko));
                XmlNode node_etykieta = WebService.Pobierz_XmlNode(doc1_etykieta, "dane");

                


                if (node_etykieta["komunikat"].InnerText != "OK")
                {
                    MessageBox.Show(node_etykieta["komunikat"].InnerText);
                }
                else
                {
                    operac_id_global = node_etykieta["operac_id"].InnerText;

                    XmlNodeList xmlnode2 = WebService.Pobierz_XmlNodeList(doc1_etykieta, "wynik");
                    DataTable tabela = Konwersja_XmlNodeList_DataTable(xmlnode2);
                    if (tabela.Rows.Count < 1)
                    {
                        //return 0;
                    }
                    else
                    {
                        ilosc_poziomow = tabela.Rows.Count;// Convert.ToInt32(words[3].ToString());

                        label8.Visible = false;
                        label7.Visible = false;
                        label6.Visible = false;
                        label5.Visible = false;
                        label4.Visible = false;
                        label3.Visible = false;
                        label2.Visible = false;
                        label1.Visible = false;

                        for (byte x = 1; x <= ilosc_poziomow; x++)
                        {
                            if (x == 1)
                            {
                                label1.Visible = true;
                                if (tabela.Rows[x - 1]["ile"].ToString() != "0")
                                {
                                    label1.Text = tabela.Rows[x - 1]["poziom"].ToString() + " - " + tabela.Rows[x - 1]["ile"].ToString();
                                }
                                else
                                {
                                    label1.Text = tabela.Rows[x - 1]["poziom"].ToString() + " - wolne ";
                                    label1.Font = new System.Drawing.Font("Tahoma", 18F, System.Drawing.FontStyle.Bold);
                                }
                            }

                            if (x == 2)
                            {
                                label2.Visible = true;
                                if (tabela.Rows[x - 1]["ile"].ToString() != "0")
                                {
                                    label2.Text = tabela.Rows[x - 1]["poziom"].ToString() + " - " + tabela.Rows[x - 1]["ile"].ToString();
                                }
                                else
                                {
                                    label2.Text = tabela.Rows[x - 1]["poziom"].ToString() + " - wolne ";
                                    label2.Font = new System.Drawing.Font("Tahoma", 18F, System.Drawing.FontStyle.Bold);
                                }
                            }
                            if (x == 3)
                            {
                                label3.Visible = true;
                                if (tabela.Rows[x - 1]["ile"].ToString() != "0")
                                {
                                    label3.Text = tabela.Rows[x - 1]["poziom"].ToString() + " - " + tabela.Rows[x - 1]["ile"].ToString();
                                }
                                else
                                {
                                    label3.Text = tabela.Rows[x - 1]["poziom"].ToString() + " - wolne ";
                                    label3.Font = new System.Drawing.Font("Tahoma", 18F, System.Drawing.FontStyle.Bold);
                                }
                            }
                            if (x == 4)
                            {
                                label4.Visible = true;
                                if (tabela.Rows[x - 1]["ile"].ToString() != "0")
                                {
                                    label4.Text = tabela.Rows[x - 1]["poziom"].ToString() + " - " + tabela.Rows[x - 1]["ile"].ToString();
                                }
                                else
                                {
                                    label4.Text = tabela.Rows[x - 1]["poziom"].ToString() + " - wolne ";
                                    label4.Font = new System.Drawing.Font("Tahoma", 18F, System.Drawing.FontStyle.Bold);
                                }
                            }
                            if (x == 5)
                            {
                                label5.Visible = true;
                                if (tabela.Rows[x - 1]["ile"].ToString() != "0")
                                {
                                    label5.Text = tabela.Rows[x - 1]["poziom"].ToString() + " - " + tabela.Rows[x - 1]["ile"].ToString();
                                }
                                else
                                {
                                    label5.Text = tabela.Rows[x - 1]["poziom"].ToString() + " - wolne ";
                                    label5.Font = new System.Drawing.Font("Tahoma", 18F, System.Drawing.FontStyle.Bold);
                                }
                            }
                            if (x == 6)
                            {
                                label6.Visible = true;
                                if (tabela.Rows[x - 1]["ile"].ToString() != "0")
                                {
                                    label6.Text = tabela.Rows[x - 1]["poziom"].ToString() + " - " + tabela.Rows[x - 1]["ile"].ToString();
                                }
                                else
                                {
                                    label6.Text = tabela.Rows[x - 1]["poziom"].ToString() + " - wolne ";
                                    label6.Font = new System.Drawing.Font("Tahoma", 18F, System.Drawing.FontStyle.Bold);
                                }
                            }
                            if (x == 7)
                            {
                                label7.Visible = true;
                                if (tabela.Rows[x - 1]["ile"].ToString() != "0")
                                {
                                    label7.Text = tabela.Rows[x - 1]["poziom"].ToString() + " - " + tabela.Rows[x - 1]["ile"].ToString();
                                }
                                else
                                {
                                    label7.Text = tabela.Rows[x - 1]["poziom"].ToString() + " - wolne ";
                                    label7.Font = new System.Drawing.Font("Tahoma", 18F, System.Drawing.FontStyle.Bold);
                                }
                            }
                            if (x == 8)
                            {
                                label8.Visible = true;
                                if (tabela.Rows[x - 1]["ile"].ToString() != "0")
                                {
                                    label8.Text = tabela.Rows[x - 1]["poziom"].ToString() + " - " + tabela.Rows[x - 1]["ile"].ToString();
                                }
                                else
                                {
                                    label8.Text = tabela.Rows[x - 1]["poziom"].ToString() + " - wolne ";
                                    label8.Font = new System.Drawing.Font("Tahoma", 18F, System.Drawing.FontStyle.Bold);
                                }
                            }

                        }




                        label9.Text = tabela.Rows[0]["regal"].ToString() + " - " + tabela.Rows[0]["miejsce"].ToString();







                        //tabela.Rows[0]["id"];
                    }

                }

            }
            else
            {
                MessageBox.Show("To nie jest etykieta miejsca. Spróbuj ponownie.");
            }
            ZacznijSkanowanie();



        }

        private static DataTable Konwersja_XmlNodeList_DataTable(XmlNodeList xmlnode2)
        {
            DataTable dt = new DataTable();
            dt.Columns.Add("poziom", typeof(string));
            dt.Columns.Add("regal", typeof(string));
            dt.Columns.Add("miejsce", typeof(string));
            dt.Columns.Add("ile", typeof(string));
            foreach (XmlNode wynik in xmlnode2)
            {
                DataRow dtrow = dt.NewRow();
                dtrow["poziom"] = wynik["poziom"].InnerText;
                dtrow["regal"] = wynik["regal"].InnerText;
                dtrow["miejsce"] = wynik["miejsce"].InnerText;
                dtrow["ile"] = wynik["ile"].InnerText;
                dt.Rows.Add(dtrow);
            }
            return dt;
        }

        private void button1_Click(object sender, EventArgs e)
        {
            Wlasciwosci.CurrentOperacja = "0";
            this.Skanowanie.Abort();
            Skaner.Przewij_Skanowanie();
            this.myParent.Show();
            this.Close();

        }
    }


}