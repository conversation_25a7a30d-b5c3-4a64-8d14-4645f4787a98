<?php

include_once 'Db.class.php';
include_once 'funkcje.inc';
$db = new Db();

$imie_nazwisko = $_GET['imie_nazwisko'];
$akcja = $_GET['akcja'];

$komunikat = "OK";


$zadania_dane_id = $_GET['zadania_dane_id'];


//header('Content-type: text/xml');
//echo '<dane>';
//$aa= $db->insertInto("zadania_dane", array('zadanie_head_id' => '3'));
//echo "$aa";
//return false;
//$stanowisko_id = $_GET['stanowisko_id'];

$zadania_dane_id_local = 0;
$baza_danych_local = "";

if (!empty($_GET['status_wymagany'])) {
    $status_wymagany = " and zd.status=" . $_GET['status_wymagany'];
} else {
    $status_wymagany = "";
}

if (!empty($_GET['zadanie_head_id_wymagany'])) {
    $zadanie_head_id_wymagany = " and zh.id=" . $_GET['zadanie_head_id_wymagany'];
} else {
    $zadanie_head_id_wymagany = "";
}



$sql = 'SELECT  zh.baza_danych,zd.id,zh.system_id,zh.docin_id_wew,zh.docout_id_wew,zh.typ,zd.zadanie_head_id
FROM wmsgg.zadania_dane zd
LEFT JOIN zadania_head AS zh ON zh.id=zd.zadanie_head_id
LEFT JOIN zadania_doc_type AS zdt ON zh.doc_type=zdt.id
LEFT JOIN zadania_typ AS zt ON zh.typ=zt.id
LEFT JOIN pracownicy AS p ON zd.przydzielenie_pracownik_id=p.id
LEFT JOIN pracownicy AS p2 ON zd.realizacja_pracownik_id=p2.id


WHERE p.imie_nazwisko="' . $_GET['imie_nazwisko'] . '"
  AND (zd.status=1 or zd.status=2 or zd.status=11) ' . $status_wymagany . ' ' . $zadanie_head_id_wymagany . '
  AND zh.status_dokumentu>0
GROUP BY zd.id
         
ORDER BY zh.priorytet DESC,
if(zh.planowany_czas_realizacji is null,DATE_ADD(zh.ts, INTERVAL +24 HOUR),
zh.planowany_czas_realizacji) DESC, 
zh.zadania_head_rodzic_id DESC 


LIMIT 1 ';
//echo $sql;
$result = $db->mGetResultAsXML($sql);

$ile = count($result);

if ($ile > 0) {



    foreach ($result as $key => $value) {
        $baza_danych_local = $value["baza_danych"];
        $zadania_dane_id_local = $value["id"];
        $system_id = $value["system_id"];
    }



    if (count($result) > 0 && $result[0]['status'] == "1") {
        $sql = ' update  zadania_dane z set  z.start=NOW() WHERE z.id=' . $result[0]['id'];
        //echo "<br>" . $sql;
        $result8 = $db->mGetResultAsXML($sql);
    }
}
$komunikat.=$db->errors;
//    echo '<komunikat>', htmlentities($komunikat), '</komunikat>';
//    echo '<zadan>', htmlentities($ile), '</zadan>';
$sql = 'select now() as czas_aktualny';
//echo "<br>" . $sql;
$czas_aktualny = $db->mGetResultAsXML($sql);
$arr = array(
    'komunikat' => $komunikat,
    'zadan' => $ile,
    'czas_aktualny' => $czas_aktualny[0]['czas_aktualny']
);

foreach ($result as $index => $aRow) {
    //$typ = $aRow['typ'];
    //echo "aaa".$aRow['id'];
    $arr = array_merge($arr, $aRow);

    //echo "aaa";
}
?>