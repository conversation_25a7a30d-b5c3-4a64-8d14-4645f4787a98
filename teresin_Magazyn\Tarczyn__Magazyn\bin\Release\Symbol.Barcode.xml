<?xml version="1.0"?>
<doc>
    <assembly>
        <name>Symbol.Barcode</name>
    </assembly>
    <members>
        <member name="T:Symbol.Barcode.RSMAPI">
            <summary>
            The class for the RSM (Remote Scanner Management) API. It provides the list of RSM attributes.
            An object of this class gets created by the Barcode object automatically. 
            Application should not attempt to instantiate an object of this class.
            NOTE: The RSM feature can only be used when using remote scanner devices such as RS507.
            </summary>
            <example>
            <code lang="C#">
            // Create the Barcode Reader object using the first available scanner
            private Symbol.Barcode.Reader MyReader = null;
            
            MyReader = new Symbol.Barcode.Reader();
            
            // Enable the Reader
            MyReader.Actions.Enable();
            
            // Read the current and factory default values of the model number attribute 
            string txtModelNumber = MyReader.RSM.ModelNumber.CurrentValue;
            string txtFdfltModelNumber = MyReader.RSM.ModelNumber.FactoryDefaultValue;
            
            // Read the status of the Bluetooth encryption
            bool bBTEncryption = MyReader.RSM.BluetoothEncryption.CurrentValue;
            
            // Check if the PIN code attribute is supported and modify it
            if (MyReader.RSM.BluetoothPINCode.IsSupported)
            {
                MyReader.RSM.BluetoothPINCode.CurrentValue = "12346";
            }
            
            // Disable the Reader object
            MyReader.Actions.Disable();
            
            // Dispose the Reader object
            MyReader.Dispose();
            </code>
            
            <code lang="VB.NET">
            ' Create the Barcode Reader object using the first available scanner
            Dim MyReader As Symbol.Barcode.Reader = Nothing
            MyReader = New Symbol.Barcode.Reader()
            
            ' Enable the Reader
            MyReader.Actions.Enable()
            
            ' Read the current and factory default values of the model number attribute
            Dim txtModelNumber As String = MyReader.RSM.ModelNumber.CurrentValue
            Dim txtFdfltModelNumber As String = MyReader.RSM.ModelNumber.FactoryDefaultValue
            
            ' Write the Bluetooth PIN code
            Dim bBTEncryption As Boolean = MyReader.RSM.BluetoothEncryption.CurrentValue
            
            ' Check if the PIN code attribute is supported and modify it
            If MyReader.RSM.BluetoothPINCode.IsSupported Then
               MyReader.RSM.BluetoothPINCode.CurrentValue = "12346"
            End If
            
            ' Disable the Reader object
            MyReader.Actions.Disable()
            
            ' Dispose the Reader object
            MyReader.Dispose()
            </code>
            </example>
        </member>
        <member name="M:Symbol.Barcode.RSMAPI.#ctor(Symbol.Barcode.Reader)">
            <summary>
            The constructor.
            </summary>
        </member>
        <member name="P:Symbol.Barcode.RSMAPI.SupportedAttribs">
            <summary>
            Provides a list of supported RSM attributes.
            </summary>
        </member>
        <member name="P:Symbol.Barcode.RSMAPI.ModelNumber">
            <summary>
            Provides access to model number. The maximum length of the attribute value is 18.
            </summary>
        </member>
        <member name="P:Symbol.Barcode.RSMAPI.SerialNumber">
            <summary>
            Provides access to serial number. The maximum length of the attribute value is 16.
            NOTE: Only current value is relevant for this attribute.
            </summary>
        </member>
        <member name="P:Symbol.Barcode.RSMAPI.DateofManufacture">
            <summary>
            Provides access to date of scanner device manufacture. The maximum length of the attribute value is 7.
            NOTE: Only current value is relevant for this attribute.
            </summary>
        </member>
        <member name="P:Symbol.Barcode.RSMAPI.DateofService">
            <summary>
            Provides access to date of last repair done at a Motorola authorized repair facility. The maximum length of the attribute value is 7.
            </summary>
        </member>
        <member name="P:Symbol.Barcode.RSMAPI.BluetoothAddress">
            <summary>
            Provides access to unique Bluetooth address. The length of the byte array value of the attribute is 6 bytes.
            NOTE: Only current value is relevant for this attribute.
            </summary>
        </member>
        <member name="P:Symbol.Barcode.RSMAPI.BluetoothAuthentication">
            <summary>
            Provides access to the status of Bluetooth Authetication.
            </summary>
        </member>
        <member name="P:Symbol.Barcode.RSMAPI.BluetoothEncryption">
            <summary>
            Provides access to the status of Bluetooth encryption.
            </summary>
        </member>
        <member name="P:Symbol.Barcode.RSMAPI.BluetoothPINCode">
            <summary>
            Provides access to the PIN code of the Bluetooth device when Bluetooth authentication is enabled.  
            The PIN code is permanently saved. The maximum length of the attribute value is 5.
            The default PIN stored in memory is “12345”.
            </summary>
        </member>
        <member name="P:Symbol.Barcode.RSMAPI.ReconnectAttempts">
            <summary>
            Provides access to the reconnect attempts parameter of the scanner device.  
            </summary>
        </member>
        <member name="P:Symbol.Barcode.RSMAPI.BeeponReconnectAttempt">
            <summary>
            Provides access to the beeps when reconnect attempts is made.  
            </summary>
        </member>
        <member name="P:Symbol.Barcode.RSMAPI.HIDAutoReconnect">
            <summary>
            Provides access to the HID reconnection parameter.  
            </summary>
        </member>
        <member name="P:Symbol.Barcode.RSMAPI.BluetoothFriendlyName">
            <summary>
            Provides access to the friendly name of the Bluetooth device. The maximum length of the attribute value is 23.
            NOTE: Only current value is relevant for this attribute.
            </summary>
        </member>
        <member name="P:Symbol.Barcode.RSMAPI.PINCodeType">
            <summary>
            Provides access to the type of PIN code to use for the Bluetooth device. This attribute is not saved permanently on the Bluetooth device.
            </summary>
        </member>
        <member name="P:Symbol.Barcode.RSMAPI.BluetoothInquiryMode">
            <summary>
            Provides access to the inquiry mode of the Bluetooth device.  
            </summary>
        </member>
        <member name="P:Symbol.Barcode.RSMAPI.MemsEnable">
            <summary>
            Provides access to the mems feature.  
            </summary>
        </member>
        <member name="P:Symbol.Barcode.RSMAPI.ProximityEnable">
            <summary>
            Provides access to the proximity feature.  
            </summary>
        </member>
        <member name="P:Symbol.Barcode.RSMAPI.ProximityDistance">
            <summary>
            Provides access to the proximity distance.  
            </summary>
        </member>
        <member name="P:Symbol.Barcode.RSMAPI.PagingEnable">
            <summary>
            Provides access to the paging feature.  
            </summary>
        </member>
        <member name="P:Symbol.Barcode.RSMAPI.PagingBeepSequence">
            <summary>
            Provides access to the paging beep sequence.  
            </summary>
        </member>
        <member name="P:Symbol.Barcode.RSMAPI.LowBatteryIndicationEnable">
            <summary>
            Provides access to the low battery indicator.  
            </summary>
        </member>
        <member name="P:Symbol.Barcode.RSMAPI.ScantriggerWakeupEnable">
            <summary>
            Provides access to the trigger wakeup feature.  
            </summary>
        </member>
        <member name="P:Symbol.Barcode.RSMAPI.BluetoothAutoReconnect">
            <summary>
            Provides access to the automatic reconnect feature of the Bluetooth device.
            </summary>
        </member>
        <member name="P:Symbol.Barcode.RSMAPI.LowBatteryIndicationCycle">
            <summary>
            Provides access to the low battery indication cycle.
            </summary>
        </member>
        <member name="P:Symbol.Barcode.RSMAPI.PreferredWirelessHost">
            <summary>
            Provides access to the preferred Bluetoothwireless host.
            NOTE: PreferredWirelessHost attribute is not supported. Therefore, this property and its corresponding class "rsmPreferredWirelessHost" will be deprecated in future releases.
            </summary>
        </member>
        <member name="P:Symbol.Barcode.RSMAPI.PagingActivate">
            <summary>
            Provides access to the paging activation command.
            </summary>
        </member>
        <member name="P:Symbol.Barcode.RSMAPI.FirmwareVersion">
            <summary>
            Provides access to the firmware version. The maximum length of the attribute value is 18.
            NOTE: Only current value is relevant for this attribute.
            </summary>
        </member>
        <member name="P:Symbol.Barcode.RSMAPI.DeviceClass">
            <summary>
            Provides access to the device class of the system. The maximum length of the attribute value is 18.
            </summary>
        </member>
        <member name="P:Symbol.Barcode.RSMAPI.BatteryStatus">
            <summary>
            Provides access to the battery status.
            </summary>
        </member>
        <member name="P:Symbol.Barcode.RSMAPI.BatteryCapacity">
            <summary>
            Provides access to the battery capacity.
            </summary>
        </member>
        <member name="P:Symbol.Barcode.RSMAPI.BatteryID">
            <summary>
            Provides access to the battery ID.
            </summary>
        </member>
        <member name="P:Symbol.Barcode.RSMAPI.HardwareVersion">
            <summary>
            Provides access to the hardware version.
            NOTE: HardwareVersion attribute will not be supported in the future. Therefore, this property and its corresponding class "rsmHardwareVersion" will be deprecated in future releases.
            </summary>
        </member>
        <member name="P:Symbol.Barcode.RSMAPI.ScanlineWidth">
            <summary>
            Provides access to the pick list mode.
            </summary>
        </member>
        <member name="P:Symbol.Barcode.RSMAPI.BluetoothDisconnect">
            <summary>
            Provides access to the Bluetooth disconnect command.
            </summary>
        </member>
        <member name="P:Symbol.Barcode.RSMAPI.BluetoothUnPair">
            <summary>
            Provides access to the Bluetooth unpair command.
            </summary>
        </member>
        <member name="P:Symbol.Barcode.RSMAPI.GoodScansDelay">
            <summary>
            Provides access to the delay between proximity continuous good scans.
            </summary>
        </member>
        <member name="P:Symbol.Barcode.RSMAPI.ProximityContinuousEnable">
            <summary>
            Provides access to the proximity continuous feature.
            </summary>
        </member>
        <member name="P:Symbol.Barcode.RSMAPI.ExclusiveCode128Enable">
            <summary>
            Provides access to the feature for ignoring Code 128’s beginning with 420 and 421
            </summary>
        </member>
        <member name="P:Symbol.Barcode.RSMAPI.DisableGoodDecodeLEDBeep">
            <summary>
            Provides access to disable the good decode Green LED and beep on scanner
            </summary>
        </member>
        <member name="P:Symbol.Barcode.RSMAPI.ForcePairingSave">
            <summary>
            Provides access to the feature to save the Bluetooth address when pairing fails. This attribute is permanently saved.
            </summary>
        </member>
        <member name="T:Symbol.Barcode.RSMAPI.RSMAttrib">
            <summary>
            The base class for providing access to the RSM (Remote Scanner Management) attributes.
            </summary>
        </member>
        <member name="M:Symbol.Barcode.RSMAPI.RSMAttrib.#ctor">
            <summary>
            The constructor.
            </summary>
        </member>
        <member name="P:Symbol.Barcode.RSMAPI.RSMAttrib.IsSupported">
            <summary>
            Returns whether or not the RSM attribute is supported by the scanner device.
            </summary>
        </member>
        <member name="P:Symbol.Barcode.RSMAPI.RSMAttrib.AttribNumber">
            <summary>
            Returns the unique number of the RSM attribute.
            </summary>
        </member>
        <member name="P:Symbol.Barcode.RSMAPI.RSMAttrib.AttribType">
            <summary>
            Returns the type of the RSM attribute.
            </summary>
        </member>
        <member name="T:Symbol.Barcode.RSMAPI.rsmModelNumber">
            <summary>
            Provides access to model number. The maximum length of the attribute value is 18.
            </summary>
        </member>
        <member name="P:Symbol.Barcode.RSMAPI.rsmModelNumber.CurrentValue">
            <summary>
            Provides the current value of the attribute.
            </summary>
        </member>
        <member name="P:Symbol.Barcode.RSMAPI.rsmModelNumber.FactoryDefaultValue">
            <summary>
            Provides the factory default value of the attribute.
            </summary>
        </member>
        <member name="P:Symbol.Barcode.RSMAPI.rsmModelNumber.CustomDefaultValue">
            <summary>
            Provides the custom default value of the attribute.
            </summary>
        </member>
        <member name="T:Symbol.Barcode.RSMAPI.rsmSerialNumber">
            <summary>
            Provides access to serial number. The maximum length of the attribute value is 16.
            NOTE: Only current value is relevant for this attribute.
            </summary>
        </member>
        <member name="P:Symbol.Barcode.RSMAPI.rsmSerialNumber.CurrentValue">
            <summary>
            Provides the current value of the attribute.
            </summary>
        </member>
        <member name="P:Symbol.Barcode.RSMAPI.rsmSerialNumber.FactoryDefaultValue">
            <summary>
            Provides the factory default value of the attribute.
            </summary>
        </member>
        <member name="P:Symbol.Barcode.RSMAPI.rsmSerialNumber.CustomDefaultValue">
            <summary>
            Provides the custom default value of the attribute.
            </summary>
        </member>
        <member name="T:Symbol.Barcode.RSMAPI.rsmDateofManufacture">
            <summary>
            Provides access to date of scanner device manufacture. The maximum length of the attribute value is 7.
            NOTE: Only current value is relevant for this attribute.
            </summary>
        </member>
        <member name="P:Symbol.Barcode.RSMAPI.rsmDateofManufacture.CurrentValue">
            <summary>
            Provides the current value of the attribute.
            </summary>
        </member>
        <member name="P:Symbol.Barcode.RSMAPI.rsmDateofManufacture.FactoryDefaultValue">
            <summary>
            Provides the factory default value of the attribute.
            </summary>
        </member>
        <member name="P:Symbol.Barcode.RSMAPI.rsmDateofManufacture.CustomDefaultValue">
            <summary>
            Provides the custom default value of the attribute.
            </summary>
        </member>
        <member name="T:Symbol.Barcode.RSMAPI.rsmDateofService">
            <summary>
            Provides access to date of last repair done at a Motorola authorized repair facility. The maximum length of the attribute value is 7.
            </summary>
        </member>
        <member name="P:Symbol.Barcode.RSMAPI.rsmDateofService.CurrentValue">
            <summary>
            Provides the current value of the attribute.
            </summary>
        </member>
        <member name="P:Symbol.Barcode.RSMAPI.rsmDateofService.FactoryDefaultValue">
            <summary>
            Provides the factory default value of the attribute.
            </summary>
        </member>
        <member name="P:Symbol.Barcode.RSMAPI.rsmDateofService.CustomDefaultValue">
            <summary>
            Provides the custom default value of the attribute.
            </summary>
        </member>
        <member name="T:Symbol.Barcode.RSMAPI.rsmBluetoothAddress">
            <summary>
            Provides access to unique Bluetooth address. The length of the byte array value of the attribute is 6 bytes.
            NOTE: Only current value is relevant for this attribute.
            </summary>
        </member>
        <member name="P:Symbol.Barcode.RSMAPI.rsmBluetoothAddress.CurrentValue">
            <summary>
            Provides the current value of the attribute.
            </summary>
        </member>
        <member name="P:Symbol.Barcode.RSMAPI.rsmBluetoothAddress.FactoryDefaultValue">
            <summary>
            Provides the factory default value of the attribute.
            </summary>
        </member>
        <member name="P:Symbol.Barcode.RSMAPI.rsmBluetoothAddress.CustomDefaultValue">
            <summary>
            Provides the custom default value of the attribute.
            </summary>
        </member>
        <member name="P:Symbol.Barcode.RSMAPI.rsmBluetoothAddress.RawCurrentValue">
            <summary>
            Provides the raw (as byte array) current value of the attribute as received from the driver.  The length of the byte array is 6 bytes.
            </summary>
        </member>
        <member name="P:Symbol.Barcode.RSMAPI.rsmBluetoothAddress.RawFactoryDefaultValue">
            <summary>
            Provides the raw (as byte array) factory default value of the attribute as received from the driver. The length of the byte array is 6 bytes.
            </summary>
        </member>
        <member name="P:Symbol.Barcode.RSMAPI.rsmBluetoothAddress.RawCustomDefaultValue">
            <summary>
            Provides the raw (as byte array) custom default value of the attribute as received from the driver. The length of the byte array is 6 bytes.
            </summary>
        </member>
        <member name="P:Symbol.Barcode.RSMAPI.rsmBluetoothAddress.AttribSubType">
            <summary>
            Returns the subtype of the attribute.
            </summary>
        </member>
        <member name="T:Symbol.Barcode.RSMAPI.rsmBluetoothAuthentication">
            <summary>
            Provides access to the status of Bluetooth Authetication.
            </summary>
        </member>
        <member name="P:Symbol.Barcode.RSMAPI.rsmBluetoothAuthentication.CurrentValue">
            <summary>
            Provides the current value of the attribute. 
            True if the Bluetooth authentication is required.
            </summary>
        </member>
        <member name="P:Symbol.Barcode.RSMAPI.rsmBluetoothAuthentication.FactoryDefaultValue">
            <summary>
            Provides the factory default value of the attribute.
            </summary>        
        </member>
        <member name="P:Symbol.Barcode.RSMAPI.rsmBluetoothAuthentication.CustomDefaultValue">
            <summary>
            Provides the custom default value of the attribute.
            </summary>
        </member>
        <member name="T:Symbol.Barcode.RSMAPI.rsmBluetoothEncryption">
            <summary>
            Provides access to the status of Bluetooth encryption.
            </summary>
        </member>
        <member name="P:Symbol.Barcode.RSMAPI.rsmBluetoothEncryption.CurrentValue">
            <summary>
            Provides the current value of the attribute.
            True if the encryption over Bluetooth is required.
            </summary>
        </member>
        <member name="P:Symbol.Barcode.RSMAPI.rsmBluetoothEncryption.FactoryDefaultValue">
            <summary>
            Provides the factory default value of the attribute.
            </summary>
        </member>
        <member name="P:Symbol.Barcode.RSMAPI.rsmBluetoothEncryption.CustomDefaultValue">
            <summary>
            Provides the custom default value of the attribute.
            </summary>
        </member>
        <member name="T:Symbol.Barcode.RSMAPI.rsmBluetoothPINCode">
            <summary>
            Provides access to the PIN code of the Bluetooth device when Bluetooth authentication is enabled.  
            The PIN code is permanently saved. The maximum length of the attribute value is 5.
            The default PIN stored in memory is “12345”.
            </summary>
        </member>
        <member name="P:Symbol.Barcode.RSMAPI.rsmBluetoothPINCode.CurrentValue">
            <summary>
            Provides the current value of the attribute.
            Pincode used when the Bluetooth Authentication is enabled (Permanently saved).
            </summary>
        </member>
        <member name="P:Symbol.Barcode.RSMAPI.rsmBluetoothPINCode.FactoryDefaultValue">
            <summary>
            Provides the factory default value of the attribute.
            </summary>
        </member>
        <member name="P:Symbol.Barcode.RSMAPI.rsmBluetoothPINCode.CustomDefaultValue">
            <summary>
            Provides the custom default value of the attribute.
            </summary>
        </member>
        <member name="T:Symbol.Barcode.RSMAPI.rsmReconnectAttempts">
            <summary>
            Provides access to the reconnect attempts parameter of the scanner device.
            </summary>
        </member>
        <member name="P:Symbol.Barcode.RSMAPI.rsmReconnectAttempts.CurrentValue">
            <summary>
            Provides the current value of the attribute.
            Indicates the time duration during which the scanner tries to reestablish the connection, when it goes out of range.
            Use the value: 
                6 for 30 secs duration
                7 for 35 secs duration
                8 for 40 secs duration
                9 for 45 secs duration
                10 for 50 secs duration
                11 for 55 secs duration
                12 for 60 secs duration
            </summary>
        </member>
        <member name="P:Symbol.Barcode.RSMAPI.rsmReconnectAttempts.FactoryDefaultValue">
            <summary>
            Provides the factory default value of the attribute.
            </summary>
        </member>
        <member name="P:Symbol.Barcode.RSMAPI.rsmReconnectAttempts.CustomDefaultValue">
            <summary>
            Provides the custom default value of the attribute.
            </summary>
        </member>
        <member name="T:Symbol.Barcode.RSMAPI.rsmBeeponReconnectAttempt">
            <summary>
            Provides access to the beeps when reconnect attempts is made.  
            </summary>
        </member>
        <member name="P:Symbol.Barcode.RSMAPI.rsmBeeponReconnectAttempt.CurrentValue">
            <summary>
            Provides the current value of the attribute.
            When this attribute is enabled, the scanner will emit 5 short high beeps every 5 seconds 
            while the reconnection attempt is in progress.
            </summary>
        </member>
        <member name="P:Symbol.Barcode.RSMAPI.rsmBeeponReconnectAttempt.FactoryDefaultValue">
            <summary>
            Provides the factory default value of the attribute.
            </summary>
        </member>
        <member name="P:Symbol.Barcode.RSMAPI.rsmBeeponReconnectAttempt.CustomDefaultValue">
            <summary>
            Provides the custom default value of the attribute.
            </summary>
        </member>
        <member name="T:Symbol.Barcode.RSMAPI.rsmHIDAutoReconnect">
            <summary>
            Provides access to the HID reconnection parameter.  
            </summary>
        </member>
        <member name="P:Symbol.Barcode.RSMAPI.rsmHIDAutoReconnect.CurrentValue">
            <summary>
            Provides the current value of the attribute.
            Auto-reconnect behavior of the scanner when HID connection is lost.
            0 - Never Reconnect
            1 - Reconnect on Data
            2 - Reconnect Immediately
            </summary>
        </member>
        <member name="P:Symbol.Barcode.RSMAPI.rsmHIDAutoReconnect.FactoryDefaultValue">
            <summary>
            Provides the factory default value of the attribute.
            </summary>
        </member>
        <member name="P:Symbol.Barcode.RSMAPI.rsmHIDAutoReconnect.CustomDefaultValue">
            <summary>
            Provides the custom default value of the attribute.
            </summary>
        </member>
        <member name="T:Symbol.Barcode.RSMAPI.rsmBluetoothFriendlyName">
            <summary>
            Provides access to the friendly name of the Bluetooth device. The maximum length of the attribute value is 23.
            NOTE: Only current value is relevant for this attribute.
            </summary>
        </member>
        <member name="P:Symbol.Barcode.RSMAPI.rsmBluetoothFriendlyName.CurrentValue">
            <summary>
            Provides the current value of the attribute.
            Friendly name displayed by Bluetooth remote devices (Permanently saved).
            </summary>
        </member>
        <member name="P:Symbol.Barcode.RSMAPI.rsmBluetoothFriendlyName.FactoryDefaultValue">
            <summary>
            Provides the factory default value of the attribute.
            </summary>
        </member>
        <member name="P:Symbol.Barcode.RSMAPI.rsmBluetoothFriendlyName.CustomDefaultValue">
            <summary>
            Provides the custom default value of the attribute.
            </summary>
        </member>
        <member name="T:Symbol.Barcode.RSMAPI.rsmPINCodeType">
            <summary>
            Provides access to the type of PIN code to use for the Bluetooth device. This attribute is not saved permanently on the Bluetooth device.
            </summary>
        </member>
        <member name="P:Symbol.Barcode.RSMAPI.rsmPINCodeType.CurrentValue">
            <summary>
            Provides the current value of the attribute.
            False – (default) Use PIN code stored in memory of Bluetooth device. The default PIN stored in memory is “12345”.
            True  – Prompt the user to scan a new PIN code using the Bluetooth device.
            This attribute is not saved permanently on the Bluetooth device.
            </summary>
        </member>
        <member name="P:Symbol.Barcode.RSMAPI.rsmPINCodeType.FactoryDefaultValue">
            <summary>
            Provides the factory default value of the attribute.
            </summary>
        </member>
        <member name="P:Symbol.Barcode.RSMAPI.rsmPINCodeType.CustomDefaultValue">
            <summary>
            Provides the custom default value of the attribute.
            </summary>
        </member>
        <member name="T:Symbol.Barcode.RSMAPI.rsmBluetoothInquiryMode">
            <summary>
            Provides access to the inquiry mode of the Bluetooth device.  
            </summary>
        </member>
        <member name="P:Symbol.Barcode.RSMAPI.rsmBluetoothInquiryMode.CurrentValue">
            <summary>
            Provides the current value of the attribute.
            True for using the limited Inquiry mode
            False for using the general inquiry mode
            </summary>
        </member>
        <member name="P:Symbol.Barcode.RSMAPI.rsmBluetoothInquiryMode.FactoryDefaultValue">
            <summary>
            Provides the factory default value of the attribute.
            </summary>
        </member>
        <member name="P:Symbol.Barcode.RSMAPI.rsmBluetoothInquiryMode.CustomDefaultValue">
            <summary>
            Provides the custom default value of the attribute.
            </summary>
        </member>
        <member name="T:Symbol.Barcode.RSMAPI.rsmMemsEnable">
            <summary>
            Provides access to the mems feature.  
            </summary>
        </member>
        <member name="P:Symbol.Barcode.RSMAPI.rsmMemsEnable.CurrentValue">
            <summary>
            Provides the current value of the attribute.
            Enable/Disable mems feature
            </summary>
        </member>
        <member name="P:Symbol.Barcode.RSMAPI.rsmMemsEnable.FactoryDefaultValue">
            <summary>
            Provides the factory default value of the attribute.
            </summary>
        </member>
        <member name="P:Symbol.Barcode.RSMAPI.rsmMemsEnable.CustomDefaultValue">
            <summary>
            Provides the custom default value of the attribute.
            </summary>
        </member>
        <member name="T:Symbol.Barcode.RSMAPI.rsmProximityEnable">
            <summary>
            Provides access to the proximity feature.  
            </summary>
        </member>
        <member name="P:Symbol.Barcode.RSMAPI.rsmProximityEnable.CurrentValue">
            <summary>
            Provides the current value of the attribute.
            Enable/disable proximity
            </summary>
        </member>
        <member name="P:Symbol.Barcode.RSMAPI.rsmProximityEnable.FactoryDefaultValue">
            <summary>
            Provides the factory default value of the attribute.
            </summary>
        </member>
        <member name="P:Symbol.Barcode.RSMAPI.rsmProximityEnable.CustomDefaultValue">
            <summary>
            Provides the custom default value of the attribute.
            </summary>
        </member>
        <member name="T:Symbol.Barcode.RSMAPI.rsmProximityDistance">
            <summary>
            Provides access to the proximity distance.  
            </summary>
        </member>
        <member name="P:Symbol.Barcode.RSMAPI.rsmProximityDistance.CurrentValue">
            <summary>
            Provides the current value of the attribute.
            Proximity distance:
            0 – Short
            1 – Mid
            2 – Long
            </summary>
        </member>
        <member name="P:Symbol.Barcode.RSMAPI.rsmProximityDistance.FactoryDefaultValue">
            <summary>
            Provides the factory default value of the attribute.
            </summary>
        </member>
        <member name="P:Symbol.Barcode.RSMAPI.rsmProximityDistance.CustomDefaultValue">
            <summary>
            Provides the custom default value of the attribute.
            </summary>
        </member>
        <member name="T:Symbol.Barcode.RSMAPI.rsmPagingEnable">
            <summary>
            Provides access to the paging feature.  
            </summary>
        </member>
        <member name="P:Symbol.Barcode.RSMAPI.rsmPagingEnable.CurrentValue">
            <summary>
            Provides the current value of the attribute.
            Enable/disable paging
            </summary>
        </member>
        <member name="P:Symbol.Barcode.RSMAPI.rsmPagingEnable.FactoryDefaultValue">
            <summary>
            Provides the factory default value of the attribute.
            </summary>
        </member>
        <member name="P:Symbol.Barcode.RSMAPI.rsmPagingEnable.CustomDefaultValue">
            <summary>
            Provides the custom default value of the attribute.
            </summary>
        </member>
        <member name="T:Symbol.Barcode.RSMAPI.rsmPagingBeepSequence">
            <summary>
            Provides access to the paging beep sequence.  
            </summary>
        </member>
        <member name="P:Symbol.Barcode.RSMAPI.rsmPagingBeepSequence.CurrentValue">
            <summary>
            Provides the current value of the attribute.
            Paging beep sequence
            Beep pattern values: 0-15
            </summary>
        </member>
        <member name="P:Symbol.Barcode.RSMAPI.rsmPagingBeepSequence.FactoryDefaultValue">
            <summary>
            Provides the factory default value of the attribute.
            </summary>
        </member>
        <member name="P:Symbol.Barcode.RSMAPI.rsmPagingBeepSequence.CustomDefaultValue">
            <summary>
            Provides the custom default value of the attribute.
            </summary>
        </member>
        <member name="T:Symbol.Barcode.RSMAPI.rsmLowBatteryIndicationEnable">
            <summary>
            Provides access to the low battery indicator.  
            </summary>
        </member>
        <member name="P:Symbol.Barcode.RSMAPI.rsmLowBatteryIndicationEnable.CurrentValue">
            <summary>
            Provides the current value of the attribute.
            </summary>
        </member>
        <member name="P:Symbol.Barcode.RSMAPI.rsmLowBatteryIndicationEnable.FactoryDefaultValue">
            <summary>
            Provides the factory default value of the attribute.
            </summary>
        </member>
        <member name="P:Symbol.Barcode.RSMAPI.rsmLowBatteryIndicationEnable.CustomDefaultValue">
            <summary>
            Provides the custom default value of the attribute.
            </summary>
        </member>
        <member name="T:Symbol.Barcode.RSMAPI.rsmScantriggerWakeupEnable">
            <summary>
            Provides access to the trigger wakeup feature.  
            </summary>
        </member>
        <member name="P:Symbol.Barcode.RSMAPI.rsmScantriggerWakeupEnable.CurrentValue">
            <summary>
            Provides the current value of the attribute.
            True - Scan trigger serves as device wakeup source from low power
            False - Scan trigger does NOT serve as device wakeup source from low power
            </summary>
        </member>
        <member name="P:Symbol.Barcode.RSMAPI.rsmScantriggerWakeupEnable.FactoryDefaultValue">
            <summary>
            Provides the factory default value of the attribute.
            </summary>
        </member>
        <member name="P:Symbol.Barcode.RSMAPI.rsmScantriggerWakeupEnable.CustomDefaultValue">
            <summary>
            Provides the custom default value of the attribute.
            </summary>
        </member>
        <member name="T:Symbol.Barcode.RSMAPI.rsmBluetoothAutoReconnect">
            <summary>
            Provides access to the automatic reconnect feature of the Bluetooth device.
            </summary>
        </member>
        <member name="P:Symbol.Barcode.RSMAPI.rsmBluetoothAutoReconnect.CurrentValue">
            <summary>
            Provides the current value of the attribute.
            Defines Bluetooth reconnection scheme:
            0 –None
            1 - On Power
            2 - On Out Of Range
            3 - On Power Out Of Range
            </summary>
        </member>
        <member name="P:Symbol.Barcode.RSMAPI.rsmBluetoothAutoReconnect.FactoryDefaultValue">
            <summary>
            Provides the factory default value of the attribute.
            </summary>
        </member>
        <member name="P:Symbol.Barcode.RSMAPI.rsmBluetoothAutoReconnect.CustomDefaultValue">
            <summary>
            Provides the custom default value of the attribute.
            </summary>
        </member>
        <member name="T:Symbol.Barcode.RSMAPI.rsmLowBatteryIndicationCycle">
            <summary>
            Provides access to the low battery indication cycle.
            </summary>
        </member>
        <member name="P:Symbol.Barcode.RSMAPI.rsmLowBatteryIndicationCycle.CurrentValue">
            <summary>
            Provides the current value of the attribute.
            Low battery indication cycle time in seconds.
            Use the value:
                0 for 15 secs cycle
                1 for 30 secs cycle
                2 for 60 secs cycle
                3 for 90 secs cycle
                4 for 120 secs cycle
            </summary>
        </member>
        <member name="P:Symbol.Barcode.RSMAPI.rsmLowBatteryIndicationCycle.FactoryDefaultValue">
            <summary>
            Provides the factory default value of the attribute.
            </summary>
        </member>
        <member name="P:Symbol.Barcode.RSMAPI.rsmLowBatteryIndicationCycle.CustomDefaultValue">
            <summary>
            Provides the custom default value of the attribute.
            </summary>
        </member>
        <member name="T:Symbol.Barcode.RSMAPI.rsmPreferredWirelessHost">
            <summary>
            Provides access to the preferred Bluetoothwireless host.
            NOTE: PreferredWirelessHost attribute is not supported. Therefore, this class will be deprecated in future releases.
            </summary>
        </member>
        <member name="P:Symbol.Barcode.RSMAPI.rsmPreferredWirelessHost.CurrentValue">
            <summary>
            Provides the current value of the attribute.
            Preferred Bluetooth wireless host:
            18 – Scan
            19 – Spp
            20 – Hid
            </summary>
        </member>
        <member name="P:Symbol.Barcode.RSMAPI.rsmPreferredWirelessHost.FactoryDefaultValue">
            <summary>
            Provides the factory default value of the attribute.
            </summary>
        </member>
        <member name="P:Symbol.Barcode.RSMAPI.rsmPreferredWirelessHost.CustomDefaultValue">
            <summary>
            Provides the custom default value of the attribute.
            </summary>
        </member>
        <member name="T:Symbol.Barcode.RSMAPI.rsmPagingActivate">
            <summary>
            Provides access to the paging activation command.
            </summary>
        </member>
        <member name="P:Symbol.Barcode.RSMAPI.rsmPagingActivate.CurrentValue">
            <summary>
            Start /Stop paging to scanner
            0 – stop
            1 – start
            NOTE: Since this property is used for issuing a command, this is a set-only property.
            </summary>
        </member>
        <member name="T:Symbol.Barcode.RSMAPI.rsmFirmwareVersion">
            <summary>
            Provides access to the firmware version. The maximum length of the attribute value is 18.
            NOTE: Only current value is relevant for this attribute.
            </summary>
        </member>
        <member name="P:Symbol.Barcode.RSMAPI.rsmFirmwareVersion.CurrentValue">
            <summary>
            Provides the current value of the attribute.
            Scanner’s operating system version.
            </summary>
        </member>
        <member name="P:Symbol.Barcode.RSMAPI.rsmFirmwareVersion.FactoryDefaultValue">
            <summary>
            Provides the factory default value of the attribute.
            </summary>
        </member>
        <member name="P:Symbol.Barcode.RSMAPI.rsmFirmwareVersion.CustomDefaultValue">
            <summary>
            Provides the custom default value of the attribute.
            </summary>
        </member>
        <member name="T:Symbol.Barcode.RSMAPI.rsmDeviceClass">
            <summary>
            Provides access to the device class of the system. The maximum length of the attribute value is 18.
            </summary>
        </member>
        <member name="P:Symbol.Barcode.RSMAPI.rsmDeviceClass.CurrentValue">
            <summary>
            Provides the current value of the attribute.
            The device class of the system.
            </summary>
        </member>
        <member name="P:Symbol.Barcode.RSMAPI.rsmDeviceClass.FactoryDefaultValue">
            <summary>
            Provides the factory default value of the attribute.
            </summary>
        </member>
        <member name="P:Symbol.Barcode.RSMAPI.rsmDeviceClass.CustomDefaultValue">
            <summary>
            Provides the custom default value of the attribute.
            </summary>
        </member>
        <member name="T:Symbol.Barcode.RSMAPI.rsmBatteryStatus">
            <summary>
            Provides access to the battery status.
            </summary>
        </member>
        <member name="P:Symbol.Barcode.RSMAPI.rsmBatteryStatus.CurrentValue">
            <summary>
            Provides the current value of the attribute.
            Indicates the status of Battery in the device:
            0- unknown
            1- Full
            2- Medium
            3- Empty
            4- Charging – full rate
            5- Charging – half rate
            6- Charging – Trickle
            7- Discharging – Battery Cycle in progress
            </summary>
        </member>
        <member name="P:Symbol.Barcode.RSMAPI.rsmBatteryStatus.FactoryDefaultValue">
            <summary>
            Provides the factory default value of the attribute.
            </summary>
        </member>
        <member name="P:Symbol.Barcode.RSMAPI.rsmBatteryStatus.CustomDefaultValue">
            <summary>
            Provides the custom default value of the attribute.
            </summary>
        </member>
        <member name="T:Symbol.Barcode.RSMAPI.rsmHardwareVersion">
            <summary>
            Provides access to the hardware version.
            NOTE: HardwareVersion attribute will not be supported in the future. Therefore, this class will be deprecated in future releases.
            </summary>
        </member>
        <member name="P:Symbol.Barcode.RSMAPI.rsmHardwareVersion.CurrentValue">
            <summary>
            Provides the current value of the attribute.
            </summary>
        </member>
        <member name="P:Symbol.Barcode.RSMAPI.rsmHardwareVersion.FactoryDefaultValue">
            <summary>
            Provides the factory default value of the attribute.
            </summary>
        </member>
        <member name="P:Symbol.Barcode.RSMAPI.rsmHardwareVersion.CustomDefaultValue">
            <summary>
            Provides the custom default value of the attribute.
            </summary>
        </member>
        <member name="T:Symbol.Barcode.RSMAPI.rsmBatteryCapacity">
            <summary>
            Provides access to the battery capacity.
            </summary>
        </member>
        <member name="P:Symbol.Barcode.RSMAPI.rsmBatteryCapacity.CurrentValue">
            <summary>
            Provides the current value of the attribute.
            Capacity of the Battery:
            0-100 (in percent)
            9999 if error (i.e. try to get capacity when no battery)
            </summary>
        </member>
        <member name="P:Symbol.Barcode.RSMAPI.rsmBatteryCapacity.FactoryDefaultValue">
            <summary>
            Provides the factory default value of the attribute.
            </summary>
        </member>
        <member name="P:Symbol.Barcode.RSMAPI.rsmBatteryCapacity.CustomDefaultValue">
            <summary>
            Provides the custom default value of the attribute.
            </summary>
        </member>
        <member name="T:Symbol.Barcode.RSMAPI.rsmBatteryID">
            <summary>
            Provides access to the battery ID.
            </summary>
        </member>
        <member name="P:Symbol.Barcode.RSMAPI.rsmBatteryID.CurrentValue">
            <summary>
            Provides the current value of the attribute.
            Battery ID:
            0 – Simple
            1 – Double
            2 – Cabled
            9999 – Error
            </summary>
        </member>
        <member name="P:Symbol.Barcode.RSMAPI.rsmBatteryID.FactoryDefaultValue">
            <summary>
            Provides the factory default value of the attribute.
            </summary>
        </member>
        <member name="P:Symbol.Barcode.RSMAPI.rsmBatteryID.CustomDefaultValue">
            <summary>
            Provides the custom default value of the attribute.
            </summary>
        </member>
        <member name="T:Symbol.Barcode.RSMAPI.rsmScanlineWidth">
            <summary>
            Provides access to the pick list mode.
            </summary>
        </member>
        <member name="P:Symbol.Barcode.RSMAPI.rsmScanlineWidth.CurrentValue">
            <summary>
            Provides the current value of the attribute.
            Enables or disables the Pick list mode.
            0 – Disable
            2 – Enable
            </summary>
        </member>
        <member name="P:Symbol.Barcode.RSMAPI.rsmScanlineWidth.FactoryDefaultValue">
            <summary>
            Provides the factory default value of the attribute.
            </summary>
        </member>
        <member name="P:Symbol.Barcode.RSMAPI.rsmScanlineWidth.CustomDefaultValue">
            <summary>
            Provides the custom default value of the attribute.
            </summary>
        </member>
        <member name="T:Symbol.Barcode.RSMAPI.rsmBluetoothDisconnect">
            <summary>
            Provides access to the Bluetooth disconnect command 
            </summary>
        </member>
        <member name="P:Symbol.Barcode.RSMAPI.rsmBluetoothDisconnect.CurrentValue">
            <summary>
            Command the scanner to disconnect from mobile computer.
            0 - disconnect
            NOTE: Since this property is used for issuing a command, this is a set-only property.
            </summary>
        </member>
        <member name="T:Symbol.Barcode.RSMAPI.rsmBluetoothUnPair">
            <summary>
            Provides access to the Bluetooth unpair command 
            </summary>
        </member>
        <member name="P:Symbol.Barcode.RSMAPI.rsmBluetoothUnPair.CurrentValue">
            <summary>
            Commands scanner to unpair from mobile computer
            0 - unpair
            NOTE: Since this property is used for issuing a command, this is a set-only property.
            </summary>
        </member>
        <member name="T:Symbol.Barcode.RSMAPI.rsmGoodScansDelay">
            <summary>
            Provides access to the delay between proximity continuous good scans.
            </summary>
        </member>
        <member name="P:Symbol.Barcode.RSMAPI.rsmGoodScansDelay.CurrentValue">
            <summary>
            Provides the current value of the attribute.
            Defines the delay between proximity continuous good scans in msec.
            Values are betwwen 0 and 150 (* 100 msec)
            0 –  No delay
            1 – 100 msec 
            2 – 200 msec
            ..
            ..
            150 – 15000 msec
            </summary>
        </member>
        <member name="P:Symbol.Barcode.RSMAPI.rsmGoodScansDelay.FactoryDefaultValue">
            <summary>
            Provides the factory default value of the attribute.
            </summary>
        </member>
        <member name="P:Symbol.Barcode.RSMAPI.rsmGoodScansDelay.CustomDefaultValue">
            <summary>
            Provides the custom default value of the attribute.
            </summary>
        </member>
        <member name="T:Symbol.Barcode.RSMAPI.rsmProximityContinuousEnable">
            <summary>
            Provides access to the proximity continuous feature.
            </summary>
        </member>
        <member name="P:Symbol.Barcode.RSMAPI.rsmProximityContinuousEnable.CurrentValue">
            <summary>
            Provides the current value of the attribute.
            Enable/disable proximity continuous feature.
            </summary>
        </member>
        <member name="P:Symbol.Barcode.RSMAPI.rsmProximityContinuousEnable.FactoryDefaultValue">
            <summary>
            Provides the factory default value of the attribute.
            </summary>
        </member>
        <member name="P:Symbol.Barcode.RSMAPI.rsmProximityContinuousEnable.CustomDefaultValue">
            <summary>
            Provides the custom default value of the attribute.
            </summary>
        </member>
        <member name="T:Symbol.Barcode.RSMAPI.rsmExclusiveCode128Enable">
            <summary>
            Provides access to the feature for ignoring Code 128’s beginning with 420 and 421
            </summary>
        </member>
        <member name="P:Symbol.Barcode.RSMAPI.rsmExclusiveCode128Enable.CurrentValue">
            <summary>
            Provides the current value of the attribute.
            Enable/Disable the feature for ignoring Code 128’s beginning with 420 and 421
            </summary>
        </member>
        <member name="P:Symbol.Barcode.RSMAPI.rsmExclusiveCode128Enable.FactoryDefaultValue">
            <summary>
            Provides the factory default value of the attribute.
            </summary>
        </member>
        <member name="P:Symbol.Barcode.RSMAPI.rsmExclusiveCode128Enable.CustomDefaultValue">
            <summary>
            Provides the custom default value of the attribute.
            </summary>
        </member>
        <member name="T:Symbol.Barcode.RSMAPI.rsmDisableGoodDecodeLEDBeep">
            <summary>
            Provides access to disable the good decode Green LED and beep on scanner
            </summary>
        </member>
        <member name="P:Symbol.Barcode.RSMAPI.rsmDisableGoodDecodeLEDBeep.CurrentValue">
            <summary>
            Provides the current value of the attribute.
            True  - Disables the good decode Green LED and beep on scanner
            False - Enables the good decode Green LED and beep on scanner
            </summary>
        </member>
        <member name="P:Symbol.Barcode.RSMAPI.rsmDisableGoodDecodeLEDBeep.FactoryDefaultValue">
            <summary>
            Provides the factory default value of the attribute.
            </summary>
        </member>
        <member name="P:Symbol.Barcode.RSMAPI.rsmDisableGoodDecodeLEDBeep.CustomDefaultValue">
            <summary>
            Provides the custom default value of the attribute.
            </summary>
        </member>
        <member name="T:Symbol.Barcode.RSMAPI.rsmForcePairingSave">
            <summary>
            Provides access to the feature to save the Bluetooth address when pairing fails. This attribute is permanently saved.
            </summary>
        </member>
        <member name="P:Symbol.Barcode.RSMAPI.rsmForcePairingSave.CurrentValue">
            <summary>
            Provides the current value of the attribute.
            True - Saves Bluetooth address when pairing fails
            False - Does not save Bluetooth address when pairing fails
            This attribute is saved permanently.
            </summary>
        </member>
        <member name="P:Symbol.Barcode.RSMAPI.rsmForcePairingSave.FactoryDefaultValue">
            <summary>
            Provides the factory default value of the attribute.
            </summary>
        </member>
        <member name="P:Symbol.Barcode.RSMAPI.rsmForcePairingSave.CustomDefaultValue">
            <summary>
            Provides the custom default value of the attribute.
            </summary>
        </member>
        <member name="T:Symbol.Barcode.ATTRIB_NUMBER">
            <summary>
            Provides the list of possible attribute numbers.
            </summary>
        </member>
        <member name="F:Symbol.Barcode.ATTRIB_NUMBER.MODEL_NUMBER">
            <summary>
            Model Number
            </summary>
        </member>
        <member name="F:Symbol.Barcode.ATTRIB_NUMBER.SERIAL_NUMBER">
            <summary>
            Serial Number
            </summary>
        </member>
        <member name="F:Symbol.Barcode.ATTRIB_NUMBER.DATE_OF_MANUFACTURE">
            <summary>
            Date of Device Manufacture
            </summary>
        </member>
        <member name="F:Symbol.Barcode.ATTRIB_NUMBER.DATE_OF_SERVICE">
            <summary>
            Date of last repair within a Motorola Authorized repair facility
            </summary>
        </member>
        <member name="F:Symbol.Barcode.ATTRIB_NUMBER.BT_ADDR">
            <summary>
            Unique Bluetooth Address of the scanner device
            </summary>
        </member>
        <member name="F:Symbol.Barcode.ATTRIB_NUMBER.BT_AUTHENTICATION">
            <summary>
            Whether Bluetooth authentication is required or not.
            </summary>
        </member>
        <member name="F:Symbol.Barcode.ATTRIB_NUMBER.BT_ENCRYPTION">
            <summary>
            Whether encryption over Bluetooth is required or not.
            </summary>
        </member>
        <member name="F:Symbol.Barcode.ATTRIB_NUMBER.BT_PINCODE">
            <summary>
            Pincode used when Bluetooth Authentication is enabled.
            </summary>
        </member>
        <member name="F:Symbol.Barcode.ATTRIB_NUMBER.RECONNECT_ATTEMPTS">
            <summary>
            Duration for which the scanner tries to reestablish the connection, 
            if it goes out of range.
            </summary>
        </member>
        <member name="F:Symbol.Barcode.ATTRIB_NUMBER.BEEP_ON_RECON_ATTEMPT">
            <summary>
            Indicates whether the scanner should beep during reconnection attemps.
            </summary>
        </member>
        <member name="F:Symbol.Barcode.ATTRIB_NUMBER.HID_AUTO_RECON">
            <summary>
            Auto-reconnect behavior of the scanner when HID connection is lost.
            </summary>
        </member>
        <member name="F:Symbol.Barcode.ATTRIB_NUMBER.BT_FRIENDLY_NAME">
            <summary>
            Friendly name displayed by Bluetooth remote devices.
            </summary>
        </member>
        <member name="F:Symbol.Barcode.ATTRIB_NUMBER.PIN_CODE_TYPE">
            <summary>
            Indicates whether to prompt the user for PIN code or use PIN code stored in memory.
            </summary>
        </member>
        <member name="F:Symbol.Barcode.ATTRIB_NUMBER.BT_INQUIRY_MODE">
            <summary>
            Whether general or limited inquiry mode is used.
            </summary>
        </member>
        <member name="F:Symbol.Barcode.ATTRIB_NUMBER.EXCLUSIVE_CODE128_EN">
            <summary>
            Feature for ignoring Code 128’s beginning with 420 and 421 enable/ disable.
            </summary>
        </member>
        <member name="F:Symbol.Barcode.ATTRIB_NUMBER.MEMS_ENABLE">
            <summary>
            Used to enable/disable mems feature
            </summary>
        </member>
        <member name="F:Symbol.Barcode.ATTRIB_NUMBER.PROXIMITY_ENABLE">
            <summary>
            Used to enable/disable proximity feature
            </summary>
        </member>
        <member name="F:Symbol.Barcode.ATTRIB_NUMBER.PROXIMITY_DISTANCE">
            <summary>
            Specifies Proximity distance.
            </summary>
        </member>
        <member name="F:Symbol.Barcode.ATTRIB_NUMBER.PAGING_ENABLE">
            <summary>
            Used to enable/disable paging
            </summary>
        </member>
        <member name="F:Symbol.Barcode.ATTRIB_NUMBER.PAGING_BEEP_SEQ">
            <summary>
            Paging beep sequence.
            </summary>
        </member>
        <member name="F:Symbol.Barcode.ATTRIB_NUMBER.LOW_BATTERY_IND_EN">
            <summary>
            Used to enable/disable low battery indication.
            </summary>
        </member>
        <member name="F:Symbol.Barcode.ATTRIB_NUMBER.SCAN_TRIG_WAKEUP_EN">
            <summary>
            Enables/Disables Scan trigger as a wakeup source for the device from low power.
            </summary>
        </member>
        <member name="F:Symbol.Barcode.ATTRIB_NUMBER.BLUETOOTH_AUTO_RECON">
            <summary>
            Defines Bluetooth reconnection scheme.
            </summary>
        </member>
        <member name="F:Symbol.Barcode.ATTRIB_NUMBER.LOW_BATTERY_IND_CYCLE">
            <summary>
            Low battery indication cycle time in seconds.
            </summary>
        </member>
        <member name="F:Symbol.Barcode.ATTRIB_NUMBER.PREFERRED_WIRELESSHOST">
            <summary>
            Preferred Bluetooth wireless host:
            </summary>
        </member>
        <member name="F:Symbol.Barcode.ATTRIB_NUMBER.PROXIMITY_CONTINUOUS_EN">
            <summary>
            Proximity continuous mode enable / disable
            </summary>
        </member>
        <member name="F:Symbol.Barcode.ATTRIB_NUMBER.GOOD_SCANS_DELAY">
            <summary>
            Delay between proximity continuous good scans in msec: Values 0 -150 (* 100 msec)
            0 –  No delay
            1 – 100 msec 
            2 – 200 msec
            ..
            ..
            150 – 15000 msec
            </summary>
        </member>
        <member name="F:Symbol.Barcode.ATTRIB_NUMBER.PAGING_ACTIVATE">
            <summary>
            Start /Stop paging to scanner
            </summary>
        </member>
        <member name="F:Symbol.Barcode.ATTRIB_NUMBER.FIRM_VERSION">
            <summary>
            Scanner’s operating system version.
            </summary>
        </member>
        <member name="F:Symbol.Barcode.ATTRIB_NUMBER.HARDWARE_VERSION">
            <summary>
            Hardware version of the device.
            </summary>
        </member>
        <member name="F:Symbol.Barcode.ATTRIB_NUMBER.DEVICE_CLASS">
            <summary>
            The device class of the system.
            </summary>
        </member>
        <member name="F:Symbol.Barcode.ATTRIB_NUMBER.BATTERY_STATUS">
            <summary>
            Indicates the status of Battery in the device.
            </summary>
        </member>
        <member name="F:Symbol.Barcode.ATTRIB_NUMBER.BATTERY_CAPACITY">
            <summary>
            Capacity of the Battery.
            </summary>
        </member>
        <member name="F:Symbol.Barcode.ATTRIB_NUMBER.BATTERY_ID">
            <summary>
            Battery ID.
            </summary>
        </member>
        <member name="F:Symbol.Barcode.ATTRIB_NUMBER.SCANLINE_WIDTH">
            <summary>
            This attribute defines the laser scan line width (Pick list).
            </summary>
        </member>
        <member name="F:Symbol.Barcode.ATTRIB_NUMBER.BT_DISCONNECT">
            <summary>
            Command scanner to disconnect from terminal.
            </summary>
        </member>
        <member name="F:Symbol.Barcode.ATTRIB_NUMBER.BT_UNPAIR">
            <summary>
            Command scanner to unpair from terminal.
            </summary>
        </member>
        <member name="F:Symbol.Barcode.ATTRIB_NUMBER.DISABLE_GOOD_DECODE_LED_BEEP">
            <summary>
            This attribute disables good decode LED and beep on scanner.
            </summary>
        </member>
        <member name="F:Symbol.Barcode.ATTRIB_NUMBER.FORCE_PAIRING_SAVE">
            <summary>
            Forces Bluetooth address saving when pairing fails
            </summary>
        </member>
        <member name="T:Symbol.Barcode.ATTRIB_TYPE">
            <summary>
            Provides the list of possible attribute types.
            </summary>
        </member>
        <member name="F:Symbol.Barcode.ATTRIB_TYPE.UNTYPED">
            <summary>
            Attribute type is unknown
            </summary>
        </member>
        <member name="F:Symbol.Barcode.ATTRIB_TYPE.BYTE">
            <summary>
            Attribute type is BYTE
            </summary>
        </member>
        <member name="F:Symbol.Barcode.ATTRIB_TYPE.CHAR">
            <summary>
            Attribute type is CHAR 
            </summary>
        </member>
        <member name="F:Symbol.Barcode.ATTRIB_TYPE.FLAG">
            <summary>
            Attribute type is FLAG 
            </summary>
        </member>
        <member name="F:Symbol.Barcode.ATTRIB_TYPE.USHORT">
            <summary>
            Attribute type is USHORT 
            </summary>
        </member>
        <member name="F:Symbol.Barcode.ATTRIB_TYPE.SHORT">
            <summary>
            Attribute type is SHORT 
            </summary>
        </member>
        <member name="F:Symbol.Barcode.ATTRIB_TYPE.ULONG">
            <summary>
            Attribute type is ULONG 
            </summary>
        </member>
        <member name="F:Symbol.Barcode.ATTRIB_TYPE.LONG">
            <summary>
            Attribute type is LONG 
            </summary>
        </member>
        <member name="F:Symbol.Barcode.ATTRIB_TYPE.ARRAY">
            <summary>
            Attribute type is array 
            </summary>
        </member>
        <member name="F:Symbol.Barcode.ATTRIB_TYPE.STRING">
            <summary>
            Attribute type is string 
            </summary>
        </member>
        <member name="F:Symbol.Barcode.ATTRIB_TYPE.ACTION">
            <summary>
            Attribute type is action (byte)
            </summary>
        </member>
        <member name="T:Symbol.Barcode.Info">
            <summary>
            The Barcode.Info Class contains information about the scanner.
            </summary>
            <remarks>
            The Info class is normally constructed by the Reader class and can be accessed
            using the Reader.Info property.
            </remarks>
        </member>
        <member name="M:Symbol.Barcode.Info.#ctor(Symbol.Barcode.Reader)">
            <summary>
            The Info class constructor is not normally called.
            </summary>
            <param name="r">A Reader object from which the information will be obtained.</param>
        </member>
        <member name="P:Symbol.Barcode.Info.IsEnabled">
            <summary>
            Specifies whether or not the Reader is enabled.
            </summary>
            <value>
            A boolean flag which when true indicates that the Reader is enabled, otherwise it
            is disabled.
            </value>
        </member>
        <member name="P:Symbol.Barcode.Info.IsSimulating">
            <summary>
            Read only property returns whether or not the reader is in simulation mode.
            </summary>
            <remarks>
            Simulation mode is used primarily by the class libraries to allow support for 
            programming using the "stock" emulators provided by Microsoft. While in
            simulation mode scans are faked so that programs can be tested and developed.
            </remarks>
            <value>
            A boolean flag which when true indicates that the Reader is in simulation mode, 
            otherwise actual hardware has been targeted.
            </value>
        </member>
        <member name="P:Symbol.Barcode.Info.PendingReads">
            <summary>
            Returns the number of pending reads.
            </summary>
            <remarks>
            A pending read is a read that has been submitted but has not yet completed. To 
            submit a pending read call the <see cref="M:Symbol.Barcode.Actions.Read(Symbol.Generic.ReaderData)"/> method. A read
            completes by either a successful decode, a failed decode, or due to a cancellation of 
            the read.
            </remarks>
            <value>
            An integer which returns the number of current pending reads.
            </value>
        </member>
        <member name="P:Symbol.Barcode.Info.SoftTrigger">
            <summary>
            The state of the soft-trigger.
            </summary>
            <remarks>
            A soft-trigger is a trigger that has been initiated via software. Unlike a hard
            trigger initiated by a physical button press.
            </remarks>
            <value>
            A boolean flag which when true sets the state of the soft-trigger to on. If a 
            read is pending then the laser will turn on. When the flag is set to false the 
            laser will turn off.
            </value>
        </member>
        <member name="P:Symbol.Barcode.Info.Decoders">
            <summary>
            The list of decoders supported by the scan engine.
            </summary>
        </member>
        <member name="P:Symbol.Barcode.Info.IsBeamWidthSupported">
            <summary>
            Flag telling whether the device supports narrow beam width.
            NOTE: This property is being deprecated. It will be removed in a future verison.
            Please use SupportedBeamWidths property instead.
            </summary>
        </member>
        <member name="P:Symbol.Barcode.Info.IsAimModeSupported">
            <summary>
            Flag telling whether the device supports aiming.
            </summary>
        </member>
        <member name="P:Symbol.Barcode.Info.IsDirectionSupported">
            <summary>
            Flag telling whether the device supports scan direction reporting.
            </summary>
        </member>
        <member name="P:Symbol.Barcode.Info.IsFeedbackSupported">
            <summary>
            Flag telling whether the device supports remote (non-local) feedback.
            </summary>
        </member>
        <member name="P:Symbol.Barcode.Info.SupportedImageFormats">
            <summary>
            The image formats supported.
            </summary>
        </member>
        <member name="P:Symbol.Barcode.Info.MaxImageRect">
            <summary>
            Maximum image cropping rectangle.
            </summary>
        </member>
        <member name="P:Symbol.Barcode.Info.DPMCapable">
            <summary>
            Flag describing whether the scan engine is capable of decoding DPM bar codes.
            </summary>
        </member>
        <member name="P:Symbol.Barcode.Info.ViewFinderCapable">
            <summary>
            Flag describing whether the scan engine supports a video viewfinder. 
            </summary>
        </member>
        <member name="P:Symbol.Barcode.Info.AdaptiveScanning">
            <summary>
            Flag describing whether this engine supports the adaptive scanning feature.
            NOTE: The adaptive scanning feature can be enabled/disabled using the Reader.ReaderParameters.ReaderSpecific.LaserSpecific.AdaptiveScanning property.
            Refer to this property's help page for important information about the Adaptive Scanning feature.
            </summary>
        </member>
        <member name="P:Symbol.Barcode.Info.SupportedBeamWidths">
            <summary>
            Provides a list of supported beam widths. The beam width parameter for the laser scanner can be modified using the Reader.ReaderParameters.ReaderSpecific.LaserSpecific.BeamWidth property.
            The beam width feature is not supported on Imager scanners.
            <para>
            NOTE: All laser scanners support BEAM_WIDTH.NORMAL by default. This array can be used by the application to query any other beam widths it may support. 
            </para>
            </summary>
        </member>
        <member name="T:Symbol.Barcode.Reader">
            <summary>
            The Barcode.Reader class can be considered the primary class of the Barcode class
            library. It provides access to actions, information, and parameters.
            </summary>
            <remarks>
            The Reader class provides all the capabilities of the scanner. It is normally constructed
            by having a <see cref="T:Symbol.Barcode.Device"/> object passed in as a parameter. Once created, 
            actions can be performed on the reader using object returned from the 
            <see cref="P:Symbol.Barcode.Reader.Actions"/> property. One of the most commonly used actions is the 
            submitting of a <see cref="M:Symbol.Barcode.Actions.Read(Symbol.Generic.ReaderData)"/> request. To perform a Read you will need to
            contruct a <see cref="T:Symbol.Barcode.ReaderData"/> object and pass it as a parameter to the 
            Read method. You will then need to attach to the <see cref="E:Symbol.Barcode.Reader.ReadNotify"/> event
            to be notified when a read completes.<para> Decoder and scan parameter objects can be
            access using the Reader.Decoders and Reader.Parameters properties, respectively.
            </para><para>The Dispose method of the Reader should be called when the object is no
            longer needed by the application.</para>
            </remarks>
        </member>
        <member name="M:Symbol.Barcode.Reader.#ctor">
            <summary>
            Default Reader constructor creates an instance using the first available device.
            </summary>
            <remarks>
            The first available device may be a Simulated device if no scanning hardware is
            found. To be sure that the correct device is targeted during the instantiation of 
            the Reader, it is suggested that you specify the Device fully using the other
            Reader constructors.
            </remarks>
        </member>
        <member name="M:Symbol.Barcode.Reader.#ctor(System.String)">
            <summary>
            Reader constructor with settable device name.
            </summary>
            <param name="sReaderName">A string that contains the device name of the hardware
            to access. This is normally of the form "SCNx:", where the x is the number of the
            scanner.</param>
        </member>
        <member name="M:Symbol.Barcode.Reader.#ctor(Symbol.Generic.Device)">
            <summary>
            Reader constructor with settable Barcode.Device.
            </summary>
            <remarks>
            This is the recommended constructor to use when creating a Reader object.
            </remarks>
            <param name="d">A Generic.Device or <see cref="T:Symbol.Barcode.Device"/> object that targets a particular
            scanner.</param>
        </member>
        <member name="M:Symbol.Barcode.Reader.Finalize">
            <summary>
            Allows a Reader to attempt to free resources and perform other cleanup 
            operations before it is reclaimed by garbage collection.
            </summary>
        </member>
        <member name="M:Symbol.Barcode.Reader.Dispose">
            <summary>
            Frees up all resources used by the Reader, including any objects it has created. 
            It is strongly recommended that this method be called when the Reader is no longer
            needed by the application.
            </summary>
        </member>
        <member name="M:Symbol.Barcode.Reader.GetNextReaderData">
            <summary>
            Gets the next ReaderData object from the read completion queue.
            </summary>
            <remarks>
            The GetNextReaderData method is most commonly called by methods that have been
            invoked via the ReadNotify event. This method provides access to the ReaderData
            object of completed reads (whether the reads have been completed successfully or
            not should be determined from the status information provided by the ReaderData
            object).
            </remarks>
            <returns>A <see cref="T:Symbol.Barcode.ReaderData"/> object that has completed.</returns>
        </member>
        <member name="M:Symbol.Barcode.Reader.GetNextStatus">
            <summary>
            Gets the next BarcodeStatus object from the scan status event queue.
            </summary>
            <remarks>
            The GetNextStatus method is most commonly called by methods that have been
            invoked via the StatusNotify event. This method provides access to the BarcodeStatus
            object that contains information about the scan event that has occured.
            </remarks>
            <returns>A <see cref="T:Symbol.Barcode.BarcodeStatus"/> object with scan event
            information.</returns>
        </member>
        <member name="M:Symbol.Barcode.Reader.ToString">
            <summary>
            String representation of class.
            </summary>
            <returns>The device name of the Reader in string form.</returns>
        </member>
        <member name="M:Symbol.Barcode.Reader.Save(System.Boolean)">
            <summary>
            Saves the parameters so that they can be restored later if necessary.
            </summary>
            <param name="bRecurseIntoSubClasses">When "true", the save procedure
            will recurse into the subclasses and save their parameters as well. 
            (i.e. perform a deep copy).
            </param>
            <returns>
            An object that represents the saved parameters.
            </returns>
        </member>
        <member name="M:Symbol.Barcode.Reader.Restore(System.Object)">
            <summary>
            Restores saved parameters. The object passed as
            a parameter should be obtained using the <see cref="M:Symbol.Barcode.Reader.Save(System.Boolean)"/> method.
            </summary>
            <param name="SaveInfo">An object obtained that contains the saved state</param>
        </member>
        <member name="E:Symbol.Barcode.Reader.ReadNotify">
            <summary>
            Attach to this notification event to be called back when a read completes.
            </summary>
            <value>
            A <see cref="T:System.EventHandler"/> delegate of the method that will be invoked
            when a read completes.
            </value>
        </member>
        <member name="E:Symbol.Barcode.Reader.StatusNotify">
            <summary>
            Attach to this notification event to be called back when a scan event occurs.
            </summary>
            <value>
            A <see cref="T:System.EventHandler"/> delegate of the method that will be invoked
            when a scan event occurs.
            </value>
        </member>
        <member name="P:Symbol.Barcode.Reader.Actions">
            <summary>
            Provides access to actions that can be performed on the Reader.
            </summary>
            <value>A <see cref="T:Symbol.Barcode.Actions"/> object.</value>
        </member>
        <member name="P:Symbol.Barcode.Reader.Info">
            <summary>
            Provides access to information about the Reader.
            </summary>
            <value> A <see cref="T:Symbol.Barcode.Info"/> object.</value>
        </member>
        <member name="P:Symbol.Barcode.Reader.Version">
            <summary>
            Provides access to version information.
            </summary>
            <value> A <see cref="T:Symbol.Barcode.Version"/> object.</value>
        </member>
        <member name="P:Symbol.Barcode.Reader.Text">
            <summary>
            Returns the name of the device being used for the Reader.
            </summary>
            <value>String that contains the Device name</value>
        </member>
        <member name="P:Symbol.Barcode.Reader.Decoders">
            <summary>
            Provides access to Decoder-specific parameters so that they can be viewed
            and modified.
            </summary>
            <value>
            A <see cref="T:Symbol.Barcode.DecoderAPI"/> object that contains the decoders.
            </value>
        </member>
        <member name="P:Symbol.Barcode.Reader.Parameters">
            <summary>
            Provides access to scan-specific parameters so that they can be viewed
            and modified.
            </summary>
            <value>
            A <see cref="T:Symbol.Barcode.ScanParamsAPI"/> object that contains the scan parameters.
            </value>
        </member>
        <member name="P:Symbol.Barcode.Reader.ReaderParameters">
            <summary>
            Provides access to Reader-specific parameters so that they can be viewed
            and modified.
            </summary>
            <value>
            A <see cref="T:Symbol.Barcode.ReaderParams"/> object that contains the reader parameters.
            </value>
        </member>
        <member name="P:Symbol.Barcode.Reader.UPCEAN">
            <summary>
            Provides access to UPC/EAN-specific parameters so that they can be viewed
            and modified.
            </summary>
            <value>
            A <see cref="T:Symbol.Barcode.UPCEANParamsAPI"/> object that contains the parameters.
            </value>
        </member>
        <member name="P:Symbol.Barcode.Reader.RSM">
            <summary>
            Provides access to RSM (Remote Scanner Management) attributes so that they can be viewed
            and modified. The Reader must be enabled before accessing RSM attributes.
            NOTE: The RSM feature can only be used when using remote scanner devices such as RS507.
            </summary>
        </member>
        <member name="P:Symbol.Barcode.Reader.InterfaceParameters">
            <summary>
            Provides access to Interface-specific parameters so that they can be viewed
            and modified.
            
            NOTE: Beginning with the ScanMDD file version 6.01, the InterfaceParameters object can be accessed before or after enabling the 
            Reader object. In the earlier versions of ScanMDD, the InterfaceParameters object can only be accessed after enabling the Reader object. 
            The ScanMDD version can be retrieved using the ControlPanel tool available on the support central website.
            </summary>
        </member>
        <member name="T:Symbol.Barcode.Device">
            <summary>
            The Symbol.Barcode.Device class provides information about to all the
            available scanners in the system.
            </summary>
            <remarks>
            The Symbol.Barcode.Device class provides an excellent starting point for applications
            that wish to gain more information about all the scanners available. The
            AvailableDevices property provides a static Device array with each entry mapping to
            available scanning hardware. Each entry contains information about the hardware and 
            can also be used to contruct a <see cref="T:Symbol.Barcode.Reader"/> object. This is the 
            recommended method for instantiating Reader objects.
            </remarks>
        </member>
        <member name="M:Symbol.Barcode.Device.#ctor">
            <summary>
            Default device class constructor.
            </summary>
            <remarks>
            By default, a Barcode.Device object will be contructed with the "SCN1:" scanner
            targeted. Its friendly name will be "Default Reader".
            </remarks>
        </member>
        <member name="M:Symbol.Barcode.Device.#ctor(System.String)">
            <summary>
            Device class constructor with settable device name.
            </summary>
            <remarks>
            A Barcode.Device object will be contructed with the given Device Name. Its 
            friendly name will be "Explicit Reader".
            </remarks>
            <param name="sDeviceName">The name of the device (should be in the form "SCNx:",
            where the x is the number of the scanner).</param>
        </member>
        <member name="M:Symbol.Barcode.Device.#ctor(System.String,System.String)">
            <summary>
            Device class constructor with settable device name and friendly name.
            </summary>
            <remarks>
            A Barcode.Device object will be contructed with the given Device Name and Friendly
            Name.
            </remarks>
            <param name="sDeviceName">The name of the device (should be in the form "SCNx:",
            where the x is the number of the scanner).</param>
            <param name="sFriendlyName">A friendly name for the device. Can be any string.</param>
        </member>
        <member name="P:Symbol.Barcode.Device.Title">
            <summary>
            Static title of the barcode device class.
            </summary>
            <value>
            A String that returns "Barcode".
            </value>
        </member>
        <member name="P:Symbol.Barcode.Device.AvailableDevices">
            <summary>
            The AvailableDevices property can be used to obtain a list of all available 
            hardware for use with the barcode classes.
            </summary>
            <remarks>
            The AvailableDevices property provides a static Device array with each entry
            mapping to available scanning hardware. Each entry contains information about
            the hardware and can also be used to contruct a <see cref="T:Symbol.Barcode.Reader"/>
            object. This is the recommended method for instantiating Reader objects.
            </remarks>
            <value>
            A <see cref="T:Symbol.Barcode.Device"/> array that represents all available scanning hardware.
            </value>
        </member>
        <member name="P:Symbol.Barcode.Device.DeviceType">
            <summary>
            Provides the scanner device type information. This information is retrieved when the device is enumerated using AvailableDevices[] property.
            </summary>
        </member>
        <member name="T:Symbol.Barcode.DeviceType">
            <summary>
            Provides the scanner device type information. This information is retrieved when the device is enumerated using AvailableDevices[] property.
            </summary>
        </member>
        <member name="P:Symbol.Barcode.DeviceType.ScannerType">
            <summary>
            Specifies the type of scanner.
            </summary>
        </member>
        <member name="P:Symbol.Barcode.DeviceType.TransportType">
            <summary>
            Specifies the transport type used by the scanner.
            </summary>
        </member>
        <member name="T:Symbol.Barcode.DecoderTypes">
            <summary>
            Enumeration of all barcode symbologies supported. Not all scanner
            hardware supports all symbologies, refer to your hardware manual
            for more information. 
            </summary>
        </member>
        <member name="F:Symbol.Barcode.DecoderTypes.UPCE0">
            <summary>
            The UPC-E0 symbology.
            </summary>
        </member>
        <member name="F:Symbol.Barcode.DecoderTypes.UPCE1">
            <summary>
            The UPC-E1 symbology.
            </summary>
        </member>
        <member name="F:Symbol.Barcode.DecoderTypes.UPCA">
            <summary>
            The UPC-A symbology.
            </summary>
        </member>
        <member name="F:Symbol.Barcode.DecoderTypes.MSI">
            <summary>
            The MSI symbology.
            </summary>
        </member>
        <member name="F:Symbol.Barcode.DecoderTypes.EAN8">
            <summary>
            The EAN-8 symbology.
            </summary>
        </member>
        <member name="F:Symbol.Barcode.DecoderTypes.EAN13">
            <summary>
            The EAN-13 symbology.
            </summary>
        </member>
        <member name="F:Symbol.Barcode.DecoderTypes.CODABAR">
            <summary>
            The CODABAR symbology.
            </summary>
        </member>
        <member name="F:Symbol.Barcode.DecoderTypes.CODE39">
            <summary>
            The CODE-39 symbology.
            </summary>
        </member>
        <member name="F:Symbol.Barcode.DecoderTypes.D2OF5">
            <summary>
            The Discrete 2 of 5 symbology.
            </summary>
        </member>
        <member name="F:Symbol.Barcode.DecoderTypes.I2OF5">
            <summary>
            The Interleaved 2 of 5 symbology.
            </summary>
        </member>
        <member name="F:Symbol.Barcode.DecoderTypes.CODE11">
            <summary>
            The CODE-11 symbology.
            </summary>
        </member>
        <member name="F:Symbol.Barcode.DecoderTypes.CODE93">
            <summary>
            The CODE-93 symbology.
            </summary>
        </member>
        <member name="F:Symbol.Barcode.DecoderTypes.CODE128">
            <summary>
            The CODE-128 symbology.
            </summary>
        </member>
        <member name="F:Symbol.Barcode.DecoderTypes.IATA2OF5">
            <summary>
            The IATA 2 of 5 symbology.
            </summary>
        </member>
        <member name="F:Symbol.Barcode.DecoderTypes.EAN128">
            <summary>
            The EAN-128 symbology.
            </summary>
        </member>
        <member name="F:Symbol.Barcode.DecoderTypes.PDF417">
            <summary>
            The PDF 417 symbology.
            </summary>
        </member>
        <member name="F:Symbol.Barcode.DecoderTypes.ISBT128">
            <summary>
            The ISBT 128 symbology.
            </summary>
        </member>
        <member name="F:Symbol.Barcode.DecoderTypes.TRIOPTIC39">
            <summary>
            The TRIOPTIC 3 of 9 symbology.
            </summary>
        </member>
        <member name="F:Symbol.Barcode.DecoderTypes.COUPON">
            <summary>
            The COUPON CODE symbology.
            </summary>
        </member>
        <member name="F:Symbol.Barcode.DecoderTypes.BOOKLAND">
            <summary>
            The BOOKLAND EAN symbology.
            </summary>
        </member>
        <member name="F:Symbol.Barcode.DecoderTypes.MICROPDF">
            <summary>
            The MICRO PDF symbology.
            </summary>
        </member>
        <member name="F:Symbol.Barcode.DecoderTypes.CODE32">
            <summary>
            The CODE-32 symbology.
            </summary>
        </member>
        <member name="F:Symbol.Barcode.DecoderTypes.MACROPDF">
            <summary>
            The MACRO PDF symbology.
            </summary>
        </member>
        <member name="F:Symbol.Barcode.DecoderTypes.MAXICODE">
            <summary>
            The MAXICODE symbology.
            </summary>
        </member>
        <member name="F:Symbol.Barcode.DecoderTypes.DATAMATRIX">
            <summary>
            The DATAMATRIX symbology.
            </summary>
        </member>
        <member name="F:Symbol.Barcode.DecoderTypes.QRCODE">
            <summary>
            The QRCODE symbology.
            </summary>
        </member>
        <member name="F:Symbol.Barcode.DecoderTypes.MACROMICROPDF">
            <summary>
            The MACRO MICRO PDF symbology.
            </summary>
        </member>
        <member name="F:Symbol.Barcode.DecoderTypes.RSS14">
            <summary>
            The RSS-14 symbology.
            </summary>
        </member>
        <member name="F:Symbol.Barcode.DecoderTypes.RSSLIM">
            <summary>
            The RSS limited symbology.
            </summary>
        </member>
        <member name="F:Symbol.Barcode.DecoderTypes.RSSEXP">
            <summary>
            The RSS expanded symbology.
            </summary>
        </member>
        <member name="F:Symbol.Barcode.DecoderTypes.POINTER">
            <summary>
            Pointer label type.
            </summary>
        </member>
        <member name="F:Symbol.Barcode.DecoderTypes.IMAGE">
            <summary>
            Image label type.
            </summary>
        </member>
        <member name="F:Symbol.Barcode.DecoderTypes.SIGNATURE">
            <summary>
            Signature label type.
            </summary>
        </member>
        <member name="F:Symbol.Barcode.DecoderTypes.RESERVED_53">
            <summary>
            RESERVED.
            </summary>
        </member>
        <member name="F:Symbol.Barcode.DecoderTypes.WEBCODE">
            <summary>
            The Scanlet WEBCODE symbology.
            </summary>
        </member>
        <member name="F:Symbol.Barcode.DecoderTypes.CUECODE">
            <summary>
            The CUE CAT CODE symbology.
            </summary>
        </member>
        <member name="F:Symbol.Barcode.DecoderTypes.COMPOSITE_AB">
            <summary>
            The COMPOSITE AB symbology.
            </summary>
        </member>
        <member name="F:Symbol.Barcode.DecoderTypes.COMPOSITE_C">
            <summary>
            The COMPOSITE C symbology.
            </summary>
        </member>
        <member name="F:Symbol.Barcode.DecoderTypes.TLC39">
            <summary>
            The TCIF Linked CODE 39 symbology.
            </summary>
        </member>
        <member name="F:Symbol.Barcode.DecoderTypes.USPOSTNET">
            <summary>
            The US POSTNET symbology.
            </summary>
        </member>
        <member name="F:Symbol.Barcode.DecoderTypes.USPLANET">
            <summary>
            The US PLANET symbology.
            </summary>
        </member>
        <member name="F:Symbol.Barcode.DecoderTypes.UKPOSTAL">
            <summary>
            The UK POSTAL symbology.
            </summary>
        </member>
        <member name="F:Symbol.Barcode.DecoderTypes.JAPPOSTAL">
            <summary>
            The JAPANESE POSTAL symbology.
            </summary>
        </member>
        <member name="F:Symbol.Barcode.DecoderTypes.AUSPOSTAL">
            <summary>
            The AUSTRALIAN POSTAL symbology.
            </summary>
        </member>
        <member name="F:Symbol.Barcode.DecoderTypes.DUTCHPOSTAL">
            <summary>
            The DUTCH POSTAL symbology.
            </summary>
        </member>
        <member name="F:Symbol.Barcode.DecoderTypes.CANPOSTAL">
            <summary>
            The CANADIAN POSTAL symbology.
            </summary>
        </member>
        <member name="F:Symbol.Barcode.DecoderTypes.CHINESE_2OF5">
            <summary>
            The CHINESE_2OF5 symbology.
            </summary>
        </member>
        <member name="F:Symbol.Barcode.DecoderTypes.AZTEC">
            <summary>
            The AZTEC symbology
            </summary>
        </member>
        <member name="F:Symbol.Barcode.DecoderTypes.MICROQR">
            <summary>
            The MICROQR symbology
            </summary>
        </member>
        <member name="F:Symbol.Barcode.DecoderTypes.KOREAN_3OF5">
            <summary>
            Korean 3 of 5 symbology
            </summary>
        </member>
        <member name="F:Symbol.Barcode.DecoderTypes.US4STATE">
            <summary>
            US4State Symbology
            </summary>
        </member>
        <member name="F:Symbol.Barcode.DecoderTypes.US4STATE_FICS">
            <summary>
            US4STATE_FICS Symbology
            </summary>
        </member>
        <member name="F:Symbol.Barcode.DecoderTypes.MATRIX_2OF5">
            <summary>
            MATRIX_2OF5 Symbology
            </summary>
        </member>
        <member name="F:Symbol.Barcode.DecoderTypes.FIRST">
            <summary>
            The first item in the DecoderTypes enumeration. This item is 
            used during simulation mode to cycle through all symbologies.
            </summary>
        </member>
        <member name="F:Symbol.Barcode.DecoderTypes.NEXT">
            <summary>
            The next item in the DecoderTypes enumeration. This item is 
            used during simulation mode to cycle through all symbologies.
            </summary>
        </member>
        <member name="F:Symbol.Barcode.DecoderTypes.LAST">
            <summary>
            The last item in the DecoderTypes enumeration. This item is 
            used during simulation mode to cycle through all symbologies.
            </summary>
        </member>
        <member name="F:Symbol.Barcode.DecoderTypes.UNKNOWN">
            <summary>
            The symbology or labeltype is unknown.
            </summary>
        </member>
        <member name="T:Symbol.Barcode.ReaderDataTypes">
            <summary>
            Data types for the data that is read from barcode scanner.
            </summary>
        </member>
        <member name="F:Symbol.Barcode.ReaderDataTypes.Binary">
            <summary>
            The buffer of memory used to access the hardware will hold raw binary data.
            </summary>
        </member>
        <member name="F:Symbol.Barcode.ReaderDataTypes.Text">
            <summary>
            The buffer of memory used to access the hardware will hold text.
            </summary>
        </member>
        <member name="T:Symbol.Barcode.ReaderDataLengths">
            <summary>
            Length of ReaderData memory buffer.
            </summary>
        </member>
        <member name="F:Symbol.Barcode.ReaderDataLengths.DefaultText">
            <summary>
            Default for text. 55 characters in text mode, bytes in binary mode.
            </summary>
        </member>
        <member name="F:Symbol.Barcode.ReaderDataLengths.MaximumLabel">
            <summary>
            Maximum amount memory for data. 7905 character in text mode, bytes in binary mode.
            </summary>
        </member>
        <member name="T:Symbol.Barcode.States">
            <summary>
            Defines the different states of the barcode Reader.
            </summary>
        </member>
        <member name="F:Symbol.Barcode.States.BT_CONNECTION_STATE_DISCONNECTED">
            <summary>
            The Bluetooth Scanner is disconnected.
            </summary>
        </member>
        <member name="F:Symbol.Barcode.States.BT_CONNECTION_STATE_CONNECTED">
            <summary>
            The Bluetooth Scanner is connected.
            </summary>
        </member>
        <member name="F:Symbol.Barcode.States.STOPPED">
            <summary>
            Scanner is not enabled.
            </summary>
        </member>
        <member name="F:Symbol.Barcode.States.IDLE">
            <summary>
            Scanner is enabled but no reads are pending.
            </summary>
        </member>
        <member name="F:Symbol.Barcode.States.WAITING">
            <summary>
            Scanner has one or more reads pending and is waiting for trigger event.
            </summary>
        </member>
        <member name="F:Symbol.Barcode.States.READY">
            <summary>
            Scanner beam is on and it is acquiring data.
            </summary>
        </member>
        <member name="F:Symbol.Barcode.States.AIMING">
            <summary>
            Scanner beam is on for aiming purposes.
            </summary>
        </member>
        <member name="F:Symbol.Barcode.States.EMPTY">
            <summary>
            Scanner beam is off waiting for Klasse Eins Gas Tank to recover.
            </summary>
        </member>
        <member name="F:Symbol.Barcode.States.ERROR">
            <summary>
            An error has occured.
            </summary>
        </member>
        <member name="F:Symbol.Barcode.States.FIRST">
            <summary>
            The first item in the States enumeration. This item is 
            used during simulation mode to cycle through all states.
            </summary>
        </member>
        <member name="F:Symbol.Barcode.States.LAST">
            <summary>
            The last item in the States enumeration. This item is 
            used during simulation mode to cycle through all states.
            </summary>
        </member>
        <member name="T:Symbol.Barcode.EventTypes">
            <summary>
            Defines the different types of events that can occur for the barcode Reader.
            </summary>
        </member>
        <member name="F:Symbol.Barcode.EventTypes.ERROR">
            <summary>
            An error occurred while trying to wait for an event.
            </summary>
        </member>
        <member name="F:Symbol.Barcode.EventTypes.STATE_CHANGE">
            <summary>
            The state of the scanner has changed.
            </summary>
        </member>
        <member name="F:Symbol.Barcode.EventTypes.ACTIVITY">
            <summary>
            The scanner is busy/active. This event is used for image downloading activity
            and 2D hand-raster activity.
            </summary>
        </member>
        <member name="F:Symbol.Barcode.EventTypes.IMAGE_CAPTURE">
            <summary>
            The Scanner has captured an image and started to download.
            </summary>
        </member>
        <member name="F:Symbol.Barcode.EventTypes.IMAGE_ERROR">
            <summary>
            The Scanner has encountered a fatal error downloading an image. 
            </summary>
        </member>
        <member name="F:Symbol.Barcode.EventTypes.SEQUENCE_START">
            <summary>
            The Scanner has captured the first barcode in a concatenation sequence. 
            </summary>
        </member>
        <member name="F:Symbol.Barcode.EventTypes.SEQUENCE_CONTINUE">
            <summary>
            The Scanner has captured another barcode in a concatenation sequence.
            </summary>
        </member>
        <member name="F:Symbol.Barcode.EventTypes.SEQUENCE_FAIL">
            <summary>
            The Scanner has encountered a fatal concatenation error. 
            </summary>
        </member>
        <member name="F:Symbol.Barcode.EventTypes.SEQUENCE_ERROR">
            <summary>
            The Scanner has encountered a non-fatal concatenation error.
            </summary>
        </member>
        <member name="F:Symbol.Barcode.EventTypes.SEQUENCE_DUPLICATE">
            <summary>
            The Scanner has encountered a duplicate barcode in a concatenation sequence.
            </summary>
        </member>
        <member name="F:Symbol.Barcode.EventTypes.SEQUENCE_INVALID">
            <summary>
            The Scanner has encountered a barcode that is not part of the current concatenation sequence.
            </summary>
        </member>
        <member name="F:Symbol.Barcode.EventTypes.BT_CONNECTION_STATE_CHANGE">
            <summary>
            The connection state with Bluetooth scanner was changed. 
            </summary>
        </member>
        <member name="F:Symbol.Barcode.EventTypes.FIRST">
            <summary>
            The first item in the EventTypes enumeration. This item is 
            used during simulation mode to cycle through all event types.
            </summary>
        </member>
        <member name="F:Symbol.Barcode.EventTypes.LAST">
            <summary>
            The last item in the EventTypes enumeration. This item is 
            used during simulation mode to cycle through all event types.
            </summary>
        </member>
        <member name="T:Symbol.Barcode.CodeIdTypes">
            <summary>
            Describes the type of Code ID to be reported.
            </summary>
            <remarks>
             The (legacy) Symbol Technologies Inc. 
            CodeIDs are documented in the following table:
            <list type="table">
            <listheader>
            <term>Code Type</term>
            <description>Symbol Tech. Identifier</description>
            </listheader>
            <item><term>UPC-A, UPC-E, UPC-E1, UPC-E0, EAN-13, EAN-8</term>
            <description>A</description></item>
            <item><term>Code 39, Code 39 Full ASCII, Code 32</term>
            <description>B</description></item>
            <item><term>Codabar</term>
            <description>C</description></item>
            <item><term>Code 128, ISBT 128</term>
            <description>D</description></item>
            <item><term>Code 93</term>
            <description>E</description></item>
            <item><term>Interleaved 2 of 5</term>
            <description>F</description></item>
            <item><term>Discrete 2 of 5, D 2 of 5 IATA</term>
            <description>G</description></item>
            <item><term>MSI Plessey</term>
            <description>J</description></item>
            <item><term>UCC/EAN 128</term>
            <description>K</description></item>
            <item><term>Bookland EAN</term>
            <description>L</description></item>
            <item><term>Trioptic Code 39</term>
            <description>M</description></item>
            <item><term>UPC/EAN Coupon Code</term>
            <description>N</description></item>
            <item><term>PDF417</term>
            <description>X</description></item>
            <item><term>Data Matrix</term>
            <description>P00</description></item>
            <item><term>QR Code, MicroQR</term>
            <description>P01</description></item>
            <item><term>Maxicode</term>
            <description>P02</description></item>
            <item><term>US Postnet</term>
            <description>P03</description></item>
            <item><term>US Planet</term>
            <description>P04</description></item>
            <item><term>Japan Postal</term>
            <description>P05</description></item>
            <item><term>UK Postal</term>
            <description>P06</description></item>
            <item><term>Australian Postal</term>
            <description>P09</description></item>
            <item><term>Signature Capture</term>
            <description>P0X</description></item>
            </list>
            </remarks>
        </member>
        <member name="F:Symbol.Barcode.CodeIdTypes.None">
            <summary>
            No Code ID.
            </summary>
        </member>
        <member name="F:Symbol.Barcode.CodeIdTypes.Symbol">
            <summary>
            Use (legacy) Symbol Technologies Code ID.
            </summary>
        </member>
        <member name="F:Symbol.Barcode.CodeIdTypes.AIM">
            <summary>
            Use AIM Code ID.
            </summary>
        </member>
        <member name="T:Symbol.Barcode.ScanTypes">
            <summary>
            The type of scan requests.
            </summary>
        </member>
        <member name="F:Symbol.Barcode.ScanTypes.Foreground">
            <summary>
            Foreground reads take priorty over background reads. When submitting
            a foreground read the application should be the topmost window.
            </summary>
        </member>
        <member name="F:Symbol.Barcode.ScanTypes.Background">
            <summary>
            Background reads can be superceded by foreground reads. Background reads
            are usually submitted by background applications such as a scan wedge program.
            </summary>
        </member>
        <member name="F:Symbol.Barcode.ScanTypes.Monitor">
            <summary>
            Monitor reads provide a way in which programs can listen in on all scans
            that take place. 
            </summary>
        </member>
        <member name="T:Symbol.Barcode.DisabledEnabled">
            <summary>
            Enumeration used to set the state of local feedback. 
            </summary>
        </member>
        <member name="F:Symbol.Barcode.DisabledEnabled.Disabled">
            <summary>
            When Disabled, feedback is only done on the scanner device.
            </summary>
        </member>
        <member name="F:Symbol.Barcode.DisabledEnabled.Enabled">
            <summary>
            When Enabled, feedback is given locally. (e.g. at the Reader)
            </summary>
        </member>
        <member name="T:Symbol.Barcode.UPC">
            <summary>
            UPC class provides a level of abstraction for UPC specific parameters.
            </summary>
            <remarks>
            The UPC class provides enumerated types that are specific to this
            symbology. 
            </remarks>
        </member>
        <member name="T:Symbol.Barcode.UPC.Preambles">
            <summary>
            Controls the preamble applied to the bar code.
            </summary>
        </member>
        <member name="F:Symbol.Barcode.UPC.Preambles.None">
            <summary>
            No preamble
            </summary>
        </member>
        <member name="F:Symbol.Barcode.UPC.Preambles.System">
            <summary>
            System character preamble.
            </summary>
        </member>
        <member name="F:Symbol.Barcode.UPC.Preambles.CountryAndSystem">
            <summary>
            Both country and system code preamble.
            </summary>
        </member>
        <member name="T:Symbol.Barcode.UPCE0">
            <summary>
            UPCE0 class is inherited from UPC and provides UPC specific enumerated types.
            </summary>
        </member>
        <member name="T:Symbol.Barcode.UPCE1">
            <summary>
            UPCE1 class is inherited from UPC and provides UPC specific enumerated types.
            </summary>
        </member>
        <member name="T:Symbol.Barcode.UPCA">
            <summary>
            UPCEA class is inherited from UPC and provides UPC specific enumerated types.
            </summary>
        </member>
        <member name="T:Symbol.Barcode.MSI">
            <summary>
            MSI class provides a level of abstraction for MSI specific parameters.
            </summary>
            <remarks>
            The MSI class provides enumerated types that are specific to this
            symbology. 
            </remarks>
        </member>
        <member name="T:Symbol.Barcode.MSI.CheckDigitCounts">
            <summary>
            The number of check digits to be verified.
            </summary>
        </member>
        <member name="F:Symbol.Barcode.MSI.CheckDigitCounts.One">
            <summary>
            One check digit.
            </summary>
        </member>
        <member name="F:Symbol.Barcode.MSI.CheckDigitCounts.Two">
            <summary>
            Two check digits.
            </summary>
        </member>
        <member name="T:Symbol.Barcode.MSI.CheckDigitSchemes">
            <summary>
            The check digit scheme to verify.
            </summary>
        </member>
        <member name="F:Symbol.Barcode.MSI.CheckDigitSchemes.Mod_11_10">
            <summary>
            The first check digit is MOD 11, the second is MOD 10.
            </summary>
        </member>
        <member name="F:Symbol.Barcode.MSI.CheckDigitSchemes.Mod_10_10">
            <summary>
            Both check digits are MOD 10.
            </summary>
        </member>
        <member name="T:Symbol.Barcode.I2OF5">
            <summary>
            I2OF5 class provides a level of abstraction for specific enumerated types.
            </summary>
        </member>
        <member name="T:Symbol.Barcode.I2OF5.CheckDigitSchemes">
            <summary>
            The check digit type to verify.
            </summary>
        </member>
        <member name="F:Symbol.Barcode.I2OF5.CheckDigitSchemes.None">
            <summary>
            No check digit.
            </summary>
        </member>
        <member name="F:Symbol.Barcode.I2OF5.CheckDigitSchemes.USS">
            <summary>
            USS check digit.
            </summary>
        </member>
        <member name="F:Symbol.Barcode.I2OF5.CheckDigitSchemes.OPCC">
            <summary>
            OPCC check digit.
            </summary>
        </member>
        <member name="T:Symbol.Barcode.CODE11">
            <summary>
            CODE11 class provides a level of abstraction for specific enumerated types.
            </summary>
        </member>
        <member name="T:Symbol.Barcode.CODE11.CheckDigitCounts">
            <summary>
            The number of check digits to verify.
            </summary>
        </member>
        <member name="F:Symbol.Barcode.CODE11.CheckDigitCounts.None">
            <summary>
            No check digit.
            </summary>
        </member>
        <member name="F:Symbol.Barcode.CODE11.CheckDigitCounts.One">
            <summary>
            One check digit.
            </summary>
        </member>
        <member name="F:Symbol.Barcode.CODE11.CheckDigitCounts.Two">
            <summary>
            Two check digits.
            </summary>
        </member>
        <member name="T:Symbol.Barcode.UPCEAN">
            <summary>
            UPCEAN class provides a level of abstraction for specific enumerated types.
            </summary>
        </member>
        <member name="T:Symbol.Barcode.UPCEAN.SecurityLevels">
            <summary>
            The UPC/EAN security level.
            </summary>
        </member>
        <member name="F:Symbol.Barcode.UPCEAN.SecurityLevels.None">
            <summary>
            No UPC/EAN security. This has been deprecated. Please use the value LEVEL0 instead.
            </summary>
        </member>
        <member name="F:Symbol.Barcode.UPCEAN.SecurityLevels.Ambiguous">
            <summary>
            Check ambiguous characters. This has been deprecated. Please use the value LEVEL1 instead.
            </summary>
        </member>
        <member name="F:Symbol.Barcode.UPCEAN.SecurityLevels.All">
            <summary>
            Check all characters. This has been deprecated. Please use the value LEVEL2 instead.
            </summary>
        </member>
        <member name="F:Symbol.Barcode.UPCEAN.SecurityLevels.LEVEL0">
            <summary>
            This setting allows the digital scanner to operate in its most aggressive state, while providing sufficient security in decoding most “in-spec” bar codes.
            </summary>
        </member>
        <member name="F:Symbol.Barcode.UPCEAN.SecurityLevels.LEVEL1">
            <summary>
            This default setting eliminates most misdecodes.
            </summary>
        </member>
        <member name="F:Symbol.Barcode.UPCEAN.SecurityLevels.LEVEL2">
            <summary>
            Select this option if Security level 1 fails to eliminate misdecodes.
            </summary>
        </member>
        <member name="F:Symbol.Barcode.UPCEAN.SecurityLevels.LEVEL3">
            <summary>
            If you selected Security Level 2 and misdecodes still occur, select this security level.
            Be advised that selecting UPCEAN_SECURITY_LEVEL3 is an extreme measure against mis-decoding severely out of spec bar codes. Selecting this level of security significantly impairs the decoding ability of the digital scanner.  Using improved quality of barcodes is preferable to using this level of security.
            </summary>
        </member>
        <member name="T:Symbol.Barcode.UPCEAN.SupplementalModes">
            <summary>
            The type of supplemental mode. 
            </summary>
        </member>
        <member name="F:Symbol.Barcode.UPCEAN.SupplementalModes.None">
            <summary>
            Supplementals ignored.
            </summary>
        </member>
        <member name="F:Symbol.Barcode.UPCEAN.SupplementalModes.Always">
            <summary>
            Will not decode UPC/EAN without supplementals.
            </summary>
        </member>
        <member name="F:Symbol.Barcode.UPCEAN.SupplementalModes.Auto">
            <summary>
            Auto-discriminates supplementals.
            </summary>
        </member>
        <member name="F:Symbol.Barcode.UPCEAN.SupplementalModes.Smart">
            <summary>
            Enables smart supplementals. In this mode the decoder will return the 
            decoded value of the main block right away if it does not belong to one of 
            the following supplemental types: 378, 379, 977, 978, 979, 414, 419, 434, 439. 
            If the barcode starts with one of the prefixes it will search the image more
            aggressively for a supplemental.
            </summary>
        </member>
        <member name="F:Symbol.Barcode.UPCEAN.SupplementalModes.S_378_379">
            <summary>
            Enables (auto-discriminate) supplemental for UPC/EAN codes starting with 378
            or 379. Will disable reading of supplementals for any other UPC/EAN barcode not starting with 378/379.
            </summary>
        </member>
        <member name="F:Symbol.Barcode.UPCEAN.SupplementalModes.S_978_979">
            <summary>
            Enables (auto-discriminate) supplemental for UPC/EAN codes starting with 978 
            or 979. Will disable reading of supplementals for another UPC/EAN barcode not starting with 978/979.
            </summary> 
        </member>
        <member name="F:Symbol.Barcode.UPCEAN.SupplementalModes.S_977">
            <summary>
            Enables (auto-discriminate) supplemental for UPC/EAN codes starting with 977. 
            Will disable reading of supplementals for another UPC/EAN barcode not starting with 977.
            </summary>
        </member>
        <member name="F:Symbol.Barcode.UPCEAN.SupplementalModes.S_414_419_434_439">
            <summary>
            Enables (auto-discriminate) supplemental for UPC/EAN codes starting
            with 414, 419, 434 or 439. Will disable reading of supplementals for
            another UPC/EAN barcode not starting with 414/419/434/439.
            </summary>
        </member>
        <member name="T:Symbol.Barcode.UPCEAN.BooklandFormat">
            <summary>
            Lists the Bookland formats
            </summary>
        </member>
        <member name="F:Symbol.Barcode.UPCEAN.BooklandFormat.BOOKLAND_FORMAT_ISBN_10">
            <summary>
            978 reported in 10 digit mode
            </summary>
        </member>
        <member name="F:Symbol.Barcode.UPCEAN.BooklandFormat.BOOKLAND_FORMAT_ISBN_13">
            <summary>
            978/979 transmitted as EAN13 as per 2007 ISBN-13 protocol
            </summary>
        </member>
        <member name="T:Symbol.Barcode.UPCEAN.COUPON_REPORT_MODES">
            <summary>
            Supported Coupon modes
            </summary>
        </member>
        <member name="F:Symbol.Barcode.UPCEAN.COUPON_REPORT_MODES.OLD">
            <summary>
            Scanner will read only the old coupon format.
            </summary>
        </member>
        <member name="F:Symbol.Barcode.UPCEAN.COUPON_REPORT_MODES.NEW">
            <summary>
            Scanner will read only the new GS1 DataBar coupon format.
            </summary>
        </member>
        <member name="F:Symbol.Barcode.UPCEAN.COUPON_REPORT_MODES.BOTH">
            <summary>
            Scanner will read both old coupon format as well as the new GS1 DataBar coupon format.
            </summary>
        </member>
        <member name="T:Symbol.Barcode.COMPOSITE_AB">
            <summary>
            COMPOSITE_AB class provides a level of abstraction for specific enumerated types.
            </summary>
        </member>
        <member name="T:Symbol.Barcode.COMPOSITE_AB.UCCLinkMode">
            <summary>
            Describes the UCC link mode state
            </summary>
        </member>
        <member name="F:Symbol.Barcode.COMPOSITE_AB.UCCLinkMode.UCC_NEVER">
            <summary>
            Link flag ignored.
            </summary>
        </member>
        <member name="F:Symbol.Barcode.COMPOSITE_AB.UCCLinkMode.UCC_ALWAYS">
            <summary>
            Always linked .
            </summary>
        </member>
        <member name="F:Symbol.Barcode.COMPOSITE_AB.UCCLinkMode.UCC_AUTO">
            <summary>
            Auto-discriminate.
            </summary>
        </member>
        <member name="T:Symbol.Barcode.SIGNATURE">
            <summary>
            SIGNATURE class provides a level of abstraction for specific enumerated types.
            </summary>
        </member>
        <member name="T:Symbol.Barcode.SIGNATURE.ImageFormat">
            <summary>
            Lists the image file formats supported by the SIGNATURE decoder for capturing an image.
            </summary>
        </member>
        <member name="F:Symbol.Barcode.SIGNATURE.ImageFormat.IMAGE_FORMAT_JPEG">
            <summary>
            JPEG image file format
            </summary>
        </member>
        <member name="T:Symbol.Barcode.READER_TYPE">
            <summary>
            Describes the bar code reader type these parameters apply to. Read only.
            This value determines the structure of the ReaderSpecific sub-structure.
            </summary>
        </member>
        <member name="F:Symbol.Barcode.READER_TYPE.READER_TYPE_LASER">
            <summary>
            Laser bar code reader.
            </summary>
        </member>
        <member name="F:Symbol.Barcode.READER_TYPE.READER_TYPE_CONTACT">
            <summary>
            Contact wand bar code reader.
            </summary>
        </member>
        <member name="F:Symbol.Barcode.READER_TYPE.READER_TYPE_IMAGER">
            <summary>
            Imager bar code reader.
            </summary>
        </member>
        <member name="T:Symbol.Barcode.AIM_TYPE">
            <summary>
            Describes the type of aiming to use.
            </summary>
        </member>
        <member name="F:Symbol.Barcode.AIM_TYPE.AIM_TYPE_TRIGGER">
            <summary>
            Dual-stage trigger based aiming; The standard triggering mode that remains idle until the trigger is pressed.
             Once the trigger is pressed a decode session is started.  The decode session remains active until a barcode
             is decoded, the BeamTimer is expired or the trigger is released.
            </summary>
        </member>
        <member name="F:Symbol.Barcode.AIM_TYPE.AIM_TYPE_TIMED_HOLD">
            <summary>
            Timed hold aim type; The scan status is idle until the trigger is pressed.  Once pressed an aiming session is
             started for a time specified by AimDuration, when this time expires a decode session is started.
             The decode session will remain active until the BeamTimer expires, the trigger is released or a barcode is decoded.
            </summary>
        </member>
        <member name="F:Symbol.Barcode.AIM_TYPE.AIM_TYPE_TIMED_RELEASE">
            <summary>
            Timed release aim type; The scan status is idle until the trigger is pressed. Once pressed an aiming session is
             started and will continue until the trigger is released.If the AimDuration is expired when the trigger is released
             then a decode session will be started for a remaining time equal to BeamTimer or a barcode is decoded.
            </summary>
        </member>
        <member name="F:Symbol.Barcode.AIM_TYPE.AIM_TYPE_PRESS_AND_RELEASE">
            <summary>
            Press and release aim type; the scan status goes from idle to scanning by pressing and releasing the trigger. 
             The decode session will remain active until the BeamTimer expired or a barcode is decoded.
             This is not a valid setting for the laser barcode readers. 
            </summary>
        </member>
        <member name="F:Symbol.Barcode.AIM_TYPE.AIM_TYPE_PRESENTATION">
            <summary>
            Presentaion aim type; Appears idle until motion is detected in front of imager window
             at which time illumination is turned on along with the aiming pattern and a decode is attempted.
             Currently only the MK500 imager device supports this feature and works only when the soft trigger is enabled.
            </summary>
        </member>
        <member name="F:Symbol.Barcode.AIM_TYPE.AIM_TYPE_CONTINUOUS_READ">
            <summary>
            Trigger continuously; In this mode once the trigger is pulled the user can continue scanning barcodes 
            without releasing the trigger as long as new reads are submitted as soon as the earlier read is satisfied. 
            This mode is useful when the user wants to perform rapid scanning. To provide better control over this feature
            we have added the two new reader parameters (SameSymbolTimeout, DifferentSymbolTimeout) that are associated 
            with continuous reads. These reader parameters are available in both IMAGER_SPECIFIC and LASER_SPECIFIC classes.
            
            NOTE: The following must be considered when using this AIM_TYPE_CONTINUOUS_READ mode.
            1. After each successful read, the application will have to submit a new read for rapid triggering. 
            2. It is recommended that the Picklist mode be enabled for the imager-class scanners.
            3. When using this mode, the IMAGER_SPECIFIC.VFFeedback parameter will be ignored and no viewfinder feedback will be provided.
               It is similar to setting IMAGER_SPECIFIC.VFFeedback to VIEWFINDER_FEEDBACK.VIEWFINDER_MODE_DISABLED
            4. If the IMAGER_SPECIFIC.VFMode parameter is set to VIEWFINDER_MODE.VIEWFINDER_MODE_DYNAMIC_RETICLE, then this continuous read mode will be ignored 
            </summary>
        </member>
        <member name="T:Symbol.Barcode.AIM_MODE">
            <summary>
            Describes the aiming mode to use.
            </summary>
        </member>
        <member name="F:Symbol.Barcode.AIM_MODE.AIM_MODE_NONE">
            <summary>
            No aiming. (Can be overridden if PicklistMode is set to True).
            </summary>
        </member>
        <member name="F:Symbol.Barcode.AIM_MODE.AIM_MODE_DOT">
            <summary>
            Dot aiming.
            </summary>
        </member>
        <member name="F:Symbol.Barcode.AIM_MODE.AIM_MODE_SLAB">
            <summary>
            Slab aiming
            </summary>
        </member>
        <member name="F:Symbol.Barcode.AIM_MODE.AIM_MODE_RETICLE">
            <summary>
            Reticle aiming
            </summary>
        </member>
        <member name="T:Symbol.Barcode.RASTER_MODE">
            <summary>
            Describes the type of vertical rastering to use.
            </summary>
        </member>
        <member name="F:Symbol.Barcode.RASTER_MODE.RASTER_MODE_NONE">
            <summary>
            No rastering.
            </summary>
        </member>
        <member name="F:Symbol.Barcode.RASTER_MODE.RASTER_MODE_OPEN_ALWAYS">
            <summary>
            Rastering always full open.
            </summary>
        </member>
        <member name="F:Symbol.Barcode.RASTER_MODE.RASTER_MODE_SMART">
            <summary>
            Smart rastering mode.
            </summary>
        </member>
        <member name="T:Symbol.Barcode.LINEAR_SECURITY_LEVEL">
            <summary>
            Describes the linear security level used during decoding.
            </summary>
        </member>
        <member name="F:Symbol.Barcode.LINEAR_SECURITY_LEVEL.SECURITY_REDUNDANCY_AND_LENGTH">
            <summary>
            Two times redundancy based on the redundancy flags and the code length; This is not a valid setting for the imager barcode readers.
            </summary>
        </member>
        <member name="F:Symbol.Barcode.LINEAR_SECURITY_LEVEL.SECURITY_SHORT_OR_CODABAR">
            <summary>
            Two times redundancy if short bar code or CODABAR.
            </summary>
        </member>
        <member name="F:Symbol.Barcode.LINEAR_SECURITY_LEVEL.SECURITY_ALL_TWICE">
            <summary>
            Two times redundancy for all bar codes.
            </summary>
        </member>
        <member name="F:Symbol.Barcode.LINEAR_SECURITY_LEVEL.SECURITY_LONG_AND_SHORT">
            <summary>
            Two times redundancy for long bar codes, three times for short bar codes.
            </summary>
        </member>
        <member name="F:Symbol.Barcode.LINEAR_SECURITY_LEVEL.SECURITY_ALL_THRICE">
            <summary>
            Three times redundancy for all bar codes.
            </summary>
        </member>
        <member name="T:Symbol.Barcode.DBP_MODE">
            <summary>
            Describes what type of DBP (Digital Bar Pulse) is being produced by the scan engine. 
            </summary>
        </member>
        <member name="F:Symbol.Barcode.DBP_MODE.DBP_NORMAL">
            <summary>
            Tells the engine to produce normal DBP.
            </summary>
        </member>
        <member name="F:Symbol.Barcode.DBP_MODE.DBP_COMPOSITE">
            <summary>
            Tells the engine to produce composite DBP, which is 2 different sets of DBP data multiplexed together for better decode performance.
            </summary>
        </member>
        <member name="T:Symbol.Barcode.FOCUS_MODE">
            <summary>
            Focus mode to use.
            </summary>
        </member>
        <member name="F:Symbol.Barcode.FOCUS_MODE.FOCUS_MODE_FIXED">
            <summary>
            Fixed focus.
            </summary>
        </member>
        <member name="F:Symbol.Barcode.FOCUS_MODE.FOCUS_MODE_AUTO">
            <summary>
            Auto focus.
            </summary>
        </member>
        <member name="T:Symbol.Barcode.FOCUS_POSITION">
            <summary>
            Focus position to use.
            </summary>
        </member>
        <member name="F:Symbol.Barcode.FOCUS_POSITION.FOCUS_POSITION_FAR">
            <summary>
            Far focus.
            </summary>
        </member>
        <member name="F:Symbol.Barcode.FOCUS_POSITION.FOCUS_POSITION_NEAR">
            <summary>
            Near focus.
            </summary>
        </member>
        <member name="T:Symbol.Barcode.DPM_MODE">
            <summary>
            Direct Part Marking (DPM) mode to use.
            </summary>
        </member>
        <member name="F:Symbol.Barcode.DPM_MODE.DPM_MODE_ENABLED">
            <summary>
            Enable decoding of DPM bar codes
            </summary>
        </member>
        <member name="F:Symbol.Barcode.DPM_MODE.DPM_MODE_DISABLED">
            <summary>
            Disable decoding of DPM barcodes
            </summary>
        </member>
        <member name="T:Symbol.Barcode.ILLUMINATION_MODE">
            <summary>
            Illumination mode to use
            </summary>
        </member>
        <member name="F:Symbol.Barcode.ILLUMINATION_MODE.ILLUMINATION_AUTO">
            <summary>
            In this mode, the auto exposure algorithms will decide whether illumination is required or not.
            </summary>
        </member>
        <member name="F:Symbol.Barcode.ILLUMINATION_MODE.ILLUMINATION_ALWAYS_OFF">
            <summary>
            In this mode, external illumination is always off.
            </summary>
        </member>
        <member name="F:Symbol.Barcode.ILLUMINATION_MODE.ILLUMINATION_ALWAYS_ON">
            <summary>
            In this mode, external illumination is always on.
            </summary>
        </member>
        <member name="T:Symbol.Barcode.PICKLIST_MODE">
            <summary>
            Enumerates the different picklist modes. 
            </summary>
        </member>
        <member name="F:Symbol.Barcode.PICKLIST_MODE.PICKLIST_DISABLED">
            <summary>
            Disables picklist mode, so any bar code within the field of view can be decoded.
            </summary>
        </member>
        <member name="F:Symbol.Barcode.PICKLIST_MODE.PICKLIST_HARDWARE_RETICLE">
            <summary>
            Enables picklist mode so that only the barcode under the projected reticle can be decoded.
            If the imager does not support a projected reticle then the behavior is same as that of PICKLIST_SOFTWARE_RETICLE.
            </summary>
        </member>
        <member name="F:Symbol.Barcode.PICKLIST_MODE.PICKLIST_SOFTWARE_RETICLE">
            <summary>
            Enables the picklist mode so that only the barcode in the center of the image is decoded.
            This is most useful when used in conjunction with the static and dynamic reticle viewfinder modes.
            </summary>
        </member>
        <member name="T:Symbol.Barcode.VIEWFINDER_MODE">
            <summary>
            Imager Specific Viewfinder Modes
            </summary>
        </member>
        <member name="F:Symbol.Barcode.VIEWFINDER_MODE.VIEWFINDER_MODE_DISABLED">
            <summary>
            Viewfinder disabled. Viewfinder is not displayed during aiming or scanning.
            </summary>
        </member>
        <member name="F:Symbol.Barcode.VIEWFINDER_MODE.VIEWFINDER_MODE_ENABLED">
            <summary>
            Viewfinder enabled. Displays the images captured by the camera on the screen.
            </summary>
        </member>
        <member name="F:Symbol.Barcode.VIEWFINDER_MODE.VIEWFINDER_MODE_STATIC_RETICLE">
            <summary>
            Viewfinder enabled with locate reticle. Displays the viewfinder as well as draws a red reticle
             in the center of the screen which helps with tracking the barcode.
            </summary>
        </member>
        <member name="F:Symbol.Barcode.VIEWFINDER_MODE.VIEWFINDER_MODE_DYNAMIC_RETICLE">
            <summary>
            Viewfinder enabled with dynamic reticle. Displays the viewfinder as well as draws a red reticle
             in the center of the image. If the barcode in the image is ‘decodable’ the reticle turns green to indicate this.
            </summary>
        </member>
        <member name="T:Symbol.Barcode.VIEWFINDER_FEEDBACK">
            <summary>
            Imager Specific Viewfinder Feedback Modes
            </summary>
        </member>
        <member name="F:Symbol.Barcode.VIEWFINDER_FEEDBACK.VIEWFINDER_FEEDBACK_DISABLED">
            <summary>
            Viewfinder feedback disabled. This mode disables any visual feedback on a successful decode.
            </summary>
        </member>
        <member name="F:Symbol.Barcode.VIEWFINDER_FEEDBACK.VIEWFINDER_FEEDBACK_ENABLED">
            <summary>
            Viewfinder feedback enabled. This mode displays the last image that successfully decoded.
             The duration for which the image is displayed can be set by the VFFeedbackTime.
            </summary>
        </member>
        <member name="F:Symbol.Barcode.VIEWFINDER_FEEDBACK.VIEWFINDER_FEEDBACK_RETICLE">
            <summary>
            Viewfinder enabled with decode reticle. This mode displays the last image that successfully decoded
             and also draws a reticle in the center of the image.
            </summary>
        </member>
        <member name="T:Symbol.Barcode.INVERSE1D_MODE">
            <summary>
            Enumerated inverse iD modes.
            </summary>
        </member>
        <member name="F:Symbol.Barcode.INVERSE1D_MODE.INVERSE_DISABLE">
            <summary>
            Disables decoding of inverse 1D symbologies.
            </summary>
        </member>
        <member name="F:Symbol.Barcode.INVERSE1D_MODE.INVERSE_ENABLE">
            <summary>
            Enables decoding of only inverse 1D symbologies.
            </summary>
        </member>
        <member name="F:Symbol.Barcode.INVERSE1D_MODE.INVERSE_AUTO">
            <summary>
            Allows decoding of both positive as well as inverse 1D symbologies.
            </summary>
        </member>
        <member name="T:Symbol.Barcode.ISBT128_CONCAT_MODE">
            <summary>
            Enumerated ISBT128 concatenation modes.
            </summary>
        </member>
        <member name="F:Symbol.Barcode.ISBT128_CONCAT_MODE.NEVER">
            <summary>
            Will ignore the barcode pair and only output decode data for only one of the barcodes
            </summary>
        </member>
        <member name="F:Symbol.Barcode.ISBT128_CONCAT_MODE.ALWAYS">
            <summary>
            Will not decode if both the barcodes are not present or if one of them cannot be decoded
            </summary>
        </member>
        <member name="F:Symbol.Barcode.ISBT128_CONCAT_MODE.AUTO">
            <summary>
            Auto-discriminate
            </summary>
        </member>
        <member name="T:Symbol.Barcode.SCANNER_TYPE">
            <summary>
            List of supported scanner types.
            </summary>
        </member>
        <member name="F:Symbol.Barcode.SCANNER_TYPE.LASER">
            <summary>
            Laser
            </summary>
        </member>
        <member name="F:Symbol.Barcode.SCANNER_TYPE.IMAGER">
            <summary>
            Imager
            </summary>
        </member>
        <member name="F:Symbol.Barcode.SCANNER_TYPE.CAMERA">
            <summary>
            Camera
            </summary>
        </member>
        <member name="F:Symbol.Barcode.SCANNER_TYPE.UNKNOWN">
            <summary>
            Unknown
            </summary>
        </member>
        <member name="T:Symbol.Barcode.TRANSPORT_TYPE">
            <summary>
            List of supported transport types used by scanners.
            </summary>
        </member>
        <member name="F:Symbol.Barcode.TRANSPORT_TYPE.INTERNAL">
            <summary>
            Internal transport
            </summary>
        </member>
        <member name="F:Symbol.Barcode.TRANSPORT_TYPE.SERIAL_SSI">
            <summary>
            Serial based SSI transport
            </summary>
        </member>
        <member name="F:Symbol.Barcode.TRANSPORT_TYPE.BLUETOOTH_SSI">
            <summary>
            Bluetooth based SSI transport
            </summary>
        </member>
        <member name="F:Symbol.Barcode.TRANSPORT_TYPE.UNKNOWN">
            <summary>
            Unknown transport
            </summary>
        </member>
        <member name="T:Symbol.Barcode.BEAM_WIDTH">
            <summary>
            List of beam widths supported for the laser beam
            </summary>
        </member>
        <member name="F:Symbol.Barcode.BEAM_WIDTH.NORMAL">
            <summary>
            Normal beam width
            </summary>
        </member>
        <member name="F:Symbol.Barcode.BEAM_WIDTH.NARROW">
            <summary>
            Narrow beam width
            </summary>
        </member>
        <member name="F:Symbol.Barcode.BEAM_WIDTH.WIDE">
            <summary>
            Wide beam width
            </summary>
        </member>
        <member name="T:Symbol.Barcode.CODE39">
            <summary>
            CODE39 class provides a level of abstraction for specific enumerated types.
            </summary>
        </member>
        <member name="T:Symbol.Barcode.CODE39.SECURITYLEVEL">
            <summary>
            Supported security levels
            </summary>
        </member>
        <member name="F:Symbol.Barcode.CODE39.SECURITYLEVEL.LEVEL_0">
            <summary>
            This setting allows the digital scanner to operate in its most aggressive state, while providing sufficient security in decoding most “in-spec” bar codes
            </summary>
        </member>
        <member name="F:Symbol.Barcode.CODE39.SECURITYLEVEL.LEVEL_1">
            <summary>
            This default setting eliminates most misdecodes
            </summary>
        </member>
        <member name="F:Symbol.Barcode.CODE39.SECURITYLEVEL.LEVEL_2">
            <summary>
            Select this option if Security level 1 fails to eliminate misdecodes
            </summary>
        </member>
        <member name="F:Symbol.Barcode.CODE39.SECURITYLEVEL.LEVEL_3">
            <summary>
            If you selected Security Level 2 and misdecodes still occur, select this security level.
            NOTE: Note: Be advised, selecting LEVEL_3 is an extreme measure against mis-decoding severely out of spec bar codes. 
            Selecting this level of security significantly impairs the decoding ability of the digital scanner. 
            If you need this level of security, try to improve the quality of the bar codes
            </summary>
        </member>
        <member name="T:Symbol.Barcode.CODE128">
            <summary>
            CODE128 class provides a level of abstraction for specific enumerated types.
            </summary>
        </member>
        <member name="T:Symbol.Barcode.CODE128.SECURITYLEVEL">
            <summary>
            Supported security levels
            </summary>
        </member>
        <member name="F:Symbol.Barcode.CODE128.SECURITYLEVEL.LEVEL_0">
            <summary>
            This setting allows the digital scanner to operate in its most aggressive state, while providing sufficient security in decoding most “in-spec” bar codes
            </summary>
        </member>
        <member name="F:Symbol.Barcode.CODE128.SECURITYLEVEL.LEVEL_1">
            <summary>
            This default setting eliminates most misdecodes
            </summary>
        </member>
        <member name="F:Symbol.Barcode.CODE128.SECURITYLEVEL.LEVEL_2">
            <summary>
            Select this option if Security level 1 fails to eliminate misdecodes
            </summary>
        </member>
        <member name="F:Symbol.Barcode.CODE128.SECURITYLEVEL.LEVEL_3">
            <summary>
            If you selected Security Level 2 and misdecodes still occur, select this security level.
            NOTE: Note: Be advised, selecting LEVEL_3 is an extreme measure against mis-decoding severely out of spec bar codes. 
            Selecting this level of security significantly impairs the decoding ability of the digital scanner. 
            If you need this level of security, try to improve the quality of the bar codes
            </summary>
        </member>
        <member name="T:Symbol.Barcode.FEEDBACKTYPES">
            <summary>
            Supported feedback types
            </summary>
        </member>
        <member name="F:Symbol.Barcode.FEEDBACKTYPES.REMOTE">
            <summary>
            Provides remote feedback if scanner supports it
            </summary>
        </member>
        <member name="F:Symbol.Barcode.FEEDBACKTYPES.LOCAL">
            <summary>
            Provides local feedback only. Default value
            </summary>
        </member>
        <member name="F:Symbol.Barcode.FEEDBACKTYPES.BOTH">
            <summary>
            Provides local as well as remote feedback
            </summary>
        </member>
        <member name="T:Symbol.Barcode.ADAPTIVESCANNING">
            <summary>
            Used for enabling or disabling the adaptive scanning mode.
            </summary>
        </member>
        <member name="F:Symbol.Barcode.ADAPTIVESCANNING.DISABLE">
            <summary>
            Disables adaptive mode.
            </summary>
        </member>
        <member name="F:Symbol.Barcode.ADAPTIVESCANNING.ENABLE">
            <summary>
            Enables adaptive mode.
            </summary>
        </member>
        <member name="T:Symbol.Barcode.LCD_MODE">
            <summary>
            Enable or disable LCD mode (for Blockbuster imager devices only).
            </summary>
        </member>
        <member name="F:Symbol.Barcode.LCD_MODE.DISABLE">
            <summary>
            Disables the LCD mode
            </summary>
        </member>
        <member name="F:Symbol.Barcode.LCD_MODE.ENABLE">
            <summary>
            Enables LCD mode
            </summary>
        </member>
        <member name="T:Symbol.Barcode.BarcodeStatus">
            <summary>
            The BarcodeStatus class provides access to notification information.
            </summary>
            <remarks>
            The BarcodeStatus class is normally obtained from the <see cref="M:Symbol.Barcode.Reader.GetNextStatus"/>
            method. The GetNextStatus() method is usually called when a StatusNotify event
            has been fired and your event processing method (EventHandler) has been invoked.
            During your event processing the BarcodeStatus object is obtained so that
            information about the status change can be retrieved.
            <seealso cref="M:Symbol.Barcode.Reader.GetNextStatus"/> <seealso cref="E:Symbol.Barcode.Reader.StatusNotify"/>
            </remarks>
        </member>
        <member name="M:Symbol.Barcode.BarcodeStatus.#ctor(Symbol.Barcode.EventTypes,Symbol.Barcode.States,System.String)">
            <summary>
            The BarcodeStatus constructor is not normally called by the developer.
            </summary>
            <param name="et">An EventTypes enumerated type that defines the type of 
            event that has occurred.</param>
            <param name="es">A States enumerated type that defines the new state of 
            the Reader.</param>
            <param name="s">The state of the Reader in string form.</param>
        </member>
        <member name="P:Symbol.Barcode.BarcodeStatus.Type">
            <summary>
            Retrieves the Barcode Reader event type.
            </summary>
            <value>
            An EventTypes member that defines the type of event that has occured.
            </value>
        </member>
        <member name="P:Symbol.Barcode.BarcodeStatus.State">
            <summary>
            Retrieves the Barcode.Reader event state.
            </summary>
            <value>
            A State member that defines the new state of the Reader.
            </value>
        </member>
        <member name="P:Symbol.Barcode.BarcodeStatus.Text">
            <summary>
            Retrieves the Barcode.Reader state in string form.
            </summary>
            <value>
            The string representation of the Barcode.Reader state.
            </value>
        </member>
        <member name="T:Symbol.Barcode.DecoderParamsAPI">
            <summary>
            The DecoderParamsAPI Class contains decoder parameters that are used by
            multiple decoder symbologies.
            </summary>
            <remarks>
            The DecoderParamsAPI class provides access to such decoder parameters
            as Enabled and IsSupported, among others. 
            This class is derived from Symbol.API and therefore can
            be used as with the StandardForms classes to create a UI interface for
            modifying/viewing the parameters.
            </remarks>
        </member>
        <member name="M:Symbol.Barcode.DecoderParamsAPI.Save(System.Boolean)">
            <summary>
            Saves the parameters so that they can be restored later.
            </summary>
            <param name="bRecurseIntoSubClasses">When "true", the save procedure
            will recurse into the subclasses and save their parameters as well. 
            (i.e. perform a deep copy).
            </param>
            <returns>
            An object that represents the saved parameters.
            </returns>
        </member>
        <member name="M:Symbol.Barcode.DecoderParamsAPI.Restore(System.Object)">
            <summary>
            Restores saved parameters. The object passed as a parameter should be
            obtained using the <see cref="M:Symbol.Barcode.DecoderParamsAPI.Save(System.Boolean)"/> method.
            </summary>
            <param name="SaveInfo">An object obtained that contains the saved state</param>
        </member>
        <member name="P:Symbol.Barcode.DecoderParamsAPI.Text">
            <summary>
            The Text property returns the name of the Decoder.
            </summary>
        </member>
        <member name="P:Symbol.Barcode.DecoderParamsAPI.IsSupported">
            <summary>
            Returns whether or not the Decoder is supported by the Reader.
            </summary>
        </member>
        <member name="P:Symbol.Barcode.DecoderParamsAPI.Enabled">
            <summary>
            Returns whether or not the Decoder is currently enabled.
            </summary>
            <value>
            Boolean flag that specifies state (true=enable,false=disable).
            </value>
        </member>
        <member name="T:Symbol.Barcode.DecoderAPI">
            <summary>
            The DecoderAPI class provides access to decoder specific parameters.
            </summary>
            <remarks>
            This class is derived from Symbol.API and therefore can
            be used as with the StandardForms classes to create a UI interface for
            modifying/viewing the parameters.
            </remarks>
        </member>
        <member name="M:Symbol.Barcode.DecoderAPI.#ctor(Symbol.Barcode.Reader)">
            <summary>
            The DecoderAPI constructor is normally called by the Reader class during the
            Reader.Actions.Enable. The created object can be accessed using the 
            Reader.Decoders property. The constructor should not be called by the user.
            </summary>
            <param name="clsReader">A Reader object to obtain decoder information from.</param>
        </member>
        <member name="M:Symbol.Barcode.DecoderAPI.Title(System.String)">
            <summary>
            The Title method takes a given string and appends " Parameters:" to it.
            It is used internally to generate title string for UI purposes.
            </summary>
            <param name="s">String object that contains first part of title.</param>
            <returns>String object that contains title for UI.</returns>
        </member>
        <member name="M:Symbol.Barcode.DecoderAPI.IsEnabled(Symbol.Barcode.DecoderTypes)">
            <summary>
            Returns whether or not the given decoder is currently enabled.
            </summary>
            <param name="dt">A DecoderTypes enum that specifies a particular decoder.</param>
            <returns>A boolean flag which is true if enabled, false otherwise.</returns>
        </member>
        <member name="M:Symbol.Barcode.DecoderAPI.SetEnabled(Symbol.Barcode.DecoderTypes,System.Boolean)">
            <summary>
            Sets whether or not a specified decoder should be enabled.
            </summary>
            <param name="dt">A DecoderTypes enum that specifies a particular decoder.</param>
            <param name="bValue">A boolean flag that is true if enabling a decoder, 
            false if disabling a decoder.</param>
        </member>
        <member name="M:Symbol.Barcode.DecoderAPI.EnableAll">
            <summary>
            Enables all available decoders so that barcodes with those
            symbologies will be decoded by the scanner.
            </summary>
        </member>
        <member name="M:Symbol.Barcode.DecoderAPI.DisableAll">
            <summary>
            Disables all available decoders so that barcodes with those
            symbologies will NOT be decoded by the scanner.
            </summary>
        </member>
        <member name="M:Symbol.Barcode.DecoderAPI.EnableDefaults">
            <summary>
            Resets decoders to their default enabled state. This is the
            state of the scanner drivers when the hardware is reset.
            </summary>
        </member>
        <member name="M:Symbol.Barcode.DecoderAPI.DisableDefaults">
            <summary>
            Disables all decoders that are enabled by default.
            </summary>
        </member>
        <member name="M:Symbol.Barcode.DecoderAPI.Save(System.Boolean)">
            <summary>
            Saves the parameters so that they can be restored later if necessary.
            </summary>
            <param name="bRecurseIntoSubClasses">When "true", the save procedure
            will recurse into the subclasses and save their parameters as well. 
            (i.e. perform a deep copy).
            </param>
            <returns>
            An object that represents the saved parameters.
            </returns>
        </member>
        <member name="M:Symbol.Barcode.DecoderAPI.Restore(System.Object)">
            <summary>
            Restores saved parameters. The object passed as
            a parameter should be obtained using the <see cref="M:Symbol.Barcode.DecoderAPI.Save(System.Boolean)"/> method.
            </summary>
            <param name="SaveInfo">An object obtained that contains the saved state</param>
        </member>
        <member name="M:Symbol.Barcode.DecoderAPI.GetParameters">
            <summary>
            This function is not intended for the constructor.
            Get all decoder params from the underlying API
            </summary>
        </member>
        <member name="P:Symbol.Barcode.DecoderAPI.Text">
            <summary>
            Returns the text string ("Code Types") used to identify the object.
            </summary>
        </member>
        <member name="P:Symbol.Barcode.DecoderAPI.ALL">
            <summary>
            Returns an dpALL object that provides access to decoder parameters for all
            decoders. With this object all decoders can be enabled or disabled.
            </summary>
            <value>
            A dpALL object containing decoder parameters for all decoders.
            </value>
        </member>
        <member name="P:Symbol.Barcode.DecoderAPI.DEFAULT">
            <summary>
            Returns a dpDEFAULT object that provides access to all decoders that 
            are enabled by default. The enabled and disabled state of these "default"
            decoders can be set through this object.
            </summary>
            <value>
            A dpDEFAULT object containing decoder parameters for all decoders that
            are enabled by default.
            </value>
        </member>
        <member name="P:Symbol.Barcode.DecoderAPI.UPCE0">
            <summary>
            Parameters for the this decoder can be viewed/modified using this object.
            </summary>
            <value>
            A dpUPCE0 object containing decoder parameters for the UPCE0 decoder.
            </value>
        </member>
        <member name="P:Symbol.Barcode.DecoderAPI.UPCE1">
            <summary>
            Parameters for the this decoder can be viewed/modified using this object.
            </summary>
            <value>
            A dpUPCE1 object containing decoder parameters for the UPCE1 decoder.
            </value>
        </member>
        <member name="P:Symbol.Barcode.DecoderAPI.UPCA">
            <summary>
            Parameters for the this decoder can be viewed/modified using this object.
            </summary>
            <value>
            A dpUPCEA object containing decoder parameters for the UPCEA decoder.
            </value>
        </member>
        <member name="P:Symbol.Barcode.DecoderAPI.MSI">
            <summary>
            Parameters for the this decoder can be viewed/modified using this object.
            </summary>
            <value>
            A dpMSI object containing decoder parameters for the MSI decoder.
            </value>
        </member>
        <member name="P:Symbol.Barcode.DecoderAPI.EAN8">
            <summary>
            Parameters for the this decoder can be viewed/modified using this object.
            </summary>
            <value>
            A dpEAN8 object containing decoder parameters for the EAN8 decoder.
            </value>
        </member>
        <member name="P:Symbol.Barcode.DecoderAPI.EAN13">
            <summary>
            Parameters for the this decoder can be viewed/modified using this object.
            </summary>
            <value>
            A dpEAN13 object containing decoder parameters for the EAN13 decoder.
            </value>
        </member>
        <member name="P:Symbol.Barcode.DecoderAPI.CODABAR">
            <summary>
            Parameters for the this decoder can be viewed/modified using this object.
            </summary>
            <value>
            A dpCODABAR object containing decoder parameters for the CODABAR decoder.
            </value>
        </member>
        <member name="P:Symbol.Barcode.DecoderAPI.CODE39">
            <summary>
            Parameters for the this decoder can be viewed/modified using this object.
            </summary>
            <value>
            A dpCODE39 object containing decoder parameters for the CODE39 decoder.
            </value>
        </member>
        <member name="P:Symbol.Barcode.DecoderAPI.D2OF5">
            <summary>
            Parameters for the this decoder can be viewed/modified using this object.
            </summary>
            <value>
            A dpD2OF5 object containing decoder parameters for the D2OF5 decoder.
            </value>
        </member>
        <member name="P:Symbol.Barcode.DecoderAPI.I2OF5">
            <summary>
            Parameters for the this decoder can be viewed/modified using this object.
            </summary>
            <value>
            A dpI2OF5 object containing decoder parameters for the I2OF5 decoder.
            </value>
        </member>
        <member name="P:Symbol.Barcode.DecoderAPI.CODE11">
            <summary>
            Parameters for the this decoder can be viewed/modified using this object.
            </summary>
            <value>
            A dpCODE11 object containing decoder parameters for the CODE11 decoder.
            </value>
        </member>
        <member name="P:Symbol.Barcode.DecoderAPI.CODE93">
            <summary>
            Parameters for the this decoder can be viewed/modified using this object.
            </summary>
            <value>
            A dpCODE93 object containing decoder parameters for the CODE93 decoder.
            </value>
        </member>
        <member name="P:Symbol.Barcode.DecoderAPI.CODE128">
            <summary>
            Parameters for the this decoder can be viewed/modified using this object.
            </summary>
            <value>
            A dpCODE128 object containing decoder parameters for the CODE128 decoder.
            </value>
        </member>
        <member name="P:Symbol.Barcode.DecoderAPI.PDF417">
            <summary>
            Parameters for the this decoder can be viewed/modified using this object.
            </summary>
            <value>
            A dpPDF417 object containing decoder parameters for the PDF417 decoder.
            </value>
        </member>
        <member name="P:Symbol.Barcode.DecoderAPI.TRIOPTIC39">
            <summary>
            Parameters for the this decoder can be viewed/modified using this object.
            </summary>
            <value>
            A dpTRIOPTIC39 object containing decoder parameters for the TRIOPTIC39
            decoder.
            </value>
        </member>
        <member name="P:Symbol.Barcode.DecoderAPI.MICROPDF">
            <summary>
            Parameters for the this decoder can be viewed/modified using this object.
            </summary>
            <value>
            A dpMICROPDF object containing decoder parameters for the MICROPDF decoder.
            </value>
        </member>
        <member name="P:Symbol.Barcode.DecoderAPI.MACROPDF">
            <summary>
            Parameters for the this decoder can be viewed/modified using this object.
            </summary>
            <value>
            A dpMACROPDF object containing decoder parameters for the MACROPDF decoder.
            </value>
        </member>
        <member name="P:Symbol.Barcode.DecoderAPI.MAXICODE">
            <summary>
            Parameters for the this decoder can be viewed/modified using this object.
            </summary>
            <value>
            A dpMAXICODE object containing decoder parameters for the MAXICODE decoder.
            </value>
        </member>
        <member name="P:Symbol.Barcode.DecoderAPI.DATAMATRIX">
            <summary>
            Parameters for the this decoder can be viewed/modified using this object.
            </summary>
            <value>
            A dpDATAMATRIX object containing decoder parameters for the DATAMATRIX decoder.
            </value>
        </member>
        <member name="P:Symbol.Barcode.DecoderAPI.QRCODE">
            <summary>
            Parameters for the this decoder can be viewed/modified using this object.
            </summary>
            <value>
            A dpQRCODE object containing decoder parameters for the QRCODE decoder.
            </value>
        </member>
        <member name="P:Symbol.Barcode.DecoderAPI.MACROMICROPDF">
            <summary>
            Parameters for the this decoder can be viewed/modified using this object.
            </summary>
            <value>
            A dpMACROMICROPDF object containing decoder parameters for the 
            MACROMICROPDF decoder.
            </value>
        </member>
        <member name="P:Symbol.Barcode.DecoderAPI.RSS14">
            <summary>
            Parameters for the this decoder can be viewed/modified using this object.
            </summary>
            <value>
            A dpRSS14 object containing decoder parameters for the RSS14 decoder.
            </value>
        </member>
        <member name="P:Symbol.Barcode.DecoderAPI.RSSLIM">
            <summary>
            Parameters for the this decoder can be viewed/modified using this object.
            </summary>
            <value>
            A dpRSSLIM object containing decoder parameters for the RSSLIM decoder.
            </value>
        </member>
        <member name="P:Symbol.Barcode.DecoderAPI.RSSEXP">
            <summary>
            Parameters for the this decoder can be viewed/modified using this object.
            </summary>
            <value>
            A dpRSSEXP object containing decoder parameters for the RSSEXP decoder.
            </value>
        </member>
        <member name="P:Symbol.Barcode.DecoderAPI.WEBCODE">
            <summary>
            Parameters for the this decoder can be viewed/modified using this object.
            </summary>
            <value>
            A dpWEBCODE object containing decoder parameters for the WEBCODE decoder.
            </value>
        </member>
        <member name="P:Symbol.Barcode.DecoderAPI.COMPOSITE_AB">
            <summary>
            Parameters for the this decoder can be viewed/modified using this object.
            </summary>
            <value>
            A dpCOMPOSITE_AB object containing decoder parameters for the COMPOSITE_AB decoder.
            </value>
        </member>
        <member name="P:Symbol.Barcode.DecoderAPI.COMPOSITE_C">
            <summary>
            Parameters for the this decoder can be viewed/modified using this object.
            </summary>
            <value>
            A dpCOMPOSITE_C object containing decoder parameters for the COMPOSITE_C decoder.
            </value>
        </member>
        <member name="P:Symbol.Barcode.DecoderAPI.TLC39">
            <summary>
            Parameters for the this decoder can be viewed/modified using this object.
            </summary>
            <value>
            A dpTLC39 object containing decoder parameters for the TLC39 decoder.
            </value>
        </member>
        <member name="P:Symbol.Barcode.DecoderAPI.USPOSTNET">
            <summary>
            Parameters for the this decoder can be viewed/modified using this object.
            </summary>
            <value>
            A dpUSPOSTNET object containing decoder parameters for the USPOSTNET decoder.
            </value>
        </member>
        <member name="P:Symbol.Barcode.DecoderAPI.USPLANET">
            <summary>
            Parameters for the this decoder can be viewed/modified using this object.
            </summary>
            <value>
            A dpUSPLANET object containing decoder parameters for the USPLANET decoder.
            </value>
        </member>
        <member name="P:Symbol.Barcode.DecoderAPI.UKPOSTAL">
            <summary>
            Parameters for the this decoder can be viewed/modified using this object.
            </summary>
            <value>
            A dpUKPOSTAL object containing decoder parameters for the UKPOSTAL decoder.
            </value>
        </member>
        <member name="P:Symbol.Barcode.DecoderAPI.JAPPOSTAL">
            <summary>
            Parameters for the this decoder can be viewed/modified using this object.
            </summary>
            <value>
            A dpJAPPOSTAL object containing decoder parameters for the JAPPOSTAL decoder.
            </value>
        </member>
        <member name="P:Symbol.Barcode.DecoderAPI.AUSPOSTAL">
            <summary>
            Parameters for the this decoder can be viewed/modified using this object.
            </summary>
            <value>
            A dpAUSPOSTAL object containing decoder parameters for the AUSPOSTAL decoder.
            </value>
        </member>
        <member name="P:Symbol.Barcode.DecoderAPI.DUTCHPOSTAL">
            <summary>
            Parameters for the this decoder can be viewed/modified using this object.
            </summary>
            <value>
            A dpDUTCHPOSTAL object containing decoder parameters for the DUTCHPOSTAL
            decoder.
            </value>
        </member>
        <member name="P:Symbol.Barcode.DecoderAPI.CANPOSTAL">
            <summary>
            Parameters for the this decoder can be viewed/modified using this object.
            </summary>
            <value>
            A dpCANPOSTAL object containing decoder parameters for the CANPOSTAL decoder.
            </value>
        </member>
        <member name="P:Symbol.Barcode.DecoderAPI.AZTEC">
            <summary>
            Parameters for the this decoder can be viewed/modified using this object.
            </summary>
            <value>
            A dpAZTEC object containing decoder parameters for the AZTEC decoder.
            </value>
        </member>
        <member name="P:Symbol.Barcode.DecoderAPI.MICROQR">
            <summary>
            Parameters for the this decoder can be viewed/modified using this object.
            </summary>
            <value>
            A dpMICROQR object containing decoder parameters for the MICROQR decoder.
            </value>
        </member>
        <member name="P:Symbol.Barcode.DecoderAPI.KOREAN_3OF5">
            <summary>
            Parameters for the this decoder can be viewed/modified using this object.
            </summary>
            <value>
            A dpKOREAN_3OF5 object containing decoder parameters for the KOREAN_3OF5 decoder.
            </value>
        </member>
        <member name="P:Symbol.Barcode.DecoderAPI.US4STATE">
            <summary>
            Parameters for the this decoder can be viewed/modified using this object.
            </summary>
            <value>
            A dpUS4STATE object containing decoder parameters for the US4STATE decoder.
            </value>
        </member>
        <member name="P:Symbol.Barcode.DecoderAPI.US4STATE_FICS">
            <summary>
            Parameters for the this decoder can be viewed/modified using this object.
            </summary>
            <value>
            A dpUS4STATE_FICS object containing decoder parameters for the US4STATE_FICS decoder.
            </value>
        </member>
        <member name="P:Symbol.Barcode.DecoderAPI.MATRIX_2OF5">
            <summary>
            Parameters for the this decoder can be viewed/modified using this object.
            </summary>
            <value>
            A dpMATRIX_2OF5 object containing decoder parameters for the MATRIX_2OF5 decoder.
            </value>
        </member>
        <member name="P:Symbol.Barcode.DecoderAPI.CHINESE_2OF5">
            <summary>
            Parameters for the this decoder can be viewed/modified using this object.
            </summary>
            <value>
            A dpCHINESE_2OF5 object containing decoder parameters for the CHINESE_2OF5 decoder.
            </value>
        </member>
        <member name="P:Symbol.Barcode.DecoderAPI.CUECODE">
            <summary>
            Parameters for the this decoder can be viewed/modified using this object.
            </summary>
            <value>
            A dpCUECODE object containing decoder parameters for the CUECODE decoder.
            </value>
        </member>
        <member name="P:Symbol.Barcode.DecoderAPI.SIGNATURE">
            <summary>
            Parameters for the this decoder can be viewed/modified using this object.
            </summary>
            <value>
            A dpSIGNATURE object containing decoder parameters for the SIGNATURE decoder.
            </value>
        </member>
        <member name="P:Symbol.Barcode.DecoderAPI.POINTER">
            <summary>
            Parameters for the this decoder can be viewed/modified using this object.
            </summary>
            <value>
            A dpPOINTER object containing decoder parameters for the POINTER decoder.
            </value>
        </member>
        <member name="T:Symbol.Barcode.DecoderAPI.dpALL">
            <summary>
            The dpALL class provides access to parameters that are standard across
            all decoders. When these parameters are modified, all decoders are modified.
            </summary>
            <remarks> 
            The dpALL class is normally constructed by the DecoderParamsAPI class, which
            is constructed by the Reader. Therefore, access to these parameters should be
            made through the Reader.Decoders.ALL property.
            </remarks>
        </member>
        <member name="M:Symbol.Barcode.DecoderAPI.dpALL.#ctor(Symbol.Barcode.Reader)">
            <summary>
            The dpALL constructor should not normally be called. 
            </summary>
            <param name="clsReader">The Reader object in which decoder information 
            should come from.</param>
        </member>
        <member name="P:Symbol.Barcode.DecoderAPI.dpALL.Enabled">
            <summary>
            The Enabled property when set to true enables all decoders, when false all 
            decoders are disabled.
            </summary>
            <value>
            Boolean flag that specifies state (true=enable,false=disable).
            </value>
        </member>
        <member name="P:Symbol.Barcode.DecoderAPI.dpALL.MinimumLength">
            <summary>
            This property is not used. Returns 0 at all times.
            </summary>
        </member>
        <member name="P:Symbol.Barcode.DecoderAPI.dpALL.MaximumLength">
            <summary>
            This property is not used. Returns 0 at all times.
            </summary>
        </member>
        <member name="T:Symbol.Barcode.DecoderAPI.dpDEFAULT">
            <summary>
            The dpDEFAULT class provides access to parameters that are standard across
            all decoders that are enabled by default. When these parameters are modified,
            all of the default decoders are modified.
            </summary>
            <remarks> 
            The dpDEFAULT class is normally constructed by the DecoderParamsAPI class, which
            is constructed by the Reader. Therefore, access to these parameters should be
            made through the Reader.Decoders.DEFAULT property.
            </remarks>
        </member>
        <member name="M:Symbol.Barcode.DecoderAPI.dpDEFAULT.#ctor(Symbol.Barcode.Reader)">
            <summary>
            The dpDEFAULT constructor should not normally be called. 
            </summary>
            <param name="clsReader">The Reader object in which decoder information 
            should come from.</param>
        </member>
        <member name="P:Symbol.Barcode.DecoderAPI.dpDEFAULT.IsSupported">
            <summary>
            Returns whether or not the Decoder is supported by the Reader.
            With dpDEFAULT, this will be always true.
            </summary>
        </member>
        <member name="P:Symbol.Barcode.DecoderAPI.dpDEFAULT.Enabled">
            <summary>
            The Enabled property when set to true enables all decoders, when false all 
            decoders are disabled.
            </summary>
            <value>
            Boolean flag that specifies state (true=enable,false=disable).
            </value>
        </member>
        <member name="P:Symbol.Barcode.DecoderAPI.dpDEFAULT.MinimumLength">
            <summary>
            This property is not used. Returns 0 at all times.
            </summary>
        </member>
        <member name="P:Symbol.Barcode.DecoderAPI.dpDEFAULT.MaximumLength">
            <summary>
            This property is not used. Returns 0 at all times.
            </summary>
        </member>
        <member name="T:Symbol.Barcode.DecoderAPI.dpUPCE0">
            <summary>
            The dpUPCE0 class provides access to parameters that are available for 
            the UPCE0 decoder.
            </summary>
            <remarks> 
            The dpUPCE0 class is normally constructed by the DecoderParamsAPI class, which
            is constructed by the Reader. Therefore, access to these parameters should be
            made through the Reader.Decoders.UPCE0 property.
            </remarks>
        </member>
        <member name="M:Symbol.Barcode.DecoderAPI.dpUPCE0.#ctor(Symbol.Barcode.Reader)">
            <summary>
            The dpUPCE0 constructor should not normally be called. 
            </summary>
            <param name="clsReader">The Reader object in which decoder information 
            should come from.</param>
        </member>
        <member name="P:Symbol.Barcode.DecoderAPI.dpUPCE0.ReportCheckDigit">
            <summary>
            Flag to enable reporting the bar code check digit. 
            </summary>
            <value>
            A boolen flag that is true to enabled check digit reporting, false to
            disable check digit reporting.
            </value>
        </member>
        <member name="P:Symbol.Barcode.DecoderAPI.dpUPCE0.Preamble">
            <summary>
            Controls the preamble applied to the bar code. 
            </summary>
            <value>
            Refer to <see cref="T:Symbol.Barcode.UPC.Preambles"/> for
            possible preamble values.
            </value>
        </member>
        <member name="P:Symbol.Barcode.DecoderAPI.dpUPCE0.ConvertToUPCA">
            <summary>
            Flag to enable conversion from UPCE0 to UPCA bar code. If this flag
            is set, the bar code is converted to UPCA and UPCA parameters are used. 
            </summary>
            <value>
            A boolean flag that is true if conversion is on, false if conversion is
            off.
            </value>
        </member>
        <member name="P:Symbol.Barcode.DecoderAPI.dpUPCE0.MinimumLength">
            <summary>
            This property is not used. Returns 0 at all times.
            </summary>
        </member>
        <member name="P:Symbol.Barcode.DecoderAPI.dpUPCE0.MaximumLength">
            <summary>
            This property is not used. Returns 0 at all times.
            </summary>
        </member>
        <member name="T:Symbol.Barcode.DecoderAPI.dpUPCE1">
            <summary>
            The dpUPCE1 class provides access to parameters that are available for 
            the UPCE1 decoder.
            </summary>
            <remarks> 
            The dpUPCE1 class is normally constructed by the DecoderParamsAPI class, which
            is constructed by the Reader. Therefore, access to these parameters should be
            made through the Reader.Decoders.UPCE1 property.
            </remarks>
        </member>
        <member name="M:Symbol.Barcode.DecoderAPI.dpUPCE1.#ctor(Symbol.Barcode.Reader)">
            <summary>
            The dpUPCE1 constructor should not normally be called. 
            </summary>
            <param name="clsReader">The Reader object in which decoder information 
            should come from.
            </param>
        </member>
        <member name="P:Symbol.Barcode.DecoderAPI.dpUPCE1.ReportCheckDigit">
            <summary>
            Flag to enable reporting the bar code check digit. 
            </summary>
            <value>
            A boolen flag that is true to enabled check digit reporting, false to
            disable check digit reporting.
            </value>
        </member>
        <member name="P:Symbol.Barcode.DecoderAPI.dpUPCE1.Preamble">
            <summary>
            Controls the preamble applied to the bar code. 
            </summary>
            <value>
            Refer to <see cref="T:Symbol.Barcode.UPC.Preambles"/> for
            possible preamble values.
            </value>
        </member>
        <member name="P:Symbol.Barcode.DecoderAPI.dpUPCE1.ConvertToUPCA">
            <summary>
            Flag to enable conversion from UPCE1 to UPCA bar code. If this flag
            is set, the bar code is converted to UPCA and UPCA parameters are used. 
            </summary>
            <value>
            A boolean flag that is true if conversion is on, false if conversion is
            off.
            </value>
        </member>
        <member name="P:Symbol.Barcode.DecoderAPI.dpUPCE1.MinimumLength">
            <summary>
            This property is not used. Returns 0 at all times.
            </summary>
        </member>
        <member name="P:Symbol.Barcode.DecoderAPI.dpUPCE1.MaximumLength">
            <summary>
            This property is not used. Returns 0 at all times.
            </summary>
        </member>
        <member name="T:Symbol.Barcode.DecoderAPI.dpUPCA">
            <summary>
            The dpUPCA class provides access to parameters that are available for 
            the UPCA decoder.
            </summary>
            <remarks> 
            The dpUPCA class is normally constructed by the DecoderParamsAPI class, which
            is constructed by the Reader. Therefore, access to these parameters should be
            made through the Reader.Decoders.UPCA property.
            </remarks>
        </member>
        <member name="M:Symbol.Barcode.DecoderAPI.dpUPCA.#ctor(Symbol.Barcode.Reader)">
            <summary>
            The dpUPCE1 constructor should not normally be called. 
            </summary>
            <param name="clsReader">The Reader object in which decoder information 
            should come from.</param>
        </member>
        <member name="P:Symbol.Barcode.DecoderAPI.dpUPCA.ReportCheckDigit">
            <summary>
            Flag to enable reporting the bar code check digit. 
            </summary>
            <value>
            A boolen flag that is true to enabled check digit reporting, false to
            disable check digit reporting.
            </value>
        </member>
        <member name="P:Symbol.Barcode.DecoderAPI.dpUPCA.Preamble">
            <summary>
            Controls the preamble applied to the bar code. 
            </summary>
            <value>
            Refer to <see cref="T:Symbol.Barcode.UPC.Preambles"/> for
            possible preamble values.
            </value>
        </member>
        <member name="P:Symbol.Barcode.DecoderAPI.dpUPCA.MinimumLength">
            <summary>
            This property is not used. Returns 0 at all times.
            </summary>
        </member>
        <member name="P:Symbol.Barcode.DecoderAPI.dpUPCA.MaximumLength">
            <summary>
            This property is not used. Returns 0 at all times.
            </summary>
        </member>
        <member name="T:Symbol.Barcode.DecoderAPI.dpMSI">
            <summary>
            The dpMSI class provides access to parameters that are available for 
            the MSI decoder.
            </summary>
            <remarks> 
            The dpMSI class is normally constructed by the DecoderParamsAPI class, which
            is constructed by the Reader. Therefore, access to these parameters should be
            made through the Reader.Decoders.MSI property.
            </remarks>
        </member>
        <member name="M:Symbol.Barcode.DecoderAPI.dpMSI.#ctor(Symbol.Barcode.Reader)">
            <summary>
            The dpMSI constructor should not normally be called. 
            </summary>
            <param name="clsReader">The Reader object in which decoder information 
            should come from.</param>
        </member>
        <member name="P:Symbol.Barcode.DecoderAPI.dpMSI.Redundancy">
            <summary>
            A flag the sets the redundancy.
            </summary>
            <value>
            A boolean flag that when true the bar code must be decoded twice
            before being accepted.
            </value>
        </member>
        <member name="P:Symbol.Barcode.DecoderAPI.dpMSI.CheckDigitCount">
            <summary>
            The number of check digits to be verified.
            </summary>
            <value>
            A <see cref="T:Symbol.Barcode.MSI.CheckDigitCounts"/> enumerated type member that 
            indicates the number of check digits.
            </value>
        </member>
        <member name="P:Symbol.Barcode.DecoderAPI.dpMSI.CheckDigitScheme">
            <summary>
            The check digit scheme to verify.
            </summary>
            <value>
            A <see cref="T:Symbol.Barcode.MSI.CheckDigitSchemes"/> enumerated type member that 
            indicates the check digit scheme.
            </value>
        </member>
        <member name="P:Symbol.Barcode.DecoderAPI.dpMSI.ReportCheckDigit">
            <summary>
            Flag to enable reporting the bar code check digit. 
            </summary>
            <value>
            A boolen flag that is true to enabled check digit reporting, false to
            disable check digit reporting.
            </value>
        </member>
        <member name="P:Symbol.Barcode.DecoderAPI.dpMSI.MinimumLength">
            <summary>
            Returns the minimum length of a valid barcode for the decoder.
            </summary>
            <value>
            An integer, 0-55, representing the minimum size of the barcode.
            </value>
        </member>
        <member name="P:Symbol.Barcode.DecoderAPI.dpMSI.MaximumLength">
            <summary>
            Returns the maximum length of a valid barcode for the decoder.
            </summary>
            <value>
            An integer, 0-55, representing the maximum size of the barcode.
            </value>
        </member>
        <member name="T:Symbol.Barcode.DecoderAPI.dpEAN8">
            <summary>
            The dpEAN8 class provides access to parameters that are available for 
            the EAN8 decoder.
            </summary>
            <remarks> 
            The dpEAN8 class is normally constructed by the DecoderParamsAPI class, which
            is constructed by the Reader. Therefore, access to these parameters should be
            made through the Reader.Decoders.EAN8 property.
            </remarks>
        </member>
        <member name="M:Symbol.Barcode.DecoderAPI.dpEAN8.#ctor(Symbol.Barcode.Reader)">
            <summary>
            The dpEAN8 constructor should not normally be called. 
            </summary>
            <param name="clsReader">The Reader object in which decoder information 
            should come from.</param>
        </member>
        <member name="P:Symbol.Barcode.DecoderAPI.dpEAN8.ConvertToEAN13">
            <summary>
            Flag to enable conversion from EAN8 to EAN13 bar code.
            </summary>
            <value>
            A boolean flag that when set to true, the bar code is converted to EAN13
            and EAN13 parameters are used. 
            </value>
        </member>
        <member name="P:Symbol.Barcode.DecoderAPI.dpEAN8.MinimumLength">
            <summary>
            This property is not used. Returns 0 at all times.
            </summary>
        </member>
        <member name="P:Symbol.Barcode.DecoderAPI.dpEAN8.MaximumLength">
            <summary>
            This property is not used. Returns 0 at all times.
            </summary>
        </member>
        <member name="T:Symbol.Barcode.DecoderAPI.dpEAN13">
            <summary>
            The dpEAN13 class provides access to parameters that are available for 
            the EAN13 decoder.
            </summary>
            <remarks> 
            The dpEAN13 class is normally constructed by the DecoderParamsAPI class, which
            is constructed by the Reader. Therefore, access to these parameters should be
            made through the Reader.Decoders.EAN13 property.
            </remarks>
        </member>
        <member name="M:Symbol.Barcode.DecoderAPI.dpEAN13.#ctor(Symbol.Barcode.Reader)">
            <summary>
            The dpEAN13 constructor should not normally be called. 
            </summary>
            <param name="clsReader">The Reader object in which decoder information 
            should come from.</param>
        </member>
        <member name="P:Symbol.Barcode.DecoderAPI.dpEAN13.MinimumLength">
            <summary>
            This property is not used. Returns 0 at all times.
            </summary>
        </member>
        <member name="P:Symbol.Barcode.DecoderAPI.dpEAN13.MaximumLength">
            <summary>
            This property is not used. Returns 0 at all times.
            </summary>
        </member>
        <member name="T:Symbol.Barcode.DecoderAPI.dpCODABAR">
            <summary>
            The dpCODABAR class provides access to parameters that are available for 
            the CODABAR decoder.
            </summary>
            <remarks> 
            The dpCODABAR class is normally constructed by the DecoderParamsAPI class, which
            is constructed by the Reader. Therefore, access to these parameters should be
            made through the Reader.Decoders.CODABAR property.
            </remarks>
        </member>
        <member name="M:Symbol.Barcode.DecoderAPI.dpCODABAR.#ctor(Symbol.Barcode.Reader)">
            <summary>
            The dpCODABAR constructor should not normally be called. 
            </summary>
            <param name="clsReader">The Reader object in which decoder information 
            should come from.</param>
        </member>
        <member name="P:Symbol.Barcode.DecoderAPI.dpCODABAR.Redundancy">
            <summary>
            Flag to enable redundancy.
            </summary>
            <value>
            A boolean flag that when set to true, the bar code must be decoded
            twice before being accepted. 
            </value>
        </member>
        <member name="P:Symbol.Barcode.DecoderAPI.dpCODABAR.ClsiEditing">
            <summary>
            Flag to enable CLSI formatting.
            </summary>
            <value>
            A boolean flag that when set to true, CLSI formatting is turned on.
            </value>
        </member>
        <member name="P:Symbol.Barcode.DecoderAPI.dpCODABAR.NotisEditing">
            <summary>
            Flag to enable NOTIS formatting.
            </summary>
            <value>
            A boolean flag that when set to true, NOTIS formatting is turned on.
            </value>
        </member>
        <member name="P:Symbol.Barcode.DecoderAPI.dpCODABAR.MinimumLength">
            <summary>
            Returns the minimum length of a valid barcode for the decoder.
            </summary>
            <value>
            An integer, 0-55, representing the minimum size of the barcode.
            </value>
        </member>
        <member name="P:Symbol.Barcode.DecoderAPI.dpCODABAR.MaximumLength">
            <summary>
            Returns the maximum length of a valid barcode for the decoder.
            </summary>
            <value>
            An integer, 0-55, representing the maximum size of the barcode.
            </value>
        </member>
        <member name="T:Symbol.Barcode.DecoderAPI.dpCODE39">
            <summary>
            The dpCODE39 class provides access to parameters that are available for 
            the CODE39 decoder.
            </summary>
            <remarks> 
            The dpCODE39 class is normally constructed by the DecoderParamsAPI class, which
            is constructed by the Reader. Therefore, access to these parameters should be
            made through the Reader.Decoders.CODE39 property.
            </remarks>
        </member>
        <member name="M:Symbol.Barcode.DecoderAPI.dpCODE39.#ctor(Symbol.Barcode.Reader)">
            <summary>
            The dpCODE39 constructor should not normally be called. 
            </summary>
            <param name="clsReader">The Reader object in which decoder information 
            should come from.</param>
        </member>
        <member name="P:Symbol.Barcode.DecoderAPI.dpCODE39.VerifyCheckDigit">
            <summary>
            Flag to enable verification of the bar code check digit. 
            </summary>
            <value>
            A boolean flag that when true enables the verification of the check
            digit.
            </value>
        </member>
        <member name="P:Symbol.Barcode.DecoderAPI.dpCODE39.ReportCheckDigit">
            <summary>
            Flag to enable reporting the bar code check digit. 
            </summary>
            <value>
            A boolen flag that is true to enabled check digit reporting, false to
            disable check digit reporting.
            </value>
            <remarks>The ReportCheckDigit parameter requires the VerifyCheckDigit parameter to be enabled.
             Otherwise the check digit is always transmitted.</remarks>
        </member>
        <member name="P:Symbol.Barcode.DecoderAPI.dpCODE39.Concatenation">
            <summary>
            Flag to enable Code 39 barcode concatenation.
            </summary>
            <value>
            A boolean flag that when true enables Code 39 barcode concatenation.
            </value>
        </member>
        <member name="P:Symbol.Barcode.DecoderAPI.dpCODE39.FullAscii">
            <summary>
            Flag to enable full ASCII conversion of the bar code.
            </summary>
            <value>
            A boolean flag that when true enables the full ASCII conversion of
            the barcode.
            </value>
        </member>
        <member name="P:Symbol.Barcode.DecoderAPI.dpCODE39.Redundancy">
            <summary>
            Flag to enable redundancy.
            </summary>
            <value>
            A boolean flag that when set to true the barcode must be decoded
            twice before being accepted.
            </value>
        </member>
        <member name="P:Symbol.Barcode.DecoderAPI.dpCODE39.ConvertToCode32">
            <summary>
            Flag to enable conversion of Code 39 bar codes to Code 32.
            </summary>
            <value>
            A boolean flag that when set to true enables the conversion of the 
            Code 39 barcode to Code 32.
            </value>
        </member>
        <member name="P:Symbol.Barcode.DecoderAPI.dpCODE39.Code32Prefix">
            <summary>
            Flag to enable reporting the Code 32 prefix when a Code 39 bar code
            is converted.
            </summary>
            <value>
            A boolean flag that when true enables the reporting of Code 32 prefix
            when a Code 39 is converted.
            </value>
        </member>
        <member name="P:Symbol.Barcode.DecoderAPI.dpCODE39.MinimumLength">
            <summary>
            Returns the minimum length of a valid barcode for the decoder.
            </summary>
            <value>
            An integer, 0-55, representing the minimum size of the barcode.
            </value>
        </member>
        <member name="P:Symbol.Barcode.DecoderAPI.dpCODE39.MaximumLength">
            <summary>
            Returns the maximum length of a valid barcode for the decoder.
            </summary>
            <value>
            An integer, 0-55, representing the maximum size of the barcode.
            </value>
        </member>
        <member name="P:Symbol.Barcode.DecoderAPI.dpCODE39.SecurityLevel">
            <summary>
            Specifies the Code39 security level
            </summary>
        </member>
        <member name="T:Symbol.Barcode.DecoderAPI.dpD2OF5">
            <summary>
            The dpD2OF5 class provides access to parameters that are available for 
            the D2OF5 decoder.
            </summary>
            <remarks> 
            The dpD2OF5 class is normally constructed by the DecoderParamsAPI class, which
            is constructed by the Reader. Therefore, access to these parameters should be
            made through the Reader.Decoders.D2OF5 property.
            </remarks>
        </member>
        <member name="M:Symbol.Barcode.DecoderAPI.dpD2OF5.#ctor(Symbol.Barcode.Reader)">
            <summary>
            The dpD2OF5 constructor should not normally be called. 
            </summary>
            <param name="clsReader">The Reader object in which decoder information 
            should come from.</param>
        </member>
        <member name="P:Symbol.Barcode.DecoderAPI.dpD2OF5.Redundancy">
            <summary>
            Flag to enable redundancy.
            </summary>
            <value>
            A boolean flag that when set to true the barcode must be decoded
            twice before being accepted.
            </value>
        </member>
        <member name="P:Symbol.Barcode.DecoderAPI.dpD2OF5.MinimumLength">
            <summary>
            Returns the minimum length of a valid barcode for the decoder.
            </summary>
            <value>
            An integer, 0-55, representing the minimum size of the barcode.
            </value>
        </member>
        <member name="P:Symbol.Barcode.DecoderAPI.dpD2OF5.MaximumLength">
            <summary>
            Returns the maximum length of a valid barcode for the decoder.
            </summary>
            <value>
            An integer, 0-55, representing the maximum size of the barcode.
            </value>
        </member>
        <member name="T:Symbol.Barcode.DecoderAPI.dpI2OF5">
            <summary>
            The dpI2OF5 class provides access to parameters that are available for 
            the I2OF5 decoder.
            </summary>
            <remarks> 
            The dpI2OF5 class is normally constructed by the DecoderParamsAPI class, which
            is constructed by the Reader. Therefore, access to these parameters should be
            made through the Reader.Decoders.I2OF5 property.
            </remarks>
        </member>
        <member name="M:Symbol.Barcode.DecoderAPI.dpI2OF5.#ctor(Symbol.Barcode.Reader)">
            <summary>
            The dpI2OF5 constructor should not normally be called. 
            </summary>
            <param name="clsReader">The Reader object in which decoder information 
            should come from.</param>
        </member>
        <member name="P:Symbol.Barcode.DecoderAPI.dpI2OF5.Redundancy">
            <summary>
            Flag to enable redundancy.
            </summary>
            <value>
            A boolean flag that when set to true the barcode must be decoded
            twice before being accepted.
            </value>
        </member>
        <member name="P:Symbol.Barcode.DecoderAPI.dpI2OF5.CheckDigitScheme">
            <summary>
            The check digit scheme to verify.
            </summary>
            <value>
            A <see cref="T:Symbol.Barcode.I2OF5.CheckDigitSchemes"/> enumerated type member that 
            indicates the check digit scheme.
            </value>
        </member>
        <member name="P:Symbol.Barcode.DecoderAPI.dpI2OF5.ReportCheckDigit">
            <summary>
            Flag to enable reporting the bar code check digit. 
            </summary>
            <value>
            A boolen flag that is true to enabled check digit reporting, false to
            disable check digit reporting.
            </value>
        </member>
        <member name="P:Symbol.Barcode.DecoderAPI.dpI2OF5.ConvertToEAN13">
            <summary>
            Flag to enable conversion from I2OF5 to EAN13 bar code. 
            </summary>
            <value>
            A boolean flag that when set to true, the bar code is converted to
            EAN13 and EAN13 parameters are used.
            </value>
        </member>
        <member name="P:Symbol.Barcode.DecoderAPI.dpI2OF5.MinimumLength">
            <summary>
            Returns the minimum length of a valid barcode for the decoder.
            </summary>
            <value>
            An integer, 0-55, representing the minimum size of the barcode.
            </value>
        </member>
        <member name="P:Symbol.Barcode.DecoderAPI.dpI2OF5.MaximumLength">
            <summary>
            Returns the maximum length of a valid barcode for the decoder.
            </summary>
            <value>
            An integer, 0-55, representing the maximum size of the barcode.
            </value>
        </member>
        <member name="T:Symbol.Barcode.DecoderAPI.dpCODE11">
            <summary>
            The dpCODE11 class provides access to parameters that are available for 
            the CODE11 decoder.
            </summary>
            <remarks> 
            The dpCODE11 class is normally constructed by the DecoderParamsAPI class, which	
            is constructed by the Reader. Therefore, access to these parameters should be
            made through the Reader.Decoders.CODE11 property.
            </remarks>
        </member>
        <member name="M:Symbol.Barcode.DecoderAPI.dpCODE11.#ctor(Symbol.Barcode.Reader)">
            <summary>
            The dpCODE11 constructor should not normally be called. 
            </summary>
            <param name="clsReader">The Reader object in which decoder information 
            should come from.</param>
        </member>
        <member name="P:Symbol.Barcode.DecoderAPI.dpCODE11.Redundancy">
            <summary>
            Flag to enable redundancy.
            </summary>
            <value>
            A boolean flag that when set to true the barcode must be decoded
            twice before being accepted.
            </value>
        </member>
        <member name="P:Symbol.Barcode.DecoderAPI.dpCODE11.CheckDigitCount">
            <summary>
            The number of check digits to be verified.
            </summary>
            <value>
            A <see cref="T:Symbol.Barcode.CODE11.CheckDigitCounts"/> enumerated type member that 
            indicates the number of check digits.
            </value>
        </member>
        <member name="P:Symbol.Barcode.DecoderAPI.dpCODE11.ReportCheckDigit">
            <summary>
            Flag to enable reporting the bar code check digit. 
            </summary>
            <value>
            A boolen flag that is true to enabled check digit reporting, false to
            disable check digit reporting.
            </value>
        </member>
        <member name="P:Symbol.Barcode.DecoderAPI.dpCODE11.MinimumLength">
            <summary>
            Returns the minimum length of a valid barcode for the decoder.
            </summary>
            <value>
            An integer, 0-55, representing the minimum size of the barcode.
            </value>
        </member>
        <member name="P:Symbol.Barcode.DecoderAPI.dpCODE11.MaximumLength">
            <summary>
            Returns the maximum length of a valid barcode for the decoder.
            </summary>
            <value>
            An integer, 0-55, representing the maximum size of the barcode.
            </value>
        </member>
        <member name="T:Symbol.Barcode.DecoderAPI.dpCODE93">
            <summary>
            The dpCODE93 class provides access to parameters that are available for 
            the CODE93 decoder.
            </summary>
            <remarks> 
            The dpCODE93 class is normally constructed by the DecoderParamsAPI class, which	
            is constructed by the Reader. Therefore, access to these parameters should be
            made through the Reader.Decoders.CODE93 property.
            </remarks>
        </member>
        <member name="M:Symbol.Barcode.DecoderAPI.dpCODE93.#ctor(Symbol.Barcode.Reader)">
            <summary>
            The dpCODE93 constructor should not normally be called. 
            </summary>
            <param name="clsReader">The Reader object in which decoder information 
            should come from.</param>
        </member>
        <member name="P:Symbol.Barcode.DecoderAPI.dpCODE93.Redundancy">
            <summary>
            Flag to enable redundancy.
            </summary>
            <value>
            A boolean flag that when set to true the barcode must be decoded
            twice before being accepted.
            </value>
        </member>
        <member name="P:Symbol.Barcode.DecoderAPI.dpCODE93.MinimumLength">
            <summary>
            Returns the minimum length of a valid barcode for the decoder.
            </summary>
            <value>
            An integer, 0-55, representing the minimum size of the barcode.
            </value>
        </member>
        <member name="P:Symbol.Barcode.DecoderAPI.dpCODE93.MaximumLength">
            <summary>
            Returns the maximum length of a valid barcode for the decoder.
            </summary>
            <value>
            An integer, 0-55, representing the maximum size of the barcode.
            </value>
        </member>
        <member name="T:Symbol.Barcode.DecoderAPI.dpCODE128">
            <summary>
            The dpCODE128 class provides access to parameters that are available for 
            the CODE128 decoder.
            </summary>
            <remarks> 
            The dpCODE128 class is normally constructed by the DecoderParamsAPI class, which	
            is constructed by the Reader. Therefore, access to these parameters should be
            made through the Reader.Decoders.CODE128 property.
            </remarks>
        </member>
        <member name="M:Symbol.Barcode.DecoderAPI.dpCODE128.#ctor(Symbol.Barcode.Reader)">
            <summary>
            The dpCODE128 constructor should not normally be called. 
            </summary>
            <param name="clsReader">The Reader object in which decoder information 
            should come from.</param>
        </member>
        <member name="P:Symbol.Barcode.DecoderAPI.dpCODE128.Redundancy">
            <summary>
            Flag to enable redundancy.
            </summary>
            <value>
            A boolean flag that when set to true the barcode must be decoded
            twice before being accepted.
            </value>
        </member>
        <member name="P:Symbol.Barcode.DecoderAPI.dpCODE128.EAN128">
            <summary>
            Flag to enable EAN128 subtype. 
            </summary>
            <value>
            A boolean flag that when true enables the EAN128 subtype.
            </value>
        </member>
        <member name="P:Symbol.Barcode.DecoderAPI.dpCODE128.ISBT128">
            <summary>
            Flag to enable ISBT128 subtype. 
            </summary>
            <value>
            A boolean flag that when true enables the ISBT128 subtype.
            </value>
        </member>
        <member name="P:Symbol.Barcode.DecoderAPI.dpCODE128.Other128">
            <summary>
            Flag to enable other (non EAN or ISBT) 128 subtype. 
            </summary>
            <value>
            A boolean flag that when true enables the another (non EAN or ISBT) subtype.
            </value>
        </member>
        <member name="P:Symbol.Barcode.DecoderAPI.dpCODE128.ISBT128ConcatMode">
            <summary>
             The ISBT128 concatenation feature allows a pair of barcodes which meets a
             certain criteria defined in the ISBT128 spec to be reported as a single barcode.
             This parameter describes the different concatenation modes available for ISBT128. 
            </summary>
            <value>
            The value describing the different concatenation modes available for ISBT128.
            </value>
        </member>
        <member name="P:Symbol.Barcode.DecoderAPI.dpCODE128.CheckISBTTable">
            <summary>
             Will decode concatenated output only if the pair belongs to one of the 
             commonly concatenated pair as defined by the standard.
            </summary>
            <value>
            A boolean flag that when true enables the checking.
            </value>
        </member>
        <member name="P:Symbol.Barcode.DecoderAPI.dpCODE128.MinimumLength">
            <summary>
            Returns the minimum length of a valid barcode for the decoder.
            </summary>
            <value>
            An integer, 0-55, representing the minimum size of the barcode.
            </value>
        </member>
        <member name="P:Symbol.Barcode.DecoderAPI.dpCODE128.MaximumLength">
            <summary>
            Returns the maximum length of a valid barcode for the decoder.
            </summary>
            <value>
            An integer, 0-55, representing the maximum size of the barcode.
            </value>
        </member>
        <member name="P:Symbol.Barcode.DecoderAPI.dpCODE128.SecurityLevel">
            <summary>
            Specifies the Code128 security level
            </summary>
        </member>
        <member name="T:Symbol.Barcode.DecoderAPI.dpPDF417">
            <summary>
            The dpPDF417 class provides access to parameters that are available for 
            the PDF417 decoder.
            </summary>
            <remarks> 
            The dpPDF417 class is normally constructed by the DecoderParamsAPI class, which	
            is constructed by the Reader. Therefore, access to these parameters should be
            made through the Reader.Decoders.PDF417 property.
            </remarks>
        </member>
        <member name="M:Symbol.Barcode.DecoderAPI.dpPDF417.#ctor(Symbol.Barcode.Reader)">
            <summary>
            The dpPDF417 constructor should not normally be called. 
            </summary>
            <param name="clsReader">The Reader object in which decoder information 
            should come from.</param>
        </member>
        <member name="P:Symbol.Barcode.DecoderAPI.dpPDF417.MinimumLength">
            <summary>
            Returns the minimum length of a valid barcode for the decoder.
            </summary>
            <value>
            An integer, 0-55, representing the minimum size of the barcode.
            </value>
        </member>
        <member name="P:Symbol.Barcode.DecoderAPI.dpPDF417.MaximumLength">
            <summary>
            Returns the maximum length of a valid barcode for the decoder.
            </summary>
            <value>
            An integer, 0-55, representing the maximum size of the barcode.
            </value>
        </member>
        <member name="T:Symbol.Barcode.DecoderAPI.dpTRIOPTIC39">
            <summary>
            The dpTRIOPTIC39 class provides access to parameters that are available for 
            the TRIOPTIC39 decoder.
            </summary>
            <remarks> 
            The dpTRIOPTIC39 class is normally constructed by the DecoderParamsAPI class, which	
            is constructed by the Reader. Therefore, access to these parameters should be
            made through the Reader.Decoders.TRIOPTIC39 property.
            </remarks>
        </member>
        <member name="M:Symbol.Barcode.DecoderAPI.dpTRIOPTIC39.#ctor(Symbol.Barcode.Reader)">
            <summary>
            The dpTRIOPTIC39 constructor should not normally be called. 
            </summary>
            <param name="clsReader">The Reader object in which decoder information 
            should come from.</param>
        </member>
        <member name="P:Symbol.Barcode.DecoderAPI.dpTRIOPTIC39.Redundancy">
            <summary>
            Flag to enable redundancy.
            </summary>
            <value>
            A boolean flag that when set to true the barcode must be decoded
            twice before being accepted.
            </value>
        </member>
        <member name="P:Symbol.Barcode.DecoderAPI.dpTRIOPTIC39.MinimumLength">
            <summary>
            Returns the minimum length of a valid barcode for the decoder.
            </summary>
            <value>
            An integer, 0-55, representing the minimum size of the barcode.
            </value>
        </member>
        <member name="P:Symbol.Barcode.DecoderAPI.dpTRIOPTIC39.MaximumLength">
            <summary>
            Returns the maximum length of a valid barcode for the decoder.
            </summary>
            <value>
            An integer, 0-55, representing the maximum size of the barcode.
            </value>
        </member>
        <member name="T:Symbol.Barcode.DecoderAPI.dpMICROPDF">
            <summary>
            The dpMICROPDF class provides access to parameters that are available for 
            the MICROPDF decoder.
            </summary>
            <remarks> 
            The dpMICROPDF class is normally constructed by the DecoderParamsAPI class, which	
            is constructed by the Reader. Therefore, access to these parameters should be
            made through the Reader.Decoders.MICROPDF property.
            </remarks>
        </member>
        <member name="M:Symbol.Barcode.DecoderAPI.dpMICROPDF.#ctor(Symbol.Barcode.Reader)">
            <summary>
            The dpMICROPDF constructor should not normally be called. 
            </summary>
            <param name="clsReader">The Reader object in which decoder information 
            should come from.</param>
        </member>
        <member name="P:Symbol.Barcode.DecoderAPI.dpMICROPDF.MinimumLength">
            <summary>
            Returns the minimum length of a valid barcode for the decoder.
            </summary>
            <value>
            An integer, 0-55, representing the minimum size of the barcode.
            </value>
        </member>
        <member name="P:Symbol.Barcode.DecoderAPI.dpMICROPDF.MaximumLength">
            <summary>
            Returns the maximum length of a valid barcode for the decoder.
            </summary>
            <value>
            An integer, 0-55, representing the maximum size of the barcode.
            </value>
        </member>
        <member name="T:Symbol.Barcode.DecoderAPI.dpMACROPDF">
            <summary>
            The dpMACROPDF class provides access to parameters that are available for 
            the MACROPDF decoder.
            </summary>
            <remarks> 
            The dpMACROPDF class is normally constructed by the DecoderParamsAPI class, which	
            is constructed by the Reader. Therefore, access to these parameters should be
            made through the Reader.Decoders.MACROPDF property.
            </remarks>
        </member>
        <member name="M:Symbol.Barcode.DecoderAPI.dpMACROPDF.#ctor(Symbol.Barcode.Reader)">
            <summary>
            The dpMACROPDF constructor should not normally be called. 
            </summary>
            <param name="clsReader">The Reader object in which decoder information 
            should come from.</param>
        </member>
        <member name="P:Symbol.Barcode.DecoderAPI.dpMACROPDF.ReportAppendInfo">
            <summary>
            Flag to enable reporting of append information.
            </summary>
            <value>
            A boolean flag for reporting append information.
            </value>
        </member>
        <member name="P:Symbol.Barcode.DecoderAPI.dpMACROPDF.BufferLabels">
            <summary>
            Flag to enable buffering of labels.
            </summary>
            <value>
            A boolean flag for buffering labels.
            </value>
        </member>
        <member name="P:Symbol.Barcode.DecoderAPI.dpMACROPDF.ConvertToPDF417">
            <summary>
            Flag to enable conversion to PDF417 symbology.
            </summary>
            <value>
            A boolean flag for converting decode data to PDF417 symbology.
            </value>
        </member>
        <member name="P:Symbol.Barcode.DecoderAPI.dpMACROPDF.Exclusive">
            <summary>
            Flag to enable exclusive state.
            </summary>
            <value>
            A boolean flag for exclusive state.
            </value>
        </member>
        <member name="P:Symbol.Barcode.DecoderAPI.dpMACROPDF.MinimumLength">
            <summary>
            Returns the minimum length of a valid barcode for the decoder.
            </summary>
            <value>
            An integer, 0-55, representing the minimum size of the barcode.
            </value>
        </member>
        <member name="P:Symbol.Barcode.DecoderAPI.dpMACROPDF.MaximumLength">
            <summary>
            Returns the maximum length of a valid barcode for the decoder.
            </summary>
            <value>
            An integer, 0-55, representing the maximum size of the barcode.
            </value>
        </member>
        <member name="T:Symbol.Barcode.DecoderAPI.dpMAXICODE">
            <summary>
            The dpMAXICODE class provides access to parameters that are available for 
            the MAXICODE decoder.
            </summary>
            <remarks> 
            The dpMAXICODE class is normally constructed by the DecoderParamsAPI class, which	
            is constructed by the Reader. Therefore, access to these parameters should be
            made through the Reader.Decoders.MAXICODE property.
            </remarks>
        </member>
        <member name="M:Symbol.Barcode.DecoderAPI.dpMAXICODE.#ctor(Symbol.Barcode.Reader)">
            <summary>
            The dpMAXICODE constructor should not normally be called. 
            </summary>
            <param name="clsReader">The Reader object in which decoder information 
            should come from.</param>
        </member>
        <member name="P:Symbol.Barcode.DecoderAPI.dpMAXICODE.MinimumLength">
            <summary>
            Returns the minimum length of a valid barcode for the decoder.
            </summary>
            <value>
            An integer, 0-55, representing the minimum size of the barcode.
            </value>
        </member>
        <member name="P:Symbol.Barcode.DecoderAPI.dpMAXICODE.MaximumLength">
            <summary>
            Returns the maximum length of a valid barcode for the decoder.
            </summary>
            <value>
            An integer, 0-55, representing the maximum size of the barcode.
            </value>
        </member>
        <member name="T:Symbol.Barcode.DecoderAPI.dpDATAMATRIX">
            <summary>
            The dpDATAMATRIX class provides access to parameters that are available for 
            the DATAMATRIX decoder.
            </summary>
            <remarks> 
            The dpDATAMATRIX class is normally constructed by the DecoderParamsAPI class, which	
            is constructed by the Reader. Therefore, access to these parameters should be
            made through the Reader.Decoders.DATAMATRIX property.
            </remarks>
        </member>
        <member name="M:Symbol.Barcode.DecoderAPI.dpDATAMATRIX.#ctor(Symbol.Barcode.Reader)">
            <summary>
            The dpDATAMATRIX constructor should not normally be called. 
            </summary>
            <param name="clsReader">The Reader object in which decoder information 
            should come from.</param>
        </member>
        <member name="P:Symbol.Barcode.DecoderAPI.dpDATAMATRIX.MinimumLength">
            <summary>
            Returns the minimum length of a valid barcode for the decoder.
            </summary>
            <value>
            An integer, 0-55, representing the minimum size of the barcode.
            </value>
        </member>
        <member name="P:Symbol.Barcode.DecoderAPI.dpDATAMATRIX.MaximumLength">
            <summary>
            Returns the maximum length of a valid barcode for the decoder.
            </summary>
            <value>
            An integer, 0-55, representing the maximum size of the barcode.
            </value>
        </member>
        <member name="T:Symbol.Barcode.DecoderAPI.dpQRCODE">
            <summary>
            The dpQRCODE class provides access to parameters that are available for 
            the QRCODE decoder.
            </summary>
            <remarks> 
            The dpQRCODE class is normally constructed by the DecoderParamsAPI class, which	
            is constructed by the Reader. Therefore, access to these parameters should be
            made through the Reader.Decoders.QRCODE property.
            </remarks>
        </member>
        <member name="M:Symbol.Barcode.DecoderAPI.dpQRCODE.#ctor(Symbol.Barcode.Reader)">
            <summary>
            The dpQRCODE constructor should not normally be called. 
            </summary>
            <param name="clsReader">The Reader object in which decoder information 
            should come from.</param>
        </member>
        <member name="P:Symbol.Barcode.DecoderAPI.dpQRCODE.MinimumLength">
            <summary>
            Returns the minimum length of a valid barcode for the decoder.
            </summary>
            <value>
            An integer, 0-55, representing the minimum size of the barcode.
            </value>
        </member>
        <member name="P:Symbol.Barcode.DecoderAPI.dpQRCODE.MaximumLength">
            <summary>
            Returns the maximum length of a valid barcode for the decoder.
            </summary>
            <value>
            An integer, 0-55, representing the maximum size of the barcode.
            </value>
        </member>
        <member name="T:Symbol.Barcode.DecoderAPI.dpMACROMICROPDF">
            <summary>
            The dpMACROMICROPDF class provides access to parameters that are available for 
            the MACROMICRO decoder.
            </summary>
            <remarks> 
            The dpMACROMICROPDF class is normally constructed by the DecoderParamsAPI class, which	
            is constructed by the Reader. Therefore, access to these parameters should be
            made through the Reader.Decoders.MACROMICROPDF property.
            </remarks>
        </member>
        <member name="M:Symbol.Barcode.DecoderAPI.dpMACROMICROPDF.#ctor(Symbol.Barcode.Reader)">
            <summary>
            The dpMACROMICROPDF constructor should not normally be called. 
            </summary>
            <param name="clsReader">The Reader object in which decoder information 
            should come from.</param>
        </member>
        <member name="P:Symbol.Barcode.DecoderAPI.dpMACROMICROPDF.ReportAppendInfo">
            <summary>
            Flag to enable reporting of append information.
            </summary>
            <value>
            A boolean flag for reporting append information.
            </value>
        </member>
        <member name="P:Symbol.Barcode.DecoderAPI.dpMACROMICROPDF.BufferLabels">
            <summary>
            Flag to enable buffering of labels.
            </summary>
            <value>
            A boolean flag for buffering labels.
            </value>
        </member>
        <member name="P:Symbol.Barcode.DecoderAPI.dpMACROMICROPDF.ConvertToPDF417">
            <summary>
            Flag to enable conversion to MICROPDF symbology.
            </summary>
            <value>
            A boolean flag for converting decode data to MICROPDF symbology.
            </value>
        </member>
        <member name="P:Symbol.Barcode.DecoderAPI.dpMACROMICROPDF.Exclusive">
            <summary>
            Flag to enable exclusive state.
            </summary>
            <value>
            A boolean flag for exclusive state.
            </value>
        </member>
        <member name="P:Symbol.Barcode.DecoderAPI.dpMACROMICROPDF.MinimumLength">
            <summary>
            Returns the minimum length of a valid barcode for the decoder.
            </summary>
            <value>
            An integer, 0-55, representing the minimum size of the barcode.
            </value>
        </member>
        <member name="P:Symbol.Barcode.DecoderAPI.dpMACROMICROPDF.MaximumLength">
            <summary>
            Returns the maximum length of a valid barcode for the decoder.
            </summary>
            <value>
            An integer, 0-55, representing the maximum size of the barcode.
            </value>
        </member>
        <member name="T:Symbol.Barcode.DecoderAPI.dpRSS14">
            <summary>
            The dpRSS14 class provides access to parameters that are available for 
            the RSS14 decoder.
            </summary>
            <remarks> 
            The dpRSS14 class is normally constructed by the DecoderParamsAPI class, which	
            is constructed by the Reader. Therefore, access to these parameters should be
            made through the Reader.Decoders.RSS14 property.
            </remarks>
        </member>
        <member name="M:Symbol.Barcode.DecoderAPI.dpRSS14.#ctor(Symbol.Barcode.Reader)">
            <summary>
            The dpRSS14 constructor should not normally be called. 
            </summary>
            <param name="clsReader">The Reader object in which decoder information 
            should come from.</param>
        </member>
        <member name="P:Symbol.Barcode.DecoderAPI.dpRSS14.MinimumLength">
            <summary>
            Returns the minimum length of a valid barcode for the decoder.
            </summary>
            <value>
            An integer, 0-55, representing the minimum size of the barcode.
            </value>
        </member>
        <member name="P:Symbol.Barcode.DecoderAPI.dpRSS14.MaximumLength">
            <summary>
            Returns the maximum length of a valid barcode for the decoder.
            </summary>
            <value>
            An integer, 0-55, representing the maximum size of the barcode.
            </value>
        </member>
        <member name="T:Symbol.Barcode.DecoderAPI.dpRSSLIM">
            <summary>
            The dpRSSLIM class provides access to parameters that are available for 
            the RSSLIM decoder.
            </summary>
            <remarks> 
            The dpRSSLIM class is normally constructed by the DecoderParamsAPI class, which	
            is constructed by the Reader. Therefore, access to these parameters should be
            made through the Reader.Decoders.RSSLIM property.
            </remarks>
        </member>
        <member name="M:Symbol.Barcode.DecoderAPI.dpRSSLIM.#ctor(Symbol.Barcode.Reader)">
            <summary>
            The dpRSSLIM constructor should not normally be called. 
            </summary>
            <param name="clsReader">The Reader object in which decoder information 
            should come from.</param>
        </member>
        <member name="P:Symbol.Barcode.DecoderAPI.dpRSSLIM.MinimumLength">
            <summary>
            Returns the minimum length of a valid barcode for the decoder.
            </summary>
            <value>
            An integer, 0-55, representing the minimum size of the barcode.
            </value>
        </member>
        <member name="P:Symbol.Barcode.DecoderAPI.dpRSSLIM.MaximumLength">
            <summary>
            Returns the maximum length of a valid barcode for the decoder.
            </summary>
            <value>
            An integer, 0-55, representing the maximum size of the barcode.
            </value>
        </member>
        <member name="T:Symbol.Barcode.DecoderAPI.dpRSSEXP">
            <summary>
            The dpRSSEXP class provides access to parameters that are available for 
            the RSSEXP decoder.
            </summary>
            <remarks> 
            The dpRSSEXP class is normally constructed by the DecoderParamsAPI class, which	
            is constructed by the Reader. Therefore, access to these parameters should be
            made through the Reader.Decoders.RSSEXP property.
            </remarks>
        </member>
        <member name="M:Symbol.Barcode.DecoderAPI.dpRSSEXP.#ctor(Symbol.Barcode.Reader)">
            <summary>
            The dpRSSEXP constructor should not normally be called. 
            </summary>
            <param name="clsReader">The Reader object in which decoder information 
            should come from.</param>
        </member>
        <member name="P:Symbol.Barcode.DecoderAPI.dpRSSEXP.MinimumLength">
            <summary>
            Returns the minimum length of a valid barcode for the decoder.
            </summary>
            <value>
            An integer, 0-55, representing the minimum size of the barcode.
            </value>
        </member>
        <member name="P:Symbol.Barcode.DecoderAPI.dpRSSEXP.MaximumLength">
            <summary>
            Returns the maximum length of a valid barcode for the decoder.
            </summary>
            <value>
            An integer, 0-55, representing the maximum size of the barcode.
            </value>
        </member>
        <member name="T:Symbol.Barcode.DecoderAPI.dpWEBCODE">
            <summary>
            The dpWEBCODE class provides access to parameters that are available for 
            the WEBCODE decoder.
            </summary>
            <remarks> 
            The dpWEBCODE class is normally constructed by the DecoderParamsAPI class, which	
            is constructed by the Reader. Therefore, access to these parameters should be
            made through the Reader.Decoders.WEBCODE property.
            </remarks>
        </member>
        <member name="M:Symbol.Barcode.DecoderAPI.dpWEBCODE.#ctor(Symbol.Barcode.Reader)">
            <summary>
            The dpWEBCODE constructor should not normally be called. 
            </summary>
            <param name="clsReader">The Reader object in which decoder information 
            should come from.</param>
        </member>
        <member name="P:Symbol.Barcode.DecoderAPI.dpWEBCODE.MinimumLength">
            <summary>
            Returns the minimum length of a valid barcode for the decoder.
            </summary>
            <value>
            An integer, 0-55, representing the minimum size of the barcode.
            </value>
        </member>
        <member name="P:Symbol.Barcode.DecoderAPI.dpWEBCODE.MaximumLength">
            <summary>
            Returns the maximum length of a valid barcode for the decoder.
            </summary>
            <value>
            An integer, 0-55, representing the maximum size of the barcode.
            </value>
        </member>
        <member name="T:Symbol.Barcode.DecoderAPI.dpCOMPOSITE_AB">
            <summary>
            The dpCOMPOSITE_AB class provides access to parameters that are available for 
            the COMPOSITE_AB decoder.
            </summary>
            <remarks> 
            The dpCOMPOSITE_AB class is normally constructed by the DecoderParamsAPI class, which	
            is constructed by the Reader. Therefore, access to these parameters should be
            made through the Reader.Decoders.COMPOSITE_AB property.
            </remarks>
        </member>
        <member name="M:Symbol.Barcode.DecoderAPI.dpCOMPOSITE_AB.#ctor(Symbol.Barcode.Reader)">
            <summary>
            The dpCOMPOSITE_AB constructor should not normally be called. 
            </summary>
            <param name="clsReader">The Reader object in which decoder information 
            should come from.</param>
        </member>
        <member name="P:Symbol.Barcode.DecoderAPI.dpCOMPOSITE_AB.UCCLinkMode">
            <summary>
            The check digit scheme to verify.
            </summary>
            <value>
            A <see cref="T:Symbol.Barcode.COMPOSITE_AB.UCCLinkMode"/> enumerated type member that 
            indicates the check digit scheme.
            </value>
        </member>
        <member name="P:Symbol.Barcode.DecoderAPI.dpCOMPOSITE_AB.UseUPCPreambleCheckDigitRules">
            <summary>
            Enables the use of UPC rules specified in the UPC-EAN params 
            when reporting composite decode data.
            </summary>
            <value>
            A <see cref="T:Symbol.Barcode.DisabledEnabled"/> member that enables or disables this property 
            </value>
        </member>
        <member name="P:Symbol.Barcode.DecoderAPI.dpCOMPOSITE_AB.MinimumLength">
            <summary>
            This property is not used. Returns 0 at all times.
            </summary>
        </member>
        <member name="P:Symbol.Barcode.DecoderAPI.dpCOMPOSITE_AB.MaximumLength">
            <summary>
            This property is not used. Returns 0 at all times.
            </summary>
        </member>
        <member name="T:Symbol.Barcode.DecoderAPI.dpCOMPOSITE_C">
            <summary>
            The dpCOMPOSITE_C class provides access to parameters that are available for 
            the COMPOSITE_C decoder.
            </summary>
            <remarks> 
            The dpCOMPOSITE_C class is normally constructed by the DecoderParamsAPI class, which	
            is constructed by the Reader. Therefore, access to these parameters should be
            made through the Reader.Decoders.COMPOSITE_C property.
            </remarks>
        </member>
        <member name="M:Symbol.Barcode.DecoderAPI.dpCOMPOSITE_C.#ctor(Symbol.Barcode.Reader)">
            <summary>
            The dpCOMPOSITE_C constructor should not normally be called. 
            </summary>
            <param name="clsReader">The Reader object in which decoder information 
            should come from.</param>
        </member>
        <member name="P:Symbol.Barcode.DecoderAPI.dpCOMPOSITE_C.MinimumLength">
            <summary>
            This property is not used. Returns 0 at all times.
            </summary>
        </member>
        <member name="P:Symbol.Barcode.DecoderAPI.dpCOMPOSITE_C.MaximumLength">
            <summary>
            This property is not used. Returns 0 at all times.
            </summary>
        </member>
        <member name="T:Symbol.Barcode.DecoderAPI.dpTLC39">
            <summary>
            The dpTLC39 class provides access to parameters that are available for 
            the TLC39 decoder.
            </summary>
            <remarks> 
            The dpTLC39 class is normally constructed by the DecoderParamsAPI class, which	
            is constructed by the Reader. Therefore, access to these parameters should be
            made through the Reader.Decoders.TLC39 property.
            </remarks>
        </member>
        <member name="M:Symbol.Barcode.DecoderAPI.dpTLC39.#ctor(Symbol.Barcode.Reader)">
            <summary>
            The dpTLC39 constructor should not normally be called. 
            </summary>
            <param name="clsReader">The Reader object in which decoder information 
            should come from.</param>
        </member>
        <member name="P:Symbol.Barcode.DecoderAPI.dpTLC39.MinimumLength">
            <summary>
            This property is not used. Returns 0 at all times.
            </summary>
        </member>
        <member name="P:Symbol.Barcode.DecoderAPI.dpTLC39.MaximumLength">
            <summary>
            This property is not used. Returns 0 at all times.
            </summary>
        </member>
        <member name="T:Symbol.Barcode.DecoderAPI.dpUSPOSTNET">
            <summary>
            The dpUSPOSTNET class provides access to parameters that are available for 
            the USPOSTNET decoder.
            </summary>
            <remarks> 
            The dpUSPOSTNET class is normally constructed by the DecoderParamsAPI class, which	
            is constructed by the Reader. Therefore, access to these parameters should be
            made through the Reader.Decoders.USPOSTNET property.
            </remarks>
        </member>
        <member name="M:Symbol.Barcode.DecoderAPI.dpUSPOSTNET.#ctor(Symbol.Barcode.Reader)">
            <summary>
            The dpUSPOSTNET constructor should not normally be called. 
            </summary>
            <param name="clsReader">The Reader object in which decoder information 
            should come from.</param>
        </member>
        <member name="P:Symbol.Barcode.DecoderAPI.dpUSPOSTNET.MinimumLength">
            <summary>
            Returns the minimum length of a valid barcode for the decoder.
            </summary>
            <value>
            An integer, 0-55, representing the minimum size of the barcode.
            </value>
        </member>
        <member name="P:Symbol.Barcode.DecoderAPI.dpUSPOSTNET.MaximumLength">
            <summary>
            Returns the maximum length of a valid barcode for the decoder.
            </summary>
            <value>
            An integer, 0-55, representing the maximum size of the barcode.
            </value>
        </member>
        <member name="T:Symbol.Barcode.DecoderAPI.dpUSPLANET">
            <summary>
            The dpUSPLANET class provides access to parameters that are available for 
            the USPLANET decoder.
            </summary>
            <remarks> 
            The dpUSPLANET class is normally constructed by the DecoderParamsAPI class, which	
            is constructed by the Reader. Therefore, access to these parameters should be
            made through the Reader.Decoders.USPLANET property.
            </remarks>
        </member>
        <member name="M:Symbol.Barcode.DecoderAPI.dpUSPLANET.#ctor(Symbol.Barcode.Reader)">
            <summary>
            The dpUSPLANET constructor should not normally be called. 
            </summary>
            <param name="clsReader">The Reader object in which decoder information 
            should come from.</param>
        </member>
        <member name="P:Symbol.Barcode.DecoderAPI.dpUSPLANET.MinimumLength">
            <summary>
            Returns the minimum length of a valid barcode for the decoder.
            </summary>
            <value>
            An integer, 0-55, representing the minimum size of the barcode.
            </value>
        </member>
        <member name="P:Symbol.Barcode.DecoderAPI.dpUSPLANET.MaximumLength">
            <summary>
            Returns the maximum length of a valid barcode for the decoder.
            </summary>
            <value>
            An integer, 0-55, representing the maximum size of the barcode.
            </value>
        </member>
        <member name="T:Symbol.Barcode.DecoderAPI.dpUKPOSTAL">
            <summary>
            The dpUKPOSTAL class provides access to parameters that are available for 
            the UKPOSTAL decoder.
            </summary>
            <remarks> 
            The dpUKPOSTAL class is normally constructed by the DecoderParamsAPI class, which	
            is constructed by the Reader. Therefore, access to these parameters should be
            made through the Reader.Decoders.UKPOSTAL property.
            </remarks>
        </member>
        <member name="M:Symbol.Barcode.DecoderAPI.dpUKPOSTAL.#ctor(Symbol.Barcode.Reader)">
            <summary>
            The dpUKPOSTAL constructor should not normally be called. 
            </summary>
            <param name="clsReader">The Reader object in which decoder information 
            should come from.</param>
        </member>
        <member name="P:Symbol.Barcode.DecoderAPI.dpUKPOSTAL.MinimumLength">
            <summary>
            Returns the minimum length of a valid barcode for the decoder.
            </summary>
            <value>
            An integer, 0-55, representing the minimum size of the barcode.
            </value>
        </member>
        <member name="P:Symbol.Barcode.DecoderAPI.dpUKPOSTAL.MaximumLength">
            <summary>
            Returns the maximum length of a valid barcode for the decoder.
            </summary>
            <value>
            An integer, 0-55, representing the maximum size of the barcode.
            </value>
        </member>
        <member name="T:Symbol.Barcode.DecoderAPI.dpJAPPOSTAL">
            <summary>
            The dpJAPPOSTAL class provides access to parameters that are available for 
            the JAPOSTAL decoder.
            </summary>
            <remarks> 
            The dpJAPPOSTAL class is normally constructed by the DecoderParamsAPI class, which	
            is constructed by the Reader. Therefore, access to these parameters should be
            made through the Reader.Decoders.JAPPOSTAL property.
            </remarks>
        </member>
        <member name="M:Symbol.Barcode.DecoderAPI.dpJAPPOSTAL.#ctor(Symbol.Barcode.Reader)">
            <summary>
            The dpJAPPOSTAL constructor should not normally be called. 
            </summary>
            <param name="clsReader">The Reader object in which decoder information 
            should come from.</param>
        </member>
        <member name="P:Symbol.Barcode.DecoderAPI.dpJAPPOSTAL.MinimumLength">
            <summary>
            Returns the minimum length of a valid barcode for the decoder.
            </summary>
            <value>
            An integer, 0-55, representing the minimum size of the barcode.
            </value>
        </member>
        <member name="P:Symbol.Barcode.DecoderAPI.dpJAPPOSTAL.MaximumLength">
            <summary>
            Returns the maximum length of a valid barcode for the decoder.
            </summary>
            <value>
            An integer, 0-55, representing the maximum size of the barcode.
            </value>
        </member>
        <member name="T:Symbol.Barcode.DecoderAPI.dpAUSPOSTAL">
            <summary>
            The dpAUSPOSTAL class provides access to parameters that are available for 
            the AUSPOSTAL decoder.
            </summary>
            <remarks> 
            The dpAUSPOSTAL class is normally constructed by the DecoderParamsAPI class, which	
            is constructed by the Reader. Therefore, access to these parameters should be
            made through the Reader.Decoders.AUSPOSTAL property.
            </remarks>
        </member>
        <member name="M:Symbol.Barcode.DecoderAPI.dpAUSPOSTAL.#ctor(Symbol.Barcode.Reader)">
            <summary>
            The dpAUSPOSTAL constructor should not normally be called. 
            </summary>
            <param name="clsReader">The Reader object in which decoder information 
            should come from.</param>
        </member>
        <member name="P:Symbol.Barcode.DecoderAPI.dpAUSPOSTAL.MinimumLength">
            <summary>
            Returns the minimum length of a valid barcode for the decoder.
            </summary>
            <value>
            An integer, 0-55, representing the minimum size of the barcode.
            </value>
        </member>
        <member name="P:Symbol.Barcode.DecoderAPI.dpAUSPOSTAL.MaximumLength">
            <summary>
            Returns the maximum length of a valid barcode for the decoder.
            </summary>
            <value>
            An integer, 0-55, representing the maximum size of the barcode.
            </value>
        </member>
        <member name="T:Symbol.Barcode.DecoderAPI.dpDUTCHPOSTAL">
            <summary>
            The dpDUTCHPOSTAL class provides access to parameters that are available for 
            the DUTCHPOSTAL decoder.
            </summary>
            <remarks> 
            The dpDUTCHPOSTAL class is normally constructed by the DecoderParamsAPI class, which	
            is constructed by the Reader. Therefore, access to these parameters should be
            made through the Reader.Decoders.DUTCHPOSTAL property.
            </remarks>
        </member>
        <member name="M:Symbol.Barcode.DecoderAPI.dpDUTCHPOSTAL.#ctor(Symbol.Barcode.Reader)">
            <summary>
            The dpDUTCHPOSTAL constructor should not normally be called. 
            </summary>
            <param name="clsReader">The Reader object in which decoder information 
            should come from.</param>
        </member>
        <member name="P:Symbol.Barcode.DecoderAPI.dpDUTCHPOSTAL.MinimumLength">
            <summary>
            Returns the minimum length of a valid barcode for the decoder.
            </summary>
            <value>
            An integer, 0-55, representing the minimum size of the barcode.
            </value>
        </member>
        <member name="P:Symbol.Barcode.DecoderAPI.dpDUTCHPOSTAL.MaximumLength">
            <summary>
            Returns the maximum length of a valid barcode for the decoder.
            </summary>
            <value>
            An integer, 0-55, representing the maximum size of the barcode.
            </value>
        </member>
        <member name="T:Symbol.Barcode.DecoderAPI.dpCANPOSTAL">
            <summary>
            The dpCANPOSTAL class provides access to parameters that are available for 
            the CANPOSTAL decoder.
            </summary>
            <remarks> 
            The dpCANPOSTAL class is normally constructed by the DecoderParamsAPI class, which	
            is constructed by the Reader. Therefore, access to these parameters should be
            made through the Reader.Decoders.CANPOSTAL property.
            </remarks>
        </member>
        <member name="M:Symbol.Barcode.DecoderAPI.dpCANPOSTAL.#ctor(Symbol.Barcode.Reader)">
            <summary>
            The dpCANPOSTAL constructor should not normally be called. 
            </summary>
            <param name="clsReader">The Reader object in which decoder information 
            should come from.</param>
        </member>
        <member name="P:Symbol.Barcode.DecoderAPI.dpCANPOSTAL.MinimumLength">
            <summary>
            Returns the minimum length of a valid barcode for the decoder.
            </summary>
            <value>
            An integer, 0-55, representing the minimum size of the barcode.
            </value>
        </member>
        <member name="P:Symbol.Barcode.DecoderAPI.dpCANPOSTAL.MaximumLength">
            <summary>
            Returns the maximum length of a valid barcode for the decoder.
            </summary>
            <value>
            An integer, 0-55, representing the maximum size of the barcode.
            </value>
        </member>
        <member name="T:Symbol.Barcode.DecoderAPI.dpAZTEC">
            <summary>
            The dpAZTEC class provides access to parameters that are available for 
            the AZTEC decoder.
            </summary>
            <remarks> 
            The dpAZTEC class is normally constructed by the DecoderParamsAPI class, which	
            is constructed by the Reader. Therefore, access to these parameters should be
            made through the Reader.Decoders.AZTEC property.
            </remarks>
        </member>
        <member name="M:Symbol.Barcode.DecoderAPI.dpAZTEC.#ctor(Symbol.Barcode.Reader)">
            <summary>
            The dpAZTEC constructor should not normally be called. 
            </summary>
            <param name="clsReader">The Reader object in which decoder information 
            should come from.</param>
        </member>
        <member name="P:Symbol.Barcode.DecoderAPI.dpAZTEC.MinimumLength">
            <summary>
            Returns the minimum length of a valid barcode for the decoder.
            </summary>
            <value>
            An integer, 0-55, representing the minimum size of the barcode.
            </value>
        </member>
        <member name="P:Symbol.Barcode.DecoderAPI.dpAZTEC.MaximumLength">
            <summary>
            Returns the maximum length of a valid barcode for the decoder.
            </summary>
            <value>
            An integer, 0-55, representing the maximum size of the barcode.
            </value>
        </member>
        <member name="T:Symbol.Barcode.DecoderAPI.dpMICROQR">
            <summary>
            The dpMICROQR class provides access to parameters that are available for 
            the MICROQR decoder.
            </summary>
            <remarks> 
            The dpMICROQR class is normally constructed by the DecoderParamsAPI class, which	
            is constructed by the Reader. Therefore, access to these parameters should be
            made through the Reader.Decoders.MICROQR property.
            </remarks>
        </member>
        <member name="M:Symbol.Barcode.DecoderAPI.dpMICROQR.#ctor(Symbol.Barcode.Reader)">
            <summary>
            The dpMICROQR constructor should not normally be called. 
            </summary>
            <param name="clsReader">The Reader object in which decoder information 
            should come from.</param>
        </member>
        <member name="P:Symbol.Barcode.DecoderAPI.dpMICROQR.MinimumLength">
            <summary>
            Returns the minimum length of a valid barcode for the decoder.
            </summary>
            <value>
            An integer, 0-55, representing the minimum size of the barcode.
            </value>
        </member>
        <member name="P:Symbol.Barcode.DecoderAPI.dpMICROQR.MaximumLength">
            <summary>
            Returns the maximum length of a valid barcode for the decoder.
            </summary>
            <value>
            An integer, 0-55, representing the maximum size of the barcode.
            </value>
        </member>
        <member name="T:Symbol.Barcode.DecoderAPI.dpKOREAN_3OF5">
            <summary>
            The dpKOREAN_3OF5 class provides access to parameters that are available for 
            the KOREAN_3OF5 decoder.
            </summary>
            <remarks> 
            The dpKOREAN_3OF5 class is normally constructed by the DecoderParamsAPI class, which	
            is constructed by the Reader. Therefore, access to these parameters should be
            made through the Reader.Decoders.KOREAN_3OF5 property.
            </remarks>
        </member>
        <member name="M:Symbol.Barcode.DecoderAPI.dpKOREAN_3OF5.#ctor(Symbol.Barcode.Reader)">
            <summary>
            The dpKOREAN_3OF5 constructor should not normally be called. 
            </summary>
            <param name="clsReader">The Reader object in which decoder information 
            should come from.</param>
        </member>
        <member name="P:Symbol.Barcode.DecoderAPI.dpKOREAN_3OF5.Redundancy">
            <summary>
            A flag the sets the redundancy.
            </summary>
            <value>
            A boolean flag that when true the bar code must be decoded twice
            before being accepted.
            </value>
            <remarks>This flag is only considered if 
            LASER_SPECIFIC.dwLinearSecurityLevel = SECURITY_REDUNDANCY_AND_LENGTH ( = 0 ).</remarks>
        </member>
        <member name="P:Symbol.Barcode.DecoderAPI.dpKOREAN_3OF5.MinimumLength">
            <summary>
            Returns the minimum length of a valid barcode for the decoder.
            </summary>
            <value>
            An integer, 0-55, representing the minimum size of the barcode.
            </value>
        </member>
        <member name="P:Symbol.Barcode.DecoderAPI.dpKOREAN_3OF5.MaximumLength">
            <summary>
            Returns the maximum length of a valid barcode for the decoder.
            </summary>
            <value>
            An integer, 0-55, representing the maximum size of the barcode.
            </value>
        </member>
        <member name="T:Symbol.Barcode.DecoderAPI.dpUS4STATE">
            <summary>
            The dpUS4STATE class provides access to parameters that are available for 
            the US4STATE decoder.
            </summary>
            <remarks> 
            The dpUS4STATE class is normally constructed by the DecoderParamsAPI class, which	
            is constructed by the Reader. Therefore, access to these parameters should be
            made through the Reader.Decoders.US4STATE property.
            </remarks>
        </member>
        <member name="M:Symbol.Barcode.DecoderAPI.dpUS4STATE.#ctor(Symbol.Barcode.Reader)">
            <summary>
            The dpUS4STATE constructor should not normally be called. 
            </summary>
            <param name="clsReader">The Reader object in which decoder information 
            should come from.</param>
        </member>
        <member name="P:Symbol.Barcode.DecoderAPI.dpUS4STATE.MinimumLength">
            <summary>
            Returns the minimum length of a valid barcode for the decoder.
            </summary>
            <value>
            An integer, 0-55, representing the minimum size of the barcode.
            </value>
        </member>
        <member name="P:Symbol.Barcode.DecoderAPI.dpUS4STATE.MaximumLength">
            <summary>
            Returns the maximum length of a valid barcode for the decoder.
            </summary>
            <value>
            An integer, 0-55, representing the maximum size of the barcode.
            </value>
        </member>
        <member name="T:Symbol.Barcode.DecoderAPI.dpUS4STATE_FICS">
            <summary>
            The dpUS4STATE_FICS class provides access to parameters that are available for 
            the US4STATE_FICS decoder.
            </summary>
            <remarks> 
            The dpUS4STATE_FICS class is normally constructed by the DecoderParamsAPI class, which	
            is constructed by the Reader. Therefore, access to these parameters should be
            made through the Reader.Decoders.US4STATE_FICS property.
            </remarks>
        </member>
        <member name="M:Symbol.Barcode.DecoderAPI.dpUS4STATE_FICS.#ctor(Symbol.Barcode.Reader)">
            <summary>
            The dpUS4STATE_FICS constructor should not normally be called. 
            </summary>
            <param name="clsReader">The Reader object in which decoder information 
            should come from.</param>
        </member>
        <member name="P:Symbol.Barcode.DecoderAPI.dpUS4STATE_FICS.MinimumLength">
            <summary>
            Returns the minimum length of a valid barcode for the decoder.
            </summary>
            <value>
            An integer, 0-55, representing the minimum size of the barcode.
            </value>
        </member>
        <member name="P:Symbol.Barcode.DecoderAPI.dpUS4STATE_FICS.MaximumLength">
            <summary>
            Returns the maximum length of a valid barcode for the decoder.
            </summary>
            <value>
            An integer, 0-55, representing the maximum size of the barcode.
            </value>
        </member>
        <member name="T:Symbol.Barcode.DecoderAPI.dpMATRIX_2OF5">
            <summary>
            The dpMATRIX_2OF5 class provides access to parameters that are available for 
            the MATRIX_2OF5 decoder.
            </summary>
            <remarks> 
            The dpMATRIX_2OF5 class is normally constructed by the DecoderParamsAPI class, which	
            is constructed by the Reader. Therefore, access to these parameters should be
            made through the Reader.Decoders.MATRIX_2OF5 property.
            </remarks>
        </member>
        <member name="M:Symbol.Barcode.DecoderAPI.dpMATRIX_2OF5.#ctor(Symbol.Barcode.Reader)">
            <summary>
            The dpMATRIX_2OF5 constructor should not normally be called. 
            </summary>
            <param name="clsReader">The Reader object in which decoder information 
            should come from.</param>
        </member>
        <member name="P:Symbol.Barcode.DecoderAPI.dpMATRIX_2OF5.VerifyCheckDigit">
            <summary>
            Flag to enable verification of the bar code check digit. 
            </summary>
            <value>
            A boolean flag that when true enables the verification of the check
            digit.
            </value>
        </member>
        <member name="P:Symbol.Barcode.DecoderAPI.dpMATRIX_2OF5.ReportCheckDigit">
            <summary>
            Flag to enable reporting the bar code check digit. 
            </summary>
            <value>
            A boolen flag that is true to enabled check digit reporting, false to
            disable check digit reporting.
            </value>
            <remarks>The ReportCheckDigit parameter requires the VerifyCheckDigit parameter to be enabled.
             Otherwise the check digit is always transmitted.</remarks>
        </member>
        <member name="P:Symbol.Barcode.DecoderAPI.dpMATRIX_2OF5.MinimumLength">
            <summary>
            Returns the minimum length of a valid barcode for the decoder.
            </summary>
            <value>
            An integer, 0-55, representing the minimum size of the barcode.
            </value>
        </member>
        <member name="P:Symbol.Barcode.DecoderAPI.dpMATRIX_2OF5.MaximumLength">
            <summary>
            Returns the maximum length of a valid barcode for the decoder.
            </summary>
            <value>
            An integer, 0-55, representing the maximum size of the barcode.
            </value>
        </member>
        <member name="T:Symbol.Barcode.DecoderAPI.dpCHINESE_2OF5">
            <summary>
            The dpCHINESE_2OF5 class provides access to parameters that are available for 
            the CHINESE_2OF5 decoder.
            </summary>
            <remarks> 
            The dpCHINESE_2OF5 class is normally constructed by the DecoderParamsAPI class, which	
            is constructed by the Reader. Therefore, access to these parameters should be
            made through the Reader.Decoders.CHINESE_2OF5 property.
            </remarks>
        </member>
        <member name="M:Symbol.Barcode.DecoderAPI.dpCHINESE_2OF5.#ctor(Symbol.Barcode.Reader)">
            <summary>
            The dpCHINESE_2OF5 constructor should not normally be called. 
            </summary>
            <param name="clsReader">The Reader object in which decoder information 
            should come from.</param>
        </member>
        <member name="P:Symbol.Barcode.DecoderAPI.dpCHINESE_2OF5.MinimumLength">
            <summary>
            Returns the minimum length of a valid barcode for the decoder.
            </summary>
            <value>
            An integer, 0-55, representing the minimum size of the barcode.
            </value>
        </member>
        <member name="P:Symbol.Barcode.DecoderAPI.dpCHINESE_2OF5.MaximumLength">
            <summary>
            Returns the maximum length of a valid barcode for the decoder.
            </summary>
            <value>
            An integer, 0-55, representing the maximum size of the barcode.
            </value>
        </member>
        <member name="T:Symbol.Barcode.DecoderAPI.dpCUECODE">
            <summary>
            The dpCUECODE class provides access to parameters that are available for 
            the CUECODE decoder.
            </summary>
            <remarks> 
            The dpCUECODE class is normally constructed by the DecoderParamsAPI class, which	
            is constructed by the Reader. Therefore, access to these parameters should be
            made through the Reader.Decoders.CUECODE property.
            </remarks>
        </member>
        <member name="M:Symbol.Barcode.DecoderAPI.dpCUECODE.#ctor(Symbol.Barcode.Reader)">
            <summary>
            The dpCUECODE constructor should not normally be called. 
            </summary>
            <param name="clsReader">The Reader object in which decoder information 
            should come from.</param>
        </member>
        <member name="P:Symbol.Barcode.DecoderAPI.dpCUECODE.MinimumLength">
            <summary>
            Returns the minimum length of a valid barcode for the decoder.
            </summary>
            <value>
            An integer, 0-55, representing the minimum size of the barcode.
            </value>
        </member>
        <member name="P:Symbol.Barcode.DecoderAPI.dpCUECODE.MaximumLength">
            <summary>
            Returns the maximum length of a valid barcode for the decoder.
            </summary>
            <value>
            An integer, 0-55, representing the maximum size of the barcode.
            </value>
        </member>
        <member name="T:Symbol.Barcode.DecoderAPI.dpSIGNATURE">
            <summary>
            The dpSIGNATURE class provides access to parameters that are available for 
            the SIGNATURE decoder.
            </summary>
            <remarks> 
            The dpSIGNATURE class is normally constructed by the DecoderParamsAPI class, which	
            is constructed by the Reader. Therefore, access to these parameters should be
            made through the Reader.Decoders.SIGNATURE property.
            </remarks>
        </member>
        <member name="M:Symbol.Barcode.DecoderAPI.dpSIGNATURE.#ctor(Symbol.Barcode.Reader)">
            <summary>
            The dpSIGNATURE constructor should not normally be called. 
            </summary>
            <param name="clsReader">The Reader object in which decoder information 
            should come from.</param>
        </member>
        <member name="P:Symbol.Barcode.DecoderAPI.dpSIGNATURE.ImageFormat">
            <summary>
            Requested image file format.
            </summary>
        </member>
        <member name="P:Symbol.Barcode.DecoderAPI.dpSIGNATURE.ImageQuality">
            <summary>
            Quality of compressed JPEG image.  
            Range is  0 - 100, where 100 is highest quality image and 0 means optimize for image size.
            Note: JPEG image will be optimized for either Quality or Size. Size restrictions take priority over quality restrictions.
            </summary>
        </member>
        <member name="P:Symbol.Barcode.DecoderAPI.dpSIGNATURE.ImageSize">
            <summary>
            Maximum compressed JPEG image size in kilobytes (KB). 0 means optimize for image quality.
            Note: JPEG image will be optimized for either Quality or Size. Size restrictions take priority over quality restrictions.
            </summary>
        </member>
        <member name="P:Symbol.Barcode.DecoderAPI.dpSIGNATURE.ImageWidth">
            <summary>
            Specifies the desired output image width.
            </summary>
            <remarks> 
            The aspect ratio of ImageWidth to ImageHeight must match the aspect ratio of the signature capture bar code. 
            For example, a 4-by-1 inch signature capture area would require a 4-to-1 aspect ratio of the image width to height.
            </remarks>
        </member>
        <member name="P:Symbol.Barcode.DecoderAPI.dpSIGNATURE.ImageHeight">
            <summary>
            Specifies the desired output image height.
            </summary>
            <remarks> 
            The aspect ratio of ImageWidth to ImageHeight must match the aspect ratio of the signature capture bar code. 
            For example, a 4-by-1 inch signature capture area would require a 4-to-1 aspect ratio of the image width to height.
            </remarks>
        </member>
        <member name="P:Symbol.Barcode.DecoderAPI.dpSIGNATURE.MinimumLength">
            <summary>
            Returns the minimum length of a valid barcode for the decoder.
            </summary>
            <value>
            An integer, 0-55, representing the minimum size of the barcode.
            </value>
        </member>
        <member name="P:Symbol.Barcode.DecoderAPI.dpSIGNATURE.MaximumLength">
            <summary>
            Returns the maximum length of a valid barcode for the decoder.
            </summary>
            <value>
            An integer, 0-55, representing the maximum size of the barcode.
            </value>
        </member>
        <member name="T:Symbol.Barcode.DecoderAPI.dpPOINTER">
            <summary>
            The dpPOINTER class provides access to parameters that are available for 
            the POINTER decoder.
            </summary>
            <remarks> 
            The dpPOINTER class is normally constructed by the DecoderParamsAPI class, which	
            is constructed by the Reader. Therefore, access to these parameters should be
            made through the Reader.Decoders.POINTER property.
            </remarks>
        </member>
        <member name="M:Symbol.Barcode.DecoderAPI.dpPOINTER.#ctor(Symbol.Barcode.Reader)">
            <summary>
            The dpPOINTER constructor should not normally be called. 
            </summary>
            <param name="clsReader">The Reader object in which decoder information 
            should come from.</param>
        </member>
        <member name="P:Symbol.Barcode.DecoderAPI.dpPOINTER.MinimumLength">
            <summary>
            Returns the minimum length of a valid barcode for the decoder.
            </summary>
            <value>
            An integer, 0-55, representing the minimum size of the barcode.
            </value>
        </member>
        <member name="P:Symbol.Barcode.DecoderAPI.dpPOINTER.MaximumLength">
            <summary>
            Returns the maximum length of a valid barcode for the decoder.
            </summary>
            <value>
            An integer, 0-55, representing the maximum size of the barcode.
            </value>
        </member>
        <member name="T:Symbol.Barcode.UPCEANParamsAPI">
            <summary>
            The UPCEANParamsAPI class provides access to parameters that are available for 
            the UPC and EAN decoders.
            </summary>
            <remarks> 
            The UPCEANParamsAPI class is normally constructed by the Reader class and can be
            accessed through the Reader.UPCEANParams property. This class is derived from
            Symbol.API and can be used as with the StandardForms classes to create a UI
            interface for modifying/viewing the parameters.
            </remarks>
        </member>
        <member name="M:Symbol.Barcode.UPCEANParamsAPI.#ctor(Symbol.Barcode.Reader)">
            <summary>
            The UPCEANParamsAPI constructor should not normally be called. 
            </summary>
            <param name="clsReader">The Reader object from which decoder information 
            should come from.</param>
        </member>
        <member name="M:Symbol.Barcode.UPCEANParamsAPI.Save(System.Boolean)">
            <summary>
            Saves the parameters so that they can be restored later.
            </summary>
            <param name="bRecurseIntoSubClasses">When "true", the save procedure
            will recurse into the subclasses and save their parameters as well. 
            (i.e. perform a deep copy).
            </param>
            <returns>
            An object that represents the saved parameters.
            </returns>
        </member>
        <member name="M:Symbol.Barcode.UPCEANParamsAPI.Restore(System.Object)">
            <summary>
            Restores saved parameters. The object passed as
            a parameter should be obtained using the <see cref="M:Symbol.Barcode.UPCEANParamsAPI.Save(System.Boolean)"/> method.
            </summary>
            <param name="SaveInfo">An object obtained that contains the saved state</param>
        </member>
        <member name="P:Symbol.Barcode.UPCEANParamsAPI.Text">
            <summary>
            The Text property returns a friendly class name.
            </summary>
            <value>
            String returns "UPCEAN General".
            </value>
        </member>
        <member name="P:Symbol.Barcode.UPCEANParamsAPI.SecurityLevel">
            <summary>
            The UPC/EAN security level.
            </summary>
            <value>
            A <see cref="T:Symbol.Barcode.UPCEAN.SecurityLevels"/> member that indicates the type of 
            security.
            </value>
        </member>
        <member name="P:Symbol.Barcode.UPCEANParamsAPI.Supplemental2">
            <summary>
            Flag to enable length 2 supplementals. 
            </summary>
            <value>
            A <see cref="T:Symbol.Barcode.DisabledEnabled"/> member that indicates whether or not length
            2 supplementals are enabled.
            </value>
        </member>
        <member name="P:Symbol.Barcode.UPCEANParamsAPI.Supplemental5">
            <summary>
            Flag to enable length 5 supplementals. 
            </summary>
            <value>
            A <see cref="T:Symbol.Barcode.DisabledEnabled"/> member that indicates whether or not length
            5 supplementals are enabled.
            </value>
        </member>
        <member name="P:Symbol.Barcode.UPCEANParamsAPI.SupplementalMode">
            <summary>
            The supplemental mode. 
            </summary>
            <value>
            A <see cref="T:Symbol.Barcode.UPCEAN.SupplementalModes"/> member that indicates the current
            supplemental mode.
            </value>
        </member>
        <member name="P:Symbol.Barcode.UPCEANParamsAPI.SupplementalRetries">
            <summary>
            Retry count for auto-discriminating for supplementals. Possible values are 2 to 20 inclusive.
            Note that flag is only considered if <see cref="P:Symbol.Barcode.UPCEANParamsAPI.SupplementalMode"/> is set to one of the following values: SUPPLEMENTALS_ AUTO, SUPPLEMENTALS_SMART, SUPPLEMENTALS_378_379, SUPPLEMENTALS_978_979, SUPPLEMENTALS_977 or SUPPLEMENTALS_414_419_434_439. 
            </summary>
            <value>
            The number of retries. An integer type that can be 2 to 20 inclusive.
            </value>
        </member>
        <member name="P:Symbol.Barcode.UPCEANParamsAPI.RandomWeightCheckDigit">
            <summary>
            Flag to enable random weight check digit verification. 
            </summary>
            <value>
            A <see cref="T:Symbol.Barcode.DisabledEnabled"/> member that indicates whether or not random weight
            check digit verification is on.
            </value>
        </member>
        <member name="P:Symbol.Barcode.UPCEANParamsAPI.LinearDecode">
            <summary>
            Flag to enable linear decode.
            </summary>
            <value>
            A <see cref="T:Symbol.Barcode.DisabledEnabled"/> member that indicates whether or not linear 
            decode has been enabled.
            </value>
        </member>
        <member name="P:Symbol.Barcode.UPCEANParamsAPI.BooklandCode">
            <summary>
            Flag to enable Bookland code decoding.
            </summary>
            <value>
            A <see cref="T:Symbol.Barcode.DisabledEnabled"/> member that indicates whether or not Bookland 
            decoding has been enabled.
            </value>
        </member>
        <member name="P:Symbol.Barcode.UPCEANParamsAPI.CouponCode">
            <summary>
            Flag to enable Coupon Code decoding.
            </summary>
            <value>
            A <see cref="T:Symbol.Barcode.DisabledEnabled"/> member that indicates whether or not Coupon Code
            decoding has been enabled.
            </value>
        </member>
        <member name="P:Symbol.Barcode.UPCEANParamsAPI.BooklandFormat">
            <summary>
            Specifies the bookland format to use.
            </summary>
            <value>
            A <see cref="T:Symbol.Barcode.UPCEAN.BooklandFormat"/> member that indicates the current
            bookland format.
            </value>
        </member>
        <member name="P:Symbol.Barcode.UPCEANParamsAPI.ConvertRSSToUPCEAN">
            <summary>
            Flag to enable converting RSS barcodes to UPC/EAN format. 
            For this setting to work UPC/EAN symbologies must be enabled
            </summary>
            <value>
            A <see cref="T:Symbol.Barcode.DisabledEnabled"/> member that indicates whether or not, converting RSS barcodes to UPC/EAN format
            has been enabled.
            </value>
        </member>
        <member name="P:Symbol.Barcode.UPCEANParamsAPI.CouponReportMode">
            <summary>
            Used to differentiate between old coupon (UPC/EAN and Code128) and 
            new GS1 DataBar Coupons.
            NOTE: There is an Interim GS1 DataBar Coupon (UPC A and the GS1 DataBar),
            which is meant for smooth transition of adaption from old coupon format 
            to new coupon format. If an interim coupon is presented to the scanner, 
            it will read old or new potion of it, depending on the report mode setting.
            </summary>
            <value>
            A <see cref="T:Symbol.Barcode.UPCEAN.COUPON_REPORT_MODES"/> member that indicates the current
            coupon report mode.
            </value>
        </member>
        <member name="T:Symbol.Barcode.ScanParamsAPI">
            <summary>
            The ScanParamsAPI class provides access to scanning parameters that are available for 
            all decoders.
            </summary>
            <remarks> 
            The ScanParamsAPI class is normally constructed by the Reader class and can be
            accessed through the Reader.Parameters property. This class is derived from
            Symbol.API and can be used as with the StandardForms classes to create a UI
            interface for modifying/viewing the parameters.
            </remarks>
        </member>
        <member name="M:Symbol.Barcode.ScanParamsAPI.#ctor(Symbol.Barcode.Reader)">
            <summary>
            The ScanParamsAPI constructor should not normally be called. 
            </summary>
            <param name="r">The Reader object from which decoder information 
            should come from.</param>
        </member>
        <member name="M:Symbol.Barcode.ScanParamsAPI.Save(System.Boolean)">
            <summary>
            Saves the parameters so that they can be restored later.
            </summary>
            <param name="bRecurseIntoSubClasses">When "true", the save procedure
            will recurse into the subclasses and save their parameters as well. 
            (i.e. perform a deep copy).
            </param>
            <returns>
            An object that represents the saved parameters.
            </returns>
        </member>
        <member name="M:Symbol.Barcode.ScanParamsAPI.Restore(System.Object)">
            <summary>
            Restores saved parameters. The object passed as
            a parameter should be obtained using the <see cref="M:Symbol.Barcode.ScanParamsAPI.Save(System.Boolean)"/> method.
            </summary>
            <param name="SaveInfo">An object obtained that contains the saved state</param>
        </member>
        <member name="P:Symbol.Barcode.ScanParamsAPI.Text">
            <summary>
            A Text property that returns a friendly name for the class.
            </summary>
            <value>
            String that returns "Parameters".
            </value>
        </member>
        <member name="P:Symbol.Barcode.ScanParamsAPI.CodeIdType">
            <summary>
            The type of Code ID to be reported.
            </summary>
            <value>
            A <see cref="T:Symbol.Barcode.CodeIdTypes"/> member that sets the Code ID prefix to be reported.
            </value>
        </member>
        <member name="P:Symbol.Barcode.ScanParamsAPI.ScanType">
            <summary>
            The type of scan request. 
            </summary>
            <value>
            A <see cref="T:Symbol.Barcode.ScanTypes"/> member that indicates the type of scan to perform.
            </value>
        </member>
        <member name="P:Symbol.Barcode.ScanParamsAPI.LocalFeedback">
            <summary>
            Flag to enable local feedback.  
            </summary>
            <value>
            A <see cref="T:Symbol.Barcode.DisabledEnabled"/> member. When set to disabled, feedback is
            only done on the scanner device. Otherwise, feedback will be provided locally
            (e.g. at the Reader).
            </value>
        </member>
        <member name="P:Symbol.Barcode.ScanParamsAPI.Feedback">
            <summary>
            The Feedback property provides access to a LIST that contains the different
            types of notification events available when scanning. This list includes Success,
            Start, Proceed, Fail, among others.
            </summary>
            <value>
            Returns a <see cref="T:Symbol.Barcode.ScanParamsAPI.FeedbackListPropertyInfo"/> object. This object provides a
            list of feedback types that can be modified.
            </value>
        </member>
        <member name="T:Symbol.Barcode.ScanParamsAPI.FeedbackListPropertyInfo">
            <summary>
            The FeedbackListPropertyInfo class provides access to a LIST of feedback
            types. Each feedback type will contain a separate set of parameters which can 
            be modified to suit the needs of the scanning environment.
            </summary>
            <remarks> 
            The FeedbackListPropertyInfo class is normally constructed by the ScanParamsAPI
            class and can be accessed through the Reader.Parameters.Feedback property. This
            class is derived from Symbol.API and can be used as with the StandardForms
            classes to create a UI interface for modifying/viewing the parameters.
            </remarks>
        </member>
        <member name="M:Symbol.Barcode.ScanParamsAPI.FeedbackListPropertyInfo.#ctor(Symbol.Barcode.ScanParamsAPI)">
            <summary>
            The FeedbackListPropertyInfo constructor should not normally be called. 
            </summary>
            <param name="parent">The ScanParamsAPI object from which parameter information 
            should come from.</param>
        </member>
        <member name="M:Symbol.Barcode.ScanParamsAPI.FeedbackListPropertyInfo.Save(System.Boolean)">
            <summary>
            This class does not allow for the saving and restoring of state
            information.
            </summary>
            <param name="bRecurseIntoSubClasses">This parameter is not used.</param>
            <returns>This function simply returns a null.</returns>
        </member>
        <member name="M:Symbol.Barcode.ScanParamsAPI.FeedbackListPropertyInfo.Restore(System.Object)">
            <summary>
            This class does not allow for the saving and restoring of state
            information. Therefore, this method does nothing.
            </summary>
            <param name="SaveInfo">This parameter is not used.</param>
        </member>
        <member name="P:Symbol.Barcode.ScanParamsAPI.FeedbackListPropertyInfo.Text">
            <summary>
            A Text string that contains the Friendly name for this class.
            </summary>
            <value>
            String that returns "Feedback".
            </value>
        </member>
        <member name="P:Symbol.Barcode.ScanParamsAPI.FeedbackListPropertyInfo.Success">
            <summary>
            The Success property provides access to feedback parameters that can be set
            upon a successful decode. 
            </summary>
            <value>
            A <see cref="T:Symbol.Barcode.ScanParamsAPI.FeedbackPropertyInfo"/> object will be returned. This object can
            be used to set parameters about the feedback.
            </value>
        </member>
        <member name="P:Symbol.Barcode.ScanParamsAPI.FeedbackListPropertyInfo.Start">
            <summary>
            The Start property provides access to feedback parameters that can be set
            upon a start event. 
            </summary>
            <value>
            A <see cref="T:Symbol.Barcode.ScanParamsAPI.FeedbackPropertyInfo"/> object will be returned. This object can
            be used to set parameters about the feedback.
            </value>
        </member>
        <member name="P:Symbol.Barcode.ScanParamsAPI.FeedbackListPropertyInfo.Proceed">
            <summary>
            The Proceed property provides access to feedback parameters that can be set
            upon an intermediate event. 
            </summary>
            <value>
            A <see cref="T:Symbol.Barcode.ScanParamsAPI.FeedbackPropertyInfo"/> object will be returned. This object can
            be used to set parameters about the feedback.
            </value>
        </member>
        <member name="P:Symbol.Barcode.ScanParamsAPI.FeedbackListPropertyInfo.Fail">
            <summary>
            The Fail property provides access to feedback parameters that can be set
            upon a fatal error event. 
            </summary>
            <value>
            A <see cref="T:Symbol.Barcode.ScanParamsAPI.FeedbackPropertyInfo"/> object will be returned. This object can
            be used to set parameters about the feedback.
            </value>
        </member>
        <member name="P:Symbol.Barcode.ScanParamsAPI.FeedbackListPropertyInfo.Problem">
            <summary>
            The Problem property provides access to feedback parameters that can be set
            upon a non-fatal error event. 
            </summary>
            <value>
            A <see cref="T:Symbol.Barcode.ScanParamsAPI.FeedbackPropertyInfo"/> object will be returned. This object can
            be used to set parameters about the feedback.
            </value>
        </member>
        <member name="P:Symbol.Barcode.ScanParamsAPI.FeedbackListPropertyInfo.Progress">
            <summary>
            The Progress property provides access to feedback parameters that can be set
            upon an activity event. 
            </summary>
            <value>
            A <see cref="T:Symbol.Barcode.ScanParamsAPI.FeedbackPropertyInfo"/> object will be returned. This object can
            be used to set parameters about the feedback.
            </value>
        </member>
        <member name="T:Symbol.Barcode.ScanParamsAPI.FeedbackPropertyInfo">
            <summary>
            The FeedbackPropertyInfo class provides access to feedback parameters. 
            </summary>
            <remarks> 
            The FeedbackPropertyInfo class is normally constructed by the 
            FeedbackListPropertyInfo class and can be accessed through the 
            Reader.Parameters.Feedback.THE_FEEDBACK_TYPE properties (THE_FEEDBACK_PROPERTIES
            include Success, Fail, Proceed...etc.). This class is derived from Symbol.API
            and can be used as with the StandardForms classes to create a UI interface
            for modifying/viewing the parameters.
            </remarks>
        </member>
        <member name="M:Symbol.Barcode.ScanParamsAPI.FeedbackPropertyInfo.Save(System.Boolean)">
            <summary>
            Saves the parameters so that they can be restored later.
            </summary>
            <param name="bRecurseIntoSubClasses">When "true", the save procedure
            will recurse into the subclasses and save their parameters as well. 
            (i.e. perform a deep copy).
            </param>
            <returns>
            An object that represents the saved parameters.
            </returns>
        </member>
        <member name="M:Symbol.Barcode.ScanParamsAPI.FeedbackPropertyInfo.Restore(System.Object)">
            <summary>
            Restores saved parameters. The object passed as
            a parameter should be obtained using the <see cref="M:Symbol.Barcode.ScanParamsAPI.FeedbackPropertyInfo.Save(System.Boolean)"/> method.
            </summary>
            <param name="SaveInfo">An object obtained that contains the saved state</param>
        </member>
        <member name="P:Symbol.Barcode.ScanParamsAPI.FeedbackPropertyInfo.Text">
            <summary>
            A Text string that provides a friendly name for the class.
            </summary>
            <value>
            String that returns "Feedback".
            </value>
        </member>
        <member name="P:Symbol.Barcode.ScanParamsAPI.FeedbackPropertyInfo.BeepTime">
            <summary>
            Time to beep.
            </summary>
            <value>
            An integer that specifies the beep time in milliseconds (Range of 0-2000). 
            </value>
        </member>
        <member name="P:Symbol.Barcode.ScanParamsAPI.FeedbackPropertyInfo.BeepFrequency">
            <summary>
            Frequency of the beep sound.
            </summary>
            <value>
            An integer that specifies the frequency of the beep in hertz (Range of 2500-3500). 
            </value>
        </member>
        <member name="P:Symbol.Barcode.ScanParamsAPI.FeedbackPropertyInfo.LedTime">
            <summary>
            Time to light decode LED.
            </summary>
            <value>
            An integer that specifies the amount of time to light the decode LED in
            milliseconds (Range 0-5000).
            </value>
        </member>
        <member name="P:Symbol.Barcode.ScanParamsAPI.FeedbackPropertyInfo.WaveFile">
            <summary>
            Name of .WAV file to play as feedback.
            </summary>
            <value>
            A string that contains the absolute path to a .WAV file that will be played
            when the feedback event occurs.
            </value>
        </member>
        <member name="T:Symbol.Barcode.InterfaceParams">
            <summary>
            The InterfaceParams class holds the parameters for the interface to the attached bar code reader.
            Interface parameters are global to all reads on all open handles on the same scanner.
            
            NOTE: Beginning with the ScanMDD file version 6.01, the InterfaceParameters object can be accessed before or after enabling the 
            Reader object. In the earlier versions of ScanMDD, the InterfaceParameters object can only be accessed after enabling the Reader object. 
            The ScanMDD version can be retrieved using the ControlPanel tool available on the support central website.
            </summary>
        </member>
        <member name="P:Symbol.Barcode.InterfaceParams.InterfaceType">
            <summary>
            Provides the interface type which determines the specific interface. Read only. 
            This interface type determines the values for InterfaceSpecific.
            </summary>
        </member>
        <member name="P:Symbol.Barcode.InterfaceParams.InterfaceSpecific">
            <summary>
            The InterfaceSpecific holds parameters for one interface type. 
            The value of InterfaceType determines which specific interface parameters to use.
            </summary>
        </member>
        <member name="T:Symbol.Barcode.INTERFACE_TYPE">
            <summary>
            Enumerates the supported interface types.
            </summary>
        </member>
        <member name="F:Symbol.Barcode.INTERFACE_TYPE.INTERFACE_TYPE_QSNAC">
            <summary>
            QSNAC Interface
            </summary>
        </member>
        <member name="F:Symbol.Barcode.INTERFACE_TYPE.INTERFACE_TYPE_SSI">
            <summary>
            SSI Interface
            </summary>
        </member>
        <member name="F:Symbol.Barcode.INTERFACE_TYPE.INTERFACE_TYPE_LS48XX">
            <summary>
            LS48XX Interface
            </summary>
        </member>
        <member name="F:Symbol.Barcode.INTERFACE_TYPE.INTERFACE_TYPE_LIGHTHOUSE">
            <summary>
            Lighthouse Interface
            </summary>
        </member>
        <member name="F:Symbol.Barcode.INTERFACE_TYPE.INTERFACE_TYPE_CAMSCAN">
            <summary>
            Camscan Interface
            </summary>
        </member>
        <member name="T:Symbol.Barcode.InterfaceSpecific">
            <summary>
            The InterfaceSpecific holds parameters for one interface type. 
            The parameter values in InterfaceSpecific depends on the interface type. 
            </summary>
        </member>
        <member name="P:Symbol.Barcode.InterfaceSpecific.QSNACSpecific">
            <summary>
            Provides the QSNAC specific interface parameters
            </summary>
        </member>
        <member name="P:Symbol.Barcode.InterfaceSpecific.SSISpecific">
            <summary>
            Provides the SSI specific interface parameters
            </summary>
        </member>
        <member name="P:Symbol.Barcode.InterfaceSpecific.LS48XXSpecific">
            <summary>
            Provides the LS48XX specific interface parameters
            </summary>
        </member>
        <member name="P:Symbol.Barcode.InterfaceSpecific.LighthouseSpecific">
            <summary>
            Provides the Lighthouse specific interface parameters
            </summary>
        </member>
        <member name="P:Symbol.Barcode.InterfaceSpecific.CamScanSpecific">
            <summary>
            Provides the CamScan specific interface parameters
            </summary>
        </member>
        <member name="T:Symbol.Barcode.QSNACSpecific">
            <summary>
            Provides the QSNAC specific interface parameters
            </summary>
        </member>
        <member name="P:Symbol.Barcode.QSNACSpecific.EnableSettlingTime">
            <summary>
            Time in milliseconds after engine is enabled before the laser is turned on.
            </summary>
        </member>
        <member name="P:Symbol.Barcode.QSNACSpecific.InverseLabelData">
            <summary>
            Flag to enable decoding of inverse labels.
            </summary>
        </member>
        <member name="P:Symbol.Barcode.QSNACSpecific.WhiteDataLogicLevel">
            <summary>
            The white data logic level of the scanner device.
            </summary>
        </member>
        <member name="P:Symbol.Barcode.QSNACSpecific.TransitionResolution">
            <summary>
            Sampling clock divisor used for data acquisition.
            Possible values are 1 to 32 inclusive.
            </summary>
        </member>
        <member name="P:Symbol.Barcode.QSNACSpecific.PowerSettlingTime">
            <summary>
            Time in milliseconds after power is supplied before engine is ready for use.
            NOTE: Changing the default value is not recommended.
            </summary>
        </member>
        <member name="T:Symbol.Barcode.SSISpecific">
            <summary>
            Provides the SSI specific interface parameters
            </summary>
        </member>
        <member name="P:Symbol.Barcode.SSISpecific.PowerSettlingTime">
            <summary>
            Time in milliseconds after power is supplied before engine is ready for use.
            NOTE: Changing the default value is not recommended.
            </summary>
        </member>
        <member name="P:Symbol.Barcode.SSISpecific.PowerOffSettlingTime">
            <summary>
            Time to wait before re-powering.
            </summary>
        </member>
        <member name="P:Symbol.Barcode.SSISpecific.EstablishConnectionTime">
            <summary>
            Specifies the timeout (in seconds) that Reader.Actions.Enable() will block,  
            before returning an error if a remote scanner connection is not established.
            In order to use this parameter, the user should set the required value 
            to this property and call Reader.Actions.SetParameter(), before calling Actions.Enable().
            NOTE: This is currently applicable only to Bluetooth scanners such as RS507.
            </summary>
        </member>
        <member name="P:Symbol.Barcode.SSISpecific.ConnectionIdleTimeout">
            <summary>
            Specifies the timeout (in seconds), when a remote scanner is idle in a particular
            state (IDLE, WAITING, READY or AIMING), after which the connection between 
            the mobile computer and the remote scanner is severed to conserve power.
            NOTE: This is currently applicable only to Bluetooth scanners such as RS507.
            </summary>
        </member>
        <member name="P:Symbol.Barcode.SSISpecific.BTDisconnectOnExit">
            <summary>
            Specifies whether to disconnect any existing Bluetooth connection, between 
            a remote Bluetooth scanner and the mobile computer, when disabling the Reader object.
            True - Disconnect existing Bluetooth connection after disabling the Reader object.
            False - Do not disconnect existing Bluetooth connection after disabling the Reader object.
            NOTE: This is currently applicable only to Bluetooth scanners such as RS507.
            Setting this parameter will only persist for one session. To use this feature effectively,
            it is best to set this permanently using Motorola's CtlPanel tool which can be obtained 
            from the support central website.
            </summary>
        </member>
        <member name="P:Symbol.Barcode.SSISpecific.DisplayBTAddressBarcode">
            <summary>
            Displays the Bluetooth address of the mobile computer as a barcode, when calling Actions.Enable().
            When using Bluetooth scanners such as RS507, the pairing process between the mobile computer 
            and Bluetooth scanner may require scanning the Bluetooth address of the mobile computer.  
            If this parameter is set to TRUE, the Bluetooth address will be displayed as a barcode 
            when the enabling the Reader object. This feature eliminates the need to open external 
            tools such as BT_Information in order to display the pairing barcode.
            NOTE: This is currently applicable only to Bluetooth scanners such as RS507.
            </summary>
        </member>
        <member name="T:Symbol.Barcode.LS48XXSpecific">
            <summary>
            Provides the LS48XX specific interface parameters
            </summary>
        </member>
        <member name="P:Symbol.Barcode.LS48XXSpecific.PowerSettlingTime">
            <summary>
            Time in milliseconds after power is supplied before engine is ready for use.
            NOTE: Changing the default value is not recommended.
            </summary>
        </member>
        <member name="T:Symbol.Barcode.LighthouseSpecific">
            <summary>
            Provides the Lighthouse specific interface parameters
            </summary>
        </member>
        <member name="P:Symbol.Barcode.LighthouseSpecific.PowerSettlingTime">
            <summary>
            Time in milliseconds after power is supplied before engine is ready for use.
            NOTE: Changing the default value is not recommended.
            </summary>
        </member>
        <member name="P:Symbol.Barcode.LighthouseSpecific.EnableSettlingTime">
            <summary>
            Time in milliseconds after engine is enabled before the laser is turned on.
            </summary>
        </member>
        <member name="P:Symbol.Barcode.LighthouseSpecific.LowPowerTime">
            <summary>
            Time in milliseconds of non-use before dropping to a low-power mode.
            </summary>
        </member>
        <member name="T:Symbol.Barcode.CamscanSpecific">
            <summary>
            Provides the Camscan specific interface parameters
            </summary>
        </member>
        <member name="P:Symbol.Barcode.CamscanSpecific.EnableSettlingTime">
            <summary>
            Time in milliseconds after engine is enabled before the laser is turned on.
            </summary>
        </member>
        <member name="P:Symbol.Barcode.CamscanSpecific.PowerSettlingTime">
            <summary>
            Time in milliseconds after power is supplied before engine is ready for use.
            NOTE: Changing the default value is not recommended.
            </summary>
        </member>
        <member name="P:Symbol.Barcode.CamscanSpecific.LowPowerTime">
            <summary>
            Time in milliseconds of non-use before dropping to a low-power mode.
            </summary>
        </member>
        <member name="T:Symbol.Barcode.Actions">
            <summary>
            Performs actions on the Reader.
            </summary>
            <remarks>
            The Symbol.Barcode.Actions is used to perform actions on the scanner hardware.
            Such actions include starting and canceling pending reads, as well as, enabling
            and disabling the hardware. The class is normally contructed by the
             <see cref="T:Symbol.Barcode.Reader"/> class during its instantiation process. 
            </remarks>
        </member>
        <member name="M:Symbol.Barcode.Actions.Finalize">
            <summary>
            Allows an Action object to attempt to free resources and perform other cleanup 
            operations before it is reclaimed by garbage collection.
            </summary>
        </member>
        <member name="M:Symbol.Barcode.Actions.Dispose">
            <summary>
            Release any resources used by the Actions class.
            </summary>
        </member>
        <member name="M:Symbol.Barcode.Actions.Enable">
            <summary>
            Enables the scanner hardware. This method does not make the scanner scan or 
            turn on the laser. 
            </summary>
            <remarks>
            The Enable() method may take a while to process due to that fact that 
            it instantiates the ScanParamsAPI, DecoderAPI, and UPCANParamsAPI, and
            Version objects. During this time the WaitCursor will be displayed on the
            screen. The Enable() method allows this initialization to be done when it
            least impacts the user experience (e.g. entry to a form). To perform the 
            scan of a barcode additional methods must be called. <seealso cref="M:Symbol.Barcode.Actions.Disable"/> 
            <seealso cref="M:Symbol.Barcode.Actions.Read(Symbol.Generic.ReaderData)"/> <seealso cref="M:Symbol.Barcode.Actions.ToggleSoftTrigger"/>
            </remarks>
        </member>
        <member name="M:Symbol.Barcode.Actions.Disable">
            <summary>
            Disables the scanner hardware. Reverses the <see cref="M:Symbol.Barcode.Actions.Enable"/> process. The
            scanner must be re-enabled before its use if it has been disabled.
            <seealso cref="M:Symbol.Barcode.Actions.Enable"/>
            </summary> 
        </member>
        <member name="M:Symbol.Barcode.Actions.Read(Symbol.Generic.ReaderData)">
            <summary>
            Starts a pending read. The method will not turn on the
            laser. It will, however, put the scanner in a state in which the laser can
            be turned on either by pressing a hardware trigger or by performing a software
            trigger.
            </summary>
            <param name="rd">The ReaderData object that will contain the scanned data once
            the read completes.</param>
        </member>
        <member name="M:Symbol.Barcode.Actions.Flush">
            <summary>
            Removes all pending reads from the Reader. 
            </summary>
            <remarks>Calling Flush method is same as 
            calling <see cref="M:Symbol.Barcode.Actions.CancelRead(Symbol.Barcode.ReaderData)"/> method for all pending reads. 
            <seealso cref="M:Symbol.Barcode.Actions.Read(Symbol.Generic.ReaderData)"/> <seealso cref="M:Symbol.Barcode.Actions.CancelRead(Symbol.Barcode.ReaderData)"/></remarks>
        </member>
        <member name="M:Symbol.Barcode.Actions.CancelRead(Symbol.Barcode.ReaderData)">
            <summary>
            Removes a single pending read from the Reader. This method will only work
            if the given ReaderData object is still in a pending read situation (i.e. 
            the read has been submitted but no notification of a read completion or 
            failure has occurred).
            </summary>
            <param name="rd">A ReaderData object that has a pending read.</param>
        </member>
        <member name="M:Symbol.Barcode.Actions.ToggleSoftTrigger">
            <summary>
            Toggles the soft trigger of the scanner.
            </summary>
            <example>
            An application can use this method in the event handler for a software
            scan button press:
            <code>private void button1_Click(object sender, System.EventArgs e)
            {
            	ToggleSoftTrigger();
            }
            </code>
            </example>
        </member>
        <member name="M:Symbol.Barcode.Actions.NewReaderData">
            <summary>
            Creates a generic ReaderData object for use with a Symbol.Barcode.Reader 
            object.
            <para>
            NOTE: This method has been deprecated and will not be supported.
            Please use the Symbol.Barcode.ReaderData class to create a ReaderData object. 
            </para>
            </summary>
            <remarks>
            The Symbol.Generic base classes provide a level of abstraction for data providers
            such as Barcode Readers and Magnetic Stripe Readers. This abstraction provides 
            application developers with the capability to write generic code that can be
            used across different data providers and hardware. Since only common
            functionality is provided by the Generic classes, some specific features may
            be lost.
            </remarks>
            <returns>A generic ReaderData object.</returns>
        </member>
        <member name="M:Symbol.Barcode.Actions.SetParameters">
            <summary>
            All changes in decoder parameters, reader parameters, UPCEAN parameters, Scanner parameters
            and Interface parameters will be applied to the barcode that will be read immediately.
            
            NOTE: Beginning with the ScanMDD file version 6.01, the InterfaceParameters object can be accessed before or after enabling the 
            Reader object. In the earlier versions of ScanMDD, the InterfaceParameters object can only be accessed after enabling the Reader object. 
            The ScanMDD version can be retrieved using the ControlPanel tool available on the support central website.
            </summary>
            <remarks>If this function is not called after making modifications to any of these 
            parameters, then the changes would take effect only after the completion of any
            pending reads.</remarks>
        </member>
        <member name="M:Symbol.Barcode.Actions.GetParameters">
            <summary>
            Get decoder parameters, reader parameters, interface parameters, UPCEAN parameters and Scanner parameters from the
            underlying API.
            
            NOTE: Beginning with the ScanMDD file version 6.01, the InterfaceParameters object can be accessed before or after enabling the 
            Reader object. In the earlier versions of ScanMDD, the InterfaceParameters object can only be accessed after enabling the Reader object. 
            The ScanMDD version can be retrieved using the ControlPanel tool available on the support central website.
            </summary>
        </member>
        <member name="M:Symbol.Barcode.Actions.DoRemoteFeedBack(Symbol.Barcode.Actions.RemoteFeedbackParams)">
            <summary>
            Provides Beep and/or LED feedback on the remote scanner.
            </summary>
            <param name="remoteFeedbackParams">RemoteFeedbackParams object that contains the feedback parameters</param>
            <remarks>
            This feature is available only on remote scanners with feedback capabilites like RS507.
            To determine if this feature is available check the IsFeedbackSupported property in Reader.Info object.
            </remarks>
        </member>
        <member name="M:Symbol.Barcode.Actions.StartStatusThread">
            <summary>
            Start status monitoring thread
            </summary>
        </member>
        <member name="T:Symbol.Barcode.Actions.RemoteFeedbackParams">
            <summary>
            This class provides access to the feedback parameters on the remote scanner.
            <remarks>
            NOTE: The Remote feedback feature is available only on remote scanners with feedback capabilites like RS507.
            To determine if this feature is available check the IsFeedbackSupported property in Reader.Info object.
            </remarks>
            </summary>
        </member>
        <member name="M:Symbol.Barcode.Actions.RemoteFeedbackParams.#ctor">
            <summary>
            Constructor
            </summary>
        </member>
        <member name="P:Symbol.Barcode.Actions.RemoteFeedbackParams.BeepTime">
            <summary>
            A value of zero (0) indicates no beep and a value is greater then zero (>0) enables the beep.
            </summary>
        </member>
        <member name="P:Symbol.Barcode.Actions.RemoteFeedbackParams.BeepFrequency">
            <summary>
            Provides the beep frequency.
            </summary>
        </member>
        <member name="P:Symbol.Barcode.Actions.RemoteFeedbackParams.LEDTime">
            <summary>
            Provides the LED time duration in milliseconds.
            A value of zero (0) indicates no LED used in feedback.
            </summary>
        </member>
        <member name="P:Symbol.Barcode.Actions.RemoteFeedbackParams.WaveFile">
            <summary>
            Provides the path of a wave file.
            NOTE: This property is reserved for future use. So please do not use this property currently.
            </summary>
        </member>
        <member name="P:Symbol.Barcode.Actions.RemoteFeedbackParams.BeepPattern">
            <summary>
            Provides the pattern of the beep.
            </summary>
        </member>
        <member name="P:Symbol.Barcode.Actions.RemoteFeedbackParams.LEDPattern">
            <summary>
            Provides the pattern of the LED.
            </summary>
        </member>
        <member name="T:Symbol.Barcode.Actions.BEEPPATTERN">
            <summary>
            Supported beep patterns.
            NOTE: All remote scanners do not support all beep patterns.
            Refer to the programmer's guide of the corresponding remote scanner 
            to find out the supported beep patterns. 
            For RS507 remote scanner, refer to "Using RS507 Bluetooth Scanner" 
            under Barcode section of the Programmer's Guide.
            </summary>
        </member>
        <member name="F:Symbol.Barcode.Actions.BEEPPATTERN.BEEP_PATTERN_ONE_SHORT_HIGH">
            <summary>
            Beep pattern
            </summary>
        </member>
        <member name="F:Symbol.Barcode.Actions.BEEPPATTERN.BEEP_PATTERN_TWO_SHORT_HIGH">
            <summary>
            Beep pattern
            </summary>
        </member>
        <member name="F:Symbol.Barcode.Actions.BEEPPATTERN.BEEP_PATTERN_THREE_SHORT_HIGH">
            <summary>
            Beep pattern
            </summary>
        </member>
        <member name="F:Symbol.Barcode.Actions.BEEPPATTERN.BEEP_PATTERN_FOUR_SHORT_HIGH">
            <summary>
            Beep pattern
            </summary>
        </member>
        <member name="F:Symbol.Barcode.Actions.BEEPPATTERN.BEEP_PATTERN_FIVE_SHORT_HIGH">
            <summary>
            Beep pattern
            </summary>
        </member>
        <member name="F:Symbol.Barcode.Actions.BEEPPATTERN.BEEP_PATTERN_ONE_SHORT_LOW">
            <summary>
            Beep pattern
            </summary>
        </member>
        <member name="F:Symbol.Barcode.Actions.BEEPPATTERN.BEEP_PATTERN_TWO_SHORT_LOW">
            <summary>
            Beep pattern
            </summary>
        </member>
        <member name="F:Symbol.Barcode.Actions.BEEPPATTERN.BEEP_PATTERN_THREE_SHORT_LOW">
            <summary>
            Beep pattern
            </summary>
        </member>
        <member name="F:Symbol.Barcode.Actions.BEEPPATTERN.BEEP_PATTERN_FOUR_SHORT_LOW">
            <summary>
            Beep pattern
            </summary>
        </member>
        <member name="F:Symbol.Barcode.Actions.BEEPPATTERN.BEEP_PATTERN_FIVE_SHORT_LOW">
            <summary>
            Beep pattern
            </summary>
        </member>
        <member name="F:Symbol.Barcode.Actions.BEEPPATTERN.BEEP_PATTERN_ONE_LONG_HIGH">
            <summary>
            Beep pattern
            </summary>
        </member>
        <member name="F:Symbol.Barcode.Actions.BEEPPATTERN.BEEP_PATTERN_TWO_LONG_HIGH">
            <summary>
            Beep pattern
            </summary>
        </member>
        <member name="F:Symbol.Barcode.Actions.BEEPPATTERN.BEEP_PATTERN_THREE_LONG_HIGH">
            <summary>
            Beep pattern
            </summary>
        </member>
        <member name="F:Symbol.Barcode.Actions.BEEPPATTERN.BEEP_PATTERN_FOUR_LONG_HIGH">
            <summary>
            Beep pattern
            </summary>
        </member>
        <member name="F:Symbol.Barcode.Actions.BEEPPATTERN.BEEP_PATTERN_FIVE_LONG_HIGH">
            <summary>
            Beep pattern
            </summary>
        </member>
        <member name="F:Symbol.Barcode.Actions.BEEPPATTERN.BEEP_PATTERN_ONE_LONG_LOW">
            <summary>
            Beep pattern
            </summary>
        </member>
        <member name="F:Symbol.Barcode.Actions.BEEPPATTERN.BEEP_PATTERN_TWO_LONG_LOW">
            <summary>
            Beep pattern
            </summary>
        </member>
        <member name="F:Symbol.Barcode.Actions.BEEPPATTERN.BEEP_PATTERN_THREE_LONG_LOW">
            <summary>
            Beep pattern
            </summary>
        </member>
        <member name="F:Symbol.Barcode.Actions.BEEPPATTERN.BEEP_PATTERN_FOUR_LONG_LOW">
            <summary>
            Beep pattern
            </summary>
        </member>
        <member name="F:Symbol.Barcode.Actions.BEEPPATTERN.BEEP_PATTERN_FIVE_LONG_LOW">
            <summary>
            Beep pattern
            </summary>
        </member>
        <member name="F:Symbol.Barcode.Actions.BEEPPATTERN.BEEP_PATTERN_FAST_WARBLE">
            <summary>
            Beep pattern
            </summary>
        </member>
        <member name="F:Symbol.Barcode.Actions.BEEPPATTERN.BEEP_PATTERN_SLOW_WARBLE">
            <summary>
            Beep pattern
            </summary>
        </member>
        <member name="F:Symbol.Barcode.Actions.BEEPPATTERN.BEEP_PATTERN_MIX1">
            <summary>
            Beep pattern
            </summary>
        </member>
        <member name="F:Symbol.Barcode.Actions.BEEPPATTERN.BEEP_PATTERN_MIX2">
            <summary>
            Beep pattern
            </summary>
        </member>
        <member name="F:Symbol.Barcode.Actions.BEEPPATTERN.BEEP_PATTERN_MIX3">
            <summary>
            Beep pattern
            </summary>
        </member>
        <member name="F:Symbol.Barcode.Actions.BEEPPATTERN.BEEP_PATTERN_MIX4">
            <summary>
            Beep pattern
            </summary>
        </member>
        <member name="T:Symbol.Barcode.Actions.LEDPATTERN">
            <summary>
            Supported LED patterns.
            NOTE: All remote scanners do not support all LED patterns.
            Refer to the programmer's guide of the corresponding remote scanner 
            to find out the supported LED patterns. 
            For RS507 remote scanner, refer to "Using RS507 Bluetooth Scanner" 
            under Barcode section of the Programmer's Guide.
            </summary>
        </member>
        <member name="F:Symbol.Barcode.Actions.LEDPATTERN.LED_PATTERN_DECODE">
            <summary>
            LED pattern interpreted as bit masks for 7 LEDs
            </summary>
        </member>
        <member name="F:Symbol.Barcode.Actions.LEDPATTERN.LED_PATTERN_LED1">
            <summary>
            LED pattern interpreted as bit masks for 7 LEDs
            </summary>
        </member>
        <member name="F:Symbol.Barcode.Actions.LEDPATTERN.LED_PATTERN_LED2">
            <summary>
            LED pattern interpreted as bit masks for 7 LEDs
            </summary>
        </member>
        <member name="F:Symbol.Barcode.Actions.LEDPATTERN.LED_PATTERN_LED3">
            <summary>
            LED pattern interpreted as bit masks for 7 LEDs
            </summary>
        </member>
        <member name="F:Symbol.Barcode.Actions.LEDPATTERN.LED_PATTERN_LED4">
            <summary>
            LED pattern interpreted as bit masks for 7 LEDs
            </summary>
        </member>
        <member name="F:Symbol.Barcode.Actions.LEDPATTERN.LED_PATTERN_LED5">
            <summary>
            LED pattern interpreted as bit masks for 7 LEDs
            </summary>
        </member>
        <member name="F:Symbol.Barcode.Actions.LEDPATTERN.LED_PATTERN_LED6">
            <summary>
            LED pattern interpreted as bit masks for 7 LEDs
            </summary>
        </member>
        <member name="F:Symbol.Barcode.Actions.LEDPATTERN.LED_PATTERN_BLACK">
            <summary>
            LED blinking pattern
            </summary>
        </member>
        <member name="F:Symbol.Barcode.Actions.LEDPATTERN.LED_PATTERN_PULSE_GREEN">
            <summary>
            LED blinking pattern
            </summary>
        </member>
        <member name="F:Symbol.Barcode.Actions.LEDPATTERN.LED_PATTERN_PULSE_YELLOW">
            <summary>
            LED blinking pattern
            </summary>
        </member>
        <member name="F:Symbol.Barcode.Actions.LEDPATTERN.LED_PATTERN_PULSE_RED">
            <summary>
            LED blinking pattern
            </summary>
        </member>
        <member name="F:Symbol.Barcode.Actions.LEDPATTERN.LED_PATTERN_GREEN">
            <summary>
            LED blinking pattern
            </summary>
        </member>
        <member name="F:Symbol.Barcode.Actions.LEDPATTERN.LED_PATTERN_YELLOW">
            <summary>
            LED blinking pattern
            </summary>
        </member>
        <member name="F:Symbol.Barcode.Actions.LEDPATTERN.LED_PATTERN_RED">
            <summary>
            LED blinking pattern
            </summary>
        </member>
        <member name="F:Symbol.Barcode.Actions.LEDPATTERN.LED_PATTERN_ALT_GB">
            <summary>
            LED blinking pattern. Alternate Green / Black
            </summary>
        </member>
        <member name="F:Symbol.Barcode.Actions.LEDPATTERN.LED_PATTERN_ALT_YB">
            <summary>
            LED blinking pattern. Alternate Yellow / Black
            </summary>
        </member>
        <member name="F:Symbol.Barcode.Actions.LEDPATTERN.LED_PATTERN_ALT_RB">
            <summary>
            LED blinking pattern. Alternate Red / Black
            </summary>
        </member>
        <member name="F:Symbol.Barcode.Actions.LEDPATTERN.LED_PATTERN_ALT_GY">
            <summary>
            LED blinking pattern. Alternate Green / Yellow
            </summary>
        </member>
        <member name="F:Symbol.Barcode.Actions.LEDPATTERN.LED_PATTERN_ALT_YR">
            <summary>
            LED blinking pattern. Alternate Yellow / Red
            </summary>
        </member>
        <member name="F:Symbol.Barcode.Actions.LEDPATTERN.LED_PATTERN_ALT_RG">
            <summary>
            LED blinking pattern. Alternate Red / Green
            </summary>
        </member>
        <member name="F:Symbol.Barcode.Actions.LEDPATTERN.LED_PATTERN_ALT_RYG">
            <summary>
            LED blinking pattern. Alternate Red / Yellow / Green
            </summary>
        </member>
        <member name="F:Symbol.Barcode.Actions.LEDPATTERN.LED_PATTERN_ALT_GYB">
            <summary>
            LED blinking pattern. Alternate Green / Yellow / Black
            </summary>
        </member>
        <member name="F:Symbol.Barcode.Actions.LEDPATTERN.LED_PATTERN_ALT_YRG">
            <summary>
            LED blinking pattern. Alternate Yellow / Red / Green
            </summary>
        </member>
        <member name="F:Symbol.Barcode.Actions.LEDPATTERN.LED_PATTERN_ALT_RGB">
            <summary>
            LED blinking pattern. Alternate Red / Green / Black
            </summary>
        </member>
        <member name="F:Symbol.Barcode.Actions.LEDPATTERN.LED_PATTERN_ALT_RYGB">
            <summary>
            LED blinking pattern. Alternate Red / Yellow / Green / Black
            </summary>
        </member>
        <member name="T:Symbol.Barcode.Version">
            <summary>
            Barcode reader version class.
            </summary>
        </member>
        <member name="M:Symbol.Barcode.Version.#ctor(Symbol.Barcode.Reader)">
            <summary>
            Version class constructor
            </summary>
            <param name="r">The Reader object to obtain version information from.</param>
        </member>
        <member name="P:Symbol.Barcode.Version.Hardware">
            <summary>
            Version of scanner hardware if applicable
            </summary>
        </member>
        <member name="P:Symbol.Barcode.Version.Decoder">
            <summary>
            Version of decoder software if applicable
            </summary>
        </member>
        <member name="P:Symbol.Barcode.Version.PDD">
            <summary>
            Version of the scanner driver PDD.
            </summary>
        </member>
        <member name="P:Symbol.Barcode.Version.MDD">
            <summary>
            Version of the scanner driver MDD.
            </summary>
        </member>
        <member name="P:Symbol.Barcode.Version.CAPI">
            <summary>
            Version of the scanner native API.
            </summary>
        </member>
        <member name="P:Symbol.Barcode.Version.Assembly">
            <summary>
            Version of the Assembly.
            </summary>
        </member>
        <member name="T:Symbol.Barcode.ReaderParams">
            <summary>
            The READER_PARAMS structure holds the parameters for the attached bar code reader. 
            Reader parameters are global to all reads on all open handles on the same scanner.
            </summary>
        </member>
        <member name="F:Symbol.Barcode.ReaderParams.StructInfo">
            <summary>
            Sub-structure describing memory allocated and used by this structure.
            </summary>
        </member>
        <member name="F:Symbol.Barcode.ReaderParams.ReaderType">
            <summary>
            Describes the bar code reader type these parameters apply to. Read only. 
            This value determines the values for ReaderSpecific
            </summary>
        </member>
        <member name="F:Symbol.Barcode.ReaderParams.ReaderSpecific">
            <summary>
            The ReaderSpecific holds parameters for one reader type. 
            The structure of ReaderSpecific depends on the value of ReaderType. 
            </summary>
        </member>
        <member name="M:Symbol.Barcode.ReaderParams.#ctor(System.IntPtr)">
            <summary>
            The Constructor for ReaderParams
            </summary>
        </member>
        <member name="T:Symbol.Barcode.STRUCT_INFO">
            <summary>
            Sub-structure describing memory allocated and used by this structure.
            </summary>
        </member>
        <member name="M:Symbol.Barcode.STRUCT_INFO.#ctor">
            <summary>
            The constructor should not normally be called
            </summary>
        </member>
        <member name="T:Symbol.Barcode.READER_SPECIFIC">
            <summary>
            The ReaderSpecific holds parameters for one reader type. 
            The structure of ReaderSpecific depends on the value of ReaderType. 
            </summary>
        </member>
        <member name="F:Symbol.Barcode.READER_SPECIFIC.LaserSpecific">
            <summary>
            The LASER_SPECIFIC class holds laser specific reader parameters.
            </summary>
        </member>
        <member name="F:Symbol.Barcode.READER_SPECIFIC.ContactSpecific">
            <summary>
            The CONTACT_SPECIFIC class holds contact wand specific reader parameters.
            </summary>
        </member>
        <member name="F:Symbol.Barcode.READER_SPECIFIC.ImagerSpecific">
            <summary>
            The IMAGER_SPECIFIC class holds imager specific reader parameters.
            </summary>
        </member>
        <member name="M:Symbol.Barcode.READER_SPECIFIC.#ctor">
            <summary>
            The constructor should not normally be called
            </summary>
        </member>
        <member name="T:Symbol.Barcode.LASER_SPECIFIC">
            <summary>
            The LASER_SPECIFIC class holds laser specific reader parameters.
            </summary>
        </member>
        <member name="M:Symbol.Barcode.LASER_SPECIFIC.#ctor">
            <summary>
            The constructor should not normally be called
            </summary>
        </member>
        <member name="P:Symbol.Barcode.LASER_SPECIFIC.AimType">
            <summary>
            Describes the type of aiming to use.
            </summary>
        </member>
        <member name="P:Symbol.Barcode.LASER_SPECIFIC.AimDuration">
            <summary>
            Duration in milliseconds for timed aim modes.
            </summary>
        </member>
        <member name="P:Symbol.Barcode.LASER_SPECIFIC.AimMode">
            <summary>
            Describes the mode of aiming to use.
            NOTE: This property is deprecated and it is no longer supported. 
            It will be removed in a future release.
            </summary>
        </member>
        <member name="P:Symbol.Barcode.LASER_SPECIFIC.RasterMode">
            <summary>
            Describes the type of vertical rastering to use.
            </summary>
        </member>
        <member name="P:Symbol.Barcode.LASER_SPECIFIC.BeamTimer">
            <summary>
            Maximum laser on time in milliseconds. A value of 0 means infinite timeout.
            </summary>
        </member>
        <member name="P:Symbol.Barcode.LASER_SPECIFIC.ControlScanLed">
            <summary>
            Flag to enable LED decode notification by the scanner device (disables notification in the mobile computer). Currently not used.
            </summary>
        </member>
        <member name="P:Symbol.Barcode.LASER_SPECIFIC.ScanLedLogicLevel">
            <summary>
            Logic level to use when controlling scanner LED. Currently not used.
            </summary>
        </member>
        <member name="P:Symbol.Barcode.LASER_SPECIFIC.KlasseEinsEnable">
            <summary>
            Flag to enable Klasse Eins laser on time function.
            </summary>
        </member>
        <member name="P:Symbol.Barcode.LASER_SPECIFIC.BidirRedundancy">
            <summary>
            Flag to enable birdirectional redundancy.
            </summary>
        </member>
        <member name="P:Symbol.Barcode.LASER_SPECIFIC.LinearSecurityLevel">
            <summary>
            Describes the linear security level used during decoding.
            </summary>
        </member>
        <member name="P:Symbol.Barcode.LASER_SPECIFIC.PointerTimer">
            <summary>
            Maximum laser on time in milliseconds for laser pointer. A value of 0 means infinite timeout.
            </summary>
        </member>
        <member name="P:Symbol.Barcode.LASER_SPECIFIC.RasterHeight">
            <summary>
            Describes, as a percentage, the vertical rastering height to use when the RasterMode is RASTER_MODE_OPEN_ALWAYS.
            </summary>
        </member>
        <member name="P:Symbol.Barcode.LASER_SPECIFIC.DBPMode">
            <summary>
            Describes what type of DBP (Digital Bar Pulse) is being produced by the scan engine. 
            If the product does not support I2C or if using an older engine the default value for DBP Mode is DBP_NORMAL.
            An attempt to change this mode to DBP_COMPOSITE will result in an E_SCN_NOTSUPPORTED error.
            </summary>
        </member>
        <member name="P:Symbol.Barcode.LASER_SPECIFIC.SameSymbolTimeout">
            <summary>
            This parameter is used to prevent the scanner from decoding the same symbol within 
            this time interval (applicable only when AimType is set to AIM_TYPE_CONTINUOUS_READ). 
            The continuous read mode is used to perform rapid scans. In order to prevent decoding the 
            same barcode twice, this parameter is set to an appropriate interval (in milliseconds).
            A value of 0 means no interval is required between two successive reads
            NOTE: Applicable only when AimType is set to AIM_TYPE_CONTINUOUS_READ.
            </summary>
        </member>
        <member name="P:Symbol.Barcode.LASER_SPECIFIC.DifferentSymbolTimeout">
            <summary>
            This parameter is used to prevent the scanner from decoding another symbol within 
            this time interval (applicable only when AimType is set to AIM_TYPE_CONTINUOUS_READ). 
            The continuous read mode is used to perform rapid scans. In order to prevent decoding 
            too quickly and have enough time to aim before decoding the next barcode, 
            this parameter is set to an appropriate interval (in milliseconds).
            A value of 0 means no interval is required between two successive reads
            NOTE: Applicable only when AimType is set to AIM_TYPE_CONTINUOUS_READ.
            </summary>
        </member>
        <member name="P:Symbol.Barcode.LASER_SPECIFIC.NarrowBeam">
            <summary>
            Flag to enable narrow beam width.
            NOTE: This property is being deprecated. It will be removed from a future version.
            Please use the BeamWidth property instead.
            </summary>
        </member>
        <member name="P:Symbol.Barcode.LASER_SPECIFIC.BeamWidth">
            <summary>
            Allows the user to set the width of the laser beam.
            NOTE: User can query the Reader.Info.SupportedBeamWidths property 
            to see what beam widths are supported on the device.
            </summary>
        </member>
        <member name="P:Symbol.Barcode.LASER_SPECIFIC.AdaptiveScanning">
            <summary>
            Specifies the adaptive scanning mode. 
            Adaptive Scanning is enabled by default on devices that support this feature. 
            Please refer to the remarks section below for information about Adaptive Scanning.
            <para>
            NOTE: User can query the Reader.Info.AdaptiveScanning property to check if 
            adaptive scanning feature is supported on the device.
            </para>
            </summary>
            <remarks>
            ADAPTIVE SCANNING:
            <para>
            The high performance and long working range scan engines such as SE960 will support 
            the adaptive scanning feature to enable scanning high and low density bar codes. 
            When adaptive scanning is enabled, the scan engine will automatically toggle 
            between 2 scan angles, wide and narrow, allowing the scan engine to decode barcodes 
            as close as 10 inches (high density) and as far as 100 inches (low density).
            </para>
            <para>
            To determine the type of the scan engine on your device, click on the 
            Start->System->Settings->System Info and select “ConfigInfo” tab. 
            The "Scanner" field under this tab provides the name of the scan engine. 
            </para>
            </remarks>
        </member>
        <member name="T:Symbol.Barcode.CONTACT_SPECIFIC">
            <summary>
            The CONTACT_SPECIFIC class holds contact wand specific reader parameters.
            </summary>
        </member>
        <member name="M:Symbol.Barcode.CONTACT_SPECIFIC.#ctor">
            <summary>
            The constructor should not normally be called
            </summary>
        </member>
        <member name="P:Symbol.Barcode.CONTACT_SPECIFIC.QuietZoneRatio">
            <summary>
            Quiet zone ratio x:1 to 1:x used for decoding.
            </summary>
        </member>
        <member name="P:Symbol.Barcode.CONTACT_SPECIFIC.InitialScanTime">
            <summary>
            Time in milliseconds that contact wand will remain in acquire mode waiting for a bar code.
            </summary>
        </member>
        <member name="P:Symbol.Barcode.CONTACT_SPECIFIC.PulseDelay">
            <summary>
            Time in milliseconds to pause between pulses of the contact wand.
            </summary>
        </member>
        <member name="T:Symbol.Barcode.IMAGER_SPECIFIC">
            <summary>
            The IMAGER_SPECIFIC class holds imager specific reader parameters.
            </summary>
        </member>
        <member name="M:Symbol.Barcode.IMAGER_SPECIFIC.#ctor">
            <summary>
            The constructor should not normally be called
            </summary>
        </member>
        <member name="P:Symbol.Barcode.IMAGER_SPECIFIC.AimType">
            <summary>
            Describes the type of aiming to use.
            </summary>
        </member>
        <member name="P:Symbol.Barcode.IMAGER_SPECIFIC.AimDuration">
            <summary>
            Duration in milliseconds for timed aim modes.
            </summary>
        </member>
        <member name="P:Symbol.Barcode.IMAGER_SPECIFIC.AimMode">
            <summary>
            Describes the mode of aiming to use.
            </summary>
        </member>
        <member name="P:Symbol.Barcode.IMAGER_SPECIFIC.BeamTimer">
            <summary>
            Maximum Imager on time in milliseconds. A value of 0 means infinite timeout.
            </summary>
        </member>
        <member name="P:Symbol.Barcode.IMAGER_SPECIFIC.PointerTimer">
            <summary>
            Maximum beam timer in milliseconds for pointer mode. A value of 0 means infinite timeout.
            </summary>
        </member>
        <member name="P:Symbol.Barcode.IMAGER_SPECIFIC.ImageCaptureTimeout">
            <summary>
            Timeout for image capture (in milliseconds).
            </summary>
        </member>
        <member name="P:Symbol.Barcode.IMAGER_SPECIFIC.ImageCompressionTimeout">
            <summary>
            Timeout for image compression and processing (in milliseconds).
            </summary>
        </member>
        <member name="P:Symbol.Barcode.IMAGER_SPECIFIC.LinearSecurityLevel">
            <summary>
            Describes the linear security level used during decoding.
            </summary>
        </member>
        <member name="P:Symbol.Barcode.IMAGER_SPECIFIC.FocusMode">
            <summary>
            Focus mode to use.
            </summary>
        </member>
        <member name="P:Symbol.Barcode.IMAGER_SPECIFIC.FocusPosition">
            <summary>
            Focus position to use.
            </summary>
        </member>
        <member name="P:Symbol.Barcode.IMAGER_SPECIFIC.PoorQuality1DMode">
            <summary>
            This parameter allows poor quality 1D barcodes to be read, 
            BUT adversely affecting the overall decoding performance.
            </summary>
        </member>
        <member name="P:Symbol.Barcode.IMAGER_SPECIFIC.PicklistMode">
            <summary>
            This parameter allows the imager to decode only the barcode that is directly under the 
            cross-hair/reticule (+) part of the AIM pattern.  This feature is most useful in applications 
            where multiple barcodes may appear in the field of view during a decode session and only 
            one of them is targeted for decode.  When enabled, PicklistMode will override AimMode if 
            no aiming is chosen and use the AIM_MODE_RETICLE mode.  When enabled, PicklistMode may adversely 
            affect overall decoding performance.
            		ENABLE – Enables picklist mode, so only the barcode under the cross-hair can be decoded.
            		DISABLE – Disables picklist mode, so any barcode within the field of view can be decoded.
            </summary>
            <remarks>
            When both PicklistMode and PicklistModeEx are set by the user, PicklistModeEx takes precedance over PicklistMode.
            </remarks>
        </member>
        <member name="P:Symbol.Barcode.IMAGER_SPECIFIC.PicklistModeEx">
            <summary>
            This parameter allows the imager to decode only the barcode that is directly under the cross-hair/center
            of the reticle. This feature is most useful in applications where multiple barcodes may appear in the 
            field of view during a decode session and only one of them is targeted for decode.  When enabled, 
            PicklistModeEx will override AimMode if no aiming is chosen and use the AIM_MODE_RETICLE mode.  When enabled, 
            PicklistModeEx may adversely affect overall decoding performance. Also read note on interaction between the 
            picklist modes and the viewfinder modes (only for imagers that support viewfinder capability).
            PICKLIST_DISABLED – Disables picklist mode, so any bar code within the field of view can be decoded. 
            PICKLIST_HARDWARE_RETICLE – Enables picklist mode so that only the barcode under the projected reticle can be decoded. If the imager does not support a projected reticle then the behavior is same as that of PICKLIST_SOFTWARE_RETICLE.
            </summary>
            <remarks>
            When both PicklistMode and PicklistModeEx are set by the user, PicklistModeEx takes precedance over PicklistMode.
            </remarks>
        </member>
        <member name="P:Symbol.Barcode.IMAGER_SPECIFIC.DPMMode">
            <summary>
            This parameter allows Direct Part Marking (DPM) bar codes to be read but may adversely affect 
            overall decoding performance. Direct Part Marking is a way of stamping bar codes directly on 
            physical objects. 
            Support for this feature is available on DPM terminals only. If this feature is not available 
            and user attempts to enable it, an error (E_SCN_NOTSUPPORTED) will result.
            </summary>
            <remarks>This feature cannot be turned on in conjunction with Picklist as both these 
            modes are mutually exclusive. An attempt to turn on both will result in an error 
            (E_SCN_NOTSUPPORTED).
            </remarks>
        </member>
        <member name="P:Symbol.Barcode.IMAGER_SPECIFIC.IlluminationMode">
            <summary>
            Illumination Mode to use.
            </summary>
        </member>
        <member name="P:Symbol.Barcode.IMAGER_SPECIFIC.VFPosition">
            <summary>
            Specifies the coordinates of the viewfinder on the screen.
            When scanning is enabled via an imager that supports the viewfinder,
            the user can enable the viewfinder which helps with locating the barcode.
            The images displayed on the viewfinder will be scaled to fit in the area specified.
            <para>
            NOTE: The VFPosition property can be modified by assigning a new Rectangle object containing
            the necessary Left, Top, Right and Bottom values. Please refer the example section below for code snippets.
            </para>
            </summary>
            <example>
            <code lang="C#">
            Symbol.Barcode.Rectangle rect = new Symbol.Barcode.Rectangle();
            rect.Left = 20;
            rect.Top = 20;
            rect.Right = 50;
            rect.Bottom = 50;
            myReader.ReaderParameters.ReaderSpecific.ImagerSpecific.VFPosition = rect;
            myReader.Actions.SetParameters();
            </code>
            
            <code lang="VB.NET">
            Dim rect As New Symbol.Barcode.Rectangle()
            rect.Left = 20
            rect.Top = 20
            rect.Right = 50
            rect.Bottom = 50
            myReader.ReaderParameters.ReaderSpecific.ImagerSpecific.VFPosition = rect
            myReader.Actions.SetParameters()
            </code>
            </example>
        </member>
        <member name="P:Symbol.Barcode.IMAGER_SPECIFIC.VFMode">
            <summary>
            Viewfinder modes supported for scanning.
            </summary>
        </member>
        <member name="P:Symbol.Barcode.IMAGER_SPECIFIC.VFFeedback">
            <summary>
            This parameter allows selection of the different feedback parameters on a successful decode.
            </summary>
        </member>
        <member name="P:Symbol.Barcode.IMAGER_SPECIFIC.VFFeedbackTime">
            <summary>
            Time (in milli seconds) for which the visual display selected by VFFeedback is shown. 
            </summary>
        </member>
        <member name="P:Symbol.Barcode.IMAGER_SPECIFIC.Inverse1DMode">
            <summary>
            This parameter allows the user to select decoding on inverse 1D barcodes.
            </summary>
        </member>
        <member name="P:Symbol.Barcode.IMAGER_SPECIFIC.SameSymbolTimeout">
            <summary>
            This parameter is used to prevent the scanner from decoding the same symbol within 
            this time interval (applicable only when AimType is set to AIM_TYPE_CONTINUOUS_READ). 
            The continuous read mode is used to perform rapid scans. In order to prevent decoding the 
            same barcode twice, this parameter is set to an appropriate interval (in milliseconds).
            A value of 0 means no interval is required between two successive reads
            NOTE: Applicable only when AimType is set to AIM_TYPE_CONTINUOUS_READ.
            </summary>
        </member>
        <member name="P:Symbol.Barcode.IMAGER_SPECIFIC.DifferentSymbolTimeout">
            <summary>
            This parameter is used to prevent the scanner from decoding another symbol within 
            this time interval (applicable only when AimType is set to AIM_TYPE_CONTINUOUS_READ). 
            The continuous read mode is used to perform rapid scans. In order to prevent decoding 
            too quickly and have enough time to aim before decoding the next barcode, 
            this parameter is set to an appropriate interval (in milliseconds).
            A value of 0 means no interval is required between two successive reads
            NOTE: Applicable only when AimType is set to AIM_TYPE_CONTINUOUS_READ.
            </summary>
        </member>
        <member name="P:Symbol.Barcode.IMAGER_SPECIFIC.LCDMode">
            <summary>
            Enables or disables LCD mode for Blockbuster imager devices only.
            LCD mode enhances the ability of the Blockbuster imager to 
            read barcodes from LCD displays such as cell phone screens. 
            Enabling this mode for Pico imagers and cameras is not required 
            because they already have the ability to read LCD displays
            NOTE: When using the LCD mode, a degradation in performance
            may be observed and the aiming crosshair will blink until the 
            barcode is decoded.
            </summary>
        </member>
        <member name="T:Symbol.Barcode.Rectangle">
            <summary>
            The class for representing a rectangle with Left, Top, Right and Bottom coordinates.
            </summary>
        </member>
        <member name="F:Symbol.Barcode.Rectangle.Left">
            <summary>
            x-coordinate of the upper-left corner.
            </summary>
        </member>
        <member name="F:Symbol.Barcode.Rectangle.Top">
            <summary>
            y-coordinate of the upper-left corner.
            </summary>
        </member>
        <member name="F:Symbol.Barcode.Rectangle.Right">
            <summary>
            x-coordinate of the bottom-right corner.
            </summary>
        </member>
        <member name="F:Symbol.Barcode.Rectangle.Bottom">
            <summary>
            y-coordinate of the bottom-right corner.
            </summary>
        </member>
        <member name="M:Symbol.Barcode.Rectangle.#ctor">
            <summary>
            The constructor. All the members are initialized to 0s.
            </summary>
        </member>
        <member name="T:Symbol.Barcode.ReaderData">
            <summary>
            ReaderData class encapsulates all the scanned data and meta-data for a given read.
            </summary>
            <remarks>
            The ReaderData class is most commonly used to create objects that will be passed to the
            <see cref="M:Symbol.Barcode.Actions.Read(Symbol.Generic.ReaderData)"/> method. This will start a pending read.
            </remarks>
        </member>
        <member name="M:Symbol.Barcode.ReaderData.#ctor(Symbol.Barcode.ReaderDataTypes,System.Int32)">
            <summary>
            ReaderData constructor with integer based buffer size.
            </summary>
            <param name="ReaderDataType">A <see cref="T:Symbol.Barcode.ReaderDataTypes"/> member that specifies 
            whether the memory buffer sent to the hardware will hold text or raw binary data.
            </param>
            <param name="nBufferSize">An integer that specifies the size of the memory buffer
            in bytes for binary mode, in characters for text mode.</param>
        </member>
        <member name="M:Symbol.Barcode.ReaderData.#ctor(Symbol.Barcode.ReaderDataTypes,Symbol.Barcode.ReaderDataLengths)">
            <summary>
            ReaderData constructor with enumerated type buffer size.
            </summary>
            <param name="ReaderDataType">A <see cref="T:Symbol.Barcode.ReaderDataTypes"/> member that specifies 
            whether the memory buffer sent to the hardware will hold text or raw binary data.
            </param>
            <param name="nBufferSize">An integer that specifies the size of the memory buffer
            in bytes for binary mode, in characters for text mode.</param>
        </member>
        <member name="M:Symbol.Barcode.ReaderData.Finalize">
            <summary>
            Allows a ReaderData to attempt to free resources and perform other cleanup 
            operations before it is reclaimed by garbage collection.
            </summary>
        </member>
        <member name="M:Symbol.Barcode.ReaderData.Dispose">
            <summary>
            Release all resources used by ReaderData object.
            </summary>
            <remarks> 
            It is highly recommended that this method be called when the ReaderData object is no
            longer being used by the application. <para>Note: Do not Dispose of the ReaderData
            object until any Reads associated with it have been cancelled.</para>
            </remarks>
        </member>
        <member name="M:Symbol.Barcode.ReaderData.ToString">
            <summary>
            Returns the scanned data text.
            </summary>
            <returns>
            A string of the scanned data if the memory buffer has been set to text mode.
            </returns>
            <exception cref="T:Symbol.Exceptions.InvalidDataTypeException"> An 
            InvalidDataTypeException is thrown if this property is accessed and they
            memory buffer has been set to binary mode. </exception>
        </member>
        <member name="P:Symbol.Barcode.ReaderData.IsText">
            <summary>
            Specifies whether or not the ReaderData memory buffer is set to text mode.
            </summary>
            <value>
            A boolean flag which when true indicates that the memory buffer is set to 
            text mode, otherwise the memory buffer is set to binary mode.
            </value>
        </member>
        <member name="P:Symbol.Barcode.ReaderData.IsPending">
            <summary>
            Specifies whether or not the ReaderData is currently in a pending read situation.
            </summary>
            <value>
            A boolean flag which when true indicate that the ReaderData has not completed a  
            read that has been requested of it. Reads will be pending until they complete 
            with a valid decode, fail with an error, or are cancelled.
            </value>
        </member>
        <member name="P:Symbol.Barcode.ReaderData.IsAllocated">
            <summary>
            Specifies whether or not a memory buffer has been allocated for the ReaderData.
            The memory buffer will be allocated after a read is issued using the Reader.Actions.Read(..) call.
            </summary>
            <value>
            A boolean flag which when true indicates that a valid memory buffer has been 
            allocated, otherwise the memory buffer has not been allocated.
            </value>
        </member>
        <member name="P:Symbol.Barcode.ReaderData.BufferSize">
            <summary>
            Specifies the memory buffer size allocated by the ReaderData.
            </summary>
            <value>
            An integer which specifies the memory buffer size in bytes for binary mode or 
            characters for text mode.
            </value>
        </member>
        <member name="P:Symbol.Barcode.ReaderData.Text">
            <summary>
            Returns the scanned data text.
            </summary>
            <value>
            A string of the scanned data if the memory buffer has been set to text mode.
            </value>
            <exception cref="T:Symbol.Exceptions.InvalidDataTypeException"> An InvalidDataTypeException
            is thrown if this property is accessed and they memory buffer has been set 
            to binary mode. </exception>
        </member>
        <member name="P:Symbol.Barcode.ReaderData.RawData">
            <summary>
            Returns a byte array with the scanned data.
            </summary>
            <value>
            A byte array with the scanned data if the ReaderDataType has been set to binary mode.
            </value>
            <exception cref="T:Symbol.Exceptions.InvalidDataTypeException"> An InvalidDataTypeException
            is thrown if this property is accessed and ReaderDataType has been set 
            to text mode in the constructor of ReaderData object. </exception>
        </member>
        <member name="P:Symbol.Barcode.ReaderData.Source">
            <summary>
            Returns the source of the scanned data. (ie The barcode scanner device name)
            </summary>
            <value> 
            A string that contains the name of the scanner which provided the source.
            </value>
        </member>
        <member name="P:Symbol.Barcode.ReaderData.TimeStamp">
            <summary>
            The time at which the barcode was scanned.
            </summary>
            <value>
            A DateTime structure that contains the time information.
            </value>
        </member>
        <member name="P:Symbol.Barcode.ReaderData.Type">
            <summary>
            The Decoder type of the barcode.
            </summary>
            <value>
            A <see cref="T:Symbol.Barcode.DecoderTypes"/> member that specifies the type of barcode scanned.
            </value>
        </member>
        <member name="P:Symbol.Barcode.ReaderData.TypeHex">
            <summary>
            The Decoder Type of the barcode in hexadecimal format.
            </summary>
            <value>
            A string of the form "0x#", where # is the decoder type in hexadecimal.
            </value>
        </member>
        <member name="P:Symbol.Barcode.ReaderData.Length">
            <summary>
            The length of the barcode data.
            </summary>
            <value>
            An integer that specifies the length of the scanned data.
            </value>
        </member>
        <member name="P:Symbol.Barcode.ReaderData.MultiPart">
            <summary>
            The flag indicating whether the data is in the multi-part format. 
            If this has been set to true, the user is supposed to get the auxiliary data.
            </summary>
        </member>
        <member name="P:Symbol.Barcode.ReaderData.AuxData">
            <summary>
            Provides the access to the auxiliary data. 
            The auxiliary data will be avaialble only if the flag MultiPart is true.
            </summary>
        </member>
        <member name="P:Symbol.Barcode.ReaderData.Event">
            <summary>
            Not currently used, returns either "Scan Complete" or "".
            </summary>
        </member>
        <member name="P:Symbol.Barcode.ReaderData.HasReader">
            <summary>
            Specifies whether or not the ReaderData has been attached to a Reader object via 
            a Actions.Read method call.
            </summary>
            <remarks>
             Note: This property will return true after a read has completed.
            </remarks>
            <value>
            A boolean flag that when set to true indicates that the ReaderData has been attached
            to a Reader at some point.
            </value>
        </member>
        <member name="P:Symbol.Barcode.ReaderData.RequestId">
            <summary>
            The ID of the read submitted to the barcode scanner hardware.
            </summary>
            <value>
            An integer that represents the ID of the requested read.
            </value>
        </member>
        <member name="P:Symbol.Barcode.ReaderData.Result">
            <summary>
            The result of the read request.
            </summary>
            <remarks>
            This result is a managed code version of the actual result returned by the
            underlying scan driver stack. It can be used to determine whether or not the 
            read was successful or failed. 
            </remarks>
            <value>
            A <see cref="T:Symbol.Results"/> member that indicates whether or not the read was 
            successful.
            </value>
        </member>
        <member name="T:Symbol.Barcode.ReaderData.AuxiliaryData">
            <summary>
            Provides the access to the auxiliary data.
            This is meant to be used in conjunction with a ReaderData instance.
            </summary>
        </member>
        <member name="M:Symbol.Barcode.ReaderData.AuxiliaryData.ToString">
            <summary>
            Returns the auxiliary data text.
            </summary>
            <returns>
            A string of the auxiliary data if the ReaderData memory buffer has been set to text mode.
            </returns>
            <exception cref="T:Symbol.Exceptions.InvalidDataTypeException"> An 
            InvalidDataTypeException is thrown if this property is accessed and the ReaderData
            memory buffer has been set to binary mode. </exception>
        </member>
        <member name="P:Symbol.Barcode.ReaderData.AuxiliaryData.Text">
            <summary>
            Returns the auxiliary data text.
            </summary>
            <returns>
            A string of the auxiliary data if the ReaderData memory buffer has been set to text mode.
            </returns>
            <exception cref="T:Symbol.Exceptions.InvalidDataTypeException"> An 
            InvalidDataTypeException is thrown if this property is accessed and the ReaderData
            memory buffer has been set to binary mode. </exception>
        </member>
        <member name="P:Symbol.Barcode.ReaderData.AuxiliaryData.RawData">
            <summary>
            Returns a byte array of the auxiliary data.
            </summary>
            <value>
            A byte array of the auxiliary data if the ReaderData memory buffer has been set to binary mode.
            </value>
            <exception cref="T:Symbol.Exceptions.InvalidDataTypeException"> An InvalidDataTypeException
            is thrown if this property is accessed and ReaderDataType has been set 
            to text mode in the constructor of ReaderData object. </exception>
        </member>
        <member name="P:Symbol.Barcode.ReaderData.AuxiliaryData.Length">
            <summary>
            The length of the auxiliary data.
            </summary>
        </member>
    </members>
</doc>
