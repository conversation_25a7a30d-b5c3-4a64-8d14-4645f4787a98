﻿using System;
using System.Linq;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using System.Threading;
using System.Text.RegularExpressions;
using System.Net;
using System.IO;
using System.Xml;

namespace Tarczyn__Magazyn
{
    public partial class EtykietyLaczenie : Form
    {
        //MainMenu myParent = null;
        ActionMenu myParent = null;
        TextBox[] TextBoxArray = null;

        TextBox AktualnyTextBox = null;

        string operac_id_global = "";


        double[] sztuki = new double[4] { 0, 0, 0, 0 };
        double[] opakowania = new double[4] { 0, 0, 0, 0 };

        string wydrukowano = "NIE";




        public EtykietyLaczenie(ActionMenu c)
        {
            FullScreenMode.OknoOFF(this);
            InitializeComponent();
            //TextBoxArray = new TextBox[] { et1, scan };
            myParent = c;
            Etykieta.Inicjalizacja();
            wms_wybrany.Text = Wlasciwosci.system_id;


            et1.Focus();

            label_sztuk.Text = "0";
            label_opak.Text = "0";
            //textBox1_sscc.Text = "559082583551150372";

            this.comboBox1.Items.Add("172.7.1.49");
            this.comboBox1.Items.Add("172.7.1.24");

            this.comboBox1.SelectedIndex = 0;

        }





        public void RemoveText(object sender, EventArgs e)
        {
            TextBox Pole_Tekstowe = (TextBox)sender;
            if (Pole_Tekstowe.Text == "RRMMDD")
            {
                Pole_Tekstowe.Text = "";
            }

        }

        public void AddText(object sender, EventArgs e)
        {

        }

        private void button1_Click(object sender, EventArgs e)
        {
            Skaner.Przewij_Skanowanie();
            myParent.Show();
            this.Close();
        }


        int TrybSkanu = 0;

        private void GotFocus_TextBox(object sender, EventArgs e)
        {
            Skaner.Przewij_Skanowanie();
            TextBox Pole_Tekstowe = (TextBox)sender;

            AktualnyTextBox = Pole_Tekstowe;

            //if (DW == Pole_Tekstowe)
            //{

            //    if (!(dataprod.Text == "" || dataprod.Text == "RRMMDD"))
            //    {
            //        // sprawdź poprawność daty

            //        try
            //        {
            //            DateTime gg = DateTime.ParseExact("20" + dataprod.Text, "yyyyMMdd", null);
            //            if (ilosc_dni_przydatnosci != "0")
            //            {
            //                DW.Text = gg.AddDays(Convert.ToDouble(ilosc_dni_przydatnosci)).ToString("yyMMdd"); 
            //                //MessageBox.Show("DW:" + DW.Text);
            //                opak_real.Focus();
            //            }
            //        }
            //        catch (Exception ex)
            //        {
            //            MessageBox.Show("Błędna data produkcji.");
            //            dataprod.Text = "";
            //        }
            //    }

            //}
           


            ZacznijSkanowanie();
        }

        private void LostFocus_TextBox(object sender, EventArgs e)
        {
            AktualnyTextBox = null;
            Zakoncz_Skanowanie();

            //TextBox Pole_Tekstowe = (TextBox)sender;



            //if (Pole_Tekstowe.Text != "") //scan == Pole_Tekstowe && scan.Text != "" &&
            //{


            //    XmlDocument doc1_etykieta = WebService.Pobierz_XmlDocument("delivery/delivery_scan_label.php?scan=" + Pole_Tekstowe.Text + "&system_id=" + Wlasciwosci.system_id_id);
            //    XmlNode node_etykieta = WebService.Pobierz_XmlNode(doc1_etykieta, "dane");
            //    if (node_etykieta["komunikat"].InnerText != "OK")
            //    {
            //        AktualnyTextBox = null;
            //        //MessageBox.Show("1a");
            //        MessageBox.Show(node_etykieta["komunikat"].InnerText);

            //    }//szukaj();
            //}
            //else
            //{
            //    label_sztuk.Text += node_etykieta["ilosc"].InnerText;
            //    label_opak.Text += node_etykieta["ilosc_opak"].InnerText;
            //}


            
        }





        private void button2_Click(object sender, EventArgs e)
        {



        }



        #region Skanowanie

        Thread Skanowanie = null;



        private void ZacznijSkanowanie()
        {
            StringBuilder login = new StringBuilder();
            Skaner.UstawTryb_String(login);
            Skaner.Zacznij_Skanowanie();
            Skanowanie = new Thread(() => CheckString(login));
            Skanowanie.IsBackground = true;
            Skanowanie.Start();
        }

        private void CheckString(StringBuilder cc)
        {
            while (true)
            {
                if (cc.ToString() != "")
                {
                    this.Invoke(new EventHandler(delegate
                    {
                        dodawanie(cc.ToString());
                    }));
                    return;
                }
                Thread.Sleep(200);
            }
        }


        private void Zakoncz_Skanowanie()
        {
            {
                Skanowanie.Abort();
                Skaner.Przewij_Skanowanie();
            }
        }


        private void delivery_scan_label(string ops, TextBox AktualnyTextBox_local)
        {
            if (ops == "")
            {
                MessageBox.Show("Etykieta nie może być pusta");
                return;
            }

            XmlDocument doc1_etykieta = WebService.Pobierz_XmlDocument("delivery/delivery_scan_label.php?system_id=" + Wlasciwosci.system_id_id + "&scan=" + ops);
            XmlNode node_etykieta = WebService.Pobierz_XmlNode(doc1_etykieta, "dane");
            if (node_etykieta["komunikat"].InnerText != "OK")
            {
                //ETYKIETA.Text = "";
                AktualnyTextBox_local.Text = "";
                MessageBox.Show(node_etykieta["komunikat"].InnerText);
                return;
            }
            else
            {
                AktualnyTextBox_local.Text = ops;



                TextBox[] textBoxes = new TextBox[] { et1, et2, et3, et4 };
                int index = Array.IndexOf(textBoxes, AktualnyTextBox_local);

                if (index >= 0)
                {
                    try
                    {
                        double ilosc = Convert.ToDouble(node_etykieta["ilosc"].InnerText);
                        double iloscOpak = Convert.ToDouble(node_etykieta["ilosc_opak"].InnerText);

                        sztuki[index] = ilosc;
                        opakowania[index] = iloscOpak;
                    }
                    catch (FormatException)
                    {
                        MessageBox.Show("Nie można przekonwertować jednej z wartości na liczbę całkowitą.");
                        return; // lub inna odpowiednia obsługa błędu
                    }
                    catch (OverflowException)
                    {
                        MessageBox.Show("Jedna z wartości jest zbyt duża lub zbyt mała dla typu int.");
                        return; // lub inna odpowiednia obsługa błędu
                    }
                }

                /*

                if (int.TryParse(node_etykieta["ilosc"].InnerText, out sztuki[0]))
                {
                    // Konwersja powiodła się, sztuki[0] ma teraz wartość int
                }
                else
                {
                    // Obsługa błędu, jeśli konwersja się nie powiodła
                    MessageBox.Show("Nie można przekonwertować wartości na liczbę całkowitą.");
                }



                if(AktualnyTextBox_local == et1)
                {
                    //sztuki[0] = (int)node_etykieta["ilosc"].InnerText;
                    //opakowania [0] = (int)node_etykieta["ilosc_opak"].InnerText;
                }
                if(AktualnyTextBox_local == et2)
                {
                    sztuki[1] = (int)node_etykieta["ilosc"].InnerText;
                    opakowania [1] = (int)node_etykieta["ilosc_opak"].InnerText;
                }

                    if(AktualnyTextBox_local == et3)
                {
                    sztuki[2] = (int)node_etykieta["ilosc"].InnerText;
                    opakowania [2] = (int)node_etykieta["ilosc_opak"].InnerText;
                }

                if(AktualnyTextBox_local == et4)
                {
                    sztuki[3] = (int)node_etykieta["ilosc"].InnerText;
                    opakowania [3] = (int)node_etykieta["ilosc_opak"].InnerText;
                }
                 */

                if (AktualnyTextBox_local == et1) et2.Focus();
                if (AktualnyTextBox_local == et2) et3.Focus();
                if (AktualnyTextBox_local == et3) et4.Focus();

                PodsumujIlosci();

            }
        }


        private void PodsumujIlosci()
        {
            double sumaSztuk = 0;
            double sumaOpakowan = 0;

            for (int i = 0; i < sztuki.Length; i++)
            {
                sumaSztuk += sztuki[i];
                sumaOpakowan += opakowania[i];
            }

            label_sztuk.Text = sumaSztuk.ToString();
            label_opak.Text = sumaOpakowan.ToString();
        }


        private void dodawanie(string ops)
        {
            //MessageBox.Show("TrybSkanu:" + TrybSkanu + "   ,   ops.Length:" + ops.Length);
            Skaner.Przewij_Skanowanie();

            //


            if (ops.StartsWith("00"))
            {
                ops = ops.Substring(2);
            }



            if (AktualnyTextBox == et1 || AktualnyTextBox == et2 || AktualnyTextBox == et3 || AktualnyTextBox == et4)
            {
                delivery_scan_label(ops, AktualnyTextBox);

                //if(AktualnyTextBox == et1)

                //ZacznijSkanowanie();
            }           

        }

        #endregion





        private void button6_Click(object sender, EventArgs e)
        {


        }








        private void QT_KeyDown(object sender, KeyEventArgs e)
        {
            if (e.KeyCode == Keys.Enter)
            {
                button2_Click(this, new EventArgs());
            }
        }

        private void opak_real_KeyDown(object sender, KeyEventArgs e)
        {
            if (e.KeyCode == Keys.Enter)
            {
                button2_Click(this, new EventArgs());
            }
        }

        private void button2_Click_1(object sender, EventArgs e)
        {
                if(et1.Text=="" && et2.Text=="")
                {
                    MessageBox.Show("Wymagane są przynajmniej etykiety 1 i 2. Przerywam operację");
                    return;
                }

                textBox1_sscc.Text = "";
                XmlDocument doc1_etykieta = WebService.Pobierz_XmlDocument("delivery/laczenie_etykiet_sscc.php?etykieta_klient1="+et1.Text+"&etykieta_klient2="+et2.Text+"&etykieta_klient3="+et3.Text+"&etykieta_klient4="+et4.Text+"&system_id="+Wlasciwosci.system_id_id+"&pracownik_id="+Wlasciwosci.id_Pracownika+"&imie_nazwisko="+Wlasciwosci.imie_nazwisko+"");
                XmlNode node_etykieta = WebService.Pobierz_XmlNode(doc1_etykieta, "dane");
                if (node_etykieta["komunikat"].InnerText != "OK")
                {
                    MessageBox.Show(node_etykieta["komunikat"].InnerText);
                    return;
                }
                else
                {

                    for (int i = 0; i < sztuki.Length; i++)
                    {
                        sztuki[i] = 0;
                        opakowania[i] = 0;
                    }
                    et1.Text = "";
                    et2.Text = "";
                    et3.Text = "";
                    et4.Text = "";
                    

                    label_sztuk.Text = "0";
                    label_opak.Text = "0";
                    et1.Focus();
                    wydrukowano = "NIE";


                    textBox1_sscc.Text = node_etykieta["nowy_sscc"].InnerText;
                }
        }

        private void button3_Click(object sender, EventArgs e)
        {
            //MessageBox.Show(textBox1_sscc.Text);
            if (textBox1_sscc.Text == "")
            {
                MessageBox.Show("Brak etykiety do reprintu. Przerywam operację");
                return;
            }
            //MessageBox.Show(comboBox1.Text);

            if (comboBox1.Text == "")
            {
                MessageBox.Show("Brak etykiety do reprintu. Przerywam operację");
                return;
            }

            //MessageBox.Show("OK");

            if (wydrukowano == "TAK")
            {
                DialogResult dialogresult = MessageBox.Show("Czy chcesz wydrukować ponownie te same etykiety ?", "", MessageBoxButtons.YesNo, MessageBoxIcon.None, MessageBoxDefaultButton.Button1);
                if (dialogresult == DialogResult.Yes)
                {
                    reprint_sscc(textBox1_sscc.Text);
                }
            }
            else
            {
                reprint_sscc(textBox1_sscc.Text);
            }
           

            


            wydrukowano = "TAK";

        }



        private void reprint_sscc(string ops)
        {
            //MessageBox.Show(comboBox1.SelectedValue.ToString());
            XmlDocument doc1_etykieta = WebService.Pobierz_XmlDocument("delivery/reprint_sscc.php?sscc=" + textBox1_sscc.Text + "&system_id=" + Wlasciwosci.system_id_id + "&adres_ip=" + comboBox1.Text);
            XmlNode node_etykieta = WebService.Pobierz_XmlNode(doc1_etykieta, "dane");
            if (node_etykieta["komunikat"].InnerText != "OK")
            {
                MessageBox.Show(node_etykieta["komunikat"].InnerText);
                return;
            }
           
        }












    }
}