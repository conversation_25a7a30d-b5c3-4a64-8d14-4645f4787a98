﻿using System;
using System.Linq;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using System.Text.RegularExpressions;
using System.Xml;

namespace Tarczyn__Magazyn
{
    public partial class PoleComboboxXml : Form
    {
        public string komunikat = "";
        public string wybierane_id = "";
        public string wybierana_nazwa = "";

        List<string> nazwy_wyswietlane = new List<string>();
        int[] nazwy_id = new int[100];

        public PoleComboboxXml(XmlDocument doc1, string komunikat, string domyslna_nazwa)
        {
            InitializeComponent();
            label1.Text = komunikat;        
            inicjalizacja(doc1);
            if(domyslna_nazwa!=""){
                comboBox1.SelectedIndex = comboBox1.Items.IndexOf(domyslna_nazwa);
            }
        }

        public void inicjalizacja(XmlDocument doc1)
        {
            XmlNode node = doc1.SelectSingleNode("//dane");
            
            if (node["komunikat"].InnerText != "OK")
            {
                MessageBox.Show(node["komunikat"].InnerText);
                return;
            }

            XmlNodeList typy_palet = doc1.SelectNodes("//typy_palet");
            
            nazwy_wyswietlane.Clear();
            nazwy_wyswietlane.Add("");
            nazwy_id[0] = 0;
            
            int k = 0;
            foreach (XmlNode paleta in typy_palet)
            {
                nazwy_wyswietlane.Add(paleta["nazwa"].InnerText);
                nazwy_id[k + 1] = Convert.ToInt32(paleta["id"].InnerText);
                k++;
            }

            BindingSource bs = new BindingSource();
            bs.DataSource = nazwy_wyswietlane;
            comboBox1.DataSource = bs;
        }

        private void button1_Click(object sender, EventArgs e)
        {
            //this.DialogResult = DialogResult.Cancel;
            //return "przerwij";
        }

        private void Dalej_Click(object sender, EventArgs e)
        {
            
            wybierane_id = nazwy_id[comboBox1.SelectedIndex].ToString();
            wybierana_nazwa = comboBox1.SelectedValue.ToString();
            if (wybierana_nazwa == "")
            {
                MessageBox.Show("Nie dokonano wyboru");
                return;
            }
     
        }

       
    }
}