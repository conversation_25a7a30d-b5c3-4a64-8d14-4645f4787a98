# Lista Zadań (TODO)

Ta lista śledzi zadania związane z rozwojem, utrzymaniem i dokumentacją projektu WMS. 

## Faza 1: Analiza i Dokumentacja (100% - UKOŃCZONE)

- [x] Wstępna analiza struktury projektu (100%)
- [x] Utworzenie podstawowej dokumentacji (`PRD.md`, `ARCHITECTURE.md`, `TODO.md`, `STYLE_GUIDE.md`) (100%)
- [x] Szczegółowa analiza modułów aplikacji C# w celu zrozumienia logiki biznesowej (100%)
- [x] Analiza backendu PHP w celu zmapowania wszystkich endpointów API (100%)
- [x] Dokumentacja schematu bazy danych (100%)
- [x] Opisanie formatu komunikacji (struktury XML/JSON) między klientem a serwerem (100%)
- [x] Szczegółowa analiza i dokumentacja klasy `UpdateEtykietaMenu.cs` (100%)
- [x] Szczegółowa analiza i dokumentacja klasy `MainMenu.cs` (100%)
- [x] **NOWE:** Kompleksowa analiza systemu inwentaryzacji (100%)
- [x] **NOWE:** Dokumentacja workflow procesów biznesowych (100%)
- [x] **NOWE:** Specyfikacja API dla aplikacji MAUI (100%)
- [x] **NOWE:** Mapowanie funkcjonalności desktop → MAUI (100%)
- [x] **NOWE:** Diagramy przepływów procesów (100%)
- [x] Uzupełnienie `STYLE_GUIDE.md` o specyficzne dla projektu konwencje (100%)

## Faza 2: System Inwentaryzacji - Analiza Szczegółowa (100% - UKOŃCZONE)

### Analiza Struktury Danych (100%)
- [x] Tabela `inwentaryzacja` - główna tabela z danymi inwentaryzacji
- [x] Tabela `etykiety` - informacje o etykietach produktów
- [x] Tabela `miejsca` - struktura lokalizacji magazynowych
- [x] Tabela `kody` - kartoteka produktów
- [x] Tabela `operacje` - historia operacji
- [x] Tabela `zmianym` - rejestr zmian lokalizacji
- [x] Relacje między tabelami i klucze obce
- [x] Indeksy i optymalizacja zapytań

### Analiza Klas Inwentaryzacji (100%)
- [x] `Inwentaryzacja.cs` - inwentaryzacja ogólna z pełną kontrolą
- [x] `InwentaryzacjaProd.cs` - inwentaryzacja produktowa z auto-dodawaniem
- [x] `InwentaryzacjaGG.cs` - inwentaryzacja według grup/kategorii
- [x] `InwentaryzacjaMiejsca.cs` - inwentaryzacja według lokalizacji
- [x] Różnice w logice biznesowej między typami
- [x] Mechanizmy walidacji i obsługi błędów

### Dokumentacja Workflow (100%)
- [x] Przepływ główny inwentaryzacji
- [x] Proces skanowania i dekodowania kodów
- [x] Obsługa różnych typów kodów (etykiety, palety, GS1-128)
- [x] Walidacja i zapis danych
- [x] Zarządzanie lokalizacjami i zmianami miejsc
- [x] Synchronizacja online/offline

### Specyfikacja API MAUI (100%)
- [x] Endpoint pobierania listy inwentaryzacji
- [x] Endpoint wyszukiwania etykiet/kodów
- [x] Endpoint zapisywania pozycji inwentaryzacji
- [x] Endpoint pobierania stanu inwentaryzacji
- [x] Endpoint synchronizacji offline
- [x] Obsługa błędów i autoryzacja
- [x] Formaty XML zapytań i odpowiedzi

### Mapowanie Desktop → MAUI (100%)
- [x] Struktura projektu MAUI
- [x] Modele danych (InwentaryzacjaModel, EtykietaModel, LokalizacjaModel)
- [x] ViewModels z wzorcem MVVM
- [x] Serwisy (ApiService, DatabaseService, ScannerService)
- [x] Interfejsy XAML
- [x] Obsługa funkcjonalności mobilnych

### Diagramy Przepływów (100%)
- [x] Główny przepływ inwentaryzacji (Mermaid)
- [x] Proces skanowania i walidacji
- [x] Workflow zapisu danych
- [x] Synchronizacja offline
- [x] Obsługa zmian lokalizacji
- [x] Workflow dla każdego typu inwentaryzacji
- [x] Obsługa błędów i wyjątków
- [x] Dekodowanie GS1-128
- [x] Monitoring i raportowanie

## Faza 3: Potencjalne Usprawnienia (do dyskusji)

- [ ] Refaktoryzacja kodu w celu poprawy czytelności i wydajności
- [ ] Wprowadzenie centralnego systemu logowania błędów
- [ ] Modernizacja interfejsu użytkownika
- [ ] Implementacja aplikacji MAUI na podstawie dokumentacji
- [ ] Rozważenie migracji na nowszą technologię

---

**Postęp prac nad dokumentacją: 95%**
**Postęp analizy systemu inwentaryzacji: 100%**
