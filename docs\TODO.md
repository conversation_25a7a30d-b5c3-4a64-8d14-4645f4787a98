# Lista Zadań (TODO)

Ta lista śledzi zadania związane z rozwojem, utrzymaniem i dokumentacją projektu WMS. 

## Faza 1: Analiza i Dokumentacja (w toku)

- [x] Wstępna analiza struktury projektu (100%)
- [x] Utworzenie podstawowej dokumentacji (`PRD.md`, `ARCHITECTURE.md`, `TODO.md`, `STYLE_GUIDE.md`) (100%)
- [x] Szczegółowa analiza modułów aplikacji C# w celu zrozumienia logiki biznesowej (100%)
- [x] Analiza backendu PHP w celu zmapowania wszystkich endpointów API (80%)
- [x] Dokumentacja schematu bazy danych (100%)
- [x] Opisanie formatu komunikacji (struktury XML/JSON) między klientem a serwerem (90%)
- [x] Szczegółowa analiza i dokumentacja klasy `UpdateEtykietaMenu.cs` (100%)
- [x] Szczegółowa analiza i dokumentacja klasy `MainMenu.cs` (100%)
- [ ] Uzupełnienie `STYLE_GUIDE.md` o specyficzne dla projektu konwencje (20%)

## Faza 2: Potencjalne Usprawnienia (do dyskusji)

- [ ] Refaktoryzacja kodu w celu poprawy czytelności i wydajności.
- [ ] Wprowadzenie centralnego systemu logowania błędów.
- [ ] Modernizacja interfejsu użytkownika.
- [ ] Rozważenie migracji na nowszą technologię (jeśli dotychczasowa stanie się przestarzała).

---

**Postęp prac nad dokumentacją: 35%**
