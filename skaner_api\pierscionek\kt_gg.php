<?php

error_reporting(E_ALL);
ini_set('display_errors', 1);

include_once '../Db_multi.class.php';
include_once '../funkcje.inc';

$db = new Db("localhost", "wmsgg");

$delivery_id = $_GET["delivery_id"];
$karton = $_GET["karton"];
$listcontrol_id = $_GET["listcontrol_id"];
$system_id = 17;
$komunikat = "OK";
sprawdzanie_towaru($delivery_id, $karton, $system_id, $komunikat, $db);




// sprzwdzenie czy jest na KT dany karton
function czyKartonNaDocin($karton, $docin_id,$komunikat, $db)
{
    $sql = "select * from etykiety where docin_id=" . $docin_id . " and etykieta_klient='" . $karton . "'";
    $result = $db->mGetResultAsXML($sql);
    if (!empty($result)) {
        xml_from_indexed_array(
            array(
                'komunikat' => $komunikat, 'result' => "Ta etykieta była skanowana",
            )
        );
    }
}


















function tworzenie_KT($docout_type,$docout_ref, $system_id,$db)
{
    // sprawdż, czy jest id do docout_type
    $docout_id = getDocoutIdForDoctypeDocoutref($docout_type, $system_id, $docout_ref, $db);
    if(empty($docout_id))
    {
        // jeśli nie ma to stwórz
        $docout_id = createDocout($docout_type,$numer_dokumentu, $docout_ref,$pracownik_id, $system_id, $db)
    }

    // sprawdż, czy jest id do doc_type
    $docin_id = getDocinIdForDoctypeDocref($doc_type, $system_id, $doc_ref, $db);
    if(empty($docin_id))
    {
        // jeśli nie ma to stwórz
        $docin_id = createDocin($doc_type,$numer_dokumentu, $doc_ref,$pracownik_id, $system_id, $db);
    }
}

function createDocin($doc_type,$numer_dokumentu, $doc_ref,$pracownik_id, $system_id, $db)
{
    $sql = "insert into docin(docin_internal,doc_type,doc_nr,doc_date,doc_ts,pracownik_id,kontrah_id,doc_ref,doc_uwagi) 
    values(3,'$doc_type',$numer_dokumentu,date(now()),sysdate(),$pracownik_id,(select kontrah_wew_id from systemy where wartosc=$system_id),'$doc_ref','')";
    return $db->mGetResultAsXML($sql);
}

function createDocout($docout_type,$numer_dokumentu, $docout_ref,$pracownik_id, $system_id, $db)
{
    $sql = "insert into docout(docout_internal,docout_type,docout_nr,docout_date,docout_ts,pracownik_id,kontrah_id,docout_ref,docout_uwagi) 
    values(3,'$docout_type',$numer_dokumentu,date(now()),sysdate(),$pracownik_id,(select kontrah_wew_id from systemy where wartosc=$system_id),'$docout_ref','')";
    return $db->mGetResultAsXML($sql);
}

function getDocoutIdForDoctypeDocoutref($docout_type,$system_id, $docout_ref, $db)
{
    $sql = "select dout.id from docout dout,etykiety et where et.system_id = " .$system_id. " and et.docout_id=dout.id and dout.docout_type='" .$docout_type. "' and dout.docout_ref='".$docout_ref. "'"  ;
    $result = $db->mGetResultAsXML($sql);
    if (empty($result)) {
        return 0;
    } else {
        return $result[0]['id'];
    }
}

function getDocinIdForDoctypeDocref($docin_type,$system_id, $doc_ref, $db)
{
    $sql = "select din.id from docin din,etykiety et where et.system_id = " .$system_id. " and et.docin_id=din.id and din.doc_type='" .$docin_type. "' and din.doc_ref='".$doc_ref. "'"  ;
    $result = $db->mGetResultAsXML($sql);
    if (empty($result)) {
        return 0;
    } else {
        return $result[0]['id'];
    }
}


// robi kopię etykiety ale docout_id ma być PP-, active=0 i ona będzie stanowiła archiwum dla PZ
//przepakowanie_etykiety_step1_oryginalna($baza_danych, $etykieta_id, $docout_id, $db);

//przepakowanie_etykiety_step3_ilosc_zostawiana($baza_danych, $etykieta_id, $docin_id, $ilosc_zostawiana, $db)







// 1. stworzyć funkcję, która będzie sprawdzała czy dany karton jest aktywny w etykietach i czy jest na DL, i jeśli tak to update delivery_et pole skan
function sprawdzanie_towaru($delivery_id, $karton, $system_id, $komunikat, $db)
{
    $czyAktywna = $db->mGetResultAsXML("select count(1) from etykiety e where e.etykieta_klient='" . $karton . "' and e.active=1 and e.system_id='" . $system_id . "' limit 1");
    if (empty($czyAktywna)) {
        $komunikat = " $karton nieaktywny";
        xml_from_indexed_array(
            array(
                'komunikat' => $komunikat,
            )
        );
    } else {
        //$czyKartonNaDl = $db->mGetResultAsXML("select count(1) from etykiety e left join delivery_et de on de.etykieta_id=e.id left join delivery d on de.delivery_id=d.id where d.id='" . $delivery_id . "' and e.system_id='" . $system_id . "' and e.etykieta_klient='" . $karton . "'");
        $czyKartonNaDL = $db->mGetResultAsXML("select e.id,etykieta_klient from etykiety e  where e.etykieta_klient='" . $karton . "' and e.active=1 and e.system_id='" . $system_id . "' limit 1");
        if (empty($czyKartonNaDL)) {
            xml_from_indexed_array(
                array(
                    'komunikat' => $komunikat, 'result' => '',
                )
            );
        } else {
            $sql = "update delivery_et set skan=1 where delivery_id='" . $delivery_id . "' and etykieta_id='" . $czyKartonNaDL[0]['id'] . "'";
            echo $sql;
            //$db->mGetResultAsXML($sql);

            xml_from_indexed_array(
                array(
                    'komunikat' => $komunikat, 'result' => $czyKartonNaDL[0]['etykieta_klient'],
                )
            );
        }
    }


}



// wyświetla GET
print_r($_GET);

exit();







$numer_karty = $_GET["numer_karty"];

$sql = 'SELECT * FROM wmsgg.pracownicy
where numer_Karty="' . $numer_karty . '" limit 1;   ';
$result = $db->mGetResultAsXML($sql);
//

if (empty($result)) {
    $sql = 'SELECT * FROM pikt.osoby
where numer_karty="' . $numer_karty . '" limit 1;
       ';
    $result2 = $db->mGetResultAsXML($sql);

    if (empty($result2)) {
        xml_from_indexed_array(
            array(
                'komunikat' => "NOOK",
            )
        );
    } else {

        //print_r($result2[0]['numer_karty']);
        $sql = "insert into wmsgg.pracownicy(login,haslo,imie_nazwisko,stanowisko,numer_Karty,pin)
values ('" . $result2[0]['nazwisko'] . $result2[0]['imie'] . "','" . $result2[0]['nazwisko'] . $result2[0]['imie'] . "','" . $result2[0]['imie'] . " " . $result2[0]['nazwisko'] . "','magazynier','" . $result2[0]['numer_karty'] . "','1234')";
        //echo $sql;
        $result = $db->mGetResultAsXML($sql);
        if (!empty($result)) {
            xml_from_indexed_array(
                array(
                    'komunikat' => "OK",
                )
            );
        } else {
            xml_from_indexed_array(
                array(
                    'komunikat' => "NOOK",
                )
            );
        }
    }
} else {
    xml_from_indexed_array(
        array(
            'komunikat' => "OK",
        )
    );
}



// koniec funkcji printlabel
