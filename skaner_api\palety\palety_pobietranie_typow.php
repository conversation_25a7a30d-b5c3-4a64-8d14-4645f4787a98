<?php


// wyswietloenie błędów
error_reporting(E_ALL);
ini_set('display_errors', 1);

include_once '../Db.class.php';
include_once '../funkcje.inc';

$baza_danych = "wmsgg";
$db = new Db("localhost", $baza_danych);
$komunikat['komunikat']="OK";

$sql = "SELECT t.id, t.opis as nazwa FROM typypalet t WHERE t.id!=0 ORDER BY t.kolejnosc_pal";
$typy_palet = $db->mGetResultAsXML($sql);

$komunikat['typy_palet'] = $typy_palet;

return xml_from_indexed_array($komunikat);
