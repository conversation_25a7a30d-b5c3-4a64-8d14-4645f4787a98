<?php

//require_once("./../../lib/nusoap/lib/nusoap.php");


include_once './../Db.class.php';
include_once './../funkcje.inc';

ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

$db = new Db();
$params = get_params_to_array();
$baza_danych = "wmsferrero";
$komunikat = array();


echo "<pre>";
print_r($params);
echo "</pre>";
exit();

$operacja_id = 0;

if (!empty($params['operacja_id'])) {
    $operacja_id = $params['operacja_id'];
}

if (empty($operacja_id)) {
    $operacja_id = docnumber_increment($baza_danych, "operacja_id", $db);
}


if ($params['akcja'] == 'wstawianie') {

    $paleta_id = docnumber_increment($baza_danych, 'nrpalety', $db);
    insert_nosnika($baza_danych, $paleta_id, $db);
    $nretykiety = docnumber_increment($baza_danych, 'nretykiety', $db);
    $idetykieta = insert_etykieta($baza_danych, $params, $db);
    $komunikat['komunikat']="OK";
    $komunikat['operacja_id']=$operacja_id;
    return xml_from_indexed_array($komunikat);
}

function insert_etykieta($baza_danych, $params, $db)
{
    $sql = "insert into etykiety(system_id,kod_id,paleta_id,dataprod,data_waznosci,ilosc,"
        + " lot,nretykiety,edycja_et,etykieta_klient,ts,listcontrol_id)
            values(  '" . $params['system_id'] . "' ," .
        "  '" . $params['kod_id'] . "' ," .
        "  '" . $params['paleta_id'] . "' ," .
        "  '" . $params['dataprod'] . "' ," .
        "  '" . $params['data_waznosci'] . "' ," .
        "  '" . $params['ilosc'] . "' ," .
        "  '" . $params['lot'] . "' ," .
        "  '" . $params['nretykiety'] . "' ," .
        "  '" . $params['edycja_et'] . "' ," .
        "  '" . $params['etykieta_klient'] . "' ," .
        "  '" . $params['ts'] . "' ," .
        "  '" . $params['listcontrol_id'] . "' );";

echo $sql;
    //$result = $db->mGetResultAsXML($sql);
    return $result;
}



function insert_nosnika($baza_danych, $paleta_id, $db)
{
    $sql = "insert into $baza_danych.palety(id, typypalet_id,ilosc,j_skladowania_id, ts_utworzenia)
            values(  '" . $paleta_id . "' ," .
        "  1 ," .
        "  1 ," .
        "  1 ,"
        . "NOW());"
        . "  ";
    $result = $db->mGetResultAsXML($sql);
    return $result;
}
