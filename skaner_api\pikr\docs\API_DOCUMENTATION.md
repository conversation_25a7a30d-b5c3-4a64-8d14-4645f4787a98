# Dokumentacja API - api.php

## Opis ogólny
Plik `api.php` zawiera implementację API dla systemu obsługi linii produkcyjnej (deklaracji). API umożliwia rozpoczynanie i kończenie pracy osób na linii produkcyjnej oraz zarządzanie deklaracjami produkcyjnymi. Komunikacja z API odbywa się poprzez zapytania HTTP, a odpowiedzi są zwracane w formacie XML.

## Punkty końcowe (Endpoints)

### Dodawanie osoby do linii produkcyjnej
**Endpoint:** `api.php?akcja=dodawanie`

**Metoda:** GET

**Parametry:**
- `typ_operacji` (wymagany) - typ operacji, możliwe warto<PERSON>ci: "start" lub "stop"
- `godzina` (wymagany) - godzina operacji w formacie HH:MM lub H:MM
- `nr_karty` (wymagany) - numer karty osoby
- `wyrob_id` (wymagany) - identyfikator wyrobu
- `zadaniem` (opcjonalny) - zadanie
- `utworzyl` (opcjonalny) - kto utworzył wpis, domyślnie: "api"
- `przenies_osobe` (opcjonalny) - czy przenieść osobę z innej deklaracji, wartość: "tak"

**Scenariusze:**

1. **Rozpoczęcie pracy (typ_operacji=start):**
   - Jeśli osoba nie jest jeszcze w żadnej deklaracji: dodaje osobę do deklaracji
   - Jeśli osoba jest już w innej deklaracji: prosi o potwierdzenie przeniesienia
   - Jeśli osoba jest już w tej samej deklaracji: zwraca błąd

2. **Zakończenie pracy (typ_operacji=stop):**
   - Kończy pracę osoby w deklaracji
   - Aktualizuje czasy w deklaracji wyrobów

**Odpowiedzi XML:**

Sukces:
```xml
<dane>
    <status>success</status>
    <message>Rozpoczęto pracę dla [imię_nazwisko]</message>
    <deklaracja_id>[id_deklaracji]</deklaracja_id>
</dane>
```

Błąd:
```xml
<dane>
    <komunikat>Treść błędu</komunikat>
</dane>
```

Żądanie potwierdzenia:
```xml
<dane>
    <status>confirm</status>
    <message>Osoba [...] jest już na deklaracji: [...]. Czy chcesz przenieść osobę do nowej deklaracji?</message>
    <osoba_id>[id_osoby]</osoba_id>
    <deklaracja_id>[id_deklaracji]</deklaracja_id>
</dane>
```

## Funkcje pomocnicze

### dodaj_osobe_do_linii
Główna funkcja obsługująca proces dodawania/usuwania osoby z linii produkcyjnej.

### sprawdz_osobe
Sprawdza dane osoby na podstawie numeru karty.

### sprawdz_czy_osoba_w_deklaracji
Sprawdza, czy osoba jest już w deklaracji w danym dniu.

### znajdz_lub_utworz_deklaracje
Znajduje istniejącą deklarację dla danego wyrobu i daty lub tworzy nową.

### dodaj_osobe_do_deklaracji
Dodaje osobę do deklaracji.

### zakoncz_prace_osoby
Kończy pracę osoby w deklaracji.

### get_max_nr_deklaracji_from_setup_and_increment
Pobiera i zwiększa numer deklaracji z tabeli setup.

### update_time_in_deklaracja_wyroby
Aktualizuje czasy w deklaracji wyrobów.

### waliduj_format_czasu
Waliduje format podanego czasu (HH:MM lub H:MM).

## Walidacja danych

API przeprowadza walidację wszystkich przesyłanych danych:
- Sprawdzanie wymaganych parametrów
- Walidacja formatu czasu (HH:MM lub H:MM)
- Sprawdzanie poprawności typu operacji (start/stop)
- Weryfikacja istnienia osoby o podanym numerze karty

## Format odpowiedzi

API zawsze zwraca odpowiedzi w formacie XML. Kodowanie znaków to `UTF-8`.

## Logika biznesowa

System jest zaimplementowany w PHP i składa się z głównego pliku `api.php`, który działa jako punkt wejściowy, klasy `ApiLogika.class.php` zawierającej logikę biznesową, klasy `Dab.class.php` do obsługi połączenia z bazą danych MySQL oraz pliku `funkcje.inc` z funkcjami pomocniczymi, w tym do generowania odpowiedzi XML.

### Przebieg Operacji (Logika w `ApiLogika.class.php`)

1.  **Walidacja parametrów**: Sprawdzane są wszystkie wymagane parametry oraz format godziny.
2.  **Sprawdzenie osoby**: Weryfikacja, czy osoba o podanym `nr_karty` istnieje w bazie.
3.  **Obsługa deklaracji**: 
    *   Wyszukiwana jest istniejąca deklaracja dla danego `wyrob_id` i bieżącej daty.
    *   Jeśli deklaracja nie istnieje, tworzona jest nowa, a jej numer jest pobierany i inkrementowany z tabeli `setup` (klucz `max_nr_deklaracji`).
4.  **Sprawdzenie konfliktu**: System sprawdza, czy osoba nie jest już zalogowana na innej deklaracji w tym samym dniu.
    *   Jeśli tak, i parametr `przenies_osobe` nie jest `tak`, zwracany jest komunikat `confirm` z pytaniem o przeniesienie.
    *   Jeśli `przenies_osobe=tak`, osoba jest automatycznie wyrejestrowywana z poprzedniej deklaracji i rejestrowana na nowej.
5.  **Operacja START (`typ_operacji=start`)**:
    *   Jeśli nie ma konfliktu lub jest rozwiązany, osoba jest dodawana do tabeli `deklaracja_osoby` z czasem rozpoczęcia pracy.
    *   Zwracany jest komunikat `success`.
6.  **Operacja STOP (`typ_operacji=stop`)**:
    *   Wyszukiwany jest aktywny wpis osoby w `deklaracja_osoby` dla bieżącej daty.
    *   Jeśli wpis istnieje, aktualizowany jest czas zakończenia pracy (`stop`).
    *   Aktualizowane są również czasy `start` i `stop` w tabeli `deklaracja_wyroby` na podstawie minimalnego startu i maksymalnego stopu wszystkich osób na tej deklaracji.
    *   Zwracany jest komunikat `success`.
