﻿using System;
using System.Linq;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using MySql.Data.MySqlClient;
using System.Text.RegularExpressions;
using System.Threading;
using System.Net;


using System.Collections;

using System.IO;


namespace Tarczyn__Magazyn
{
    public partial class ZadaniaZM1 : Form
    {
        ZadaniaMain myParent = null;
        List<string> _hala = new List<string>();
        List<string> _regal = new List<string>();
        List<string> _poziom = new List<string>();
        private static string zm_date = "";
        List<string> Etykiety_dodane = new List<string>();
        int z = 0;
        int licznik = 0;
        public string poziom = "";
        public string prawa = "";
        public string lewa = "";

        public string hala = "";
        public string regal = "";
        public string miejsce = "";
        public string paleta_id = "";

        public string regal_ostatnio = "";

        public string wysokosc_opis = "";
        public string grupa_id = "";

        string operac_id_global = "";
        int nowe_m = 0;

        public string ostatnio_hala = "";
        public string ostatnio_regal = "";
        public string ostatnio_miejsce = "";
        public string id_zmiany_miejsca_niezrealizowane = "0";



        private Thread Skanowanie;
        private Thread Nasluchiwanie;

        StringBuilder next = null;


        Dictionary<string, string> rec = new Dictionary<string, string>();


        public ZadaniaZM1(ZadaniaMain MyParent)
        {
            //
            //MessageBox.Show("ZadaniaZmianaMiejsca ");
            InitializeComponent();

            //this.etykieta_textbox.KeyPress += new KeyPressEventHandler(etykieta_textbox_KeyPress);
            this.myParent = MyParent;
            wms_wybrany.Text = Wlasciwosci.system_id;
            imie_nazwisk_label.Text = Wlasciwosci.wozek + ":" + Wlasciwosci.imie_nazwisko;
            //FERRERO_Synchronizacja.SprawdzWolnaNazwePliku();
            timer1.Enabled = false;


            Lokalizacja_label.Text = "H:" + myParent.hala + " " + myParent.regal + "-" + myParent.miejsce + "-" + myParent.poziom;
            nosnik_text.Text = "DS"+myParent.paleta_id;
            zawartosc_label.Text = myParent.kod + " ; " + myParent.lot + " ; " + myParent.ilosc;
            textBox1.Focus();


            this.ZacznijSkanowanie();
        }

      





        private void etykieta_textbox_KeyPress(object sender, KeyPressEventArgs e)
        {
            if (e.KeyChar == (char)Keys.Back)
            {
                e.Handled = true;
                //etykieta_textbox.Text = "";
            }
            else
            {
                e.Handled = false;
                return;
            }
        }



       


        private void Zakoncz_Skanowanie(object sender, EventArgs e)
        {
            this.Skanowanie.Abort();
            Skaner.Przewij_Skanowanie();
        }

        private void powrot_Click(object sender, EventArgs e)
        {
            string zapyt = "update zadania z set pracownik_id=0,czas_pobrania='0000-00-00 00:00:00' WHERE z.id=" + myParent.zadanie_id + " limit 1";
            BazaDanychExternal.Wyczytaj_Jedna_Wartosc(zapyt);

            this.myParent.tryb_pracy = "pobieranie_pauza";

            this.Skanowanie.Abort();
            Skaner.Przewij_Skanowanie();
            Wlasciwosci.CurrentOperacja = "0";
            //myParent.wczytwanieEtykiet = false;
            this.myParent.Show();
            timer1.Enabled = false;
            this.Close();
                
            

        }



        private void button1_Anuluj(object sender, EventArgs e)
        {

        }

        


        public void ZacznijSkanowanie()
        {
            StringBuilder login = new StringBuilder();
            Skaner.UstawTryb_String(login);
            Skaner.Zacznij_Skanowanie();
            this.Skanowanie = new Thread(() => this.CheckString(login));
            this.Skanowanie.IsBackground = true;
            this.Skanowanie.Start();
        }



        private void CheckString(StringBuilder cc)
        {
            EventHandler method = null; //to jest delegat

            while (true)
            {
                if (cc.ToString() != "")
                {
                    if (method == null)
                    {
                        method = delegate
                        {
                            this.dodawanie(cc.ToString());
                        };
                    }
                    this.Invoke(method);
                    return;
                }
                Thread.Sleep(200);
            }
        }




        



        // 'Internal' - wyszukuje w bazie etykiete i wypełnia pola
        private void dodawanie(string ops)
        {
            Skaner.Przewij_Skanowanie();
            
            if(ops.Substring(0, 2) != "DS")
            {
                MessageBox.Show("To nie jest etykieta ");
                ZacznijSkanowanie();
                return;
            }


            if (ops.Substring(0, 2) == "DS")
            {
                string zapytanie = "SELECT z.id,z.paleta_id, z.stare_m, z.nowe_m, z.zgodnosc_towaru,z.zgodnosc_miejsca,hala,regal,miejsce,poziom,k.kod,ifnull(e.lot,'') as lot,cast(TRIM(TRAILING '.' FROM TRIM(TRAILING '0' from e.ilosc)) as char) as ilosc FROM zadania z left join miejsca m on m.id=z.stare_m left join etykiety e on e.paleta_id=z.paleta_id left join kody k on e.kod_id=k.id WHERE z.paleta_id=" + ops.Replace("DS", "") + " and z.status=1 order by id desc limit 1";

                DataTable temp = (DataTable)BazaDanychExternal.Wyczytaj_Tabele(zapytanie); //or active is null

                if (temp.Rows.Count < 1)
                {

                    string zapytanie3 = "SELECT e.paleta_id,hala,regal,miejsce,poziom,k.kod,ifnull(e.lot,'') as lot, cast(TRIM(TRAILING '.' FROM TRIM(TRAILING '0' from e.ilosc)) as char) as ilosc FROM etykiety e left join miejsca m on m.id=e.miejscep left join kody k on e.kod_id=k.id WHERE e.paleta_id=" + ops.Replace("DS", "") + " and e.active=1 order by e.id desc limit 1";

                    DataTable temp3 = (DataTable)BazaDanychExternal.Wyczytaj_Tabele(zapytanie3); //or active is null

                    if (temp3.Rows.Count > 0)
                    {

                        if (myParent.zgodnosc_towaru == "1")
                        {
                            if (temp3.Rows[0]["kod"].ToString() != myParent.kod && temp3.Rows[0]["ilosc"].ToString() != myParent.ilosc)
                            {
                                MessageBox.Show("Wymagany jest kodu:" + myParent.kod + " w ilości:" + myParent.ilosc);
                                ZacznijSkanowanie();
                                return;
                            }
                        }
                        if (myParent.zgodnosc_towaru == "4")
                        {
                            if (temp3.Rows[0]["paleta_id"].ToString() != myParent.paleta_id && temp3.Rows[0]["ilosc"].ToString() != myParent.ilosc)
                            {
                                MessageBox.Show("Wymagany jest paleta:DS" + myParent.paleta_id + " w ilości:" + myParent.ilosc);
                                ZacznijSkanowanie();
                                return;
                            }
                        }

                        if (myParent.zgodnosc_towaru == "5")
                        {
                            if (temp3.Rows[0]["kod"].ToString() != myParent.kod && temp3.Rows[0]["lot"].ToString() != myParent.lot && temp3.Rows[0]["ilosc"].ToString() != myParent.ilosc)
                            {
                                MessageBox.Show("Wymagany jest kod:" + myParent.kod + " i lot:" + myParent.lot + " w ilości:" + myParent.ilosc);
                                ZacznijSkanowanie();
                                return;
                            }
                        }

                        string zapyt = "update zadania z set paleta_id=" + ops.Replace("DS", "") + " ,czas_pobrania=NOW() WHERE  z.id=" + myParent.zadanie_id + " limit 1";
                        BazaDanychExternal.Wyczytaj_Jedna_Wartosc(zapyt);
                        myParent.ilosc_realizanych_nosnikow += 1;

                    }
                    else
                    {
                        MessageBox.Show("Brak zadania związanego danym nośnikiem");
                        ZacznijSkanowanie();
                        return;
                    }
                    

                }
                else
                {
                    if (myParent.zgodnosc_towaru == "1")
                    {
                        if (temp.Rows[0]["kod"].ToString() != myParent.kod && temp.Rows[0]["ilosc"].ToString() != myParent.ilosc)
                        {
                            MessageBox.Show("Wymagany jest kodu:" + myParent.kod + " w ilości:" + myParent.ilosc);
                            ZacznijSkanowanie();
                            return;
                        }
                    }
                    if (myParent.zgodnosc_towaru == "4")
                    {
                        if (temp.Rows[0]["paleta_id"].ToString() != myParent.paleta_id && temp.Rows[0]["ilosc"].ToString() != myParent.ilosc)
                        {
                            MessageBox.Show("Wymagany jest paleta:DS" + myParent.paleta_id + " w ilości:" + myParent.ilosc);
                            ZacznijSkanowanie();
                            return;
                        }
                    }

                    if (myParent.zgodnosc_towaru == "5")
                    {
                        if (temp.Rows[0]["kod"].ToString() != myParent.kod && temp.Rows[0]["lot"].ToString() != myParent.lot &&  temp.Rows[0]["ilosc"].ToString() != myParent.ilosc)
                        {
                            MessageBox.Show("Wymagany jest kod:" + myParent.kod + " i lot:" + myParent.lot + " w ilości:" + myParent.ilosc);
                            ZacznijSkanowanie();
                            return;
                        }
                    }

                    myParent.paleta_id = "DS" + temp.Rows[0]["paleta_id"].ToString();
                    myParent.hala = temp.Rows[0]["hala"].ToString();
                    myParent.regal = temp.Rows[0]["regal"].ToString();
                    myParent.miejsce = temp.Rows[0]["miejsce"].ToString();
                    myParent.poziom = temp.Rows[0]["poziom"].ToString();

                    myParent.zgodnosc_towaru = temp.Rows[0]["zgodnosc_towaru"].ToString();
                    myParent.zgodnosc_miejsca = temp.Rows[0]["zgodnosc_miejsca"].ToString();
                    
                    myParent.kod = temp.Rows[0]["kod"].ToString();
                    myParent.lot = temp.Rows[0]["lot"].ToString();
                    myParent.ilosc = temp.Rows[0]["ilosc"].ToString();
                    myParent.zadanie_id = temp.Rows[0]["id"].ToString();
                    string zapyt = "update zadania z set pracownik_id=" + Wlasciwosci.id_Pracownika + ",czas_pobrania=NOW() WHERE  z.id=" + temp.Rows[0]["id"].ToString() + " limit 1";
                    BazaDanychExternal.Wyczytaj_Jedna_Wartosc(zapyt);
                    myParent.ilosc_realizanych_nosnikow += 1;

                }

            }





            if (Wlasciwosci.wozek_ilosc_nosnikow == myParent.ilosc_realizanych_nosnikow)
            {                
                this.myParent.tryb_pracy = "odkladanie";
            }
            this.myParent.ZacznijNasluchiwanie();
            this.myParent.Show();
            this.Close();
           
        }



        private void timer1_Tick(object sender, EventArgs e)
        {
            this.Skanowanie.Abort();
            Skaner.Przewij_Skanowanie();

            //myParent.wczytwanieEtykiet = false;
            this.myParent.Show();
            timer1.Enabled = false;
            this.Close();


        }



        #region fullscreen
        private void fullscreenmode()
        {
            this.Width = Screen.PrimaryScreen.Bounds.Width;
            this.Height = Screen.PrimaryScreen.Bounds.Height;
            this.Left = 0;
            this.Top = 0;
        }
        #endregion

      


       






    }
}