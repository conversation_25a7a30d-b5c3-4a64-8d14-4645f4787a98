<?php

$db_name = 'wmsgg';
$app_config_file = "/etc/www_pass/" . $db_name . ".env";

if (file_exists($app_config_file)) {
    $CONF = parse_ini_file($app_config_file);
} else {
    die("Brak pliku konfiguracji: $app_config_file");
}

$conn = new mysqli($CONF['DB_HOST'], $CONF['DB_USER'], $CONF['DB_PASSWORD'], $CONF['DB_NAME']);

$mysql_host = $CONF['DB_HOST'];
$port = '3306'; //domyślnie jest to port 3306
$username = $CONF['DB_USER'];
$password = $CONF['DB_PASSWORD'];
$database = $CONF['DB_NAME'];

try {
    $pdo = new PDO('mysql:host=' . $CONF['DB_HOST'] . ';dbname=' . $CONF['DB_NAME'] . ';port=' . '3306', $CONF['DB_USER'], $CONF['DB_PASSWORD']);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    echo 'Połączenie nawiązane!';
} catch (PDOException $e) {
    echo 'Połączenie nie mogło zostać utworzone.<br />';
}
