<?php

include_once 'Db.class.php';
include_once 'funkcje.inc';

$db = new Db();

$baza_danych = $_GET['db'];
$akcja = $_GET['akcja'];
$hala = $_GET['hala'];
$regal = $_GET['regal'];
$limit = $_GET['limit'];
$operac_id = $_GET['operac_id'];
$imie_nazwisko = $_GET['imie_nazwisko'];

$komunikat = "OK";

if ($akcja == "szukaj") {
    $wynik = wyswietl_wolne_miejsca($baza_danych, $hala, $regal, $limit, $db);

    if (empty($operac_id)) {
        $operac_id = docnumber_increment($baza_danych, "operacja_id", $db);
    }
    $sql = "insert into operacje(etykieta_id, doc_type, doc_nr, imie_nazwisko, typ_operacji,system_id,wozek,operac_id,ilosc) values('0','','0','" . $imie_nazwisko . "','WOLN_MIEJ','0', '0','" . $operac_id . "','1');";
    $result8 = $db->mGetResultAsXML($sql);

    return xml_from_indexed_array(array('komunikat' => $komunikat, 'operac_id' => $operac_id, 'wynik' => $wynik));
}

function wyswietl_wolne_miejsca($baza_danych, $hala, $regal, $limit, $db) {
    $sql = "
    select a.regal,a.miejsce,a.poziom,sum(a.aktywne) as aktywne from (
        SELECT m.id,m.hala, m.regal, m.miejsce, m.poziom, m.wysokosc,   
        ( select count(distinct e.miejscep,e.paleta_id) from $baza_danych.etykiety e  
        where e.active=1 and e.miejscep=m.id )  as aktywne 
        FROM $baza_danych.miejsca m 
        where widoczne=1 and  m.hala = '$hala' AND m.regal='$regal'
        GROUP BY m.hala, m.regal, m.miejsce, m.poziom
    ) as a 
    GROUP BY a.hala, a.regal, a.miejsce, a.poziom 
    having aktywne=0 
    ORDER BY hala, regal, miejsce, poziom 
    limit $limit,8
    ";
    
    $result = $db->mGetResultAsXML($sql);
    return $result;
}