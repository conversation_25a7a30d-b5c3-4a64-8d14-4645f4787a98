﻿using System;
using System.Linq;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
//using MySql.Data.MySqlClient;

namespace Tarczyn__Magazyn
{
    public partial class Wybierz_Poziom_ZM : Form
    {

        //public MySqlConnection conn_FERRERO = null;

        ZmianaMiejscRegal aa = null;
        ZadaniaZM2 bb = null;
        InwentaryzacjaMiejsca cc = null;
        List<string> _poziom = new List<string>();
        string poziom = "";
        string prawa = "0";
        string lewa = "0";
        string tryb = "";


        public Wybierz_Poziom_ZM(ZmianaMiejscRegal objekt, string hala_local, string regal_local, string miejsce_local)
        {
            tryb = "aa";
            this.aa = objekt;
            InitializeComponent();
            _poziom.Add("");
            string zapytanie = "";
            zapytanie = "SELECT m.poziom FROM miejsca m where  poziom!='' and hala='" + hala_local + "' and regal='" + regal_local + "'  and miejsce='" + miejsce_local + "' and m.widoczne=1  GROUP BY m.poziom order by poziom DESC;";
            object obj2 = BazaDanychExternal.Wyczytaj_Tabele(zapytanie);
            DataTable tabela = (DataTable)obj2;

            for (byte k = 0; k < tabela.Rows.Count; k++)
            {
                _poziom.Add(tabela.Rows[k]["poziom"].ToString());
            }
            listBox1.Focus();
            listBox1.DataSource = _poziom;
        }

        public Wybierz_Poziom_ZM(ZadaniaZM2 objekt, string hala_local, string regal_local, string miejsce_local)
        {
            tryb = "bb";
            this.bb = objekt;
            InitializeComponent();
            _poziom.Add("");
            string zapytanie = "";
            zapytanie = "SELECT m.poziom FROM miejsca m where  poziom!='' and hala='" + hala_local + "' and regal='" + regal_local + "'  and miejsce='" + miejsce_local + "' and m.widoczne=1  GROUP BY m.poziom order by poziom DESC;";
            object obj2 = BazaDanychExternal.Wyczytaj_Tabele(zapytanie);
            DataTable tabela = (DataTable)obj2;

            for (byte k = 0; k < tabela.Rows.Count; k++)
            {
                _poziom.Add(tabela.Rows[k]["poziom"].ToString());
            }
            listBox1.Focus();
            listBox1.DataSource = _poziom;
        }

        public Wybierz_Poziom_ZM(InwentaryzacjaMiejsca objekt, string hala_local, string regal_local, string miejsce_local)
        {
            tryb = "cc";
            this.cc = objekt;
            InitializeComponent();
            _poziom.Add("");
            string zapytanie = "";
            zapytanie = "SELECT m.poziom FROM miejsca m where  poziom!='' and hala='" + hala_local + "' and regal='" + regal_local + "'  and miejsce='" + miejsce_local + "' and m.widoczne=1  GROUP BY m.poziom order by poziom DESC;";
            object obj2 = BazaDanychExternal.Wyczytaj_Tabele(zapytanie);
            DataTable tabela = (DataTable)obj2;

            for (byte k = 0; k < tabela.Rows.Count; k++)
            {
                _poziom.Add(tabela.Rows[k]["poziom"].ToString());
            }
            listBox1.Focus();
            listBox1.DataSource = _poziom;
        }



        private void Wybierz_Poziom_Powrot(object sender, EventArgs e)
        {
            if (_poziom[listBox1.SelectedIndex] != "")
            {
                poziom = _poziom[listBox1.SelectedIndex];
                //if (lewej.Checked == true) { lewa = "1"; };
                //if (prawej.Checked == true) { prawa = "1"; };

                if (tryb == "aa")
                {
                    aa.ustaw_poziom(poziom, prawa, lewa);
                    aa.Show();
                    
                }
                if (tryb == "bb")
                {
                    bb.ustaw_poziom(poziom);                    
                    bb.myParent.Show();
                }
                if (tryb == "cc")
                {
                    cc.ustaw_poziom(poziom);
                    cc.Show();
                }
                this.DialogResult = DialogResult.Yes;
            }
        }

        private DateTime _lastKeyPress;
        private string _searchString;

        private void wybor_klawiatura(object sender, KeyPressEventArgs e)
        {
            var newDate = DateTime.Now;
            var diff = newDate - _lastKeyPress;

            if (diff.TotalSeconds >= 1.5)
                _searchString = string.Empty;
            _searchString += e.KeyChar;

            //MessageBox.Show(e.KeyChar.ToString());
            if (e.KeyChar.ToString() == "1") _searchString = "A";
            if (e.KeyChar.ToString() == "2") _searchString = "B";
            if (e.KeyChar.ToString() == "3") _searchString = "C";
            if (e.KeyChar.ToString() == "4") _searchString = "D";
            if (e.KeyChar.ToString() == "5") _searchString = "E";
            if (e.KeyChar.ToString() == "6") _searchString = "F";

            for (var i = 0; i < listBox1.Items.Count; i++)
            {
                var item = listBox1.Items[i].ToString();
                if (item.ToLower().StartsWith(_searchString.ToLower()))
                {
                    listBox1.SelectedItem = item;
                    break;
                }
            }
            _lastKeyPress = newDate;
            e.Handled = true; //REALLY IMPORTANT TO HAVE THIS
        }

        private void listBox1_SelectedIndexChanged(object sender, EventArgs e)
        {
            System.Threading.Thread.Sleep(50);
            if (_poziom[listBox1.SelectedIndex] != "")
            {
                poziom = _poziom[listBox1.SelectedIndex];
                //if (lewej.Checked == true) { lewa = "1"; };
                //if (prawej.Checked == true) { prawa = "1"; };

                if (tryb == "aa")
                {
                    aa.ustaw_poziom(poziom, prawa, lewa);
                    aa.Show();
                }
                if (tryb == "bb")
                {                   
                    bb.Show();
                    bb.ustaw_poziom(poziom);
                    //this.Close();
                }
                if (tryb == "cc")
                {
                    cc.ustaw_poziom(poziom);
                    cc.Show();
                }
                this.DialogResult = DialogResult.Yes;
            }
            //button1.Focus();
        }
    }



}