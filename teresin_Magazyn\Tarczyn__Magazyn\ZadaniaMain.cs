﻿using System;
using System.Linq;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using MySql.Data.MySqlClient;
using System.Text.RegularExpressions;
using System.Threading;
using System.Net;


using System.Collections;

using System.IO;


namespace Tarczyn__Magazyn
{
    public partial class ZadaniaMain : Form
    {
        ActionMenu myParent = null;
        List<string> _hala = new List<string>();
        List<string> _regal = new List<string>();
        List<string> _poziom = new List<string>();
        private static string zm_date = "";
        private static string zm_nr_global = "";

        List<string> Etykiety_dodane = new List<string>();
        int z = 0;
        int licznik = 0;


        public string regal_ostatnio = "";

        public string wysokosc_opis = "";
        public string grupa_id = "";

        string operac_id_global = "";
        

        public string ostatnio_hala = "";
        public string ostatnio_regal = "";
        public string ostatnio_miejsce = "";
        public string id_zmiany_miejsca_niezrealizowane = "0";



        private Thread Skanowanie;
        private Thread Nasluchiwanie;

        public string paleta_id = "";
        public string hala = "";
        public string regal = "";
        public string miejsce = "";
        public string poziom = "";


        public string zgodnosc_towaru = "";
        public string zgodnosc_miejsca = "";
        public string zadanie_id = "";
        public string kod = "";
        public string lot = "";
        public string ilosc = "";

        


        public List<string> realizowane_palety = new List<string>();
        public List<string> zadania_lista_id = new List<string>();


        public string tryb_pracy = "odkladanie";
        public string czas_przydzielenia = "";
        public string czas_pobrania = "";
        public string czas_realizacji = "";
        public int ilosc_realizanych_nosnikow = 0;
        public string stanowisko_id = "0";
        public string kierunek = "IN";
        public int nowe_m = 0;





        StringBuilder next = null;


        Dictionary<string, string> rec = new Dictionary<string, string>();


        public ZadaniaMain(ActionMenu MyParent)
        {
            //
            InitializeComponent();
            //this.etykieta_textbox.KeyPress += new KeyPressEventHandler(etykieta_textbox_KeyPress);
            this.myParent = MyParent;
            wms_wybrany.Text = Wlasciwosci.system_id;
            imie_nazwisk_label.Text = Wlasciwosci.wozek + ":" + Wlasciwosci.imie_nazwisko;
            //FERRERO_Synchronizacja.SprawdzWolnaNazwePliku();
            timer1.Enabled = false;
            Wlasciwosci.CurrentOperacja = "11";

            if (BazaDanychExternal.SprawdzCzyIstniejePolaczenie())
            {
                //    Sprawdz_zgodnosc_opisow();

                string zapytanie = "SELECT last FROM docnumber d WHERE d.name='operacja_id';";

                operac_id_global = (string)BazaDanychExternal.Wyczytaj_Jedna_Wartosc(zapytanie);

                string zapyt = "update docnumber d set last=last+1 WHERE d.name='operacja_id'; ";
                BazaDanychExternal.Wyczytaj_Jedna_Wartosc(zapyt);
            }
            string stanowisko_nazwa="";

            PoleCombobox XC = new PoleCombobox("SELECT id,nazwa FROM zadania_stanowiska", "Wybierz stanowisko", "");
            if (XC.ShowDialog() == DialogResult.OK)
            {
                if (XC.wybierana_nazwa == "")
                {
                    MessageBox.Show("Nie dokonano wyboru");
                    this.Nasluchiwanie.Abort();
                    Wlasciwosci.CurrentOperacja = "0";
                    //myParent.wczytwanieEtykiet = false;
                    this.myParent.Show();
                    timer1.Enabled = false;
                    this.Close();

                    return;
                }
                stanowisko_id = XC.wybierane_id;
                stanowisko_nazwa = XC.wybierana_nazwa;
                label5.Text = "Stanowisko:" + stanowisko_nazwa;
            }
            else
            {
                

            }
                     


            this.ZacznijNasluchiwanie();
        }

        private void Nasluchowanie_metoda()
        {
            EventHandler method = null; //to jest delegat

            while (true)
            {

                if (method == null)
                {
                    method = delegate
                    {
                        //bb += 1;
                        this.Nasluchowanie_wykonawca();
                    };
                }
                this.Invoke(method);
                //return;
                Thread.Sleep(5000);
            }

        }

        public string czas_bazy()
        {
            return (string)BazaDanychExternal.Wyczytaj_Jedna_Wartosc("select NOW() as czas_bazy");            
        }



        public static string zadan_aktywnych(string stanowisko_local)
        {
            string zapytanie = "SELECT count(1) FROM zadania z WHERE z.status=1 and pracownik_id=0";
            if (stanowisko_local != "0")                
            {
                zapytanie = "SELECT count(1) FROM zadania z WHERE z.status=1 and pracownik_id=0 and stanowisko_id=" + stanowisko_local + " ";
            }

            string aa = (string)BazaDanychExternal.Wyczytaj_Jedna_Wartosc(zapytanie);
            return aa;
        }
        public static string zadan_zrealizowanych()
        {
            string zapytanie = "SELECT count(1) FROM zadania z WHERE z.pracownik_id="+Wlasciwosci.id_Pracownika+" and z.czas_realizacji like concat(CURDATE(),'%')";
            string aa = (string)BazaDanychExternal.Wyczytaj_Jedna_Wartosc(zapytanie);
            return aa;
        }



        private bool PrzydzielanieZadan()
        {
            string URL = "http://172.6.1.249/wmsgg/public/skrypty/zadania_wms/przydzielanie_zadan_produkcja.php";
            const string data = @"{""object"":{""name"":""Title""}}";

            HttpWebRequest request = (HttpWebRequest)WebRequest.Create(URL);
            request.Method = "POST";
            request.ContentType = "application/json";
            request.ContentLength = data.Length;
            StreamWriter requestWriter = new StreamWriter(request.GetRequestStream(), System.Text.Encoding.ASCII);
            requestWriter.Write(data);
            requestWriter.Close();

            try
            {
                // get the response
                WebResponse webResponse = request.GetResponse();
                Stream webStream = webResponse.GetResponseStream();
                StreamReader responseReader = new StreamReader(webStream);
                string response = responseReader.ReadToEnd();
                responseReader.Close();
                return true;
            }
            catch (WebException we)
            {
                string webExceptionMessage = we.Message;
                return false;
            }
            catch (Exception ex)
            {
                // no need to do anything special here....
                return false;
            }

        }


        private void Nasluchowanie_wykonawca()
        {




            //while(realizowane_palety.Count<=Wlasciwosci.wozek_ilosc_nosnikow)
            //{
            //if (realizowane_palety.Count >= Wlasciwosci.wozek_ilosc_nosnikow)
            //{
            //    Nasluchiwanie.Abort();
            //}

            


            if (tryb_pracy == "pobieranie") 
            {
                //MessageBox.Show("Nasluchowanie_wykonawca pobieranie");
                string zapytanie = "SELECT z.id,z.paleta_id, z.stare_m, z.nowe_m, z.zgodnosc_towaru, m.hala,m.regal,m.miejsce,m.poziom, m2.hala as hala2,m2.regal as regal2,m2.miejsce as miejsce2,m2.poziom as poziom2, k.kod,ifnull(e.lot,'') as lot,cast(TRIM(TRAILING '.' FROM TRIM(TRAILING '0' from e.ilosc)) as char) as ilosc FROM zadania z left join miejsca m on m.id=z.stare_m left join miejsca m2 on m2.id=z.nowe_m left join etykiety e on e.paleta_id=z.paleta_id left join kody k on e.kod_id=k.id WHERE z.pracownik_id=" + Wlasciwosci.id_Pracownika + " and z.status=1 and z.czas_pobrania='0000-00-00 00:00:00'  limit 1";
                object obj2 = BazaDanychExternal.Wyczytaj_Tabele(zapytanie);
                DataTable tabela = (DataTable)obj2;

                for (byte k = 0; k < tabela.Rows.Count; k++)
                {
                    paleta_id = tabela.Rows[k]["paleta_id"].ToString();
                    hala = tabela.Rows[k]["hala"].ToString();
                    regal = tabela.Rows[k]["regal"].ToString();
                    miejsce = tabela.Rows[k]["miejsce"].ToString();
                    poziom = tabela.Rows[k]["poziom"].ToString();

                    zgodnosc_towaru = tabela.Rows[k]["zgodnosc_towaru"].ToString();
                    kod = tabela.Rows[k]["kod"].ToString();
                    lot = tabela.Rows[k]["lot"].ToString();
                    ilosc = tabela.Rows[k]["ilosc"].ToString();
                    nowe_m = Convert.ToInt32(tabela.Rows[k]["nowe_m"].ToString());

                    zadanie_id = tabela.Rows[k]["id"].ToString();
                    //BazaDanychExternal.DokonajUpdate("REPLACE INTO zadania_zgloszenia(pracownik_id, nr_wozka, ts, status, kierunek, stanowisko_id) values(" + Wlasciwosci.id_Pracownika + "," + Wlasciwosci.wozek + ",NOW(),'w_trakcje_realizacji','" + kierunek + "','" + stanowisko_id + "'); ");

                    //string zapyt = "update zadania z set czas_przydzielenia=NOW() WHERE z.id=" + tabela.Rows[0]["id"].ToString() + " limit 1";
                    //BazaDanychExternal.Wyczytaj_Jedna_Wartosc(zapyt);


                    this.Nasluchiwanie.Abort();
                    ZadaniaZM1 aa = new ZadaniaZM1(this);
                    aa.Show();
                    this.Hide();
                    //}

                }
            }

            if (tryb_pracy == "pobieranie_pauza")
            {
                //MessageBox.Show("pobieranie_pauza");
                this.Show();
                BazaDanychExternal.DokonajUpdate("REPLACE INTO zadania_zgloszenia(pracownik_id, nr_wozka, ts, status, kierunek, stanowisko_id) values(" + Wlasciwosci.id_Pracownika + "," + Wlasciwosci.wozek + ",NOW(),'oczekuje_na_zadanie','" + kierunek + "','" + stanowisko_id + "'); ");
                label3.Text = "Do realizacji:" + zadan_aktywnych(stanowisko_id);
                label4.Text = "Zrealizowanych:" + zadan_zrealizowanych();
                //Thread.Sleep(3000);
                tryb_pracy = "pobieranie";
            } 






            if (tryb_pracy == "odkladanie")
            {
                //MessageBox.Show("Nasluchowanie_wykonawca odkladanie");



                string zapytanie = "SELECT z.id,z.paleta_id, z.stare_m, z.nowe_m, z.zgodnosc_towaru,hala,regal,miejsce,poziom,k.kod,ifnull(e.lot,'') as lot,cast(TRIM(TRAILING '.' FROM TRIM(TRAILING '0' from e.ilosc)) as char) as ilosc FROM zadania z left join miejsca m on m.id=z.nowe_m left join etykiety e on e.paleta_id=z.paleta_id left join kody k on e.kod_id=k.id WHERE z.status=1 and z.pracownik_id=" + Wlasciwosci.id_Pracownika + " and czas_realizacji='0000-00-00 00:00:00' and czas_pobrania!='0000-00-00 00:00:00'  order by priorytet DESC,czas_pobrania desc";
                object obj2 = BazaDanychExternal.Wyczytaj_Tabele(zapytanie);
                DataTable tabela = (DataTable)obj2;

                if (tabela.Rows.Count > 0)
                {

                    for (byte k = 0; k < tabela.Rows.Count; k++)
                    {
                        paleta_id = "DS" + tabela.Rows[k]["paleta_id"].ToString();
                        hala = tabela.Rows[k]["hala"].ToString();
                        regal = tabela.Rows[k]["regal"].ToString();
                        miejsce = tabela.Rows[k]["miejsce"].ToString();
                        poziom = tabela.Rows[k]["poziom"].ToString();

                        zgodnosc_towaru = tabela.Rows[k]["zgodnosc_towaru"].ToString();
                        kod = tabela.Rows[k]["kod"].ToString();
                        lot = tabela.Rows[k]["lot"].ToString();
                        ilosc = tabela.Rows[k]["ilosc"].ToString();

                        zadanie_id = tabela.Rows[k]["id"].ToString();




                        this.Nasluchiwanie.Abort();
                        //MessageBox.Show("odkladanie1");
                        ZadaniaZM2 bb = new ZadaniaZM2(this);
                        bb.Show();
                        this.Hide();
                        //}

                    }
                }
                else
                {

                    this.Nasluchiwanie.Abort();
                    tryb_pracy = "pobieranie_pauza";
                    this.ZacznijNasluchiwanie();
                }



            }




            //return;


        }







        private void etykieta_textbox_KeyPress(object sender, KeyPressEventArgs e)
        {
            if (e.KeyChar == (char)Keys.Back)
            {
                e.Handled = true;
                //etykieta_textbox.Text = "";
            }
            else
            {
                e.Handled = false;
                return;
            }
        }









        //ok

        private static string[] miejsce_aktualne(string etykieta) //pobiera
        {
            string zapytanie = "select m.hala,m.regal,m.miejsce,m.poziom from etykiety e left join miejsca m on e.miejscep=m.id where (e.id='" + etykieta + "' OR e.etykieta_klient='" + etykieta + "' ) and e.active=1 order by e.id desc limit 1";
            if (etykieta.Substring(0, 2) == "DS")
            {
                zapytanie = "select m.hala,m.regal,m.miejsce,m.poziom from etykiety e left join miejsca m on e.miejscep=m.id where (e.paleta_id='" + etykieta.Replace("DS", "") + "') and e.active=1 order by e.id desc limit 1";
            }

            object obj2 = BazaDanychExternal.Wyczytaj_Tabele(zapytanie);
            DataTable tabela = (DataTable)obj2;
            string[] bb = new string[4];
            bb[0] = tabela.Rows[0]["hala"].ToString();
            bb[1] = tabela.Rows[0]["regal"].ToString();
            bb[2] = tabela.Rows[0]["miejsce"].ToString();
            bb[3] = tabela.Rows[0]["poziom"].ToString();
            return bb;
        }



        public void Realizuj_Zmiane_Miejsca(string etykieta, string zm_type, string zm_date, string login, string hala, string regal, string miejsce, string poziom, string paleta_id, int nowe_m_local)
        {
            //MessageBox.Show(etykieta[0] + ";" + etykieta[1]);
            //MessageBox.Show("ZadaniaMain: Realizuj_Zmiane_Miejsca" + etykieta);


            if (etykieta.Substring(0, 2) == "DS")
            {
                DataTable temp = (DataTable)BazaDanychExternal.Wyczytaj_Tabele("select e.id,e.system_id,miejscep,paleta_id,regal,miejsce,poziom,ifnull(e.active,3) as active  from etykiety e left join miejsca m on e.miejscep=m.id where e.paleta_id=" + etykieta.Replace("DS", "") + " and (active=1 )"); //or active is null


                if (zm_nr_global == "") { zm_nr_global = (string)BazaDanychExternal.Wyczytaj_Jedna_Wartosc("select max(doc_nr)+1 FROM zmianym"); }
                if (temp.Rows.Count > 0)
                {
                    Wlasciwosci.system_id_id = temp.Rows[0][1].ToString();
                    //string nowemiejsce = miejscepaletowe2();
                    //nowe_m = 0;
                    //nowe_m = pobierz_id_miejsca(hala, regal, miejsce, poziom);
                    int licz = 0;
                    if (nowe_m_local != 0)
                    {
                        for (int t = 0; t < temp.Rows.Count; t++)
                        {

                            BazaDanychExternal.DokonajUpdate("insert into zmianym(typ,doc_nr,pracownik_id,data,etykieta,system_id,stare_m,nowe_m,doc_internal,stat,tszm) values(" +
                                "'ZM'," + zm_nr_global + "," + Wlasciwosci.id_Pracownika + ",sysdate()," + temp.Rows[t][0].ToString() + "," + temp.Rows[t][1].ToString() + "," + temp.Rows[t][2].ToString() + "," + nowe_m_local + ",3,1,sysdate())");

                            if (t == 0)
                            {

                                BazaDanychExternal.DokonajUpdate("insert into operacje(etykieta_id, doc_type, doc_nr, imie_nazwisko, typ_operacji,system_id,wozek,operac_id,ilosc) values('" + temp.Rows[t][0].ToString() + "','ZM','" + zm_nr_global + "','" + Wlasciwosci.imie_nazwisko + "','ZM','" + Wlasciwosci.system_id_id + "','" + Wlasciwosci.wozek + "','" + operac_id_global + "','1');");

                            }

                            if (temp.Rows[t]["active"].ToString() == "1")
                            {
                                licz += 1;
                            }

                        }


                        BazaDanychExternal.DokonajUpdate("update etykiety set miejscep = " + nowe_m_local + " where paleta_id=" + etykieta.Replace("DS", "") + " and (active=1 or active is null) ");
                        licznik++;
                        

                        /*
                        if (id_zmiany_miejsca_niezrealizowane != "0")
                        {
                            BazaDanychExternal.DokonajUpdate("delete from zmiany_miejsca_niezrealizowane where id='" + id_zmiany_miejsca_niezrealizowane + "';");
                            id_zmiany_miejsca_niezrealizowane = "0";
                        }
                        */

                        string[] kk = new string[4];
                        kk = miejsce_aktualne(etykieta);

                        //MessageBox.Show("Hala:"+kk[0]+";"+hala);
                        //MessageBox.Show("regal:" + kk[1] + ";" + regal);
                        //MessageBox.Show("miejsce:" + kk[2] + ";" + miejsce);
                        //MessageBox.Show("poziom:" + kk[3] + ";" + poziom);
                        if (kk[0] != hala || kk[1] != regal || kk[2] != miejsce || kk[3] != poziom)
                        {
                            licznik--;

                            MessageBox.Show("Nie udało się zmienić miejsca !!. " + Environment.NewLine + "Obecnie jest: H:" + kk[0] + " " + kk[1] + "-" + kk[2] + "-" + kk[3]);
                            return;
                        }

                        last_result_text.Text = "Zmieniono: " + etykieta + Environment.NewLine + "" + temp.Rows[0]["regal"] + "-" + temp.Rows[0]["miejsce"] + "-" + temp.Rows[0]["poziom"] + " -> H:" + kk[0] + " " + kk[1] + "-" + kk[2] + "-" + kk[3];


                        //for (int v = 0; v < this.realizowane_palety.Count; v++)
                        //{
                        //    if (this.realizowane_palety[v] == paleta_id)
                        //    {
                        //        this.realizowane_palety.RemoveAt(v);
                        //        this.zadania_lista_id.RemoveAt(v);

                        //        string zapyt1 = "update zadania z set czas_realizacji=NOW(),status='0',nowe_m_realizowane='" + nowe_m + "' WHERE z.paleta_id=" + paleta_id + " and pracownik_id=" + Wlasciwosci.id_Pracownika + "  and status=1 limit 1";
                        //        BazaDanychExternal.Wyczytaj_Jedna_Wartosc(zapyt1);
                        //        //this.zadanie_id
                        //        ZacznijNasluchiwanie();

                        //    }
                        //}
                        string zapyt1 = "update zadania z set czas_realizacji=NOW(),status='0',nowe_m_realizowane='" + nowe_m_local + "' WHERE z.paleta_id=" + paleta_id.Replace("DS", "") + " and pracownik_id=" + Wlasciwosci.id_Pracownika + "  and status=1 limit 1";

                        BazaDanychExternal.Wyczytaj_Jedna_Wartosc(zapyt1);
                        this.ilosc_realizanych_nosnikow -= 1;

                        if (this.ilosc_realizanych_nosnikow==0)
                        {
                            this.tryb_pracy = "pobieranie_pauza";
                        }
                        this.Nasluchiwanie.Abort();
                        this.ZacznijNasluchiwanie();


                    }
                    else
                    {
                        MessageBox.Show("Brak takiego miejsca w bazie. Spróbuj ponownie.");
                        return;
                    }


                }

                else
                {
                    MessageBox.Show("Brak takiej palety w systemie lub jest nieaktywna , spróbuj ponownie.");
                }
            }
            else
            {

                string zapytanie = "";
                zapytanie = "select IFNULL(e.miejscep,3) as miejscep ,IFNULL(e.active,3) as active,docout_type,docout_nr,e.id,m.hala,m.regal,m.miejsce,m.poziom,drobnicowe_miejsca from etykiety e left join docout d on e.docout_id=d.id left join miejsca m on e.miejscep=m.id left join systemy s on e.system_id=s.wartosc where (etykieta_klient='" + etykieta + "' or e.id='" + etykieta + "')  order by e.id desc limit 1";

                object obj2 = BazaDanychExternal.Wyczytaj_Tabele(zapytanie);
                if (obj2 == null)
                {
                    this.ZacznijSkanowanie();
                }
                DataTable table = (DataTable)obj2;

                DateTime ts = DateTime.Now;

                if (table.Rows.Count < 1)  //sprawdza czy jest etykieta
                {
                    MessageBox.Show("Brak w bazie et: " + etykieta + ".");

                }
                else                     // czy jest wydana z systemu
                    if (table.Rows[0]["active"].ToString() == "0")
                    {
                        MessageBox.Show("Et" + etykieta + " wydana : " + table.Rows[0]["docout_type"].ToString() + " - " + table.Rows[0]["docout_nr"].ToString() + ". Dodana do offline");

                    }
                    else
                    {

                        if (nowe_m_local == 0)
                        {
                            MessageBox.Show("Brak w bazie miejsca: Hala: " + hala + " " + regal + "-" + miejsce + "-" + poziom + ". Spróbuj jeszze raz.");

                        }
                        else
                        {

                            if (nowe_m_local.ToString() != table.Rows[0]["miejscep"].ToString())
                            {
                                if (table.Rows[0]["drobnicowe_miejsca"].ToString() == "1")
                                {
                                    //MessageBox.Show("drobnicowe_miejsca");
                                    string zap2 = "";
                                    zap2 = "SELECT ifnull(cast(max(paleta_id) as char),'brak') as paleta_id FROM etykiety e  where active=1 and system_id=" + Wlasciwosci.system_id_id + " and miejscep=" + nowe_m_local + ";";
                                    string pal_id_loc = (string)BazaDanychExternal.Wyczytaj_Jedna_Wartosc(zap2);
                                    //MessageBox.Show(pal_id_loc);
                                    if (pal_id_loc != "brak")
                                    {
                                        //MessageBox.Show("update etykiety e set e.paleta_id=" + pal_id_loc + " where e.id=" + table.Rows[0]["id"] + ";");
                                        BazaDanychExternal.DokonajUpdate("update etykiety e set e.paleta_id=" + pal_id_loc + " where e.id=" + table.Rows[0]["id"] + ";");
                                    }

                                }

                                BazaDanychExternal.DokonajUpdate("insert into operacje(etykieta_id, doc_type, doc_nr, imie_nazwisko, typ_operacji,system_id,wozek,operac_id,ilosc) values('" + table.Rows[0]["id"] + "','" + zm_type + "','" + zm_nr_global + "','" + Wlasciwosci.imie_nazwisko + "','ZM','" + Wlasciwosci.system_id_id + "','" + Wlasciwosci.wozek + "','" + operac_id_global + "','1');");

                                BazaDanychExternal.DokonajUpdate("update etykiety e set e.miejscep=" + nowe_m_local + " where e.id=" + table.Rows[0]["id"] + ";");

                                BazaDanychExternal.DokonajUpdate("insert into zmianym(typ,doc_nr,pracownik_id,data,etykieta,stare_m,nowe_m,doc_internal,stat,system_id) values ('" + zm_type + "','" + zm_nr_global + "','" + Wlasciwosci.id_Pracownika + "','" + zm_date + "','" + table.Rows[0]["id"] + "','" + table.Rows[0]["miejscep"].ToString() + "','" + nowe_m_local + "','Z','1'," + Wlasciwosci.system_id_id + ");");



                                label2.Text = "Zmieniono: " + table.Rows[0]["regal"] + "-" + table.Rows[0]["miejsce"] + "-" + table.Rows[0]["poziom"] + " -> " + regal + "-" + miejsce + "-" + poziom;
                            }
                            else
                            {
                                label2.Text = "Jest:" + table.Rows[0]["regal"] + " - " + table.Rows[0]["miejsce"] + " - " + table.Rows[0]["poziom"];
                            }

                        }
                    }
            }

        }








        private void Zakoncz_Skanowanie(object sender, EventArgs e)
        {
            this.Skanowanie.Abort();
            Skaner.Przewij_Skanowanie();
        }

        private void powrot_Click(object sender, EventArgs e)
        {
            //BazaDanychExternal.DokonajUpdate("REPLACE INTO zadania_zgloszenia(pracownik_id, nr_wozka, ts, status, kierunek, stanowisko_id) values(" + Wlasciwosci.id_Pracownika + "," + Wlasciwosci.wozek + ",NOW(),'opuszczenie_zadan','" + kierunek + "','" + stanowisko_id + "'); ");
                


            this.Nasluchiwanie.Abort();
            Wlasciwosci.CurrentOperacja = "0";
            //myParent.wczytwanieEtykiet = false;
            this.myParent.Show();
            timer1.Enabled = false;
            this.Close();



        }



        private void button1_Anuluj(object sender, EventArgs e)
        {

        }

        public void ZacznijNasluchiwanie()
        {
            StringBuilder login = new StringBuilder();
            this.Nasluchiwanie = new Thread(() => this.Nasluchowanie_metoda());
            this.Nasluchiwanie.IsBackground = true;
            this.Nasluchiwanie.Start();
        }

        private void Zakoncz_Nasluchiwanie(object sender, EventArgs e)
        {
            this.Nasluchiwanie.Abort();
        }


        public void ZacznijSkanowanie()
        {
            StringBuilder login = new StringBuilder();
            Skaner.UstawTryb_String(login);
            Skaner.Zacznij_Skanowanie();
            this.Skanowanie = new Thread(() => this.CheckString(login));
            this.Skanowanie.IsBackground = true;
            this.Skanowanie.Start();
        }



        private void CheckString(StringBuilder cc)
        {
            EventHandler method = null; //to jest delegat

            while (true)
            {
                if (cc.ToString() != "")
                {
                    if (method == null)
                    {
                        method = delegate
                        {
                            this.dodawanie(cc.ToString());
                        };
                    }
                    this.Invoke(method);
                    return;
                }
                Thread.Sleep(200);
            }
        }








        // 'Internal' - wyszukuje w bazie etykiete i wypełnia pola
        private void dodawanie(string ops)
        {

        }










        private void timer1_Tick(object sender, EventArgs e)
        {
            this.Skanowanie.Abort();
            Skaner.Przewij_Skanowanie();

            //myParent.wczytwanieEtykiet = false;
            this.myParent.Show();
            timer1.Enabled = false;
            this.Close();


        }



        #region fullscreen
        private void fullscreenmode()
        {
            this.Width = Screen.PrimaryScreen.Bounds.Width;
            this.Height = Screen.PrimaryScreen.Bounds.Height;
            this.Left = 0;
            this.Top = 0;
        }
        #endregion

        private void button1_Click(object sender, EventArgs e)
        {
            PoleCombobox XC = new PoleCombobox("SELECT id,nazwa FROM zadania_stanowiska", "Wybierz paletę", "");
            if (XC.ShowDialog() == DialogResult.OK)
            {
                if (XC.wybierana_nazwa == "")
                {
                    MessageBox.Show("Nie dokonano wyboru");
                    return;
                }

            }
            else
            {

            }
        }











    }
}