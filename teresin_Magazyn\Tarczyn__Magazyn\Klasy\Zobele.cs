﻿using System;
using System.Linq;
using System.Collections.Generic;
using System.Text;
using System.Xml;
using System.Xml.Linq;
using System.Reflection;
using System.IO;

namespace Tarczyn__Magazyn
{
    public class Zobele
    {
        public Zobele()
        {
            string[] WartosciXML = Wlasciwosci.wczytanieZobele();
            Etykieta = WartosciXML[0];
            GTIN = WartosciXML[1];
            SSC = WartosciXML[2];
            LOT = WartosciXML[3];
            DW = WartosciXML[4];
            QT = WartosciXML[5];
            USER = WartosciXML[6];
        }
        public void Zapis()
        {
            Wlasciwosci.ZapisXMLZobele(Etykieta, GTIN, SSC, LOT, DW, QT, USER);
        }
        public void Clear()
        {
        Etykieta = "";
        GTIN= "";
        SSC = "";
        LOT = "";
        DW = "";
        QT = "";
        USER = "";
        Wlasciwosci.ZapisXMLZobele(Etykieta, GTIN, SSC, LOT, DW, QT, USER);    
        }

        public string Etykieta;
        public string GTIN;
        public string SSC;
        public string LOT;
        public string DW;
        public string QT;
        public string USER;

    }
}
