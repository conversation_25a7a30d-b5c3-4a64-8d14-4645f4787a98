<?php

//include_once 'db_pdo.php';
include_once 'ArtsanaDb.class.php';
include_once 'Db.class.php';
include_once 'funkcje.inc';
//error_reporting(E_ALL);
//ini_set('display_errors', 1);

$artsanaDb = new ArtsanaDb();
$db = new Db();

$scan = $_GET['scan'];

$doc_ref = $_GET['doc_ref'];

$akcja = "szukaj";



$komunikat = "OK";

if ($akcja == "szukaj") {
    $result = wyswietl_doc_ref($scan, $artsanaDb);
    //print_r($dane);
    if (empty($result)) {
        return xml_from_indexed_array(array('komunikat' => "Nie znaleziono"));
    }
    $result2 = szukaj_head($result[0]['delivery_number'].",".$doc_ref , $db);
    $delivery_wymiary_head_id = "";
    if (!empty($result2)) {
        $delivery_wymiary_head_id = $result[0]['id'];
    }
    return xml_from_indexed_array(array('komunikat' => $komunikat, 'delivery_number' => $result[0]['delivery_number'], 'delivery_wymiary_head_id' => $delivery_wymiary_head_id));
}

function wyswietl_doc_ref($scan, $artsanaDb) {
    $sql = "SELECT delivery_number FROM artsana._hu
where _hu.hu_sscc_number='" . $scan . "' limit 1";
    $result = $artsanaDb->mGetResultAsXML($sql);

    return $result;
}

function szukaj_head($scan, $db) {
    $sql = 'SELECT id FROM wmsgg.delivery_wymiary_head
where dokument_dl="' . $scan . '" limit 1';
    //echo $sql;
    $result = $db->mGetResultAsXML($sql);

    return $result;
}

function sortuj_doc_ref($doc_ref) {
    $dane = "";
    $array = explode(',', $doc_ref);
    //print_r($array);
    sort($array);
    foreach ($array as $value) {
        if ($dane == "") {
            $dane = $value;
        } else {
            $dane .= "," . $value;
        }
    }
    return $dane;
}
