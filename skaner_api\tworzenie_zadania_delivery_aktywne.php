<?php

include_once 'Db.class.php';
include_once 'funkcje.inc';
//error_reporting(E_ALL);
//ini_set('display_errors', 1);

$db = new Db();

if (empty($argv)) {
    //$argv = array();
    $argv[1] = $_GET['db'];
    $argv[2] = $_GET['tb'];
    $argv[3] = $_GET['id'];
}

if ($argv[1] == '1') {
    $baza_danych = 'wmsgg';
}
if ($argv[1] == '2') {
    $baza_danych = 'wmsftl';
}
if ($argv[1] == '3') {
    $baza_danych = 'prod';
}

$tabela = $argv[2];
$id = $argv[3];


//if ($_GET['db'] == '1') {
//    $baza_danych = 'wmsgg';
//}
//
//$tabela = $_GET['tb'];
//$id = $_GET['id'];

echo "<br>aa,$baza_danych";

echo "<br>bb";
if ($tabela == "delivery") {
    echo "<br>cc";
    $typ = "4";
    $doc_type = "2";

    $sql = "SELECT e.system_id,d.id,d.miejsce_kompletacji FROM $baza_danych.delivery d
left join $baza_danych.delivery_et de on de.delivery_id=d.id
left join $baza_danych.etykiety e on de.etykieta_id=e.id where d.id=$id and e.id is not null group by d.id limit 1 ";
    echo "<br>" . $sql;
    $result = $db->mGetResult($sql);

    foreach ($result as $index => $aRow) {

        $system_id_id = $aRow['system_id'];

        $doc_id = $aRow['id'];
        $miejsce_kompletacji = $aRow['miejsce_kompletacji'];
        $miejsce_kompletacji = '48682';

        $sql = "SELECT zh.id from wmsgg.zadania_head zh
left join wmsgg.zadania_dane z  on  z.zadanie_head_id=zh.id where zh.baza_danych='$baza_danych' and system_id=$system_id_id and zh.typ='$typ' and doc_id=$doc_id and z.id is not null limit 1";
        echo "<br>" . $sql;
        $result24 = $db->mGetResult($sql);
        $ile = count($result24);
        echo "$ile";
        if ($ile == "0") {
            echo "bbbbbbb";


            //echo "asdasd" . $aRow2;
            $sql_ins_zad = "insert into wmsgg.zadania_head(system_id,baza_danych,typ,doc_id,doc_type,ts,zgodnosc_towaru,realizuj_cale_zadanie,status_dokumentu) values"
                    . " ('" . $system_id_id . "','$baza_danych','$typ','" . $id . "','$doc_type',NOW(),'4','1','1')";
            echo "<br>" . $sql_ins_zad;
            $zadanie_head_id = $db->mGetResult($sql_ins_zad);



            $query_zap1 = "UPDATE wmsgg.zadania_head z set zadania_head_rodzic_id=$zadanie_head_id where id='$zadanie_head_id' limit 1";
            echo "<br>" . $query_zap1;
            $result_zadanie2 = $db->mGetResult($query_zap1);


            /// dodawanie pozycji dl

            $sql4 = "SELECT e.system_id,e.id,e.paleta_id,e.kod_id,ifnull(e.lot,'') as lot,de.ilosc_zamawiana,e.miejscep, k.ilosc_szt_palecie,e.ilosc,k.kod,
if(de.ilosc_zamawiana=ilosc_szt_palecie and de.ilosc_zamawiana=e.ilosc,0,1) as kompletacja,
m.zbiorka
     FROM $baza_danych.delivery d
left join $baza_danych.delivery_et de on de.delivery_id=d.id
left join $baza_danych.etykiety e on de.etykieta_id=e.id
left join $baza_danych.kody k on e.kod_id=k.id
left join $baza_danych.miejsca m on e.miejscep=m.id
 where d.id=$id and e.id is not null
order by kompletacja desc";
            if ($system_id_id == "31") {
                $sql4 .= " ,k.kod asc,m.hala asc,m.regal asc,m.miejsce asc,m.poziom asc";
            } else {
                //$sql4.=" , m.hala asc,m.regal asc,m.miejsce asc,m.poziom asc";
                $sql4 .= " ,floor(m.regal/2),if(((floor(m.regal/2)%2)='1'),(300-m.miejsce),m.miejsce),m.poziom";
            }

            echo "<br>" . $sql;
            $result4 = $db->mGetResult($sql4); //mysql_query($sql4, $conn);
            $czy_dokument_zawiera_kompletacje = "NIE";




            foreach ($result4 as $index => $aRow4) {
                $status = 0;

                $wysokie = 0;
                if ($aRow4['zbiorka'] != "1") {
                    $wysokie = 1;
                }
                if ($aRow4['kompletacja'] == "1") {
                    $czy_dokument_zawiera_kompletacje = "TAK";
                }


                $sql_ins_zad = "insert into wmsgg.zadania_dane(zadanie_head_id,status, stare_m, nowe_m, paleta_id, etykieta_id, kod_id, lot, ilosc,kompletacja,wysokie,stanowisko_id) values "
                        . "('" . $zadanie_head_id . "','" . $status . "','" . $aRow4['miejscep'] . "','" . $miejsce_kompletacji . "','" . $aRow4['paleta_id'] . "','" . $aRow4['id'] . "','" . $aRow4['kod_id'] . "','" . $aRow4['lot'] . "','" . $aRow4['ilosc_zamawiana'] . "','" . $aRow4['kompletacja'] . "','" . $wysokie . "','3')";
                echo "<br>" . $sql_ins_zad;
                $zadania_dane_insert_id = $db->mGetResult($sql_ins_zad);
                $sql = "update zadania_dane z set z.zadanie_dane_rodzic_id=$zadania_dane_insert_id WHERE id=" . $zadania_dane_insert_id . " limit 1";
                //echo "<br>" . $sql_ins_zad;
                $result_zadanie4 = $db->mGetResult($sql);

                if ( $aRow4['zbiorka'] != "1" ) { // && $czy_dokument_zawiera_kompletacje == "TAK" $aRow4['kompletacja'] == 1 && 
                    //$status = 5; // status = Zdejmowanie pod kompletację
                    $czy_jest_zadanie_palety = sprawdz_czy_juz_jest_takie_polecenie_zdjecia_palety($aRow4['paleta_id'], 5, $db);
                    if (empty($czy_jest_zadanie_palety)) {
                        $sql_ins_zad = "insert into wmsgg.zadania_dane(zadanie_head_id,status, stare_m, nowe_m, paleta_id, etykieta_id, kod_id, lot, ilosc,kompletacja,wysokie,zadanie_dane_rodzic_id,stanowisko_id) values "
                                . "('" . $zadanie_head_id . "','5','" . $aRow4['miejscep'] . "','" . sugerowane_miejsce($aRow4['kod'], $aRow4['system_id'], $db) . "','" . $aRow4['paleta_id'] . "','" . $aRow4['id'] . "','" . $aRow4['kod_id'] . "','" . $aRow4['lot'] . "','" . $aRow4['ilosc_zamawiana'] . "','0','1','" . $zadania_dane_insert_id . "','3')";
                        echo "<br>" . $sql_ins_zad;
                        $result_zadanie4 = $db->mGetResult($sql_ins_zad);
                        
//                        $sql_ins_zad = "update zadania_head z set priorytet=5 WHERE z.priorytet=0 AND z.id=" . $zadanie_head_id;
//                        //echo "<br>" . $sql_ins_zad;
//                        $result_zadanie7 = $db->mGetResult($sql_ins_zad);
                                            
                    }
                }
            }


            $sprawdz_czy_wszystkie_kompletowane_sa_dostepne_na_dole = sprawdz_czy_wszystkie_kompletowane_sa_dostepne_na_dole($zadanie_head_id, $db);
            if ($sprawdz_czy_wszystkie_kompletowane_sa_dostepne_na_dole == "TAK") {
                mozna_rozpoczac_kompletacje($zadanie_head_id, $db);
            }
            if ($czy_dokument_zawiera_kompletacje == "TAK") {
                oczekuje_na_zdjecie($zadanie_head_id, $db);
                oczekuje_na_cala_kompletacje($zadanie_head_id, $db);
                rozpocznij_pelne_po_kompletacji($zadanie_head_id, $db);
            }
            status_aktywne_dla_pozostalych($zadanie_head_id, $db);
        }
    }
}

function sugerowane_miejsce($kod, $system_id, $db) {
    $sugerowane_miejsce_id = 0;

    return $sugerowane_miejsce_id;
}

function status_aktywne_dla_pozostalych($zadanie_head_id, $db) {
    $sql_ins_zad = "update zadania_dane z set z.status=1 WHERE status=0 and zadanie_head_id=" . $zadanie_head_id;
    echo "<br>" . $sql_ins_zad;
    $result_zadanie4 = $db->mGetResult($sql_ins_zad);
}

function rozpocznij_pelne_po_kompletacji($zadanie_head_id, $db) {
    $sql_ins_zad = "update zadania_dane z set z.status=6 WHERE kompletacja=0 and status=0 and zadanie_head_id=" . $zadanie_head_id;
    echo "<br>" . $sql_ins_zad;
    $result_zadanie4 = $db->mGetResult($sql_ins_zad);
}

function oczekuje_na_cala_kompletacje($zadanie_head_id, $db) {
    $sql_ins_zad = "update zadania_dane z set z.status=17 WHERE kompletacja=1 and status=0 and wysokie=0 and zadanie_head_id=" . $zadanie_head_id;
    echo "<br>" . $sql_ins_zad;
    $result_zadanie4 = $db->mGetResult($sql_ins_zad);
}

function oczekuje_na_zdjecie($zadanie_head_id, $db) {
    $sql_ins_zad = "update zadania_dane z set z.status=10,wysokie=0 WHERE kompletacja=1 AND status=0 AND wysokie=1 and zadanie_head_id=" . $zadanie_head_id;
    echo "<br>" . $sql_ins_zad;
    $result_zadanie4 = $db->mGetResult($sql_ins_zad);
}

function sprawdz_czy_dokument_zawiera_kompletacje($zadanie_head_id, $db) {
    $wynik = "NIE";
    $sql_ins_zad = "SELECT sum(if(kompletacja=1,1,0)) as kompletacja FROM zadania_dane z WHERE zadanie_head_id=" . $zadanie_head_id;
    echo "<br>" . $sql_ins_zad;
    $result_zadanie4 = $db->mGetResult($sql_ins_zad);
    print_r($result_zadanie4);
    if (!empty($result_zadanie4)) {
        if (!empty($result_zadanie4[0]['kompletacja'])) {
            $wynik = "TAK";
        }
    }
    //print_r($result_zadanie4);
//    $wynik = empty($result_zadanie4) ? 0 : count($result_zadanie4[0]);
    return $wynik;
}

function mozna_rozpoczac_kompletacje($zadanie_head_id, $db) {
    $sql_ins_zad = "update zadania_dane z set z.status=1 WHERE kompletacja=1 and zadanie_head_id=" . $zadanie_head_id;
    echo "<br>" . $sql_ins_zad;
    $result_zadanie4 = $db->mGetResult($sql_ins_zad);
}

function sprawdz_czy_wszystkie_kompletowane_sa_dostepne_na_dole($zadanie_head_id, $db) {
    $wynik = "NIE";
    $sql_ins_zad = "SELECT sum(if(z.wysokie=0,1,0)) as kompletacja, sum(1) as wszystkie FROM zadania_dane z WHERE kompletacja=1 and zadanie_head_id=" . $zadanie_head_id;
    echo "<br>" . $sql_ins_zad;
    $result_zadanie4 = $db->mGetResult($sql_ins_zad);
    if (!empty($result_zadanie4)) {
        if ($result_zadanie4[0]['kompletacja'] == $result_zadanie4[0]['wszystkie']) {
            $wynik = "TAK";
        }
    }
    //print_r($result_zadanie4);
//    $wynik = empty($result_zadanie4) ? 0 : count($result_zadanie4[0]);
    return $wynik;
}

function sprawdz_czy_juz_jest_takie_polecenie_zdjecia_palety($paleta_id, $status, $db) {
    $sql_ins_zad = "SELECT 1 as ile FROM zadania_dane z left join zadania_head zh on z.zadanie_head_id=zh.id
WHERE z.paleta_id=" . $paleta_id . " and z.status=$status and zh.id is not null limit 1;";
    echo "<br>" . $sql_ins_zad;
    $result_zadanie4 = $db->mGetResult($sql_ins_zad);
    $wynik = empty($result_zadanie4) ? 0 : count($result_zadanie4[0]);
    return $wynik;
}

//
//$komunikat.=//mysql_error();
////echo $komunikat;
//header('Content-type: text/xml');
//echo '<dane>';
//echo '<komunikat>', htmlentities($komunikat), '</komunikat>';
////echo '<ilosc_pozostala>', htmlentities($ilosc_pozostala), '</ilosc_pozostala>';
////echo '<nowa_etykieta>', htmlentities($nowa_etykieta), '</nowa_etykieta>';
////echo '<kod>', htmlentities($kod), '</kod>';
//echo '</dane>';
?>