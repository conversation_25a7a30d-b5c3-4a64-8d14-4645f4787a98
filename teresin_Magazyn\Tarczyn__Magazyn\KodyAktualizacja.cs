﻿using System;
using System.Linq;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using MySql.Data.MySqlClient;
using System.Text.RegularExpressions;
using System.Threading;

namespace Tarczyn__Magazyn
{
    public partial class KodyAktualizacja : Form
    {
        UpdateEtykietaMenu myParent = null;
        GS1_Test myParent2 = null;



        List<string> _kod_wybor = new List<string>();
        int[] _kod = new int[100];


        int nr_dl = 0;
        private Thread Skanowanie;

        //string skaner = "";
        string nr_etykiety = "";
        string nrsap = "";
        //string sap_gora = "";
        //string sap_gora_czesc2 = "";
        //string kod = "";
        string podkod = "";
        string Kontrolka = "";
        //string nrsap_baza = "";
        TextBox AktualnyTextBox = null;

        int poczatek = 0;
        int ilosc_kodow = 0;

        public string wybrany_kod = "";







        public MySqlConnection ok = null;

        public KodyAktualizacja(GS1_Test MyParent)
        {

            InitializeComponent();
            this.myParent2 = MyParent;
            Etykieta.Inicjalizacja();
            wms_wybrany.Text = Wlasciwosci.system_id;
            imie_nazwisk_label.Text = Wlasciwosci.wozek + ":" + Wlasciwosci.imie_nazwisko;
            Wlasciwosci.CurrentOperacja = "6";
            //ok = myParent.conn;
            wyszukaj_kod();

            button2.Visible = false;
            //Etykieta.Inicjalizacja();

            //this.ZacznijSkanowanie();

            //skanuj();
        }

        public KodyAktualizacja(UpdateEtykietaMenu MyParent)
        {
            
            InitializeComponent();
            this.myParent = MyParent;
            Etykieta.Inicjalizacja();
            wms_wybrany.Text = Wlasciwosci.system_id;
            imie_nazwisk_label.Text = Wlasciwosci.wozek + ":" + Wlasciwosci.imie_nazwisko;
            Wlasciwosci.CurrentOperacja = "6";
            //ok = myParent.conn;
            wyszukaj_kod();

            button2.Visible = false;
            //Etykieta.Inicjalizacja();

            //this.ZacznijSkanowanie();

            //skanuj();
        }

        private void wyszukaj_kod()
        {
            edytuj_kod_checkbox.Checked = false;
            //this.DialogResult = DialogResult.None;
            try
            {
                ilosc_kodow = Convert.ToInt32((string)BazaDanychExternal.Wyczytaj_Jedna_Wartosc("SELECT IFNULL(count(1),0) as ile  FROM kody k WHERE k.system_id='" + Wlasciwosci.system_id_id + "'  and k.active=1"));
                


                string zapytanie = "";

                if (poczatek == 0)
                { //20 ostatatnio dodanych kodów
                    //if (myParent.kod.Text != "")
                    //{

                    //}
                    //zapytanie = "SELECT k.id,k.kod  FROM kody k WHERE k.system_id='" + Wlasciwosci.system_id_id + "'  and k.active=1 ORDER BY k.id DESC limit 20";
                    zapytanie = "select bb.id,bb.kod from  (SELECT k.id,k.kod  FROM kody k WHERE k.system_id='" + Wlasciwosci.system_id_id + "'  and k.kod='" + myParent.kod.Text + "' limit 1) as bb  union  select aa.id,aa.kod from  (SELECT k.id,k.kod  FROM kody k WHERE k.system_id='" + Wlasciwosci.system_id_id + "'  and k.active=1 ORDER BY k.id DESC limit 20) as aa ";                    //and (ean='' or ean is null or ean_jednostki='' or ean_jednostki is null)
                }
                else
                {
                    zapytanie = "SELECT k.id,k.kod  FROM kody k WHERE k.system_id='" + Wlasciwosci.system_id_id + "'  and k.active=1 ORDER BY k.kod asc limit " + (poczatek - 50) + ",50;";  //and (ean='' or ean is null or ean_jednostki='' or ean_jednostki is null)
                }

                //MessageBox.Show("" + (poczatek) + " ");

                object obj2 = BazaDanychExternal.Wyczytaj_Tabele(zapytanie);
                DataTable tabela = (DataTable)obj2;
                _kod_wybor.Clear();

                _kod[0] = 0;
                _kod_wybor.Add("");
                for (byte k = 0; k < tabela.Rows.Count; k++)
                {

                    _kod_wybor.Add(tabela.Rows[k]["kod"].ToString());
                    _kod[k + 1] = Convert.ToInt32(tabela.Rows[k]["id"]);


                }
                if (_kod_wybor.Count == 1 || _kod_wybor.Count == 0)
                {
                    MessageBox.Show("Brak kodów w kartotece ");
                }
                else
                {

                    BindingSource bs = new BindingSource();
                    bs.DataSource = _kod_wybor;
                    comboBox1_kody.DataSource = bs;
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show(ex.Message);
            }
        }





        private void button1_Click(object sender, EventArgs e)
        {
            

            if (edytuj_kod_checkbox.Checked == true)
            {
                

                nr_dl = _kod[comboBox1_kody.SelectedIndex];

                
                if (nr_dl == 0 || nr_dl.ToString() == "")
                {

                    MessageBox.Show("Wybierz kod");
                    return;
                }

                if (ean_jednostki.Text == "" && ean.Text == "")
                {
                    MessageBox.Show("Podaj EAN jednostki lub opakowania");
                    return;
                }

                
                if (ilosc_w_opakowaniu.Text == "" || ilosc_w_opakowaniu.Text == "0")
                {
                    MessageBox.Show("Ilość sztuk w opakowaniu jest wymagane");
                    return;
                }
                if (ilosc_w_palecie.Text == "" || ilosc_w_palecie.Text == "0")
                {
                    MessageBox.Show("Ilość sztuk w palecie jest wymagana");
                    return;
                }
                if (ean_jednostki.Text == ean.Text)
                {
                    MessageBox.Show("EAN jednostki i opakowania nie mogą być takie same");
                    return;
                }

                if (waga_szt_kg.Text == "" || waga_szt_kg.Text == "0")
                {
                    MessageBox.Show("Waga jednej sztuki jest wymagana");
                    return;
                }
                waga_szt_kg.Text = waga_szt_kg.Text.Replace(",", ".");


                //MessageBox.Show("Kod id:" + nr_dl + "ean jedn:" + textBox1.Text+" ean opak:"+textBox2.Text);



                // sprawdza czy już był dodawany ten EAN jednostki
                string zapytanie = "";

                

                if (ean_opakowanie_zbiorcze.Text != "")
                {
                    zapytanie = "SELECT k.id,k.kod FROM kody k WHERE k.system_id='" + Wlasciwosci.system_id_id + "' and (ean_opakowanie_zbiorcze='" + ean_jednostki.Text + "' ) and k.active=1 and k.id!='" + _kod[comboBox1_kody.SelectedIndex].ToString() + "' limit 1";
                    object obj2 = BazaDanychExternal.Wyczytaj_Tabele(zapytanie);
                    DataTable tabela = (DataTable)obj2;

                    //MessageBox.Show(zapytanie);
                    //MessageBox.Show(tabela.Rows.Count.ToString());
                    if (tabela.Rows.Count != 0)
                    {
                        MessageBox.Show("EAN opakowanie zbiorcze " + ean_opakowanie_zbiorcze.Text + " jest już przypisany do kodu:" + tabela.Rows[0]["kod"].ToString());
                        //return;
                    }

                    BazaDanychExternal.DokonajUpdate("update kody k set k.ean_opakowanie_zbiorcze='" + ean_opakowanie_zbiorcze.Text + "' where k.id='" + _kod[comboBox1_kody.SelectedIndex].ToString() + "' limit 1");
                    
                    if (ilosc_szt_w_zbiorczym.Text != "")
                    {
                        BazaDanychExternal.DokonajUpdate("update kody k set k.ilosc_szt_w_zbiorczym='" + ilosc_szt_w_zbiorczym.Text + "' where k.id='" + _kod[comboBox1_kody.SelectedIndex].ToString() + "' limit 1");
                    }

                }

                if (ean_jednostki.Text != "")
                {
                    zapytanie = "SELECT k.id,k.kod FROM kody k WHERE k.system_id='" + Wlasciwosci.system_id_id + "' and (ean_jednostki='" + ean_jednostki.Text + "') and k.active=1 and k.id!='" + _kod[comboBox1_kody.SelectedIndex].ToString() + "' limit 1";
                    object obj2 = BazaDanychExternal.Wyczytaj_Tabele(zapytanie);
                    DataTable tabela = (DataTable)obj2;

                    //MessageBox.Show(zapytanie);
                    //MessageBox.Show(tabela.Rows.Count.ToString());
                    if (tabela.Rows.Count != 0)
                    {
                        MessageBox.Show("EAN jednostki " + ean_jednostki.Text + " jest już przypisany do kodu:" + tabela.Rows[0]["kod"].ToString());
                        //return;
                    }
                    BazaDanychExternal.DokonajUpdate("update kody k set k.ean_jednostki='" + ean_jednostki.Text + "' where k.id='" + _kod[comboBox1_kody.SelectedIndex].ToString() + "' ");
                }

                if (ean.Text != "")
                {
                    zapytanie = "SELECT k.id,k.kod FROM kody k WHERE k.system_id='" + Wlasciwosci.system_id_id + "' and (ean='" + ean.Text + "') and k.active=1 and k.id!='" + _kod[comboBox1_kody.SelectedIndex].ToString() + "' limit 1";
                    object obj2 = BazaDanychExternal.Wyczytaj_Tabele(zapytanie);
                    DataTable tabela = (DataTable)obj2;

                    if (tabela.Rows.Count != 0)
                    {
                        MessageBox.Show("EAN opakowania " + ean.Text + " jest już przypisany do kodu:" + tabela.Rows[0]["kod"].ToString());
                        //return;
                    }
                    BazaDanychExternal.DokonajUpdate("update kody k set k.ean='" + ean.Text + "'  where k.id='" + _kod[comboBox1_kody.SelectedIndex].ToString() + "' ");

                    

                }
                BazaDanychExternal.DokonajUpdate("update kody k set k.ilosc_w_opakowaniu='" + ilosc_w_opakowaniu.Text + "' where k.id='" + _kod[comboBox1_kody.SelectedIndex].ToString() + "' ");
                BazaDanychExternal.DokonajUpdate("update kody k set k.ilosc_szt_palecie='" + ilosc_w_palecie.Text + "' where k.id='" + _kod[comboBox1_kody.SelectedIndex].ToString() + "' ");

                
                //wyszukaj_kod();

            }


            nr_dl = _kod[comboBox1_kody.SelectedIndex];


            if (nr_dl == 0 || nr_dl.ToString() == "")
            {

                MessageBox.Show("Wybierz kod");
                return;
            }

            
            wybrany_kod = _kod_wybor[comboBox1_kody.SelectedIndex];

            //wybrany_kod = comboBox1_kody.SelectedItem.ToString();

            //MessageBox.Show(wybrany_kod);

            //wybrany_kod = comboBox1_kody.SelectedItem.ToString();
            //MessageBox.Show(wybrany_kod);

            if (wybrany_kod == "")
            {

                MessageBox.Show("Wybierz kod");
                return;
            }
            this.DialogResult = DialogResult.OK;
            button1.DialogResult = DialogResult.OK;
            ean_jednostki.Text = "";
            ean.Text = "";
            ilosc_w_opakowaniu.Text = "";
            ilosc_szt_w_zbiorczym.Text = "";
            ilosc_w_palecie.Text = "";
            ean_opakowanie_zbiorcze.Text = "";
            //wybrany_kod = "";
            kod_nazwa_label.Text = "";
            

        }


        private void Zakoncz_Skanowanie(object sender, EventArgs e)
        {
            comboBox1_kody.Enabled = true;
            powrot.Visible = true;
            ean_jednostki.Visible = false;
            button2.Visible = false;
            this.Skanowanie.Abort();
            Skaner.Przewij_Skanowanie();
            this.button1.Click -= new EventHandler(this.Zakoncz_Skanowanie);
            this.button1.Click += new EventHandler(this.button1_Click);

            //MessageBox.Show("Zakończyłem tworzenie dokumentu DL:"+nr_dl);
            //this.myParent.Show();
            //base.Close();
        }

        private void powrot_Click(object sender, EventArgs e)
        {
            this.myParent.Show();
            base.Close();
        }



        private void button1_Anuluj(object sender, EventArgs e)
        {

        }
        private void ZacznijSkanowanie()
        {
            StringBuilder login = new StringBuilder();
            Skaner.UstawTryb_String(login);
            Skaner.Zacznij_Skanowanie();
            this.Skanowanie = new Thread(() => this.CheckString(login));
            this.Skanowanie.IsBackground = true;
            this.Skanowanie.Start();
        }

        private void CheckString(StringBuilder cc)
        {
            EventHandler method = null;
            while (true)
            {
                if (cc.ToString() != "")
                {
                    if (method == null)
                    {
                        method = delegate
                        {
                            //this.vvv();
                            this.dodawanie(cc.ToString());
                        };
                        //method = () => this.dodawanie(cc.ToString());
                    }
                    this.Invoke(method);
                    return;
                }
                Thread.Sleep(200);
            }
        }


        private void dodawanie(string ops)
        {

            Skaner.Przewij_Skanowanie();
            //MessageBox.Show("1");



            if (AktualnyTextBox == null)
            {
                MessageBox.Show("Wybierz wczytanie");
                return;
            }
            //MessageBox.Show("2");
            AktualnyTextBox.Text = ops;

            if (ops.Length > 12)
            {
                //string cleanString = Regex.Replace(ops, @"[^a-zA-Z0-9\-\.\,]", "");
                //ops = ops.Replace("\r\n", string.Empty);
                //MessageBox.Show("2");
                Dictionary<Etykieta.AII, string> ff = Etykieta.Parse(ops, false);
                //MessageBox.Show("3");
                foreach (Etykieta.AII tt in ff.Keys)
                {
                    //MessageBox.Show("Dla kodu:" + tt.AI + "   ,   Otrzymujemy wartość:" + ff[tt].ToString());
                    //Console.WriteLine("Dla kodu:" + tt.AI + "   ,   Otrzymujemy wartość:" + ff[tt].ToString());
                    if (tt.AI == "91") { AktualnyTextBox.Text = Regex.Replace(ff[tt].ToString(), @"[^a-zA-Z0-9\-\.\,\\\/]", "");  }
                    if (tt.AI == "02") { AktualnyTextBox.Text = Regex.Replace(ff[tt].ToString(), @"[^a-zA-Z0-9\-\.\,\\\/]", "");  }
                    //MessageBox.Show("4");



                }
            }

            //MessageBox.Show("3");

            //MessageBox.Show("10");


            //ZacznijSkanowanie();
        }


        public void uaktualnij_ean_kodu()
        {



        }


        private string wczytane_licznik(string nr_dl)
        {
            return (string)BazaDanychExternal.Wyczytaj_Jedna_Wartosc("select count(1) from sap_kolektor s where nr='" + nr_dl + "';");
        }

        private void kasuj_ostatnio_wczytane(string nr_dl)
        {
            string aa = (string)BazaDanychExternal.Wyczytaj_Jedna_Wartosc("select id from sap_kolektor d where nr='" + nr_dl + "' order by id desc limit 1;");
            BazaDanychExternal.DokonajUpdate("delete from sap_kolektor where id='" + aa + "' limit 1;");
        }




































        private void aktualizuj_etykiete(string etykieta, string kod, string ilosc)
        {
            //if(nr_etykiety)
            try
            {
                //ok = myParent.conn;
                //MySqlCommand cmdSel = new MySqlCommand("update etykiety set nrsap='" + Convert.ToInt64(nrsap) + "',podkod='" + podkod + "' where id=" + nr_etykiety + " limit 1;", ok);
                //cmdSel.CommandText = "update etykiety set nrsap='" + Convert.ToInt64(nrsap) + "',podkod='" + podkod + "' where id=" + nr_etykiety + " limit 1;";
                //cmdSel.ExecuteNonQuery();
                
                //etykieta_text.Text = "";

            }
            catch (MySqlException ex)
            {
                MessageBox.Show(ex.ToString());
            }

            /*
             *            DialogResult result3 = MessageBox.Show("Napewno chcesz zakończyć pracę na deklaracji?",
                "Eksport danych",
                MessageBoxButtons.YesNo,
                MessageBoxIcon.Question,
                MessageBoxDefaultButton.Button2);
            if (result3 == DialogResult.Yes)
            {}
             */

        }
        private void czysc()
        {



        }



        private void timer1_Tick(object sender, EventArgs e)
        {
            this.Close();
            myParent.Show();
        }




        private void label2_ParentChanged(object sender, EventArgs e)
        {

        }

        /*
        private void FERRERO_TworzenieDL_Load(object sender, EventArgs e)
        {
             try
            {
            //fullscreenmode();
            EventHandler MyReadNotifyHandler = new EventHandler(MyReader_ReadNotify);
            EventHandler MyStatusNotifyHandler = new EventHandler(MyReader_StatusNotify);
            this.MyReader.StatusNotify += MyStatusNotifyHandler;
            this.MyReader.ReadNotify += MyReadNotifyHandler;
            }
             catch (Exception ex)
             {
                 MessageBox.Show(ex.Message.ToString());
             }
        }*/

        #region fullscreen
        private void fullscreenmode()
        {
            this.Width = Screen.PrimaryScreen.Bounds.Width;
            this.Height = Screen.PrimaryScreen.Bounds.Height;
            this.Left = 0;
            this.Top = 0;
        }
        #endregion

        private void button3_Click(object sender, EventArgs e)
        {

        }

        private void button2_Click(object sender, EventArgs e)
        {
            dodawanie(ean_jednostki.Text);
            ean_jednostki.Text = "";
        }

        private void GotFocus_TextBox(object sender, EventArgs e)
        {
            TextBox Pole_Tekstowe = (TextBox)sender;

            AktualnyTextBox = Pole_Tekstowe;

            ZacznijSkanowanie();
        }

        private void LostFocus_TextBox(object sender, EventArgs e)
        {
            AktualnyTextBox = null;
            Skaner.Przewij_Skanowanie();
        }



        private void comboBox1_kody_SelectedIndexChanged(object sender, EventArgs e)
        {
            if (_kod[comboBox1_kody.SelectedIndex] != 0)
            {
                string zapytanie = "SELECT k.id,k.kod, k.ean, k.ilosc_w_opakowaniu,k.ilosc_szt_palecie, k.gln, k.ean_jednostki,k.kod_nazwa,k.ean_opakowanie_zbiorcze,k.ilosc_szt_w_zbiorczym FROM kody k WHERE k.system_id='" + Wlasciwosci.system_id_id + "' and k.id='" + _kod[comboBox1_kody.SelectedIndex] + "' limit 1;";
                object obj2 = BazaDanychExternal.Wyczytaj_Tabele(zapytanie);
                DataTable tabela = (DataTable)obj2;

                ilosc_w_opakowaniu.Text = tabela.Rows[0]["ilosc_w_opakowaniu"].ToString();
                ean.Text = tabela.Rows[0]["ean"].ToString();
                ean_jednostki.Text = tabela.Rows[0]["ean_jednostki"].ToString();
                ean_opakowanie_zbiorcze.Text = tabela.Rows[0]["ean_opakowanie_zbiorcze"].ToString();
                kod_nazwa_label.Text = tabela.Rows[0]["kod_nazwa"].ToString();
                ilosc_szt_w_zbiorczym.Text = tabela.Rows[0]["ilosc_szt_w_zbiorczym"].ToString();
                ilosc_w_palecie.Text = tabela.Rows[0]["ilosc_szt_palecie"].ToString();
            }
        }

        private void button3_Click_1(object sender, EventArgs e)
        {
            if (poczatek - 50 >= 0)
            {
                poczatek -= 50;
                wyszukaj_kod();
            }

        }

        private void button4_Click(object sender, EventArgs e)
        {
            if (poczatek + 50 <= (ilosc_kodow+50))
            {
                poczatek += 50;
                wyszukaj_kod();
            }

        }

        private void edytuj_kod_check(object sender, EventArgs e)
        {
            edytuj_kod_zmien();
        }

        void edytuj_kod_zmien()
        {
            if (edytuj_kod_checkbox.Checked == false)
            {
                ean.Enabled = false;
                ean_jednostki.Enabled = false;
                ean_opakowanie_zbiorcze.Enabled = false;
                ilosc_w_opakowaniu.Enabled = false;
                ilosc_w_palecie.Enabled = false;
                ilosc_szt_w_zbiorczym.Enabled = false;
            }
            if (edytuj_kod_checkbox.Checked == true)
            {
                ean.Enabled = true;
                ean_jednostki.Enabled = true;
                ean_opakowanie_zbiorcze.Enabled = true;
                ilosc_w_opakowaniu.Enabled = true;
                ilosc_w_palecie.Enabled = true;
                ilosc_szt_w_zbiorczym.Enabled = true;
            }

        }






    }
}