﻿using System;
using System.Linq;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;

namespace Tarczyn__Magazyn
{
    public partial class WyborBazy : Form
    {

        List<string> systemy = new List<string>();
        int[] systemy_id = new int[100];
        string[] baza_danych = new string[100];


        public WyborBazy()
        {
            InitializeComponent();
            //Dalej.Focus();
        }

        private void button1_Click(object sender, EventArgs e)
        {
            //this.DialogResult = DialogResult.Cancel;
        }

        private void Dalej_Click(object sender, EventArgs e)
        {
            switch (comboBox1.Text)
            {
                case "wmsgg":
                    Wlasciwosci.TrybActionMenu = 1;
                    Wlasciwosci.bazaDanych = 1;
                    break;
               
                        
            }
            Wlasciwosci.system_id_id = systemy_id[comboBox1.SelectedIndex].ToString();
            Wlasciwosci.system_id = comboBox1.SelectedValue.ToString();

            Wlasciwosci.LastChoose = comboBox1.Text;
            Wlasciwosci.ZapisLast("wmsgg"); //comboBox1.Text
        }

        private void WyborBazy_Load(object sender, EventArgs e)
        {
            systemy.Clear();

            string zapytanie = "";
            zapytanie = "SELECT 'wmsgg' as baza,nazwa,wartosc,s.kolejnosc FROM wmsgg.systemy s  WHERE s.aktywny=1 and  s.nazwa!='prace_wew' group by s.wartosc order by kolejnosc desc,nazwa asc"; //and LENGTH(regal)<=2
            object obj2 = BazaDanychExternal.Wyczytaj_Tabele(zapytanie);
            DataTable tabela = (DataTable)obj2;

            for (byte k = 0; k < tabela.Rows.Count; k++)
            {
                systemy.Add(tabela.Rows[k]["nazwa"].ToString());
                systemy_id[k] = Convert.ToInt32(tabela.Rows[k]["wartosc"]);
                baza_danych[k] = tabela.Rows[k]["baza"].ToString();
            }

            BindingSource bs = new BindingSource();
            bs.DataSource = systemy;
            comboBox1.DataSource = bs;
            comboBox1.Focus();
        }
    }
}