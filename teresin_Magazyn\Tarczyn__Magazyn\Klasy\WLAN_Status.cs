﻿using System;
using System.Linq;
using System.Collections.Generic;
using System.Text;
using Symbol;
using Symbol.Fusion;
using Symbol.Fusion.WLAN;
using System.Windows.Forms;
using Symbol.Exceptions;

namespace Tarczyn__Magazyn
{
    public class WLAN_Status
    {
        //string Status = "";
        private  MainMenu myParent = null;
        private  WLAN myWlan = null;
        private  Adapter.SignalQualityHandler mySignalQualityHandler = null;
        private  int Wifi_Sygnal = 0;
        private  bool polaczPonownie = false;



        public WLAN_Status(MainMenu parent)
        {
            myParent = parent;
            WLAN.Monitor.AdapterPower = true;
            WLAN.Monitor.AccessPoint = true;
            mySignalQualityHandler = new Adapter.SignalQualityHandler(myAdapter_SignalQualityChanged);

            try
            {
                myWlan = new WLAN(FusionAccessType.STATISTICS_MODE);
            }
            catch (OperationFailureException ex)
            {

                System.Windows.Forms.MessageBox.Show("Komunikat. Uruchom skaner ponownie.", "Komunikat");
                return;
            }
             
            Wifi_Sygnal = get_Signal_int();
            myWlan.Adapters[0].SignalQualityChanged += mySignalQualityHandler;
        }
        ~WLAN_Status()
        {
            myWlan.Dispose();
            myWlan = null;
        }



        public  string get_MacAddress()
        {
            return myWlan.Adapters[0].MacAddress;
        }

        public  string get_Signal_String()
        {
            return myWlan.Adapters[0].SignalQuality.ToString();
        }

        public  int get_Signal_int()
        {

            try
            {
                switch (myWlan.Adapters[0].SignalQuality)
                {
                    case Symbol.Fusion.WLAN.Adapter.SignalQualityRange.EXCELLENT:
                        Wifi_Sygnal = 5;
                        break;
                    case Symbol.Fusion.WLAN.Adapter.SignalQualityRange.VERYGOOD:
                        Wifi_Sygnal = 4;

                        break;
                    case Symbol.Fusion.WLAN.Adapter.SignalQualityRange.GOOD:
                        Wifi_Sygnal = 3;

                        break;
                    case Symbol.Fusion.WLAN.Adapter.SignalQualityRange.FAIR:
                        Wifi_Sygnal = 2;
                        break;
                    case Symbol.Fusion.WLAN.Adapter.SignalQualityRange.POOR:
                        Wifi_Sygnal = 1;
                        break;
                    case Symbol.Fusion.WLAN.Adapter.SignalQualityRange.NONE:
                        Wifi_Sygnal = 0;
                        break;
                }
                return Wifi_Sygnal;
            }
            catch
            {
                return 0;
            }
        }


        private  void AkcjapolaczPonownie()
        {
            if (polaczPonownie == true)
            {
                polaczPonownie = false;
                if (Wlasciwosci.bazaDanych == 1)
                {
                    BazaDanychExternal.Inicjalizacja(Wlasciwosci.Varbaza, Wlasciwosci.Varhost, Wlasciwosci.VarLoginMYSQL, Wlasciwosci.VarHasloMYSQL, myParent);
                }
                if (Wlasciwosci.bazaDanych == 2)
                {
                    BazaDanychExternal.Inicjalizacja(Wlasciwosci.GNG, Wlasciwosci.Varhost, Wlasciwosci.VarLoginMYSQL, Wlasciwosci.VarHasloMYSQL, myParent);
                }
                MessageBox.Show("Połączyłem ponownie bazę danych.");
            }

        }



        public  int Wez_Sygnal()
        {
            return Wifi_Sygnal;
        }

        public  void Wyczysc()
        {
            //myWlan.Dispose();
        }

        private  void myAdapter_SignalQualityChanged(object sender, StatusChangeArgs e)
        {
            Wifi_Sygnal = get_Signal_int();
            myParent.WifiBar.Value = get_Signal_int();
            if (Wifi_Sygnal < 2)
            {
                polaczPonownie = true;
            }
            else
            {
                AkcjapolaczPonownie();
            }


            //BazaDanychExternal.SprawdzCzyIstniejePolaczenie();
        }

    }
}
