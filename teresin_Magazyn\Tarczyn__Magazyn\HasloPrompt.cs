﻿using System;
using System.Linq;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;

namespace Tarczyn__Magazyn
{
    public partial class HasloPrompt : Form
    {


        string h;
        int messageInt =0;
        public HasloPrompt(string z ,int message , int var)
        {

            h = z;
            messageInt = message;
            InitializeComponent();
            Variable.Text = Wlasciwosci.Message[var];    

        }

        private void button1_Click(object sender, EventArgs e)
        {

            //MessageBox.Show(h);
            if (h != textBox1.Text)
            {
                MessageBox.Show(Wlasciwosci.Message[messageInt]);
                this.DialogResult = DialogResult.None;
            }
        }
    }
}