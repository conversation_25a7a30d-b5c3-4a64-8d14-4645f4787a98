# PRD Frontend – <PERSON><PERSON><PERSON>wy” (MAUI)

## 1. Cel i zakres
Zaprojektowanie dwóch widoków w aplikacji MAUI dla rejestracji dostaw:
- Widok 1: „Dostawa wybór” – wprowadzenie/zeskanowanie LK, lista dostaw, generowanie DS (palet), przejście do rejestracji.
- Widok 2: „Dostawa rejestracja” – skanowanie GS1/DS, rejestracja pozycji na nośnik, podgląd, „Nośnik kompletny”, „Koniec”.

Komunikacja wyłącznie JSON z nowym API .NET.

## 2. Widok 1 – Dostawa wybór
Elementy:
- Pole tekstowe „LK” z walidacją: akceptuje „LK” + liczba (bez zer wiodących). Możliwość skanowania.
- Przycisk „Lista dostaw” – wyświetla listę z API (list_control z docin_id_man IS NULL; opcjonalnie odfiltrowane zajęte). Prezentacja: "numer_zamowienia | system_nazwa | miejsce_dostawy | data".
- Przycisk „Generuj DS” – otwiera modal/dodatkowy ekran:
  - Typ palety (lista z typypalet),
  - Ilość palet (1..100),
  - Skan drukarki (IP…) + checkbox „Czy drukować?”
  - Przycisk „Generuj DS” – wywołuje API generujące DS (i druk, jeśli zaznaczono).
- Po wyborze/dokonaniu wyboru dostawy: aplikacja wywołuje claim; w razie 409 informuje, że dostawa jest zajęta.
- Po udanym wyborze LK: przejście do Widoku 2.

UX/Focus:
- Po powrocie z listy „Wybierz” – wartość LK wstawia się do pola i jest gotowa do potwierdzenia.
- Walidacja: nie pozwól przejść dalej bez poprawnego LK/claim.

## 3. Widok 2 – Dostawa rejestracja
Elementy główne:
- Pole „Skanuj” (autofocus po każdym przetworzeniu): akceptuje GS1 i DS.
- Pole „Nr nośnika (DS/SSCC)”: pokazuje bieżący nośnik; domyślnie „(Auto)”.
- Sekcja „Towar”: pole Kod/Towar z przyciskiem „Szukaj”.
- Daty: Data przyjęcia (auto) i Data ważności (edytowalna, jeśli wymagana wg kodu/GS1).
- Partia (pole, jeśli wymagana/ze skanu AI 10).
- Certyfikat (pole opcjonalne, jeśli projekt tego wymaga – domyślnie ukryte).
- Stan jakości i Typ palety (typ palety edytowalny także tutaj).
- Ilość opakowań / Ilość sztuk (przeliczanie wg kody.ilosc_w_opakowaniu; możliwość korekty).
- Przyciski: „Koniec”, „Podgląd”, „Nośnik kompletny”.

Zachowanie:
- Po skanie GS1:
  - Parsujemy przez API: 00 (SSCC), 02 (lookup kody.ean/kody.id → kodId), 10 (lot), 17 (data_waznosci), 37 (ilość = ilosc_w_opakowaniu × AI37).
  - Jeśli 02 zwraca wiele kandydatów – pokaż selektor (id, kod) i umożliw wybór.
  - Jeśli znajdziemy SSCC w awizacji – prefiluj pola; jeśli nie – dopuszczamy przyjęcie bez awizacji.
- Rejestracja pozycji:
  - Gdy bieżący nośnik = „(Auto)” → aplikacja tworzy nowy DS przez endpoint „/nosniki” (typ palety z UI), następnie wykonuje insert pozycji.
  - Gdy bieżący nośnik wskazany (SSCC lub DS) → insert pozycji na ten nośnik.
  - Walidacje z API: brak wymaganej partii/daty → błąd; niezgodność ilości z awizacją → confirm (pokaż modal potwierdzenia).
- „Nośnik kompletny”: zamyka bieżący DS w kontekście sesji; kolejny insert w trybie „(Auto)” utworzy nowy DS.
- „Podgląd”: pokazuje pozycje dla bieżącego DS (GET /nosniki/{paletaId}/pozycje), sumy, ewentualne ostrzeżenia.
- „Koniec”: kończy sesję rejestracji (POST /koniec), zwalnia claim; jeśli spełnione warunki zamknięcia – pokaż informację.

UX/Focus:
- Po każdym skanie focus wraca do pola „Skanuj”.
- Komunikaty z API (success/error/confirm) w formie toast/modal; confirm z wyraźnymi przyciskami „Kontynuuj”/„Anuluj”.

## 4. Integracje i dane
- typypalet: źródło listy typów (SELECT id, opis…)
- kody: źródło wymagań i przeliczników (ilosc_w_opakowaniu, flags wymagane_partia/data/dataprod)
- list_control: źródło LK, miejsce_id (niezmienialne), system/magazyn kontekstowy
- etykiety: efekt końcowy insertów pozycji
- drukarki: lista IP do walidacji druku

## 5. Walidacje frontend
- Format LK – bez zer wiodących, prefiks „LK”.
- Ilość palet 1..100.
- IP drukarki – weryfikuj z listą pobraną z API.
- Blokada akcji, gdy claim odrzucony (zajęte przez innego pracownika).

## 6. Błędy i confirm
- error – blokuje; confirm – wymaga potwierdzenia użytkownika (np. niezgodność ilości vs awizacja).

## 7. Kryteria akceptacyjne (wybór)
- Z listy dostaw wybieram LK, claim przechodzi; przy próbie drugiego operatora – informacja „zajęte”.
- Generuję 5 DS – aplikacja pokazuje listę wygenerowanych numerów i (opcjonalnie) drukuje.
- Skan GS1 (00/02/10/17/37) wypełnia pola zgodnie z mapowaniem; przy wielu kodach – selektor „id, kod”.
- „Nośnik kompletny” powoduje, że kolejna pozycja z „(Auto)” utworzy nowy DS.
- „Koniec” zwalnia claim i zamyka sesję; jeśli awizacja kompletna – pojawia się informacja o gotowości zamknięcia dostawy.

