﻿using System;
using System.Linq;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using System.Text.RegularExpressions;
using System.Threading;

namespace Tarczyn__Magazyn
{
    public partial class PoleTekstowe : Form
    {
        double minimum = 0;

        private Thread Skanowanie;

        double maximum = 0;
        public string komunikat = "";
        public string wartosc_wpisana = "";


        public PoleTekstowe(string komunikat,string wartosc_domyslna)
        {
            InitializeComponent();
            //textBox1.Text = ilosc.ToString();
            textBox1.Focus();
            textBox1.Text = wartosc_domyslna;
            label1.Text = komunikat;
            this.ZacznijSkanowanie();
        }

        private void button1_Click(object sender, EventArgs e)
        {
            //this.DialogResult = DialogResult.Cancel;
            //return "przerwij";
            this.Skanowanie.Abort();
            Skaner.Przewij_Skanowanie();
        }

        private void Dalej_Click(object sender, EventArgs e)
        {
            this.wartosc_wpisana = textBox1.Text;            
            this.Skanowanie.Abort();
            Skaner.Przewij_Skanowanie();
            this.DialogResult = DialogResult.OK;
        }



        public void ZacznijSkanowanie()
        {
            StringBuilder login = new StringBuilder();
            Skaner.UstawTryb_String(login);
            Skaner.Zacznij_Skanowanie();
            this.Skanowanie = new Thread(() => this.CheckString(login));
            this.Skanowanie.IsBackground = true;
            this.Skanowanie.Start();
        }

        private void Zakoncz_Skanowanie(object sender, EventArgs e)
        {
            this.Skanowanie.Abort();
            Skaner.Przewij_Skanowanie();
        }

        private void CheckString(StringBuilder cc)
        {
            EventHandler method = null; //to jest delegat

            while (true)
            {
                if (cc.ToString() != "")
                {
                    if (method == null)
                    {
                        method = delegate
                        {
                            this.dodawanie(cc.ToString());
                        };
                    }
                    this.Invoke(method);
                    return;
                }
                Thread.Sleep(200);
            }
        }


        // 'Internal' - wyszukuje w bazie etykiete i wypełnia pola
        private void dodawanie(string ops)
        {

            textBox1.Text = ops;
            this.wartosc_wpisana = textBox1.Text;
            this.Skanowanie.Abort();
            Skaner.Przewij_Skanowanie();
            this.DialogResult = DialogResult.OK;
           
        }



    }
}