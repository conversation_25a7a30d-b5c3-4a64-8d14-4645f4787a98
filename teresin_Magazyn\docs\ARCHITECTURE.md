# Architektura Systemu WMS - Moduł Przyjęcia Towarów

## 1. Struktura Bazy Danych

### Tabele Główne

#### **etykiety**
Główna tabela przechowująca informacje o etykietach produktów.

| Pole | Typ | Opis |
|------|-----|------|
| id | INT | Klucz główny |
| system_id | INT | ID systemu/klienta |
| kod_id | INT | Klucz obcy do tabeli `kody` |
| paleta_id | INT | Klucz obcy do tabeli `palety` |
| dataprod | DATE | Data produkcji |
| data_waznosci | DATE | Data ważności produktu |
| ilosc | DECIMAL | Ilość sztuk |
| lot | VARCHAR | Numer partii/LOT |
| blloc | VARCHAR | Lokalizacja |
| nretykiety | VARCHAR | Numer etykiety |
| edycja_et | INT | Wersja edycji etykiety |
| etykieta_klient | VARCHAR | Numer etykiety klienta (SSC) |
| ts | DATETIME | Znacznik czasu |
| listcontrol_id | INT | Klucz obcy do `list_control` |
| active | INT | Czy etykieta jest aktywna (NULL = aktywna) |
| sscc | VARCHAR | Numer SSCC |

#### **kody**
Kartoteka produktów/kodów towarowych.

| Pole | Typ | Opis |
|------|-----|------|
| id | INT | Klucz główny |
| system_id | INT | ID systemu/klienta |
| kod | VARCHAR | Kod produktu |
| ean | VARCHAR | Kod EAN opakowania |
| ean_jednostki | VARCHAR | Kod EAN jednostki |
| ean_opakowanie_zbiorcze | VARCHAR | Kod EAN opakowania zbiorczego |
| ilosc_w_opakowaniu | DECIMAL | Ilość sztuk w opakowaniu |
| ilosc_szt_palecie | DECIMAL | Ilość sztuk na palecie |
| ilosc_szt_w_zbiorczym | DECIMAL | Ilość sztuk w opakowaniu zbiorczym |
| gln | VARCHAR | Numer GLN |
| ilosc_dni_przydatnosci | INT | Ilość dni przydatności |
| wymagana_partia | INT (0/1) | Czy wymagany jest LOT |
| wymagana_data_waznosci | INT (0/1) | Czy wymagana jest data ważności |
| wymagana_dataprod | INT (0/1) | Czy wymagana jest data produkcji |
| kod_nazwa | VARCHAR | Nazwa produktu |
| active | INT | Czy kod jest aktywny |
| status_jakosci_domyslny | INT | Domyślny status jakości |

#### **list_control**
Listy kontrolne przyjęć.

| Pole | Typ | Opis |
|------|-----|------|
| id | INT | Klucz główny |
| listcontrol_system_id | INT | ID systemu |
| awizacje_id | INT | Klucz obcy do `awizacje_dostaw_head` |

#### **listcontrol_palety**
Tabela łącząca listy kontrolne z paletami.

| Pole | Typ | Opis |
|------|-----|------|
| listcontrol_id | INT | Klucz obcy do `list_control` |
| paleta_id | INT | Klucz obcy do `palety` |

#### **palety**
Informacje o paletach.

| Pole | Typ | Opis |
|------|-----|------|
| id | INT | Klucz główny (numer palety DS) |
| typypalet_id | INT | Klucz obcy do `typypalet` |
| ilosc | DECIMAL | Ilość na palecie |
| j_skladowania_id | INT | Jednostka składowania |

#### **typypalet**
Słownik typów palet.

| Pole | Typ | Opis |
|------|-----|------|
| id | INT | Klucz główny |
| opis | VARCHAR | Opis typu palety |
| kolejnosc_pal | INT | Kolejność wyświetlania |

#### **awizacje_dostaw_head**
Nagłówki awizacji dostaw.

| Pole | Typ | Opis |
|------|-----|------|
| id | INT | Klucz główny |
| listcontrol_id | INT | Klucz obcy do `list_control` |
| system_id | INT | ID systemu |

#### **awizacje_dostaw_dane**
Pozycje awizacji dostaw.

| Pole | Typ | Opis |
|------|-----|------|
| id | INT | Klucz główny |
| awizacje_dostaw_id | INT | Klucz obcy do `awizacje_dostaw_head` |
| kod | VARCHAR | Kod produktu |
| ilosc | DECIMAL | Ilość awizowana |
| etykieta_klient | VARCHAR | Numer etykiety klienta |
| lot | VARCHAR | Numer partii |
| dataprod | DATE | Data produkcji |
| data_waznosci | DATE | Data ważności |
| blloc | VARCHAR | Lokalizacja |

#### **operacje**
Rejestr operacji wykonywanych w systemie.

| Pole | Typ | Opis |
|------|-----|------|
| etykieta_id | INT | ID etykiety |
| doc_type | VARCHAR | Typ dokumentu (np. 'AKT') |
| doc_nr | VARCHAR | Numer dokumentu |
| imie_nazwisko | VARCHAR | Imię i nazwisko operatora |
| typ_operacji | VARCHAR | Typ operacji |
| system_id | INT | ID systemu |
| wozek | VARCHAR | Numer wózka |
| operac_id | VARCHAR | ID operacji |
| ilosc | DECIMAL | Ilość |

#### **docnumber**
Sekwencje numeracji dokumentów.

| Pole | Typ | Opis |
|------|-----|------|
| name | VARCHAR | Nazwa sekwencji |
| last | INT | Ostatni użyty numer |

Przykładowe sekwencje:
- `operacja_id` - numeracja operacji
- `nrpalety` - numeracja palet
- `nretykiety` - numeracja etykiet

#### **status_system**
Słownik statusów jakości.

| Pole | Typ | Opis |
|------|-----|------|
| id | INT | Klucz główny |
| nazwa | VARCHAR | Nazwa statusu (np. 'OK') |

#### **skaner_lot_dataprod**
Konfiguracja dekodowania LOT i daty produkcji.

| Pole | Typ | Opis |
|------|-----|------|
| system_id | INT | ID systemu |
| rok | VARCHAR | Pozycje roku w LOT (format: "start,długość") |
| miesiac | VARCHAR | Pozycje miesiąca w LOT |
| dzien | VARCHAR | Pozycje dnia w LOT |
| prefix_lot | VARCHAR | Prefix do dodania przed LOT |

## 2. Relacje między tabelami

### Diagram relacji głównych

```
list_control (1) ----< (N) listcontrol_palety
                              |
                              v
awizacje_dostaw_head (1) ----< (N) palety (1) ----< (N) etykiety
        |                                                  |
        v                                                  v
awizacje_dostaw_dane                                    kody

operacje >---- etykiety (rejestr operacji)

typypalet (1) ----< (N) palety

status_system (1) ----< (N) kody
```

### Opis relacji

1. **list_control** → **listcontrol_palety** → **palety**
   - Lista kontrolna może zawierać wiele palet
   - Relacja N:M realizowana przez tabelę pośrednią

2. **palety** → **etykiety**
   - Jedna paleta może zawierać wiele etykiet
   - Relacja 1:N

3. **kody** → **etykiety**
   - Jeden kod produktu może być użyty w wielu etykietach
   - Relacja 1:N

4. **awizacje_dostaw_head** → **awizacje_dostaw_dane**
   - Nagłówek awizacji może mieć wiele pozycji
   - Relacja 1:N

5. **list_control** → **awizacje_dostaw_head**
   - Lista kontrolna jest powiązana z awizacją
   - Relacja 1:1

## 3. Indeksy i klucze

### Klucze główne
- Wszystkie tabele używają pola `id` jako klucza głównego

### Klucze obce
- `etykiety.kod_id` → `kody.id`
- `etykiety.paleta_id` → `palety.id`
- `etykiety.listcontrol_id` → `list_control.id`
- `listcontrol_palety.listcontrol_id` → `list_control.id`
- `listcontrol_palety.paleta_id` → `palety.id`
- `palety.typypalet_id` → `typypalet.id`
- `awizacje_dostaw_dane.awizacje_dostaw_id` → `awizacje_dostaw_head.id`
- `list_control.awizacje_id` → `awizacje_dostaw_head.id`

### Indeksy sugerowane
- `etykiety.system_id` - filtrowanie po kliencie
- `etykiety.etykieta_klient` - wyszukiwanie po SSC
- `kody.system_id, kody.kod` - wyszukiwanie kodów
- `kody.ean, kody.ean_jednostki` - wyszukiwanie po EAN

## 4. Integralność danych

### Ograniczenia
1. Kod produktu musi być unikalny w ramach systemu
2. EAN nie może się powtarzać między różnymi kodami
3. Numer palety (DS) jest unikalny
4. Etykieta klienta (SSC) powinna być unikalna gdy active=1

### Walidacje biznesowe
1. Sprawdzanie duplikatów etykiet przed dodaniem
2. Weryfikacja ilości względem awizacji
3. Kontrola wymaganych pól (LOT, daty) w zależności od konfiguracji kodu
4. Walidacja dat (data produkcji < data ważności)
