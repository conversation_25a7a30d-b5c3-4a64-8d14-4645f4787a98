# Dokumentacja funkcjonalności przesunięć magazynowych (WMS) - Część 2

## 2. OPERACJE NA BAZIE DANYCH

### Operacja: Pobranie ostatniego identyfikatora operacji

- **Nazwa biznesowa**: Inicjalizacja identyfikatora operacji
- **Metoda komunikacji**: SQL bezpośrednie
- **Przykład kodu**:
```csharp
string zapytanie = "SELECT last FROM docnumber d WHERE d.name='operacja_id';"
operac_id_global = (string)BazaDanychExternal.Wyczytaj_Jedna_Wartosc(zapytanie);

string zapyt = "update docnumber d set last=last+1 WHERE d.name='operacja_id'; ";
BazaDanychExternal.Wyczytaj_Jedna_Wartosc(zapyt);
```
- **Parametry wejściowe**: Brak
- **Parametry wyjściowe**: Identyfikator operacji (string)
- **Warunki biz<PERSON>owe**: Operacja wymaga połączenia z bazą danych

### Operacja: Sprawdzenie czy miejsce jest zajęte

- **Nazwa biznesowa**: Weryfikacja dostępności miejsca magazynowego
- **Metoda komunikacji**: SQL bezpośrednie
- **Przykład kodu**:
```csharp
public static string Czy_miejsce_zajete(string hala_local, string regal_local, string miejsce_local, string poziom_local)
{
    string zapytanie = "SELECT count(1) FROM etykiety e left join miejsca m on e.miejscep=m.id where (e.active=1) and hala='" + hala_local + "' and regal='" + regal_local + "' and miejsce='" + miejsce_local + "' and poziom='" + poziom_local + "'  and poziom!='A' and !(regal between 105 and 109) and zbiorka!=1;";
    string aa = (string)BazaDanychExternal.Wyczytaj_Jedna_Wartosc(zapytanie);
    return aa;
}
```
- **Parametry wejściowe**: hala, regał, miejsce, poziom (wszystkie string)
- **Parametry wyjściowe**: Liczba etykiet przypisanych do miejsca (string)
- **Warunki biznesowe**: Miejsca z poziomem "A", regałami 105-109 oraz miejsca zbiórki są obsługiwane inaczej

### Operacja: Pobranie aktualnego miejsca etykiety

- **Nazwa biznesowa**: Lokalizacja etykiety
- **Metoda komunikacji**: SQL bezpośrednie z JOIN
- **Przykład kodu**:
```csharp
private static string[] miejsce_aktualne(string etykieta)
{
    string zapytanie = "select m.hala,m.regal,m.miejsce,m.poziom from etykiety e left join miejsca m on e.miejscep=m.id where (e.id='" + etykieta + "' OR e.etykieta_klient='" + etykieta + "' ) and (e.active=1 or e.active is null) order by e.id desc limit 1";
    if (etykieta.Substring(0, 2) == "DS")
    {
        zapytanie = "select m.hala,m.regal,m.miejsce,m.poziom from etykiety e left join miejsca m on e.miejscep=m.id where (e.paleta_id='" + etykieta.Replace("DS", "") + "') and (e.active=1 or e.active is null) order by e.id desc limit 1";
    }
    
    object obj2 = BazaDanychExternal.Wyczytaj_Tabele(zapytanie);
    DataTable tabela = (DataTable)obj2;
    string[] bb = new string[4];
    bb[0] = tabela.Rows[0]["hala"].ToString();
    bb[1] = tabela.Rows[0]["regal"].ToString();
    bb[2] = tabela.Rows[0]["miejsce"].ToString();
    bb[3] = tabela.Rows[0]["poziom"].ToString();
    return bb;
}
```
- **Parametry wejściowe**: Numer etykiety (string)
- **Parametry wyjściowe**: Tablica stringów [hala, regał, miejsce, poziom]
- **Warunki biznesowe**: Specjalne traktowanie etykiet zaczynających się od "DS" (odwołanie do paleta_id)

### Operacja: Realizacja zmiany miejsca

- **Nazwa biznesowa**: Przesunięcie magazynowe
- **Metoda komunikacji**: API przez XML
- **Przykład kodu**:
```csharp
public void Realizuj_Zmiane_Miejsca(string etykieta, string zm_type, string zm_date, string login, string hala, string regal, string miejsce, string poziom, string paleta_id, string miejsce_id)
{
    typ_operacji = "ZM";

    if (regal == "POD") typ_operacji = "ZM_POD";

    XmlNode node_local2 = KomunikacjaSerwerem("zmiana_miejsca_regal_manual.php?akcja=realizacja_zmiany&pracownik_id=" + Wlasciwosci.id_Pracownika + "&imie_nazwisko=" + Uri.EscapeUriString(Wlasciwosci.imie_nazwisko) + "&operac_id=" + operac_id_global + "&wozek=" + Wlasciwosci.wozek + "&system_id=" + Wlasciwosci.system_id_id + "&miejsce_id=" + miejsce_id + "&zm_nr=" + zm_nr_global + "&etykieta_scan=" + etykieta + "&id_niezrealizowane=" + id_zmiany_miejsca_niezrealizowane);
    if (node_local2["komunikat"].InnerText != "OK")
    {
        MessageBox.Show(node_local2["komunikat"].InnerText);
    }
    else
    {
        label4.Text = "";
        etykieta_ostatnia = etykieta;
        etykieta_textbox.Text = "";
        licznik_label.Text = node_local2["licznik"].InnerText;
        last_result_text.Text = "Zmieniono: " + Environment.NewLine + node_local2["last_result_text"].InnerText;
        zm_nr_global = node_local2["zm_nr"].InnerText;
        operac_id_global = node_local2["operac_id"].InnerText;
        ilosc_etykiet.Text = node_local2["ile_dokument"].InnerText + "/" + node_local2["ile_wstawione"].InnerText;
    }
}
```
- **Parametry wejściowe**: etykieta, typ zmiany, data, login, hala, regał, miejsce, poziom, id palety, id miejsca
- **Parametry wyjściowe**: Odpowiedź XML z komunikatem, licznikiem, ostatnim rezultatem, numerem ZM, id operacji, ilościami
- **Warunki biznesowe**: Specjalne traktowanie regału "POD" (typ operacji zmienia się na "ZM_POD")

### Operacja: Pobieranie danych etykiety

- **Nazwa biznesowa**: Pobranie informacji o etykiecie
- **Metoda komunikacji**: SQL bezpośrednie z wieloma JOIN
- **Przykład kodu**:
```csharp
private bool get_etykieta(string etykieta)
{
    string zapytanie = "select ifnull(m.hala,'') as hala,ifnull(m.regal,'') as regal,ifnull(m.miejsce,'') as miejsce,e.docin_id, ifnull(concat(m.hala,'-',m.regal,'-',m.miejsce,'-',m.poziom),'')  as adres,e.paleta_id, ifnull(delivery_nr,'') as delivery_nr , ifnull((select nr_dl from dlcollect dd where nr_et=e.id order by dd.id desc limit 1 ),'') as dlcollect,ifnull(g.nazwa,kg.nazwa) as nazwa,ifnull(g.id,kg.id) as grupa_id, ifnull(g.zakres_opis,kg.nazwa) as zakres,(SELECT count(1) as ile FROM miejsca_wolne w left join miejsca_grupy m on m.miejsce_id=w.miejsce_id left join miejsca mm on mm.id=w.miejsce_id WHERE m.kody_grupy_id=g.id and w.miejsce_id is not null and w.ts is null and mm.widoczne=1) as miejsc_przydzielonych,  s.nazwa as status_nazwa,s.funkcja_stat from etykiety e left join miejsca m on e.miejscep=m.id left join delivery d on e.delivery_id=d.id left join kody k on e.kod_id=k.id left join kody_grupy g on k.kody_grupy_id=g.id left join kody_grupy kg on e.system_id=kg.system_id left join status_system s on e.status_id=s.id where   (e.id='" + etykieta + "' OR e.etykieta_klient='" + etykieta + "' ) and (e.active=1 or e.active is null) order by e.id desc limit 1 ";
    if (etykieta.Substring(0, 2) == "DS")
    {
        zapytanie = "select ifnull(m.hala,'') as hala,ifnull(m.regal,'') as regal,ifnull(m.miejsce,'') as miejsce,e.docin_id, ifnull(concat(m.hala,'-',m.regal,'-',m.miejsce,'-',m.poziom),'')  as adres,e.paleta_id, ifnull(delivery_nr,'') as delivery_nr , ifnull((select nr_dl from dlcollect dd where nr_et=e.id order by dd.id desc limit 1 ),'') as dlcollect,ifnull(g.nazwa,kg.nazwa) as nazwa,ifnull(g.id,kg.id) as grupa_id, ifnull(g.zakres_opis,kg.nazwa) as zakres,(SELECT count(1) as ile FROM miejsca_wolne w left join miejsca_grupy m on m.miejsce_id=w.miejsce_id left join miejsca mm on mm.id=w.miejsce_id WHERE m.kody_grupy_id=g.id and w.miejsce_id is not null and w.ts is null and mm.widoczne=1) as miejsc_przydzielonych,  s.nazwa as status_nazwa,s.funkcja_stat from etykiety e left join miejsca m on e.miejscep=m.id left join delivery d on e.delivery_id=d.id left join kody k on e.kod_id=k.id left join kody_grupy g on k.kody_grupy_id=g.id left join kody_grupy kg on e.system_id=kg.system_id left join status_system s on e.status_id=s.id where   (e.paleta_id='" + etykieta.Replace("DS", "") + "') and (e.active=1 or e.active is null) order by e.id desc limit 1 ";
    }
    
    object obj2 = BazaDanychExternal.Wyczytaj_Tabele(zapytanie);

    DataTable table = (DataTable)obj2;
    if (table.Rows.Count < 1)
    {
        MessageBox.Show("Brak etykiety w bazie danych lub jeszcze nie przyjęta!");
        etykieta_textbox.Text = "";
        miejsce_poprzednie.Text = "";
        return false;
    }

    if (table.Rows[0]["funkcja_stat"].ToString() == "blokada_wyd" && table.Rows[0]["regal"].ToString() != "PROD")
    {
        MessageBox.Show("Etykieta " + etykieta + " ma status " + table.Rows[0]["status_nazwa"].ToString());
        etykieta_textbox.Text = "";
        miejsce_poprzednie.Text = "";
        return false;
    }

    if (table.Rows[0]["delivery_nr"].ToString() != "" || table.Rows[0]["dlcollect"].ToString() != "")
    {
        MessageBox.Show("Towar przygotowany na wysyłkę DL " + table.Rows[0]["delivery_nr"].ToString() + "," + table.Rows[0]["dlcollect"].ToString());
        etykieta_textbox.Text = "";
        miejsce_poprzednie.Text = "";
        return false;
    }

    // Aktualizacja interfejsu
    ilosc_etykiet.Text = "" + Count_PZ_Wstawione(etykieta).ToString() + " z " + Count_PZ(etykieta).ToString();
    docin_ostatnie = table.Rows[0]["docin_id"].ToString();
    etykieta_ostatnia = etykieta;
    grupa_id = table.Rows[0]["grupa_id"].ToString();
    miejsce_poprzednie.Text = "Aktualnie:" + Environment.NewLine + "" + table.Rows[0]["adres"].ToString();
    paleta_id = table.Rows[0]["zakres"].ToString() + table.Rows[0]["paleta_id"].ToString();
    last_result_text.Text = "";

    return true;
}
```
- **Parametry wejściowe**: Numer etykiety (string)
- **Parametry wyjściowe**: Boolean (true jeśli etykieta istnieje i może być przesunięta)
- **Warunki biznesowe**:
  - Specjalne traktowanie etykiet zaczynających się od "DS"
  - Sprawdzenie statusu etykiety (blokowanie etykiet o statusie "blokada_wyd" nie będących w regale "PROD")
  - Sprawdzenie czy towar nie jest przygotowany na wysyłkę
