using System;
using System.Collections.Generic;
using System.Data;
using System.Threading;
using System.Windows.Forms;
using MySql.Data.MySqlClient;

namespace Tarczyn__Magazyn
{
    public static class BazaDanychExternal_new
    {
        private static MySqlConnection _connection;
        private static MainMenu _parent;
        private static MySqlTransaction _transaction;
        
        // Publiczne pole Command dla kompatybilności wstecznej
        public static MySqlCommand Command;
        
        // Publiczne pole polaczenie_mysql dla kompatybilności wstecznej
        public static string polaczenie_mysql = "";

        // Użycie pooling: mín i max rozmiar puli
        private const string PoolOptions = "Pooling=true;Min Pool Size=5;Max Pool Size=50;";
        
        // Przechowuje string połączenia dla ewentualnego ponownego użycia
        public static string ConnectionString { get; private set; }

        public static void Inicjalizacja(string baza, string host, string user, string pass, MainMenu parent)
        {
            _parent = parent;
            ConnectionString = "Server=" + host + ";User ID=" + user + ";Password=" + pass + ";Database=" + baza + ";" + PoolOptions + ";CharSet=utf8;";
            polaczenie_mysql = "Server=" + host + ";userid=" + user + ";password=" + pass + ";database=" + baza + "";
            _connection = new MySqlConnection(ConnectionString);
            
            if (_parent != null && _parent.MYSQLbar != null)
                _connection.StateChange += ConnectionStateChanged;
                
            OpenConnection();
            
            // Inicjalizacja Command dla kompatybilności wstecznej
            Command = new MySqlCommand("", _connection);
        }

        private static bool OpenConnection()
        {
            try
            {
                if (_connection.State != ConnectionState.Open)
                    _connection.Open();
                return true;
            }
            catch
            {
                return false;
            }
        }
        
        public static void ChangeHost(string baza, string host, string user, string pass)
        {
            // Zamknij stare połączenie jeśli istnieje
            if (_connection != null && _connection.State == ConnectionState.Open)
            {
                _connection.Close();
                _connection.Dispose();
            }
            
            // Tworzenie nowego połączenia z nowymi parametrami
            ConnectionString = "Server=" + host + ";User ID=" + user + ";Password=" + pass + ";Database=" + baza + ";" + PoolOptions + ";CharSet=utf8;";
            polaczenie_mysql = "Server=" + host + ";userid=" + user + ";password=" + pass + ";database=" + baza + "";
            _connection = new MySqlConnection(ConnectionString);
            
            if (_parent != null && _parent.MYSQLbar != null)
                _connection.StateChange += ConnectionStateChanged;
                
            OpenConnection();
            
            // Aktualizacja Command dla kompatybilności wstecznej
            Command = new MySqlCommand("", _connection);
        }
        
        private static void ConnectionStateChanged(object sender, StateChangeEventArgs e)
        {
            try
            {
                if (_parent != null && _parent.MYSQLbar != null)
                {
                    switch (e.CurrentState)
                    {
                        case ConnectionState.Open:
                            _parent.MYSQLbar.Value = 1;
                            break;
                        case ConnectionState.Closed:
                            _parent.MYSQLbar.Value = 0;
                            break;
                    }
                }
            }
            catch
            {
                if (_parent != null && _parent.MYSQLbar != null)
                    _parent.MYSQLbar.Value = 0;
            }
        }
        
        public static bool SprawdzCzyIstniejePolaczenie()
        {
            // Jeśli sieć jest niedostępna, zaczekaj kilka sekund
            int proby = 0;
            const int maxProby = 3;
            
            // Sprawdź sygnał, jeśli _parent.myWlan jest dostępny
            if (_parent != null && _parent.myWlan != null)
            {
                while (proby < maxProby)
                {
                    if (_parent.myWlan != null && _parent.myWlan.get_Signal_int() < 2)
                    {
                        proby++;
                        Thread.Sleep(1000); // Krótszy czas oczekiwania dla lepszej responsywności
                    }
                    else
                    {
                        break;
                    }
                }
            }
            
            // Próba połączenia z bazą danych
            try
            {
                if (_connection.State == ConnectionState.Open)
                    return true;
                
                proby = 0;
                while (proby < maxProby)
                {
                    try
                    {
                        _connection.Open();
                        return true;
                    }
                    catch
                    {
                        proby++;
                        Thread.Sleep(1000);
                    }
                }
                return false;
            }
            catch (Exception ex)
            {
                MessageBox.Show(ex.ToString());
                return false;
            }
        }

        public static int DokonajUpdate(string zapytanie)
        {
            return DokonajUpdate(zapytanie, null);
        }

        public static int DokonajUpdate(string zapytanie, Dictionary<string, object> parameters)
        {
            if (!OpenConnection())
                throw new InvalidOperationException("Brak połączenia z bazą");

            // Używamy Command dla kompatybilności wstecznej
            Command.CommandText = zapytanie;
            
            // Czyścimy poprzednie parametry
            Command.Parameters.Clear();
            
            if (parameters != null)
            {
                foreach (KeyValuePair<string, object> p in parameters)
                    Command.Parameters.AddWithValue(p.Key, p.Value);
            }
            Command.Prepare();
            return Command.ExecuteNonQuery();
        }

        public static object Wyczytaj_Jedna_Wartosc(string zapytanie)
        {
            return Wyczytaj_Jedna_Wartosc(zapytanie, null);
        }

        public static object Wyczytaj_Jedna_Wartosc(string zapytanie, Dictionary<string, object> parameters)
        {
            if (!OpenConnection())
                throw new InvalidOperationException("Brak połączenia z bazą");

            // Używamy Command dla kompatybilności wstecznej
            Command.CommandText = zapytanie;
            
            // Czyścimy poprzednie parametry
            Command.Parameters.Clear();
            
            if (parameters != null)
            {
                foreach (KeyValuePair<string, object> p in parameters)
                    Command.Parameters.AddWithValue(p.Key, p.Value);
            }
            Command.Prepare();
            object result = Command.ExecuteScalar();
            if (result == null || result == DBNull.Value)
                return null;
            return result;
        }

        public static DataTable Wyczytaj_Tabele(string zapytanie)
        {
            return Wyczytaj_Tabele(zapytanie, null);
        }

        public static DataTable Wyczytaj_Tabele(string zapytanie, Dictionary<string, object> parameters)
        {
            if (!OpenConnection())
                throw new InvalidOperationException("Brak połączenia z bazą");

            // Używamy Command dla kompatybilności wstecznej
            Command.CommandText = zapytanie;
            
            // Czyścimy poprzednie parametry
            Command.Parameters.Clear();
            
            if (parameters != null)
            {
                foreach (KeyValuePair<string, object> p in parameters)
                    Command.Parameters.AddWithValue(p.Key, p.Value);
            }
            Command.Prepare();
            
            using (MySqlDataAdapter adapter = new MySqlDataAdapter(Command))
            {
                DataTable table = new DataTable();
                adapter.Fill(table);
                return table;
            }
        }

        public static void ZamknijPolaczenie()
        {
            if (_connection != null)
            {
                _connection.Close();
                _connection.Dispose();
            }
        }
        
        // Alternatywna nazwa metody dla kompatybilności
        public static void Zamknij_polaczenie()
        {
            ZamknijPolaczenie();
        }
        
        public static void Wyczysc()
        {
            try
            {
                // Czyszczenie Command dla kompatybilności wstecznej
                if (Command != null)
                {
                    Command.Dispose();
                    Command = null;
                }
                
                // Czyszczenie połączenia, jeśli jeszcze istnieje
                if (_connection != null)
                {
                    _connection.Dispose();
                }
            }
            catch
            {
                // Ignorowanie błędów podczas czyszczenia zasobów
            }
        }
        
        // Metody do obsługi transakcji
        public static void Inicjuj_Transakcje()
        {
            if (!OpenConnection())
                throw new InvalidOperationException("Brak połączenia z bazą");
                
            _transaction = _connection.BeginTransaction();
            
            // Aktualizacja Command.Transaction dla kompatybilności wstecznej
            if (Command != null)
                Command.Transaction = _transaction;
        }
        
        public static void Zacznij_Transakcje()
        {
            if (_transaction == null)
                Inicjuj_Transakcje();
            else if (Command != null)
                Command.Transaction = _transaction;
        }
        
        public static void Przerwij_Transakcje()
        {
            if (_transaction != null)
            {
                _transaction.Rollback();
                _transaction.Dispose();
                _transaction = null;
            }
        }
        
        public static void Zakoncz_Transakcje()
        {
            if (_transaction != null)
            {
                _transaction.Commit();
                _transaction.Dispose();
                _transaction = null;
            }
        }
    }
}
