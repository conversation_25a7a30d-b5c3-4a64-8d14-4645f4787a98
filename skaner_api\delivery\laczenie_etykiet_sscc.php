<?php

include_once '../Db.class.php';
include_once '../funkcje.inc';

$db = new Db();

$baza_danych = "wmsgg";
$komunikat = "OK";

// Pobierz dane z $_GET
$etykieta_klient1 = $_GET['etykieta_klient1']; // wymagany
$etykieta_klient2 = $_GET['etykieta_klient2'];  // wymagany
$etykieta_klient3 = $_GET['etykieta_klient3']; // opcjonalny
$etykieta_klient4 = $_GET['etykieta_klient4']; // opcjonaly
$doc_nr_max = ""; // niech zawsze łączy w obrębie oddzielnych PP by można było łatwo sprawdzić.
$pracownik_id = $_GET['pracownik_id']; // wymagany

$system_id = $_GET['system_id']; // wymagany
$imie_nazwisko = $_GET['imie_nazwisko']; // wymagany

// wybrać palete id
$ilosc_etykieta1 = 0;
$ilosc_etykieta2 = 0;
$ilosc_etykieta3 = 0;
$ilosc_etykieta4 = 0;
$ilosc_suma = 0;


// Brak etykiety 1
if (empty($etykieta_klient1)) {
        $komunikat = "Brak etykiety1";
        return show_komunikat_xml($komunikat);
}
if (empty($etykieta_klient2)) {
        $komunikat = "Brak etykiety2";
        return show_komunikat_xml($komunikat);
}

if (empty($system_id)) {
        $komunikat = "Nie rozpoznano systemu klietna !!!";
        return show_komunikat_xml($komunikat);
}

if (empty($pracownik_id) || empty($imie_nazwisko)) {
        $komunikat = "Nie rozpoznano pracownika !!!";
        return show_komunikat_xml($komunikat);
}


$tmp_arr1 = sprawdz_szukana_etykiete_WMS($etykieta_klient1, $system_id, $komunikat, $baza_danych, $db);

if (empty($tmp_arr1['aRowEtWms'])) {
        $komunikat = "Nie znaleziono aktywnej etykietyty $etykieta_klient1 !!!! ";
        return show_komunikat_xml($komunikat);
}
$aRowEtWms1 = $tmp_arr1['aRowEtWms'];


$delivery_nr_wiodace = $aRowEtWms1['delivery_id'];
$kod_wiodacy = $aRowEtWms1['kod'];
$ean_wiodacy = $aRowEtWms1['ean']; // zmiana by pilnował ean
$ilosc_etykieta1 = $aRowEtWms1['ilosc'];
$etykieta_id1 = $aRowEtWms1['id'];
$ilosc_suma = $ilosc_etykieta1;


if (empty($ean_wiodacy)) {
        $komunikat = "Kod $kod_wiodacy nie zawiera wymaganego kodu ean opakowania. Przerywam operację !!!! ";
        return show_komunikat_xml($komunikat);
}

if (empty($delivery_nr_wiodace)) {
        $komunikat = "Etykieta  $etykieta_klient1 nie jest na żadnej DL. Przerywam operację !!!! ";
        return show_komunikat_xml($komunikat);
}


if (!empty($etykieta_klient2)) {
        $tmp_arr2 = sprawdz_szukana_etykiete_WMS($etykieta_klient2, $system_id, $komunikat, $baza_danych, $db);
        if (empty($tmp_arr2['aRowEtWms'])) {
                $komunikat = "Nie znaleziono aktywnej etykietyty $etykieta_klient2 !!!! ";
                return show_komunikat_xml($komunikat);
        }
        $aRowEtWms2 = $tmp_arr2['aRowEtWms'];

        $delivery_nr_2 = $aRowEtWms2['delivery_id'];
        $kod_2 = $aRowEtWms2['kod'];
        $ean_2 = $aRowEtWms2['ean'];
        $ilosc_etykieta2 = $aRowEtWms2['ilosc'];
        $etykieta_id2 = $aRowEtWms2['id'];
        $ilosc_suma += $ilosc_etykieta2;

        if ($delivery_nr_2 != $delivery_nr_wiodace) {
                $komunikat = "Etykieta $etykieta_klient2 jest na DL $delivery_nr_2 a  $etykieta_klient1 jest na DL $delivery_nr_wiodace. Przerwam operację !!!! ";
                return show_komunikat_xml($komunikat);
        }

        if ($ean_2 != $ean_wiodacy) {
                $komunikat = "Etykieta $etykieta_klient2  ma kod opakowania $ean_2 a $etykieta_klient1 ma kod opakowania $ean_wiodacy. Przerwam operację !!!! ";
                return show_komunikat_xml($komunikat);
        }
}

if (!empty($etykieta_klient3)) {
        $tmp_arr3 = sprawdz_szukana_etykiete_WMS($etykieta_klient3, $system_id, $komunikat, $baza_danych, $db);
        if (empty($tmp_arr3['aRowEtWms'])) {
                $komunikat = "Nie znaleziono aktywnej etykietyty $etykieta_klient3 !!!! ";
                return show_komunikat_xml($komunikat);
        }
        $aRowEtWms3 = $tmp_arr3['aRowEtWms'];
        $delivery_nr_3 = $aRowEtWms3['delivery_id'];
        $kod_3 = $aRowEtWms3['kod'];
        $ean_3 = $aRowEtWms3['ean'];

        $ilosc_etykieta3 = $aRowEtWms3['ilosc'];
        $etykieta_id3 = $aRowEtWms3['id'];
        $ilosc_suma += $ilosc_etykieta3;


        if ($delivery_nr_3 != $delivery_nr_wiodace) {
                $komunikat = "Etykieta $etykieta_klient3 jest na DL $delivery_nr_3 a  $etykieta_klient1 jest na DL $delivery_nr_wiodace. Przerwam operację !!!! ";
                return show_komunikat_xml($komunikat);
        }

        if ($ean_3 != $ean_wiodacy) {
                $komunikat = "Etykieta $etykieta_klient3  ma kod opakowania $ean_3 a $etykieta_klient1 ma kod opakowania $ean_wiodacy. Przerwam operację !!!! ";
                return show_komunikat_xml($komunikat);
        }
}

if (!empty($etykieta_klient4)) {
        $tmp_arr4 = sprawdz_szukana_etykiete_WMS($etykieta_klient4, $system_id, $komunikat, $baza_danych, $db);
        if (empty($tmp_arr4['aRowEtWms'])) {
                $komunikat = "Nie znaleziono aktywnej etykietyty $etykieta_klient4 !!!! ";
                return show_komunikat_xml($komunikat);
        }
        $aRowEtWms4 = $tmp_arr4['aRowEtWms'];
        $delivery_nr_4 = $aRowEtWms4['delivery_id'];
        $kod_4 = $aRowEtWms4['kod'];
        $ean_4 = $aRowEtWms4['ean'];
        $ilosc_etykieta4 = $aRowEtWms4['ilosc'];
        $etykieta_id4 = $aRowEtWms4['id'];
        $ilosc_suma += $ilosc_etykieta4;


        if ($delivery_nr_4 != $delivery_nr_wiodace) {
                $komunikat = "Etykieta $etykieta_klient4 jest na DL $delivery_nr_4 a  $etykieta_klient1 jest na DL $delivery_nr_wiodace. Przerwam operację !!!! ";
                return show_komunikat_xml($komunikat);
        }

        if ($ean_4 != $ean_wiodacy) {
                $komunikat = "Etykieta $etykieta_klient4  ma kod opakowania $ean_4 a $etykieta_klient1 ma kod opakowania $ean_wiodacy. Przerwam operację !!!! ";
                return show_komunikat_xml($komunikat);
        }
}




// Sprawdź, czy doc_nr_max jest pusty

$kontrah_wew_id = get_kontrah_wew($baza_danych, $system_id, $db);
if(empty($kontrah_wew_id)) {
        $komunikat = "Nie znaleziono kontrahenta wewnętrznego!!!";
        return show_komunikat_xml($komunikat);
    }
$pracownik_id = get_pracownik($baza_danych, $imie_nazwisko, $db);
if(empty($pracownik_id)) {
        $komunikat = "Nie znaleziono pracownika!!!";
        return show_komunikat_xml($komunikat);
    }
$nrpalety = docnumber_increment($baza_danych, "nrpalety", $db);
if(empty($nrpalety)) {
        $komunikat = "Nie udało się pobrać nowego nrpalety!!!";
        return show_komunikat_xml($komunikat);
    }
$numer = docnumber_increment($baza_danych, "PP", $db);
if(empty($numer)) {
        $komunikat = "Nie można uzyskać numeru palety!!!";
        return show_komunikat_xml($komunikat);
    }
$docout_id = tworz_dokument_docout($baza_danych, "PP-", $numer, $pracownik_id, $kontrah_wew_id, $db);
if(empty($docout_id)) {
        $komunikat = "Nie można utworzyć dokumentu docout!!!";
        return show_komunikat_xml($komunikat);
    }
$docin_id = tworz_dokument_docin($baza_danych, "PP", $numer, $pracownik_id, $kontrah_wew_id, $db);
if(empty($docin_id)) {
        $komunikat = "Nie można utworzyć dokumentu docin!!!";
        return show_komunikat_xml($komunikat);
    }


    



// Wstaw nowy rekord do tabeli palety
$sql = "INSERT INTO palety (id, typypalet_id, ilosc, j_skladowania_id, pal_klient, ts_utworzenia) 
        VALUES ('$nrpalety', 11, 1, 1, '', NOW())";
$db->mGetResultAsXML($sql);


$etykieta_id_nowa = przepakowanie_etykiety_step2_ilosc_pobierana($baza_danych, $etykieta_id1, $docin_id, $ilosc_suma, $db);

$nowy_sscc = get_nowy_sscc("wmsgg", $db);

// aktualizacja etykiety_klient w bazie

$sql = "UPDATE etykiety SET etykieta_klient = '$nowy_sscc', paleta_id='$nrpalety' WHERE id = $etykieta_id_nowa";
$result = $db->mGetResultAsXML($sql);


if (!empty($etykieta_id1)) {
        $sql = "DELETE FROM delivery_et WHERE etykieta_id = $etykieta_id1 AND delivery_id = $delivery_nr_wiodace";
        $result = $db->mGetResultAsXML($sql);

        $sql = "DELETE FROM dlcollect WHERE nr_et = $etykieta_id1 AND nr_dl = $delivery_nr_wiodace";
        $result = $db->mGetResultAsXML($sql);

        przepakowanie_etykiety_dezaktywowana($baza_danych, $etykieta_id1, $docout_id, $db);
}

if (!empty($etykieta_id2)) {
        $sql = "DELETE FROM delivery_et WHERE etykieta_id = $etykieta_id2 AND delivery_id = $delivery_nr_wiodace";
        $result = $db->mGetResultAsXML($sql);

        $sql = "DELETE FROM dlcollect WHERE nr_et = $etykieta_id2 AND nr_dl = $delivery_nr_wiodace";
        $result = $db->mGetResultAsXML($sql);

        przepakowanie_etykiety_dezaktywowana($baza_danych, $etykieta_id2, $docout_id, $db);
}

if (!empty($etykieta_id3)) {
        $sql = "DELETE FROM delivery_et WHERE etykieta_id = $etykieta_id3 AND delivery_id = $delivery_nr_wiodace";
        $result = $db->mGetResultAsXML($sql);

        $sql = "DELETE FROM dlcollect WHERE nr_et = $etykieta_id3 AND nr_dl = $delivery_nr_wiodace";
        $result = $db->mGetResultAsXML($sql);

        przepakowanie_etykiety_dezaktywowana($baza_danych, $etykieta_id3, $docout_id, $db);
}

if (!empty($etykieta_id4)) {
        $sql = "DELETE FROM delivery_et WHERE etykieta_id = $etykieta_id4 AND delivery_id = $delivery_nr_wiodace";
        $result = $db->mGetResultAsXML($sql);

        $sql = "DELETE FROM dlcollect WHERE nr_et = $etykieta_id4 AND nr_dl = $delivery_nr_wiodace";
        $result = $db->mGetResultAsXML($sql);

        przepakowanie_etykiety_dezaktywowana($baza_danych, $etykieta_id4, $docout_id, $db);
}



$sql = "insert into delivery_et(delivery_id, etykieta_id, ilosc_zamawiana) values (" . $delivery_nr_wiodace . ", " . $etykieta_id_nowa . "," . $ilosc_suma . ")";
//echo "<br>" . $sql;
$result13 = $db->mGetResultAsXML($sql);


$sql = "insert into $baza_danych.dlcollect(nr_dl,nr_et,system_id) values('" . $delivery_nr_wiodace . "','" . $etykieta_id_nowa . "','" . $system_id . "') ";
$result4 = $db->mGetResultAsXML($sql);


return xml_from_indexed_array(
        array(
            'komunikat' => $komunikat,
            'nowy_sscc' => $nowy_sscc,            
        )
    );

//return show_komunikat_xml($komunikat);





// realizuj laczenie etykiet z systemu WMS





function generate_checkdigit($upc_code) {
        $odd_total = 0;
        $even_total = 0;
    
        for ($i = 0; $i < 17; $i++) {
            if ((($i + 1) % 2) == 0) {
                /* Sum even digits */
                $even_total += $upc_code[$i];
            } else {
                /* Sum odd digits */
                $odd_total += $upc_code[$i];
            }
        }
    
        $sum = (3 * $odd_total) + $even_total;
    
        /* Get the remainder MOD 10 */
        $check_digit = $sum % 10;
    
        /* If the result is not zero, subtract the result from ten. */
        $aa = ($check_digit > 0) ? 10 - $check_digit : $check_digit;
        return $upc_code . $aa;
    }



function docnumber_get($baza_danych, $name, $db) {
        $sql = "SELECT last FROM $baza_danych.docnumber d
    WHERE d.name='$name' limit 1 "; //,dc_prac_id
        //echo "<br>" . $sql;
        $result = $db->mGetResultAsXML($sql);
    
        foreach ($result as $index => $aRow) {
            $wynik = $aRow['last'];
        }
        return $wynik;
    }



function get_nowy_sscc($baza_danych, $db) {
        $sscc_serial_num = docnumber_increment($baza_danych, 'sscc_serial_num', $db);
        $sscc_company_nu = docnumber_get($baza_danych, 'sscc_company_nu', $db);
        $sscc_prefix = docnumber_get($baza_danych, 'sscc_prefix', $db);
        $sscc = generate_checkdigit($sscc_prefix . $sscc_company_nu . str_pad($sscc_serial_num, 6, '0', STR_PAD_LEFT));
        return $sscc;
    }



function sprawdz_szukana_etykiete_WMS($etykieta_scan, $system_id, $komunikat, $baza_danych, $db)
{

        if (substr($etykieta_scan, 0, 2) == "DS") {
                $sql = 'select e.id,e.etykieta_klient,k.kod,k.ean,k.ilosc_w_opakowaniu,k.kod_nazwa,e.lot,e.active,TRIM(TRAILING "." FROM TRIM(TRAILING "0" from e.ilosc)) as ilosc,de.delivery_id,e.paleta_id,ss.nazwa as status_system_nazwa, ss.funkcja_stat , TRIM(TRAILING "." FROM TRIM(TRAILING "0" from de.ilosc_zamawiana))  as ilosc_zamawiana, d.dl_docin_id_wew, d.dl_docout_id_wew, d.miejsce_kompletacji,e.miejscep from ' . $baza_danych . '.etykiety e left join ' . $baza_danych . '.kody k on e.kod_id=k.id left join ' . $baza_danych . '.dlcollect dl ' . ' on dl.nr_et=e.id left join ' . $baza_danych . '.status_system ss  on e.status_id=ss.id left join ' . $baza_danych . '.delivery_et de ' . ' on de.etykieta_id=e.id left join ' . $baza_danych . '.delivery d ' . ' on de.delivery_id=d.id  where e.active=1  and e.paleta_id=' . str_replace("DS", "", $etykieta_scan) . ' and e.system_id=' . $system_id . '  order by nr_dl asc,e.ilosc asc limit 1'; //having nr_dl is null
        } else {
                $sql = 'select e.id,e.etykieta_klient,k.kod,k.ean,k.ilosc_w_opakowaniu,k.kod_nazwa,e.lot,e.active,TRIM(TRAILING "." FROM TRIM(TRAILING "0" from e.ilosc)) as ilosc,de.delivery_id,e.paleta_id,ss.nazwa as status_system_nazwa, ss.funkcja_stat , TRIM(TRAILING "." FROM TRIM(TRAILING "0" from de.ilosc_zamawiana))  as ilosc_zamawiana , d.dl_docin_id_wew, d.dl_docout_id_wew,d.miejsce_kompletacji,e.miejscep from ' . $baza_danych . '.etykiety e left join ' . $baza_danych . '.kody k on e.kod_id=k.id left join ' . $baza_danych . '.dlcollect dl ' . ' on dl.nr_et=e.id left join ' . $baza_danych . '.status_system ss  on e.status_id=ss.id left join ' . $baza_danych . '.delivery_et de ' . ' on de.etykieta_id=e.id left join ' . $baza_danych . '.delivery d ' . ' on de.delivery_id=d.id  where e.active=1  and  (e.id="' . $etykieta_scan . '" or e.etykieta_klient="' . $etykieta_scan . '")  and e.system_id=' . $system_id . '  order by nr_dl asc,e.ilosc asc limit 1'; //having nr_dl is null
        }
        //echo $sql;
        $result2 = $db->mGetResultAsXML($sql);
        $aRowEtWms = array();

        //    if (count($result2) > 1) {
        //        return array('komunikat' => $komunikat, 'aRowEtWms' => $result2, 'ilosc_pozycji_etykiety' => count($result2));
        //    }
        //echo "<br>" . $sql;
        foreach ($result2 as $index => $aRowEtWms) {
                if ($aRowEtWms['active'] != "1") {
                        $komunikat = "Etykieta jest nieaktywna. Przerywam operacje";
                        //echo "<br>" . $komunikat;
                        return show_komunikat_xml($komunikat);
                }

                if (!empty($aRowEtWms['funkcja_stat'])) {
                        $komunikat = "Etykieta jest w statusie: " . $aRowEtWms['status_system_nazwa'] . ". Przerywam operacje";
                        //echo "<br>" . $komunikat;
                        return show_komunikat_xml($komunikat);
                }

                // jeśli ilosc_zamawiana jest rózna od ilosc
                if ($aRowEtWms['ilosc_zamawiana'] != $aRowEtWms['ilosc']) {
                        $komunikat = "Etykieta zamawiana " . $aRowEtWms['ilosc_zamawiana'] . " jest różna od ilości etykiety: " . $aRowEtWms['ilosc'] . ". Przerywam operacje";
                        //echo "<br>" . $komunikat;
                        return show_komunikat_xml($komunikat);
                }
        }
        return array('komunikat' => $komunikat, 'aRowEtWms' => $aRowEtWms);
}
