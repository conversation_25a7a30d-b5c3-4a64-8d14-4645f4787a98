# Wymagania Produktu (PRD) dla Systemu WMS

## 1. Wprowadzenie

Projekt WMS (Warehouse Management System) to system informatyczny przeznaczony do zarządzania operacjami magazynowymi przy użyciu mobilnych skanerów kodów kreskowych. Aplikacja ma na celu usprawnienie i automatyzację kluczowych procesów, takich jak przyjmowanie towaru, jego składowanie, kompletacja zamówień i inwentaryzacja.

## 2. Główne Cele

- **Zwiększenie wydajności:** Automatyzacja zadań i redukcja operacji manualnych.
- **Redukcja błędów:** Minimalizacja pomyłek dzięki skanowaniu kodów kreskowych i walidacji danych w czasie rzeczywistym.
- **Poprawa kontroli:** Zapewnienie bieżącego wglądu w stany magazynowe i lokalizację towarów.

## 3. Zidentyfikowane Funkcjonalności

Na podstawie analizy plików projektu, zidentyfikowano następujące moduły i funkcje:

- **Zarządzanie Dostawami:**
  - Awizacje dostaw i rozkładanie towaru.
  - Kontrola przyjmowanego towaru.
  - Generowanie i drukowanie etykiet.

- **Operacje Magazynowe:**
  - Zmiana lokalizacji palet i kartonów.
  - Konsolidacja i łączenie etykiet.
  - Inwentaryzacja (ogólna, według miejsc, według produktów).

- **Kompletacja i Wydania (Picking):**
  - Realizacja zadań kompletacji (DL).
  - Szykowanie i kontrola wysyłek.
  - Obsługa dokumentów przewozowych i drukowanie etykiet kurierskich.

- **Zarządzanie Produkcją:**
  - Deklaracja produkcji.
  - Wczytywanie komponentów na linie produkcyjne.

- **Funkcje Pomocnicze:**
  - Wyszukiwanie informacji o produktach (EAN), paletach, miejscach.
  - Obsługa uszkodzonego towaru.
  - Aktualizacja kodów.

## 4. Użytkownicy Systemu

- **Operatorzy Magazynowi:** Główni użytkownicy aplikacji na skanerach, wykonujący operacje magazynowe.
- **Administratorzy/Kierownicy:** Osoby nadzorujące procesy, prawdopodobnie z dostępem do dodatkowych funkcji lub raportów (może przez interfejs webowy).
