﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using System.Threading;
using MySql.Data.MySqlClient;
using Tarczyn__Magazyn;

namespace Tarczyn__Magazyn
{
    public partial class Delivery_kontrola_new : Form
    {
        private bool disposed = false;

        // Usuń tę implementację, ponieważ już istnieje w Designer.cs
        /*protected override void Dispose(bool disposing)
        {
            if (!disposed)
            {
                if (disposing)
                {
                    ZakonczSkanowanie();
                    
                    if (AktualneSztuki_Global != null)
                    {
                        AktualneSztuki_Global.Dispose();
                        AktualneSztuki_Global = null;
                    }

                    if (_tableStyle != null)
                    {
                        _tableStyle.Dispose();
                        _tableStyle = null;
                    }

                    if (components != null)
                    {
                        components.Dispose();
                        components = null;
                    }
                }
                disposed = true;
            }
            base.Dispose(disposing);
        }*/

        // Zamiast tego, zmodyfikuj metodę Dispose w Designer.cs dodając własną logikę
        protected override void Dispose(bool disposing)
        {
            if (!disposed)
            {
                if (disposing)
                {
                    ZakonczSkanowanie();
                    
                    if (AktualneSztuki_Global != null)
                    {
                        AktualneSztuki_Global.Dispose();
                        AktualneSztuki_Global = null;
                    }

                    if (_tableStyle != null)
                    {
                        _tableStyle.Dispose();
                        _tableStyle = null;
                    }

                    if (components != null)
                    {
                        components.Dispose();
                        components = null;
                    }
                }
                disposed = true;
            }
            base.Dispose(disposing);
        }

        ActionMenu myParent = null;
        DataTable AktualneSztuki_Global = null;

        public string delivery_id_global="";
        bool edycja = false;
        string operac_id_global = "";

        private DataGridCell editCell;
        private bool inEditMode = false;
        private bool inUpdateMode = false;
        private DataGridTableStyle _tableStyle;

        int ile_kodow = 0;

        public Delivery_kontrola_new(ActionMenu MyParent)
        {
            this.myParent = MyParent; 

            FullScreenMode.OknoOFF(this);
            InitializeComponent();
            ZacznijSkanowanie();
            Wlasciwosci.CurrentOperacja = "26";
            Exit.Text = "Zapisz " + Environment.NewLine + " kontrole";
            //pokaz_podglad();
            //BazaDanychExternal.Inicjalizacja(Wlasciwosci.Varbaza, Wlasciwosci.Varhost, Wlasciwosci.VarLoginMYSQL, Wlasciwosci.VarHasloMYSQL, this);  
            
            //if (Wlasciwosci.system_id_id == "34")
            //{
            //    button2.Visible=false;
                
            //}

        }

        private Thread _scanningThread;
        private volatile bool _shouldStop;

        private delegate void InvokeDelegate(string text);

        private void ZacznijSkanowanie()
        {
            _shouldStop = false;
            StringBuilder login = new StringBuilder();
            Skaner.UstawTryb_String(login);
            Skaner.Zacznij_Skanowanie();
            
            _scanningThread = new Thread(() => CheckString(login));
            _scanningThread.IsBackground = true;
            _scanningThread.Start();
        }
        private void CheckString(StringBuilder cc)
        {
            while (!_shouldStop)
            {
                try 
                {
                    if (cc.ToString() != "")
                    {
                        this.Invoke(new InvokeDelegate(dodawanie), new object[] { cc.ToString() });
                        return;
                    }
                    Thread.Sleep(200);
                }
                catch (ThreadAbortException)
                {
                    break;
                }
                catch (Exception ex)
                {
                    // Logowanie błędu
                    break;
                }
            }
        }
        private void ZakonczSkanowanie()
        {
            _shouldStop = true;
            if (_scanningThread != null)
            {
                try
                {
                    // W Windows CE nie ma IsAlive, więc po prostu próbujemy przerwać wątek
                    _scanningThread.Abort();
                    _scanningThread = null;
                }
                catch (ThreadStateException)
                {
                    // Wątek już mógł zostać zakończony
                }
                catch (Exception)
                {
                    // Obsługa innych potencjalnych błędów
                }
            }
            
            // Upewnij się, że skaner jest zatrzymany
            Skaner.Przewij_Skanowanie();
        }

        private void Exit_Click_1(object sender, EventArgs e)
        {
            if (AktualneSztuki_Global.Rows.Count>0)
            {
                zapisz_kontrole();
            }

            string zapyt = "select sum(bb.liczenie_picking) as ilosc from (SELECT s.nazwa as klient,  if(DATE_FORMAT(delivery_ts, '%H') < '14', 1, 2) as zmiana, d.id as nr_dl, docout_type, docout_nr,docout_date, firma, d.dl_doc_ref, d.dl_doc_ref_klient, k.kod,kod_nazwa, k.ilosc_w_opakowaniu,k.ilosc_szt_w_zbiorczym, (sum(de.ilosc_zamawiana) / if(k.ilosc_szt_w_zbiorczym = 0, 1, k.ilosc_szt_w_zbiorczym)) as suma_opak_zbiorczych, sum(de.ilosc_zamawiana) as ilosc_zam, FLOOR(sum(de.ilosc_zamawiana) / if(k.ilosc_w_opakowaniu = 0, 1, k.ilosc_w_opakowaniu)) as suma_opak, TRIM(TRAILING ' . ' FROM TRIM(TRAILING '0' from ( sum(de.ilosc_zamawiana) - (FLOOR(sum(de.ilosc_zamawiana) / if(k.ilosc_w_opakowaniu = 0, 1, k.ilosc_w_opakowaniu))*if(k.ilosc_w_opakowaniu = 0, 1, k.ilosc_w_opakowaniu)) ) )) as suma_sztuk, k.ilosc_szt_palecie, if(sum(de.ilosc_zamawiana)=k.ilosc_szt_palecie,1,0) as ile_palet_pelnych, if(sum(de.ilosc_zamawiana)=k.ilosc_szt_palecie,1,( FLOOR(sum(de.ilosc_zamawiana) / if(k.ilosc_w_opakowaniu = 0, 1, k.ilosc_w_opakowaniu)) + TRIM(TRAILING ' . ' FROM TRIM(TRAILING '0' from ( sum(de.ilosc_zamawiana) - (FLOOR(sum(de.ilosc_zamawiana) / if(k.ilosc_w_opakowaniu = 0, 1, k.ilosc_w_opakowaniu))*if(k.ilosc_w_opakowaniu = 0, 1, k.ilosc_w_opakowaniu)) ) )) )) as liczenie_picking  FROM delivery d left join delivery_et de on d.id = de.delivery_id  left join etykiety e on e.id = de.etykieta_id left join kody k on e.kod_id = k.id  left join systemy s on s.wartosc = d.dl_system_id left join kontrah kk on d.dl_kontrah_id = kk.id  left join docout dout on dout.id=d.dl_docout_id left  join zadania_head zh on zh.doc_id=d.id  left join zadania_dane zd on zh.id=zd.zadanie_head_id and zd.etykieta_id=de.etykieta_id and zd.status=3 where d.id=" + delivery_id_global + "  group by de.id order by s.nazwa,nr_dl) as bb ";
            string ile_picking = (string)BazaDanychExternal.Wyczytaj_Jedna_Wartosc(zapyt);


            BazaDanychExternal.DokonajUpdate("insert into operacje(etykieta_id, doc_type, doc_nr, imie_nazwisko, typ_operacji,system_id,wozek,operac_id,ilosc) values('0','DL','" + delivery_id_global + "','" + Wlasciwosci.imie_nazwisko + "','DL_KON','" + Wlasciwosci.system_id_id + "','" + Wlasciwosci.wozek + "','" + operac_id_global + "','" + ile_picking + "');");


            BazaDanychExternal.DokonajUpdate("update zlecenia_head z set status=10 WHERE z.delivery_id=" + delivery_id_global);
            
            
            ZakonczSkanowanie();
            //BazaDanychExternal.ChangeHost(Wlasciwosci.Varbaza, Wlasciwosci.Varhost, Wlasciwosci.VarLoginMYSQL, Wlasciwosci.VarHasloMYSQL);
            myParent.Show();
            this.Close();
        }


        private void button2_Click(object sender, EventArgs e)
        {


        }

        private void Zakoncz_Skanowanie(object sender, EventArgs e)
        {
            ZakonczSkanowanie();
        }

        private void dodawanie(string gg)
        {
            PerformanceMonitor.StartOperation("Scan");
            try 
            {
                ZakonczSkanowanie();

                if (gg.Substring(0, 2) == "DL")
                {
                    if (delivery_id_global != "")
                    {
                        MessageBox.Show("Nie zakończono obecnej kontroli");
                    }
                    delivery_id_global = gg.Trim(new Char[] { 'D', 'L' });
                    label1.Text = "DL " + delivery_id_global;
                    pokaz_podglad(delivery_id_global);

                    string zapytanie = "SELECT last FROM docnumber d WHERE d.name='operacja_id';";

                    operac_id_global = (string)BazaDanychExternal.Wyczytaj_Jedna_Wartosc(zapytanie);

                    string zapyt = "update docnumber d set last=last+1 WHERE d.name='operacja_id'; ";
                    BazaDanychExternal.Wyczytaj_Jedna_Wartosc(zapyt);
                    BazaDanychExternal.DokonajUpdate("insert into operacje(etykieta_id, doc_type, doc_nr, imie_nazwisko, typ_operacji,system_id,wozek,operac_id,ilosc) values('0','DL','" + delivery_id_global + "','" + Wlasciwosci.imie_nazwisko + "','DL_KON','" + Wlasciwosci.system_id_id + "','" + Wlasciwosci.wozek + "','" + operac_id_global + "','0');");

                
                }
                else
                {
                    if (delivery_id_global != "")
                    {
                        edycja = true;
                        sprawdz_dl(delivery_id_global, gg);
                    }
                    else
                    {
                        MessageBox.Show("Wczytaj DL");
                    }
                }
                ZacznijSkanowanie();
            }
            finally 
            {
                PerformanceMonitor.EndOperation("Scan");
            }
        }




        void refresh_view_table(DataTable AktualneSztuki)
        {
            //MessageBox.Show("refresh_view_table");
            dataGrid1.DataSource = AktualneSztuki;
            dataGrid1.TableStyles.Clear();
            DataGridTableStyle tableStyle = new DataGridTableStyle();
            tableStyle.MappingName = AktualneSztuki.TableName;

            DataGridTextBoxColumn tbcName = new DataGridTextBoxColumn();
            tbcName.Width = 150;
            tbcName.MappingName = AktualneSztuki.Columns[0].ColumnName;
            tbcName.HeaderText = AktualneSztuki.Columns[0].ColumnName;
            tableStyle.GridColumnStyles.Add(tbcName);
            DataGridTextBoxColumn tbcName1 = new DataGridTextBoxColumn();
            tbcName1.Width = 40;
            tbcName1.MappingName = AktualneSztuki.Columns[1].ColumnName;
            tbcName1.HeaderText = AktualneSztuki.Columns[1].ColumnName;
            tableStyle.GridColumnStyles.Add(tbcName1);
            DataGridTextBoxColumn tbcName2 = new DataGridTextBoxColumn();
            tbcName2.Width = 40;
            tbcName2.MappingName = AktualneSztuki.Columns[2].ColumnName;
            tbcName2.HeaderText = AktualneSztuki.Columns[2].ColumnName;
            tableStyle.GridColumnStyles.Add(tbcName2);
            dataGrid1.TableStyles.Add(tableStyle);

            DataGridTextBoxColumn tbcName3 = new DataGridTextBoxColumn();
            tbcName3.Width = 25;
            tbcName3.MappingName = AktualneSztuki.Columns[3].ColumnName;
            tbcName3.HeaderText = AktualneSztuki.Columns[3].ColumnName;
            tableStyle.GridColumnStyles.Add(tbcName3);
            dataGrid1.TableStyles.Add(tableStyle);
            //MessageBox.Show("Koniec refresh_view_table");
        }



        void pokaz_podglad(string delivery_id)
        {
            //MessageBox.Show("pokaz_podglad start");
            AktualneSztuki_Global = (DataTable)BazaDanychExternal.Wyczytaj_Tabele("select aa.*,if(dl=skaner,'OK','-') as wynik from ( SELECT k.kod, cast(TRIM(TRAILING '.' FROM TRIM(TRAILING '0' from sum(de.ilosc_zamawiana))) as char) as dl, ifnull( cast((SELECT TRIM(TRAILING '.' FROM TRIM(TRAILING '0' from sum(dd.ilosc)))   FROM delivery_kontrola dd WHERE dd.delivery_id=d.id and dd.kod=k.kod)  as char),0) as skaner from  delivery d left join delivery_et de on de.delivery_id=d.id left join etykiety e on de.etykieta_id=e.id left join kody k on e.kod_id=k.id WHERE d.id='" + delivery_id + "' and e.kod_id is not null group by e.kod_id order by k.kod ) as aa");
            if (AktualneSztuki_Global.Rows.Count == 0)
            {
                MessageBox.Show("Brak wczytanego towaru.");
                return;
            }
            else 
            {
                //MessageBox.Show("pokaz_podglad refresh");
                refresh_view_table(AktualneSztuki_Global);
            }


            //MessageBox.Show("pokaz_podglad");
            
        }

        void zapisz_kontrole()
        {
            BazaDanychExternal.DokonajUpdate("delete from  delivery_kontrola where delivery_id='" + delivery_id_global + "';");
            
            for (int i = 0; i < AktualneSztuki_Global.Rows.Count; i++)
            {
                BazaDanychExternal.DokonajUpdate("insert into delivery_kontrola(delivery_id, kod, ilosc,pracownik_id,ts) values('" + delivery_id_global + "','" + AktualneSztuki_Global.Rows[i]["kod"].ToString() + "','" + AktualneSztuki_Global.Rows[i]["skaner"].ToString() + "','" + Wlasciwosci.id_Pracownika + "',NOW());");
                
            }
        }


        void sprawdz_dl(string delivery_id, string ean)
        {
            //MessageBox.Show("13");
            label2.Text = "";
            label3.Text = "";
            label4.Text = "";
            ean = ean.TrimStart('0');
            DataTable NoweAktualneSztuki =
            (DataTable)BazaDanychExternal.Wyczytaj_Tabele("SELECT k.kod,k.kod_nazwa, cast(TRIM(TRAILING '.' FROM TRIM(TRAILING '0' from sum(de.ilosc_zamawiana))) as char) as dl, TRIM(LEADING '0' FROM k.ean) as ean , TRIM(LEADING '0' FROM k.ean_jednostki) as ean_jednostki, k.ilosc_w_opakowaniu, TRIM(LEADING '0' FROM k.ean_opakowanie_zbiorcze) as ean_opakowanie_zbiorcze,k.ilosc_szt_w_zbiorczym from  delivery d left join delivery_et de on de.delivery_id=d.id left join etykiety e on de.etykieta_id=e.id left join kody k on e.kod_id=k.id WHERE d.id='" + delivery_id + "' and (TRIM(LEADING '0' FROM k.ean)='" + ean + "' or TRIM(LEADING '0' FROM k.ean_jednostki)='" + ean + "' or TRIM(LEADING '0' FROM k.ean_opakowanie_zbiorcze)='" + ean + "' ) and e.kod_id is not null group by e.kod_id order by k.kod;");
            if (NoweAktualneSztuki.Rows.Count == 0)
            {
                MessageBox.Show("Nie ma EAN " + ean + " w DL " + delivery_id);
                return;
            }
            else if (NoweAktualneSztuki.Rows.Count > 0 ) //NoweAktualneSztuki.Rows.Count == 1
            {
                //MessageBox.Show("14");
                for (int i = 0; i < AktualneSztuki_Global.Rows.Count; i++)
                {
                    //MessageBox.Show(AktualneSztuki_Global.Rows[i]["kod"].ToString() + ";" + NoweAktualneSztuki.Rows[0]["kod"].ToString());
                    if(AktualneSztuki_Global.Rows[i]["kod"].ToString()==NoweAktualneSztuki.Rows[0]["kod"].ToString())
                    {
                        

                            //MessageBox.Show(NoweAktualneSztuki.Rows[0]["ean"] + ";" + ean);
                            if (NoweAktualneSztuki.Rows[0]["ean"].ToString() == ean)
                            {
                                //MessageBox.Show(";" + NoweAktualneSztuki.Rows[0]["ilosc_w_opakowaniu"]);
                                if (Convert.ToInt32(AktualneSztuki_Global.Rows[i]["dl"].ToString()) < Convert.ToInt32(AktualneSztuki_Global.Rows[i]["skaner"].ToString()) + Convert.ToInt32(NoweAktualneSztuki.Rows[0]["ilosc_w_opakowaniu"]))
                                {
                                    MessageBox.Show("Nie można dodać " + NoweAktualneSztuki.Rows[0]["ilosc_w_opakowaniu"] + ". Przekroczy DL");
                                }
                                else
                                {
                                    AktualneSztuki_Global.Rows[i]["skaner"] = Convert.ToInt32(AktualneSztuki_Global.Rows[i]["skaner"]) + Convert.ToInt32(NoweAktualneSztuki.Rows[0]["ilosc_w_opakowaniu"]);
                                }

                                

                            }
                            else if (NoweAktualneSztuki.Rows[0]["ean_opakowanie_zbiorcze"].ToString() == ean)
                            {
                                //MessageBox.Show(";" + NoweAktualneSztuki.Rows[0]["ilosc_w_opakowaniu"]);
                                if (Convert.ToInt32(AktualneSztuki_Global.Rows[i]["dl"].ToString()) < Convert.ToInt32(AktualneSztuki_Global.Rows[i]["skaner"].ToString()) + Convert.ToInt32(NoweAktualneSztuki.Rows[0]["ilosc_szt_w_zbiorczym"]))
                                {
                                    MessageBox.Show("Nie można dodać " + NoweAktualneSztuki.Rows[0]["ilosc_szt_w_zbiorczym"] + ". Przekroczy DL");
                                }
                                else
                                {
                                    AktualneSztuki_Global.Rows[i]["skaner"] = Convert.ToInt32(AktualneSztuki_Global.Rows[i]["skaner"]) + Convert.ToInt32(NoweAktualneSztuki.Rows[0]["ilosc_szt_w_zbiorczym"]);
                                }



                            }
                            else
                            {
                                if (Convert.ToInt32(AktualneSztuki_Global.Rows[i]["dl"].ToString())< Convert.ToInt32(AktualneSztuki_Global.Rows[i]["skaner"].ToString())+1)
                                {
                                    MessageBox.Show("Nie można dodać 1. Przekroczy DL");
                                }
                                else
                                {
                                    AktualneSztuki_Global.Rows[i]["skaner"] = Convert.ToInt32(AktualneSztuki_Global.Rows[i]["skaner"]) + 1;
                                }

                                
                            }

                            label2.Text = NoweAktualneSztuki.Rows[0]["kod"].ToString();
                            label3.Text = AktualneSztuki_Global.Rows[i]["dl"].ToString();
                            label4.Text = AktualneSztuki_Global.Rows[i]["skaner"].ToString();
                            label5.Text = NoweAktualneSztuki.Rows[0]["kod_nazwa"].ToString();
                       
                        
                    }
                    if (AktualneSztuki_Global.Rows[i]["skaner"].ToString() != AktualneSztuki_Global.Rows[i]["dl"].ToString())
                    {
                        AktualneSztuki_Global.Rows[i]["wynik"] = "-";
                    }
                    else
                    {
                        AktualneSztuki_Global.Rows[i]["wynik"] = "OK";
                    }

                }
                refresh_view_table(AktualneSztuki_Global);

            }
        //    else if (NoweAktualneSztuki.Rows.Count > 1)
        //    {
        //        MessageBox.Show("Jeden EAN jest przypisany do kilku kodów, przerywam operację");
        //        return;
        //    }
        }



        private void WczytywanieNaLinie_Load_1(object sender, EventArgs e)
        {
            //ZacznijSkanowanie();
        }

        private void button1_Click(object sender, EventArgs e)
        {
            DialogResult result3 = MessageBox.Show("Czy chcesz usunąć wynik kontroli dla DL "+delivery_id_global+"?",
                                    "Czy usunąć?",
                                    MessageBoxButtons.YesNo,
                                    MessageBoxIcon.Question,
                                    MessageBoxDefaultButton.Button2);
            if (result3 == DialogResult.Yes)
            {
                BazaDanychExternal.DokonajUpdate("delete from  delivery_kontrola where delivery_id='" + delivery_id_global + "';");
                edycja = false;
                pokaz_podglad(delivery_id_global);
            }
        }

        private void button2_Click_1(object sender, EventArgs e)
        {
            akceptacja_manualna();
            refresh_view_table(AktualneSztuki_Global);
            //pokaz_podglad(delivery_id_global);
            edycja = true;
        }

        void akceptacja_manualna()
        {
            for (int i = 0; i < AktualneSztuki_Global.Rows.Count; i++)
            {
                AktualneSztuki_Global.Rows[i]["wynik"] = "OK";
                AktualneSztuki_Global.Rows[i]["skaner"] = AktualneSztuki_Global.Rows[i]["dl"].ToString();

                //BazaDanychExternal.DokonajUpdate("insert into delivery_kontrola(delivery_id, kod, ilosc,pracownik_id,ts) values('" + delivery_id_global + "','" + AktualneSztuki_Global.Rows[i]["kod"].ToString() + "','" + AktualneSztuki_Global.Rows[i]["dl"].ToString() + "','" + Wlasciwosci.id_Pracownika + "',NOW());");

            }
            refresh_view_table(AktualneSztuki_Global);
        }



        private void button3_Click(object sender, EventArgs e)
        {
            ZakonczSkanowanie();
            
            myParent.Show();
            this.Close();
        }

        private void grdOrders_CurrentCellChanged(object sender, EventArgs e)
        {
            if (!inUpdateMode)
            {
                if (inEditMode && !dataGrid1.CurrentCell.Equals(editCell))
                {
                    // Update edited cell
                    inUpdateMode = true;
                    dataGrid1.Visible = false;
                    DataGridCell currentCell = dataGrid1.CurrentCell;
                    dataGrid1[editCell.RowNumber, editCell.ColumnNumber] =
                      txtEdit.Text;
                    dataGrid1.CurrentCell = currentCell;
                    dataGrid1.Visible = true;
                    inUpdateMode = false;
                    txtEdit.Visible = false;
                    inEditMode = false;

                    AktualneSztuki_Global.Rows[editCell.RowNumber][editCell.ColumnNumber] = txtEdit.Text;
                    
                    
                }

                if (dataGrid1.CurrentCell.ColumnNumber == 2)
                {
                    // Enter edit mode
                    editCell = dataGrid1.CurrentCell;
                    txtEdit.Text = (string)dataGrid1[editCell.RowNumber,
                      editCell.ColumnNumber];
                    Rectangle cellPos = dataGrid1.GetCellBounds(editCell.RowNumber,
                      editCell.ColumnNumber);
                    txtEdit.Left = cellPos.Left - 1;
                    txtEdit.Top = cellPos.Top + dataGrid1.Top - 1;
                    txtEdit.Width = cellPos.Width + 2;
                    txtEdit.Height = cellPos.Height + 2;
                    txtEdit.Visible = true;
                    inEditMode = true;
                }
                
                //MessageBox.Show(AktualneSztuki_Global.Rows[editCell.RowNumber][editCell.ColumnNumber].ToString());
                odswiez_tabele();
                refresh_view_table(AktualneSztuki_Global);
            }
        }

        void odswiez_tabele()
        {
            //AktualneSztuki_Global = (DataTable)(dataGrid1.DataSource);
            for (int i = 0; i < AktualneSztuki_Global.Rows.Count; i++)
            {
                if (AktualneSztuki_Global.Rows[i]["skaner"].ToString() != AktualneSztuki_Global.Rows[i]["dl"].ToString())
                {
                    AktualneSztuki_Global.Rows[i]["wynik"] = "-";
                }
                else
                {
                    AktualneSztuki_Global.Rows[i]["wynik"] = "OK";
                }
            }
        }
        









    }
}
public class CacheItem
{
    public object Data { get; private set; }
    public DateTime ExpirationTime { get; private set; }

    public CacheItem(object data, TimeSpan duration)
    {
        Data = data;
        ExpirationTime = DateTime.Now.Add(duration);
    }

    public bool IsExpired()
    {
        return DateTime.Now > ExpirationTime;
    }
}

public class SimpleCache
{
    private static readonly Dictionary<string, CacheItem> _cache = 
        new Dictionary<string, CacheItem>();

    public static object Get(string key)
    {
        if (_cache.ContainsKey(key))
        {
            var item = _cache[key];
            if (!item.IsExpired())
                return item.Data;
            
            _cache.Remove(key);
        }
        return null;
    }

    public static void Add(string key, object data, TimeSpan duration)
    {
        _cache[key] = new CacheItem(data, duration);
    }

    public static void Clear()
    {
        _cache.Clear();
    }
}
public class DatabaseHelper
{
    private static readonly Dictionary<string, string> _queryCache = 
        new Dictionary<string, string>();

    public static DataTable ExecuteQuery(string sql, Dictionary<string, object> parameters)
    {
        using (MySqlConnection conn = new MySqlConnection(BazaDanychExternal.polaczenie_mysql))
        {
            using (MySqlCommand cmd = new MySqlCommand(sql, conn))
            {
                if (parameters != null)
                {
                    foreach (var param in parameters)
                    {
                        cmd.Parameters.AddWithValue(param.Key, param.Value ?? DBNull.Value);
                    }
                }

                DataTable dt = new DataTable();
                try
                {
                    conn.Open();
                    using (MySqlDataAdapter da = new MySqlDataAdapter(cmd))
                    {
                        da.Fill(dt);
                    }
                }
                finally
                {
                    if (conn.State == ConnectionState.Open)
                        conn.Close();
                }
                return dt;
            }
        }
    }
}
public static class PerformanceMonitor
{
    private static readonly Dictionary<string, DateTime> _operations = 
        new Dictionary<string, DateTime>();
        
    private static string _lastPerformanceResult = string.Empty;
    
    public static string LastResult 
    { 
        get { return _lastPerformanceResult; } 
    }

    public static void StartOperation(string name)
    {
        _operations[name] = DateTime.Now;
    }

    public static TimeSpan EndOperation(string name)
    {
        if (_operations.ContainsKey(name))
        {
            TimeSpan duration = DateTime.Now - _operations[name];
            _operations.Remove(name);
            
            _lastPerformanceResult = string.Format("{0} took {1} ms", 
                name, duration.TotalMilliseconds);
                
            return duration;
        }
        return TimeSpan.Zero;
    }
    
    public static void ClearLastResult()
    {
        _lastPerformanceResult = string.Empty;
    }
}

/*private void SomeMethod()
{
    PerformanceMonitor.StartOperation("NazwaOperacji");
    
    // Twój kod do zmierzenia
    
    PerformanceMonitor.EndOperation("NazwaOperacji");
    
    // Jeśli chcesz zobaczyć wynik, możesz użyć:
    label6.Text = PerformanceMonitor.LastResult;
    // Możesz np. wyświetlić wynik w MessageBox lub zapisać go gdzieś
    if (!string.IsNullOrEmpty(wynik))
    {
        MessageBox.Show(wynik);
    }
}*/
