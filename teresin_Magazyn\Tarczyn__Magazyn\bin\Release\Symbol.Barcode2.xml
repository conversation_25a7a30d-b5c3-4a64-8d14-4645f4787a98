<?xml version="1.0"?>
<doc>
    <assembly>
        <name>Symbol.Barcode2</name>
    </assembly>
    <members>
        <member name="T:Symbol.Barcode2.Version">
            <summary>
            Barcode reader version class.
            </summary>
        </member>
        <member name="M:Symbol.Barcode2.Version.#ctor(Symbol.Barcode2.PInvokes)">
            <summary>
            Version class constructor  
            </summary>
        </member>
        <member name="P:Symbol.Barcode2.Version.PDD">
            <summary>
            Version of the scanner driver PDD.  
            </summary>
        </member>
        <member name="P:Symbol.Barcode2.Version.MDD">
            <summary>
            Version of the scanner driver MDD.  
            </summary>
        </member>
        <member name="P:Symbol.Barcode2.Version.Hardware">
            <summary>
            Version of scanner hardware if applicable  
            </summary>
        </member>
        <member name="P:Symbol.Barcode2.Version.Decoder">
            <summary>
            Version of decoder software if applicable  
            </summary>
        </member>
        <member name="P:Symbol.Barcode2.Version.CAPI">
            <summary>
            Version of the scanner native API.  
            </summary>
        </member>
        <member name="P:Symbol.Barcode2.Version.Assembly">
            <summary>
            Version of the Assembly.  
            </summary>
        </member>
        <member name="P:Symbol.Barcode2.Version.DecoderBuild">
            <summary>
            Additional build version information about the decoder module.
            </summary>
        </member>
        <member name="T:Symbol.Barcode2.RemoteScanner">
            <summary>
            The class for the RemoteScanner API. It provides the list of RSM attributes. 
            An object of this class gets created by the Barcode object automatically. 
            Application should not attempt to instantiate an object of this class. 
            NOTE: The RSM feature can only be used when using remote scanner devices such as RS507. 
            </summary>
        </member>
        <member name="M:Symbol.Barcode2.RemoteScanner.DoRemoteFeedback">
             <summary>
             Provides Beep and/or LED feedback on the remote scanner. 
             </summary>
             <remarks>
             This feature is available only on remote scanners with feedback capabilites like RS507. To determine if this feature is available check the <see cref="P:Symbol.Barcode2.DeviceInfo.IsFeedbackSupported"/> property in the Barcode2.DeviceInfo object. 
            </remarks>
        </member>
        <member name="P:Symbol.Barcode2.RemoteScanner.Config">
            <summary>
            The Config class for the RSM (Remote Scanner Management). It provides access to list of RSM config parameters. Application should not attempt to instantiate an object of this class. NOTE: The RSM feature can only be used when using remote scanner devices such as RS507. 
            </summary>
        </member>
        <member name="T:Symbol.Barcode2.RemoteScanner.RemoteScannerConfig">
            <summary>
            The RemoteScannerConfig class for the RSM (Remote Scanner Management). It provides access to list of RSM config parameters. Application should not attempt to instantiate an object of this class. NOTE: The RSM feature can only be used when using remote scanner devices such as RS507. 
            </summary>
        </member>
        <member name="M:Symbol.Barcode2.RemoteScanner.RemoteScannerConfig.#ctor(System.IntPtr,System.Resources.ResourceManager)">
            <summary>
            The constructor.
            </summary>
        </member>
        <member name="P:Symbol.Barcode2.RemoteScanner.RemoteScannerConfig.RemoteFeedback">
            <summary>
            The RemoteFeedback property provides access to feedback parameters. 
            </summary>
        </member>
        <member name="P:Symbol.Barcode2.RemoteScanner.RemoteScannerConfig.SupportedAttribs">
            <summary>
            Provides a list of supported  attributes.
            </summary>
        </member>
        <member name="P:Symbol.Barcode2.RemoteScanner.RemoteScannerConfig.ModelNumber">
            <summary>
            Provides access to model number. The maximum length of the attribute value is 18.
            </summary>
        </member>
        <member name="P:Symbol.Barcode2.RemoteScanner.RemoteScannerConfig.SerialNumber">
            <summary>
            Provides access to serial number. The maximum length of the attribute value is 16.
            NOTE: Only current value is relevant for this attribute.
            </summary>
        </member>
        <member name="P:Symbol.Barcode2.RemoteScanner.RemoteScannerConfig.DateofManufacture">
            <summary>
            Provides access to date of scanner device manufacture. The maximum length of the attribute value is 7.
            NOTE: Only current value is relevant for this attribute.
            </summary>
        </member>
        <member name="P:Symbol.Barcode2.RemoteScanner.RemoteScannerConfig.DateofService">
            <summary>
            Provides access to date of last repair done at a Motorola authorized repair facility. The maximum length of the attribute value is 7.
            </summary>
        </member>
        <member name="P:Symbol.Barcode2.RemoteScanner.RemoteScannerConfig.BluetoothAddress">
            <summary>
            Provides access to unique Bluetooth address. The length of the byte array value of the attribute is 6 bytes.
            NOTE: Only current value is relevant for this attribute.
            </summary>
        </member>
        <member name="P:Symbol.Barcode2.RemoteScanner.RemoteScannerConfig.BluetoothAuthentication">
            <summary>
            Provides access to the status of Bluetooth Authetication.
            </summary>
        </member>
        <member name="P:Symbol.Barcode2.RemoteScanner.RemoteScannerConfig.BluetoothEncryption">
            <summary>
            Provides access to the status of Bluetooth encryption.
            </summary>
        </member>
        <member name="P:Symbol.Barcode2.RemoteScanner.RemoteScannerConfig.BluetoothPINCode">
            <summary>
            Provides access to the PIN code of the Bluetooth device when Bluetooth authentication is enabled.  
            The PIN code is permanently saved. The maximum length of the attribute value is 5.
            The default PIN stored in memory is “12345”.
            </summary>
        </member>
        <member name="P:Symbol.Barcode2.RemoteScanner.RemoteScannerConfig.ReconnectAttempts">
            <summary>
            Provides access to the reconnect attempts parameter of the scanner device.  
            </summary>
        </member>
        <member name="P:Symbol.Barcode2.RemoteScanner.RemoteScannerConfig.BeeponReconnectAttempt">
            <summary>
            Provides access to the beeps when reconnect attempts is made.  
            </summary>
        </member>
        <member name="P:Symbol.Barcode2.RemoteScanner.RemoteScannerConfig.HIDAutoReconnect">
            <summary>
            Provides access to the HID reconnection parameter.  
            </summary>
        </member>
        <member name="P:Symbol.Barcode2.RemoteScanner.RemoteScannerConfig.BluetoothFriendlyName">
            <summary>
            Provides access to the friendly name of the Bluetooth device. The maximum length of the attribute value is 23.
            NOTE: Only current value is relevant for this attribute.
            </summary>
        </member>
        <member name="P:Symbol.Barcode2.RemoteScanner.RemoteScannerConfig.PINCodeType">
            <summary>
            Provides access to the type of PIN code to use for the Bluetooth device. This attribute is not saved permanently on the Bluetooth device.
            </summary>
        </member>
        <member name="P:Symbol.Barcode2.RemoteScanner.RemoteScannerConfig.BluetoothInquiryMode">
            <summary>
            Provides access to the inquiry mode of the Bluetooth device.  
            </summary>
        </member>
        <member name="P:Symbol.Barcode2.RemoteScanner.RemoteScannerConfig.MemsEnable">
            <summary>
            Provides access to the mems feature.  
            </summary>
        </member>
        <member name="P:Symbol.Barcode2.RemoteScanner.RemoteScannerConfig.ProximityEnable">
            <summary>
            Provides access to the proximity feature.  
            </summary>
        </member>
        <member name="P:Symbol.Barcode2.RemoteScanner.RemoteScannerConfig.ProximityDistance">
            <summary>
            Provides access to the proximity distance.  
            </summary>
        </member>
        <member name="P:Symbol.Barcode2.RemoteScanner.RemoteScannerConfig.PagingEnable">
            <summary>
            Provides access to the paging feature.  
            </summary>
        </member>
        <member name="P:Symbol.Barcode2.RemoteScanner.RemoteScannerConfig.PagingBeepSequence">
            <summary>
            Provides access to the paging beep sequence.  
            </summary>
        </member>
        <member name="P:Symbol.Barcode2.RemoteScanner.RemoteScannerConfig.LowBatteryIndicationEnable">
            <summary>
            Provides access to the low battery indicator.  
            </summary>
        </member>
        <member name="P:Symbol.Barcode2.RemoteScanner.RemoteScannerConfig.ScantriggerWakeupEnable">
            <summary>
            Provides access to the trigger wakeup feature.  
            </summary>
        </member>
        <member name="P:Symbol.Barcode2.RemoteScanner.RemoteScannerConfig.BluetoothAutoReconnect">
            <summary>
            Provides access to the automatic reconnect feature of the Bluetooth device.
            </summary>
        </member>
        <member name="P:Symbol.Barcode2.RemoteScanner.RemoteScannerConfig.LowBatteryIndicationCycle">
            <summary>
            Provides access to the low battery indication cycle.
            </summary>
        </member>
        <member name="P:Symbol.Barcode2.RemoteScanner.RemoteScannerConfig.PreferredWirelessHost">
            <summary>
            Provides access to the preferred Bluetoothwireless host.
            NOTE: PreferredWirelessHost attribute is not supported. Therefore, this property and its corresponding class "PreferredWirelessHost" will be deprecated in future releases.
            </summary>
        </member>
        <member name="P:Symbol.Barcode2.RemoteScanner.RemoteScannerConfig.PagingActivate">
            <summary>
            Provides access to the paging activation command.
            </summary>
        </member>
        <member name="P:Symbol.Barcode2.RemoteScanner.RemoteScannerConfig.FirmwareVersion">
            <summary>
            Provides access to the firmware version. The maximum length of the attribute value is 18.
            NOTE: Only current value is relevant for this attribute.
            </summary>
        </member>
        <member name="P:Symbol.Barcode2.RemoteScanner.RemoteScannerConfig.DeviceClass">
            <summary>
            Provides access to the device class of the system. The maximum length of the attribute value is 18.
            </summary>
        </member>
        <member name="P:Symbol.Barcode2.RemoteScanner.RemoteScannerConfig.BatteryStatus">
            <summary>
            Provides access to the battery status.
            </summary>
        </member>
        <member name="P:Symbol.Barcode2.RemoteScanner.RemoteScannerConfig.BatteryCapacity">
            <summary>
            Provides access to the battery capacity.
            </summary>
        </member>
        <member name="P:Symbol.Barcode2.RemoteScanner.RemoteScannerConfig.BatteryID">
            <summary>
            Provides access to the battery ID.
            </summary>
        </member>
        <member name="P:Symbol.Barcode2.RemoteScanner.RemoteScannerConfig.HardwareVersion">
            <summary>
            Provides access to the hardware version.
            NOTE: HardwareVersion attribute will not be supported in the future. Therefore, this property and its corresponding class "HardwareVersion" will be deprecated in future releases.
            </summary>
        </member>
        <member name="P:Symbol.Barcode2.RemoteScanner.RemoteScannerConfig.ScanlineWidth">
            <summary>
            Provides access to the pick list mode.
            </summary>
        </member>
        <member name="P:Symbol.Barcode2.RemoteScanner.RemoteScannerConfig.BluetoothDisconnect">
            <summary>
            Provides access to the Bluetooth disconnect command.
            </summary>
        </member>
        <member name="P:Symbol.Barcode2.RemoteScanner.RemoteScannerConfig.BluetoothUnPair">
            <summary>
            Provides access to the Bluetooth unpair command.
            </summary>
        </member>
        <member name="P:Symbol.Barcode2.RemoteScanner.RemoteScannerConfig.GoodScansDelay">
            <summary>
            Provides access to the delay between proximity continuous good scans.
            </summary>
        </member>
        <member name="P:Symbol.Barcode2.RemoteScanner.RemoteScannerConfig.ProximityContinuousEnable">
            <summary>
            Provides access to the proximity continuous feature.
            </summary>
        </member>
        <member name="P:Symbol.Barcode2.RemoteScanner.RemoteScannerConfig.ExclusiveCode128Enable">
            <summary>
            Provides access to the feature for ignoring Code 128’s beginning with 420 and 421
            </summary>
        </member>
        <member name="P:Symbol.Barcode2.RemoteScanner.RemoteScannerConfig.DisableGoodDecodeLEDBeep">
            <summary>
            Provides access to disable the good decode Green LED and beep on scanner
            </summary>
        </member>
        <member name="P:Symbol.Barcode2.RemoteScanner.RemoteScannerConfig.ForcePairingSave">
            <summary>
            Provides access to the feature to save the Bluetooth address when pairing fails. This attribute is permanently saved.
            </summary>
        </member>
        <member name="T:Symbol.Barcode2.RemoteScanner.RemoteScannerConfig.BatteryCapacityClass">
            <summary>
            Provides access to the battery capacity.
            </summary>
        </member>
        <member name="T:Symbol.Barcode2.RemoteScanner.RemoteScannerConfig.RSMAttrib">
            <summary>
            The base class for providing access to the RSM (Remote Scanner Management) attributes. 
            </summary>
        </member>
        <member name="M:Symbol.Barcode2.RemoteScanner.RemoteScannerConfig.RSMAttrib.#ctor">
            <summary>
            The constructor.
            </summary>
        </member>
        <member name="P:Symbol.Barcode2.RemoteScanner.RemoteScannerConfig.RSMAttrib.IsSupported">
            <summary>
            Returns whether or not the RSM attribute is supported by the scanner device.
            </summary>
        </member>
        <member name="P:Symbol.Barcode2.RemoteScanner.RemoteScannerConfig.RSMAttrib.AttribNumber">
            <summary>
            Returns the unique number of the RSM attribute.
            </summary>
        </member>
        <member name="P:Symbol.Barcode2.RemoteScanner.RemoteScannerConfig.RSMAttrib.AttribType">
            <summary>
            Returns the type of the RSM attribute.
            </summary>
        </member>
        <member name="P:Symbol.Barcode2.RemoteScanner.RemoteScannerConfig.BatteryCapacityClass.CurrentValue">
            <summary>
            Provides the current value of the attribute.
            Capacity of the Battery:
            0-100 (in percent)
            9999 if error (i.e. try to get capacity when no battery)
            </summary>
        </member>
        <member name="P:Symbol.Barcode2.RemoteScanner.RemoteScannerConfig.BatteryCapacityClass.FactoryDefaultValue">
            <summary>
            Provides the factory default value of the attribute.
            </summary>
        </member>
        <member name="P:Symbol.Barcode2.RemoteScanner.RemoteScannerConfig.BatteryCapacityClass.CustomDefaultValue">
            <summary>
            Provides the custom default value of the attribute.
            </summary>
        </member>
        <member name="T:Symbol.Barcode2.RemoteScanner.RemoteScannerConfig.BatteryIDClass">
            <summary>
            Provides access to the battery ID.
            </summary>
        </member>
        <member name="P:Symbol.Barcode2.RemoteScanner.RemoteScannerConfig.BatteryIDClass.CurrentValue">
            <summary>
            Provides the current value of the attribute.
            Battery ID:
            0 – Simple
            1 – Double
            2 – Cabled
            9999 – Error
            </summary>
        </member>
        <member name="P:Symbol.Barcode2.RemoteScanner.RemoteScannerConfig.BatteryIDClass.FactoryDefaultValue">
            <summary>
            Provides the factory default value of the attribute.
            </summary>
        </member>
        <member name="P:Symbol.Barcode2.RemoteScanner.RemoteScannerConfig.BatteryIDClass.CustomDefaultValue">
            <summary>
            Provides the custom default value of the attribute.
            </summary>
        </member>
        <member name="T:Symbol.Barcode2.RemoteScanner.RemoteScannerConfig.BatteryStatusClass">
            <summary>
            Provides access to the battery status.
            </summary>
        </member>
        <member name="P:Symbol.Barcode2.RemoteScanner.RemoteScannerConfig.BatteryStatusClass.CurrentValue">
            <summary>
            Provides the current value of the attribute.
            Indicates the status of Battery in the device:
            0- unknown
            1- Full
            2- Medium
            3- Empty
            4- Charging – full rate
            5- Charging – half rate
            6- Charging – Trickle
            7- Discharging – Battery Cycle in progress
            </summary>
        </member>
        <member name="P:Symbol.Barcode2.RemoteScanner.RemoteScannerConfig.BatteryStatusClass.FactoryDefaultValue">
            <summary>
            Provides the factory default value of the attribute.
            </summary>
        </member>
        <member name="P:Symbol.Barcode2.RemoteScanner.RemoteScannerConfig.BatteryStatusClass.CustomDefaultValue">
            <summary>
            Provides the custom default value of the attribute.
            </summary>
        </member>
        <member name="T:Symbol.Barcode2.RemoteScanner.RemoteScannerConfig.BeeponReconnectAttemptClass">
            <summary>
            Provides access to the beeps when reconnect attempts is made.  
            </summary>
        </member>
        <member name="P:Symbol.Barcode2.RemoteScanner.RemoteScannerConfig.BeeponReconnectAttemptClass.CurrentValue">
            <summary>
            Provides the current value of the attribute.
            When this attribute is enabled, the scanner will emit 5 short high beeps every 5 seconds 
            while the reconnection attempt is in progress.
            </summary>
        </member>
        <member name="P:Symbol.Barcode2.RemoteScanner.RemoteScannerConfig.BeeponReconnectAttemptClass.FactoryDefaultValue">
            <summary>
            Provides the factory default value of the attribute.
            </summary>
        </member>
        <member name="P:Symbol.Barcode2.RemoteScanner.RemoteScannerConfig.BeeponReconnectAttemptClass.CustomDefaultValue">
            <summary>
            Provides the custom default value of the attribute.
            </summary>
        </member>
        <member name="T:Symbol.Barcode2.RemoteScanner.RemoteScannerConfig.BluetoothAddressClass">
            <summary>
            Provides access to unique Bluetooth address. The length of the byte array value of the attribute is 6 bytes.
            NOTE: Only current value is relevant for this attribute.
            </summary>
        </member>
        <member name="P:Symbol.Barcode2.RemoteScanner.RemoteScannerConfig.BluetoothAddressClass.CurrentValue">
            <summary>
            Provides the current value of the attribute.
            </summary>
        </member>
        <member name="P:Symbol.Barcode2.RemoteScanner.RemoteScannerConfig.BluetoothAddressClass.FactoryDefaultValue">
            <summary>
            Provides the factory default value of the attribute.
            </summary>
        </member>
        <member name="P:Symbol.Barcode2.RemoteScanner.RemoteScannerConfig.BluetoothAddressClass.CustomDefaultValue">
            <summary>
            Provides the custom default value of the attribute.
            </summary>
        </member>
        <member name="P:Symbol.Barcode2.RemoteScanner.RemoteScannerConfig.BluetoothAddressClass.RawCurrentValue">
            <summary>
            Provides the raw (as byte array) current value of the attribute as received from the driver.  The length of the byte array is 6 bytes.
            </summary>
        </member>
        <member name="P:Symbol.Barcode2.RemoteScanner.RemoteScannerConfig.BluetoothAddressClass.RawFactoryDefaultValue">
            <summary>
            Provides the raw (as byte array) factory default value of the attribute as received from the driver. The length of the byte array is 6 bytes.
            </summary>
        </member>
        <member name="P:Symbol.Barcode2.RemoteScanner.RemoteScannerConfig.BluetoothAddressClass.RawCustomDefaultValue">
            <summary>
            Provides the raw (as byte array) custom default value of the attribute as received from the driver. The length of the byte array is 6 bytes.
            </summary>
        </member>
        <member name="P:Symbol.Barcode2.RemoteScanner.RemoteScannerConfig.BluetoothAddressClass.AttribSubType">
            <summary>
            Returns the subtype of the attribute.
            </summary>
        </member>
        <member name="T:Symbol.Barcode2.RemoteScanner.RemoteScannerConfig.BluetoothAuthenticationClass">
            <summary>
            Provides access to the status of Bluetooth Authetication.
            </summary>
        </member>
        <member name="P:Symbol.Barcode2.RemoteScanner.RemoteScannerConfig.BluetoothAuthenticationClass.CurrentValue">
            <summary>
            Provides the current value of the attribute. 
            True if the Bluetooth authentication is required.
            </summary>
        </member>
        <member name="P:Symbol.Barcode2.RemoteScanner.RemoteScannerConfig.BluetoothAuthenticationClass.FactoryDefaultValue">
            <summary>
            Provides the factory default value of the attribute.
            </summary>        
        </member>
        <member name="P:Symbol.Barcode2.RemoteScanner.RemoteScannerConfig.BluetoothAuthenticationClass.CustomDefaultValue">
            <summary>
            Provides the custom default value of the attribute.
            </summary>
        </member>
        <member name="T:Symbol.Barcode2.RemoteScanner.RemoteScannerConfig.BluetoothAutoReconnectClass">
            <summary>
            Provides access to the automatic reconnect feature of the Bluetooth device.
            </summary>
        </member>
        <member name="P:Symbol.Barcode2.RemoteScanner.RemoteScannerConfig.BluetoothAutoReconnectClass.CurrentValue">
            <summary>
            Provides the current value of the attribute.
            Defines Bluetooth reconnection scheme:
            0 –None
            1 - On Power
            2 - On Out Of Range
            3 - On Power Out Of Range
            </summary>
        </member>
        <member name="P:Symbol.Barcode2.RemoteScanner.RemoteScannerConfig.BluetoothAutoReconnectClass.FactoryDefaultValue">
            <summary>
            Provides the factory default value of the attribute.
            </summary>
        </member>
        <member name="P:Symbol.Barcode2.RemoteScanner.RemoteScannerConfig.BluetoothAutoReconnectClass.CustomDefaultValue">
            <summary>
            Provides the custom default value of the attribute.
            </summary>
        </member>
        <member name="T:Symbol.Barcode2.RemoteScanner.RemoteScannerConfig.BluetoothDisconnectClass">
            <summary>
            Provides access to the Bluetooth disconnect command 
            </summary>
        </member>
        <member name="P:Symbol.Barcode2.RemoteScanner.RemoteScannerConfig.BluetoothDisconnectClass.CurrentValue">
            <summary>
            Command the scanner to disconnect from mobile computer.
            0 - disconnect
            NOTE: Since this property is used for issuing a command, this is a set-only property.
            </summary>
        </member>
        <member name="T:Symbol.Barcode2.RemoteScanner.RemoteScannerConfig.BluetoothEncryptionClass">
            <summary>
            Provides access to the status of Bluetooth encryption.
            </summary>
        </member>
        <member name="P:Symbol.Barcode2.RemoteScanner.RemoteScannerConfig.BluetoothEncryptionClass.CurrentValue">
            <summary>
            Provides the current value of the attribute.
            True if the encryption over Bluetooth is required.
            </summary>
        </member>
        <member name="P:Symbol.Barcode2.RemoteScanner.RemoteScannerConfig.BluetoothEncryptionClass.FactoryDefaultValue">
            <summary>
            Provides the factory default value of the attribute.
            </summary>
        </member>
        <member name="P:Symbol.Barcode2.RemoteScanner.RemoteScannerConfig.BluetoothEncryptionClass.CustomDefaultValue">
            <summary>
            Provides the custom default value of the attribute.
            </summary>
        </member>
        <member name="T:Symbol.Barcode2.RemoteScanner.RemoteScannerConfig.BluetoothFriendlyNameClass">
            <summary>
            Provides access to the friendly name of the Bluetooth device. The maximum length of the attribute value is 23.
            NOTE: Only current value is relevant for this attribute.
            </summary>
        </member>
        <member name="P:Symbol.Barcode2.RemoteScanner.RemoteScannerConfig.BluetoothFriendlyNameClass.CurrentValue">
            <summary>
            Provides the current value of the attribute.
            Friendly name displayed by Bluetooth remote devices (Permanently saved).
            </summary>
        </member>
        <member name="P:Symbol.Barcode2.RemoteScanner.RemoteScannerConfig.BluetoothFriendlyNameClass.FactoryDefaultValue">
            <summary>
            Provides the factory default value of the attribute.
            </summary>
        </member>
        <member name="P:Symbol.Barcode2.RemoteScanner.RemoteScannerConfig.BluetoothFriendlyNameClass.CustomDefaultValue">
            <summary>
            Provides the custom default value of the attribute.
            </summary>
        </member>
        <member name="T:Symbol.Barcode2.RemoteScanner.RemoteScannerConfig.BluetoothInquiryModeClass">
            <summary>
            Provides access to the inquiry mode of the Bluetooth device.  
            </summary>
        </member>
        <member name="P:Symbol.Barcode2.RemoteScanner.RemoteScannerConfig.BluetoothInquiryModeClass.CurrentValue">
            <summary>
            Provides the current value of the attribute.
            True for using the limited Inquiry mode
            False for using the general inquiry mode
            </summary>
        </member>
        <member name="P:Symbol.Barcode2.RemoteScanner.RemoteScannerConfig.BluetoothInquiryModeClass.FactoryDefaultValue">
            <summary>
            Provides the factory default value of the attribute.
            </summary>
        </member>
        <member name="P:Symbol.Barcode2.RemoteScanner.RemoteScannerConfig.BluetoothInquiryModeClass.CustomDefaultValue">
            <summary>
            Provides the custom default value of the attribute.
            </summary>
        </member>
        <member name="T:Symbol.Barcode2.RemoteScanner.RemoteScannerConfig.BluetoothPINCodeClass">
            <summary>
            Provides access to the PIN code of the Bluetooth device when Bluetooth authentication is enabled.  
            The PIN code is permanently saved. The maximum length of the attribute value is 5.
            The default PIN stored in memory is “12345”.
            </summary>
        </member>
        <member name="P:Symbol.Barcode2.RemoteScanner.RemoteScannerConfig.BluetoothPINCodeClass.CurrentValue">
            <summary>
            Provides the current value of the attribute.
            Pincode used when the Bluetooth Authentication is enabled (Permanently saved).
            </summary>
        </member>
        <member name="P:Symbol.Barcode2.RemoteScanner.RemoteScannerConfig.BluetoothPINCodeClass.FactoryDefaultValue">
            <summary>
            Provides the factory default value of the attribute.
            </summary>
        </member>
        <member name="P:Symbol.Barcode2.RemoteScanner.RemoteScannerConfig.BluetoothPINCodeClass.CustomDefaultValue">
            <summary>
            Provides the custom default value of the attribute.
            </summary>
        </member>
        <member name="T:Symbol.Barcode2.RemoteScanner.RemoteScannerConfig.BluetoothUnPairClass">
            <summary>
            Provides access to the Bluetooth unpair command 
            </summary>
        </member>
        <member name="P:Symbol.Barcode2.RemoteScanner.RemoteScannerConfig.BluetoothUnPairClass.CurrentValue">
            <summary>
            Commands scanner to unpair from mobile computer
            0 - unpair
            NOTE: Since this property is used for issuing a command, this is a set-only property.
            </summary>
        </member>
        <member name="T:Symbol.Barcode2.RemoteScanner.RemoteScannerConfig.DateofManufactureClass">
            <summary>
            Provides access to date of scanner device manufacture. The maximum length of the attribute value is 7.
            NOTE: Only current value is relevant for this attribute.
            </summary>
        </member>
        <member name="P:Symbol.Barcode2.RemoteScanner.RemoteScannerConfig.DateofManufactureClass.CurrentValue">
            <summary>
            Provides the current value of the attribute.
            </summary>
        </member>
        <member name="P:Symbol.Barcode2.RemoteScanner.RemoteScannerConfig.DateofManufactureClass.FactoryDefaultValue">
            <summary>
            Provides the factory default value of the attribute.
            </summary>
        </member>
        <member name="P:Symbol.Barcode2.RemoteScanner.RemoteScannerConfig.DateofManufactureClass.CustomDefaultValue">
            <summary>
            Provides the custom default value of the attribute.
            </summary>
        </member>
        <member name="T:Symbol.Barcode2.RemoteScanner.RemoteScannerConfig.DateofServiceClass">
            <summary>
            Provides access to date of last repair done at a Motorola authorized repair facility. The maximum length of the attribute value is 7.
            </summary>
        </member>
        <member name="P:Symbol.Barcode2.RemoteScanner.RemoteScannerConfig.DateofServiceClass.CurrentValue">
            <summary>
            Provides the current value of the attribute.
            </summary>
        </member>
        <member name="P:Symbol.Barcode2.RemoteScanner.RemoteScannerConfig.DateofServiceClass.FactoryDefaultValue">
            <summary>
            Provides the factory default value of the attribute.
            </summary>
        </member>
        <member name="P:Symbol.Barcode2.RemoteScanner.RemoteScannerConfig.DateofServiceClass.CustomDefaultValue">
            <summary>
            Provides the custom default value of the attribute.
            </summary>
        </member>
        <member name="T:Symbol.Barcode2.RemoteScanner.RemoteScannerConfig.DeviceClassClass">
            <summary>
            Provides access to the device class of the system. The maximum length of the attribute value is 18.
            </summary>
        </member>
        <member name="P:Symbol.Barcode2.RemoteScanner.RemoteScannerConfig.DeviceClassClass.CurrentValue">
            <summary>
            Provides the current value of the attribute.
            The device class of the system.
            </summary>
        </member>
        <member name="P:Symbol.Barcode2.RemoteScanner.RemoteScannerConfig.DeviceClassClass.FactoryDefaultValue">
            <summary>
            Provides the factory default value of the attribute.
            </summary>
        </member>
        <member name="P:Symbol.Barcode2.RemoteScanner.RemoteScannerConfig.DeviceClassClass.CustomDefaultValue">
            <summary>
            Provides the custom default value of the attribute.
            </summary>
        </member>
        <member name="T:Symbol.Barcode2.RemoteScanner.RemoteScannerConfig.DisableGoodDecodeLEDBeepClass">
            <summary>
            Provides access to disable the good decode Green LED and beep on scanner
            </summary>
        </member>
        <member name="P:Symbol.Barcode2.RemoteScanner.RemoteScannerConfig.DisableGoodDecodeLEDBeepClass.CurrentValue">
            <summary>
            Provides the current value of the attribute.
            True  - Disables the good decode Green LED and beep on scanner
            False - Enables the good decode Green LED and beep on scanner
            </summary>
        </member>
        <member name="P:Symbol.Barcode2.RemoteScanner.RemoteScannerConfig.DisableGoodDecodeLEDBeepClass.FactoryDefaultValue">
            <summary>
            Provides the factory default value of the attribute.
            </summary>
        </member>
        <member name="P:Symbol.Barcode2.RemoteScanner.RemoteScannerConfig.DisableGoodDecodeLEDBeepClass.CustomDefaultValue">
            <summary>
            Provides the custom default value of the attribute.
            </summary>
        </member>
        <member name="T:Symbol.Barcode2.RemoteScanner.RemoteScannerConfig.ExclusiveCode128EnableClass">
            <summary>
            Provides access to the feature for ignoring Code 128’s beginning with 420 and 421
            </summary>
        </member>
        <member name="P:Symbol.Barcode2.RemoteScanner.RemoteScannerConfig.ExclusiveCode128EnableClass.CurrentValue">
            <summary>
            Provides the current value of the attribute.
            Enable/Disable the feature for ignoring Code 128’s beginning with 420 and 421
            </summary>
        </member>
        <member name="P:Symbol.Barcode2.RemoteScanner.RemoteScannerConfig.ExclusiveCode128EnableClass.FactoryDefaultValue">
            <summary>
            Provides the factory default value of the attribute.
            </summary>
        </member>
        <member name="P:Symbol.Barcode2.RemoteScanner.RemoteScannerConfig.ExclusiveCode128EnableClass.CustomDefaultValue">
            <summary>
            Provides the custom default value of the attribute.
            </summary>
        </member>
        <member name="T:Symbol.Barcode2.RemoteScanner.RemoteScannerConfig.FirmwareVersionClass">
            <summary>
            Provides access to the firmware version. The maximum length of the attribute value is 18.
            NOTE: Only current value is relevant for this attribute.
            </summary>
        </member>
        <member name="P:Symbol.Barcode2.RemoteScanner.RemoteScannerConfig.FirmwareVersionClass.CurrentValue">
            <summary>
            Provides the current value of the attribute.
            Scanner’s operating system version.
            </summary>
        </member>
        <member name="P:Symbol.Barcode2.RemoteScanner.RemoteScannerConfig.FirmwareVersionClass.FactoryDefaultValue">
            <summary>
            Provides the factory default value of the attribute.
            </summary>
        </member>
        <member name="P:Symbol.Barcode2.RemoteScanner.RemoteScannerConfig.FirmwareVersionClass.CustomDefaultValue">
            <summary>
            Provides the custom default value of the attribute.
            </summary>
        </member>
        <member name="T:Symbol.Barcode2.RemoteScanner.RemoteScannerConfig.ForcePairingSaveClass">
            <summary>
            Provides access to the feature to save the Bluetooth address 
            when pairing fails. This attribute is permanently saved.
            </summary>
        </member>
        <member name="P:Symbol.Barcode2.RemoteScanner.RemoteScannerConfig.ForcePairingSaveClass.CurrentValue">
            <summary>
            Provides the current value of the attribute.
            True - Saves Bluetooth address when pairing fails
            False - Does not save Bluetooth address when pairing fails
            This attribute is saved permanently.
            </summary>
        </member>
        <member name="P:Symbol.Barcode2.RemoteScanner.RemoteScannerConfig.ForcePairingSaveClass.FactoryDefaultValue">
            <summary>
            Provides the factory default value of the attribute.
            </summary>
        </member>
        <member name="P:Symbol.Barcode2.RemoteScanner.RemoteScannerConfig.ForcePairingSaveClass.CustomDefaultValue">
            <summary>
            Provides the custom default value of the attribute.
            </summary>
        </member>
        <member name="T:Symbol.Barcode2.RemoteScanner.RemoteScannerConfig.GoodScansDelayClass">
            <summary>
            Provides access to the delay between proximity continuous good scans.
            </summary>
        </member>
        <member name="P:Symbol.Barcode2.RemoteScanner.RemoteScannerConfig.GoodScansDelayClass.CurrentValue">
            <summary>
            Provides the current value of the attribute.
            Defines the delay between proximity continuous good scans in msec.
            Values are betwwen 0 and 150 (* 100 msec)
            0 –  No delay
            1 – 100 msec 
            2 – 200 msec
            ..
            ..
            150 – 15000 msec
            </summary>
        </member>
        <member name="P:Symbol.Barcode2.RemoteScanner.RemoteScannerConfig.GoodScansDelayClass.FactoryDefaultValue">
            <summary>
            Provides the factory default value of the attribute.
            </summary>
        </member>
        <member name="P:Symbol.Barcode2.RemoteScanner.RemoteScannerConfig.GoodScansDelayClass.CustomDefaultValue">
            <summary>
            Provides the custom default value of the attribute.
            </summary>
        </member>
        <member name="T:Symbol.Barcode2.RemoteScanner.RemoteScannerConfig.HardwareVersionClass">
            <summary>
            Provides access to the hardware version.
            NOTE: HardwareVersion attribute will not be supported in the future. Therefore, this class will be deprecated in future releases.
            </summary>
        </member>
        <member name="P:Symbol.Barcode2.RemoteScanner.RemoteScannerConfig.HardwareVersionClass.CurrentValue">
            <summary>
            Provides the current value of the attribute.
            </summary>
        </member>
        <member name="P:Symbol.Barcode2.RemoteScanner.RemoteScannerConfig.HardwareVersionClass.FactoryDefaultValue">
            <summary>
            Provides the factory default value of the attribute.
            </summary>
        </member>
        <member name="P:Symbol.Barcode2.RemoteScanner.RemoteScannerConfig.HardwareVersionClass.CustomDefaultValue">
            <summary>
            Provides the custom default value of the attribute.
            </summary>
        </member>
        <member name="T:Symbol.Barcode2.RemoteScanner.RemoteScannerConfig.HIDAutoReconnectClass">
            <summary>
            Provides access to the HID reconnection parameter.  
            </summary>
        </member>
        <member name="P:Symbol.Barcode2.RemoteScanner.RemoteScannerConfig.HIDAutoReconnectClass.CurrentValue">
            <summary>
            Provides the current value of the attribute.
            Auto-reconnect behavior of the scanner when HID connection is lost.
            0 - Never Reconnect
            1 - Reconnect on Data
            2 - Reconnect Immediately
            </summary>
        </member>
        <member name="P:Symbol.Barcode2.RemoteScanner.RemoteScannerConfig.HIDAutoReconnectClass.FactoryDefaultValue">
            <summary>
            Provides the factory default value of the attribute.
            </summary>
        </member>
        <member name="P:Symbol.Barcode2.RemoteScanner.RemoteScannerConfig.HIDAutoReconnectClass.CustomDefaultValue">
            <summary>
            Provides the custom default value of the attribute.
            </summary>
        </member>
        <member name="T:Symbol.Barcode2.RemoteScanner.RemoteScannerConfig.LowBatteryIndicationCycleClass">
            <summary>
            Provides access to the low battery indication cycle.
            </summary>
        </member>
        <member name="P:Symbol.Barcode2.RemoteScanner.RemoteScannerConfig.LowBatteryIndicationCycleClass.CurrentValue">
            <summary>
            Provides the current value of the attribute.
            Low battery indication cycle time in seconds.
            Use the value:
                0 for 15 secs cycle
                1 for 30 secs cycle
                2 for 60 secs cycle
                3 for 90 secs cycle
                4 for 120 secs cycle
            </summary>
        </member>
        <member name="P:Symbol.Barcode2.RemoteScanner.RemoteScannerConfig.LowBatteryIndicationCycleClass.FactoryDefaultValue">
            <summary>
            Provides the factory default value of the attribute.
            </summary>
        </member>
        <member name="P:Symbol.Barcode2.RemoteScanner.RemoteScannerConfig.LowBatteryIndicationCycleClass.CustomDefaultValue">
            <summary>
            Provides the custom default value of the attribute.
            </summary>
        </member>
        <member name="T:Symbol.Barcode2.RemoteScanner.RemoteScannerConfig.LowBatteryIndicationEnableClass">
            <summary>
            Provides access to the low battery indicator.  
            </summary>
        </member>
        <member name="P:Symbol.Barcode2.RemoteScanner.RemoteScannerConfig.LowBatteryIndicationEnableClass.CurrentValue">
            <summary>
            Provides the current value of the attribute.
            </summary>
        </member>
        <member name="P:Symbol.Barcode2.RemoteScanner.RemoteScannerConfig.LowBatteryIndicationEnableClass.FactoryDefaultValue">
            <summary>
            Provides the factory default value of the attribute.
            </summary>
        </member>
        <member name="P:Symbol.Barcode2.RemoteScanner.RemoteScannerConfig.LowBatteryIndicationEnableClass.CustomDefaultValue">
            <summary>
            Provides the custom default value of the attribute.
            </summary>
        </member>
        <member name="T:Symbol.Barcode2.RemoteScanner.RemoteScannerConfig.MemsEnableClass">
            <summary>
            Provides access to the mems feature.  
            </summary>
        </member>
        <member name="P:Symbol.Barcode2.RemoteScanner.RemoteScannerConfig.MemsEnableClass.CurrentValue">
            <summary>
            Provides the current value of the attribute.
            Enable/Disable mems feature
            </summary>
        </member>
        <member name="P:Symbol.Barcode2.RemoteScanner.RemoteScannerConfig.MemsEnableClass.FactoryDefaultValue">
            <summary>
            Provides the factory default value of the attribute.
            </summary>
        </member>
        <member name="P:Symbol.Barcode2.RemoteScanner.RemoteScannerConfig.MemsEnableClass.CustomDefaultValue">
            <summary>
            Provides the custom default value of the attribute.
            </summary>
        </member>
        <member name="T:Symbol.Barcode2.RemoteScanner.RemoteScannerConfig.ModelNumberClass">
            <summary>
            Provides access to model number. The maximum length of the attribute value is 18.
            </summary>
        </member>
        <member name="P:Symbol.Barcode2.RemoteScanner.RemoteScannerConfig.ModelNumberClass.CurrentValue">
            <summary>
            Provides the current value of the attribute.
            </summary>
        </member>
        <member name="P:Symbol.Barcode2.RemoteScanner.RemoteScannerConfig.ModelNumberClass.FactoryDefaultValue">
            <summary>
            Provides the factory default value of the attribute.
            </summary>
        </member>
        <member name="P:Symbol.Barcode2.RemoteScanner.RemoteScannerConfig.ModelNumberClass.CustomDefaultValue">
            <summary>
            Provides the custom default value of the attribute.
            </summary>
        </member>
        <member name="T:Symbol.Barcode2.RemoteScanner.RemoteScannerConfig.PagingActivateClass">
            <summary>
            Provides access to the paging activation command.
            </summary>
        </member>
        <member name="P:Symbol.Barcode2.RemoteScanner.RemoteScannerConfig.PagingActivateClass.CurrentValue">
            <summary>
            Start /Stop paging to scanner
            0 – stop
            1 – start
            NOTE: Since this property is used for issuing a command, this is a set-only property.
            </summary>
        </member>
        <member name="T:Symbol.Barcode2.RemoteScanner.RemoteScannerConfig.PagingBeepSequenceClass">
            <summary>
            Provides access to the paging beep sequence.  
            </summary>
        </member>
        <member name="P:Symbol.Barcode2.RemoteScanner.RemoteScannerConfig.PagingBeepSequenceClass.CurrentValue">
            <summary>
            Provides the current value of the attribute.
            Paging beep sequence
            Beep pattern values: 0-15
            </summary>
        </member>
        <member name="P:Symbol.Barcode2.RemoteScanner.RemoteScannerConfig.PagingBeepSequenceClass.FactoryDefaultValue">
            <summary>
            Provides the factory default value of the attribute.
            </summary>
        </member>
        <member name="P:Symbol.Barcode2.RemoteScanner.RemoteScannerConfig.PagingBeepSequenceClass.CustomDefaultValue">
            <summary>
            Provides the custom default value of the attribute.
            </summary>
        </member>
        <member name="T:Symbol.Barcode2.RemoteScanner.RemoteScannerConfig.PagingEnableClass">
            <summary>
            Provides access to the paging feature.  
            </summary>
        </member>
        <member name="P:Symbol.Barcode2.RemoteScanner.RemoteScannerConfig.PagingEnableClass.CurrentValue">
            <summary>
            Provides the current value of the attribute.
            Enable/disable paging
            </summary>
        </member>
        <member name="P:Symbol.Barcode2.RemoteScanner.RemoteScannerConfig.PagingEnableClass.FactoryDefaultValue">
            <summary>
            Provides the factory default value of the attribute.
            </summary>
        </member>
        <member name="P:Symbol.Barcode2.RemoteScanner.RemoteScannerConfig.PagingEnableClass.CustomDefaultValue">
            <summary>
            Provides the custom default value of the attribute.
            </summary>
        </member>
        <member name="T:Symbol.Barcode2.RemoteScanner.RemoteScannerConfig.PINCodeTypeClass">
            <summary>
            Provides access to the type of PIN code to use for the Bluetooth device. This attribute is not saved permanently on the Bluetooth device.
            </summary>
        </member>
        <member name="P:Symbol.Barcode2.RemoteScanner.RemoteScannerConfig.PINCodeTypeClass.CurrentValue">
            <summary>
            Provides the current value of the attribute.
            False – (default) Use PIN code stored in memory of Bluetooth device. The default PIN stored in memory is “12345”.
            True  – Prompt the user to scan a new PIN code using the Bluetooth device.
            This attribute is not saved permanently on the Bluetooth device.
            </summary>
        </member>
        <member name="P:Symbol.Barcode2.RemoteScanner.RemoteScannerConfig.PINCodeTypeClass.FactoryDefaultValue">
            <summary>
            Provides the factory default value of the attribute.
            </summary>
        </member>
        <member name="P:Symbol.Barcode2.RemoteScanner.RemoteScannerConfig.PINCodeTypeClass.CustomDefaultValue">
            <summary>
            Provides the custom default value of the attribute.
            </summary>
        </member>
        <member name="T:Symbol.Barcode2.RemoteScanner.RemoteScannerConfig.PreferredWirelessHostClass">
            <summary>
            Provides access to the preferred Bluetoothwireless host.
            NOTE: PreferredWirelessHost attribute is not supported. Therefore, this class will be deprecated in future releases.
            </summary>
        </member>
        <member name="P:Symbol.Barcode2.RemoteScanner.RemoteScannerConfig.PreferredWirelessHostClass.CurrentValue">
            <summary>
            Provides the current value of the attribute.
            Preferred Bluetooth wireless host:
            18 – Scan
            19 – Spp
            20 – Hid
            </summary>
        </member>
        <member name="P:Symbol.Barcode2.RemoteScanner.RemoteScannerConfig.PreferredWirelessHostClass.FactoryDefaultValue">
            <summary>
            Provides the factory default value of the attribute.
            </summary>
        </member>
        <member name="P:Symbol.Barcode2.RemoteScanner.RemoteScannerConfig.PreferredWirelessHostClass.CustomDefaultValue">
            <summary>
            Provides the custom default value of the attribute.
            </summary>
        </member>
        <member name="T:Symbol.Barcode2.RemoteScanner.RemoteScannerConfig.ProximityContinuousEnableClass">
            <summary>
            Provides access to the proximity continuous feature.
            </summary>
        </member>
        <member name="P:Symbol.Barcode2.RemoteScanner.RemoteScannerConfig.ProximityContinuousEnableClass.CurrentValue">
            <summary>
            Provides the current value of the attribute.
            Enable/disable proximity continuous feature.
            </summary>
        </member>
        <member name="P:Symbol.Barcode2.RemoteScanner.RemoteScannerConfig.ProximityContinuousEnableClass.FactoryDefaultValue">
            <summary>
            Provides the factory default value of the attribute.
            </summary>
        </member>
        <member name="P:Symbol.Barcode2.RemoteScanner.RemoteScannerConfig.ProximityContinuousEnableClass.CustomDefaultValue">
            <summary>
            Provides the custom default value of the attribute.
            </summary>
        </member>
        <member name="T:Symbol.Barcode2.RemoteScanner.RemoteScannerConfig.ProximityDistanceClass">
            <summary>
            Provides access to the proximity distance.  
            </summary>
        </member>
        <member name="P:Symbol.Barcode2.RemoteScanner.RemoteScannerConfig.ProximityDistanceClass.CurrentValue">
            <summary>
            Provides the current value of the attribute.
            Proximity distance:
            0 – Short
            1 – Mid
            2 – Long
            </summary>
        </member>
        <member name="P:Symbol.Barcode2.RemoteScanner.RemoteScannerConfig.ProximityDistanceClass.FactoryDefaultValue">
            <summary>
            Provides the factory default value of the attribute.
            </summary>
        </member>
        <member name="P:Symbol.Barcode2.RemoteScanner.RemoteScannerConfig.ProximityDistanceClass.CustomDefaultValue">
            <summary>
            Provides the custom default value of the attribute.
            </summary>
        </member>
        <member name="T:Symbol.Barcode2.RemoteScanner.RemoteScannerConfig.ProximityEnableClass">
            <summary>
            Provides access to the proximity feature.  
            </summary>
        </member>
        <member name="P:Symbol.Barcode2.RemoteScanner.RemoteScannerConfig.ProximityEnableClass.CurrentValue">
            <summary>
            Provides the current value of the attribute.
            Enable/disable proximity
            </summary>
        </member>
        <member name="P:Symbol.Barcode2.RemoteScanner.RemoteScannerConfig.ProximityEnableClass.FactoryDefaultValue">
            <summary>
            Provides the factory default value of the attribute.
            </summary>
        </member>
        <member name="P:Symbol.Barcode2.RemoteScanner.RemoteScannerConfig.ProximityEnableClass.CustomDefaultValue">
            <summary>
            Provides the custom default value of the attribute.
            </summary>
        </member>
        <member name="T:Symbol.Barcode2.RemoteScanner.RemoteScannerConfig.ReconnectAttemptsClass">
            <summary>
            Provides access to the reconnect attempts parameter of the scanner device.
            </summary>
        </member>
        <member name="P:Symbol.Barcode2.RemoteScanner.RemoteScannerConfig.ReconnectAttemptsClass.CurrentValue">
            <summary>
            Provides the current value of the attribute.
            Indicates the time duration during which the scanner tries to reestablish the connection, when it goes out of range.
            Use the value: 
                6 for 30 secs duration
                7 for 35 secs duration
                8 for 40 secs duration
                9 for 45 secs duration
                10 for 50 secs duration
                11 for 55 secs duration
                12 for 60 secs duration
            </summary>
        </member>
        <member name="P:Symbol.Barcode2.RemoteScanner.RemoteScannerConfig.ReconnectAttemptsClass.FactoryDefaultValue">
            <summary>
            Provides the factory default value of the attribute.
            </summary>
        </member>
        <member name="P:Symbol.Barcode2.RemoteScanner.RemoteScannerConfig.ReconnectAttemptsClass.CustomDefaultValue">
            <summary>
            Provides the custom default value of the attribute.
            </summary>
        </member>
        <member name="T:Symbol.Barcode2.RemoteScanner.RemoteScannerConfig.ScanlineWidthClass">
            <summary>
            Provides access to the pick list mode.
            </summary>
        </member>
        <member name="P:Symbol.Barcode2.RemoteScanner.RemoteScannerConfig.ScanlineWidthClass.CurrentValue">
            <summary>
            Provides the current value of the attribute.
            Enables or disables the Pick list mode.
            0 – Disable
            2 – Enable
            </summary>
        </member>
        <member name="P:Symbol.Barcode2.RemoteScanner.RemoteScannerConfig.ScanlineWidthClass.FactoryDefaultValue">
            <summary>
            Provides the factory default value of the attribute.
            </summary>
        </member>
        <member name="P:Symbol.Barcode2.RemoteScanner.RemoteScannerConfig.ScanlineWidthClass.CustomDefaultValue">
            <summary>
            Provides the custom default value of the attribute.
            </summary>
        </member>
        <member name="T:Symbol.Barcode2.RemoteScanner.RemoteScannerConfig.ScantriggerWakeupEnableClass">
            <summary>
            Provides access to the trigger wakeup feature.  
            </summary>
        </member>
        <member name="P:Symbol.Barcode2.RemoteScanner.RemoteScannerConfig.ScantriggerWakeupEnableClass.CurrentValue">
            <summary>
            Provides the current value of the attribute.
            True - Scan trigger serves as device wakeup source from low power
            False - Scan trigger does NOT serve as device wakeup source from low power
            </summary>
        </member>
        <member name="P:Symbol.Barcode2.RemoteScanner.RemoteScannerConfig.ScantriggerWakeupEnableClass.FactoryDefaultValue">
            <summary>
            Provides the factory default value of the attribute.
            </summary>
        </member>
        <member name="P:Symbol.Barcode2.RemoteScanner.RemoteScannerConfig.ScantriggerWakeupEnableClass.CustomDefaultValue">
            <summary>
            Provides the custom default value of the attribute.
            </summary>
        </member>
        <member name="T:Symbol.Barcode2.RemoteScanner.RemoteScannerConfig.SerialNumberClass">
            <summary>
            Provides access to serial number. The maximum length of the attribute value is 16.
            NOTE: Only current value is relevant for this attribute.
            </summary>
        </member>
        <member name="P:Symbol.Barcode2.RemoteScanner.RemoteScannerConfig.SerialNumberClass.CurrentValue">
            <summary>
            Provides the current value of the attribute.
            </summary>
        </member>
        <member name="P:Symbol.Barcode2.RemoteScanner.RemoteScannerConfig.SerialNumberClass.FactoryDefaultValue">
            <summary>
            Provides the factory default value of the attribute.
            </summary>
        </member>
        <member name="P:Symbol.Barcode2.RemoteScanner.RemoteScannerConfig.SerialNumberClass.CustomDefaultValue">
            <summary>
            Provides the custom default value of the attribute.
            </summary>
        </member>
        <member name="T:Symbol.Barcode2.RemoteScanner.RemoteFeedbackConfig">
            <summary>
            This class provides access to the feedback parameters on the remote scanner. NOTE: The Remote feedback feature is available only on remote scanners with feedback capabilites like RS507. To determine if this feature is available check the <see cref="P:Symbol.Barcode2.DeviceInfo.IsFeedbackSupported"/> property in the Barcode2.DeviceInfo object.
            </summary>
        </member>
        <member name="M:Symbol.Barcode2.RemoteScanner.RemoteFeedbackConfig.#ctor">
            <summary>
            Constructor
            </summary>
        </member>
        <member name="P:Symbol.Barcode2.RemoteScanner.RemoteFeedbackConfig.BeepTime">
            <summary>
            A value of zero (0) indicates no beep and a value is greater then zero (>0) enables the beep.
            </summary>
        </member>
        <member name="P:Symbol.Barcode2.RemoteScanner.RemoteFeedbackConfig.BeepFrequency">
            <summary>
            Provides the beep frequency.
            </summary>
        </member>
        <member name="P:Symbol.Barcode2.RemoteScanner.RemoteFeedbackConfig.LEDTime">
            <summary>
            Provides the LED time duration in milliseconds.
            A value of zero (0) indicates no LED used in feedback.
            </summary>
        </member>
        <member name="P:Symbol.Barcode2.RemoteScanner.RemoteFeedbackConfig.WaveFile">
            <summary>
            Provides the path of a wave file.
            NOTE: This property is reserved for future use. So please do not use this property currently.
            </summary>
        </member>
        <member name="P:Symbol.Barcode2.RemoteScanner.RemoteFeedbackConfig.BeepPattern">
            <summary>
            Provides the pattern of the beep.
            </summary>
        </member>
        <member name="P:Symbol.Barcode2.RemoteScanner.RemoteFeedbackConfig.LEDPattern">
            <summary>
            Provides the pattern of the LED.
            </summary>
        </member>
        <member name="T:Symbol.Barcode2.Barcode2">
            <summary>
            The Barcode2 class is the primary class of the Barcode2 class library. 
            It provides access to all Config, Version and scanning method calls for the barcode reader.
            </summary>
        </member>
        <member name="M:Symbol.Barcode2.Barcode2.#ctor">
             <summary>
             Creates a Barcode2 object. Uses the first default supported scanner on a device that supports multiple scanners (such as laser, camera, imager). The first available scanner is chosen.
             </summary>
             <example>
             <code>
             // Create new reader, first available reader will be used.
             Symbol.Barcode2 MyReader = new Symbol.Barcode2();
             </code>
            </example>
        </member>
        <member name="M:Symbol.Barcode2.Barcode2.#ctor(System.String)">
             <summary>
             Creates a Barcode2 object. Uses the provided scanner device name/ Port name of the device. 
             This is normally of the form "SCNx:", where the x is the number of the scanner. It provides you an easier way of identifying the exact device you want to use.
             </summary>
             <param name="DeviceName">Name of the scanner device/port name</param>
             <example>
             <code>
             //Create a reader with port name
             Symbol.Barcode2() MyReader = new Symbol.Barcode2("SCN1:");
             </code>
            </example>
        </member>
        <member name="M:Symbol.Barcode2.Barcode2.#ctor(Symbol.Barcode2.Device)">
             <summary>
             Creates a Barcode2 object. Uses the provided scanner device object.
             </summary>
             <param name="scannerDevice">Scanner device object</param>
             <example>
             <code>
             //get the second SupportedDevices scanner in the list
             Symbol.Barcode2.Device MyDevice = Symbol.Barcode2.Devices.SupportedDevices[1];
            
             //Create a reader with the selected scanner
             Symbol.Barcode2() MyReader = new Symbol.Barcode2(MyDevice);
             </code>
            </example>
        </member>
        <member name="M:Symbol.Barcode2.Barcode2.#ctor(Symbol.Barcode2.DEVICETYPES)">
             <summary>
             Creates a Barcode2 object. Uses the provided scanner device type. It provides you an easier way of identifying the exact device you want to use.
             </summary>
             <param name="deviceType">Scanner device type</param>
             <example>
             <code>
             //Creates a reader to access the 1st internal camera device type 
             Symbol.Barcode2() MyReader = new Symbol.Barcode2(DEVICETYPES.INTERNAL_CAMERA1);
             //or you can select a Bluetooth camera like this
             Symbol.Barcode2() MyReader = new Symbol.Barcode2(DEVICETYPES.BT_CAMERA1);
             </code>
            </example>
        </member>
        <member name="M:Symbol.Barcode2.Barcode2.Enable">
             <summary>
             Enables the scanner hardware. This method does not make the scanner scan or turn on the laser. 
             Calling this method is optional.
             NOTE: When calling Enable() after Disable(), all the latest configuration parameter values (Config.Decoders, Config.Scanner, 
             Config.Reader and Config.Interface) will be set automatically. If the parameters need to be reset to default values,
             either Config.RestoreAllDefaultParameters() or RestoreDefaults() (for the correpsonding parameter) can be called.
             </summary>       
             <example>
             <code> 
            private void button_Click(object sender, EventArgs e)
            {
                if (!myBarcode.IsEnabled)
                {
                    myBarcode.Enable();
                }
            }
             </code>
             </example>
        </member>
        <member name="M:Symbol.Barcode2.Barcode2.StartStatusThread">
            <summary>
            Start status notification thread
            </summary>
        </member>
        <member name="M:Symbol.Barcode2.Barcode2.ScanCancel">
             <summary>
             This Cancels any pending asynchronous Scan() calls. (Will not work for buffered scans)
             </summary>
             <example>
             <code> 
             private void button1_Click(object sender, EventArgs e)
             {
                //Issues an Asynchronous scan that calls back to an event delegate when a trigger is pulled
                Results result = myBarcode.Scan();
             }
             
             ...
             
             //Button to issue a cancel of the async scan
             private void button2_Click(object sender, EventArgs e)
             {
                myBarcode.ScanCancel();
             }
            
             </code>
             </example>
             <returns>Returns the <see cref="T:Symbol.Barcode2.Results"/> of the operation. Returns <see cref="F:Symbol.Barcode2.Results.SUCCESS"/> if there is a pending scan, and <see cref="F:Symbol.Barcode2.Results.E_SCN_READNOTPENDING"/> if there is no pending scan</returns>
        </member>
        <member name="M:Symbol.Barcode2.Barcode2.ScanBufferCount">
            <summary>
            Returns the number of pending ScanData objects in the buffer.
            </summary>
            <returns>The number of pending ScanData Objects. -1 if Buffered scans have not been started.</returns>
            <example>
            <code> 
            private void Form1_Load(object sender, EventArgs e)
            {
                //Attach an event handler to the barcode object
                myBarcode.OnScan += new Barcode2.OnScanHandler(myBarcode_OnScan);
            
                ...
            
                //issue a start to buffered scans            
                Results result = myBarcode.ScanBufferStart();
            }
            
            ...
            
            //button to get a count of buffered scans
            private void btnBufferCount_Click(object sender, EventArgs e)
            {          
               int NumberOfScans = myBarcode.ScanBufferCount();
            }
            </code>
            </example>
        </member>
        <member name="M:Symbol.Barcode2.Barcode2.ScanBufferClear">
            <summary>
            This clears all unread scans in the buffer.
            </summary>
            <returns>Returns the <see cref="T:Symbol.Barcode2.Results"/> of the operation. Returns <see cref="F:Symbol.Barcode2.Results.SUCCESS"/> if in buffered scan mode, and <see cref="F:Symbol.Barcode2.Results.NOBUFFEREDSCANS_PENDING"/> if it is not in buffered scan mode.</returns>
            <example>
            <code> 
            private void Form1_Load(object sender, EventArgs e)
            {
                //Attach an event handler to the barcode object
                myBarcode.OnScan += new Barcode2.OnScanHandler(myBarcode_OnScan);
            
                ...
            
                //issue a start to buffered scans       
                Results sd = myBarcode.ScanBufferStart();
            }
            
            ...
            
            //button to Clear buffered scans
            private void btnBufferClear_Click(object sender, EventArgs e)
            {          
               Results sd = myBarcode.ScanBufferClear();
            }
            </code>
            </example>
        </member>
        <member name="M:Symbol.Barcode2.Barcode2.ScanWait(System.Int32)">
            <summary>
            Starts a Synchronous Scan. The method will not turn on the scanner. 
            It will, however, put the scanner in a state in which the scanner can be 
            turned on either by pressing a hardware trigger or by performing a software trigger.  
            This is a blocking call and the thread will be unresponsive until the timeout expires or 
            the data is read.
            </summary>
            <param name="timeout">The time the scanner should wait for a trigger to be pulled (in milliseconds).           Must be between 1000 milliseconds and 5 minutes (1 second to 5 minutes.)</param>
            <returns>A ScanData Object that contains the data from the barcode</returns>
            <example>
            <code> 
            ScanData myScanData;
            private void btnScan_Click_1(object sender, EventArgs e)
            {
               myScanData = myBarcode.ScanWait(5000); // 5 second timeout
               if (myScanData.Result == Results.SUCCESS)
               {
                   if (myScanData.IsText)
                   {
                       TextBox2.Text = "ScanWait(): " + TextBox2.Text + "\r\n";
                   }
               }
               else if (myScanData.Result == Results.E_SCN_READTIMEOUT)
               {        
                    TextBox2.Text = "The Scan request has timed out."\r\n";    
               }
               else
               {
                   TextBox2.Text = "ScanWait() Unsuccessful\r\n" + myScanData.Result;
               }
            }
            </code>
            </example>
        </member>
        <member name="M:Symbol.Barcode2.Barcode2.Scan">
            <summary>
            Starts an asynchronous Scan. The method will not turn on the scanner. 
            It will, however, put the scanner in a state in which the scanner can be 
            turned ON either by pressing a hardware trigger or can be turned ON automatically.
            This is determined by the <see cref="P:Symbol.Barcode2.Config.TriggerMode"/> property. 
            An <see cref="T:Symbol.Barcode2.Barcode2.OnScanHandler"/> event handler needs to be attached in order to get the Scan Data in a callback. 
            This request can be cancelled by issuing a <see cref="M:Symbol.Barcode2.Barcode2.ScanCancel"/>.
            </summary>
            <returns>The <see cref="T:Symbol.Barcode2.Results"/> of if the scan was successful or not.</returns>
            <example>
            <code> 
            private void Form1_Load(object sender, EventArgs e)
            {
                //Attach an event handler to the barcode object
                myBarcode.OnScan += new Barcode2.OnScanHandler(myBarcode_OnScan);
            
                ...
            
                //Issues an Asynchronous scan that will call back to the event delegate when a trigger is pulled.
                //This is not a blocking call and the UI will still be responsive
                Results sd = myBarcode.Scan();
            }
            
            ...
            
            void myBarcode_OnScan(ScanDataCollection sd)
            {
               if (rd.Result == Results.SUCCESS)
               {
                  TextBox1.Text = TextBox1.Text + rd.Text + "\r\n";
               }
               else
               {
                   TextBox1.Text = TextBox1.Text + "Read Unsuccessful\r\n" + rd.Result;
               }
            }
            </code>
            </example>
        </member>
        <member name="M:Symbol.Barcode2.Barcode2.Scan(Symbol.Barcode2.Barcode2.OnScanHandler)">
             <summary>
             Starts an asynchronous Scan using an OnScan event delegate as a parameter. 
             The method will not turn on the scanner. 
             It will, however, put the scanner in a state in which the scanner can be 
             turned on either by pressing a hardware trigger or by performing a software trigger.  
             This request can be cancelled by issuing a <see cref="M:Symbol.Barcode2.Barcode2.ScanCancel"/>. When using this method there
             is no need to attach an OnScan event handler.
             </summary>
             <param name="ScanDelegate">The event handler you want to handle the callback</param>
             <returns>The <see cref="T:Symbol.Barcode2.Results"/> of if the scan was successful or not.</returns>
             <example>
             <code> 
             
              private void Form1_Load(object sender, EventArgs e)
              {
                //This is not a blocking call and the UI will still be responsive
                Results sd = myBarcode.Scan(new Barcode2.OnScanHandler(myBarcode_OnScan1));            
              }
             
             ...
            
              void myBarcode_OnScan1(ScanDataCollection sd)
              {
                 if (sd.Length == 1)
                 {
                     ScanData rd = sd.GetFirst;
                     if (rd.Result == Results.SUCCESS)
                     {
                         TextBox1.Text = TextBox1.Text + rd.Text + "\n";
                     }
                     else
                     {
                         TextBox1.Text = TextBox1.Text + "Read Unsuccessful\n" + rd.Result;
                     }
                 }
              } 
             </code>
             </example>
        </member>
        <member name="M:Symbol.Barcode2.Barcode2.Scan(System.UInt32)">
            <summary>
            Starts an asynchronous Scan. The method will not turn on the scanner. 
            It will, however, put the scanner in a state in which the scanner can be 
            turned on either by pressing a hardware trigger or by performing a software trigger. 
            An event handler needs to be attached in order to get the Scan sData in a callback. 
            This request can be cancelled by issuing a <see cref="M:Symbol.Barcode2.Barcode2.ScanCancel"/>.
            </summary>
            <remarks>
            If the time period for the timeout period expires a Scandata object will be returned with the value E_SCN_READTIMEOUT in the Results field.  
            </remarks>
            <param name="timeout">The time the scanner should wait for a trigger to be pulled (in milliseconds). Must be between 1000 milliseconds and 5 minutes (1 second to 5 minutes.)</param>
            <returns>The <see cref="T:Symbol.Barcode2.Results"/> of if the scan was successful or not.</returns>
            <example>
            <code> 
            //Attach an event handler to the barcode object
            myBarcode.OnScan += new Barcode2.OnScanHandler(myBarcode_OnScan);
            
            ...
            
            private void button8_Click(object sender, EventArgs e)
            {
               //Issues an Asynchronous scan  that calls back to an event delegate when a trigger is pulled
               // within the timeout passed
               Results sd = myBarcode.Scan(5000);
            }
            
            ...
            
            void myBarcode_OnScan(ScanDataCollection sd)
            {
               if (sd.Length == 1)
               {
                   ScanData rd = sd.GetFirst;
                   if (rd.Result == Results.SUCCESS)
                   {
                       TextBox1.Text = TextBox1.Text + rd.Text + "\r\n";
                   }
                   else
                   {
                       TextBox1.Text = TextBox1.Text + "Read Unsuccessful\r\n" + rd.Result;
                   }
               }               
            }
            </code>
            </example>
        </member>
        <member name="M:Symbol.Barcode2.Barcode2.ScanBufferStart">
             <summary>
             Starts an asynchronous Buffered scan mode. The method will not turn on the scanner. 
             It will, however, put the scanner in a state in which the scanner can be 
             turned on either by pressing a hardware trigger or by performing a software trigger. 
             <para>
             When scanning multiple barcodes, the user may want to continue scanning without waiting 
             for each OnScan callback to be validated or processed. In order to facilitate this, 
             an application can use the buffered-read mode. In this mode as soon as the application calls
             the ScanBufferStart() function the user will be able to scan bar codes. 
             See note on 'Buffered Reads' in the programmer's guide for more information.
             </para>
             </summary>
             <returns>The <see cref="T:Symbol.Barcode2.Results"/> of if the scan was successful or not.</returns>
             <example>
             <code> 
             //Attach an event handler to the barcode object
             myBarcode.OnScan += new Barcode2.OnScanHandler(myBarcode_OnScan);
             Results sd = myBarcode.ScanBufferStart();
             
             ...
             
             void myBarcode_OnScan(ScanDataCollection sd)
             {
                if (sd.Length == 1)
                {
                    ScanData rd = sd.GetFirst;
                    if (rd.Result == Results.SUCCESS)
                    {
                        TextBox1.Text = TextBox1.Text + rd.Text + "\r\n";
                    }
                    else
                    {
                        TextBox1.Text = TextBox1.Text + "Read Unsuccessful\r\n" + rd.Result;
                    }
                }
             }
            
             </code>
             </example>
        </member>
        <member name="M:Symbol.Barcode2.Barcode2.GetScannedData">
             <summary>
             Directly gets buffered scanned data instead of using an OnScan callback. In order for this method 
             to work the barcode reader must be in buffered read mode and there may not be an event 
             handler attached. The maximum number of ScanData objects returned in the collection is 
             set in Config.<see cref="P:Symbol.Barcode2.Config.MaxScanDataCollection"/>, the default is 1. It is 
             recommended to use an <see cref="T:Symbol.Barcode2.Barcode2.OnScanHandler"/> event handler to handle instead of using 
             this method.
             </summary>
             <returns><see cref="T:Symbol.Barcode2.ScanDataCollection"/>, a collection of scan data objects</returns>
             <example>
             <code> 
             private void Form1_Load(object sender, EventArgs e)
             {
                  Symbol.Barcode2 myBarcode = new Symbol.Barcode2();
                  Results result = myBarcode.ScanBufferStart();
             }
             
             ...
             
            private void button11_Click(object sender, EventArgs e)
            {
                ScanDataCollection data; //Create a collection of Scandata objects
                data = myBarcode.GetScannedData(); // get all buffered scans from memory
             
                //loop through the collection
                foreach (ScanData sd in data)
                {
                    if (sd.Result == Results.SUCCESS)
                    {
                        TextBox1.Text = TextBox1.Text + sd.Text + "\r\n";
                    }
                    else
                    {
                        TextBox1.Text = TextBox1.Text + "Scan Error\r\n";
                    }
                }
            }
             </code>
             </example>
        </member>
        <member name="M:Symbol.Barcode2.Barcode2.ScanBufferPause">
            <summary>
            Pauses buffered scan mode. It retians the state of the buffer and any unread data will not be lost.
            </summary>
             <returns>Returns the <see cref="T:Symbol.Barcode2.Results"/> of the operation. Returns <see cref="F:Symbol.Barcode2.Results.SUCCESS"/> if in buffered scan mode, and <see cref="F:Symbol.Barcode2.Results.NOBUFFEREDSCANS_PENDING"/> if it is not in buffered scan mode.</returns>
            <example>
            <code> 
            private void Form1_Load(object sender, EventArgs e)
            {
                //Attach an event handler to the barcode object
                myBarcode.OnScan += new Barcode2.OnScanHandler(myBarcode_OnScan);
            
                ...
            
                //start to buffered scans
                Results sd = myBarcode.ScanBufferStart();
            }
            
            ...
            
            //button to issue a pause to buffered scans
            private void btnBufferPause_Click(object sender, EventArgs e)
            {          
               Results sd = myBarcode.ScanBufferPause();
            }
            </code>
            </example>
        </member>
        <member name="M:Symbol.Barcode2.Barcode2.ScanBufferResume">
            <summary>
            Resumes buffered Scan Mode from a paused state.
            </summary>
            <returns>Returns the <see cref="T:Symbol.Barcode2.Results"/> of the operation. Returns <see cref="F:Symbol.Barcode2.Results.SUCCESS"/> if in buffered scan mode, and <see cref="F:Symbol.Barcode2.Results.NOBUFFEREDSCANS_PENDING"/> if it is not in buffered scan mode.</returns>
            <example>
            <code> 
            private void Form1_Load(object sender, EventArgs e)
            {
                //Attach an event handler to the barcode object
                myBarcode.OnScan += new Barcode2.OnScanHandler(myBarcode_OnScan);
            
                ...
            
                //issue a start to buffered scans
                Results sd = myBarcode.ScanBufferStart();
            }
            
            ...
            
            //button to issue a pause to buffered scans
            private void btnBufferPause_Click(object sender, EventArgs e)
            {          
               Results sd = myBarcode.ScanBufferPause();
            }
            
            ...
            
            //button to issue a resume to paused buffered scans
            private void btnBufferResume_Click(object sender, EventArgs e)
            {          
               Results sd = myBarcode.ScanBufferResume();
            }
            </code>
            </example>
        </member>
        <member name="M:Symbol.Barcode2.Barcode2.ScanBufferStop">
            <summary>
            Stops the buffered scan mode. After this method is executed any pending buffered reads will be lost.
            </summary>
            <returns>Returns the <see cref="T:Symbol.Barcode2.Results"/> of the operation. Returns <see cref="F:Symbol.Barcode2.Results.SUCCESS"/> if in buffered scan mode, and <see cref="F:Symbol.Barcode2.Results.NOBUFFEREDSCANS_PENDING"/> if it is not in buffered scan mode.</returns>
            <example>
            <code> 
            private void Form1_Load(object sender, EventArgs e)
            {
                //Attach an event handler to the barcode object
                myBarcode.OnScan += new Barcode2.OnScanHandler(myBarcode_OnScan);
            
                ...
            
                //issue a start to buffered scans
                Results sd = myBarcode.ScanBufferStart();
            }
            
            ...
            
            //button to issue a stop to buffered scans
            private void btnBufferStop_Click(object sender, EventArgs e)
            {          
               Results sd = myBarcode.ScanBufferStop();
            }
            </code>
            </example>
        </member>
        <member name="M:Symbol.Barcode2.Barcode2.Disable">
            <summary>
            Disables the scanner. Any stored scan data will be lost.
            NOTE: When calling Enable() after Disable(), all the latest configuration parameter values (Config.Decoders, Config.Scanner, 
            Config.Reader and Config.Interface) will be set automatically. If the parameters need to be reset to default values,
            either Config.RestoreAllDefaultParameters() or RestoreDefaults() (for the correpsonding parameter) can be called.
            </summary>
            <example>
            <code> 
             if (myBarcode.IsEnabled)
                {
                    myBarcode.Disable();
                }
            </code>
            </example>
        </member>
        <member name="M:Symbol.Barcode2.Barcode2.Dispose">
            <summary>
            Frees up all resources used by the Barcode Reader, including any objects it has created. 
            It is necessary that this method be called when the Barcode Reader is no 
            longer needed by the application. This call will automatically diable the scanner too. 
            </summary>
            <example>
            <code> 
             if (myBarcode != null)
                {
                    myBarcode.Dispose();
                }
            </code>
            </example>
        </member>
        <member name="M:Symbol.Barcode2.Barcode2.Finalize">
            <summary>
            Finalizer
            </summary>
        </member>
        <member name="E:Symbol.Barcode2.Barcode2.OnScan">
            <summary>
            Attach to this notification event to be called back when a scan event occurs. 
            This should be used with regular asynchronous scan's and Buffered scans.
            </summary>
            <value>
            A <see cref="T:Symbol.Barcode2.Barcode2.OnScanHandler"/> delegate of the method that will be invoked
            when a scan event occurs.
            </value>
            <example>
            <code>
            //Create Barcode2 object and attach event handler
            Barcode2 myBarcode = new Barcode2();
            myBarcode.OnScan += new Barcode2.OnScanHandler(myBarcode_OnScan);
            Results sd = myBarcode.Scan();
            
            ...
            
            void  myBarcode_OnScan(ScanDataCollection scancollection)
            {
                ScanData sd = scancollection.GetFirst;
                if ((sd.Result == Results.SUCCESS) || (sd.Result == Results.E_SCN_BUFFERTOOSMALL))
                {    
                  textScan.Text += sd.Text + "\r\n";      
                }
            }
            </code>
            </example>
        </member>
        <member name="E:Symbol.Barcode2.Barcode2.OnStatus">
             <summary>
              Attach to this notification event to be called back when a status event occurs. 
             The status Callback will provide the <see cref="T:Symbol.Barcode2.StatusData"/> object which provides <see cref="P:Symbol.Barcode2.StatusData.State"/>, <see cref="P:Symbol.Barcode2.StatusData.DocCapState"/> and <see cref="P:Symbol.Barcode2.StatusData.EventType"/>.        
             </summary>
             <example>
             <code>
             private void Form1_Load(object sender, EventArgs e)
             {
                 Barcode2 myBarcode = new Barcode2();
                 myBarcode.OnStatus += new Barcode2.OnStatusHandler(myBarcode_OnStatus);
             }
             
             ...
             
            void  myBarcode_OnStatus(StatusData status)
            {
            	   txtBoxStatus.Text = status.EventType.ToString();
            }
             </code>
             </example>
        </member>
        <member name="P:Symbol.Barcode2.Barcode2.Config">
            <summary>
            The Config property holds access to all the parameters inside the interface, scanner, reader and decoder objects.
            </summary>
        </member>
        <member name="P:Symbol.Barcode2.Barcode2.Version">
            <summary>
            Barcode reader version property. 
            </summary>
        </member>
        <member name="P:Symbol.Barcode2.Barcode2.RemoteScanner">
            <summary>
            This property provides access to the RSM 
            </summary>
        </member>
        <member name="P:Symbol.Barcode2.Barcode2.DeviceInfo">
            <summary>
            This property gets the device capabilities information for a scanner.
            </summary>
        </member>
        <member name="P:Symbol.Barcode2.Barcode2.IsEnabled">
            <summary>
             Returns if the scanner is enabled
            </summary>
        </member>
        <member name="P:Symbol.Barcode2.Barcode2.IsScanPending">
            <summary>
            Specifies whether or not there is currently in a pending scan situation. 
            <list type="bullet">
            <item>
            <description>True - After calling any of the scan methods such as Scan(), Scan(timeout), Scan(OnScanHandler), ScanBufferStart(), ScanBufferResume() and ScanWait(timeout)..</description>
            </item>
            <item>
            <description>False - After the successful scan of a barcode or if the scan failed. It will also be set to false if any of these methods are called - ScanCancel(), Disable(), ScanBufferPause() and ScanBufferStop()</description>
            </item>
            </list>
            </summary>
        </member>
        <member name="T:Symbol.Barcode2.Barcode2.OnScanHandler">
            <summary>
            Delegate of the method that will be invoked when a scan event occurs.
            </summary>
            <param name="scancollection">ScanDataCollection</param>
        </member>
        <member name="T:Symbol.Barcode2.Barcode2.OnStatusHandler">
            <summary>
            Delegate of the method that will be invoked when a status event occurs.
            </summary>
            <param name="statusdata">StatusData</param>
        </member>
        <member name="T:Symbol.Barcode2.DeviceInfo">
            <summary>
            This property gets the device capabilities information for a scanner.
            </summary>
        </member>
        <member name="P:Symbol.Barcode2.DeviceInfo.SupportedBeamWidths">
            <summary>
            Provides a list of supported beam widths. The beam width parameter for the laser scanner can be modified using the Barcode2.Config.Reader.ReaderSpecific.LaserSpecific.BeamWidth property.
            The beam width feature is not supported on Imager scanners.
            <para>
            NOTE: All laser scanners support BEAM_WIDTH.NORMAL by default. This array can be used by the application to query any other beam widths it may support. 
            </para>
            </summary>
        </member>
        <member name="P:Symbol.Barcode2.DeviceInfo.IsAimModeSupported">
            <summary>
            Flag telling if the device supports aiming.
            </summary>
        </member>
        <member name="P:Symbol.Barcode2.DeviceInfo.IsDirectionSupported">
            <summary>
            Flag telling if the device supports scan direction reporting.
            </summary>
        </member>
        <member name="P:Symbol.Barcode2.DeviceInfo.IsFeedbackSupported">
            <summary>
            Flag telling if the device supports remote (non-local) feedback.
            </summary>
        </member>
        <member name="P:Symbol.Barcode2.DeviceInfo.IsViewfinderSupported">
            <summary>
            Flag describing whether this engine supports a video viewfinder.
            </summary>
        </member>
        <member name="P:Symbol.Barcode2.DeviceInfo.IsAdaptiveScanningSupported">
            <summary>
            Flag describing whether this engine supports adaptive scanning.
            NOTE: The adaptive scanning feature can be enabled/disabled using the Barcode2.Config.Reader.ReaderSpecific.LaserSpecific.AdaptiveScanning property.
            Refer to this property's help page for important information about the Adaptive Scanning feature.
            </summary>
        </member>
        <member name="P:Symbol.Barcode2.DeviceInfo.ImageDecoderMaxRect">
            <summary>
            Maximum image cropping rectangle (pixels). This is the maximum image resolution that can be expected.
            If the image capture is not supported, this will all be zeros.
            </summary>
        </member>
        <member name="P:Symbol.Barcode2.DeviceInfo.DPMCapable">
            <summary>
            Flag describing whether engine is capable of decoding DPM bar codes.
            </summary>
        </member>
        <member name="P:Symbol.Barcode2.DeviceInfo.SupportedImageFormats">
            <summary>
            Bit mask of supported image formats. See IMAGE_FORMAT for a list of supported formats.
            </summary>
        </member>
        <member name="P:Symbol.Barcode2.DeviceInfo.DocCapVersion">
            <summary>
            Indicates support of Document Capture for a scanner.
            </summary>
        </member>
        <member name="T:Symbol.Barcode2.DeviceInfo.ImageDecoderRect">
            <summary>
            Draws a rectangle.
            </summary>
        </member>
        <member name="P:Symbol.Barcode2.DeviceInfo.ImageDecoderRect.Left">
            <summary>
            Gets or sets the distance, in pixels, between the left edge of the rectangle and the left edge of its container's client area.
            </summary>
        </member>
        <member name="P:Symbol.Barcode2.DeviceInfo.ImageDecoderRect.Top">
            <summary>
            Gets or sets the distance, in pixels, between the top edge of the rectangle and the top edge of its container's client area.
            </summary>
        </member>
        <member name="P:Symbol.Barcode2.DeviceInfo.ImageDecoderRect.Right">
            <summary>
            Gets the distance, in pixels, between the right edge of the rectangle and the left edge of its container's client area.
            </summary>
        </member>
        <member name="P:Symbol.Barcode2.DeviceInfo.ImageDecoderRect.Bottom">
            <summary>
            Gets the distance, in pixels, between the bottom edge of the rectangle and the top edge of its rectangle's client area.
            </summary>
        </member>
        <member name="T:Symbol.Barcode2.ScanData">
            <summary>
            ScanData class encapsulates all the scanned data and meta-data for a given read. 
            </summary>
        </member>
        <member name="M:Symbol.Barcode2.ScanData.ToString">
            <summary>
            Returns the scanned data text.
            </summary>
            <returns>
            A string of the scanned data if the memory buffer has been set to text mode.
            </returns>
            <exception cref="T:Symbol.Barcode2.InvalidDataTypeException"> An 
            InvalidDataTypeException is thrown if this property is accessed and they
            memory buffer has been set to binary mode. </exception>
        </member>
        <member name="M:Symbol.Barcode2.ScanData.#ctor">
            <summary>
            ScanData constructor.
            </summary>
        </member>
        <member name="M:Symbol.Barcode2.ScanData.#ctor(Symbol.Barcode2.ScanDataFormats,Symbol.Barcode2.ReaderDataLengths)">
            <summary>
            ScanData constructor with ScanDataFormats and ReaderDataLengths.  
            </summary>
        </member>
        <member name="M:Symbol.Barcode2.ScanData.#ctor(Symbol.Barcode2.ScanDataFormats,System.Int32)">
            <summary>
            ScanData constructor with ScanDataFormats and int.  
            </summary>
        </member>
        <member name="M:Symbol.Barcode2.ScanData.Reset">
            <summary>
            Reset the contents of a Scandata object
            </summary>
        </member>
        <member name="M:Symbol.Barcode2.ScanData.Finalize">
            <summary>
            Finalizer
            </summary>
        </member>
        <member name="M:Symbol.Barcode2.ScanData.Dispose">
            <summary>
            Frees up all resources used by the ScanData object.  
            </summary>
        </member>
        <member name="P:Symbol.Barcode2.ScanData.Length">
            <summary>
            The length of the barcode data.
            </summary>
            <value>
            An integer that specifies the length of the scanned data.
            </value>
        </member>
        <member name="P:Symbol.Barcode2.ScanData.IsText">
            <summary>
            Specifies whether or not the ScanData memory buffer is set to text mode.
            </summary>
            <value>
            A boolean flag which when true indicates that the memory buffer is set to 
            text mode, otherwise the memory buffer is set to binary mode.
            </value>
        </member>
        <member name="P:Symbol.Barcode2.ScanData.IsAllocated">
            <summary>
            Specifies whether or not a memory buffer has been allocated for the ScanData.
            </summary>
            <value>
            A boolean flag which when true indicates that a valid memory buffer has been 
            allocated, otherwise the memory buffer has not been allocated.
            </value>
        </member>
        <member name="P:Symbol.Barcode2.ScanData.BufferSize">
            <summary>
            Specifies the memory buffer size allocated by the ScanData.
            </summary>
            <value>
            An integer which specifies the memory buffer size in bytes for binary mode or 
            characters for text mode.
            </value>
        </member>
        <member name="P:Symbol.Barcode2.ScanData.Text">
            <summary>
            Returns the scanned data text.
            </summary>
            <value>
            A string of the scanned data if the memory buffer has been set to text mode.
            </value>
            <exception cref="T:Symbol.Barcode2.InvalidDataTypeException"> An InvalidDataTypeException
            is thrown if this property is accessed and they memory buffer has been set 
            to binary mode. </exception>
        </member>
        <member name="P:Symbol.Barcode2.ScanData.TypeHex">
            <summary>
            The Decoder Type of the barcode in hexadecimal format.
            </summary>
            <value>
            A string of the form "0x#", where # is the decoder type in hexadecimal.
            </value>
        </member>
        <member name="P:Symbol.Barcode2.ScanData.RawData">
            <summary>
            Returns a byte array with the scanned data.
            </summary>
            <value>
            A byte array with the scanned data if the ScanDataFormat has been set to binary mode.
            </value>
            <exception cref="T:Symbol.Barcode2.InvalidDataTypeException"> An InvalidDataTypeException
            is thrown if this property is accessed and ScanDataFormat has been set 
            to text mode in the constructor of ScanData object. </exception>
        </member>
        <member name="P:Symbol.Barcode2.ScanData.Source">
            <summary>
            Returns the source of the scanned data. (ie The barcode scanner device name)
            </summary>
            <value> 
            A string that contains the name of the scanner which provided the source.
            </value>
        </member>
        <member name="P:Symbol.Barcode2.ScanData.TimeStamp">
            <summary>
            The time at which the barcode was scanned.
            </summary>
            <value>
            A DateTime structure that contains the time information.
            </value>
        </member>
        <member name="P:Symbol.Barcode2.ScanData.Type">
            <summary>
            The Decoder type of the barcode.
            </summary>
            <value>
            A <see cref="T:Symbol.Barcode2.DecoderTypes"/> member that specifies the type of barcode scanned.
            </value>
        </member>
        <member name="P:Symbol.Barcode2.ScanData.MultiPart">
            <summary>
            The flag indicating whether the data is in the multi-part format. 
            If this has been set to true, the user is supposed to get the auxiliary data.
            Note: The usage of this has been deprecated. Instead, the usage of the property AuxDataFormat has been recommended.
            AuxDataFormat denotes whether the auxiliary data is available or not.
            In case of auxiliary data being available, AuxDataFormat will denote the type of auxiliary data as well (The multipacket composite barcode data, the image data, or unknown).
            </summary>
        </member>
        <member name="P:Symbol.Barcode2.ScanData.AuxData">
             <summary>
             Provides the access to the auxiliary data. 
             The availability of auxiliary data can be determined by checking the AuxData.AuxDataFormat property.
             The auxiliary data will be populated in case of some composite barcodes and for document capture.
             In case of auxiliary data being available, AuxData.AuxDataFormat will denote the type of auxiliary data as well (The multipacket composite barcode data, the image data, or unknown).
             </summary>
             <example>
             <code>
             void myBarcode2_OnScan(ScanDataCollection scancollection)
             {
                foreach (ScanData scanData in scancollection)
                {
                    // The availability of auxillary data is first determined
                    if (scanData.AuxDataFormat != AUX_FORMATS.NO_DATA)
                    {
                        // In case of composite barcodes, AuxDataFormat will be set to AUX_FORMATS.BARCODE_DATA on successful decode
                        if (scanData.AuxDataFormat == AUX_FORMATS.BARCODE_DATA)
                        {
                            barcodeAuxDataTextBox.Text = scanData.AuxData.Text;
                            //The raw composite data is available through scanData.AuxData.RawData
                        }
                        // In case of document capture, AuxDataFormat will be set to AUX_FORMATS.IMAGE_DATA on successful decode
                        else if (scanData.AuxDataFormat == AUX_FORMATS.IMAGE_DATA)
                        {
                            scanData.AuxData.GetBitmap().Save("Test.jpg", System.Drawing.Imaging.ImageFormat.Jpeg);
                            //The byte data for the image is available as MemoryStream through scanData.AuxData.scanData.AuxData.ImageStream
                        }
                    }
                }
             }
             </code>
            </example>
        </member>
        <member name="P:Symbol.Barcode2.ScanData.RequestId">
            <summary>
            The ID of the read submitted to the barcode scanner hardware.
            </summary>
            <value>
            An integer that represents the ID of the requested read.
            </value>
        </member>
        <member name="P:Symbol.Barcode2.ScanData.Result">
            <summary>
            The result of the read request.
            </summary>
            <remarks>
            This result is a managed code version of the actual result returned by the
            underlying scan driver stack. It can be used to determine whether or not the 
            read was successful or failed. 
            </remarks>
            <value>
            A <see cref="T:Symbol.Barcode2.Results"/> member that indicates whether or not the read was 
            successful.
            </value>
        </member>
        <member name="P:Symbol.Barcode2.ScanData.ReaderDataType">
            <summary>
            Data types for the data that is read from barcode scanner. 
            </summary>
        </member>
        <member name="P:Symbol.Barcode2.ScanData.ReaderDataLengths">
            <summary>
            Length of ScanData memory buffer. 
            </summary>
        </member>
        <member name="P:Symbol.Barcode2.ScanData.AuxDataFormat">
            <summary>
            Format of the data in auxiliary data buffer.
            The usage of this has been recommended over the usage of the property MultiPart.
            This property AuxDataFormat denotes whether the auxiliary data is available or not.
            In case of auxiliary data being available, this will denote the type of auxiliary data as well (The multipacket composite barcode data, the image data, or unknown).
            </summary>
        </member>
        <member name="T:Symbol.Barcode2.ScanData.AuxiliaryData">
            <summary>
            Provides the access to the auxiliary data. This is meant to be used in conjunction with a ScanData instance.
            </summary>
        </member>
        <member name="M:Symbol.Barcode2.ScanData.AuxiliaryData.ToString">
            <summary>
            Returns the auxiliary data text.
            </summary>
            <returns>
            A string of the auxiliary data if the ScanData memory buffer has been set to text mode.
            </returns>
            <exception cref="T:Symbol.Barcode2.InvalidDataTypeException"> An 
            InvalidDataTypeException is thrown if this property is accessed and the ScanData
            memory buffer has been set to binary mode. </exception>
        </member>
        <member name="M:Symbol.Barcode2.ScanData.AuxiliaryData.GetBitmap">
            <summary>
            Returns the bitmap of the image in auxiliary data buffer.
            Only valid for AUX_FORMATS.IMAGE_DATA.
            </summary>
        </member>
        <member name="P:Symbol.Barcode2.ScanData.AuxiliaryData.Text">
            <summary>
            Returns the auxiliary data text.
            </summary>
            <returns>
            A string of the auxiliary data if the ScanData memory buffer has been set to text mode.
            Only valid for AUX_FORMATS.BARCODE_DATA.
            </returns>
            <exception cref="T:Symbol.Barcode2.InvalidDataTypeException"> An 
            InvalidDataTypeException is thrown if this property is accessed and the ScanData
            memory buffer has been set to binary mode. </exception>
        </member>
        <member name="P:Symbol.Barcode2.ScanData.AuxiliaryData.RawData">
            <summary>
            Returns a byte array of the auxiliary data.
            </summary>
            <value>
            A byte array of the auxiliary data if the ScanData memory buffer has been set to binary mode.
            Only valid for AUX_FORMATS.BARCODE_DATA and AUX_FORMATS.UNKNOWN.
            </value>
            <exception cref="T:Symbol.Barcode2.InvalidDataTypeException"> An InvalidDataTypeException
            is thrown if this property is accessed and ScanDataFormat has been set 
            to text mode in the constructor of ScanData object. </exception>
        </member>
        <member name="P:Symbol.Barcode2.ScanData.AuxiliaryData.Length">
            <summary>
            The length of the auxiliary data, in bytes.
            This is valid for all formats of auxiliary data.
            Will be the length of RawData for the formats AUX_FORMATS.BARCODE_DATA and AUX_FORMATS.UNKNOWN.
            Will be the length of ImageStream in case of AUX_FORMATS.IMAGE_DATA.
            </summary>
        </member>
        <member name="P:Symbol.Barcode2.ScanData.AuxiliaryData.ImageStream">
            <summary>
            The memory stream of the image data in auxiliary data buffer.
            Only valid for AUX_FORMATS.IMAGE_DATA.
            This memory stream won't contain the type, the height and the width of the image.
            They would be given separately by the 3 properties ImageFormat, ImageHeight and ImageWidth.
            </summary>
        </member>
        <member name="P:Symbol.Barcode2.ScanData.AuxiliaryData.ImageFormat">
            <summary>
            The format of the image in auxiliary data buffer.
            Only valid for AUX_FORMATS.IMAGE_DATA.
            </summary>
        </member>
        <member name="P:Symbol.Barcode2.ScanData.AuxiliaryData.ImageHeight">
            <summary>
            The height of the image in auxiliary data buffer.
            Only valid for AUX_FORMATS.IMAGE_DATA.
            </summary>
        </member>
        <member name="P:Symbol.Barcode2.ScanData.AuxiliaryData.ImageWidth">
            <summary>
            The width of the image in auxiliary data buffer.
            Only valid for AUX_FORMATS.IMAGE_DATA.
            </summary>
        </member>
        <member name="T:Symbol.Barcode2.StatusData">
            <summary>
            The StatusData class provides access to notification information. 
            </summary>
        </member>
        <member name="P:Symbol.Barcode2.StatusData.EventType">
            <summary>
            Retrieves the Barcode Reader event type.  
            </summary>
        </member>
        <member name="P:Symbol.Barcode2.StatusData.State">
            <summary>
            Retrieves the Barcode Reader event state.  
            </summary>
        </member>
        <member name="P:Symbol.Barcode2.StatusData.DocCapState">
            <summary>
            Retrieves the Doc Capture state.
            </summary>
        </member>
        <member name="P:Symbol.Barcode2.StatusData.Text">
            <summary>
            Retrieves the Barcode Reader state in string form.  
            </summary>
        </member>
        <member name="T:Symbol.Barcode2.ScanDataCollection">
            <summary>
            This Object is a IEnumerable collection of ScanData Objects used to store multiple scans
            </summary>
        </member>
        <member name="M:Symbol.Barcode2.ScanDataCollection.#ctor">
            <summary>
            Constructor to instantiate ScanDataCollection object
            </summary>
        </member>
        <member name="M:Symbol.Barcode2.ScanDataCollection.Clear">
            <summary>
            Clear all contents in the collection
            </summary>
        </member>
        <member name="M:Symbol.Barcode2.ScanDataCollection.Clone">
            <summary>
            Clone the collection
            </summary>
            <returns>An object</returns>
        </member>
        <member name="M:Symbol.Barcode2.ScanDataCollection.Add(Symbol.Barcode2.ScanData)">
            <summary>
            Add a new ScanData object to the collection
            </summary>
            <param name="item"></param>
        </member>
        <member name="M:Symbol.Barcode2.ScanDataCollection.Remove(System.Int32)">
            <summary>
            Remove a ScanData object from the collection
            </summary>
            <param name="index">position to remove scandata object from</param>
        </member>
        <member name="M:Symbol.Barcode2.ScanDataCollection.GetEnumerator">
            <summary>
            Returns an enumerator that iterates through the ScanData collection.
            </summary>
            <returns>ScanData collection</returns>
        </member>
        <member name="P:Symbol.Barcode2.ScanDataCollection.Length">
            <summary>
            Returns the size of the collection.
            </summary>
        </member>
        <member name="P:Symbol.Barcode2.ScanDataCollection.GetFirst">
            <summary>
            Returns the first ScanData object in the collection
            </summary>
        </member>
        <member name="P:Symbol.Barcode2.ScanDataCollection.Current">
            <summary>
            Returns the Current Scandata object in the collection
            </summary>
        </member>
        <member name="T:Symbol.Barcode2.Devices">
            <summary>
            The Devices class provides information about all the available scanners in the system. 
            </summary>
        </member>
        <member name="P:Symbol.Barcode2.Devices.SupportedDevices">
            <summary>
            The SupportedDevices property can be used to obtain a list of all available hardware for use with the barcode classes.  
            </summary>
        </member>
        <member name="T:Symbol.Barcode2.Device">
            <summary>
            The Device class provides information about a specific scanner in the system. 
            </summary>
        </member>
        <member name="P:Symbol.Barcode2.Device.DeviceName">
            <summary>
            Provides read/write access to the device name of the device.
            </summary>
            <value>
            A string that contains the device name.
            </value>
        </member>
        <member name="P:Symbol.Barcode2.Device.FriendlyName">
            <summary>
            Provides read/write access to the friendly name of the device.
            </summary>
            <value>
            A string that contains the friendly name.
            </value>
        </member>
        <member name="P:Symbol.Barcode2.Device.PortName">
            <summary>
            Provides read/write access to the portname of the device.
            </summary>
            <value>
            A string that contains the portname.
            </value>
        </member>
        <member name="P:Symbol.Barcode2.Device.DeviceType">
            <summary>
            The type of scanning device.
            </summary>
        </member>
        <member name="T:Symbol.Barcode2.Resource">
            <summary>
              A strongly-typed resource class, for looking up localized strings, etc.
            </summary>
        </member>
        <member name="P:Symbol.Barcode2.Resource.ResourceManager">
            <summary>
              Returns the cached ResourceManager instance used by this class.
            </summary>
        </member>
        <member name="P:Symbol.Barcode2.Resource.Culture">
            <summary>
              Overrides the current thread's CurrentUICulture property for all
              resource lookups using this strongly typed resource class.
            </summary>
        </member>
        <member name="P:Symbol.Barcode2.Resource.CANCEL_DURING_BUFFER">
            <summary>
              Looks up a localized string similar to Cannot perform ScanCancel() in Buffered Scans mode.
            </summary>
        </member>
        <member name="P:Symbol.Barcode2.Resource.DEVICENOTUNKNOWN">
            <summary>
              Looks up a localized string similar to Device type cannot be UNKNOWN.
            </summary>
        </member>
        <member name="P:Symbol.Barcode2.Resource.E_SCN_ALREADYENABLED">
            <summary>
              Looks up a localized string similar to An attempt was made to enable scanning when scanning was already enabled..
            </summary>
        </member>
        <member name="P:Symbol.Barcode2.Resource.E_SCN_ALREADYINUSE">
            <summary>
              Looks up a localized string similar to A requested scanner resource is already in use..
            </summary>
        </member>
        <member name="P:Symbol.Barcode2.Resource.E_SCN_ALREADYSTARTED">
            <summary>
              Looks up a localized string similar to An attempt was made to start the device when the device was already started..
            </summary>
        </member>
        <member name="P:Symbol.Barcode2.Resource.E_SCN_BUFFERSIZEIN">
            <summary>
              Looks up a localized string similar to The size of the buffer passed as an input to DeviceIoControl is less than sizeof(STRUCT_INFO) or is less than the size specified in StructInfo.dwUsed..
            </summary>
        </member>
        <member name="P:Symbol.Barcode2.Resource.E_SCN_BUFFERSIZEOUT">
            <summary>
              Looks up a localized string similar to The size of the buffer passed as an output to DeviceIoControl is less than sizeof(STRUCT_INFO) or is less than the size specified in StructInfo.dwAllocated..
            </summary>
        </member>
        <member name="P:Symbol.Barcode2.Resource.E_SCN_BUFFERTOOSMALL">
            <summary>
              Looks up a localized string similar to Data buffer is too small for incoming data..
            </summary>
        </member>
        <member name="P:Symbol.Barcode2.Resource.E_SCN_CANTGETDEFAULTS">
            <summary>
              Looks up a localized string similar to The default parameters could not be obtained from the physical device driver (PDD)..
            </summary>
        </member>
        <member name="P:Symbol.Barcode2.Resource.E_SCN_CANTLOADCOMPRESSIONDLL">
            <summary>
              Looks up a localized string similar to The Image Compression DLL can not be loaded..
            </summary>
        </member>
        <member name="P:Symbol.Barcode2.Resource.E_SCN_CANTLOADDECODERDLL">
            <summary>
              Looks up a localized string similar to The specified decoder DLL can not be loaded..
            </summary>
        </member>
        <member name="P:Symbol.Barcode2.Resource.E_SCN_CANTLOADDEVICE">
            <summary>
              Looks up a localized string similar to The physical device driver (PDD) could not be loaded..
            </summary>
        </member>
        <member name="P:Symbol.Barcode2.Resource.E_SCN_CANTLOADHALDLL">
            <summary>
              Looks up a localized string similar to The Image HAL DLL can not be loaded..
            </summary>
        </member>
        <member name="P:Symbol.Barcode2.Resource.E_SCN_CANTOPENREGKEY">
            <summary>
              Looks up a localized string similar to A required registry key could not be opened..
            </summary>
        </member>
        <member name="P:Symbol.Barcode2.Resource.E_SCN_CANTREADREGVAL">
            <summary>
              Looks up a localized string similar to A required registry value could not be read..
            </summary>
        </member>
        <member name="P:Symbol.Barcode2.Resource.E_SCN_CANTSTARTDEVICE">
            <summary>
              Looks up a localized string similar to The device could not be started..
            </summary>
        </member>
        <member name="P:Symbol.Barcode2.Resource.E_SCN_CREATEEVENT">
            <summary>
              Looks up a localized string similar to Unable to create a required event..
            </summary>
        </member>
        <member name="P:Symbol.Barcode2.Resource.E_SCN_CREATETHREAD">
            <summary>
              Looks up a localized string similar to Unable to create a required thread..
            </summary>
        </member>
        <member name="P:Symbol.Barcode2.Resource.E_SCN_DEVICEDISABLED">
            <summary>
              Looks up a localized string similar to Camera security policy not allowing to use camera..
            </summary>
        </member>
        <member name="P:Symbol.Barcode2.Resource.E_SCN_DEVICEFAILURE">
            <summary>
              Looks up a localized string similar to Required device is not present, already in use or not functioning properly..
            </summary>
        </member>
        <member name="P:Symbol.Barcode2.Resource.E_SCN_DUPLICATESYMBOL">
            <summary>
              Looks up a localized string similar to The scanned macro symbol has already been scanned into the current macro sequence..
            </summary>
        </member>
        <member name="P:Symbol.Barcode2.Resource.E_SCN_EXCEPTION">
            <summary>
              Looks up a localized string similar to An exception occurred while trying to call the scanner driver..
            </summary>
        </member>
        <member name="P:Symbol.Barcode2.Resource.E_SCN_I2CFAILURE">
            <summary>
              Looks up a localized string similar to I2C communication failed..
            </summary>
        </member>
        <member name="P:Symbol.Barcode2.Resource.E_SCN_INVALIDARG">
            <summary>
              Looks up a localized string similar to A passed argument is out of range..
            </summary>
        </member>
        <member name="P:Symbol.Barcode2.Resource.E_SCN_INVALIDDECODERDLL">
            <summary>
              Looks up a localized string similar to At least one API is missing in Decoder DLL..
            </summary>
        </member>
        <member name="P:Symbol.Barcode2.Resource.E_SCN_INVALIDDEVICE">
            <summary>
              Looks up a localized string similar to The physical device driver (PDD) DLL did not contain the required entry points..
            </summary>
        </member>
        <member name="P:Symbol.Barcode2.Resource.E_SCN_INVALIDDVCCONTEXT">
            <summary>
              Looks up a localized string similar to An invalid device context ID was used..
            </summary>
        </member>
        <member name="P:Symbol.Barcode2.Resource.E_SCN_INVALIDHALDLL">
            <summary>
              Looks up a localized string similar to At least one API function is missing in Image HAL DLL..
            </summary>
        </member>
        <member name="P:Symbol.Barcode2.Resource.E_SCN_INVALIDHANDLE">
            <summary>
              Looks up a localized string similar to An invalid handle was passed to a function..
            </summary>
        </member>
        <member name="P:Symbol.Barcode2.Resource.E_SCN_INVALIDIOCTRL">
            <summary>
              Looks up a localized string similar to The control code passed to DeviceIoControl is invalid..
            </summary>
        </member>
        <member name="P:Symbol.Barcode2.Resource.E_SCN_INVALIDLICENSE">
            <summary>
              Looks up a localized string similar to The platform does not have a valid license key..
            </summary>
        </member>
        <member name="P:Symbol.Barcode2.Resource.E_SCN_INVALIDOPENCONTEXT">
            <summary>
              Looks up a localized string similar to An attempt was made to access an invalid open context..
            </summary>
        </member>
        <member name="P:Symbol.Barcode2.Resource.E_SCN_INVALIDPARAM">
            <summary>
              Looks up a localized string similar to The value of a parameter either passed as an argument to a function or as a member of a structure is out of range or conflicts with other parameters..
            </summary>
        </member>
        <member name="P:Symbol.Barcode2.Resource.E_SCN_INVALIDRESPONSE">
            <summary>
              Looks up a localized string similar to Invalid response received from scanner..
            </summary>
        </member>
        <member name="P:Symbol.Barcode2.Resource.E_SCN_INVALIDSCANBUFFER">
            <summary>
              Looks up a localized string similar to Attempt to access fields of an invalid scan buffer..
            </summary>
        </member>
        <member name="P:Symbol.Barcode2.Resource.E_SCN_INVALIDSYMBOL">
            <summary>
              Looks up a localized string similar to The symbol&apos;s format is invalid..
            </summary>
        </member>
        <member name="P:Symbol.Barcode2.Resource.E_SCN_MISSING_CONFIG">
            <summary>
              Looks up a localized string similar to Missing configuration details..
            </summary>
        </member>
        <member name="P:Symbol.Barcode2.Resource.E_SCN_MISSINGFIELD">
            <summary>
              Looks up a localized string similar to The size of a structure specified in a StructInfo is too small to contain a required field..
            </summary>
        </member>
        <member name="P:Symbol.Barcode2.Resource.E_SCN_NOFEEDBACK">
            <summary>
              Looks up a localized string similar to Attempt to perform physical device driver (PDD) feedback with no feedback capabilities..
            </summary>
        </member>
        <member name="P:Symbol.Barcode2.Resource.E_SCN_NOMOREITEMS">
            <summary>
              Looks up a localized string similar to No more items are available to be returned from SCAN_FindFirst/SCAN_FindNext..
            </summary>
        </member>
        <member name="P:Symbol.Barcode2.Resource.E_SCN_NOTENABLED">
            <summary>
              Looks up a localized string similar to An attempt was made to access the scanner device and it was not enabled..
            </summary>
        </member>
        <member name="P:Symbol.Barcode2.Resource.E_SCN_NOTENOUGHMEMORY">
            <summary>
              Looks up a localized string similar to An attempt to allocate memory failed..
            </summary>
        </member>
        <member name="P:Symbol.Barcode2.Resource.E_SCN_NOTINITIALIZED">
            <summary>
              Looks up a localized string similar to The driver was accessed before a successful initialization..
            </summary>
        </member>
        <member name="P:Symbol.Barcode2.Resource.E_SCN_NOTINSEQUENCE">
            <summary>
              Looks up a localized string similar to The scanned macro symbol is not part of the current macro sequence..
            </summary>
        </member>
        <member name="P:Symbol.Barcode2.Resource.E_SCN_NOTINUSE">
            <summary>
              Looks up a localized string similar to The specified scanner resource was not allocated..
            </summary>
        </member>
        <member name="P:Symbol.Barcode2.Resource.E_SCN_NOTSTARTED">
            <summary>
              Looks up a localized string similar to An attempt was made to use or stop the scanner device and it was not started..
            </summary>
        </member>
        <member name="P:Symbol.Barcode2.Resource.E_SCN_NOTSUPPORTED">
            <summary>
              Looks up a localized string similar to Version of function not supported (e.g. ANSI vs. UNICODE)..
            </summary>
        </member>
        <member name="P:Symbol.Barcode2.Resource.E_SCN_NULLPTR">
            <summary>
              Looks up a localized string similar to A NULL pointer was passed for a required argument..
            </summary>
        </member>
        <member name="P:Symbol.Barcode2.Resource.E_SCN_OPENINGACTIVEKEY">
            <summary>
              Looks up a localized string similar to An error occurred opening the active driver registry key..
            </summary>
        </member>
        <member name="P:Symbol.Barcode2.Resource.E_SCN_OPENINGPARAMKEY">
            <summary>
              Looks up a localized string similar to An error occurred opening the registry key containing the driver&apos;s settings..
            </summary>
        </member>
        <member name="P:Symbol.Barcode2.Resource.E_SCN_READCANCELLED">
            <summary>
              Looks up a localized string similar to A read request was cancelled..
            </summary>
        </member>
        <member name="P:Symbol.Barcode2.Resource.E_SCN_READINCOMPATIBLE">
            <summary>
              Looks up a localized string similar to Attempt to submit a read that is incompatible with reads already queued..
            </summary>
        </member>
        <member name="P:Symbol.Barcode2.Resource.E_SCN_READINGACTIVEKEY">
            <summary>
              Looks up a localized string similar to An error occurred reading the active driver registry key..
            </summary>
        </member>
        <member name="P:Symbol.Barcode2.Resource.E_SCN_READINGPARAMKEY">
            <summary>
              Looks up a localized string similar to An error occurred reading the registry key containing the driver&apos;s settings..
            </summary>
        </member>
        <member name="P:Symbol.Barcode2.Resource.E_SCN_READNOTPENDING">
            <summary>
              Looks up a localized string similar to Attempt to cancel a read that is not pending..
            </summary>
        </member>
        <member name="P:Symbol.Barcode2.Resource.E_SCN_READPENDING">
            <summary>
              Looks up a localized string similar to Attempt to start a read when one is pending..
            </summary>
        </member>
        <member name="P:Symbol.Barcode2.Resource.E_SCN_READTIMEOUT">
            <summary>
              Looks up a localized string similar to A read request timed out..
            </summary>
        </member>
        <member name="P:Symbol.Barcode2.Resource.E_SCN_RSMATTRIBINVALID">
            <summary>
              Looks up a localized string similar to The specific attribute number is not  valid on scanner..
            </summary>
        </member>
        <member name="P:Symbol.Barcode2.Resource.E_SCN_STRUCTSIZE">
            <summary>
              Looks up a localized string similar to A STRUCT_INFO structure field is invalid. Either dwAllocated or dwUsed is less than the size of STRUCT_INFO or dwUsed is greater than dwAllocated..
            </summary>
        </member>
        <member name="P:Symbol.Barcode2.Resource.E_SCN_SUCCESS">
            <summary>
              Looks up a localized string similar to The function completed successfully.
            </summary>
        </member>
        <member name="P:Symbol.Barcode2.Resource.E_SCN_WIN32ERROR">
            <summary>
              Looks up a localized string similar to A scanner API function failed with a non-scanner API error code. Call GetLastError to get extended error code..
            </summary>
        </member>
        <member name="P:Symbol.Barcode2.Resource.E_SCN_WRONGSTATE">
            <summary>
              Looks up a localized string similar to The requested operation is inconsistent with the current state of the device..
            </summary>
        </member>
        <member name="P:Symbol.Barcode2.Resource.ERROR_CREATEEVENT">
            <summary>
              Looks up a localized string similar to Failure in creating event.
            </summary>
        </member>
        <member name="P:Symbol.Barcode2.Resource.ERROR_RESETEVENT">
            <summary>
              Looks up a localized string similar to Failure in resetting event.
            </summary>
        </member>
        <member name="P:Symbol.Barcode2.Resource.ERROR_SETEVENT">
            <summary>
              Looks up a localized string similar to Failure in setting event.
            </summary>
        </member>
        <member name="P:Symbol.Barcode2.Resource.INVALID_RSMATTRIB_RANGE">
            <summary>
              Looks up a localized string similar to Invalid value. Allowed range for ReconnectAttempts: Minimum = 0 and Maximum = 0xFFFF.
            </summary>
        </member>
        <member name="P:Symbol.Barcode2.Resource.INVALIDDEVICE">
            <summary>
              Looks up a localized string similar to Invalid device. .
            </summary>
        </member>
        <member name="P:Symbol.Barcode2.Resource.INVALIDPARAM">
            <summary>
              Looks up a localized string similar to The parameter passed is not valid. It cannot be null or empty..
            </summary>
        </member>
        <member name="P:Symbol.Barcode2.Resource.NEGATIVENUM">
            <summary>
              Looks up a localized string similar to Negative values are not allowed. .
            </summary>
        </member>
        <member name="T:Symbol.Barcode2.Config">
             <summary>
             The Config class holds access to all the parameters inside the interface, scanner, reader and decoder objects. Thru this object all setting and parameters can be accessed and modified. 
             </summary>
             <remarks>
             When changes are made to either the decoder class, the reader class or scanner class it is optional to call the Set() method. When any Scan call is made the parameters are automatically set before the scan is made. Attempting to Set any changes in config during a pending scan will result in an exception.
            </remarks>
        </member>
        <member name="M:Symbol.Barcode2.Config.SetAllParameters">
            <summary>
            This method Will set all the parameters in the Scanner, Decoder, Reader and Interface Classes 
            individually if they have been modified.
            </summary>
        </member>
        <member name="M:Symbol.Barcode2.Config.RestoreAllDefaultParameters">
            <summary>
            Restores all default values for decoder, scanner, reader and interface parameters.
            </summary>
        </member>
        <member name="M:Symbol.Barcode2.Config.GetAllParameters">
            <summary>
            This method Will Get all the parameters in the Scanner, Decoder, Reader and Interface Classes. 
            </summary>
        </member>
        <member name="P:Symbol.Barcode2.Config.ScanDataFormat">
            <summary>
            Data format for the data that is read from barcode scanner. Default is Text
            </summary>
        </member>
        <member name="P:Symbol.Barcode2.Config.ScanDataSize">
            <summary>
            Length of ScanData memory buffer. Default is 55.
            </summary>
            
        </member>
        <member name="P:Symbol.Barcode2.Config.TriggerMode">
            <summary>
            Specifies the trigger mode for the scanner. It is set to HARD by default.
            Refer to the <see cref="T:Symbol.Barcode2.TRIGGERMODES"/> enumeration for more information.
            </summary>
        </member>
        <member name="P:Symbol.Barcode2.Config.Interface">
            <summary>
            The Interface property holds the parameters for the interface to the attached bar code reader.
            Interface parameters are global to all reads on all open handles on the same scanner.
            NOTE: When calling Enable() after Disable(), all the latest configuration parameter values (Config.Decoders, Config.Scanner, 
            Config.Reader and Config.Interface) will be set automatically. If the parameters need to be reset to default values,
            either Config.RestoreAllDefaultParameters() or RestoreDefaults() (for the correpsonding parameter) can be called.
            </summary>
        </member>
        <member name="P:Symbol.Barcode2.Config.Scanner">
            <summary>
            The Scanner property provides access to scanning parameters that are available for 
            all decoders.
            NOTE: When calling Enable() after Disable(), all the latest configuration parameter values (Config.Decoders, Config.Scanner, 
            Config.Reader and Config.Interface) will be set automatically. If the parameters need to be reset to default values,
            either Config.RestoreAllDefaultParameters() or RestoreDefaults() (for the correpsonding parameter) can be called.
            </summary>
        </member>
        <member name="P:Symbol.Barcode2.Config.Reader">
            <summary>
            The Reader property holds the parameters for the attached bar code reader. 
            Reader parameters are global to all reads on all open handles on the same scanner.
            NOTE: When calling Enable() after Disable(), all the latest configuration parameter values (Config.Decoders, Config.Scanner, 
            Config.Reader and Config.Interface) will be set automatically. If the parameters need to be reset to default values,
            either Config.RestoreAllDefaultParameters() or RestoreDefaults() (for the correpsonding parameter) can be called.
            </summary>
        </member>
        <member name="P:Symbol.Barcode2.Config.Decoders">
            <summary>
            The Decoders property contains decoder parameters that are used by
            multiple decoder symbologies.
            The property provides access to such decoder parameters
            as Enabled and IsSupported, among others. 
            NOTE: When calling Enable() after Disable(), all the latest configuration parameter values (Config.Decoders, Config.Scanner, 
            Config.Reader and Config.Interface) will be set automatically. If the parameters need to be reset to default values,
            either Config.RestoreAllDefaultParameters() or RestoreDefaults() (for the correpsonding parameter) can be called.
            </summary>
        </member>
        <member name="P:Symbol.Barcode2.Config.MaxScanDataCollection">
            <summary>
            This property holds the maximum number of ScanData objects in the OnScan callback or GetScannedData().
            If set to -1, the callback will return all that is in the collection. The default value is 1.
            </summary>
        </member>
        <member name="T:Symbol.Barcode2.Config.ReaderClass">
            <summary>
            The ReaderClass structure holds the parameters for the attached bar code reader. 
            Reader parameters are global to all reads on all open handles on the same scanner.
            NOTE: When calling Enable() after Disable(), all the latest configuration parameter values (Config.Decoders, Config.Scanner, 
            Config.Reader and Config.Interface) will be set automatically. If the parameters need to be reset to default values,
            either Config.RestoreAllDefaultParameters() or RestoreDefaults() (for the correpsonding parameter) can be called.
            </summary>
        </member>
        <member name="M:Symbol.Barcode2.Config.ReaderClass.RestoreDefaults">
            <summary>
            Restores default/saved parameters
            </summary>
        </member>
        <member name="M:Symbol.Barcode2.Config.ReaderClass.Get">
            <summary>
            This method will Get the current Reader Parameter setting from the device.
            </summary>
        </member>
        <member name="M:Symbol.Barcode2.Config.ReaderClass.Set">
            <summary>
            This method will Set the changes made to the Reader Parameter and must be called in 
            order for any changes to the parameters to take affect
            </summary>
        </member>
        <member name="P:Symbol.Barcode2.Config.ReaderClass.ReaderType">
            <summary>
            Describes the bar code reader type these parameters apply to. Read only. 
            This value determines the values for ReaderSpecific  
            </summary>
        </member>
        <member name="P:Symbol.Barcode2.Config.ReaderClass.ReaderSpecific">
            <summary>
            The ReaderSpecific holds parameters for one reader type. 
            The structure of ReaderSpecific depends on the value of ReaderType.  
            </summary>
        </member>
        <member name="T:Symbol.Barcode2.Config.ReaderClass.ReaderSpecificClass">
            <summary>
            The ReaderSpecificClass holds parameters for one reader type. 
            The structure of ReaderSpecific depends on the value of ReaderType.  
            </summary>
        </member>
        <member name="P:Symbol.Barcode2.Config.ReaderClass.ReaderSpecificClass.LaserSpecific">
            <summary>
            The LaserSpecific property holds laser specific reader parameters.  
            </summary>
        </member>
        <member name="P:Symbol.Barcode2.Config.ReaderClass.ReaderSpecificClass.ImagerSpecific">
            <summary>
            The ImagerSpecific property holds imager specific reader parameters. 
            </summary>
        </member>
        <member name="T:Symbol.Barcode2.Config.ReaderClass.ReaderSpecificClass.LaserSpecificClass">
            <summary>
            The LaserSpecific class holds laser specific reader parameters.
            </summary>
        </member>
        <member name="P:Symbol.Barcode2.Config.ReaderClass.ReaderSpecificClass.LaserSpecificClass.AimType">
            <summary>
            Describes the type of aiming to use.
            </summary>
        </member>
        <member name="P:Symbol.Barcode2.Config.ReaderClass.ReaderSpecificClass.LaserSpecificClass.AimDuration">
            <summary>
            Duration in milliseconds for timed aim modes.
            </summary>
        </member>
        <member name="P:Symbol.Barcode2.Config.ReaderClass.ReaderSpecificClass.LaserSpecificClass.AimMode">
            <summary>
            Describes the mode of aiming to use.
            NOTE: This property is deprecated and it will no longer be supported.
            It will be removed in a future release.
            </summary>
        </member>
        <member name="P:Symbol.Barcode2.Config.ReaderClass.ReaderSpecificClass.LaserSpecificClass.BeamWidth">
            <summary>
            Specifies the width of the beam on the device if it's supported.
            NOTE: User can query the Barcode2.DeviceInfo.SupportedBeamWidths property to see what beam widths are supported on the device.
            </summary>
        </member>
        <member name="P:Symbol.Barcode2.Config.ReaderClass.ReaderSpecificClass.LaserSpecificClass.RasterMode">
            <summary>
            Describes the type of vertical rastering to use.
            </summary>
        </member>
        <member name="P:Symbol.Barcode2.Config.ReaderClass.ReaderSpecificClass.LaserSpecificClass.BeamTimer">
            <summary>
            Maximum laser on time in milliseconds. A value of 0 means infinite timeout.
            </summary>
        </member>
        <member name="P:Symbol.Barcode2.Config.ReaderClass.ReaderSpecificClass.LaserSpecificClass.ControlScanLed">
            <summary>
            Flag to enable LED decode notification by the scanner device (disables notification in the mobile computer). Currently not used.
            </summary>
        </member>
        <member name="P:Symbol.Barcode2.Config.ReaderClass.ReaderSpecificClass.LaserSpecificClass.ScanLedLogicLevel">
            <summary>
            Logic level to use when controlling scanner LED. Currently not used.
            </summary>
        </member>
        <member name="P:Symbol.Barcode2.Config.ReaderClass.ReaderSpecificClass.LaserSpecificClass.KlasseEinsEnable">
            <summary>
            Flag to enable Klasse Eins laser on time function.
            </summary>
        </member>
        <member name="P:Symbol.Barcode2.Config.ReaderClass.ReaderSpecificClass.LaserSpecificClass.BidirRedundancy">
            <summary>
            Flag to enable birdirectional redundancy.
            </summary>
        </member>
        <member name="P:Symbol.Barcode2.Config.ReaderClass.ReaderSpecificClass.LaserSpecificClass.LinearSecurityLevel">
            <summary>
            Describes the linear security level used during decoding.
            </summary>
        </member>
        <member name="P:Symbol.Barcode2.Config.ReaderClass.ReaderSpecificClass.LaserSpecificClass.PointerTimer">
            <summary>
            Maximum laser on time in milliseconds for laser pointer. A value of 0 means infinite timeout.
            </summary>
        </member>
        <member name="P:Symbol.Barcode2.Config.ReaderClass.ReaderSpecificClass.LaserSpecificClass.RasterHeight">
            <summary>
            Describes, as a percentage, the vertical rastering height to use when the RasterMode is RASTER_MODE_OPEN_ALWAYS.
            </summary>
        </member>
        <member name="P:Symbol.Barcode2.Config.ReaderClass.ReaderSpecificClass.LaserSpecificClass.DBPMode">
            <summary>
            Describes what type of DBP (Digital Bar Pulse) is being produced by the scan engine. 
            If the product does not support I2C or if using an older engine the default value for DBP Mode is DBP_NORMAL.
            An attempt to change this mode to DBP_COMPOSITE will result in an E_SCN_NOTSUPPORTED error.
            </summary>
        </member>
        <member name="P:Symbol.Barcode2.Config.ReaderClass.ReaderSpecificClass.LaserSpecificClass.SameSymbolTimeout">
            <summary>
            This parameter is used to prevent the scanner from decoding the same symbol within 
            this time interval (applicable only when AimType is set to AIM_TYPE_CONTINUOUS_READ). 
            The continuous read mode is used to perform rapid scans. In order to prevent decoding the 
            same barcode twice, this parameter is set to an appropriate interval (in milliseconds).
            A value of 0 means no interval is required between two successive reads
            NOTE: Applicable only when AimType is set to AIM_TYPE_CONTINUOUS_READ.
            </summary>
        </member>
        <member name="P:Symbol.Barcode2.Config.ReaderClass.ReaderSpecificClass.LaserSpecificClass.DifferentSymbolTimeout">
            <summary>
            This parameter is used to prevent the scanner from decoding another symbol within 
            this time interval (applicable only when AimType is set to AIM_TYPE_CONTINUOUS_READ). 
            The continuous read mode is used to perform rapid scans. In order to prevent decoding 
            too quickly and have enough time to aim before decoding the next barcode, 
            this parameter is set to an appropriate interval (in milliseconds).
            A value of 0 means no interval is required between two successive reads
            NOTE: Applicable only when AimType is set to AIM_TYPE_CONTINUOUS_READ.
            </summary>
        </member>
        <member name="P:Symbol.Barcode2.Config.ReaderClass.ReaderSpecificClass.LaserSpecificClass.AdaptiveScanning">
            <summary>
            Specifies the adaptive scanning mode. 
            Adaptive Scanning is enabled by default on devices that support this feature. 
            Please refer to the remarks section below for information about Adaptive Scanning.
            <para>
            NOTE: User can query the Barcode2.DeviceInfo.IsAdaptiveScanningSupported property 
            to check if adaptive scanning feature is supported on the device.
            </para>
            </summary>
            <remarks>
            ADAPTIVE SCANNING:
            <para>
            The high performance and long working range scan engines such as SE960 will support 
            the adaptive scanning feature to enable scanning high and low density bar codes. 
            When adaptive scanning is enabled, the scan engine will automatically toggle 
            between 2 scan angles, wide and narrow, allowing the scan engine to decode barcodes 
            as close as 10 inches (high density) and as far as 100 inches (low density).
            </para>
            <para>
            To determine the type of the scan engine on your device, click on the 
            Start->System->Settings->System Info and select “ConfigInfo” tab. 
            The "Scanner" field under this tab provides the name of the scan engine. 
            </para>
            </remarks>
        </member>
        <member name="T:Symbol.Barcode2.Config.ReaderClass.ReaderSpecificClass.ImagerSpecificClass">
            <summary>
            The ImagerSpecific class holds imager specific reader parameters.
            </summary>
        </member>
        <member name="P:Symbol.Barcode2.Config.ReaderClass.ReaderSpecificClass.ImagerSpecificClass.AimType">
            <summary>
            Describes the type of aiming to use.
            </summary>
        </member>
        <member name="P:Symbol.Barcode2.Config.ReaderClass.ReaderSpecificClass.ImagerSpecificClass.AimDuration">
            <summary>
            Duration in milliseconds for timed aim modes.
            </summary>
        </member>
        <member name="P:Symbol.Barcode2.Config.ReaderClass.ReaderSpecificClass.ImagerSpecificClass.AimMode">
            <summary>
            Describes the mode of aiming to use.
            </summary>
        </member>
        <member name="P:Symbol.Barcode2.Config.ReaderClass.ReaderSpecificClass.ImagerSpecificClass.BeamTimer">
            <summary>
            Maximum Imager ON time in milliseconds. A value of 0 means infinite timeout.
            </summary>
        </member>
        <member name="P:Symbol.Barcode2.Config.ReaderClass.ReaderSpecificClass.ImagerSpecificClass.PointerTimer">
            <summary>
            Maximum beam timer in milliseconds for pointer mode. A value of 0 means infinite timeout.
            </summary>
        </member>
        <member name="P:Symbol.Barcode2.Config.ReaderClass.ReaderSpecificClass.ImagerSpecificClass.ImageCaptureTimeout">
            <summary>
            Timeout for image capture (in milliseconds).
            </summary>
        </member>
        <member name="P:Symbol.Barcode2.Config.ReaderClass.ReaderSpecificClass.ImagerSpecificClass.ImageCompressionTimeout">
            <summary>
            Timeout for image compression and processing (in milliseconds).
            </summary>
        </member>
        <member name="P:Symbol.Barcode2.Config.ReaderClass.ReaderSpecificClass.ImagerSpecificClass.LinearSecurityLevel">
            <summary>
            Describes the linear security level used during decoding.
            </summary>
        </member>
        <member name="P:Symbol.Barcode2.Config.ReaderClass.ReaderSpecificClass.ImagerSpecificClass.FocusMode">
            <summary>
            Focus mode to use.
            </summary>
        </member>
        <member name="P:Symbol.Barcode2.Config.ReaderClass.ReaderSpecificClass.ImagerSpecificClass.FocusPosition">
            <summary>
            Focus position to use.
            </summary>
        </member>
        <member name="P:Symbol.Barcode2.Config.ReaderClass.ReaderSpecificClass.ImagerSpecificClass.PoorQuality1DMode">
            <summary>
            This parameter allows poor quality 1D barcodes to be read, 
            BUT adversely affecting the overall decoding performance.
            </summary>
        </member>
        <member name="P:Symbol.Barcode2.Config.ReaderClass.ReaderSpecificClass.ImagerSpecificClass.PicklistMode">
            <summary>
            This parameter allows the imager to decode only the barcode that is directly under the cross-hair/center
            of the reticle. This feature is most useful in applications where multiple barcodes may appear in the 
            field of view during a decode session and only one of them is targeted for decode.  When enabled, 
            PicklistModeEx will override AimMode if no aiming is chosen and use the AIM_MODE_RETICLE mode.  When enabled, 
            PicklistModeEx may adversely affect overall decoding performance. Also read note on interaction between the 
            picklist modes and the viewfinder modes (only for imagers that support viewfinder capability).
            PICKLIST_DISABLED – Disables picklist mode, so any bar code within the field of view can be decoded. 
            PICKLIST_HARDWARE_RETICLE – Enables picklist mode so that only the barcode under the projected reticle can be decoded. If the imager does not support a projected reticle then the behavior is same as that of PICKLIST_SOFTWARE_RETICLE.
            </summary>
        </member>
        <member name="P:Symbol.Barcode2.Config.ReaderClass.ReaderSpecificClass.ImagerSpecificClass.DPMMode">
            <summary>
            This parameter allows Direct Part Marking (DPM) bar codes to be read but may adversely affect 
            overall decoding performance. Direct Part Marking is a way of stamping bar codes directly on 
            physical objects. 
            Support for this feature is available on DPM terminals only. If this feature is not available 
            and user attempts to enable it, an error (E_SCN_NOTSUPPORTED) will result.
            </summary>
            <remarks>This feature cannot be turned on in conjunction with Picklist as both these 
            modes are mutually exclusive. An attempt to turn on both will result in an error 
            (E_SCN_NOTSUPPORTED).
            </remarks>
        </member>
        <member name="P:Symbol.Barcode2.Config.ReaderClass.ReaderSpecificClass.ImagerSpecificClass.IlluminationMode">
            <summary>
            Illumination Mode to use.
            </summary>
        </member>
        <member name="P:Symbol.Barcode2.Config.ReaderClass.ReaderSpecificClass.ImagerSpecificClass.VFPosition">
            <summary>
            Specifies the coordinates of the viewfinder on the screen.
            When scanning is enabled via an imager that supports the viewfinder,
             the user can enable the viewfinder which helps with locating the barcode.
             The images displayed on the viewfinder will be scaled to fit in the area specified.
            </summary>
        </member>
        <member name="P:Symbol.Barcode2.Config.ReaderClass.ReaderSpecificClass.ImagerSpecificClass.VFMode">
            <summary>
            Viewfinder modes supported for scanning.
            </summary>
        </member>
        <member name="P:Symbol.Barcode2.Config.ReaderClass.ReaderSpecificClass.ImagerSpecificClass.VFFeedback">
            <summary>
            This parameter allows selection of the different feedback parameters on a successful decode.
            </summary>
        </member>
        <member name="P:Symbol.Barcode2.Config.ReaderClass.ReaderSpecificClass.ImagerSpecificClass.VFFeedbackTime">
            <summary>
            Time (in milli seconds) for which the visual display selected by VFFeedback is shown. 
            </summary>
        </member>
        <member name="P:Symbol.Barcode2.Config.ReaderClass.ReaderSpecificClass.ImagerSpecificClass.Inverse1DMode">
            <summary>
            This parameter allows the user to select decoding on inverse 1D barcodes.
            </summary>
        </member>
        <member name="P:Symbol.Barcode2.Config.ReaderClass.ReaderSpecificClass.ImagerSpecificClass.SameSymbolTimeout">
            <summary>
            This parameter is used to prevent the scanner from decoding the same symbol within 
            this time interval (applicable only when AimType is set to AIM_TYPE_CONTINUOUS_READ). 
            The continuous read mode is used to perform rapid scans. In order to prevent decoding the 
            same barcode twice, this parameter is set to an appropriate interval (in milliseconds).
            A value of 0 means no interval is required between two successive reads
            NOTE: Applicable only when AimType is set to AIM_TYPE_CONTINUOUS_READ.
            </summary>
        </member>
        <member name="P:Symbol.Barcode2.Config.ReaderClass.ReaderSpecificClass.ImagerSpecificClass.DifferentSymbolTimeout">
            <summary>
            This parameter is used to prevent the scanner from decoding another symbol within 
            this time interval (applicable only when AimType is set to AIM_TYPE_CONTINUOUS_READ). 
            The continuous read mode is used to perform rapid scans. In order to prevent decoding 
            too quickly and have enough time to aim before decoding the next barcode, 
            this parameter is set to an appropriate interval (in milliseconds).
            A value of 0 means no interval is required between two successive reads
            NOTE: Applicable only when AimType is set to AIM_TYPE_CONTINUOUS_READ.
            </summary>
        </member>
        <member name="P:Symbol.Barcode2.Config.ReaderClass.ReaderSpecificClass.ImagerSpecificClass.LCDMode">
            <summary>
            Enables or disables LCD mode for Blockbuster imager devices only.
            LCD mode enhances the ability of the Blockbuster imager to 
            read barcodes from LCD displays such as cell phone screens. 
            Enabling this mode for Pico imagers and cameras is not required 
            because they already have the ability to read LCD displays
            NOTE: When using the LCD mode, a degradation in performance
            may be observed and the aiming crosshair will blink until the 
            barcode is decoded.
            </summary>
        </member>
        <member name="T:Symbol.Barcode2.Config.ScannerClass">
            <summary>
            The Scanner class provides access to scanning parameters that are available for 
            all decoders.
            NOTE: When calling Enable() after Disable(), all the latest configuration parameter values (Config.Decoders, Config.Scanner, 
            Config.Reader and Config.Interface) will be set automatically. If the parameters need to be reset to default values,
            either Config.RestoreAllDefaultParameters() or RestoreDefaults() (for the correpsonding parameter) can be called.
            </summary>
        </member>
        <member name="M:Symbol.Barcode2.Config.ScannerClass.RestoreDefaults">
            <summary>
            Restores default/saved parameters
            </summary>
        </member>
        <member name="M:Symbol.Barcode2.Config.ScannerClass.Get">
            <summary>
            This method will Get the current Scanner Parameter setting from the device.
            </summary>
        </member>
        <member name="M:Symbol.Barcode2.Config.ScannerClass.Set">
            <summary>
            This method will Set the changes made to the Scanner Parameter and must be called in 
            order for any changes to the parameters to take affect
            </summary>
        </member>
        <member name="P:Symbol.Barcode2.Config.ScannerClass.CodeIdType">
            <summary>
            A CodeIdTypes member that sets the Code ID prefix to be reported. 
            </summary>
        </member>
        <member name="P:Symbol.Barcode2.Config.ScannerClass.ScanType">
            <summary>
            A SCANTYPES member that indicates the type of scan to perform. 
            </summary>
        </member>
        <member name="P:Symbol.Barcode2.Config.ScannerClass.DecodeBeepTime">
            <summary>
            Time to beep upon successful decode. 
            </summary>
        </member>
        <member name="P:Symbol.Barcode2.Config.ScannerClass.DecodeBeepFrequency">
            <summary>
            Frequency to beep upon successful decode. 
            </summary>
        </member>
        <member name="P:Symbol.Barcode2.Config.ScannerClass.DecodeLedTime">
            <summary>
            Time to light decode LED upon successful decode.
            </summary>
        </member>
        <member name="P:Symbol.Barcode2.Config.ScannerClass.DecodeWaveFile">
            <summary>
            Name of .WAV file to play as feedback upon successful decode.
            </summary>
        </member>
        <member name="P:Symbol.Barcode2.Config.ScannerClass.StartBeepTime">
            <summary>
            Time to beep upon start event. Time to beep upon start event.
            </summary>
        </member>
        <member name="P:Symbol.Barcode2.Config.ScannerClass.StartBeepFrequency">
            <summary>
            Frequency to beep upon start event.
            </summary>
        </member>
        <member name="P:Symbol.Barcode2.Config.ScannerClass.StartLedTime">
            <summary>
            Time to light decode LED upon start event.
            </summary>
        </member>
        <member name="P:Symbol.Barcode2.Config.ScannerClass.StartWaveFile">
            <summary>
            Name of .WAV file to play as feedback upon start event. 
            </summary>
        </member>
        <member name="P:Symbol.Barcode2.Config.ScannerClass.IntermedBeepTime">
            <summary>
            Time to beep upon intermediate event. 
            </summary>
        </member>
        <member name="P:Symbol.Barcode2.Config.ScannerClass.IntermedBeepFrequency">
            <summary>
            Frequency to beep upon intermediate event. 
            </summary>
        </member>
        <member name="P:Symbol.Barcode2.Config.ScannerClass.IntermedLedTime">
            <summary>
            Time to light decode LED upon intermediate event. 
            </summary>
        </member>
        <member name="P:Symbol.Barcode2.Config.ScannerClass.IntermedWaveFile">
            <summary>
            Name of .WAV file to play as feedback upon intermediate event.
            </summary>
        </member>
        <member name="P:Symbol.Barcode2.Config.ScannerClass.FatalBeepTime">
            <summary>
            Time to beep upon fatal error event
            </summary>
        </member>
        <member name="P:Symbol.Barcode2.Config.ScannerClass.FatalBeepFrequency">
            <summary>
            Frequency to beep upon fatal error event.
            </summary>
        </member>
        <member name="P:Symbol.Barcode2.Config.ScannerClass.FatalLedTime">
            <summary>
            Time to light decode LED upon fatal error event. 
            </summary>
        </member>
        <member name="P:Symbol.Barcode2.Config.ScannerClass.FatalWaveFile">
            <summary>
            Name of .WAV file to play as feedback upon fatal error event
            </summary>
        </member>
        <member name="P:Symbol.Barcode2.Config.ScannerClass.NonfatalBeepTime">
            <summary>
            Time to beep upon non-fatal error event.
            </summary>
        </member>
        <member name="P:Symbol.Barcode2.Config.ScannerClass.NonfatalBeepFrequency">
            <summary>
            Frequency to beep upon non-fatal error event.
            </summary>
        </member>
        <member name="P:Symbol.Barcode2.Config.ScannerClass.NonfatalLedTime">
            <summary>
            Time to light decode LED upon non-fatal error event
            </summary>
        </member>
        <member name="P:Symbol.Barcode2.Config.ScannerClass.NonfatalWaveFile">
            <summary>
            Name of .WAV file to play as feedback upon non-fatal error event.
            </summary>
        </member>
        <member name="P:Symbol.Barcode2.Config.ScannerClass.ActivityBeepTime">
            <summary>
            Time to beep upon activity event.
            </summary>
        </member>
        <member name="P:Symbol.Barcode2.Config.ScannerClass.ActivityBeepFrequency">
            <summary>
            Frequency to beep upon activity event.
            </summary>
        </member>
        <member name="P:Symbol.Barcode2.Config.ScannerClass.ActivityLedTime">
            <summary>
            Time to light decode LED upon activity event.
            </summary>
        </member>
        <member name="P:Symbol.Barcode2.Config.ScannerClass.ActivityWaveFile">
            <summary>
            Name of .WAV file to play as feedback upon activity event.
            </summary>
        </member>
        <member name="P:Symbol.Barcode2.Config.ScannerClass.BufferedReadBeepTime">
            <summary>
            Time to beep after a read completes when scanner is in buffered-read mode
            </summary>
        </member>
        <member name="P:Symbol.Barcode2.Config.ScannerClass.BufferedReadBeepFrequency">
            <summary>
            Frequency to beep after a read completes when scanner is in buffered-read mode.
            </summary>
        </member>
        <member name="P:Symbol.Barcode2.Config.ScannerClass.BufferedReadLedTime">
            <summary>
            Time to light decode LED after a read completes when scanner is in buffered-read mode.
            </summary>
        </member>
        <member name="P:Symbol.Barcode2.Config.ScannerClass.BufferedReadWaveFile">
            <summary>
            Name of .WAV file to play after a read completes when scanner is in buffered-read mode.
            </summary>
        </member>
        <member name="T:Symbol.Barcode2.Config.InterfaceClass">
            <summary>
            The InterfaceClass class holds the parameters for the interface to the attached bar code reader.
            Interface parameters are global to all reads on all open handles on the same scanner.
            <para>
            NOTE: Beginning with the ScanMDD file version 6.01, the InterfaceClass object can be accessed before or after enabling the 
            Reader object. In the earlier versions of ScanMDD, the InterfaceClass object can only be accessed after enabling the Reader object. 
            The ScanMDD version can be retrieved using the ControlPanel tool available on the support central website.
            </para>
            <para>
            NOTE: When calling Enable() after Disable(), all the latest configuration parameter values (Config.Decoders, Config.Scanner, 
            Config.Reader and Config.Interface) will be set automatically. If the parameters need to be reset to default values,
            either Config.RestoreAllDefaultParameters() or RestoreDefaults() (for the correpsonding parameter) can be called.
            </para>
            </summary>
        </member>
        <member name="M:Symbol.Barcode2.Config.InterfaceClass.RestoreDefaults">
            <summary>
            Restores default/saved parameters
            </summary>
        </member>
        <member name="M:Symbol.Barcode2.Config.InterfaceClass.Get">
            <summary>
            This method will Get the current Interface Parameter setting from the device.
            </summary>
        </member>
        <member name="M:Symbol.Barcode2.Config.InterfaceClass.Set">
            <summary>
            This method will Set the changes made to the Interface Parameter and must be called in 
            order for any changes to the parameters to take affect
            </summary>
        </member>
        <member name="P:Symbol.Barcode2.Config.InterfaceClass.InterfaceType">
            <summary>
            Provides the interface type which determines the specific interface. Read only. 
            This interface type determines the values for InterfaceSpecific.
            </summary>
        </member>
        <member name="P:Symbol.Barcode2.Config.InterfaceClass.InterfaceSpecific">
            <summary>
            The InterfaceSpecific holds parameters for one interface type. 
            The value of InterfaceType determines which specific interface parameters to use.
            </summary>
        </member>
        <member name="T:Symbol.Barcode2.Config.InterfaceSpecific">
            <summary>
            The InterfaceSpecific holds parameters for one interface type. 
            The parameter values in InterfaceSpecific depends on the interface type. 
            </summary>
        </member>
        <member name="P:Symbol.Barcode2.Config.InterfaceSpecific.CamScanSpecific">
            <summary>
            Provides the CamScan specific interface parameters
            </summary>
        </member>
        <member name="P:Symbol.Barcode2.Config.InterfaceSpecific.SSISpecific">
            <summary>
            Provides the SSI specific interface parameters
            </summary>
        </member>
        <member name="T:Symbol.Barcode2.Config.InterfaceSpecific.SSISpecificClass">
            <summary>
            Provides the SSI specific interface parameters
            </summary>
        </member>
        <member name="P:Symbol.Barcode2.Config.InterfaceSpecific.SSISpecificClass.PowerSettlingTime">
            <summary>
            Time in milliseconds after power is supplied before engine is ready for use.
            NOTE: Changing the default value is not recommended.
            </summary>
        </member>
        <member name="P:Symbol.Barcode2.Config.InterfaceSpecific.SSISpecificClass.PowerOffSettlingTime">
            <summary>
            Time to wait before re-powering.
            </summary>
        </member>
        <member name="P:Symbol.Barcode2.Config.InterfaceSpecific.SSISpecificClass.EstablishConnectionTime">
            <summary>
            Specifies the timeout (in seconds) that Reader.Actions.Enable() will block,  
            before returning an error if a remote scanner connection is not established.
            In order to use this parameter, the user should set the required value 
            to this property and call Reader.Actions.SetParameter(), before calling Actions.Enable().
            NOTE: This is currently applicable only to Bluetooth scanners such as RS507.
            </summary>
        </member>
        <member name="P:Symbol.Barcode2.Config.InterfaceSpecific.SSISpecificClass.ConnectionIdleTimeout">
            <summary>
            Specifies the timeout (in seconds), when a remote scanner is idle in a particular
            state (IDLE, WAITING, READY or AIMING), after which the connection between 
            the mobile computer and the remote scanner is severed to conserve power.
            NOTE: This is currently applicable only to Bluetooth scanners such as RS507.
            </summary>
        </member>
        <member name="P:Symbol.Barcode2.Config.InterfaceSpecific.SSISpecificClass.BTDisconnectOnExit">
            <summary>
            Specifies whether to disconnect any existing Bluetooth connection, between 
            a remote Bluetooth scanner and the mobile computer, when disabling the Reader object.
            True - Disconnect existing Bluetooth connection after disabling the Reader object.
            False - Do not disconnect existing Bluetooth connection after disabling the Reader object.
            NOTE: This is currently applicable only to Bluetooth scanners such as RS507.
            Setting this parameter will only persist for one session. To use this feature effectively,
            it is best to set this permanently using Motorola's CtlPanel tool which can be obtained 
            from the support central website.
            </summary>
        </member>
        <member name="P:Symbol.Barcode2.Config.InterfaceSpecific.SSISpecificClass.DisplayBTAddressBarcode">
            <summary>
            Displays the Bluetooth address of the mobile computer as a barcode, when calling Actions.Enable().
            When using Bluetooth scanners such as RS507, the pairing process between the mobile computer 
            and Bluetooth scanner may require scanning the Bluetooth address of the mobile computer.  
            If this parameter is set to TRUE, the Bluetooth address will be displayed as a barcode 
            when the enabling the Reader object. This feature eliminates the need to open external 
            tools such as BT_Information in order to display the pairing barcode.
            NOTE: This is currently applicable only to Bluetooth scanners such as RS507.
            </summary>
        </member>
        <member name="T:Symbol.Barcode2.Config.InterfaceSpecific.CamscanSpecificClass">
            <summary>
            Provides the Camscan specific interface parameters
            </summary>
        </member>
        <member name="P:Symbol.Barcode2.Config.InterfaceSpecific.CamscanSpecificClass.EnableSettlingTime">
            <summary>
            Time in milliseconds after engine is enabled before the laser is turned on.
            </summary>
        </member>
        <member name="P:Symbol.Barcode2.Config.InterfaceSpecific.CamscanSpecificClass.PowerSettlingTime">
            <summary>
            Time in milliseconds after power is supplied before engine is ready for use.
            NOTE: Changing the default value is not recommended.
            </summary>
        </member>
        <member name="P:Symbol.Barcode2.Config.InterfaceSpecific.CamscanSpecificClass.LowPowerTime">
            <summary>
            Time in milliseconds of non-use before dropping to a low-power mode.
            </summary>
        </member>
        <member name="T:Symbol.Barcode2.Config.DecodersClass">
            <remarks>
            The DecodersClass Class contains decoder parameters that are used by
            multiple decoder symbologies.
            The DecodersClass class provides access to such decoder parameters
            as Enabled and IsSupported, among others. 
            NOTE: When calling Enable() after Disable(), all the latest configuration parameter values (Config.Decoders, Config.Scanner, 
            Config.Reader and Config.Interface) will be set automatically. If the parameters need to be reset to default values,
            either Config.RestoreAllDefaultParameters() or RestoreDefaults() (for the correpsonding parameter) can be called.
            </remarks>
            <summary>
            The Decoders class provides access to decoder specific parameters. 
            </summary>
        </member>
        <member name="M:Symbol.Barcode2.Config.DecodersClass.RestoreDefaults">
            <summary>
            Restores default decoder parameters
            </summary>
        </member>
        <member name="M:Symbol.Barcode2.Config.DecodersClass.Get">
            <summary>
            This method will Get the supported Decoders from the device.
            </summary>
        </member>
        <member name="M:Symbol.Barcode2.Config.DecodersClass.Set">
            <summary>
            This method will Set the changes made to the Decoder Parameters and must be called in 
            order for any changes to the parameters to take affect
            </summary>
        </member>
        <member name="M:Symbol.Barcode2.Config.DecodersClass.DisableAll">
            <summary>
            Disables all available decoders so that barcodes with those
            symbologies will NOT be decoded by the scanner.
            </summary>
        </member>
        <member name="P:Symbol.Barcode2.Config.DecodersClass.CODE39">
            <summary>
            The CODE39 property holds the CODE39 specific parameters. These parameters control the decoding and processing of CODE39 bar codes.
            </summary>
        </member>
        <member name="P:Symbol.Barcode2.Config.DecodersClass.UPCE0">
            <summary>
            UPCE0 parameters property provides access to UPCE0 parameters.
            </summary>
        </member>
        <member name="P:Symbol.Barcode2.Config.DecodersClass.UPCE1">
            <summary>
            The UPCE1 property provides access to parameters that are available for the UPCE1 decoder.
            </summary>
        </member>
        <member name="P:Symbol.Barcode2.Config.DecodersClass.UPCA">
            <summary>
            The UPCA property provides access to parameters that are available for the UPCA decoder.
            </summary>
        </member>
        <member name="P:Symbol.Barcode2.Config.DecodersClass.UPCEAN">
            <summary>
            The UPCEAN property provides access to parameters that are available for the UPC and EAN decoders. 
            </summary>
        </member>
        <member name="P:Symbol.Barcode2.Config.DecodersClass.MSI">
            <summary>
            MSI parameters property provides access to MSI parameters.
            </summary>
        </member>
        <member name="P:Symbol.Barcode2.Config.DecodersClass.EAN8">
            <summary>
            The EAN8 property provides access to parameters that are available for the EAN8 decoder.
            </summary>
        </member>
        <member name="P:Symbol.Barcode2.Config.DecodersClass.EAN13">
            <summary>
            The EAN13 property provides access to parameters that are available for the EAN13 decoder.
            </summary>
        </member>
        <member name="P:Symbol.Barcode2.Config.DecodersClass.CODABAR">
            <summary>
            The CODABAR property provides access to parameters that are available for the CODABAR decoder.
            </summary>
        </member>
        <member name="P:Symbol.Barcode2.Config.DecodersClass.D2OF5">
            <summary>
            The D2OF5 property provides access to parameters that are available for the D2OF5 decoder.
            </summary>
        </member>
        <member name="P:Symbol.Barcode2.Config.DecodersClass.I2OF5">
            <summary>
            The I2OF5 property provides access to parameters that are available for the I2OF5 decoder.
            </summary>
        </member>
        <member name="P:Symbol.Barcode2.Config.DecodersClass.CODE11">
            <summary>
            CODE11 property provides a level of abstraction for specific enumerated types.
            </summary>
        </member>
        <member name="P:Symbol.Barcode2.Config.DecodersClass.CODE93">
            <summary>
            The CODE93 property provides access to parameters that are available for the CODE93 decoder.
            </summary>
        </member>
        <member name="P:Symbol.Barcode2.Config.DecodersClass.CODE128">
            <summary>
            The CODE128 property provides access to parameters that are available for the CODE128 decoder.
            </summary>
        </member>
        <member name="P:Symbol.Barcode2.Config.DecodersClass.PDF417">
            <summary>
            The PDF417 property provides access to parameters that are available for the PDF417 decoder.
            </summary>
        </member>
        <member name="P:Symbol.Barcode2.Config.DecodersClass.IMAGE">
            <summary>
            The IMAGE property holds the Imaging specific parameters. These parameters control the processing of Images.
            </summary>
        </member>
        <member name="P:Symbol.Barcode2.Config.DecodersClass.TRIOPTIC39">
            <summary>
            The TRIOPTIC39 property provides access to parameters that are available for the TRIOPTIC39 decoder.
            </summary>
        </member>
        <member name="P:Symbol.Barcode2.Config.DecodersClass.MICROPDF">
            <summary>
            The MICROPDF property provides access to parameters that are available for the MICROPDF decoder.
            </summary>
        </member>
        <member name="P:Symbol.Barcode2.Config.DecodersClass.MACROPDF">
            <summary>
            The MACROPDF property provides access to parameters that are available for the MACROPDF decoder.
            </summary>
        </member>
        <member name="P:Symbol.Barcode2.Config.DecodersClass.MAXICODE">
            <summary>
            The MAXICODE property provides access to parameters that are available for the MAXICODE decoder.
            </summary>
        </member>
        <member name="P:Symbol.Barcode2.Config.DecodersClass.DATAMATRIX">
            <summary>
            The DATAMATRIX property provides access to parameters that are available for the DATAMATRIX decoder.
            </summary>
        </member>
        <member name="P:Symbol.Barcode2.Config.DecodersClass.QRCODE">
            <summary>
            The QRCODE property provides access to parameters that are available for the QRCODE decoder.
            </summary>
        </member>
        <member name="P:Symbol.Barcode2.Config.DecodersClass.MACROMICROPDF">
            <summary>
            The MACROMICROPDF property provides access to parameters that are available for the MACROMICRO decoder.
            </summary>
        </member>
        <member name="P:Symbol.Barcode2.Config.DecodersClass.RSS14">
            <summary>
            The RSS14 property provides access to parameters that are available for the RSS14 decoder.
            </summary>
        </member>
        <member name="P:Symbol.Barcode2.Config.DecodersClass.RSSLIM">
            <summary>
            The RSSLIM property provides access to parameters that are available for the RSSLIM decoder.
            </summary>
        </member>
        <member name="P:Symbol.Barcode2.Config.DecodersClass.RSSEXP">
            <summary>
            The RSSEXP property provides access to parameters that are available for the RSSEXP decoder.
            </summary>
        </member>
        <member name="P:Symbol.Barcode2.Config.DecodersClass.WEBCODE">
            <summary>
            The WEBCODE property provides access to parameters that are available for the WEBCODE decoder.
            </summary>
        </member>
        <member name="P:Symbol.Barcode2.Config.DecodersClass.COMPOSITE_AB">
            <summary>
            The COMPOSITE_AB property provides access to parameters that are available for the COMPOSITE_AB decoder.
            </summary>
        </member>
        <member name="P:Symbol.Barcode2.Config.DecodersClass.COMPOSITE_C">
            <summary>
            The COMPOSITE_C property provides access to parameters that are available for the COMPOSITE_C decoder.
            </summary>
        </member>
        <member name="P:Symbol.Barcode2.Config.DecodersClass.TLC39">
            <summary>
            The TLC39 property provides access to parameters that are available for the TLC39 decoder
            </summary>
        </member>
        <member name="P:Symbol.Barcode2.Config.DecodersClass.USPOSTNET">
            <summary>
            The USPOSTNET property provides access to parameters that are available for the USPOSTNET decoder.
            </summary>
        </member>
        <member name="P:Symbol.Barcode2.Config.DecodersClass.USPLANET">
            <summary>
            The USPLANET property provides access to parameters that are available for the USPLANET decoder.
            </summary>
        </member>
        <member name="P:Symbol.Barcode2.Config.DecodersClass.UKPOSTAL">
            <summary>
            The UKPOSTAL property provides access to parameters that are available for the UKPOSTAL decoder.
            </summary>
        </member>
        <member name="P:Symbol.Barcode2.Config.DecodersClass.JAPPOSTAL">
            <summary>
            The JAPPOSTAL property provides access to parameters that are available for the JAPPOSTAL decoder.
            </summary>
        </member>
        <member name="P:Symbol.Barcode2.Config.DecodersClass.AUSPOSTAL">
            <summary>
            The AUSPOSTAL property provides access to parameters that are available for the AUSPOSTAL decoder.
            </summary>
        </member>
        <member name="P:Symbol.Barcode2.Config.DecodersClass.DUTCHPOSTAL">
            <summary>
            The DUTCHPOSTAL property provides access to parameters that are available for the DUTCHPOSTAL decoder.
            </summary>
        </member>
        <member name="P:Symbol.Barcode2.Config.DecodersClass.CANPOSTAL">
            <summary>
            The CANPOSTAL property provides access to parameters that are available for the CANPOSTAL decoder.
            </summary>
        </member>
        <member name="P:Symbol.Barcode2.Config.DecodersClass.AZTEC">
            <summary>
            The AZTEC property provides access to parameters that are available for the AZTEC decoder.
            </summary>
        </member>
        <member name="P:Symbol.Barcode2.Config.DecodersClass.MICROQR">
            <summary>
            The MICROQR property provides access to parameters that are available for the MICROQR decoder.
            </summary>
        </member>
        <member name="P:Symbol.Barcode2.Config.DecodersClass.KOREAN_3OF5">
            <summary>
            The KOREAN_3OF5 property provides access to parameters that are available for the KOREAN_3OF5 decoder.
            </summary>
        </member>
        <member name="P:Symbol.Barcode2.Config.DecodersClass.US4STATE">
            <summary>
            The US4STATE property provides access to parameters that are available for the US4STATE decoder.
            </summary>
        </member>
        <member name="P:Symbol.Barcode2.Config.DecodersClass.US4STATE_FICS">
            <summary>
            The US4STATE_FICS property provides access to parameters that are available for the US4STATE_FICS decoder.
            </summary>
        </member>
        <member name="P:Symbol.Barcode2.Config.DecodersClass.MATRIX_2OF5">
            <summary>
            The MATRIX_2OF5 property provides access to parameters that are available for the MATRIX_2OF5 decoder.
            </summary>
        </member>
        <member name="P:Symbol.Barcode2.Config.DecodersClass.CHINESE_2OF5">
            <summary>
            The CHINESE_2OF5 property provides access to parameters that are available for the CHINESE_2OF5 decoder.
            </summary>
        </member>
        <member name="P:Symbol.Barcode2.Config.DecodersClass.CUECODE">
            <summary>
            The CUECODE property provides access to parameters that are available for the CUECODE decoder.
            </summary>
        </member>
        <member name="P:Symbol.Barcode2.Config.DecodersClass.SIGNATURE">
            <summary>
            The SIGNATURE property provides access to parameters that are available for the SIGNATURE decoder.
            </summary>
        </member>
        <member name="P:Symbol.Barcode2.Config.DecodersClass.POINTER">
            <summary>
            The POINTER property provides access to parameters that are available for the POINTER decoder.
            </summary>
        </member>
        <member name="P:Symbol.Barcode2.Config.DecodersClass.DOCCAP">
            <summary>
            The DOCCAP property provides access to parameters that are available for the DOCCAP (Document Capture, also known as Decode Image Capture) decoder.
            </summary>
        </member>
        <member name="P:Symbol.Barcode2.Config.DecodersClass.SupportedDecoders">
            <summary>
            An Arraylist of Supported Decoders
            </summary>
        </member>
        <member name="P:Symbol.Barcode2.Config.DecodersClass.EnabledDecoders">
            <summary>
            An Arraylist of Enabled Decoders
            </summary>
        </member>
        <member name="T:Symbol.Barcode2.Config.DecodersClass.BaseDecoder">
            <summary>
            The BaseDecoder class is the parent decoder to all decoders and is inherited by all decoders.
            </summary>
        </member>
        <member name="P:Symbol.Barcode2.Config.DecodersClass.BaseDecoder.MinLength">
            <summary>
            Returns the minimum length of a valid barcode for the decoder.
            </summary>
        </member>
        <member name="P:Symbol.Barcode2.Config.DecodersClass.BaseDecoder.MaxLength">
            <summary>
            Returns the maximum length of a valid barcode for the decoder. 
            </summary>
        </member>
        <member name="P:Symbol.Barcode2.Config.DecodersClass.BaseDecoder.Enabled">
            <summary>
            Returns whether or not the Decoder is currently enabled.
            </summary>
        </member>
        <member name="P:Symbol.Barcode2.Config.DecodersClass.BaseDecoder.IsSupported">
            <summary>
            Returns whether or not the Decoder is supported by the Reader.
            </summary>
        </member>
        <member name="T:Symbol.Barcode2.Config.DecodersClass.CODE93Class">
            <summary>
            The CODE93 class provides access to parameters that are available for the CODE93 decoder. 
            </summary>
        </member>
        <member name="P:Symbol.Barcode2.Config.DecodersClass.CODE93Class.Redundancy">
            <summary>
            Flag to enable redundancy.  
            </summary>
        </member>
        <member name="T:Symbol.Barcode2.Config.DecodersClass.UPCE0Class">
            <summary>
            UPCE0 parameters class provides access to UPCE0 parameters. 
            </summary>
            <remarks>
            The properties MaxLength and MinLength are not used and return 0 at all times.  
            </remarks>
        </member>
        <member name="P:Symbol.Barcode2.Config.DecodersClass.UPCE0Class.ReportCheckDigit">
            <summary>
            Flag to enable reporting the bar code check digit.  
            </summary>
        </member>
        <member name="P:Symbol.Barcode2.Config.DecodersClass.UPCE0Class.Preamble">
            <summary>
            Controls the preamble applied to the bar code.  
            </summary>
        </member>
        <member name="P:Symbol.Barcode2.Config.DecodersClass.UPCE0Class.ConvertToUPCA">
            <summary>
            Flag to enable conversion from UPCE0 to UPCA bar code. If this flag is set, the bar code is converted to UPCA and UPCA parameters are used.  
            </summary>
        </member>
        <member name="P:Symbol.Barcode2.Config.DecodersClass.UPCE0Class.MaxLength">
            <summary>
            Overridden.  This property is not used. Returns 0 at all times.  
            </summary>
        </member>
        <member name="P:Symbol.Barcode2.Config.DecodersClass.UPCE0Class.MinLength">
            <summary>
            Overridden.  This property is not used. Returns 0 at all times.  
            </summary>
        </member>
        <member name="T:Symbol.Barcode2.Config.DecodersClass.UPCE1Class">
            <summary>
            The UPCE1 class provides access to parameters that are available for the UPCE1 decoder. 
            </summary>
            <remarks>
            The properties MaxLength and MinLength are not used and return 0 at all times.  
            </remarks>
        </member>
        <member name="P:Symbol.Barcode2.Config.DecodersClass.UPCE1Class.ReportCheckDigit">
            <summary>
            Flag to enable reporting the bar code check digit.  
            </summary>
        </member>
        <member name="P:Symbol.Barcode2.Config.DecodersClass.UPCE1Class.Preamble">
            <summary>
            Controls the preamble applied to the bar code.  
            </summary>
        </member>
        <member name="P:Symbol.Barcode2.Config.DecodersClass.UPCE1Class.ConvertToUPCA">
            <summary>
            Flag to enable conversion from UPCE1 to UPCA bar code. If this flag is set, the bar code is converted to UPCA and UPCA parameters are used.  
            </summary>
        </member>
        <member name="P:Symbol.Barcode2.Config.DecodersClass.UPCE1Class.MaxLength">
            <summary>
            Overridden.  This property is not used. Returns 0 at all times.  
            </summary>
        </member>
        <member name="P:Symbol.Barcode2.Config.DecodersClass.UPCE1Class.MinLength">
            <summary>
            Overridden.  This property is not used. Returns 0 at all times.  
            </summary>
        </member>
        <member name="T:Symbol.Barcode2.Config.DecodersClass.UPCAClass">
            <summary>
            The UPCA class provides access to parameters that are available for the UPCA decoder. 
            </summary>
            <remarks>
            The properties MaxLength and MinLength are not used and return 0 at all times.  
            </remarks>
        </member>
        <member name="P:Symbol.Barcode2.Config.DecodersClass.UPCAClass.ReportCheckDigit">
            <summary>
            Flag to enable reporting the bar code check digit.  
            </summary>
        </member>
        <member name="P:Symbol.Barcode2.Config.DecodersClass.UPCAClass.Preamble">
            <summary>
            Controls the preamble applied to the bar code.  
            </summary>
        </member>
        <member name="P:Symbol.Barcode2.Config.DecodersClass.UPCAClass.MaxLength">
            <summary>
            Overridden.  This property is not used. Returns 0 at all times.  
            </summary>
        </member>
        <member name="P:Symbol.Barcode2.Config.DecodersClass.UPCAClass.MinLength">
            <summary>
            Overridden.  This property is not used. Returns 0 at all times.  
            </summary>
        </member>
        <member name="T:Symbol.Barcode2.Config.DecodersClass.UPCEANClass">
            <summary>
            The UPCEAN class provides access to parameters that are available for the UPC and EAN decoders. 
            </summary>
        </member>
        <member name="P:Symbol.Barcode2.Config.DecodersClass.UPCEANClass.BooklandCode">
            <summary>
            Flag to enable Bookland code decoding.
            </summary>
        </member>
        <member name="P:Symbol.Barcode2.Config.DecodersClass.UPCEANClass.BooklandFormat">
            <summary>
            Specifies the bookland format to use.  
            </summary>
        </member>
        <member name="P:Symbol.Barcode2.Config.DecodersClass.UPCEANClass.ConvertRSSToUPCEAN">
            <summary>
            Flag to enable converting RSS barcodes to UPC/EAN format. For this setting to work UPC/EAN symbologies must be enabled  
            </summary>
        </member>
        <member name="P:Symbol.Barcode2.Config.DecodersClass.UPCEANClass.CouponCode">
            <summary>
            Flag to enable Coupon Code decoding.  
            </summary>
        </member>
        <member name="P:Symbol.Barcode2.Config.DecodersClass.UPCEANClass.LinearDecode">
            <summary>
            Flag to enable linear decode.  
            </summary>
        </member>
        <member name="P:Symbol.Barcode2.Config.DecodersClass.UPCEANClass.RandomWeightCheckDigit">
            <summary>
            Flag to enable random weight check digit verification.
            </summary>
        </member>
        <member name="P:Symbol.Barcode2.Config.DecodersClass.UPCEANClass.SecurityLevel">
            <summary>
            The UPC/EAN security level.  
            </summary>
        </member>
        <member name="P:Symbol.Barcode2.Config.DecodersClass.UPCEANClass.Supplemental2">
            <summary>
            Flag to enable length 2 supplementals.  
            </summary>
        </member>
        <member name="P:Symbol.Barcode2.Config.DecodersClass.UPCEANClass.Supplemental5">
            <summary>
            Flag to enable length 5 supplementals.  
            </summary>
        </member>
        <member name="P:Symbol.Barcode2.Config.DecodersClass.UPCEANClass.SupplementalMode">
            <summary>
            The supplemental mode.  
            </summary>
        </member>
        <member name="P:Symbol.Barcode2.Config.DecodersClass.UPCEANClass.SupplementalRetries">
            <summary>
            Retry count for auto-discriminating for supplementals. Possible values are 2 to 20 inclusive. Note that flag is only considered if <see cref="P:Symbol.Barcode2.Config.DecodersClass.UPCEANClass.SupplementalMode"/> is set to one of the following values: SUPPLEMENTALS_ AUTO, SUPPLEMENTALS_SMART, SUPPLEMENTALS_378_379, SUPPLEMENTALS_978_979, SUPPLEMENTALS_977 or SUPPLEMENTALS_414_419_434_439.  
            </summary>
        </member>
        <member name="P:Symbol.Barcode2.Config.DecodersClass.UPCEANClass.CouponReportMode">
            <summary>
            Used to differentiate between old coupon (UPC/EAN and Code128) and 
            new GS1 DataBar Coupons.
            NOTE: There is an Interim GS1 DataBar Coupon (UPC A and the GS1 DataBar),
            which is meant for smooth transition of adaption from old coupon format 
            to new coupon format. If an interim coupon is presented to the scanner, 
            it will read old or new potion of it, depending on the report mode setting.
            </summary>
        </member>
        <member name="T:Symbol.Barcode2.Config.DecodersClass.MSIClass">
            <summary>
            MSI parameters class provides access to MSI parameters. 
            </summary>
        </member>
        <member name="P:Symbol.Barcode2.Config.DecodersClass.MSIClass.Redundancy">
            <summary>
            A flag the sets the redundancy.  
            </summary>
        </member>
        <member name="P:Symbol.Barcode2.Config.DecodersClass.MSIClass.CheckDigits">
            <summary>
            The number of check digits to be verified.  
            </summary>
        </member>
        <member name="P:Symbol.Barcode2.Config.DecodersClass.MSIClass.CheckDigitScheme">
            <summary>
            The check digit scheme to verify.  
            </summary>
        </member>
        <member name="P:Symbol.Barcode2.Config.DecodersClass.MSIClass.ReportCheckDigit">
            <summary>
            Flag to enable reporting the bar code check digit.  
            </summary>
        </member>
        <member name="T:Symbol.Barcode2.Config.DecodersClass.EAN8Class">
            <summary>
            The EAN8 class provides access to parameters that are available for the EAN8 decoder.
            </summary>
        </member>
        <member name="P:Symbol.Barcode2.Config.DecodersClass.EAN8Class.ConvertToEAN13">
            <summary>
            Flag to enable conversion from EAN8 to EAN13 bar code.  
            </summary>
        </member>
        <member name="T:Symbol.Barcode2.Config.DecodersClass.CODABARClass">
            <summary>
            The CODABAR class provides access to parameters that are available for the CODABAR decoder. 
            </summary>
        </member>
        <member name="P:Symbol.Barcode2.Config.DecodersClass.CODABARClass.Redundancy">
            <summary>
            Flag to enable redundancy.  
            </summary>
        </member>
        <member name="P:Symbol.Barcode2.Config.DecodersClass.CODABARClass.ClsiEditing">
            <summary>
            Flag to enable CLSI formatting.  
            </summary>
        </member>
        <member name="P:Symbol.Barcode2.Config.DecodersClass.CODABARClass.NotisEditing">
            <summary>
            Flag to enable NOTIS formatting.  
            </summary>
        </member>
        <member name="T:Symbol.Barcode2.Config.DecodersClass.D2OF5Class">
            <summary>
            The D2OF5 class provides access to parameters that are available for the D2OF5 decoder. 
            </summary>
        </member>
        <member name="P:Symbol.Barcode2.Config.DecodersClass.D2OF5Class.Redundancy">
            <summary>
            Flag to enable redundancy.  
            </summary>
        </member>
        <member name="T:Symbol.Barcode2.Config.DecodersClass.I2OF5Class">
            <summary>
            The I2OF5 class provides access to parameters that are available for the I2OF5 decoder. 
            </summary>
        </member>
        <member name="P:Symbol.Barcode2.Config.DecodersClass.I2OF5Class.Redundancy">
            <summary>
            Flag to enable redundancy.  
            </summary>
        </member>
        <member name="P:Symbol.Barcode2.Config.DecodersClass.I2OF5Class.VerifyCheckDigit">
            <summary>
            The check digit scheme to verify.  
            </summary>
        </member>
        <member name="P:Symbol.Barcode2.Config.DecodersClass.I2OF5Class.ReportCheckDigit">
            <summary>
            Flag to enable reporting the bar code check digit.  
            </summary>
        </member>
        <member name="P:Symbol.Barcode2.Config.DecodersClass.I2OF5Class.ConvertToEAN13">
            <summary>
            Flag to enable conversion from I2OF5 to EAN13 bar code.  
            </summary>
        </member>
        <member name="T:Symbol.Barcode2.Config.DecodersClass.CODE11Class">
            <summary>
            CODE11 class provides a level of abstraction for specific enumerated types. 
            </summary>
        </member>
        <member name="P:Symbol.Barcode2.Config.DecodersClass.CODE11Class.Redundancy">
            <summary>
            Flag to enable redundancy. If this flag is set, the bar code must be decoded twice before being accepted.
            Note: This flag is only considered if LASER_SPECIFIC.LinearSecurityLevel = SECURITY_REDUNDANCY_AND_LENGTH ( = 0 ).
            </summary>
        </member>
        <member name="P:Symbol.Barcode2.Config.DecodersClass.CODE11Class.CheckDigitCount">
            <summary>
            Describes the number of check digits to verify. Possible values are:
            CODE11_NO_CHECK_DIGIT ( = 0 ) – no check digit; 
            CODE11_ONE_CHECK_DIGIT ( = 1 ) – one check digit; 
            CODE11_TWO_CHECK_DIGIT ( = 2 ) – two check digits.
            </summary>
        </member>
        <member name="P:Symbol.Barcode2.Config.DecodersClass.CODE11Class.ReportCheckDigit">
            <summary>
            Flag to enable reporting the bar code check digit(s). This flag requires “CheckDigitCount” to be other than CODE11_NO_CHECK_DIGIT, otherwise the check digit is always transmitted.
            </summary>
        </member>
        <member name="T:Symbol.Barcode2.Config.DecodersClass.CODE39Class">
            <summary>
            The CODE39 structure holds the CODE39 specific parameters. These parameters control the decoding and processing of CODE39 bar codes.
            </summary>
        </member>
        <member name="P:Symbol.Barcode2.Config.DecodersClass.CODE39Class.VerifyCheckDigit">
            <summary>
            Flag to enable verification of the bar code check digit.  
            </summary>
        </member>
        <member name="P:Symbol.Barcode2.Config.DecodersClass.CODE39Class.ReportCheckDigit">
            <summary>
            Flag to enable reporting the bar code check digit.  
            </summary>
        </member>
        <member name="P:Symbol.Barcode2.Config.DecodersClass.CODE39Class.Concatenation">
            <summary>
            Flag to enable Code 39 barcode concatenation.  
            </summary>
        </member>
        <member name="P:Symbol.Barcode2.Config.DecodersClass.CODE39Class.FullAscii">
            <summary>
            Flag to enable full ASCII conversion of the bar code.  
            </summary>
        </member>
        <member name="P:Symbol.Barcode2.Config.DecodersClass.CODE39Class.Redundancy">
            <summary>
            Flag to enable redundancy.  
            </summary>
        </member>
        <member name="P:Symbol.Barcode2.Config.DecodersClass.CODE39Class.ConvertToCode32">
            <summary>
            Flag to enable conversion of Code 39 bar codes to Code 32.  
            </summary>
        </member>
        <member name="P:Symbol.Barcode2.Config.DecodersClass.CODE39Class.Code32Prefix">
            <summary>
            Flag to enable reporting the Code 32 prefix when a Code 39 bar code is converted.  
            </summary>
        </member>
        <member name="P:Symbol.Barcode2.Config.DecodersClass.CODE39Class.SecurityLevel">
            <summary>
            Specifies the Code39 security level  
            </summary>
        </member>
        <member name="T:Symbol.Barcode2.Config.DecodersClass.CODE128Class">
            <summary>
            The CODE128 class provides access to parameters that are available for the CODE128 decoder. 
            </summary>
        </member>
        <member name="P:Symbol.Barcode2.Config.DecodersClass.CODE128Class.Redundancy">
            <summary>
            Flag to enable redundancy.  
            </summary>
        </member>
        <member name="P:Symbol.Barcode2.Config.DecodersClass.CODE128Class.EAN128">
            <summary>
            Flag to enable EAN128 subtype.  
            </summary>
        </member>
        <member name="P:Symbol.Barcode2.Config.DecodersClass.CODE128Class.ISBT128">
            <summary>
            Flag to enable ISBT128 subtype.  
            </summary>
        </member>
        <member name="P:Symbol.Barcode2.Config.DecodersClass.CODE128Class.Other128">
            <summary>
            Flag to enable other (non EAN or ISBT) 128 subtype.  
            </summary>
        </member>
        <member name="P:Symbol.Barcode2.Config.DecodersClass.CODE128Class.ISBT128ConcatMode">
            <summary>
            The ISBT128 concatenation feature allows a pair of barcodes which meets a certain criteria defined in the ISBT128 spec to be reported as a single barcode. This parameter describes the different concatenation modes available for ISBT128.
            </summary>
        </member>
        <member name="P:Symbol.Barcode2.Config.DecodersClass.CODE128Class.CheckISBTTable">
            <summary>
            Get/Sets the CheckISBTTable parameter.  
            </summary>
        </member>
        <member name="P:Symbol.Barcode2.Config.DecodersClass.CODE128Class.SecurityLevel">
            <summary>
            Specifies the Code128 security level  
            </summary>
        </member>
        <member name="T:Symbol.Barcode2.Config.DecodersClass.TRIOPTIC39Class">
            <summary>
            The TRIOPTIC39 class provides access to parameters that are available for the TRIOPTIC39 decoder. 
            </summary>
        </member>
        <member name="P:Symbol.Barcode2.Config.DecodersClass.TRIOPTIC39Class.Redundancy">
            <summary>
            Flag to enable redundancy.  
            </summary>
        </member>
        <member name="T:Symbol.Barcode2.Config.DecodersClass.MACROPDFClass">
            <summary>
            The MACROPDF class provides access to parameters that are available for the MACROPDF decoder. 
            </summary>
        </member>
        <member name="P:Symbol.Barcode2.Config.DecodersClass.MACROPDFClass.ReportAppendInfo">
            <summary>
            Flag to enable reporting of append information.  
            </summary>
        </member>
        <member name="P:Symbol.Barcode2.Config.DecodersClass.MACROPDFClass.BufferLabels">
            <summary>
            Flag to enable buffering of labels.  
            </summary>
        </member>
        <member name="P:Symbol.Barcode2.Config.DecodersClass.MACROPDFClass.Exclusive">
            <summary>
            Flag to enable exclusive state.  
            </summary>
        </member>
        <member name="P:Symbol.Barcode2.Config.DecodersClass.MACROPDFClass.ConvertToPDF417">
            <summary>
            Flag to enable conversion to PDF417 symbology. 
            </summary>
        </member>
        <member name="T:Symbol.Barcode2.Config.DecodersClass.MACROMICROPDFClass">
            <summary>
            The MACROMICROPDF class provides access to parameters that are available for the MACROMICRO decoder. 
            </summary>
        </member>
        <member name="P:Symbol.Barcode2.Config.DecodersClass.MACROMICROPDFClass.ReportAppendInfo">
            <summary>
            Flag to enable reporting of append information.  
            </summary>
        </member>
        <member name="P:Symbol.Barcode2.Config.DecodersClass.MACROMICROPDFClass.BufferLabels">
            <summary>
            Flag to enable buffering of labels.  
            </summary>
        </member>
        <member name="P:Symbol.Barcode2.Config.DecodersClass.MACROMICROPDFClass.Exclusive">
            <summary>
            Flag to enable exclusive state.  
            </summary>
        </member>
        <member name="P:Symbol.Barcode2.Config.DecodersClass.MACROMICROPDFClass.ConvertToMicroPDF">
            <summary>
            Flag to enable conversion to MICROPDF symbology.  
            </summary>
        </member>
        <member name="T:Symbol.Barcode2.Config.DecodersClass.DOCCAPClass">
            <summary>
            The DOCCAP class provides access to parameters that are available for the DOCCAP (Document Capture, also known as Decode Image Capture) decoder. 
            </summary>
        </member>
        <member name="P:Symbol.Barcode2.Config.DecodersClass.DOCCAPClass.ImageOffsetX">
            <summary>
            The X axis offset from the center of the barcode to the top left corner of the capture box. 
            This parameter can accept the negative values.
            It is measured as a multiple of barcode elements unit. Range is from -32767 to 32767. 
            </summary>
        </member>
        <member name="P:Symbol.Barcode2.Config.DecodersClass.DOCCAPClass.ImageOffsetY">
            <summary>
            The Y axis offset from the center of barcode to the top left corner of the capture box. 
            This parameter can accept negative values. 
            It is measured as a multiple of barcode elements unit. Range is from -32767 to 32767.
            </summary>
        </member>
        <member name="P:Symbol.Barcode2.Config.DecodersClass.DOCCAPClass.ImageWidth">
            <summary>
            The desired Output image width. It is measured as a multiple of barcode element unit. 
            Range is from 16 to 640.
            </summary>
        </member>
        <member name="P:Symbol.Barcode2.Config.DecodersClass.DOCCAPClass.ImageHeight">
            <summary>
            The desired Output image height. It is measured as a multiple of barcode element unit. 
            Range is from 16 to 480.
            </summary>
        </member>
        <member name="P:Symbol.Barcode2.Config.DecodersClass.DOCCAPClass.ImageFileFormat">
            <summary>
            The file format of the desired image.
            </summary>
        </member>
        <member name="P:Symbol.Barcode2.Config.DecodersClass.DOCCAPClass.ImageBPP">
            <summary>
            The requested image output pixel format.
            Feature currently supports 8bpp only. 
            </summary>
        </member>
        <member name="P:Symbol.Barcode2.Config.DecodersClass.DOCCAPClass.JPEGImageQuality">
            <summary>
            Quality of compressed image when using the JPEG image format.
            Range 0 - 100, where 100 is highest quality image
            0 means optimize for image size
            </summary>
        </member>
        <member name="P:Symbol.Barcode2.Config.DecodersClass.DOCCAPClass.JPEGImageSize">
            <summary>
            Maximum compressed image size in kilobytes (KB) when using the JPEG image format.
            0 means optimized for image quality.
            Important: This Parameter is not currently supported.  
            </summary>
        </member>
        <member name="P:Symbol.Barcode2.Config.DecodersClass.DOCCAPClass.ImgMinPixPercent">
            <summary>
            Minimum % of pixel accepted in the captured image. 
            The capture box’s four sides are compared with the output number of pixels. If any of the four side’s falls short decoding is rejected. 
            The range is 0-100. 0 means this parameter is not used.
            </summary>
        </member>
        <member name="P:Symbol.Barcode2.Config.DecodersClass.DOCCAPClass.ImgMaxRotation">
            <summary>
            Maximum allowed rotation in degrees. All four sides of the capture box are compared either with X or Y axis. 
            If any of the sides are rotated more than this amount, then the decoding is rejected. 
            The range is 0-45. 0 means this parameter is not used.
            </summary>
        </member>
        <member name="P:Symbol.Barcode2.Config.DecodersClass.DOCCAPClass.SelectedSymbologies">
            <summary>
            Allows to select/deselect the symbologies of barcode used to trigger document capture (Decode Image capture).
            By default, all these symbologies that trigger document capture are selected.
            </summary>
        </member>
        <member name="P:Symbol.Barcode2.Config.DecodersClass.DOCCAPClass.MinLength">
            <summary>
            Minimum length of decoded text to start the image capture.
            Range is from 0 to 65535.
            Default value 0 means no minimum Length checking performed.
            </summary>
        </member>
        <member name="P:Symbol.Barcode2.Config.DecodersClass.DOCCAPClass.MaxLength">
            <summary>
            Maximum length of decoded text to start the image capture.
            Range is from 0 to 65535.
            Default value  0 means no maximum length checking performed.
            </summary>
        </member>
        <member name="P:Symbol.Barcode2.Config.DecodersClass.DOCCAPClass.ImgDeskew">
            <summary>
            When enabled, the captured image will be resized and cropped to the exact frame defined by ImageWidth and ImageHeight, else the row image will be produced. 
            It is recommended to use IMAGE_FORMAT_JPEG for ImageFileFormat if this is disabled.
            </summary>
        </member>
        <member name="P:Symbol.Barcode2.Config.DecodersClass.DOCCAPClass.ImgBrighten">
            <summary>
            Whether the brightening of the image is required or not.
            </summary>
        </member>
        <member name="P:Symbol.Barcode2.Config.DecodersClass.DOCCAPClass.ImgSharpen">
            <summary>
            Whether the sharpening of the image is required or not.
            </summary>
        </member>
        <member name="P:Symbol.Barcode2.Config.DecodersClass.DOCCAPClass.ImgHighBlur">
            <summary>
            Whether the high blur decoding is required or not.
            </summary>
        </member>
        <member name="P:Symbol.Barcode2.Config.DecodersClass.DOCCAPClass.ImgBox">
            <summary>
            Whether the box verification required or not.
            When enabled it will try to locate an enclosing black lined box around the capture area. 
            Please make sure that the edges of the box are not broken and clearly visible.
            </summary>
            <remarks>This is applicable to only Barcode Anchored Mode of Document Capture.</remarks>
        </member>
        <member name="P:Symbol.Barcode2.Config.DecodersClass.DOCCAPClass.Mode">
            <summary>
            Indicates mode of Document Capture (Decode Image Capture).
            </summary>
        </member>
        <member name="P:Symbol.Barcode2.Config.DecodersClass.DOCCAPClass.EdgeDetectionType">
            <summary>
            Specify edge detection type for the document capture
            </summary>
            <remarks>This setting is applicable to only <see cref="F:Symbol.Barcode2.DOC_CAPTURE_MODES.BARCODE_LINKED"/> mode and <see cref="F:Symbol.Barcode2.DOC_CAPTURE_MODES.FREE_FORM"/> mode of Document Capture.</remarks>
        </member>
        <member name="P:Symbol.Barcode2.Config.DecodersClass.DOCCAPClass.TriggerMode">
            <summary>
            Indicates trigger mode of the Document Capture.
            </summary>
        </member>
        <member name="T:Symbol.Barcode2.Config.DecodersClass.DOCCAPClass.SelectedSymbologiesClass">
            <summary>
            The class which can be used to select/deselect the symbologies used for document capture (Decode Image capture).
            Enabling a symbology is still required before selecting it.
            By default, all these symbologies that trigger document capture are selected.
            </summary>
        </member>
        <member name="M:Symbol.Barcode2.Config.DecodersClass.DOCCAPClass.SelectedSymbologiesClass.SelectAll">
            <summary>
            Selects all the relevant symbologies for document capture.
            This would trigger document capture (Decode Image Capture) if it finds any of the supported barcode types denoted by individual flags CODE128, ..., EAN128.
            Enabling each of these symbologies is still required before selecting it.
            By default, all these symbologies that trigger document capture are selected.
            </summary>
        </member>
        <member name="M:Symbol.Barcode2.Config.DecodersClass.DOCCAPClass.SelectedSymbologiesClass.DeselectAll">
            <summary>
            Deselects all the relevant symbologies for document capture (Decode Image Capture).
            </summary>
        </member>
        <member name="P:Symbol.Barcode2.Config.DecodersClass.DOCCAPClass.SelectedSymbologiesClass.CODE128">
            <summary>
            The flag to denote whether CODE128 has been selected for document capture (Decode Image Capture).
            Enabling the symbology is still required before selecting it.
            </summary>
        </member>
        <member name="P:Symbol.Barcode2.Config.DecodersClass.DOCCAPClass.SelectedSymbologiesClass.CODE39">
            <summary>
            The flag to denote whether CODE39 has been selected for document capture (Decode Image Capture).
            Enabling the symbology is still required before selecting it.
            </summary>
        </member>
        <member name="P:Symbol.Barcode2.Config.DecodersClass.DOCCAPClass.SelectedSymbologiesClass.I2OF5">
            <summary>
            The flag to denote whether I2OF5 has been selected for document capture (Decode Image Capture).
            Enabling the symbology is still required before selecting it.
            </summary>
        </member>
        <member name="P:Symbol.Barcode2.Config.DecodersClass.DOCCAPClass.SelectedSymbologiesClass.D2OF5">
            <summary>
            The flag to denote whether D2OF5 has been selected for document capture (Decode Image Capture).
            Enabling the symbology is still required before selecting it.
            </summary>
        </member>
        <member name="P:Symbol.Barcode2.Config.DecodersClass.DOCCAPClass.SelectedSymbologiesClass.CODABAR">
            <summary>
            The flag to denote whether CODABAR  has been selected for document capture (Decode Image Capture).
            Enabling the symbology is still required before selecting it.
            </summary>
        </member>
        <member name="P:Symbol.Barcode2.Config.DecodersClass.DOCCAPClass.SelectedSymbologiesClass.PDF417">
            <summary>
            The flag to denote whether PDF417 has been selected for document capture (Decode Image Capture).
            Enabling the symbology is still required before selecting it.
            </summary>
        </member>
        <member name="P:Symbol.Barcode2.Config.DecodersClass.DOCCAPClass.SelectedSymbologiesClass.DATAMATRIX">
            <summary>
            The flag to denote whether DATAMATRIX has been selected for document capture (Decode Image Capture).
            Enabling the symbology is still required before selecting it.
            </summary>
        </member>
        <member name="P:Symbol.Barcode2.Config.DecodersClass.DOCCAPClass.SelectedSymbologiesClass.EAN128">
            <summary>
            The flag to denote whether EAN128 has been selected for document capture (Decode Image Capture).
            Enabling the symbology is still required before selecting it.
            </summary>
        </member>
        <member name="T:Symbol.Barcode2.Config.DecodersClass.DOCCAPClass.ImageFormat">
            <summary>
            Lists the image file formats supported by DOCCAP (Document Capture, also known as Decode Image Capture) decoder for capturing the image.
            </summary>
        </member>
        <member name="F:Symbol.Barcode2.Config.DecodersClass.DOCCAPClass.ImageFormat.IMAGE_FORMAT_JPEG">
            <summary>
            JPEG image file format
            </summary>
        </member>
        <member name="F:Symbol.Barcode2.Config.DecodersClass.DOCCAPClass.ImageFormat.IMAGE_FORMAT_BMP">
            <summary>
            BMP image file format
            </summary>
        </member>
        <member name="F:Symbol.Barcode2.Config.DecodersClass.DOCCAPClass.ImageFormat.IMAGE_FORMAT_TIFF">
            <summary>
            TIFF image file format
            </summary>
        </member>
        <member name="T:Symbol.Barcode2.Config.DecodersClass.WEBCODEClass">
            <summary>
            The WEBCODE class provides access to parameters that are available for the WEBCODE decoder. 
            </summary>
        </member>
        <member name="P:Symbol.Barcode2.Config.DecodersClass.WEBCODEClass.GTWebcode">
            <summary>
            Flag to enable the decoding of the GT Webcode subtype.
            </summary>
        </member>
        <member name="T:Symbol.Barcode2.Config.DecodersClass.COMPOSITE_ABClass">
            <summary>
            The COMPOSITE_AB class provides access to parameters that are available for the COMPOSITE_AB decoder. 
            </summary>
        </member>
        <member name="P:Symbol.Barcode2.Config.DecodersClass.COMPOSITE_ABClass.UCCLinkMode">
            <summary>
            The check digit scheme to verify.  
            </summary>
        </member>
        <member name="P:Symbol.Barcode2.Config.DecodersClass.COMPOSITE_ABClass.UseUPCPreambleCheckDigitRules">
            <summary>
            Enables the use of UPC rules specified in the UPC-EAN params when reporting composite decode data.  
            </summary>
        </member>
        <member name="T:Symbol.Barcode2.Config.DecodersClass.IMAGEClass">
            <summary>
            The IMAGE Class holds the Imaging specific parameters. These parameters control the processing of Images.
            </summary>
        </member>
        <member name="P:Symbol.Barcode2.Config.DecodersClass.IMAGEClass.CroppingRect">
            <summary>
            Gets or sets the rectangle used to crop the viewfinder video. 
            </summary>
        </member>
        <member name="P:Symbol.Barcode2.Config.DecodersClass.IMAGEClass.ResolutionDivisor">
            <summary>
            Resolution 1/x (multiple pixels are combined).  A parameter value of 0 or 1 
            does not modify image size at all.
            </summary>
        </member>
        <member name="P:Symbol.Barcode2.Config.DecodersClass.IMAGEClass.EnableAiming">
            <summary>
            Enables laser aiming during image capture.  Enables/Disables use 
            of aiming reader parameters for image capture.
            </summary>
        </member>
        <member name="P:Symbol.Barcode2.Config.DecodersClass.IMAGEClass.EnableIllumination">
            <summary>
            Enable illumination during image capture.
            </summary>
        </member>
        <member name="P:Symbol.Barcode2.Config.DecodersClass.IMAGEClass.ImageFormat">
            <summary>
            Requested image file format (See <see cref="P:Symbol.Barcode2.Config.DecodersClass.IMAGEClass.ImageFormat"/> for a list of supported 
            file formats).
            </summary>
        </member>
        <member name="P:Symbol.Barcode2.Config.DecodersClass.IMAGEClass.JpegImageQuality">
            <summary>
            Quality of compressed JPEG image.  Range is  0 - 100, where 100 is highest 
            quality image and 0 means optimize for image size.
            Note: JPEG image is optimized for EITHER Quality or Size. Size restrictions 
            take priority over quality restrictions.
            </summary>
        </member>
        <member name="P:Symbol.Barcode2.Config.DecodersClass.IMAGEClass.JpegImageSize">
            <summary>
            Maximum compressed JPEG image size in kilobytes (KB).  0 means optimize for image quality.
            Note: JPEG image is optimized for EITHER Quality or Size. Size restrictions take priority 
            over quality restrictions.
            </summary>
        </member>
        <member name="T:Symbol.Barcode2.Config.DecodersClass.COMPOSITE_CClass">
            <summary>
            The COMPOSITE_C class provides access to parameters that are available for the COMPOSITE_C decoder. 
            </summary>
        </member>
        <member name="P:Symbol.Barcode2.Config.DecodersClass.COMPOSITE_CClass.MultiMode">
            <summary>
            Reserved. Do not use.
            </summary>
        </member>
        <member name="T:Symbol.Barcode2.Config.DecodersClass.KOREAN_3OF5Class">
            <summary>
            The KOREAN_3OF5 class provides access to parameters that are available for the KOREAN_3OF5 decoder.
            </summary>
        </member>
        <member name="P:Symbol.Barcode2.Config.DecodersClass.KOREAN_3OF5Class.Redundancy">
            <summary>
            A flag the sets the redundancy.  
            </summary>
        </member>
        <member name="T:Symbol.Barcode2.Config.DecodersClass.MATRIX_2OF5Class">
            <summary>
            The MATRIX_2OF5 class provides access to parameters that are available for the MATRIX_2OF5 decoder.
            </summary>
        </member>
        <member name="P:Symbol.Barcode2.Config.DecodersClass.MATRIX_2OF5Class.VerifyCheckDigit">
            <summary>
            Flag to enable verification of the bar code check digit.  
            </summary>
        </member>
        <member name="P:Symbol.Barcode2.Config.DecodersClass.MATRIX_2OF5Class.ReportCheckDigit">
            <summary>
            Flag to enable reporting the bar code check digit.  
            </summary>
        </member>
        <member name="T:Symbol.Barcode2.Config.DecodersClass.SIGNATUREClass">
            <summary>
            The SIGNATURE class provides access to parameters that are available for the SIGNATURE decoder. 
            </summary>
        </member>
        <member name="P:Symbol.Barcode2.Config.DecodersClass.SIGNATUREClass.ImageFormat">
            <summary>
            Requested image file format.  
            </summary>
        </member>
        <member name="P:Symbol.Barcode2.Config.DecodersClass.SIGNATUREClass.JpegImageQuality">
            <summary>
            Quality of compressed JPEG image. Range is 0 - 100, where 100 is highest quality image and 0 means optimize for image size. Note: JPEG image will be optimized for either Quality or Size. Size restrictions take priority over quality restrictions.  
            </summary>
        </member>
        <member name="P:Symbol.Barcode2.Config.DecodersClass.SIGNATUREClass.JpegImageSize">
            <summary>
            Maximum compressed JPEG image size in kilobytes (KB). 0 means optimize for image quality. Note: JPEG image will be optimized for either Quality or Size. Size restrictions take priority over quality restrictions.  
            </summary>
        </member>
        <member name="P:Symbol.Barcode2.Config.DecodersClass.SIGNATUREClass.ImageWidth">
            <summary>
            Specifies the desired output image width.  
            </summary>
        </member>
        <member name="P:Symbol.Barcode2.Config.DecodersClass.SIGNATUREClass.ImageHeight">
            <summary>
            Specifies the desired output image height.  
            </summary>
        </member>
        <member name="T:Symbol.Barcode2.Config.DecodersClass.TLC39Class">
            <summary>
            The TLC39 class provides access to parameters that are available for the TLC39 decoder
            </summary>
        </member>
        <member name="T:Symbol.Barcode2.Config.DecodersClass.MAXICODEClass">
            <summary>
            The MAXICODE class provides access to parameters that are available for the MAXICODE decoder. 
            </summary>
        </member>
        <member name="T:Symbol.Barcode2.Config.DecodersClass.DATAMATRIXClass">
            <summary>
            The DATAMATRIX class provides access to parameters that are available for the DATAMATRIX decoder. 
            </summary>
        </member>
        <member name="T:Symbol.Barcode2.Config.DecodersClass.PDF417Class">
            <summary>
            The PDF417 class provides access to parameters that are available for the PDF417 decoder. 
            </summary>
        </member>
        <member name="T:Symbol.Barcode2.Config.DecodersClass.EAN13Class">
            <summary>
            The EAN13 class provides access to parameters that are available for the EAN13 decoder. 
            </summary>
        </member>
        <member name="P:Symbol.Barcode2.Config.DecodersClass.EAN13Class.MaxLength">
            <summary>
            Overridden.  This property is not used. Returns 0 at all times.  
            </summary>
        </member>
        <member name="P:Symbol.Barcode2.Config.DecodersClass.EAN13Class.MinLength">
            <summary>
            Overridden.  This property is not used. Returns 0 at all times.  
            </summary>
        </member>
        <member name="T:Symbol.Barcode2.Config.DecodersClass.MICROPDFClass">
            <summary>
            The MICROPDF class provides access to parameters that are available for the MICROPDF decoder. 
            </summary>
        </member>
        <member name="T:Symbol.Barcode2.Config.DecodersClass.QRCODEClass">
            <summary>
            The QRCODE class provides access to parameters that are available for the QRCODE decoder. 
            </summary>
        </member>
        <member name="T:Symbol.Barcode2.Config.DecodersClass.RSS14Class">
            <summary>
            The RSS14 class provides access to parameters that are available for the RSS14 decoder. 
            </summary>
        </member>
        <member name="T:Symbol.Barcode2.Config.DecodersClass.RSSLIMClass">
            <summary>
            The RSSLIM class provides access to parameters that are available for the RSSLIM decoder. 
            </summary>
        </member>
        <member name="T:Symbol.Barcode2.Config.DecodersClass.RSSEXPClass">
            <summary>
            The RSSEXP class provides access to parameters that are available for the RSSEXP decoder. 
            </summary>
        </member>
        <member name="T:Symbol.Barcode2.Config.DecodersClass.USPOSTNETClass">
            <summary>
            The USPOSTNET class provides access to parameters that are available for the USPOSTNET decoder. 
            </summary>
        </member>
        <member name="T:Symbol.Barcode2.Config.DecodersClass.USPLANETClass">
            <summary>
            The USPLANET class provides access to parameters that are available for the USPLANET decoder. 
            </summary>
        </member>
        <member name="T:Symbol.Barcode2.Config.DecodersClass.UKPOSTALClass">
            <summary>
            The UKPOSTAL class provides access to parameters that are available for the UKPOSTAL decoder. 
            </summary>
        </member>
        <member name="T:Symbol.Barcode2.Config.DecodersClass.JAPPOSTALClass">
            <summary>
            The JAPPOSTAL class provides access to parameters that are available for the JAPPOSTAL decoder. 
            </summary>
        </member>
        <member name="T:Symbol.Barcode2.Config.DecodersClass.AUSPOSTALClass">
            <summary>
            The AUSPOSTAL class provides access to parameters that are available for the AUSPOSTAL decoder. 
            </summary>
        </member>
        <member name="P:Symbol.Barcode2.Config.DecodersClass.AUSPOSTALClass.MaxLength">
            <summary>
            Overridden.  This property is not used. Returns 0 at all times.  
            </summary>
        </member>
        <member name="P:Symbol.Barcode2.Config.DecodersClass.AUSPOSTALClass.MinLength">
            <summary>
            Overridden.  This property is not used. Returns 0 at all times.  
            </summary>
        </member>
        <member name="T:Symbol.Barcode2.Config.DecodersClass.DUTCHPOSTALClass">
            <summary>
            The DUTCHPOSTAL class provides access to parameters that are available for the DUTCHPOSTAL decoder.
            </summary>
        </member>
        <member name="T:Symbol.Barcode2.Config.DecodersClass.CANPOSTALClass">
            <summary>
            The CANPOSTAL class provides access to parameters that are available for the CANPOSTAL decoder. 
            </summary>
        </member>
        <member name="T:Symbol.Barcode2.Config.DecodersClass.AZTECClass">
            <summary>
            The AZTEC class provides access to parameters that are available for the AZTEC decoder. 
            </summary>
        </member>
        <member name="T:Symbol.Barcode2.Config.DecodersClass.MICROQRClass">
            <summary>
            The MICROQR class provides access to parameters that are available for the MICROQR decoder. 
            </summary>
        </member>
        <member name="T:Symbol.Barcode2.Config.DecodersClass.US4STATEClass">
            <summary>
            The US4STATE class provides access to parameters that are available for the US4STATE decoder. 
            </summary>
        </member>
        <member name="T:Symbol.Barcode2.Config.DecodersClass.US4STATE_FICSClass">
            <summary>
            The US4STATE_FICS class provides access to parameters that are available for the US4STATE_FICS decoder. 
            </summary>
        </member>
        <member name="T:Symbol.Barcode2.Config.DecodersClass.CHINESE_2OF5Class">
            <summary>
            The CHINESE_2OF5 class provides access to parameters that are available for the CHINESE_2OF5 decoder. 
            </summary>
        </member>
        <member name="T:Symbol.Barcode2.Config.DecodersClass.CUECODEClass">
            <summary>
            The CUECODE class provides access to parameters that are available for the CUECODE decoder. 
            </summary>
        </member>
        <member name="T:Symbol.Barcode2.Config.DecodersClass.POINTERClass">
            <summary>
            The POINTER class provides access to parameters that are available for the POINTER decoder. 
            </summary>
        </member>
        <member name="M:Symbol.Barcode2.PInvokes.SendMessage(System.IntPtr,System.Int32,System.IntPtr,System.IntPtr)">
            <summary>
            A native SendMessage Win32 call. Used in the notification process.
            </summary>
            <param name="hWnd">The handle to a window.</param>
            <param name="nMsg">The windows message.</param>
            <param name="ipWParam">WParam</param>
            <param name="ipLParam">LParam</param>
            <returns>Return value.</returns>
        </member>
        <member name="M:Symbol.Barcode2.SYSTEMTIME.ToDateTime">
            <summary>
            Convert to System.DateTime
            </summary>
            <returns></returns>
        </member>
        <member name="T:Symbol.Barcode2.DOCCAP_VERSION">
            <summary>
            Indicates support of Document Capture for a scanner.
            </summary>
        </member>
        <member name="F:Symbol.Barcode2.DOCCAP_VERSION.NOT_SUPPORTED">
            <summary>
            Document Capture is not supported.
            </summary>
        </member>
        <member name="F:Symbol.Barcode2.DOCCAP_VERSION.DOCCAP_1">
            <summary>
            Document Capture 1.0 is supported.
            This version of Document Capture supports only Auto Trigger Mode.
            </summary>
        </member>
        <member name="F:Symbol.Barcode2.DOCCAP_VERSION.DOCCAP_2">
            <summary>
            Document Capture 1.0 and Document Capture 2.0 are supported. 
            This version of Document Capture supports Auto Trigger Mode and User Trigger Mode.
            Advanced Edge Detection is supported with User Trigger Mode.
            Document Capture related status reporting is supported with User Trigger Mode.
            </summary>
        </member>
        <member name="T:Symbol.Barcode2.DOCCAP_STATE">
            <summary>
            Describes the status of the Document capture using <see cref="F:Symbol.Barcode2.DOCCAP_TRIGGER_MODE.USER"/> mode.
            </summary>
        </member>
        <member name="F:Symbol.Barcode2.DOCCAP_STATE.NOT_AVAILABLE">
            <summary>
            This field is not used/supported in the selected trigger mode.
            </summary>
        </member>
        <member name="F:Symbol.Barcode2.DOCCAP_STATE.WAITING">
            <summary>
            Scanner(Camera/Imager) is ready to scan/capture. Waiting for trigger press.
            </summary>
        </member>
        <member name="F:Symbol.Barcode2.DOCCAP_STATE.PREVIEWING">
            <summary>
            Trigger is pressed and scanner(Camera/Imager) is displaying images through viewfinder for preview.
            </summary>
        </member>
        <member name="F:Symbol.Barcode2.DOCCAP_STATE.CAPTURING">
            <summary>
            Trigger is released and scanner(Camera/Imager) is taking picture for processing.
            </summary>
        </member>
        <member name="F:Symbol.Barcode2.DOCCAP_STATE.RESULT_PENDING">
            <summary>
            Analyzing image which is captured on the trigger  release. Result is awaited.
            </summary>
        </member>
        <member name="F:Symbol.Barcode2.DOCCAP_STATE.PROCESSING">
            <summary>
            Finished analyzing the image which was captured on the trigger release. Image is good and final (cropped) image and/or barcode data would be available soon
            </summary>
        </member>
        <member name="F:Symbol.Barcode2.DOCCAP_STATE.IMAGE_REJECTED">
            <summary>
             Image which was captured on trigger released is rejected.
            </summary>
        </member>
        <member name="F:Symbol.Barcode2.DOCCAP_STATE.IMAGE_AVAILABLE">
            <summary>
            Image which was captured on trigger released is processed and final image after processing is available in buffer.
            </summary>
        </member>
        <member name="F:Symbol.Barcode2.DOCCAP_STATE.ERROR">
            <summary>
            An error has occurred. Look for ScanError for details.
            </summary>
        </member>
        <member name="T:Symbol.Barcode2.ISBT128_CONCAT_MODE">
            <summary>
            Enumerated ISBT128 concatenation modes.
            </summary>
        </member>
        <member name="F:Symbol.Barcode2.ISBT128_CONCAT_MODE.NEVER">
            <summary>
            Will ignore the barcode pair and only output decode data for only one of the barcodes
            </summary>
        </member>
        <member name="F:Symbol.Barcode2.ISBT128_CONCAT_MODE.ALWAYS">
            <summary>
            Will not decode if both the barcodes are not present or if one of them cannot be decoded
            </summary>
        </member>
        <member name="F:Symbol.Barcode2.ISBT128_CONCAT_MODE.AUTO">
            <summary>
            Auto-discriminate
            </summary>
        </member>
        <member name="T:Symbol.Barcode2.Rectangle">
            <summary>
            The class for representing a rectangle with Left, Top, Right and Bottom coordinates.
            </summary>
        </member>
        <member name="F:Symbol.Barcode2.Rectangle.pLeft">
            <summary>
            x-coordinate of the upper-left corner.
            </summary>
        </member>
        <member name="F:Symbol.Barcode2.Rectangle.pTop">
            <summary>
            y-coordinate of the upper-left corner.
            </summary>
        </member>
        <member name="F:Symbol.Barcode2.Rectangle.pRight">
            <summary>
            x-coordinate of the bottom-right corner.
            </summary>
        </member>
        <member name="F:Symbol.Barcode2.Rectangle.pBottom">
            <summary>
            y-coordinate of the bottom-right corner.
            </summary>
        </member>
        <member name="M:Symbol.Barcode2.Rectangle.#ctor">
            <summary>
            The constructor. All the members are initialized to 0s.
            </summary>
        </member>
        <member name="P:Symbol.Barcode2.Rectangle.Left">
            <summary>
            x-coordinate of the upper-left corner. 
            </summary>
        </member>
        <member name="P:Symbol.Barcode2.Rectangle.Top">
            <summary>
            y-coordinate of the upper-left corner.  
            </summary>
        </member>
        <member name="P:Symbol.Barcode2.Rectangle.Right">
            <summary>
            x-coordinate of the bottom-right corner.  
            </summary>
        </member>
        <member name="P:Symbol.Barcode2.Rectangle.Bottom">
            <summary>
            y-coordinate of the bottom-right corner.  
            </summary>
        </member>
        <member name="T:Symbol.Barcode2.LINEAR_SECURITY_LEVEL">
            <summary>
            Describes the linear security level used during decoding.
            </summary>
        </member>
        <member name="F:Symbol.Barcode2.LINEAR_SECURITY_LEVEL.SECURITY_REDUNDANCY_AND_LENGTH">
            <summary>
            Two times redundancy based on the redundancy flags and the code length; This is not a valid setting for the imager barcode readers.
            </summary>
        </member>
        <member name="F:Symbol.Barcode2.LINEAR_SECURITY_LEVEL.SECURITY_SHORT_OR_CODABAR">
            <summary>
            Two times redundancy if short bar code or CODABAR.
            </summary>
        </member>
        <member name="F:Symbol.Barcode2.LINEAR_SECURITY_LEVEL.SECURITY_ALL_TWICE">
            <summary>
            Two times redundancy for all bar codes.
            </summary>
        </member>
        <member name="F:Symbol.Barcode2.LINEAR_SECURITY_LEVEL.SECURITY_LONG_AND_SHORT">
            <summary>
            Two times redundancy for long bar codes, three times for short bar codes.
            </summary>
        </member>
        <member name="F:Symbol.Barcode2.LINEAR_SECURITY_LEVEL.SECURITY_ALL_THRICE">
            <summary>
            Three times redundancy for all bar codes.
            </summary>
        </member>
        <member name="T:Symbol.Barcode2.DBP_MODE">
            <summary>
            Describes what type of DBP (Digital Bar Pulse) is being produced by the scan engine. 
            </summary>
        </member>
        <member name="F:Symbol.Barcode2.DBP_MODE.DBP_NORMAL">
            <summary>
            Tells the engine to produce normal DBP.
            </summary>
        </member>
        <member name="F:Symbol.Barcode2.DBP_MODE.DBP_COMPOSITE">
            <summary>
            Tells the engine to produce composite DBP, which is 2 different sets of DBP data multiplexed together for better decode performance.
            </summary>
        </member>
        <member name="T:Symbol.Barcode2.FOCUS_MODE">
            <summary>
            Focus mode to use.
            </summary>
        </member>
        <member name="F:Symbol.Barcode2.FOCUS_MODE.FOCUS_MODE_FIXED">
            <summary>
            Fixed focus.
            </summary>
        </member>
        <member name="F:Symbol.Barcode2.FOCUS_MODE.FOCUS_MODE_AUTO">
            <summary>
            Auto focus.
            </summary>
        </member>
        <member name="T:Symbol.Barcode2.FOCUS_POSITION">
            <summary>
            Focus position to use.
            </summary>
        </member>
        <member name="F:Symbol.Barcode2.FOCUS_POSITION.FOCUS_POSITION_FAR">
            <summary>
            Far focus.
            </summary>
        </member>
        <member name="F:Symbol.Barcode2.FOCUS_POSITION.FOCUS_POSITION_NEAR">
            <summary>
            Near focus.
            </summary>
        </member>
        <member name="T:Symbol.Barcode2.DPM_MODE">
            <summary>
            Direct Part Marking (DPM) mode to use.
            </summary>
        </member>
        <member name="F:Symbol.Barcode2.DPM_MODE.DPM_MODE_ENABLED">
            <summary>
            Enable decoding of DPM bar codes
            </summary>
        </member>
        <member name="F:Symbol.Barcode2.DPM_MODE.DPM_MODE_DISABLED">
            <summary>
            Disable decoding of DPM barcodes
            </summary>
        </member>
        <member name="T:Symbol.Barcode2.ILLUMINATION_MODE">
            <summary>
            Illumination mode to use
            </summary>
        </member>
        <member name="F:Symbol.Barcode2.ILLUMINATION_MODE.ILLUMINATION_AUTO">
            <summary>
            In this mode, the auto exposure algorithms will decide whether illumination is required or not.
            </summary>
        </member>
        <member name="F:Symbol.Barcode2.ILLUMINATION_MODE.ILLUMINATION_ALWAYS_OFF">
            <summary>
            In this mode, external illumination is always off.
            </summary>
        </member>
        <member name="F:Symbol.Barcode2.ILLUMINATION_MODE.ILLUMINATION_ALWAYS_ON">
            <summary>
            In this mode, external illumination is always on.
            </summary>
        </member>
        <member name="T:Symbol.Barcode2.PICKLIST_MODE">
            <summary>
            Enumerates the different picklist modes. 
            </summary>
        </member>
        <member name="F:Symbol.Barcode2.PICKLIST_MODE.PICKLIST_DISABLED">
            <summary>
            Disables picklist mode, so any bar code within the field of view can be decoded.
            </summary>
        </member>
        <member name="F:Symbol.Barcode2.PICKLIST_MODE.PICKLIST_HARDWARE_RETICLE">
            <summary>
            Enables picklist mode so that only the barcode under the projected reticle can be decoded.
            If the imager does not support a projected reticle then the behavior is same as that of PICKLIST_SOFTWARE_RETICLE.
            </summary>
        </member>
        <member name="F:Symbol.Barcode2.PICKLIST_MODE.PICKLIST_SOFTWARE_RETICLE">
            <summary>
            Enables the picklist mode so that only the barcode in the center of the image is decoded.
            This is most useful when used in conjunction with the static and dynamic reticle viewfinder modes.
            </summary>
        </member>
        <member name="T:Symbol.Barcode2.VIEWFINDER_MODE">
            <summary>
            Imager Specific Viewfinder Modes
            </summary>
        </member>
        <member name="F:Symbol.Barcode2.VIEWFINDER_MODE.VIEWFINDER_MODE_DISABLED">
            <summary>
            Viewfinder disabled. Viewfinder is not displayed during aiming or scanning.
            </summary>
        </member>
        <member name="F:Symbol.Barcode2.VIEWFINDER_MODE.VIEWFINDER_MODE_ENABLED">
            <summary>
            Viewfinder enabled. Displays the images captured by the camera on the screen.
            </summary>
        </member>
        <member name="F:Symbol.Barcode2.VIEWFINDER_MODE.VIEWFINDER_MODE_STATIC_RETICLE">
            <summary>
            Viewfinder enabled with locate reticle. Displays the viewfinder as well as draws a red reticle
             in the center of the screen which helps with tracking the barcode.
            </summary>
        </member>
        <member name="F:Symbol.Barcode2.VIEWFINDER_MODE.VIEWFINDER_MODE_DYNAMIC_RETICLE">
            <summary>
            Viewfinder enabled with dynamic reticle. Displays the viewfinder as well as draws a red reticle
             in the center of the image. If the barcode in the image is ‘decodable’ the reticle turns green to indicate this.
            </summary>
        </member>
        <member name="T:Symbol.Barcode2.VIEWFINDER_FEEDBACK">
            <summary>
            Imager Specific Viewfinder Feedback Modes
            </summary>
        </member>
        <member name="F:Symbol.Barcode2.VIEWFINDER_FEEDBACK.VIEWFINDER_FEEDBACK_DISABLED">
            <summary>
            Viewfinder feedback disabled. This mode disables any visual feedback on a successful decode.
            </summary>
        </member>
        <member name="F:Symbol.Barcode2.VIEWFINDER_FEEDBACK.VIEWFINDER_FEEDBACK_ENABLED">
            <summary>
            Viewfinder feedback enabled. This mode displays the last image that successfully decoded.
             The duration for which the image is displayed can be set by the VFFeedbackTime.
            </summary>
        </member>
        <member name="F:Symbol.Barcode2.VIEWFINDER_FEEDBACK.VIEWFINDER_FEEDBACK_RETICLE">
            <summary>
            Viewfinder enabled with decode reticle. This mode displays the last image that successfully decoded
             and also draws a reticle in the center of the image.
            </summary>
        </member>
        <member name="T:Symbol.Barcode2.INVERSE1D_MODE">
            <summary>
            Enumerated inverse iD modes.
            </summary>
        </member>
        <member name="F:Symbol.Barcode2.INVERSE1D_MODE.INVERSE_DISABLE">
            <summary>
            Disables decoding of inverse 1D symbologies.
            </summary>
        </member>
        <member name="F:Symbol.Barcode2.INVERSE1D_MODE.INVERSE_ENABLE">
            <summary>
            Enables decoding of only inverse 1D symbologies.
            </summary>
        </member>
        <member name="F:Symbol.Barcode2.INVERSE1D_MODE.INVERSE_AUTO">
            <summary>
            Allows decoding of both positive as well as inverse 1D symbologies.
            </summary>
        </member>
        <member name="T:Symbol.Barcode2.INTERFACE_TYPES">
            <summary>
            Enumerates the supported interface types. 
            </summary>
        </member>
        <member name="F:Symbol.Barcode2.INTERFACE_TYPES.INTERFACE_TYPE_QSNAC">
            <summary>
            QSNAC Interface
            </summary>
        </member>
        <member name="F:Symbol.Barcode2.INTERFACE_TYPES.INTERFACE_TYPE_SSI">
            <summary>
            SSI Interface
            </summary>
        </member>
        <member name="F:Symbol.Barcode2.INTERFACE_TYPES.INTERFACE_TYPE_LS48XX">
            <summary>
            LS48XX Interface
            </summary>
        </member>
        <member name="F:Symbol.Barcode2.INTERFACE_TYPES.INTERFACE_TYPE_LIGHTHOUSE">
            <summary>
            Lighthouse Interface
            </summary>
        </member>
        <member name="F:Symbol.Barcode2.INTERFACE_TYPES.INTERFACE_TYPE_CAMSCAN">
            <summary>
            Camscan Interface
            </summary>
        </member>
        <member name="F:Symbol.Barcode2.STRUCT_INFO.dwAllocated">
            <summary>
            Size of the allocated structure, in bytes.
            </summary>
        </member>
        <member name="F:Symbol.Barcode2.STRUCT_INFO.dwUsed">
            <summary>
            Amount of memory the structure actually uses, in bytes.
            </summary>
        </member>
        <member name="T:Symbol.Barcode2.TRIGGERMODES">
            <summary>
            List of supported trigger modes
            </summary>
        </member>
        <member name="F:Symbol.Barcode2.TRIGGERMODES.HARD">
            <summary>
            Hard trigger. When this mode is set, the user has to manually press the trigger on the device.
            It is applicable to synchronous, asynchronous and buffered scans.
            </summary>
        </member>
        <member name="F:Symbol.Barcode2.TRIGGERMODES.SOFT_ONCE">
            <summary>
            Soft trigger is used only once for a pending scan or for the next issued scan. When this mode is set, the scan beam will come up 
            automatically without having to press the trigger on the device.
            It is applicable to synchronous, asynchronous and buffered scans.
            </summary>
        </member>
        <member name="F:Symbol.Barcode2.TRIGGERMODES.SOFT_ALWAYS">
            <summary>
            Soft trigger is used for all pending scans and for future scans issued. When this mode is set, the scan beam will come up automatically without having to press the trigger on the device.
            It is applicable to synchronous, asynchronous and buffered scans.
            </summary>
        </member>
        <member name="T:Symbol.Barcode2.DEVICETYPES">
            <summary>
            List of supported device types. 
            </summary>
        </member>
        <member name="F:Symbol.Barcode2.DEVICETYPES.INTERNAL_LASER1">
            <summary>
            Internal Laser
            </summary>
        </member>
        <member name="F:Symbol.Barcode2.DEVICETYPES.INTERNAL_IMAGER1">
            <summary>
            Internal Imager
            </summary>
        </member>
        <member name="F:Symbol.Barcode2.DEVICETYPES.INTERNAL_CAMERA1">
            <summary>
            Internal Camera
            </summary>
        </member>
        <member name="F:Symbol.Barcode2.DEVICETYPES.BT_LASER1">
            <summary>
            Bluetooth Laser
            </summary>
        </member>
        <member name="F:Symbol.Barcode2.DEVICETYPES.BT_IMAGER1">
            <summary>
            Bluetooth Imager
            </summary>
        </member>
        <member name="F:Symbol.Barcode2.DEVICETYPES.BT_CAMERA1">
            <summary>
            Bluetooth Camera
            </summary>
        </member>
        <member name="F:Symbol.Barcode2.DEVICETYPES.SERIAL_LASER1">
            <summary>
            Serial Laser
            </summary>
        </member>
        <member name="F:Symbol.Barcode2.DEVICETYPES.SERIAL_IMAGER1">
            <summary>
            Serial Imager
            </summary>
        </member>
        <member name="F:Symbol.Barcode2.DEVICETYPES.SERIAL_CAMERA1">
            <summary>
            Serial Camera
            </summary>
        </member>
        <member name="F:Symbol.Barcode2.DEVICETYPES.FIRSTAVAILABLE">
            <summary>
            First Available Scanner
            </summary>
        </member>
        <member name="F:Symbol.Barcode2.DEVICETYPES.UNKNOWN">
            <summary>
            Unknown Scanner
            </summary>
        </member>
        <member name="T:Symbol.Barcode2.LABELTYPE">
            <summary>
            The LABELTYPE data type defines a type of bar code label that can be returned from a 
            successful read request.  Each label type can be decoded by one or more DECODERs. 
            Note: This enum is unused in the Symbol.Barcode2 API, hence it has been deprecated.
            </summary>
        </member>
        <member name="F:Symbol.Barcode2.LABELTYPE.LABELTYPE_UPCE0">
            <summary>
            The UPC-E0 symbology.
            </summary>
        </member>
        <member name="F:Symbol.Barcode2.LABELTYPE.LABELTYPE_UPCE1">
            <summary>
            The UPC-E1 symbology. 
            </summary>
        </member>
        <member name="F:Symbol.Barcode2.LABELTYPE.LABELTYPE_UPCA">
            <summary>
            The UPC-A symbology. 
            </summary>
        </member>
        <member name="F:Symbol.Barcode2.LABELTYPE.LABELTYPE_MSI">
            <summary>
            The MSI symbology. 
            </summary>
        </member>
        <member name="F:Symbol.Barcode2.LABELTYPE.LABELTYPE_EAN8">
            <summary>
            The EAN-8 symbology. 
            </summary>
        </member>
        <member name="F:Symbol.Barcode2.LABELTYPE.LABELTYPE_EAN13">
            <summary>
            The EAN-13 symbology. 
            </summary>
        </member>
        <member name="F:Symbol.Barcode2.LABELTYPE.LABELTYPE_CODABAR">
            <summary>
            The CODABAR symbology. 
            </summary>
        </member>
        <member name="F:Symbol.Barcode2.LABELTYPE.LABELTYPE_CODE39">
            <summary>
            The CODE-39 symbology. 
            </summary>
        </member>
        <member name="F:Symbol.Barcode2.LABELTYPE.LABELTYPE_D2OF5">
            <summary>
            The Discrete 2 of 5 symbology. 
            </summary>
        </member>
        <member name="F:Symbol.Barcode2.LABELTYPE.LABELTYPE_I2OF5">
            <summary>
            The Interleaved 2 of 5 symbology. 
            </summary>
        </member>
        <member name="F:Symbol.Barcode2.LABELTYPE.LABELTYPE_CODE11">
            <summary>
            The CODE-11 symbology. 
            </summary>
        </member>
        <member name="F:Symbol.Barcode2.LABELTYPE.LABELTYPE_CODE93">
            <summary>
            The CODE-93 symbology. 
            </summary>
        </member>
        <member name="F:Symbol.Barcode2.LABELTYPE.LABELTYPE_CODE128">
            <summary>
            The CODE-128 symbology. 
            </summary>
        </member>
        <member name="F:Symbol.Barcode2.LABELTYPE.LABELTYPE_IATA2OF5">
            <summary>
            The IATA 2 of 5 symbology. 
            </summary>
        </member>
        <member name="F:Symbol.Barcode2.LABELTYPE.LABELTYPE_EAN128">
            <summary>
            The EAN-128 symbology. 
            </summary>
        </member>
        <member name="F:Symbol.Barcode2.LABELTYPE.LABELTYPE_PDF417">
            <summary>
            The PDF 417 symbology. 
            </summary>
        </member>
        <member name="F:Symbol.Barcode2.LABELTYPE.LABELTYPE_ISBT128">
            <summary>
            The ISBT 128 symbology. 
            </summary>
        </member>
        <member name="F:Symbol.Barcode2.LABELTYPE.LABELTYPE_TRIOPTIC39">
            <summary>
            The TRIOPTIC 3 of 9 symbology.
            </summary>
        </member>
        <member name="F:Symbol.Barcode2.LABELTYPE.LABELTYPE_COUPON">
            <summary>
            The COUPON CODE symbology. 
            </summary>
        </member>
        <member name="F:Symbol.Barcode2.LABELTYPE.LABELTYPE_BOOKLAND">
            <summary>
            The BOOKLAND EAN symbology. 
            </summary>
        </member>
        <member name="F:Symbol.Barcode2.LABELTYPE.LABELTYPE_MICROPDF">
            <summary>
            The MICRO PDF symbology. 
            </summary>
        </member>
        <member name="F:Symbol.Barcode2.LABELTYPE.LABELTYPE_CODE32">
            <summary>
            The CODE-32 symbology. 
            </summary>
        </member>
        <member name="F:Symbol.Barcode2.LABELTYPE.LABELTYPE_MACROPDF">
            <summary>
            The MACRO PDF symbology. 
            </summary>
        </member>
        <member name="F:Symbol.Barcode2.LABELTYPE.LABELTYPE_MAXICODE">
            <summary>
            The MAXICODE symbology. 
            </summary>
        </member>
        <member name="F:Symbol.Barcode2.LABELTYPE.LABELTYPE_DATAMATRIX">
            <summary>
            The DATAMATRIX symbology. 
            </summary>
        </member>
        <member name="F:Symbol.Barcode2.LABELTYPE.LABELTYPE_QRCODE">
            <summary>
            The QRCODE symbology. 
            </summary>
        </member>
        <member name="F:Symbol.Barcode2.LABELTYPE.LABELTYPE_MACROMICROPDF">
            <summary>
            The MACRO MICRO PDF symbology. 
            </summary>
        </member>
        <member name="F:Symbol.Barcode2.LABELTYPE.LABELTYPE_RSS14">
            <summary>
            The RSS-14 symbology. 
            </summary>
        </member>
        <member name="F:Symbol.Barcode2.LABELTYPE.LABELTYPE_RSSLIM">
            <summary>
            The RSS limited symbology. 
            </summary>
        </member>
        <member name="F:Symbol.Barcode2.LABELTYPE.LABELTYPE_RSSEXP">
            <summary>
            The RSS expanded symbology. 
            </summary>
        </member>
        <member name="F:Symbol.Barcode2.LABELTYPE.LABELTYPE_IMAGE">
            <summary>
            Image label type. 
            </summary>
        </member>
        <member name="F:Symbol.Barcode2.LABELTYPE.LABELTYPE_SIGNATURE">
            <summary>
            Signature label type. 
            </summary>
        </member>
        <member name="F:Symbol.Barcode2.LABELTYPE.LABELTYPE_WEBCODE">
            <summary>
            The Scanlet WEBCODE symbology. 
            </summary>
        </member>
        <member name="F:Symbol.Barcode2.LABELTYPE.LABELTYPE_CUECODE">
            <summary>
            The CUE CAT CODE symbology. 
            </summary>
        </member>
        <member name="F:Symbol.Barcode2.LABELTYPE.LABELTYPE_COMPOSITE_AB">
            <summary>
            The COMPOSITE AB symbology.
            </summary>
        </member>
        <member name="F:Symbol.Barcode2.LABELTYPE.LABELTYPE_COMPOSITE_C">
            <summary>
            The COMPOSITE C symbology. 
            </summary>
        </member>
        <member name="F:Symbol.Barcode2.LABELTYPE.LABELTYPE_TLC39">
            <summary>
             The TCIF Linked CODE 39 symbology. 
            </summary>
        </member>
        <member name="F:Symbol.Barcode2.LABELTYPE.LABELTYPE_USPOSTNET">
            <summary>
             The US POSTNET symbology. 
            </summary>
        </member>
        <member name="F:Symbol.Barcode2.LABELTYPE.LABELTYPE_USPLANET">
            <summary>
             The US PLANET symbology. 
            </summary>
        </member>
        <member name="F:Symbol.Barcode2.LABELTYPE.LABELTYPE_UKPOSTAL">
            <summary>
             The UK POSTAL symbology. 
            </summary>
        </member>
        <member name="F:Symbol.Barcode2.LABELTYPE.LABELTYPE_JAPPOSTAL">
            <summary>
             The JAPANESE POSTAL symbology. 
            </summary>
        </member>
        <member name="F:Symbol.Barcode2.LABELTYPE.LABELTYPE_AUSPOSTAL">
            <summary>
             The AUSTRALIAN POSTAL symbology. 
            </summary>
        </member>
        <member name="F:Symbol.Barcode2.LABELTYPE.LABELTYPE_DUTCHPOSTAL">
            <summary>
             The DUTCH POSTAL symbology. 
            </summary>
        </member>
        <member name="F:Symbol.Barcode2.LABELTYPE.LABELTYPE_CANPOSTAL">
            <summary>
             The CANADIAN POSTAL symbology.  
            </summary>
        </member>
        <member name="F:Symbol.Barcode2.LABELTYPE.LABELTYPE_CHINESE_2OF5">
            <summary>
             The CHINESE_2OF5 symbology. 
            </summary>
        </member>
        <member name="F:Symbol.Barcode2.LABELTYPE.LABELTYPE_AZTEC">
            <summary>
             The AZTEC symbology.
            </summary>
        </member>
        <member name="F:Symbol.Barcode2.LABELTYPE.LABELTYPE_MICROQR">
            <summary>
            The MICROQR symbology.
            </summary>
        </member>
        <member name="F:Symbol.Barcode2.LABELTYPE.LABELTYPE_KOREAN_3OF5">
            <summary>
            Korean 3 of 5 symbology.
            </summary>
        </member>
        <member name="F:Symbol.Barcode2.LABELTYPE.LABELTYPE_US4STATE">
            <summary>
             US4State Symbology.
            </summary>
        </member>
        <member name="F:Symbol.Barcode2.LABELTYPE.LABELTYPE_US4STATE_FICS">
            <summary>
            US4STATE_FICS Symbology.
            </summary>
        </member>
        <member name="F:Symbol.Barcode2.LABELTYPE.LABELTYPE_UNKNOWN">
            <summary>
             The symbology or labeltype is unknown.
            </summary>
        </member>
        <member name="T:Symbol.Barcode2.SCANNER_TYPE">
            <summary>
            List of supported scanner types.
            </summary>
        </member>
        <member name="F:Symbol.Barcode2.SCANNER_TYPE.LASER">
            <summary>
            Laser
            </summary>
        </member>
        <member name="F:Symbol.Barcode2.SCANNER_TYPE.IMAGER">
            <summary>
            Imager
            </summary>
        </member>
        <member name="F:Symbol.Barcode2.SCANNER_TYPE.CAMERA">
            <summary>
            Camera
            </summary>
        </member>
        <member name="F:Symbol.Barcode2.SCANNER_TYPE.UNKNOWN">
            <summary>
            Unknown
            </summary>
        </member>
        <member name="T:Symbol.Barcode2.TRANSPORT_TYPE">
            <summary>
            List of supported transport types used by scanners.
            </summary>
        </member>
        <member name="F:Symbol.Barcode2.TRANSPORT_TYPE.INTERNAL">
            <summary>
            Internal transport
            </summary>
        </member>
        <member name="F:Symbol.Barcode2.TRANSPORT_TYPE.SERIAL_SSI">
            <summary>
            Serial based SSI transport
            </summary>
        </member>
        <member name="F:Symbol.Barcode2.TRANSPORT_TYPE.BLUETOOTH_SSI">
            <summary>
            Bluetooth based SSI transport
            </summary>
        </member>
        <member name="F:Symbol.Barcode2.TRANSPORT_TYPE.UNKNOWN">
            <summary>
            Unknown transport
            </summary>
        </member>
        <member name="T:Symbol.Barcode2.RegKeyHandles">
            <summary>
            Base registry key handles.
            </summary>
        </member>
        <member name="F:Symbol.Barcode2.RegKeyHandles.HKEY_CLASSES_ROOT">
            <summary>
            ID for creating a handler to Classes Root section in registry.
            </summary>
        </member>
        <member name="F:Symbol.Barcode2.RegKeyHandles.HKEY_CURRENT_USER">
            <summary>
            ID for creating a handler to Current User section in registry.
            </summary>
        </member>
        <member name="F:Symbol.Barcode2.RegKeyHandles.HKEY_LOCAL_MACHINE">
            <summary>
            ID for creating a handler to Local Machine section in registry.
            </summary>
        </member>
        <member name="F:Symbol.Barcode2.RegKeyHandles.HKEY_USERS">
            <summary>
            ID for creating a handler to Users section in registry.
            </summary>
        </member>
        <member name="T:Symbol.Barcode2.SCANTYPES">
            <summary>
            The type of scan requests.
            </summary>
        </member>
        <member name="F:Symbol.Barcode2.SCANTYPES.Foreground">
            <summary>
            Foreground reads take priorty over background reads. When submitting
            a foreground read the application should be the topmost window.
            </summary>
        </member>
        <member name="F:Symbol.Barcode2.SCANTYPES.Background">
            <summary>
            Background reads can be superceded by foreground reads. Background reads
            are usually submitted by background applications such as a scan wedge program.
            </summary>
        </member>
        <member name="F:Symbol.Barcode2.SCANTYPES.Monitor">
            <summary>
            Monitor reads provide a way in which programs can listen in on all scans
            that take place. 
            </summary>
        </member>
        <member name="T:Symbol.Barcode2.FEEDBACKTYPES">
            <summary>
            Supported feedback types
            </summary>
        </member>
        <member name="F:Symbol.Barcode2.FEEDBACKTYPES.REMOTE">
            <summary>
            Provides remote feedback if scanner supports it
            </summary>
        </member>
        <member name="F:Symbol.Barcode2.FEEDBACKTYPES.LOCAL">
            <summary>
            Provides local feedback only. Default value
            </summary>
        </member>
        <member name="F:Symbol.Barcode2.FEEDBACKTYPES.BOTH">
            <summary>
            Provides local as well as remote feedback
            </summary>
        </member>
        <member name="T:Symbol.Barcode2.CodeIdTypes">
            <summary>
            Describes the type of Code ID to be reported.
            </summary>
            <remarks>
             The (legacy) Symbol Technologies Inc. 
            CodeIDs are documented in the following table:
            <list type="table">
            <listheader>
            <term>Code Type</term>
            <description>Symbol Tech. Identifier</description>
            </listheader>
            <item><term>UPC-A, UPC-E, UPC-E1, UPC-E0, EAN-13, EAN-8</term>
            <description>A</description></item>
            <item><term>Code 39, Code 39 Full ASCII, Code 32</term>
            <description>B</description></item>
            <item><term>Codabar</term>
            <description>C</description></item>
            <item><term>Code 128, ISBT 128</term>
            <description>D</description></item>
            <item><term>Code 93</term>
            <description>E</description></item>
            <item><term>Interleaved 2 of 5</term>
            <description>F</description></item>
            <item><term>Discrete 2 of 5, D 2 of 5 IATA</term>
            <description>G</description></item>
            <item><term>MSI Plessey</term>
            <description>J</description></item>
            <item><term>UCC/EAN 128</term>
            <description>K</description></item>
            <item><term>Bookland EAN</term>
            <description>L</description></item>
            <item><term>Trioptic Code 39</term>
            <description>M</description></item>
            <item><term>UPC/EAN Coupon Code</term>
            <description>N</description></item>
            <item><term>PDF417</term>
            <description>X</description></item>
            <item><term>Data Matrix</term>
            <description>P00</description></item>
            <item><term>QR Code, MicroQR</term>
            <description>P01</description></item>
            <item><term>Maxicode</term>
            <description>P02</description></item>
            <item><term>US Postnet</term>
            <description>P03</description></item>
            <item><term>US Planet</term>
            <description>P04</description></item>
            <item><term>Japan Postal</term>
            <description>P05</description></item>
            <item><term>UK Postal</term>
            <description>P06</description></item>
            <item><term>Australian Postal</term>
            <description>P09</description></item>
            <item><term>Signature Capture</term>
            <description>P0X</description></item>
            </list>
            </remarks>
        </member>
        <member name="F:Symbol.Barcode2.CodeIdTypes.None">
            <summary>
            No Code ID.
            </summary>
        </member>
        <member name="F:Symbol.Barcode2.CodeIdTypes.Symbol">
            <summary>
            Use (legacy) Symbol Technologies Code ID.
            </summary>
        </member>
        <member name="F:Symbol.Barcode2.CodeIdTypes.AIM">
            <summary>
            Use AIM Code ID.
            </summary>
        </member>
        <member name="T:Symbol.Barcode2.DisabledEnabled">
            <summary>
            Enumeration used to set the state of local feedback. 
            </summary>
        </member>
        <member name="F:Symbol.Barcode2.DisabledEnabled.Disabled">
            <summary>
            When Disabled, feedback is only done on the scanner device.
            </summary>
        </member>
        <member name="F:Symbol.Barcode2.DisabledEnabled.Enabled">
            <summary>
            When Enabled, feedback is given locally. (e.g. at the Reader)
            </summary>
        </member>
        <member name="T:Symbol.Barcode2.SecurityLevels">
            <summary>
            The UPC/EAN security level.
            </summary>
        </member>
        <member name="F:Symbol.Barcode2.SecurityLevels.LEVEL0">
            <summary>
            This setting allows the digital scanner to operate in its most aggressive state, while providing sufficient security in decoding most “in-spec” bar codes.
            </summary>
        </member>
        <member name="F:Symbol.Barcode2.SecurityLevels.LEVEL1">
            <summary>
            This default setting eliminates most misdecodes.
            </summary>
        </member>
        <member name="F:Symbol.Barcode2.SecurityLevels.LEVEL2">
            <summary>
            Select this option if Security level 1 fails to eliminate misdecodes.
            </summary>
        </member>
        <member name="F:Symbol.Barcode2.SecurityLevels.LEVEL3">
            <summary>
            If you selected Security Level 2 and misdecodes still occur, select this security level.
            Be advised that selecting UPCEAN_SECURITY_LEVEL3 is an extreme measure against mis-decoding severely out of spec bar codes. Selecting this level of security significantly impairs the decoding ability of the digital scanner.  Using improved quality of barcodes is preferable to using this level of security.
            </summary>
        </member>
        <member name="T:Symbol.Barcode2.SupplementalModes">
            <summary>
            The type of supplemental mode. 
            </summary>
        </member>
        <member name="F:Symbol.Barcode2.SupplementalModes.None">
            <summary>
            Supplementals ignored.
            </summary>
        </member>
        <member name="F:Symbol.Barcode2.SupplementalModes.Always">
            <summary>
            Will not decode UPC/EAN without supplementals.
            </summary>
        </member>
        <member name="F:Symbol.Barcode2.SupplementalModes.Auto">
            <summary>
            Auto-discriminates supplementals.
            </summary>
        </member>
        <member name="F:Symbol.Barcode2.SupplementalModes.Smart">
            <summary>
            Enables smart supplementals. In this mode the decoder will return the 
            decoded value of the main block right away if it does not belong to one of 
            the following supplemental types: 378, 379, 977, 978, 979, 414, 419, 434, 439. 
            If the barcode starts with one of the prefixes it will search the image more
            aggressively for a supplemental.
            </summary>
        </member>
        <member name="F:Symbol.Barcode2.SupplementalModes.S_378_379">
            <summary>
            Enables (auto-discriminate) supplemental for UPC/EAN codes starting with 378
            or 379. Will disable reading of supplementals for any other UPC/EAN barcode not starting with 378/379.
            </summary>
        </member>
        <member name="F:Symbol.Barcode2.SupplementalModes.S_978_979">
            <summary>
            Enables (auto-discriminate) supplemental for UPC/EAN codes starting with 978 
            or 979. Will disable reading of supplementals for another UPC/EAN barcode not starting with 978/979.
            </summary> 
        </member>
        <member name="F:Symbol.Barcode2.SupplementalModes.S_977">
            <summary>
            Enables (auto-discriminate) supplemental for UPC/EAN codes starting with 977. 
            Will disable reading of supplementals for another UPC/EAN barcode not starting with 977.
            </summary>
        </member>
        <member name="F:Symbol.Barcode2.SupplementalModes.S_414_419_434_439">
            <summary>
            Enables (auto-discriminate) supplemental for UPC/EAN codes starting
            with 414, 419, 434 or 439. Will disable reading of supplementals for
            another UPC/EAN barcode not starting with 414/419/434/439.
            </summary>
        </member>
        <member name="T:Symbol.Barcode2.BooklandFormat">
            <summary>
            Lists the Bookland formats
            </summary>
        </member>
        <member name="F:Symbol.Barcode2.BooklandFormat.BOOKLAND_FORMAT_ISBN_10">
            <summary>
            978 reported in 10 digit mode
            </summary>
        </member>
        <member name="F:Symbol.Barcode2.BooklandFormat.BOOKLAND_FORMAT_ISBN_13">
            <summary>
            978/979 transmitted as EAN13 as per 2007 ISBN-13 protocol
            </summary>
        </member>
        <member name="T:Symbol.Barcode2.Results">
            <summary>
            Enumerated type that maps its members to underlying C API return codes. 
            </summary>
        </member>
        <member name="F:Symbol.Barcode2.Results.SUCCESS">
            <summary>
            The task was successful.
            </summary>
        </member>
        <member name="F:Symbol.Barcode2.Results.CANCELED">
            <summary>
            The task was cancelled.
            </summary>
        </member>
        <member name="F:Symbol.Barcode2.Results.NOTENABLED">
            <summary>
            The device is not enabled.
            </summary>
        </member>
        <member name="F:Symbol.Barcode2.Results.E_SCN_OPENINGACTIVEKEY">
            <summary>
            A Scanner error occurred opening the active driver registry key.
            </summary>
        </member>
        <member name="F:Symbol.Barcode2.Results.E_SCN_READINGACTIVEKEY">
            <summary>
            A Scanner error occurred reading the active driver registry key.
            </summary>
        </member>
        <member name="F:Symbol.Barcode2.Results.E_SCN_OPENINGPARAMKEY">
            <summary>
            A Scanner error occurred opening the registry key containing the driver's settings.
            </summary>
        </member>
        <member name="F:Symbol.Barcode2.Results.E_SCN_READINGPARAMKEY">
            <summary>
            A Scanner error occurred reading the registry key containing the driver's settings.
            </summary>
        </member>
        <member name="F:Symbol.Barcode2.Results.E_SCN_NOTENOUGHMEMORY">
            <summary>
            An attempt to allocate memory for scanner failed.
            </summary>
        </member>
        <member name="F:Symbol.Barcode2.Results.E_SCN_INVALIDDVCCONTEXT">
            <summary>
            An invalid scanner device context ID was used.
            </summary>
        </member>
        <member name="F:Symbol.Barcode2.Results.E_SCN_INVALIDOPENCONTEXT">
            <summary>
            An attempt was made to access an invalid scanner open context.
            </summary>
        </member>
        <member name="F:Symbol.Barcode2.Results.E_SCN_NOTINITIALIZED">
            <summary>
            The scanner driver was accessed before a successful initialization.
            </summary>
        </member>
        <member name="F:Symbol.Barcode2.Results.E_SCN_CANTLOADDEVICE">
            <summary>
            The physical device driver (PDD) for scanner could not be loaded.
            </summary>
        </member>
        <member name="F:Symbol.Barcode2.Results.E_SCN_INVALIDDEVICE">
            <summary>
            The physical device driver (PDD) DLL for scanner did not contain the required entry points.
            </summary>
        </member>
        <member name="F:Symbol.Barcode2.Results.E_SCN_DEVICEFAILURE">
            <summary>
            Required scanner device is not present, already in use or not functioning properly.
            </summary>
        </member>
        <member name="F:Symbol.Barcode2.Results.E_SCN_CANTSTARTDEVICE">
            <summary>
            The scanner device could not be started.
            </summary>
        </member>
        <member name="F:Symbol.Barcode2.Results.E_SCN_CANTGETDEFAULTS">
            <summary>
            The default scanner parameters could not be obtained from the physical device driver (PDD).
            </summary>
        </member>
        <member name="F:Symbol.Barcode2.Results.E_SCN_NOTSTARTED">
            <summary>
            An attempt was made to use or stop the scanner device and it was not started.
            </summary>
        </member>
        <member name="F:Symbol.Barcode2.Results.E_SCN_ALREADYSTARTED">
            <summary>
            An attempt was made to start the scanner device when the device was already started.
            </summary>
        </member>
        <member name="F:Symbol.Barcode2.Results.E_SCN_NOTENABLED">
            <summary>
            An attempt was made to access the scanner device and it was not enabled.
            </summary>
        </member>
        <member name="F:Symbol.Barcode2.Results.E_SCN_ALREADYENABLED">
            <summary>
            An attempt was made to enable scanning when scanning was already enabled.
            </summary>
        </member>
        <member name="F:Symbol.Barcode2.Results.E_SCN_INVALIDIOCTRL">
            <summary>
            The control code passed to DeviceIoControl of the scanner is invalid.
            </summary>
        </member>
        <member name="F:Symbol.Barcode2.Results.E_SCN_NULLPTR">
            <summary>
            A NULL pointer was passed for a required argument.
            </summary>
        </member>
        <member name="F:Symbol.Barcode2.Results.E_SCN_INVALIDARG">
            <summary>
            A passed argument is out of range.
            </summary>
        </member>
        <member name="F:Symbol.Barcode2.Results.E_SCN_BUFFERSIZEIN">
            <summary>
            The size of the buffer passed as an input to DeviceIoControl is less than sizeof(STRUCT_INFO) or is less than the size specified in StructInfo.dwUsed.
            </summary>
        </member>
        <member name="F:Symbol.Barcode2.Results.E_SCN_BUFFERSIZEOUT">
            <summary>
            The size of the buffer passed as an output to DeviceIoControl is less than sizeof(STRUCT_INFO) or is less than the size specified in StructInfo.dwAllocated.
            </summary>
        </member>
        <member name="F:Symbol.Barcode2.Results.E_SCN_STRUCTSIZE">
            <summary>
            A STRUCT_INFO structure field is invalid. Either dwAllocated or dwUsed is less than the size of STRUCT_INFO or dwUsed is greater than dwAllocated.
            </summary>
        </member>
        <member name="F:Symbol.Barcode2.Results.E_SCN_MISSINGFIELD">
            <summary>
            The size of a structure specified in a StructInfo is too small to contain a required field.
            </summary>
        </member>
        <member name="F:Symbol.Barcode2.Results.E_SCN_INVALIDHANDLE">
            <summary>
            An invalid handle was passed to a function.
            </summary>
        </member>
        <member name="F:Symbol.Barcode2.Results.E_SCN_INVALIDPARAM">
            <summary>
            The value of a parameter either passed as an argument to a function or as a member of a structure is out of range or conflicts with other parameters.
            </summary>
        </member>
        <member name="F:Symbol.Barcode2.Results.E_SCN_CREATEEVENT">
            <summary>
            Unable to create a required event.
            </summary>
        </member>
        <member name="F:Symbol.Barcode2.Results.E_SCN_CREATETHREAD">
            <summary>
            Unable to create a required thread.
            </summary>
        </member>
        <member name="F:Symbol.Barcode2.Results.E_SCN_READCANCELLED">
            <summary>
            A read request was cancelled.
            </summary>
        </member>
        <member name="F:Symbol.Barcode2.Results.E_SCN_READTIMEOUT">
            <summary>
            A read request timed out.
            </summary>
        </member>
        <member name="F:Symbol.Barcode2.Results.E_SCN_READNOTPENDING">
            <summary>
            Attempt to cancel a read that is not pending.
            </summary>
        </member>
        <member name="F:Symbol.Barcode2.Results.PENDING">
            <summary>
            The task is pending.
            NOTE: This enum entry is being deprecated. Use E_SCN_READPENDING instead. It will be removed in a future release.
            </summary>
        </member>
        <member name="F:Symbol.Barcode2.Results.E_SCN_READPENDING">
            <summary>
            Attempt to start a read when one is pending.
            </summary>
        </member>
        <member name="F:Symbol.Barcode2.Results.E_SCN_BUFFERTOOSMALL">
            <summary>
            Data buffer is too small for incoming data.
            </summary>
        </member>
        <member name="F:Symbol.Barcode2.Results.E_SCN_INVALIDSCANBUFFER">
            <summary>
            Attempt to access fields of an invalid scan buffer.
            </summary>
        </member>
        <member name="F:Symbol.Barcode2.Results.E_SCN_READINCOMPATIBLE">
            <summary>
            Attempt to submit a read that is incompatible with reads already queued.
            </summary>
        </member>
        <member name="F:Symbol.Barcode2.Results.E_SCN_NOFEEDBACK">
            <summary>
            Attempt to perform physical device driver (PDD) feedback with no feedback capabilities.
            </summary>
        </member>
        <member name="F:Symbol.Barcode2.Results.E_SCN_NOTSUPPORTED">
            <summary>
            Version of function not supported (e.g. ANSI vs. UNICODE).
            </summary>
        </member>
        <member name="F:Symbol.Barcode2.Results.E_SCN_WRONGSTATE">
            <summary>
            The requested operation is inconsistent with the current state of the device.
            </summary>
        </member>
        <member name="F:Symbol.Barcode2.Results.E_SCN_NOMOREITEMS">
            <summary>
            No more items are available to be returned from SCAN_FindFirst/SCAN_FindNext.
            </summary>
        </member>
        <member name="F:Symbol.Barcode2.Results.E_SCN_CANTOPENREGKEY">
            <summary>
            A required registry key could not be opened.
            </summary>
        </member>
        <member name="F:Symbol.Barcode2.Results.E_SCN_CANTREADREGVAL">
            <summary>
            A required registry value could not be read.
            </summary>
        </member>
        <member name="F:Symbol.Barcode2.Results.E_SCN_EXCEPTION">
            <summary>
            An exception occurred while trying to call the scanner driver.
            </summary>
        </member>
        <member name="F:Symbol.Barcode2.Results.E_SCN_WIN32ERROR">
            <summary>
            A scanner API function failed with a non-scanner API error code. Call GetLastError to get extended error code.
            </summary>
        </member>
        <member name="F:Symbol.Barcode2.Results.E_SCN_ALREADYINUSE">
            <summary>
            A requested scanner resource is already in use.
            </summary>
        </member>
        <member name="F:Symbol.Barcode2.Results.E_SCN_NOTINUSE">
            <summary>
            The specified scanner resource was not allocated.
            </summary>
        </member>
        <member name="F:Symbol.Barcode2.Results.E_SCN_CANTLOADDECODERDLL">
            <summary>
            The specified decoder DLL can not be loaded.
            </summary>
        </member>
        <member name="F:Symbol.Barcode2.Results.E_SCN_INVALIDDECODERDLL">
            <summary>
            At least one API is missing in Decoder DLL.
            </summary>
        </member>
        <member name="F:Symbol.Barcode2.Results.E_SCN_INVALIDSYMBOL">
            <summary>
            The symbol's format is invalid.
            </summary>
        </member>
        <member name="F:Symbol.Barcode2.Results.E_SCN_INVALIDLICENSE">
            <summary>
            The platform does not have a valid license key.
            </summary>
        </member>
        <member name="F:Symbol.Barcode2.Results.E_SCN_NOTINSEQUENCE">
            <summary>
            The scanned macro symbol is not part of the current macro sequence.
            </summary>
        </member>
        <member name="F:Symbol.Barcode2.Results.E_SCN_DUPLICATESYMBOL">
            <summary>
            The scanned macro symbol has already been scanned into the current macro sequence.
            </summary>
        </member>
        <member name="F:Symbol.Barcode2.Results.E_SCN_CANTLOADHALDLL">
            <summary>
            The Image HAL DLL can not be loaded.
            </summary>
        </member>
        <member name="F:Symbol.Barcode2.Results.E_SCN_INVALIDHALDLL">
            <summary>
            At least one API function is missing in Image HAL DLL.
            </summary>
        </member>
        <member name="F:Symbol.Barcode2.Results.E_SCN_CANTLOADCOMPRESSIONDLL">
            <summary>
            The Image Compression DLL can not be loaded.
            </summary>
        </member>
        <member name="F:Symbol.Barcode2.Results.E_SCN_I2CFAILURE">
            <summary>
            I2C communication failed.
            </summary>
        </member>
        <member name="F:Symbol.Barcode2.Results.E_SCN_DEVICEDISABLED">
            <summary>
            Camera security policy not allowing to use camera.
            </summary>
        </member>
        <member name="F:Symbol.Barcode2.Results.E_SCN_RSMATTRIBINVALID">
            <summary>
            The specific RSM attribute number is not valid on scanner.
            </summary>
        </member>
        <member name="F:Symbol.Barcode2.Results.E_SCN_INVALIDRESPONSE">
            <summary>
            Invalid response received from scanner.
            </summary>
        </member>
        <member name="F:Symbol.Barcode2.Results.E_SCN_MISSING_CONFIG">
            <summary>
            Missing configuration details.
            </summary>
        </member>
        <member name="F:Symbol.Barcode2.Results.INVALID_TIMEOUT">
            <summary>
            Timeout used is invalid or out of acceptable range.
            </summary>
        </member>
        <member name="F:Symbol.Barcode2.Results.NOBUFFEREDSCANS_PENDING">
            <summary>
            Cannot execute command due to buffered scanning has not being started.
            </summary>
        </member>
        <member name="F:Symbol.Barcode2.Results.CREATEEVENT_FAILED">
            <summary>
            Failure in creating the event.
            </summary>
        </member>
        <member name="F:Symbol.Barcode2.Results.DISABLE_IN_PROGRESS">
            <summary>
            Barcode2.Disable() method is in progress.
            </summary>
        </member>
        <member name="T:Symbol.Barcode2.Options">
            <summary>
            Options class provides the capability of setting parameters that effect that
            way in which the EMDK for .NET class libraries work.
            </summary>
            <remarks>
            The Options class can be used to select how the class libraries should handle 
            errors that are not necessarilly fatal, but may be useful to customer during
            development. 
            </remarks>
        </member>
        <member name="P:Symbol.Barcode2.Options.DebugMode">
            <summary>
            Turn on/off debug mode exceptions.
            </summary>
            <value>
            A boolean flag that when set to true enables exceptions to be thrown in 
            situations that are not fatal but may lead flag issues that should be 
            addressed prior to an application's release.
            </value>
        </member>
        <member name="T:Symbol.Barcode2.Stopwatch">
            <summary>
            A helper class that provides a way in which applications can determine how long
            tasks are taking.
            </summary>
            <remarks>
            This class is not as accurate as a Performance Counter. It should be used for 
            rough estimations only.
            </remarks>
        </member>
        <member name="M:Symbol.Barcode2.Stopwatch.start">
            <summary>
            Start stopwatch.
            </summary>
        </member>
        <member name="M:Symbol.Barcode2.Stopwatch.stop">
            <summary>
            Stop stopwatch
            </summary>
            <returns>A TimeSpan object with time elapsed information.</returns>
        </member>
        <member name="T:Symbol.Barcode2.ATTRIB_NUMBER">
            <summary>
            Provides the list of possible attribute numbers. 
            </summary>
        </member>
        <member name="F:Symbol.Barcode2.ATTRIB_NUMBER.MODEL_NUMBER">
            <summary>
            Model Number
            </summary>
        </member>
        <member name="F:Symbol.Barcode2.ATTRIB_NUMBER.SERIAL_NUMBER">
            <summary>
            Serial Number
            </summary>
        </member>
        <member name="F:Symbol.Barcode2.ATTRIB_NUMBER.DATE_OF_MANUFACTURE">
            <summary>
            Date of Device Manufacture
            </summary>
        </member>
        <member name="F:Symbol.Barcode2.ATTRIB_NUMBER.DATE_OF_SERVICE">
            <summary>
            Date of last repair within a Motorola Authorized repair facility
            </summary>
        </member>
        <member name="F:Symbol.Barcode2.ATTRIB_NUMBER.BT_ADDR">
            <summary>
            Unique Bluetooth Address of the scanner device
            </summary>
        </member>
        <member name="F:Symbol.Barcode2.ATTRIB_NUMBER.BT_AUTHENTICATION">
            <summary>
            Whether Bluetooth authentication is required or not.
            </summary>
        </member>
        <member name="F:Symbol.Barcode2.ATTRIB_NUMBER.BT_ENCRYPTION">
            <summary>
            Whether encryption over Bluetooth is required or not.
            </summary>
        </member>
        <member name="F:Symbol.Barcode2.ATTRIB_NUMBER.BT_PINCODE">
            <summary>
            Pincode used when Bluetooth Authentication is enabled.
            </summary>
        </member>
        <member name="F:Symbol.Barcode2.ATTRIB_NUMBER.RECONNECT_ATTEMPTS">
            <summary>
            Duration for which the scanner tries to reestablish the connection, 
            if it goes out of range.
            </summary>
        </member>
        <member name="F:Symbol.Barcode2.ATTRIB_NUMBER.BEEP_ON_RECON_ATTEMPT">
            <summary>
            Indicates whether the scanner should beep during reconnection attemps.
            </summary>
        </member>
        <member name="F:Symbol.Barcode2.ATTRIB_NUMBER.HID_AUTO_RECON">
            <summary>
            Auto-reconnect behavior of the scanner when HID connection is lost.
            </summary>
        </member>
        <member name="F:Symbol.Barcode2.ATTRIB_NUMBER.BT_FRIENDLY_NAME">
            <summary>
            Friendly name displayed by Bluetooth remote devices.
            </summary>
        </member>
        <member name="F:Symbol.Barcode2.ATTRIB_NUMBER.PIN_CODE_TYPE">
            <summary>
            Indicates whether to prompt the user for PIN code or use PIN code stored in memory.
            </summary>
        </member>
        <member name="F:Symbol.Barcode2.ATTRIB_NUMBER.BT_INQUIRY_MODE">
            <summary>
            Whether general or limited inquiry mode is used.
            </summary>
        </member>
        <member name="F:Symbol.Barcode2.ATTRIB_NUMBER.EXCLUSIVE_CODE128_EN">
            <summary>
            Feature for ignoring Code 128’s beginning with 420 and 421 enable/ disable.
            </summary>
        </member>
        <member name="F:Symbol.Barcode2.ATTRIB_NUMBER.MEMS_ENABLE">
            <summary>
            Used to enable/disable mems feature
            </summary>
        </member>
        <member name="F:Symbol.Barcode2.ATTRIB_NUMBER.PROXIMITY_ENABLE">
            <summary>
            Used to enable/disable proximity feature
            </summary>
        </member>
        <member name="F:Symbol.Barcode2.ATTRIB_NUMBER.PROXIMITY_DISTANCE">
            <summary>
            Specifies Proximity distance.
            </summary>
        </member>
        <member name="F:Symbol.Barcode2.ATTRIB_NUMBER.PAGING_ENABLE">
            <summary>
            Used to enable/disable paging
            </summary>
        </member>
        <member name="F:Symbol.Barcode2.ATTRIB_NUMBER.PAGING_BEEP_SEQ">
            <summary>
            Paging beep sequence.
            </summary>
        </member>
        <member name="F:Symbol.Barcode2.ATTRIB_NUMBER.LOW_BATTERY_IND_EN">
            <summary>
            Used to enable/disable low battery indication.
            </summary>
        </member>
        <member name="F:Symbol.Barcode2.ATTRIB_NUMBER.SCAN_TRIG_WAKEUP_EN">
            <summary>
            Enables/Disables Scan trigger as a wakeup source for the device from low power.
            </summary>
        </member>
        <member name="F:Symbol.Barcode2.ATTRIB_NUMBER.BLUETOOTH_AUTO_RECON">
            <summary>
            Defines Bluetooth reconnection scheme.
            </summary>
        </member>
        <member name="F:Symbol.Barcode2.ATTRIB_NUMBER.LOW_BATTERY_IND_CYCLE">
            <summary>
            Low battery indication cycle time in seconds.
            </summary>
        </member>
        <member name="F:Symbol.Barcode2.ATTRIB_NUMBER.PREFERRED_WIRELESSHOST">
            <summary>
            Preferred Bluetooth wireless host:
            </summary>
        </member>
        <member name="F:Symbol.Barcode2.ATTRIB_NUMBER.PROXIMITY_CONTINUOUS_EN">
            <summary>
            Proximity continuous mode enable / disable
            </summary>
        </member>
        <member name="F:Symbol.Barcode2.ATTRIB_NUMBER.GOOD_SCANS_DELAY">
            <summary>
            Delay between proximity continuous good scans in msec: Values 0 -150 (* 100 msec)
            0 –  No delay
            1 – 100 msec 
            2 – 200 msec
            ..
            ..
            150 – 15000 msec
            </summary>
        </member>
        <member name="F:Symbol.Barcode2.ATTRIB_NUMBER.PAGING_ACTIVATE">
            <summary>
            Start /Stop paging to scanner
            </summary>
        </member>
        <member name="F:Symbol.Barcode2.ATTRIB_NUMBER.FIRM_VERSION">
            <summary>
            Scanner’s operating system version.
            </summary>
        </member>
        <member name="F:Symbol.Barcode2.ATTRIB_NUMBER.HARDWARE_VERSION">
            <summary>
            Hardware version of the device.
            </summary>
        </member>
        <member name="F:Symbol.Barcode2.ATTRIB_NUMBER.DEVICE_CLASS">
            <summary>
            The device class of the system.
            </summary>
        </member>
        <member name="F:Symbol.Barcode2.ATTRIB_NUMBER.BATTERY_STATUS">
            <summary>
            Indicates the status of Battery in the device.
            </summary>
        </member>
        <member name="F:Symbol.Barcode2.ATTRIB_NUMBER.BATTERY_CAPACITY">
            <summary>
            Capacity of the Battery.
            </summary>
        </member>
        <member name="F:Symbol.Barcode2.ATTRIB_NUMBER.BATTERY_ID">
            <summary>
            Battery ID.
            </summary>
        </member>
        <member name="F:Symbol.Barcode2.ATTRIB_NUMBER.SCANLINE_WIDTH">
            <summary>
            This attribute defines the laser scan line width (Pick list).
            </summary>
        </member>
        <member name="F:Symbol.Barcode2.ATTRIB_NUMBER.BT_DISCONNECT">
            <summary>
            Command scanner to disconnect from terminal.
            </summary>
        </member>
        <member name="F:Symbol.Barcode2.ATTRIB_NUMBER.BT_UNPAIR">
            <summary>
            Command scanner to unpair from terminal.
            </summary>
        </member>
        <member name="F:Symbol.Barcode2.ATTRIB_NUMBER.DISABLE_GOOD_DECODE_LED_BEEP">
            <summary>
            This attribute disables good decode LED and beep on scanner.
            </summary>
        </member>
        <member name="F:Symbol.Barcode2.ATTRIB_NUMBER.FORCE_PAIRING_SAVE">
            <summary>
            Forces Bluetooth address saving when pairing fails
            </summary>
        </member>
        <member name="T:Symbol.Barcode2.ATTRIB_TYPE">
            <summary>
            Provides the list of possible attribute types.
            </summary>
        </member>
        <member name="F:Symbol.Barcode2.ATTRIB_TYPE.UNTYPED">
            <summary>
            Attribute type is unknown
            </summary>
        </member>
        <member name="F:Symbol.Barcode2.ATTRIB_TYPE.BYTE">
            <summary>
            Attribute type is BYTE
            </summary>
        </member>
        <member name="F:Symbol.Barcode2.ATTRIB_TYPE.CHAR">
            <summary>
            Attribute type is CHAR 
            </summary>
        </member>
        <member name="F:Symbol.Barcode2.ATTRIB_TYPE.FLAG">
            <summary>
            Attribute type is FLAG 
            </summary>
        </member>
        <member name="F:Symbol.Barcode2.ATTRIB_TYPE.USHORT">
            <summary>
            Attribute type is USHORT 
            </summary>
        </member>
        <member name="F:Symbol.Barcode2.ATTRIB_TYPE.SHORT">
            <summary>
            Attribute type is SHORT 
            </summary>
        </member>
        <member name="F:Symbol.Barcode2.ATTRIB_TYPE.ULONG">
            <summary>
            Attribute type is ULONG 
            </summary>
        </member>
        <member name="F:Symbol.Barcode2.ATTRIB_TYPE.LONG">
            <summary>
            Attribute type is LONG 
            </summary>
        </member>
        <member name="F:Symbol.Barcode2.ATTRIB_TYPE.ARRAY">
            <summary>
            Attribute type is array 
            </summary>
        </member>
        <member name="F:Symbol.Barcode2.ATTRIB_TYPE.STRING">
            <summary>
            Attribute type is string 
            </summary>
        </member>
        <member name="F:Symbol.Barcode2.ATTRIB_TYPE.ACTION">
            <summary>
            Attribute type is action (byte)
            </summary>
        </member>
        <member name="T:Symbol.Barcode2.DecoderTypes">
            <summary>
            Enumeration of all barcode symbologies supported. Not all scanner
            hardware supports all symbologies, refer to your hardware manual
            for more information. 
            </summary>
        </member>
        <member name="F:Symbol.Barcode2.DecoderTypes.UPCE0">
            <summary>
            The UPC-E0 symbology.
            </summary>
        </member>
        <member name="F:Symbol.Barcode2.DecoderTypes.UPCE1">
            <summary>
            The UPC-E1 symbology.
            </summary>
        </member>
        <member name="F:Symbol.Barcode2.DecoderTypes.UPCA">
            <summary>
            The UPC-A symbology.
            </summary>
        </member>
        <member name="F:Symbol.Barcode2.DecoderTypes.MSI">
            <summary>
            The MSI symbology.
            </summary>
        </member>
        <member name="F:Symbol.Barcode2.DecoderTypes.EAN8">
            <summary>
            The EAN-8 symbology.
            </summary>
        </member>
        <member name="F:Symbol.Barcode2.DecoderTypes.EAN13">
            <summary>
            The EAN-13 symbology.
            </summary>
        </member>
        <member name="F:Symbol.Barcode2.DecoderTypes.CODABAR">
            <summary>
            The CODABAR symbology.
            </summary>
        </member>
        <member name="F:Symbol.Barcode2.DecoderTypes.CODE39">
            <summary>
            The CODE-39 symbology.
            </summary>
        </member>
        <member name="F:Symbol.Barcode2.DecoderTypes.D2OF5">
            <summary>
            The Discrete 2 of 5 symbology.
            </summary>
        </member>
        <member name="F:Symbol.Barcode2.DecoderTypes.I2OF5">
            <summary>
            The Interleaved 2 of 5 symbology.
            </summary>
        </member>
        <member name="F:Symbol.Barcode2.DecoderTypes.CODE11">
            <summary>
            The CODE-11 symbology.
            </summary>
        </member>
        <member name="F:Symbol.Barcode2.DecoderTypes.CODE93">
            <summary>
            The CODE-93 symbology.
            </summary>
        </member>
        <member name="F:Symbol.Barcode2.DecoderTypes.CODE128">
            <summary>
            The CODE-128 symbology.
            </summary>
        </member>
        <member name="F:Symbol.Barcode2.DecoderTypes.IATA2OF5">
            <summary>
            The IATA 2 of 5 symbology.
            </summary>
        </member>
        <member name="F:Symbol.Barcode2.DecoderTypes.EAN128">
            <summary>
            The EAN-128 symbology.
            </summary>
        </member>
        <member name="F:Symbol.Barcode2.DecoderTypes.PDF417">
            <summary>
            The PDF 417 symbology.
            </summary>
        </member>
        <member name="F:Symbol.Barcode2.DecoderTypes.ISBT128">
            <summary>
            The ISBT 128 symbology.
            </summary>
        </member>
        <member name="F:Symbol.Barcode2.DecoderTypes.TRIOPTIC39">
            <summary>
            The TRIOPTIC 3 of 9 symbology.
            </summary>
        </member>
        <member name="F:Symbol.Barcode2.DecoderTypes.COUPON">
            <summary>
            The COUPON CODE symbology.
            </summary>
        </member>
        <member name="F:Symbol.Barcode2.DecoderTypes.BOOKLAND">
            <summary>
            The BOOKLAND EAN symbology.
            </summary>
        </member>
        <member name="F:Symbol.Barcode2.DecoderTypes.MICROPDF">
            <summary>
            The MICRO PDF symbology.
            </summary>
        </member>
        <member name="F:Symbol.Barcode2.DecoderTypes.CODE32">
            <summary>
            The CODE-32 symbology.
            </summary>
        </member>
        <member name="F:Symbol.Barcode2.DecoderTypes.MACROPDF">
            <summary>
            The MACRO PDF symbology.
            </summary>
        </member>
        <member name="F:Symbol.Barcode2.DecoderTypes.MAXICODE">
            <summary>
            The MAXICODE symbology.
            </summary>
        </member>
        <member name="F:Symbol.Barcode2.DecoderTypes.DATAMATRIX">
            <summary>
            The DATAMATRIX symbology.
            </summary>
        </member>
        <member name="F:Symbol.Barcode2.DecoderTypes.QRCODE">
            <summary>
            The QRCODE symbology.
            </summary>
        </member>
        <member name="F:Symbol.Barcode2.DecoderTypes.MACROMICROPDF">
            <summary>
            The MACRO MICRO PDF symbology.
            </summary>
        </member>
        <member name="F:Symbol.Barcode2.DecoderTypes.RSS14">
            <summary>
            The RSS-14 symbology.
            </summary>
        </member>
        <member name="F:Symbol.Barcode2.DecoderTypes.RSSLIM">
            <summary>
            The RSS limited symbology.
            </summary>
        </member>
        <member name="F:Symbol.Barcode2.DecoderTypes.RSSEXP">
            <summary>
            The RSS expanded symbology.
            </summary>
        </member>
        <member name="F:Symbol.Barcode2.DecoderTypes.POINTER">
            <summary>
            The Pointer symbology.
            </summary>
        </member>
        <member name="F:Symbol.Barcode2.DecoderTypes.IMAGE">
            <summary>
            The Image symbology.
            </summary>
        </member>
        <member name="F:Symbol.Barcode2.DecoderTypes.SIGNATURE">
            <summary>
            The Signature symbology.
            </summary>
        </member>
        <member name="F:Symbol.Barcode2.DecoderTypes.DOCCAP">
            <summary>
            The DocumentCapture (Decode Image Capture) symbology.
            </summary>
        </member>
        <member name="F:Symbol.Barcode2.DecoderTypes.RESERVED_53">
            <summary>
            RESERVED. The usage of this has been deprecated.
            The value 0x53 is now used for DOCCAP.
            </summary>
        </member>
        <member name="F:Symbol.Barcode2.DecoderTypes.WEBCODE">
            <summary>
            The Scanlet WEBCODE symbology.
            </summary>
        </member>
        <member name="F:Symbol.Barcode2.DecoderTypes.CUECODE">
            <summary>
            The CUE CAT CODE symbology.
            </summary>
        </member>
        <member name="F:Symbol.Barcode2.DecoderTypes.COMPOSITE_AB">
            <summary>
            The COMPOSITE AB symbology.
            </summary>
        </member>
        <member name="F:Symbol.Barcode2.DecoderTypes.COMPOSITE_C">
            <summary>
            The COMPOSITE C symbology.
            </summary>
        </member>
        <member name="F:Symbol.Barcode2.DecoderTypes.TLC39">
            <summary>
            The TCIF Linked CODE 39 symbology.
            </summary>
        </member>
        <member name="F:Symbol.Barcode2.DecoderTypes.USPOSTNET">
            <summary>
            The US POSTNET symbology.
            </summary>
        </member>
        <member name="F:Symbol.Barcode2.DecoderTypes.USPLANET">
            <summary>
            The US PLANET symbology.
            </summary>
        </member>
        <member name="F:Symbol.Barcode2.DecoderTypes.UKPOSTAL">
            <summary>
            The UK POSTAL symbology.
            </summary>
        </member>
        <member name="F:Symbol.Barcode2.DecoderTypes.JAPPOSTAL">
            <summary>
            The JAPANESE POSTAL symbology.
            </summary>
        </member>
        <member name="F:Symbol.Barcode2.DecoderTypes.AUSPOSTAL">
            <summary>
            The AUSTRALIAN POSTAL symbology.
            </summary>
        </member>
        <member name="F:Symbol.Barcode2.DecoderTypes.DUTCHPOSTAL">
            <summary>
            The DUTCH POSTAL symbology.
            </summary>
        </member>
        <member name="F:Symbol.Barcode2.DecoderTypes.CANPOSTAL">
            <summary>
            The CANADIAN POSTAL symbology.
            </summary>
        </member>
        <member name="F:Symbol.Barcode2.DecoderTypes.CHINESE_2OF5">
            <summary>
            The CHINESE_2OF5 symbology.
            </summary>
        </member>
        <member name="F:Symbol.Barcode2.DecoderTypes.AZTEC">
            <summary>
            The AZTEC symbology
            </summary>
        </member>
        <member name="F:Symbol.Barcode2.DecoderTypes.MICROQR">
            <summary>
            The MICROQR symbology
            </summary>
        </member>
        <member name="F:Symbol.Barcode2.DecoderTypes.KOREAN_3OF5">
            <summary>
            The Korean 3 of 5 symbology
            </summary>
        </member>
        <member name="F:Symbol.Barcode2.DecoderTypes.US4STATE">
            <summary>
            The US4State symbology
            </summary>
        </member>
        <member name="F:Symbol.Barcode2.DecoderTypes.US4STATE_FICS">
            <summary>
            The US4STATE_FICS symbology
            </summary>
        </member>
        <member name="F:Symbol.Barcode2.DecoderTypes.MATRIX_2OF5">
            <summary>
            The MATRIX_2OF5 symbology
            </summary>
        </member>
        <member name="F:Symbol.Barcode2.DecoderTypes.UPCEAN">
            <summary>
            The UPCEAN symbology
            </summary>
        </member>
        <member name="F:Symbol.Barcode2.DecoderTypes.FIRST">
            <summary>
            The first item in the DecoderTypes enumeration. This item is 
            used during simulation mode to cycle through all symbologies.
            </summary>
        </member>
        <member name="F:Symbol.Barcode2.DecoderTypes.NEXT">
            <summary>
            The next item in the DecoderTypes enumeration. This item is 
            used during simulation mode to cycle through all symbologies.
            </summary>
        </member>
        <member name="F:Symbol.Barcode2.DecoderTypes.LAST">
            <summary>
            The last item in the DecoderTypes enumeration. This item is 
            used during simulation mode to cycle through all symbologies.
            </summary>
        </member>
        <member name="F:Symbol.Barcode2.DecoderTypes.UNKNOWN">
            <summary>
            The symbology is unknown.
            </summary>
        </member>
        <member name="T:Symbol.Barcode2.ScanDataFormats">
            <summary>
            Data format for the data that is read from barcode scanner.
            </summary>
        </member>
        <member name="F:Symbol.Barcode2.ScanDataFormats.Binary">
            <summary>
            The buffer of memory used to access the hardware will hold raw binary data.
            </summary>
        </member>
        <member name="F:Symbol.Barcode2.ScanDataFormats.Text">
            <summary>
            The buffer of memory used to access the hardware will hold text.
            </summary>
        </member>
        <member name="T:Symbol.Barcode2.ReaderDataLengths">
            <summary>
            Length of ScanData memory buffer.
            </summary>
        </member>
        <member name="F:Symbol.Barcode2.ReaderDataLengths.DefaultText">
            <summary>
            Default for text. 55 characters in text mode, bytes in binary mode.
            </summary>
        </member>
        <member name="F:Symbol.Barcode2.ReaderDataLengths.MaximumLabel">
            <summary>
            Maximum amount memory for data. 7905 character in text mode, bytes in binary mode.
            </summary>
        </member>
        <member name="T:Symbol.Barcode2.States">
            <summary>
            Defines the different states of the barcode Reader.
            </summary>
        </member>
        <member name="F:Symbol.Barcode2.States.BT_CONNECTION_STATE_DISCONNECTED">
            <summary>
            The Bluetooth Scanner is disconnected.
            </summary>
        </member>
        <member name="F:Symbol.Barcode2.States.BT_CONNECTION_STATE_CONNECTED">
            <summary>
            The Bluetooth Scanner is connected.
            </summary>
        </member>
        <member name="F:Symbol.Barcode2.States.STOPPED">
            <summary>
            Scanner is not enabled.
            </summary>
        </member>
        <member name="F:Symbol.Barcode2.States.IDLE">
            <summary>
            Scanner is enabled but no reads are pending.
            </summary>
        </member>
        <member name="F:Symbol.Barcode2.States.WAITING">
            <summary>
            Scanner has one or more reads pending and is waiting for trigger event.
            </summary>
        </member>
        <member name="F:Symbol.Barcode2.States.READY">
            <summary>
            Scanner beam is on and it is acquiring data.
            </summary>
        </member>
        <member name="F:Symbol.Barcode2.States.AIMING">
            <summary>
            Scanner beam is on for aiming purposes.
            </summary>
        </member>
        <member name="F:Symbol.Barcode2.States.EMPTY">
            <summary>
            Scanner beam is off waiting for Klasse Eins Gas Tank to recover.
            </summary>
        </member>
        <member name="F:Symbol.Barcode2.States.ERROR">
            <summary>
            An error has occured.
            </summary>
        </member>
        <member name="F:Symbol.Barcode2.States.FIRST">
            <summary>
            The first item in the States enumeration. This item is 
            used during simulation mode to cycle through all states.
            </summary>
        </member>
        <member name="F:Symbol.Barcode2.States.LAST">
            <summary>
            The last item in the States enumeration. This item is 
            used during simulation mode to cycle through all states.
            </summary>
        </member>
        <member name="T:Symbol.Barcode2.EventTypes">
            <summary>
            Defines the different types of events that can occur for the barcode Reader.
            </summary>
        </member>
        <member name="F:Symbol.Barcode2.EventTypes.ERROR">
            <summary>
            An error occurred while trying to wait for an event.
            </summary>
        </member>
        <member name="F:Symbol.Barcode2.EventTypes.STATE_CHANGE">
            <summary>
            The state of the scanner has changed.
            </summary>
        </member>
        <member name="F:Symbol.Barcode2.EventTypes.ACTIVITY">
            <summary>
            The scanner is busy/active. This event is used for image downloading activity
            and 2D hand-raster activity.
            </summary>
        </member>
        <member name="F:Symbol.Barcode2.EventTypes.IMAGE_CAPTURE">
            <summary>
            The Scanner has captured an image and started to download.
            </summary>
        </member>
        <member name="F:Symbol.Barcode2.EventTypes.IMAGE_ERROR">
            <summary>
            The Scanner has encountered a fatal error downloading an image. 
            </summary>
        </member>
        <member name="F:Symbol.Barcode2.EventTypes.SEQUENCE_START">
            <summary>
            The Scanner has captured the first barcode in a concatenation sequence. 
            </summary>
        </member>
        <member name="F:Symbol.Barcode2.EventTypes.SEQUENCE_CONTINUE">
            <summary>
            The Scanner has captured another barcode in a concatenation sequence.
            </summary>
        </member>
        <member name="F:Symbol.Barcode2.EventTypes.SEQUENCE_FAIL">
            <summary>
            The Scanner has encountered a fatal concatenation error. 
            </summary>
        </member>
        <member name="F:Symbol.Barcode2.EventTypes.SEQUENCE_ERROR">
            <summary>
            The Scanner has encountered a non-fatal concatenation error.
            </summary>
        </member>
        <member name="F:Symbol.Barcode2.EventTypes.SEQUENCE_DUPLICATE">
            <summary>
            The Scanner has encountered a duplicate barcode in a concatenation sequence.
            </summary>
        </member>
        <member name="F:Symbol.Barcode2.EventTypes.SEQUENCE_INVALID">
            <summary>
            The Scanner has encountered a barcode that is not part of the current concatenation sequence.
            </summary>
        </member>
        <member name="F:Symbol.Barcode2.EventTypes.BT_CONNECTION_STATE_CHANGE">
            <summary>
            The connection state with Bluetooth scanner was changed. 
            </summary>
        </member>
        <member name="F:Symbol.Barcode2.EventTypes.ERROR_READ_BUFFER_FULL">
            <summary>
            The internal scanner buffer is full and unable to buffer any more barcodes
            </summary>
        </member>
        <member name="F:Symbol.Barcode2.EventTypes.FIRST">
            <summary>
            The first item in the EventTypes enumeration. This item is 
            used during simulation mode to cycle through all event types.
            </summary>
        </member>
        <member name="F:Symbol.Barcode2.EventTypes.LAST">
            <summary>
            The last item in the EventTypes enumeration. This item is 
            used during simulation mode to cycle through all event types.
            </summary>
        </member>
        <member name="T:Symbol.Barcode2.UCC">
            <summary>
            Describes the UCC link mode state 
            </summary>
        </member>
        <member name="F:Symbol.Barcode2.UCC.UCC_NEVER">
            <summary>
            Link flag ignored.
            </summary>
        </member>
        <member name="F:Symbol.Barcode2.UCC.UCC_ALWAYS">
            <summary>
            Always linked.
            </summary>
        </member>
        <member name="F:Symbol.Barcode2.UCC.UCC_AUTO">
            <summary>
            Auto-discriminate.
            </summary>
        </member>
        <member name="T:Symbol.Barcode2.AIM_TYPE">
            <summary>
            Describes the type of aiming to use. 
            </summary>
        </member>
        <member name="F:Symbol.Barcode2.AIM_TYPE.AIM_TYPE_TRIGGER">
            <summary>
            Dual-stage trigger based aiming; The standard triggering mode that remains idle until the trigger is pressed. Once the trigger is pressed a decode session is started. The decode session remains active until a barcode is decoded, the BeamTimer is expired or the trigger is released. 
            </summary>
        </member>
        <member name="F:Symbol.Barcode2.AIM_TYPE.AIM_TYPE_TIMED_HOLD">
            <summary>
            Timed hold aim type; The scan status is idle until the trigger is pressed. Once pressed an aiming session is started for a time specified by AimDuration, when this time expires a decode session is started. The decode session will remain active until the BeamTimer expires, the trigger is released or a barcode is decoded.
            </summary>
        </member>
        <member name="F:Symbol.Barcode2.AIM_TYPE.AIM_TYPE_TIMED_RELEASE">
            <summary>
            Timed release aim type; The scan status is idle until the trigger is pressed. Once pressed an aiming session is started and will continue until the trigger is released.If the AimDuration is expired when the trigger is released then a decode session will be started for a remaining time equal to BeamTimer or a barcode is decoded.
            </summary>
        </member>
        <member name="F:Symbol.Barcode2.AIM_TYPE.AIM_TYPE_PRESS_AND_RELEASE">
            <summary>
            Press and release aim type; the scan status goes from idle to scanning by pressing and releasing the trigger. The decode session will remain active until the BeamTimer expired or a barcode is decoded. This is not a valid setting for the laser barcode readers.
            </summary>
        </member>
        <member name="F:Symbol.Barcode2.AIM_TYPE.AIM_TYPE_PRESENTATION">
            <summary>
            Presentaion aim type; Appears idle until motion is detected in front of imager window at which time illumination is turned on along with the aiming pattern and a decode is attempted. Currently only the MK500 imager device supports this feature and works only when the soft trigger is enabled.
            </summary>
        </member>
        <member name="F:Symbol.Barcode2.AIM_TYPE.AIM_TYPE_CONTINUOUS_READ">
            <summary>
            Trigger continuously; In this mode once the trigger is pulled the user can continue scanning barcodes without releasing the trigger as long as new reads are submitted as soon as the earlier read is satisfied. This mode is useful when the user wants to perform rapid scanning. To provide better control over this feature we have added the two new reader parameters (SameSymbolTimeout, DifferentSymbolTimeout) that are associated with continuous reads. These reader parameters are available in both IMAGER_SPECIFIC and LASER_SPECIFIC classes. NOTE: The following must be considered when using this AIM_TYPE_CONTINUOUS_READ mode. 1. After each successful read, the application will have to submit a new read for rapid triggering. 2. It is recommended that the Picklist mode be enabled for the imager-class scanners. 3. When using this mode, the IMAGER_SPECIFIC.VFFeedback parameter will be ignored and no viewfinder feedback will be provided. It is similar to setting IMAGER_SPECIFIC.VFFeedback to VIEWFINDER_FEEDBACK.VIEWFINDER_MODE_DISABLED 4. If the IMAGER_SPECIFIC.VFMode parameter is set to VIEWFINDER_MODE.VIEWFINDER_MODE_DYNAMIC_RETICLE, then this continuous read mode will be ignored
            </summary>
        </member>
        <member name="T:Symbol.Barcode2.AIM_MODE">
            <summary>
            Describes the aiming mode to use. 
            </summary>
        </member>
        <member name="F:Symbol.Barcode2.AIM_MODE.AIM_MODE_NONE">
            <summary>
            No aiming.
            </summary>
        </member>
        <member name="F:Symbol.Barcode2.AIM_MODE.AIM_MODE_DOT">
            <summary>
            Dot aiming.
            </summary>
        </member>
        <member name="F:Symbol.Barcode2.AIM_MODE.AIM_MODE_SLAB">
            <summary>
            Slab aiming.
            </summary>
        </member>
        <member name="F:Symbol.Barcode2.AIM_MODE.AIM_MODE_RETICLE">
            <summary>
            Reticule aiming.
            </summary>
        </member>
        <member name="T:Symbol.Barcode2.BEAM_WIDTH">
            <summary>
            Describes the beam width to use. 
            </summary>
        </member>
        <member name="F:Symbol.Barcode2.BEAM_WIDTH.NORMAL">
            <summary>
            Normal beam
            </summary>
        </member>
        <member name="F:Symbol.Barcode2.BEAM_WIDTH.NARROW">
            <summary>
            Narrow beam
            </summary>
        </member>
        <member name="F:Symbol.Barcode2.BEAM_WIDTH.WIDE">
            <summary>
            Wide beam
            </summary>
        </member>
        <member name="T:Symbol.Barcode2.ADAPTIVESCANNING">
            <summary>
            Describes the adaptive scanning mode
            </summary>
        </member>
        <member name="F:Symbol.Barcode2.ADAPTIVESCANNING.DISABLE">
            <summary>
            Disables adaptive mode.
            </summary>
        </member>
        <member name="F:Symbol.Barcode2.ADAPTIVESCANNING.ENABLE">
            <summary>
            Enables adaptive mode.
            </summary>
        </member>
        <member name="T:Symbol.Barcode2.RASTER_MODE">
            <summary>
            Describes the type of vertical rastering to use. 
            </summary>
        </member>
        <member name="F:Symbol.Barcode2.RASTER_MODE.RASTER_MODE_NONE">
            <summary>
            No rastering. 
            </summary>
        </member>
        <member name="F:Symbol.Barcode2.RASTER_MODE.RASTER_MODE_OPEN_ALWAYS">
            <summary>
            Rastering always full open. 
            </summary>
        </member>
        <member name="F:Symbol.Barcode2.RASTER_MODE.RASTER_MODE_SMART">
            <summary>
            Smart rastering mode. 
            </summary>
        </member>
        <member name="T:Symbol.Barcode2.READER_TYPES">
            <summary>
            Describes the bar code reader type these parameters apply to. Read only. This value determines the structure of the ReaderSpecific sub-structure. 
            </summary>
        </member>
        <member name="F:Symbol.Barcode2.READER_TYPES.READER_TYPE_LASER">
            <summary>
            Laser bar code reader. 
            </summary>
        </member>
        <member name="F:Symbol.Barcode2.READER_TYPES.READER_TYPE_IMAGER">
            <summary>
            Imager bar code reader. 
            </summary>
        </member>
        <member name="T:Symbol.Barcode2.Preambles">
            <summary>
            Controls the preamble applied to the bar code.
            </summary>
        </member>
        <member name="F:Symbol.Barcode2.Preambles.None">
            <summary>
            No preamble
            </summary>
        </member>
        <member name="F:Symbol.Barcode2.Preambles.System">
            <summary>
            System character preamble.
            </summary>
        </member>
        <member name="F:Symbol.Barcode2.Preambles.CountryAndSystem">
            <summary>
            Both country and system code preamble.
            </summary>
        </member>
        <member name="T:Symbol.Barcode2.CheckDigitCounts">
            <summary>
            The number of check digits to be verified.
            </summary>
        </member>
        <member name="F:Symbol.Barcode2.CheckDigitCounts.One">
            <summary>
            One check digit.
            </summary>
        </member>
        <member name="F:Symbol.Barcode2.CheckDigitCounts.Two">
            <summary>
            Two check digits.
            </summary>
        </member>
        <member name="T:Symbol.Barcode2.CheckDigitSchemes">
            <summary>
            The check digit scheme to verify.
            </summary>
        </member>
        <member name="F:Symbol.Barcode2.CheckDigitSchemes.Mod_11_10">
            <summary>
            The first check digit is MOD 11, the second is MOD 10.
            </summary>
        </member>
        <member name="F:Symbol.Barcode2.CheckDigitSchemes.Mod_10_10">
            <summary>
            Both check digits are MOD 10.
            </summary>
        </member>
        <member name="T:Symbol.Barcode2.CODE11">
            <summary>
            CODE11 class provides a level of abstraction for specific enumerated types.
            </summary>
        </member>
        <member name="T:Symbol.Barcode2.CODE11.CheckDigitCounts">
            <summary>
            The number of check digits to verify.
            </summary>
        </member>
        <member name="F:Symbol.Barcode2.CODE11.CheckDigitCounts.None">
            <summary>
            No check digit.
            </summary>
        </member>
        <member name="F:Symbol.Barcode2.CODE11.CheckDigitCounts.One">
            <summary>
            One check digit.
            </summary>
        </member>
        <member name="F:Symbol.Barcode2.CODE11.CheckDigitCounts.Two">
            <summary>
            Two check digits.
            </summary>
        </member>
        <member name="T:Symbol.Barcode2.I2OF5">
            <summary>
            I2OF5 class provides a level of abstraction for specific enumerated types.
            </summary>
        </member>
        <member name="T:Symbol.Barcode2.I2OF5.CheckDigitSchemes">
            <summary>
            The check digit type to verify.
            </summary>
        </member>
        <member name="F:Symbol.Barcode2.I2OF5.CheckDigitSchemes.None">
            <summary>
            No check digit.
            </summary>
        </member>
        <member name="F:Symbol.Barcode2.I2OF5.CheckDigitSchemes.USS">
            <summary>
            USS check digit.
            </summary>
        </member>
        <member name="F:Symbol.Barcode2.I2OF5.CheckDigitSchemes.OPCC">
            <summary>
            OPCC check digit.
            </summary>
        </member>
        <member name="T:Symbol.Barcode2.CODE39">
            <summary>
            CODE39 structure provides a level of abstraction for specific enumerated types.
            </summary>
        </member>
        <member name="T:Symbol.Barcode2.CODE39.SECURITYLEVEL">
            <summary>
            Supported security levels
            </summary>
        </member>
        <member name="F:Symbol.Barcode2.CODE39.SECURITYLEVEL.LEVEL_0">
            <summary>
            This setting allows the digital scanner to operate in its most aggressive state, while providing sufficient security in decoding most “in-spec” bar codes
            </summary>
        </member>
        <member name="F:Symbol.Barcode2.CODE39.SECURITYLEVEL.LEVEL_1">
            <summary>
            This default setting eliminates most misdecodes
            </summary>
        </member>
        <member name="F:Symbol.Barcode2.CODE39.SECURITYLEVEL.LEVEL_2">
            <summary>
            Select this option if Security level 1 fails to eliminate misdecodes
            </summary>
        </member>
        <member name="F:Symbol.Barcode2.CODE39.SECURITYLEVEL.LEVEL_3">
            <summary>
            If you selected Security Level 2 and misdecodes still occur, select this security level.
            NOTE: Note: Be advised, selecting LEVEL_3 is an extreme measure against mis-decoding severely out of spec bar codes. 
            Selecting this level of security significantly impairs the decoding ability of the digital scanner. 
            If you need this level of security, try to improve the quality of the bar codes
            </summary>
        </member>
        <member name="T:Symbol.Barcode2.CODE128">
            <summary>
            CODE128 structure provides a level of abstraction for specific enumerated types.
            </summary>
        </member>
        <member name="T:Symbol.Barcode2.CODE128.SECURITYLEVEL">
            <summary>
            Supported security levels
            </summary>
        </member>
        <member name="F:Symbol.Barcode2.CODE128.SECURITYLEVEL.LEVEL_0">
            <summary>
            This setting allows the digital scanner to operate in its most aggressive state, while providing sufficient security in decoding most “in-spec” bar codes
            </summary>
        </member>
        <member name="F:Symbol.Barcode2.CODE128.SECURITYLEVEL.LEVEL_1">
            <summary>
            This default setting eliminates most misdecodes
            </summary>
        </member>
        <member name="F:Symbol.Barcode2.CODE128.SECURITYLEVEL.LEVEL_2">
            <summary>
            Select this option if Security level 1 fails to eliminate misdecodes
            </summary>
        </member>
        <member name="F:Symbol.Barcode2.CODE128.SECURITYLEVEL.LEVEL_3">
            <summary>
            If you selected Security Level 2 and misdecodes still occur, select this security level.
            NOTE: Note: Be advised, selecting LEVEL_3 is an extreme measure against mis-decoding severely out of spec bar codes. 
            Selecting this level of security significantly impairs the decoding ability of the digital scanner. 
            If you need this level of security, try to improve the quality of the bar codes
            </summary>
        </member>
        <member name="T:Symbol.Barcode2.UCCLinkMode">
            <summary>
            Describes the UCC link mode state
            </summary>
        </member>
        <member name="F:Symbol.Barcode2.UCCLinkMode.UCC_NEVER">
            <summary>
            Link flag ignored.
            </summary>
        </member>
        <member name="F:Symbol.Barcode2.UCCLinkMode.UCC_ALWAYS">
            <summary>
            Always linked .
            </summary>
        </member>
        <member name="F:Symbol.Barcode2.UCCLinkMode.UCC_AUTO">
            <summary>
            Auto-discriminate.
            </summary>
        </member>
        <member name="T:Symbol.Barcode2.SIGNATURE">
            <summary>
            SIGNATURE class provides a level of abstraction for specific enumerated types.
            </summary>
        </member>
        <member name="T:Symbol.Barcode2.SIGNATURE.ImageFormat">
            <summary>
            Lists the image file formats supported by the SIGNATURE decoder for capturing an image.
            </summary>
        </member>
        <member name="F:Symbol.Barcode2.SIGNATURE.ImageFormat.IMAGE_FORMAT_JPEG">
            <summary>
            JPEG image file format
            </summary>
        </member>
        <member name="T:Symbol.Barcode2.ImageFormat">
            <summary>
            Lists the image file formats supported by the IMAGE decoder for capturing an image. 
            Also used to enumerate the list of supported image formats given by DeviceInfo.
            </summary>
        </member>
        <member name="F:Symbol.Barcode2.ImageFormat.IMAGE_FORMAT_JPEG">
            <summary>
            JPEG image file format
            </summary>
        </member>
        <member name="T:Symbol.Barcode2.BEEPPATTERN">
            <summary>
            Supported beep patterns.
            NOTE: All remote scanners do not support all beep patterns.
            Refer to the programmer's guide of the corresponding remote scanner 
            to find out the supported beep patterns. 
            For RS507 remote scanner, refer to "Using RS507 Bluetooth Scanner" 
            under Barcode section of the Programmer's Guide.
            </summary>
        </member>
        <member name="F:Symbol.Barcode2.BEEPPATTERN.BEEP_PATTERN_ONE_SHORT_HIGH">
            <summary>
            Beep pattern
            </summary>
        </member>
        <member name="F:Symbol.Barcode2.BEEPPATTERN.BEEP_PATTERN_TWO_SHORT_HIGH">
            <summary>
            Beep pattern
            </summary>
        </member>
        <member name="F:Symbol.Barcode2.BEEPPATTERN.BEEP_PATTERN_THREE_SHORT_HIGH">
            <summary>
            Beep pattern
            </summary>
        </member>
        <member name="F:Symbol.Barcode2.BEEPPATTERN.BEEP_PATTERN_FOUR_SHORT_HIGH">
            <summary>
            Beep pattern
            </summary>
        </member>
        <member name="F:Symbol.Barcode2.BEEPPATTERN.BEEP_PATTERN_FIVE_SHORT_HIGH">
            <summary>
            Beep pattern
            </summary>
        </member>
        <member name="F:Symbol.Barcode2.BEEPPATTERN.BEEP_PATTERN_ONE_SHORT_LOW">
            <summary>
            Beep pattern
            </summary>
        </member>
        <member name="F:Symbol.Barcode2.BEEPPATTERN.BEEP_PATTERN_TWO_SHORT_LOW">
            <summary>
            Beep pattern
            </summary>
        </member>
        <member name="F:Symbol.Barcode2.BEEPPATTERN.BEEP_PATTERN_THREE_SHORT_LOW">
            <summary>
            Beep pattern
            </summary>
        </member>
        <member name="F:Symbol.Barcode2.BEEPPATTERN.BEEP_PATTERN_FOUR_SHORT_LOW">
            <summary>
            Beep pattern
            </summary>
        </member>
        <member name="F:Symbol.Barcode2.BEEPPATTERN.BEEP_PATTERN_FIVE_SHORT_LOW">
            <summary>
            Beep pattern
            </summary>
        </member>
        <member name="F:Symbol.Barcode2.BEEPPATTERN.BEEP_PATTERN_ONE_LONG_HIGH">
            <summary>
            Beep pattern
            </summary>
        </member>
        <member name="F:Symbol.Barcode2.BEEPPATTERN.BEEP_PATTERN_TWO_LONG_HIGH">
            <summary>
            Beep pattern
            </summary>
        </member>
        <member name="F:Symbol.Barcode2.BEEPPATTERN.BEEP_PATTERN_THREE_LONG_HIGH">
            <summary>
            Beep pattern
            </summary>
        </member>
        <member name="F:Symbol.Barcode2.BEEPPATTERN.BEEP_PATTERN_FOUR_LONG_HIGH">
            <summary>
            Beep pattern
            </summary>
        </member>
        <member name="F:Symbol.Barcode2.BEEPPATTERN.BEEP_PATTERN_FIVE_LONG_HIGH">
            <summary>
            Beep pattern
            </summary>
        </member>
        <member name="F:Symbol.Barcode2.BEEPPATTERN.BEEP_PATTERN_ONE_LONG_LOW">
            <summary>
            Beep pattern
            </summary>
        </member>
        <member name="F:Symbol.Barcode2.BEEPPATTERN.BEEP_PATTERN_TWO_LONG_LOW">
            <summary>
            Beep pattern
            </summary>
        </member>
        <member name="F:Symbol.Barcode2.BEEPPATTERN.BEEP_PATTERN_THREE_LONG_LOW">
            <summary>
            Beep pattern
            </summary>
        </member>
        <member name="F:Symbol.Barcode2.BEEPPATTERN.BEEP_PATTERN_FOUR_LONG_LOW">
            <summary>
            Beep pattern
            </summary>
        </member>
        <member name="F:Symbol.Barcode2.BEEPPATTERN.BEEP_PATTERN_FIVE_LONG_LOW">
            <summary>
            Beep pattern
            </summary>
        </member>
        <member name="F:Symbol.Barcode2.BEEPPATTERN.BEEP_PATTERN_FAST_WARBLE">
            <summary>
            Beep pattern
            </summary>
        </member>
        <member name="F:Symbol.Barcode2.BEEPPATTERN.BEEP_PATTERN_SLOW_WARBLE">
            <summary>
            Beep pattern
            </summary>
        </member>
        <member name="F:Symbol.Barcode2.BEEPPATTERN.BEEP_PATTERN_MIX1">
            <summary>
            Beep pattern
            </summary>
        </member>
        <member name="F:Symbol.Barcode2.BEEPPATTERN.BEEP_PATTERN_MIX2">
            <summary>
            Beep pattern
            </summary>
        </member>
        <member name="F:Symbol.Barcode2.BEEPPATTERN.BEEP_PATTERN_MIX3">
            <summary>
            Beep pattern
            </summary>
        </member>
        <member name="F:Symbol.Barcode2.BEEPPATTERN.BEEP_PATTERN_MIX4">
            <summary>
            Beep pattern
            </summary>
        </member>
        <member name="T:Symbol.Barcode2.LEDPATTERN">
            <summary>
            Supported LED patterns.
            NOTE: All remote scanners do not support all LED patterns.
            Refer to the programmer's guide of the corresponding remote scanner 
            to find out the supported LED patterns. 
            For RS507 remote scanner, refer to "Using RS507 Bluetooth Scanner" 
            under Barcode section of the Programmer's Guide.
            </summary>
        </member>
        <member name="F:Symbol.Barcode2.LEDPATTERN.LED_PATTERN_DECODE">
            <summary>
            LED pattern interpreted as bit masks for 7 LEDs
            </summary>
        </member>
        <member name="F:Symbol.Barcode2.LEDPATTERN.LED_PATTERN_LED1">
            <summary>
            LED pattern interpreted as bit masks for 7 LEDs
            </summary>
        </member>
        <member name="F:Symbol.Barcode2.LEDPATTERN.LED_PATTERN_LED2">
            <summary>
            LED pattern interpreted as bit masks for 7 LEDs
            </summary>
        </member>
        <member name="F:Symbol.Barcode2.LEDPATTERN.LED_PATTERN_LED3">
            <summary>
            LED pattern interpreted as bit masks for 7 LEDs
            </summary>
        </member>
        <member name="F:Symbol.Barcode2.LEDPATTERN.LED_PATTERN_LED4">
            <summary>
            LED pattern interpreted as bit masks for 7 LEDs
            </summary>
        </member>
        <member name="F:Symbol.Barcode2.LEDPATTERN.LED_PATTERN_LED5">
            <summary>
            LED pattern interpreted as bit masks for 7 LEDs
            </summary>
        </member>
        <member name="F:Symbol.Barcode2.LEDPATTERN.LED_PATTERN_LED6">
            <summary>
            LED pattern interpreted as bit masks for 7 LEDs
            </summary>
        </member>
        <member name="F:Symbol.Barcode2.LEDPATTERN.LED_PATTERN_BLACK">
            <summary>
            LED blinking pattern
            </summary>
        </member>
        <member name="F:Symbol.Barcode2.LEDPATTERN.LED_PATTERN_PULSE_GREEN">
            <summary>
            LED blinking pattern
            </summary>
        </member>
        <member name="F:Symbol.Barcode2.LEDPATTERN.LED_PATTERN_PULSE_YELLOW">
            <summary>
            LED blinking pattern
            </summary>
        </member>
        <member name="F:Symbol.Barcode2.LEDPATTERN.LED_PATTERN_PULSE_RED">
            <summary>
            LED blinking pattern
            </summary>
        </member>
        <member name="F:Symbol.Barcode2.LEDPATTERN.LED_PATTERN_GREEN">
            <summary>
            LED blinking pattern
            </summary>
        </member>
        <member name="F:Symbol.Barcode2.LEDPATTERN.LED_PATTERN_YELLOW">
            <summary>
            LED blinking pattern
            </summary>
        </member>
        <member name="F:Symbol.Barcode2.LEDPATTERN.LED_PATTERN_RED">
            <summary>
            LED blinking pattern
            </summary>
        </member>
        <member name="F:Symbol.Barcode2.LEDPATTERN.LED_PATTERN_ALT_GB">
            <summary>
            LED blinking pattern. Alternate Green / Black
            </summary>
        </member>
        <member name="F:Symbol.Barcode2.LEDPATTERN.LED_PATTERN_ALT_YB">
            <summary>
            LED blinking pattern. Alternate Yellow / Black
            </summary>
        </member>
        <member name="F:Symbol.Barcode2.LEDPATTERN.LED_PATTERN_ALT_RB">
            <summary>
            LED blinking pattern. Alternate Red / Black
            </summary>
        </member>
        <member name="F:Symbol.Barcode2.LEDPATTERN.LED_PATTERN_ALT_GY">
            <summary>
            LED blinking pattern. Alternate Green / Yellow
            </summary>
        </member>
        <member name="F:Symbol.Barcode2.LEDPATTERN.LED_PATTERN_ALT_YR">
            <summary>
            LED blinking pattern. Alternate Yellow / Red
            </summary>
        </member>
        <member name="F:Symbol.Barcode2.LEDPATTERN.LED_PATTERN_ALT_RG">
            <summary>
            LED blinking pattern. Alternate Red / Green
            </summary>
        </member>
        <member name="F:Symbol.Barcode2.LEDPATTERN.LED_PATTERN_ALT_RYG">
            <summary>
            LED blinking pattern. Alternate Red / Yellow / Green
            </summary>
        </member>
        <member name="F:Symbol.Barcode2.LEDPATTERN.LED_PATTERN_ALT_GYB">
            <summary>
            LED blinking pattern. Alternate Green / Yellow / Black
            </summary>
        </member>
        <member name="F:Symbol.Barcode2.LEDPATTERN.LED_PATTERN_ALT_YRG">
            <summary>
            LED blinking pattern. Alternate Yellow / Red / Green
            </summary>
        </member>
        <member name="F:Symbol.Barcode2.LEDPATTERN.LED_PATTERN_ALT_RGB">
            <summary>
            LED blinking pattern. Alternate Red / Green / Black
            </summary>
        </member>
        <member name="F:Symbol.Barcode2.LEDPATTERN.LED_PATTERN_ALT_RYGB">
            <summary>
            LED blinking pattern. Alternate Red / Yellow / Green / Black
            </summary>
        </member>
        <member name="T:Symbol.Barcode2.DPMCAPABLE">
            <summary>
            Flag describing whether the scan engine is capable of decoding DPM bar codes. 
            </summary>
        </member>
        <member name="F:Symbol.Barcode2.DPMCAPABLE.NONCAPABLE">
            <summary>
            Not Capabale
            </summary>
        </member>
        <member name="F:Symbol.Barcode2.DPMCAPABLE.CAPABLE">
            <summary>
            Capable
            </summary>
        </member>
        <member name="T:Symbol.Barcode2.LCD_MODE">
            <summary>
            Enable or disable LCD mode (for Blockbuster imager devices only).
            </summary>
        </member>
        <member name="F:Symbol.Barcode2.LCD_MODE.DISABLE">
            <summary>
            Disables the LCD mode
            </summary>
        </member>
        <member name="F:Symbol.Barcode2.LCD_MODE.ENABLE">
            <summary>
            Enables LCD mode
            </summary>
        </member>
        <member name="T:Symbol.Barcode2.COUPON_REPORT_MODES">
            <summary>
            Supported Coupon modes
            </summary>
        </member>
        <member name="F:Symbol.Barcode2.COUPON_REPORT_MODES.OLD">
            <summary>
            Scanner will read only the old coupon format.
            </summary>
        </member>
        <member name="F:Symbol.Barcode2.COUPON_REPORT_MODES.NEW">
            <summary>
            Scanner will read only the new GS1 DataBar coupon format.
            </summary>
        </member>
        <member name="F:Symbol.Barcode2.COUPON_REPORT_MODES.BOTH">
            <summary>
            Scanner will read both old coupon format as well as the new GS1 DataBar coupon format.
            </summary>
        </member>
        <member name="T:Symbol.Barcode2.IMAGE_PIXEL_FORMATS">
            <summary>
            The pixel formats for images.
            </summary>
        </member>
        <member name="F:Symbol.Barcode2.IMAGE_PIXEL_FORMATS.IMG_8BPP">
            <summary>
            8 bits per pixel gray scale.
            </summary>
        </member>
        <member name="T:Symbol.Barcode2.AUX_FORMATS">
            <summary>
            The auxiliary data formats.
            </summary>
        </member>
        <member name="F:Symbol.Barcode2.AUX_FORMATS.NO_DATA">
            <summary>
            No auxiliary data.
            </summary>
        </member>
        <member name="F:Symbol.Barcode2.AUX_FORMATS.BARCODE_DATA">
            <summary>
            Barcode data; The multipacket composite barcode data.
            Either ScanData.AuxiliaryData.Text or ScanData.AuxiliaryData.RawData would be populated based on the flag Scandata.IsText being set to true or false.
            </summary>
        </member>
        <member name="F:Symbol.Barcode2.AUX_FORMATS.IMAGE_DATA">
            <summary>
            Image data.
            ScanData.AuxiliaryData.ImageStream, ScanData.AuxiliaryData.ImageHeight, ScanData.AuxiliaryData.ImageWidth and ScanData.AuxiliaryData.ImageFormat would be populated.
            GetBitmap() would return the Bitmap instance of the image.
            </summary>
        </member>
        <member name="F:Symbol.Barcode2.AUX_FORMATS.UNKNOWN">
            <summary>
            Unknown.
            The auxiliary data is there, whose type is not known.
            Only ScanData.AuxiliaryData.RawData would be populated.
            </summary>
        </member>
        <member name="T:Symbol.Barcode2.DOC_CAPTURE_MODES">
            <summary>
            The different modes of document capture (Decode Image Capture).    
            </summary>
        </member>
        <member name="F:Symbol.Barcode2.DOC_CAPTURE_MODES.BARCODE_ANCHORED">
            <summary>
            Barcode-anchored Mode.    
            </summary>
        </member>
        <member name="F:Symbol.Barcode2.DOC_CAPTURE_MODES.FREE_FORM">
            <summary>
            Free form Mode (Optionally Barcode-linked).    
            </summary>
        </member>
        <member name="F:Symbol.Barcode2.DOC_CAPTURE_MODES.BARCODE_LINKED">
            <summary>
            Barcode-linked Mode.    
            </summary>
        </member>
        <member name="T:Symbol.Barcode2.DOCCAP_TRIGGER_MODE">
            <summary>
            Indicates trigger mode of the document capture.
            </summary>
        </member>
        <member name="F:Symbol.Barcode2.DOCCAP_TRIGGER_MODE.AUTO">
            <summary>
            Trigger key should be pressed and hold until the capture of the document is completed and the final image gets displayed on the device’s screen.
            If the trigger key is released before the final image gets displayed, capture operation will get cancelled. 
            </summary>
            <remarks>This trigger mode is supported in DocCap 1.0 or later.</remarks>
        </member>
        <member name="F:Symbol.Barcode2.DOCCAP_TRIGGER_MODE.USER">
            <summary>
            Trigger key should be pressed and hold until the view finder is filled with the document/desired portion of the document to be captured.
            Once the trigger key is released, image will be captured and processed. If the capture is successful, final image will be displayed on 
            the device’s screen otherwise an error will be reported. After trigger is released, during the image processing, no trigger key will be
            allowed till image processing is complete. 
            </summary>
            <remarks><see cref="F:Symbol.Barcode2.DOCCAP_EDGE_DETECTION_TYPE.ADVANCED"/> is recommended to use while the User trigger mode is selected. This trigger mode is supported in DocCap 2.0 or later.</remarks>
        </member>
        <member name="T:Symbol.Barcode2.DOCCAP_EDGE_DETECTION_TYPE">
            <summary>
            Indicates edge detection types for document capture.
            </summary>
        </member>
        <member name="F:Symbol.Barcode2.DOCCAP_EDGE_DETECTION_TYPE.ANY">
            <summary>
            A document with a contrasting background or with a solid printed background will be captured.
            </summary>
        </member>
        <member name="F:Symbol.Barcode2.DOCCAP_EDGE_DETECTION_TYPE.SOLID_BLACK">
            <summary>
            Solid (or printed) and non broken black edge/ border required in the field of view to capture a document.
            Cropping would happen with the outer most “box” visible in the field of view.
            </summary>
        </member>
        <member name="F:Symbol.Barcode2.DOCCAP_EDGE_DETECTION_TYPE.SOLID_WHITE">
            <summary>
            Solid (or printed) and non broken white edge/ border required in the field of view to capture a document.
            Cropping would happen with the outer most “box” visible in the field of view.
            </summary>
        </member>
        <member name="F:Symbol.Barcode2.DOCCAP_EDGE_DETECTION_TYPE.ADVANCED">
            <summary>
            Aggressively detects the black and/ or white edges/ borders in the field of view and crops the document. 
            </summary>
            <remarks>
            This is supported only when the <see cref="P:Symbol.Barcode2.Config.DecodersClass.DOCCAPClass.TriggerMode"/> is set to <see cref="F:Symbol.Barcode2.DOCCAP_TRIGGER_MODE.USER"/>.
            </remarks>
        </member>
        <member name="T:Symbol.Barcode2.IMAGE_DESKEW_STATUS">
            <summary>
            When enabled, the captured image will be resized and cropped to the exact frame defined by ImageWidth and ImageHeight, else the row image will be produced. 
            It is recommended to use IMAGE_FORMAT_JPEG for ImageFileFormat if this is disabled.
            </summary>
        </member>
        <member name="F:Symbol.Barcode2.IMAGE_DESKEW_STATUS.DISABLED">
            <summary>
            Disabled.  
            </summary>
        </member>
        <member name="F:Symbol.Barcode2.IMAGE_DESKEW_STATUS.ENABLED">
            <summary>
            Enabled.  
            </summary>
        </member>
        <member name="T:Symbol.Barcode2.IMAGE_BRIGHTEN_STATUS">
            <summary>
            Whether the brightening of the image is required or not.     
            </summary>
        </member>
        <member name="F:Symbol.Barcode2.IMAGE_BRIGHTEN_STATUS.DISABLED">
            <summary>
            Disabled.  
            </summary>
        </member>
        <member name="F:Symbol.Barcode2.IMAGE_BRIGHTEN_STATUS.ENABLED">
            <summary>
            Enabled.  
            </summary>
        </member>
        <member name="T:Symbol.Barcode2.IMAGE_SHARPEN_STATUS">
            <summary>
            Whether the sharpening of the image is required or not. 
            </summary>
        </member>
        <member name="F:Symbol.Barcode2.IMAGE_SHARPEN_STATUS.DISABLED">
            <summary>
            Disabled.  
            </summary>
        </member>
        <member name="F:Symbol.Barcode2.IMAGE_SHARPEN_STATUS.ENABLED">
            <summary>
            Enabled.  
            </summary>
        </member>
        <member name="T:Symbol.Barcode2.IMAGE_HIGH_BLUR_STATUS">
            <summary>
            Whether the high blur decoding is required or not. 
            </summary>
        </member>
        <member name="F:Symbol.Barcode2.IMAGE_HIGH_BLUR_STATUS.DISABLED">
            <summary>
            Disabled.  
            </summary>
        </member>
        <member name="F:Symbol.Barcode2.IMAGE_HIGH_BLUR_STATUS.ENABLED">
            <summary>
            Enabled.  
            </summary>
        </member>
        <member name="T:Symbol.Barcode2.IMAGE_BOX_VERIFICATION_STATUS">
            <summary>
            Whether the box verification required or not.
            When enabled, it will try to locate an enclosing black lined box around the capture area. 
            Please make sure that the edges of the box are not broken and clearly visible.
            </summary>
        </member>
        <member name="F:Symbol.Barcode2.IMAGE_BOX_VERIFICATION_STATUS.DISABLED">
            <summary>
            Disabled.  
            </summary>
        </member>
        <member name="F:Symbol.Barcode2.IMAGE_BOX_VERIFICATION_STATUS.ENABLED">
            <summary>
            Enabled.  
            </summary>
        </member>
        <member name="T:Symbol.Barcode2.DOCCAP_SYMBOLOGY_MASKS">
            <summary>
            Lists the bit masks used for enabling/disabling the symbologies for document capture (Decode Image Capture).
            </summary>
        </member>
        <member name="T:Symbol.Barcode2.UnimplementedFunctionException">
            <summary>
            UnimplementedFunctionException is thrown when a method is 
            not available for use.
            </summary>
            <remarks>
            Derived off of the System.Exception class, this exception provides
            no additional functionality over its derived class.
            </remarks>
        </member>
        <member name="M:Symbol.Barcode2.UnimplementedFunctionException.#ctor(System.String)">
            <summary>
            UnimplementedFunctionException constructor with settable 
            string.
            </summary>
            <param name="sMessage">A string the contain information about
            the exception.</param>
        </member>
        <member name="T:Symbol.Barcode2.WrongMarshalSizeException">
            <summary>
            WrongMarshalSizeException is thrown when marshalling fails to work
            correctly. In particular, when a size of the marshaled buffer is not the
            correct size.
            </summary>
            <remarks>
            Derived off of the System.Exception class, this exception provides
            no additional functionality over its derived class.
            </remarks>
        </member>
        <member name="M:Symbol.Barcode2.WrongMarshalSizeException.#ctor(System.String)">
            <summary>
            WrongMarshalSizeException constructor with settable 
            string.
            </summary>
            <param name="sMessage">A string the contain information about
            the exception.</param>
        </member>
        <member name="T:Symbol.Barcode2.OperationFailureException">
            <summary>
            OperationFailureException is thrown when a failure occurs in the 
            class libraries. This failure usually is one that stops the proper
            functioning of the class libraries.
            </summary>
            <remarks>
            Derived off of the System.Exception class, this exception provides
            a results enum or integer to be associated with the exception. The
            number is returned in integer form via the Result property.
            </remarks>
        </member>
        <member name="M:Symbol.Barcode2.OperationFailureException.#ctor(System.String,Symbol.Barcode2.Results)">
            <summary>
            OperationFailureException constructor with settable string and 
            results enum.
            </summary>
            <param name="sMessage">A string the contains information about
            the exception.</param>
            <param name="resResults">A Results enumerated type that contains
            information about the exception.</param>
        </member>
        <member name="M:Symbol.Barcode2.OperationFailureException.#ctor(System.String,System.Int32)">
            <summary>
            OperationFailureException constructor with settable string and 
            integer value.
            </summary>
            <param name="sMessage">A string the contains information about
            the exception.</param>
            <param name="nResults">A integer that contains information about
            the exception.</param>
        </member>
        <member name="P:Symbol.Barcode2.OperationFailureException.Result">
            <summary>
            The number associated with the exception.
            </summary>
            <value>
            An integer result that contains information about what caused the
            exception.
            </value>
        </member>
        <member name="T:Symbol.Barcode2.InvalidUsageException">
            <summary>
            InvalidUsageException is thrown when a class library method or property
            is not used correctly.
            </summary>
            <remarks>
            Derived off of the System.Exception class, this exception provides
            no additional functionality over its derived class.
            </remarks>
        </member>
        <member name="M:Symbol.Barcode2.InvalidUsageException.#ctor">
            <summary>
            Default InvalidUsageException constructor.
            </summary>
        </member>
        <member name="M:Symbol.Barcode2.InvalidUsageException.#ctor(System.String)">
            <summary>
            InvalidUsageException constructor with settable string.
            </summary>
            <param name="sMessage">A string that contains information about
            the exception.</param>
        </member>
        <member name="T:Symbol.Barcode2.InvalidDataTypeException">
            <summary>
            InvalidDataTypeException is thrown when a class library accesses an
            invalid data type.
            </summary>
            <remarks>
            Derived off of the <see cref="T:Symbol.Barcode2.InvalidUsageException"/> class, 
            this exception provides no additional functionality over its
            derived class.
            </remarks>
        </member>
        <member name="M:Symbol.Barcode2.InvalidDataTypeException.#ctor">
            <summary>
            Default InvalidDataTypeException constructor.
            </summary>
        </member>
        <member name="M:Symbol.Barcode2.InvalidDataTypeException.#ctor(System.String)">
            <summary>
            InvalidDataTypeException constructor with settable string.
            </summary>
            <param name="sMessage">A string that contains information about
            the exception.</param>
        </member>
        <member name="T:Symbol.Barcode2.InvalidRequestException">
            <summary>
            InvalidRequestException is thrown when a class library trys to perform
            an invalid request.
            </summary>
            <remarks>
            Derived off of the <see cref="T:Symbol.Barcode2.InvalidUsageException"/> class, 
            this exception provides no additional functionality over its
            derived class.
            </remarks>
        </member>
        <member name="M:Symbol.Barcode2.InvalidRequestException.#ctor">
            <summary>
            Default InvalidRequestException constructor.
            </summary>
        </member>
        <member name="M:Symbol.Barcode2.InvalidRequestException.#ctor(System.String)">
            <summary>
            InvalidRequestException constructor with settable string.
            </summary>
            <param name="sMessage">A string that contains information about
            the exception.</param>
        </member>
        <member name="T:Symbol.Barcode2.InvalidIndexerException">
            <summary>
            InvalidIndexerException is thrown when a class library trys to access
            an invalid indexer.
            </summary>
            <remarks>
            Derived off of the <see cref="T:Symbol.Barcode2.InvalidUsageException"/> class, 
            this exception provides no additional functionality over its
            derived class.
            </remarks>
        </member>
        <member name="M:Symbol.Barcode2.InvalidIndexerException.#ctor">
            <summary>
            Default InvalidIndexerException constructor.
            </summary>
        </member>
        <member name="M:Symbol.Barcode2.InvalidIndexerException.#ctor(System.String)">
            <summary>
            InvalidIndexerException constructor with settable string.
            </summary>
            <param name="sMessage">A string that contains information about
            the exception.</param>
        </member>
        <member name="T:Symbol.Barcode2.DebugModeException">
            <summary>
            DebugModeException is thrown when a class library trys to perform
            functions that are invalid but not fatal.
            </summary>
            <remarks>
            Derived off of the System.Exception class, this exception provides
            the capability of associated the exception with an integer.
            </remarks>
        </member>
        <member name="M:Symbol.Barcode2.DebugModeException.#ctor(System.String,System.Int32)">
            <summary>
            DebugModeException constructor with settable string and integer
            information.
            </summary>
            <param name="sMessage">A string that contains information about
            the exception.</param>
            <param name="debugInfo">An integer that contains information about
            the exception.</param>
        </member>
        <member name="P:Symbol.Barcode2.DebugModeException.DebugResult">
            <summary>
            The number associated with the exception.
            </summary>
            <value>
            An integer result that contains information about what caused the
            exception.
            </value>
        </member>
        <member name="T:Symbol.Barcode2.DebugModeUnsupportedEnumValue">
            <summary>
            DebugModeUnsupportedEnumValue is thrown when a class library trys to
            access an enumerated type that is not valid.
            </summary>
            <remarks>
            Derived off of the <see cref="T:Symbol.Barcode2.DebugModeException"/> class, this
            exception provides no additional functionality.
            </remarks>
        </member>
        <member name="M:Symbol.Barcode2.DebugModeUnsupportedEnumValue.#ctor(System.String,System.Int32)">
            <summary>
            DebugModeUnsupportedEnumValue constructor with settable string and integer
            information.
            </summary>
            <param name="sMessage">A string that contains information about
            the exception.</param>
            <param name="debugInfo">An integer that contains information about
            the exception.</param>
        </member>
    </members>
</doc>
