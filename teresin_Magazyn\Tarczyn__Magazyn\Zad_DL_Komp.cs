﻿using System;
using System.Linq;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using System.Threading;
using System.Text.RegularExpressions;
using System.Xml;

namespace Tarczyn__Magazyn
{



    public partial class Zad_DL_Komp : Form,IZad_DL
    {
        //MainMenu myParent = null;
        IZad_Main myParent = null;
        TextBox[] TextBoxArray = null;

        TextBox AktualnyTextBox = null;


        List<string> _paleta_wybor = new List<string>();
        int[] _paleta = new int[100];


        //string ilosc_w_opakowaniu = "1";
        string ean_jednostki = "";
        string ean = "";
        string ilosc_w_opakowaniu = "";
        string ean_opakowanie_zbiorcze = "";
        string ilosc_szt_w_zbiorczym = "";
        int ean_ok = 0;



        string nosnik_nazwa = "";
        string nosnik_numer = "";
        string nosnik_typ_id = "";



        XmlNode node = null;



        public Zad_DL_Komp(IZad_Main c, XmlNode node2, string nosnik_numer_local)
        {
            node = node2;
            FullScreenMode.OknoOFF(this);
            InitializeComponent();
            TextBoxArray = new TextBox[] { ETYKIETA };
            myParent = c;
            Etykieta.Inicjalizacja();
            nosnik_numer = nosnik_numer_local;


            wms_wybrany.Text = Wlasciwosci.system_id;
            imie_nazwisk_label.Text = Wlasciwosci.wozek + ":" + Wlasciwosci.imie_nazwisko;
            //Wlasciwosci.system_id_id = "";
            wypelnij_dane();



        }

        void wypelnij_dane()
        {

            //if (node2["zadan"].InnerText == "0")
            //{
            //    myParent.Show();
            //    myParent.ZacznijNasluchiwanie("2");
            //    this.Close();
            //}
            ean_ok = 0;
            label7.Text = node["doc_type_nazwa"].InnerText + " " + node["doc_id"].InnerText;
            label12.Text = "Et: "+node["etykieta_id"].InnerText + "       DS" +node["paleta_id"].InnerText ;
            textBox4.Text = node["stare_m_nazwa"].InnerText;
            kod.Text = node["kod"].InnerText;
            textBox3.Text = node["kod_nazwa"].InnerText;
            LOT.Text = node["lot"].InnerText;
            zam_opak.Text = node["ilosc_opakowan"].InnerText;
            zam_szt.Text = node["ilosc"].InnerText;
            //ilosc_w_opakowaniu = node["ilosc_w_opakowaniu"].InnerText;
            ean = node["ean"].InnerText;
            ilosc_w_opakowaniu = node["ilosc_w_opakowaniu"].InnerText;
            ean_jednostki = node["ean_jednostki"].InnerText;
            ean_opakowanie_zbiorcze = node["ean_opakowanie_zbiorcze"].InnerText;
            ilosc_szt_w_zbiorczym = node["ilosc_szt_w_zbiorczym"].InnerText;
            licznik_label.Text = "" + node["ile_wszystkich"].InnerText + " / " + node["ile_gotowe"].InnerText;

            if (ean != "" || ean_jednostki != "" || ean_opakowanie_zbiorcze != "")
            {
                Ean_scan.Visible = true;
            }
            else
            {
                Ean_scan.Visible = false;
            }
            ETYKIETA.Focus();

        }



        private void ChangeColorLime(TextBox textBox)
        {
            textBox.BackColor = System.Drawing.Color.Lime;
        }
        private void ChangeColorRed(TextBox textBox)
        {
            textBox.BackColor = System.Drawing.Color.Red;
        }
        private void ChangeColorWhite(TextBox textBox)
        {
            textBox.BackColor = System.Drawing.Color.White;
        }


        private void button1_Click(object sender, EventArgs e)
        {
            Skaner.Przewij_Skanowanie();
            myParent.Show();
            this.Close();
        }
        int pole_tekstowe = 0;


        Zobele DaneZobele = new Zobele();
        int TrybSkanu = 0;
        // 0 = Etykieta
        // 1 = GTIN 
        // 2 = SSC
        // 3 = LOT
        // 4 = DW
        // 5 = QT



        private void TworzenieDL_Load(object sender, EventArgs e)
        {
            /*
            
            if (DaneZobele.USER != "")
            {
                if (DaneZobele.USER == Wlasciwosci.id_Pracownika)
                {
                    MessageBox.Show("Na tym urządzeniu nie zostały zapisane Twoje dane do systemu.");
                    ETYKIETA.Text = ETYKIETA.Text;
                    SSC.Text = SSC.Text;
                    LOT.Text = LOT.Text;
                    DW.Text = DW.Text;
                    QT.Text = DaneZobele.QT;
                }
                else
                {
                    MessageBox.Show("Na tym urządzeniu nie zostały zapisane dane do systemu użytkownika ID:" + Wlasciwosci.id_Pracownika);
                }
            }*/
            ETYKIETA.Focus();
        }



        private void GotFocus_TextBox(object sender, EventArgs e)
        {
            Skaner.Przewij_Skanowanie();
            TextBox Pole_Tekstowe = (TextBox)sender;

            AktualnyTextBox = Pole_Tekstowe;




            ZacznijSkanowanie();
        }

        private void LostFocus_TextBox(object sender, EventArgs e)
        {
            AktualnyTextBox = null;
            Skaner.Przewij_Skanowanie();

            TextBox Pole_Tekstowe = (TextBox)sender;

            if (ETYKIETA == Pole_Tekstowe)
            {


            }

            if (Pole_Tekstowe == Ean_scan)
            {
                if (!(Ean_scan.Text == ean || Ean_scan.Text == ean_jednostki || Ean_scan.Text == ean_opakowanie_zbiorcze))
                {
                    Ean_scan.Text = "";
                    ChangeColorRed(Ean_scan);
                    //MessageBox.Show("Kod ean towaru nie jest zgodny z zamawianym");                    
                    Ean_scan.Focus();
                }
            }
        }

        





        private void button2_Click(object sender, EventArgs e)
        {

            if (BazaDanychExternal.SprawdzCzyIstniejePolaczenie() == false)
            {
                MessageBox.Show("Brak połączenia. Podejdź do strefy Wifi i spróbuj ponownie");
                return;

            }
            //sprawdzajka poprawności ean
            if (ETYKIETA.Text == "")
            {
                MessageBox.Show("Brak etykiety");
                return;
            }
            if (Ean_scan.Visible == true && real_szt.Text!="0")
            {
                if (ean != "" && Ean_scan.Text == ean)
                {
                    ean_ok += 1;
                }
                if (ean_jednostki != "" && Ean_scan.Text == ean_jednostki)
                {
                    ean_ok += 1;
                }
                if (ean_opakowanie_zbiorcze != "" && Ean_scan.Text == ean_opakowanie_zbiorcze)
                {
                    ean_ok += 1;
                }
                if (ean_ok==0)
                {
                    MessageBox.Show("Wymagany jest jakikolwiek ean");
                    Ean_scan.Focus();
                    ChangeColorRed(Ean_scan);
                    return;

                }
                

            }
            if(zam_szt.Text!=real_szt.Text)
            {
                DialogResult result3 = MessageBox.Show("Ilość zamawiana jest inna niż realizowana. Kontynuować?",
                                            "Kontunuować?",
                                            MessageBoxButtons.YesNo,
                                            MessageBoxIcon.Question,
                                            MessageBoxDefaultButton.Button2);
                if (result3 == DialogResult.No)
                {
                    return;
                }
            }

            
            

            // http://25.56.91.22/wmsrawa/public/skaner_api/delivery_realizacja_kompletacji.php?akcja=realizacja_zadania&zadanie_dane_id=1760&etykieta_id_realizowana=9969437&imie_nazwisko=Lukasz%20Domanski&ilosc_pobierana=3
            //MessageBox.Show("delivery_realizacja_kompletacji.php?akcja=realizacja_zadania&zadanie_dane_id=" + node["id"].InnerText + "&etykieta_id_realizowana=" + ETYKIETA.Text + "&ilosc_pobierana=" + real_szt.Text + "&imie_nazwisko=" + Uri.EscapeUriString(Wlasciwosci.imie_nazwisko) + "&paleta_id=" + nosnik_numer);

            XmlNodeList xmlnode_local = null;
            XmlNode node_local = null;
            XmlDocument doc1 = WebService.Pobierz_XmlDocument("delivery_realizacja_kompletacji_new.php?akcja=realizacja_zadania&zadanie_dane_id=" + node["id"].InnerText + "&etykieta_id_realizowana=" + ETYKIETA.Text + "&ilosc_pobierana=" + real_szt.Text + "&imie_nazwisko=" + Uri.EscapeUriString(Wlasciwosci.imie_nazwisko) + "&paleta_id=" + nosnik_numer);



            xmlnode_local = doc1.GetElementsByTagName("dane");

            foreach (XmlNode wynik in xmlnode_local)
            {
                node_local = wynik;
            }
            //MessageBox.Show("4");
            //MessageBox.Show(node_local["komunikat"].InnerText);
            //MessageBox.Show(node_local.InnerXml);
            myParent.zadanie_head_id = node["zadanie_head_id"].InnerText;




            if (node_local["komunikat"].InnerText == "OK")
            {
                if (node_local["komunikat_do_informacji"].InnerText != "")
                {
                    MessageBox.Show(node_local["komunikat_do_informacji"].InnerText);
                }

                ChangeColorLime(ETYKIETA);



                if (node_local["czy_koniec_kompletacji"].InnerText == "NIE")
                {
                    myParent.Show();
                    myParent.ZacznijNasluchiwanie("1", "");
                    this.Close();
                }
                else
                {


                    //czy_koniec_kompletacji

                    // wtawienie podmianki palet

                    //XmlDocument doc2 = WebService.Pobierz_XmlDocument("delivery_realizacja_kompletacji_new.php?akcja=realizacja_zadania&zadanie_dane_id=" + node["id"].InnerText + "&etykieta_id_realizowana=" + ETYKIETA.Text + "&ilosc_pobierana=" + real_szt.Text + "&imie_nazwisko=" + Uri.EscapeUriString(Wlasciwosci.imie_nazwisko) + "&paleta_id=" + nosnik_numer);


                    //XmlNodeList xmlnode3 = WebService.Pobierz_XmlNodeList(doc1_etykieta, "etykiety");

                    //DataTable dt = Konwersja_XmlNodeList_DataTable(xmlnode3);
                    ////MessageBox.Show("2");
                    //PoleWyborListBox XA = new PoleWyborListBox(dt, 13F, "NIE");

                    //if (XA.ShowDialog() == DialogResult.OK)
                    //{
                    //    if (XA.wartosc_wybrana != "")
                    //    {
                    //        //MessageBox.Show("3");
                    //        ETYKIETA.Text = XA.wartosc_wybrana;
                    //    }
                    //}










                    myParent.Show();
                    myParent.ZacznijNasluchiwanie("2", "");
                    this.Close();
                }                
            }
            else
            {
                MessageBox.Show(node_local["komunikat"].InnerText);
                //MessageBox.Show(node_local.InnerXml);
                ChangeColorRed(ETYKIETA);
                return;
                //this.ZacznijSkanowanie();
            }








        }



        #region Skanowanie

        Thread Skanowanie = null;

        private void UstawSieNaNowePole()
        {
            if (TrybSkanu == 2)
            {
                TrybSkanu = 0;
            }
            else
            {
                TrybSkanu++;
            }
            TextBoxArray[TrybSkanu].Focus();
        }


        private void ZacznijSkanowanie()
        {
            StringBuilder login = new StringBuilder();
            Skaner.UstawTryb_String(login);
            Skaner.Zacznij_Skanowanie();
            Skanowanie = new Thread(() => CheckString(login));
            Skanowanie.IsBackground = true;
            Skanowanie.Start();
        }

        private void CheckString(StringBuilder cc)
        {
            while (true)
            {
                if (cc.ToString() != "")
                {
                    this.Invoke(new EventHandler(delegate
                    {
                        dodawanie(cc.ToString());
                    }));
                    return;
                }
                Thread.Sleep(200);
            }
        }


        private void Zakoncz_Skanowanie()
        {
            {
                Skanowanie.Abort();
                Skaner.Przewij_Skanowanie();
            }
        }

        private void dodawanie(string ops)
        {
            //MessageBox.Show("TrybSkanu:" + TrybSkanu + "   ,   ops.Length:" + ops.Length);


            AktualnyTextBox.Text = ops;



            /*
            if (ops.Length > 9)
            {
                //string cleanString = Regex.Replace(ops, @"[^a-zA-Z0-9\-\.\,]", "");
                ops = ops.Replace("\r\n", string.Empty);
                Dictionary<Etykieta.AII, string> ff = Etykieta.Parse(ops, false);

                foreach (Etykieta.AII tt in ff.Keys)
                {
                    //MessageBox.Show("Dla kodu:" + tt.AI + "   ,   Otrzymujemy wartość:" + ff[tt].ToString());
                    //Console.WriteLine("Dla kodu:" + tt.AI + "   ,   Otrzymujemy wartość:" + ff[tt].ToString());
                    //if (tt.AI == "00") { SSC.Focus(); SSC.Text = ff[tt].ToString(); }
                    if (tt.AI == "91") { kod.Focus(); kod.Text = Regex.Replace(ff[tt].ToString(), @"[^a-zA-Z0-9\-\.\,\\\/]", ""); }
                    if (tt.AI == "02") { kod.Focus(); kod.Text = Regex.Replace(ff[tt].ToString(), @"[^a-zA-Z0-9\-\.\,\\\/]", ""); }

                    if (tt.AI == "15")
                    {
                        //MessageBox.Show("Wykryłem datę ważnosci:"+ff[tt].ToString());
                        DW.Focus(); DW.Text = ff[tt].ToString();
                        //MessageBox.Show("Wykryłem datę ważnosci:" + DW.Text);
                    }
                    //if (tt.AI == "10" && AktualnyTextBox!=LOT) { LOT.Focus(); LOT.Text = ff[tt].ToString(); opak_real.Focus(); }

                    if (tt.AI == "10") { LOT.Focus(); //LOT.Text = decode_prefix_lot + Regex.Replace(ff[tt].ToString(), @"[^a-zA-Z0-9\-\.\,\\\/]", ""); 
                        real_szt.Focus(); }



                    if (tt.AI == "37")
                    {
                        real_szt.Focus(); real_szt.Text = ff[tt].ToString();
                        if (!(ilosc_w_opakowaniu == "" || ilosc_w_opakowaniu == "0"))
                        {
                            try
                            {
                                zam_szt.Text = (Convert.ToDouble(ff[tt].ToString()) * Convert.ToDouble(ilosc_w_opakowaniu)).ToString();
                            }
                            catch (Exception ex)
                            {
                                MessageBox.Show("Błędna ilość opakowań");
                                real_szt.Text = "";
                            }
                        }
                    }

                }
            }
            */

            if (AktualnyTextBox == Ean_scan)
            {
                
                ChangeColorLime(Ean_scan);
                Ean_scan.Text = ops.TrimStart('0');
                real_szt.Focus();
            }


            if (AktualnyTextBox == ETYKIETA)
            {

                XmlDocument doc1_etykieta = WebService.Pobierz_XmlDocument("delivery_skanowanie_etykiety.php?db=" + node["baza_danych"].InnerText + "&akcja=szukaj&system_id=" + node["system_id"].InnerText + "&etykieta_id=" + node["etykieta_id"].InnerText + "&skan=" + ops + "&kod_id=" + node["kod_id"].InnerText + "&doc_id=" + node["doc_id"].InnerText);
                XmlNodeList xmlnode_etykieta = null;
                xmlnode_etykieta = doc1_etykieta.GetElementsByTagName("dane");
                XmlNode node_etykieta = null;

                foreach (XmlNode wynik in xmlnode_etykieta)
                {
                    node_etykieta = wynik;
                }
                //MessageBox.Show(node_etykieta["komunikat"].InnerText);
                if (node_etykieta["komunikat"].InnerText != "OK")
                {
                    MessageBox.Show(node_etykieta["komunikat"].InnerText);
                    ETYKIETA.Text = "";
                    //this.ZacznijSkanowanie();

                }
                else
                {
                    //XmlNodeList xmlnode2 = null;

                    //MessageBox.Show("0");

                    if (node_etykieta["ilosc_pozycji"].InnerText != "1") //&& miejsce_id==""
                    {
                        //MessageBox.Show("1");
                        XmlNodeList xmlnode3 = WebService.Pobierz_XmlNodeList(doc1_etykieta, "etykiety");
                        
                        DataTable dt = Konwersja_XmlNodeList_DataTable(xmlnode3);
                        //MessageBox.Show("2");
                        PoleWyborListBox XA = new PoleWyborListBox(dt, 13F,"NIE");

                        if (XA.ShowDialog() == DialogResult.OK)
                        {
                            if (XA.wartosc_wybrana != "")
                            {
                                //MessageBox.Show("3");
                                ETYKIETA.Text = XA.wartosc_wybrana;
                            }
                        }
                    }

                    /*
                    if (xmlnode2.Count == 1)
                    {



                        
                       
                        
                       
                          
                        foreach (XmlNode wynik in xmlnode2)
                        {
                            if (ean != "")
                            {
                                if (ean != node["ean"].InnerText)
                                {

                                }
                            }

                            
                            ean_jednostki = node["ean_jednostki"].InnerText;
                            ilosc_w_opakowaniu = node["ilosc_w_opakowaniu"].InnerText;
                            ean_opakowanie_zbiorcze = node["ean_opakowanie_zbiorcze"].InnerText;
                            ilosc_szt_w_zbiorczym = node["ilosc_szt_w_zbiorczym"].InnerText;
                        }
                    }
                         * */
                    else
                    {
                        
                        //XmlNodeList xmlnode3 = WebService.Pobierz_XmlNodeList(doc1_etykieta, "etykiety");
                        //MessageBox.Show("6");
                        XmlNode xmlnode3 = WebService.Pobierz_XmlNode(doc1_etykieta, "etykiety");

                        //MessageBox.Show("Paleta posiada wiele artykułów. Przerywam operację");
                        ETYKIETA.Text = xmlnode3["id"].InnerText;
                        //MessageBox.Show("7");
                        //this.ZacznijSkanowanie();
                    }
                }


                if (Ean_scan.Visible == true)
                {
                    Ean_scan.Focus();
                }
                else
                {
                    real_szt.Focus();
                }

            }





        }

        #endregion

        private void button3_Click(object sender, EventArgs e)
        {

        }

        private static DataTable Konwersja_XmlNodeList_DataTable(XmlNodeList xmlnode2)
        {
            DataTable dt = new DataTable();
            dt.Columns.Add("id", typeof(string));
            dt.Columns.Add("nazwa_wyswietlana", typeof(string));
            foreach (XmlNode wynik in xmlnode2)
            {
                DataRow dtrow = dt.NewRow();
                dtrow["id"] = wynik["id"].InnerText;
                dtrow["nazwa_wyswietlana"] = wynik["nazwa_wyswietlana"].InnerText ;
                dt.Rows.Add(dtrow);
            }
            return dt;
        }

        private void przelicz_numery(object sender, EventArgs e)
        {
            if (System.Text.RegularExpressions.Regex.IsMatch(((TextBox)sender).Text, "[^0-9].[^0-9]"))
            {
                MessageBox.Show("Tylko liczby.");
                ((TextBox)sender).Text = ((TextBox)sender).Text.Remove(((TextBox)sender).Text.Length - 1, 1);
            }
            TextBox Pole_Tekstowe = (TextBox)sender;


            if (real_opak == Pole_Tekstowe)
            {
                if (real_opak.Text == "" || real_opak.Text == "0" || ilosc_w_opakowaniu == "" || ilosc_w_opakowaniu == "0")
                {
                    return;
                }
                real_szt.Text = (Convert.ToInt32(ilosc_w_opakowaniu) * Convert.ToInt32(real_opak.Text)).ToString();
            }
        }



        private void QT_KeyDown(object sender, KeyEventArgs e)
        {
            if (e.KeyCode == Keys.Enter)
            {
                button2_Click(this, new EventArgs());
            }
        }

        private void opak_real_KeyDown(object sender, KeyEventArgs e)
        {
            if (e.KeyCode == Keys.Enter)
            {
                button2_Click(this, new EventArgs());
            }
        }

        private void button9_Click(object sender, EventArgs e)
        {
            //Zad_DL_Podglad okno_nowy_nosnik = new Zad_DL_Podglad(this, node);
            //okno_nowy_nosnik.Show();
            //this.Hide();

                XmlDocument doc1_etykieta = WebService.Pobierz_XmlDocument("delivery_zadanie_kompletacja_wybor.php?db=" + node["baza_danych"].InnerText + "&akcja=podglad&zadanie_head_id=" + node["zadanie_head_id"].InnerText);
                XmlNode node_etykieta = WebService.Pobierz_XmlNode(doc1_etykieta, "dane");


                if (node_etykieta["komunikat"].InnerText != "OK")
                {
                    MessageBox.Show(node_etykieta["komunikat"].InnerText);
                }
                else
                {
                    
                        XmlNodeList xmlnode2 = WebService.Pobierz_XmlNodeList(doc1_etykieta, "pozycje");
                        DataTable dt = Konwersja_XmlNodeList_DataTable(xmlnode2);
                        //MessageBox.Show("2");
                        PoleWyborListBox XA = new PoleWyborListBox(dt, 9F, "NIE");

                        if (XA.ShowDialog() == DialogResult.OK)
                        {
                            if (XA.wartosc_wybrana != "")
                            {

                                if (node_etykieta["komunikat"].InnerText != "OK")
                                {
                                    MessageBox.Show(node_etykieta["komunikat"].InnerText);
                                }
                                else
                                {
                                    myParent.Show();
                                    myParent.ZacznijNasluchiwanie("", XA.wartosc_wybrana);
                                    this.Close();
                                }
                            }
                        }
                    
                }


        }

        private void button3_Click_1(object sender, EventArgs e)
        {
            myParent.delivery_odkladanie("TAK");

            this.Close();

            //myParent.nr_nosnika_kompletowany = "";
            //myParent.delivery_kompletacja(node);
        }

        private void button4_Click(object sender, EventArgs e)
        {
            XmlDocument doc1_etykieta = WebService.Pobierz_XmlDocument("delivery_pominiecie_zadania.php?zadanie_dane_id=" + node["id"].InnerText );
            XmlNode node_etykieta = WebService.Pobierz_XmlNode(doc1_etykieta, "dane");


            if (node_etykieta["komunikat"].InnerText != "OK")
            {
                MessageBox.Show(node_etykieta["komunikat"].InnerText);
            }
            else
            {

                myParent.Show();
                myParent.ZacznijNasluchiwanie("1", "");
                this.Close();

            }
        }






    }
}