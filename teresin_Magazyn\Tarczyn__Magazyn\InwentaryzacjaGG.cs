﻿using System;
using System.Linq;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using MySql.Data.MySqlClient;
using System.Text.RegularExpressions;
using System.Threading;
//using System.Data.SqlServerCe;
using System.Collections;


namespace Tarczyn__Magazyn
{
    public partial class InwentaryzacjaGG : Form
    {
        ActionMenu myParent = null;


        List<string> _inw_wybor = new List<string>();
        List<string> _inw_data = new List<string>();
        List<string> _inw_opis = new List<string>();
        int[] _inw = new int[100];

        List<string> _inw_hala = new List<string>();
        List<string> _inw_regal = new List<string>();
        List<string> _inw_poziom = new List<string>();
        List<string> _inw_status = new List<string>();

        int nr_inw = 0;
        private Thread Skanowanie;
        string etykieta_id = "0";

        string operac_id_global = "";

        string sap_gora_czesc2 = "";
        string kod = "";
        string podkod = "";
        double ilosc = 0;

        string ilosc_na_palecie = "";


        Dictionary<string, string> rec = new Dictionary<string, string>();

        int ile_do_inw = 0;


        //public static SqlCeConnection con = new SqlCeConnection(@"Data Source =.\Program Files\moto1\Northwind.sdf");







        public MySqlConnection ok = null;

        public InwentaryzacjaGG(ActionMenu MyParent)
        {
            InitializeComponent();
            this.myParent = MyParent;
            //wms_wybrany.Text = this.myParent.system_id;
            Wlasciwosci.CurrentOperacja = "20";
            imie_nazwisk_label.Text = Wlasciwosci.wozek + ":" + Wlasciwosci.imie_nazwisko;
            //ok = myParent.conn;
            wyszukaj_inwentaryzacje();
            wypelnij_poziom();
            wypelnij_hale();
            wypelnij_regal();
            wypelnij_status();
            this.ZacznijSkanowanie();
        }


        // podczas wchodzenia do inwentaryzacji
        private void wyszukaj_inwentaryzacje()
        {
            string zapytanie = "SELECT DATE_FORMAT(i.data,'%Y-%m-%d') as data,opis,inwentaryzacja_id FROM inwentaryzacja i  where active=1 group by i.data,opis,inwentaryzacja_id order by inwentaryzacja_id desc limit 40";
            object obj2 = BazaDanychExternal.Wyczytaj_Tabele(zapytanie);
            DataTable tabela = (DataTable)obj2;

            for (byte k = 0; k < tabela.Rows.Count; k++)
            {
                _inw_wybor.Add(tabela.Rows[k]["data"].ToString() + " | " + tabela.Rows[k]["opis"].ToString());
                _inw[k] = Convert.ToInt32(tabela.Rows[k]["inwentaryzacja_id"].ToString());
                _inw_data.Add(tabela.Rows[k]["data"].ToString());
                _inw_opis.Add(tabela.Rows[k]["opis"].ToString());
            }
            if (_inw_wybor.Count == 0)
            {
                MessageBox.Show("Brak Inwentaryzacji do wykonania");
            }
            else
            {
                comboBox1.Visible = true;
                BindingSource bs = new BindingSource();
                bs.DataSource = _inw_wybor;
                comboBox1.DataSource = bs;
            }
        }


        private void wypelnij_hale()
        {
            string zapytanie = "SELECT m.hala FROM miejsca m GROUP BY m.hala;";
            object obj2 = BazaDanychExternal.Wyczytaj_Tabele(zapytanie);
            DataTable tabela = (DataTable)obj2;

            for (byte k = 0; k < tabela.Rows.Count; k++)
            {
                _inw_hala.Add(tabela.Rows[k]["hala"].ToString());
                //_inw[k] = Convert.ToInt32(tabela.Rows[k]["inwentaryzacja_id"]);
            }
            if (_inw_wybor.Count == 0)
            {
                MessageBox.Show("Brak hali");
            }
            else
            {
                //comboBox1.Visible = true;
                BindingSource bs = new BindingSource();
                bs.DataSource = _inw_hala;
                hala_comboBox2.DataSource = bs;
            }
        }

        private void wypelnij_regal()
        {
            string zapytanie = "SELECT m.regal FROM miejsca m where LENGTH(regal)<=3 GROUP BY m.regal;";
            object obj2 = BazaDanychExternal.Wyczytaj_Tabele(zapytanie);
            DataTable tabela = (DataTable)obj2;

            for (byte k = 0; k < tabela.Rows.Count; k++)
            {
                _inw_regal.Add(tabela.Rows[k]["regal"].ToString());
            }
            if (_inw_regal.Count == 0)
            {
                MessageBox.Show("Brak regalow");
            }
            else
            {
                //comboBox1.Visible = true;
                BindingSource bs = new BindingSource();
                bs.DataSource = _inw_regal;
                comboBox3.DataSource = bs;
            }
        }
        private void wypelnij_poziom()
        {
            /*
            _inw_poziom.Add("A");
            _inw_poziom.Add("B");
            _inw_poziom.Add("C");
            _inw_poziom.Add("D");
            _inw_poziom.Add("E");
            //_inw_poziom.Add("F"); 
            //_inw_poziom.Add("G");
            //_inw_poziom.Add("H");

            */
            string zapytanie = "SELECT m.poziom FROM miejsca m left join etykiety e on e.miejscep=m.id where active=1 and poziom!=''  GROUP BY m.poziom order by poziom;";
            object obj2 = BazaDanychExternal.Wyczytaj_Tabele(zapytanie);
            DataTable tabela = (DataTable)obj2;

            for (byte k = 0; k < tabela.Rows.Count; k++)
            {
                _inw_poziom.Add(tabela.Rows[k]["poziom"].ToString());
                //_inw_poziom(tabela.Rows[k]["poziom"].ToString()) = tabela.Rows[k]["poziom"].ToString();
                //_inw_poziom[k] = tabela.Rows[k]["poziom"].ToString();
                //_inw[k] = Convert.ToInt32(tabela.Rows[k]["inwentaryzacja_id"]);
            }



            BindingSource bs = new BindingSource();
            bs.DataSource = _inw_poziom;
            comboBox4.DataSource = bs;
        }
        private void wypelnij_status()
        {
            //_inw_status.Add("ET_OK");
            _inw_status.Add("");
            _inw_status.Add("REPR");
            _inw_status.Add("USZK");
            _inw_status.Add("QUAL");
            _inw_status.Add("REND");


            BindingSource bs = new BindingSource();
            bs.DataSource = _inw_status;
            status_comboBox2.DataSource = bs;
        }

















        public void wczytanie_etykiety(string aaa)
        {
            Skaner.Przewij_Skanowanie();

            string zapytanie2 = "select kod,podkod,ilosc,m.hala,m.regal,m.miejsce,m.poziom,i.hala as hala_i,i.regal as regal_i,i.miejsce as miejsce_i,i.poziom as poziom_i,i.etykieta_id,i.paleta_id from inwentaryzacja i left join miejsca m on m.id=i.miejscep where (etykieta_id='" + etykieta_textbox.Text + "' or nrsap='" + etykieta_textbox.Text + "') and inwentaryzacja_id=" + _inw[comboBox1.SelectedIndex] + " order by  hala_i  limit 1";



            object obj2 = BazaDanychExternal.Wyczytaj_Tabele(zapytanie2);
            if (obj2 == null)
            {
                this.ZacznijSkanowanie();
            }
            DataTable table = (DataTable)obj2;



            if (table.Rows.Count < 1)
            {
                //MessageBox.Show("2");
                BazaDanychExternal.DokonajUpdate("Insert into inwentaryzacja(data,opis,ilosc,ilosc_spisana,kod,inwentaryzacja_id,hala,regal,miejsce,poziom,pracownik,ts,podkod,skan,stat,nrsap) values('" + _inw_data[comboBox1.SelectedIndex] + "','" + _inw_opis[comboBox1.SelectedIndex] + "',0,'" + ilosc_textBox.Text + "','','" + _inw[comboBox1.SelectedIndex] + "','" + _inw_hala[hala_comboBox2.SelectedIndex] + "','" + _inw_regal[comboBox3.SelectedIndex] + "','" + textBox1.Text + "','" + _inw_poziom[comboBox4.SelectedIndex] + "','" + Wlasciwosci.imie_nazwisko + "',now(),'" + podkod + "','" + etykieta_textbox.Text + "','" + _inw_status[status_comboBox2.SelectedIndex] + "','" + etykieta_textbox.Text + "');");
                stan_inwentaryzacji();
            }
            else
            {
                //MessageBox.Show("3");
                if (table.Rows[0]["hala_i"].ToString() != "")
                {
                    //MessageBox.Show("4");
                    BazaDanychExternal.DokonajUpdate("Insert into inwentaryzacja(data,opis,ilosc,ilosc_spisana,kod,inwentaryzacja_id,hala,regal,miejsce,poziom,pracownik,ts,podkod,skan,stat,nrsap) values('" + _inw_data[comboBox1.SelectedIndex] + "','" + _inw_opis[comboBox1.SelectedIndex] + "',0,'" + ilosc_textBox.Text + "','','" + _inw[comboBox1.SelectedIndex] + "','" + _inw_hala[hala_comboBox2.SelectedIndex] + "','" + _inw_regal[comboBox3.SelectedIndex] + "','" + textBox1.Text + "','" + _inw_poziom[comboBox4.SelectedIndex] + "','" + Wlasciwosci.imie_nazwisko + "',now(),'" + podkod + "','" + etykieta_textbox.Text + "','" + _inw_status[status_comboBox2.SelectedIndex] + "','" + etykieta_textbox.Text + "');");
                    stan_inwentaryzacji();
                }
                else
                {
                    //MessageBox.Show("5");
                    BazaDanychExternal.DokonajUpdate("update inwentaryzacja set ilosc_spisana=" + ilosc_textBox.Text + ",pracownik='" + Wlasciwosci.imie_nazwisko + "',ts=now(),hala='" + _inw_hala[hala_comboBox2.SelectedIndex] + "',regal='" + _inw_regal[comboBox3.SelectedIndex] + "',miejsce='" + textBox1.Text + "',poziom='" + _inw_poziom[comboBox4.SelectedIndex] + "',stat='" + _inw_status[status_comboBox2.SelectedIndex] + "',podkod='" + podkod + "',skan='" + etykieta_textbox.Text + "' where active=1 and inwentaryzacja_id=" + _inw[comboBox1.SelectedIndex] + " and (etykieta_id='" + etykieta_textbox.Text + "' or nrsap='" + etykieta_textbox.Text + "');"); //DATE_FORMAT(now(),'%Y-%m-%d %H:%m:%s')
                    stan_inwentaryzacji();
                }

            }
            status_comboBox2.SelectedIndex = status_comboBox2.Items.IndexOf("");



            BazaDanychExternal.DokonajUpdate("insert into operacje(etykieta_id, doc_type, doc_nr,  imie_nazwisko, typ_operacji,wozek) values('" + etykieta_id.ToString() + "','INW','" + _inw[comboBox1.SelectedIndex] + "','" + Wlasciwosci.imie_nazwisko + "','INW','" + Wlasciwosci.wozek + "');");




            ilosc_textBox.Text = "";
            etykieta_textbox.Text = "";
            podkod = "";
            kod = "";
            ilosc = 0;


            this.ZacznijSkanowanie();

        }







        // zatwierdzanie wczytanej etykiety
        private void button1_Click(object sender, EventArgs e)
        {
            //powrot.Visible = false;
            //comboBox1.Enabled = false;


            Skaner.Przewij_Skanowanie();



            if (ilosc_na_palecie != "")
            {
                if (ilosc_textBox.Text == "")
                {
                    MessageBox.Show("Wpisz ilosc etykiet.");
                    return;
                }

                if (ilosc_textBox.Text != ilosc_na_palecie)
                {
                    MessageBox.Show("Różne ilości. Zeskanuj etykiety");
                    ilosc_textBox.Text = "1";
                    etykieta_textbox.Focus();
                    this.ZacznijSkanowanie();
                    return;
                }
                else
                {
                    // zinwenaryzuj całą paletę

                    ilosc_textBox.Text = "1";
                    BazaDanychExternal.DokonajUpdate("update inwentaryzacja set ilosc_spisana=" + ilosc_textBox.Text + ",pracownik='" + Wlasciwosci.imie_nazwisko + "',ts=now(),hala='" + _inw_hala[hala_comboBox2.SelectedIndex] + "',regal='" + _inw_regal[comboBox3.SelectedIndex] + "',miejsce='" + textBox1.Text + "',poziom='" + _inw_poziom[comboBox4.SelectedIndex] + "',stat='" + _inw_status[status_comboBox2.SelectedIndex] + "',podkod='" + podkod + "',skan='" + paletaid.Text + "' where active=1 and inwentaryzacja_id=" + _inw[comboBox1.SelectedIndex] + " and (paleta_id='" + paletaid.Text + "');"); //DATE_FORMAT(now(),'%Y-%m-%d %H:%m:%s')
                    BazaDanychExternal.DokonajUpdate("insert into operacje(etykieta_id, doc_type, doc_nr, imie_nazwisko, typ_operacji,system_id,wozek,operac_id,ilosc) values('','INW','" + _inw[comboBox1.SelectedIndex] + "','" + Wlasciwosci.imie_nazwisko + "','INW','" + Wlasciwosci.system_id_id + "','" + Wlasciwosci.wozek + "','" + operac_id_global + "','1');");

                    stan_inwentaryzacji();
                    stan_inwentaryzacji_palety(paletaid.Text);
                    
                    status_comboBox2.SelectedIndex = status_comboBox2.Items.IndexOf("");
                    ilosc_na_palecie = "";
                    paletaid.Text = "";
                    ilosc_textBox.Text = "";
                    etykieta_textbox.Text = "";
                    status_comboBox2.SelectedIndex = status_comboBox2.Items.IndexOf("");

                    paletaid.Focus();

                    this.ZacznijSkanowanie();
                    return;


                }
            }
            else
            {
                MessageBox.Show("Wczytaj etykietę palety.");
                this.ZacznijSkanowanie();
                return;
            }


        }


        private void Zakoncz_Skanowanie(object sender, EventArgs e)
        {
            this.Skanowanie.Abort();
            Skaner.Przewij_Skanowanie();
        }

        private void powrot_Click(object sender, EventArgs e)
        {
            //this.Skanowanie.Abort();
            Wlasciwosci.CurrentOperacja = "0";
            Skaner.Przewij_Skanowanie();
            this.myParent.Show();
            this.Hide();

        }



        private void button1_Anuluj(object sender, EventArgs e)
        {

        }
        private void ZacznijSkanowanie()
        {
            StringBuilder login = new StringBuilder();
            Skaner.UstawTryb_String(login);
            Skaner.Zacznij_Skanowanie();
            this.Skanowanie = new Thread(() => this.CheckString(login));
            this.Skanowanie.IsBackground = true;
            this.Skanowanie.Start();
        }



        private void CheckString(StringBuilder cc)
        {
            EventHandler method = null;
            while (true)
            {
                if (cc.ToString() != "")
                {
                    if (method == null)
                    {
                        method = delegate
                        {
                            //this.vvv();

                            this.dodawanie(cc.ToString());
                        };
                        //method = () => this.dodawanie(cc.ToString());
                    }
                    this.Invoke(method);
                    return;
                }
                Thread.Sleep(200);
            }
        }







        // 'External' - wyszukuje w bazie etykiete i wypełnia pola
        private void dodawanie(string ops)
        {

            Skaner.Przewij_Skanowanie();


            if (!BazaDanychExternal.SprawdzCzyIstniejePolaczenie())
            {
                MessageBox.Show("Brak połączenia z bazą danych, spróbuj ponownie.");
                this.ZacznijSkanowanie();
                return;
            }
            else
            {
                if (ops.Substring(0, 2) == "MP")
                {
                    Regex regex = new Regex("-");
                    string[] words = null;
                    words = regex.Split(ops.Substring(3, ops.Length - 3));

                    hala_comboBox2.SelectedIndex = hala_comboBox2.Items.IndexOf(words[0].ToString());
                    comboBox3.SelectedIndex = comboBox3.Items.IndexOf(words[1].ToString());
                    textBox1.Text = words[2].ToString();
                    this.ZacznijSkanowanie();
                    return;
                    //poziom_comboBox4.SelectedIndex = poziom_comboBox4.Items.IndexOf(words[3].ToString());
                }
                if (ops.Substring(0, 2) == "DS")
                {

                    paletaid.Text = ops.Replace("DS", "");
                    string[] bb = new string[6];
                    bb = get_paleta(paletaid.Text);

                    if (bb[0] == "0")
                    {
                        MessageBox.Show("Brak w bazie  DS" + paletaid.Text);
                        this.ZacznijSkanowanie();
                        return;
                    }


                    hala_comboBox2.SelectedIndex = hala_comboBox2.Items.IndexOf(bb[2]);
                    comboBox3.SelectedIndex = comboBox3.Items.IndexOf(bb[3]);
                    textBox1.Text = bb[4];
                    comboBox4.SelectedIndex = comboBox4.Items.IndexOf(bb[5]);

                    ilosc_na_palecie = bb[0];

                    if (bb[1] != "0000-00-00 00:00:00")
                    {
                        MessageBox.Show("Paleta DS" + paletaid.Text + " inwenatryzowana " + Environment.NewLine +
                            "" + bb[1]);
                        //paletaid.Text = "";
                        this.ZacznijSkanowanie();
                        return;
                    }
                    ilosc_textBox.Focus();
                }
                else


                    if (ops.Length > 24)
                    {

                        MessageBox.Show("Nieprawidłowa etykieta " + Environment.NewLine +
                                        "" + ops);


                        this.ZacznijSkanowanie();
                        return;
                    }
                    else
                    {
                        etykieta_textbox.Text = ops;

                        ilosc_textBox.Text = "1";

                        string[] bb = new string[6];
                        bb = get_etykieta(etykieta_textbox.Text);
                        if (bb[0] == "0")
                        {
                            DialogResult result3 = MessageBox.Show("Brak w bazie " + etykieta_textbox.Text + ".  Inwentaryzować ?",
                                    "Czy Inwentaryzować?",
                                    MessageBoxButtons.YesNo,
                                    MessageBoxIcon.Question,
                                    MessageBoxDefaultButton.Button2);
                            if (result3 == DialogResult.Yes)
                            {
                                BazaDanychExternal.DokonajUpdate("Insert into inwentaryzacja(data,opis,ilosc,ilosc_spisana,kod,inwentaryzacja_id,hala,regal,miejsce,poziom,pracownik,ts,podkod,skan,uwaga,nrsap,paleta_id) values('" + _inw_data[comboBox1.SelectedIndex] + "','" + _inw_opis[comboBox1.SelectedIndex] + "',0,'1','','" + _inw[comboBox1.SelectedIndex] + "','" + _inw_hala[hala_comboBox2.SelectedIndex] + "','" + _inw_regal[comboBox3.SelectedIndex] + "','" + textBox1.Text + "','" + _inw_poziom[comboBox4.SelectedIndex] + "','" + Wlasciwosci.imie_nazwisko + "',now(),'','" + ops + "','','" + etykieta_textbox.Text + "','" + paletaid.Text + "');");
                                etykieta_textbox.Text = "";
                                stan_inwentaryzacji();
                            }
                            Skaner.Przewij_Skanowanie();

                            this.ZacznijSkanowanie();
                            return;
                        }

                        hala_comboBox2.SelectedIndex = hala_comboBox2.Items.IndexOf(bb[2]);
                        comboBox3.SelectedIndex = comboBox3.Items.IndexOf(bb[3]);
                        textBox1.Text = bb[4];
                        comboBox4.SelectedIndex = comboBox4.Items.IndexOf(bb[5]);


                        if (bb[1] != "0000-00-00 00:00:00")
                        {
                            MessageBox.Show("Etykieta " + etykieta_textbox.Text + " inwenatryzowana " + Environment.NewLine +
                                "" + bb[1]);
                            etykieta_textbox.Text = "";
                            this.ZacznijSkanowanie();
                            return;
                        }
                        ilosc_textBox.Text = "1";

                        if (paletaid.Text != "")
                        {
                            BazaDanychExternal.DokonajUpdate("update inwentaryzacja set ilosc_spisana=" + ilosc_textBox.Text + ",pracownik='" + Wlasciwosci.imie_nazwisko + "',ts=now(),hala='" + _inw_hala[hala_comboBox2.SelectedIndex] + "',regal='" + _inw_regal[comboBox3.SelectedIndex] + "',miejsce='" + textBox1.Text + "',poziom='" + _inw_poziom[comboBox4.SelectedIndex] + "',stat='" + _inw_status[status_comboBox2.SelectedIndex] + "',podkod='" + podkod + "',skan='" + paletaid.Text + "' where active=1 and inwentaryzacja_id=" + _inw[comboBox1.SelectedIndex] + " and (paleta_id='" + paletaid.Text + "' and nrsap='" + etykieta_textbox.Text + "');"); // //DATE_FORMAT(now(),'%Y-%m-%d %H:%m:%s')
                        
                        }
                        else
                        {
                            BazaDanychExternal.DokonajUpdate("update inwentaryzacja set ilosc_spisana=" + ilosc_textBox.Text + ",pracownik='" + Wlasciwosci.imie_nazwisko + "',ts=now(),hala='" + _inw_hala[hala_comboBox2.SelectedIndex] + "',regal='" + _inw_regal[comboBox3.SelectedIndex] + "',miejsce='" + textBox1.Text + "',poziom='" + _inw_poziom[comboBox4.SelectedIndex] + "',stat='" + _inw_status[status_comboBox2.SelectedIndex] + "',podkod='" + podkod + "',skan='" + paletaid.Text + "' where active=1 and inwentaryzacja_id=" + _inw[comboBox1.SelectedIndex] + " and ( nrsap='" + etykieta_textbox.Text + "');"); //paleta_id='" + paletaid.Text + "' and //DATE_FORMAT(now(),'%Y-%m-%d %H:%m:%s')
                        
                        }

                        
                        
                        BazaDanychExternal.DokonajUpdate("insert into operacje(etykieta_id, doc_type, doc_nr, imie_nazwisko, typ_operacji,system_id,wozek,operac_id,ilosc) values('" + etykieta_textbox.Text + "','INW','" + _inw[comboBox1.SelectedIndex] + "','" + Wlasciwosci.imie_nazwisko + "','INW','" + Wlasciwosci.system_id_id + "','" + Wlasciwosci.wozek + "','" + operac_id_global + "','1');");

                        stan_inwentaryzacji();
                        stan_inwentaryzacji_palety(bb[6]);
                        etykieta_textbox.Text = "";
                        ilosc_textBox.Text = "";
                        //status_comboBox2.SelectedIndex = status_comboBox2.Items.IndexOf("");





                        /*




                        //MessageBox.Show("A");
                        //ops = Convert.ToInt64(ops).ToString();
                        string zapytanie = "";
                        etykieta_id ="0";
                        zapytanie = "select kod,podkod,TRIM(TRAILING '.' FROM TRIM(TRAILING '0' from i.ilosc)) as ilosc,m.hala,m.regal,m.miejsce,m.poziom,i.hala as hala_i,i.regal as regal_i,i.miejsce as miejsce_i,i.poziom as poziom_i,i.etykieta_id from inwentaryzacja i left join miejsca m on m.id=i.miejscep where (etykieta_id='" + ops + "' or nrsap='" + ops + "') and inwentaryzacja_id=" + _inw[comboBox1.SelectedIndex] + " limit 1";

                        //MessageBox.Show("B");

                        object obj2 = BazaDanychExternal.Wyczytaj_Tabele(zapytanie);
                        if (obj2 == null) 
                        {
                            this.ZacznijSkanowanie();
                        }
                        DataTable table = (DataTable)obj2;

                        //MessageBox.Show("C");

                        if (table.Rows.Count < 1)
                        {
                            //MessageBox.Show("D");
                            BazaDanychExternal.DokonajUpdate("Insert into inwentaryzacja(data,opis,ilosc,ilosc_spisana,kod,inwentaryzacja_id,hala,regal,miejsce,poziom,pracownik,ts,podkod,skan,uwaga) values('" + _inw_data[comboBox1.SelectedIndex] + "','" + _inw_opis[comboBox1.SelectedIndex] + "',0,'0','10101','" + _inw[comboBox1.SelectedIndex] + "','" + _inw_hala[hala_comboBox2.SelectedIndex] + "','" + _inw_regal[comboBox3.SelectedIndex] + "','" + textBox1.Text + "','" + _inw_poziom[comboBox4.SelectedIndex] + "','" + Wlasciwosci.imie_nazwisko + "',now(),'" + podkod + "','" + ops + "','brak_w_inw');");
                            MessageBox.Show("Brak w tej inwentaryzacji etykiety: " + ops);

                            //MessageBox.Show("E");
                        }
                        else
                        {
                            if (table.Rows[0]["hala_i"].ToString() != "")
                            {
                                //MessageBox.Show("F");
                                BazaDanychExternal.DokonajUpdate("Insert into inwentaryzacja(data,opis,ilosc,ilosc_spisana,kod,inwentaryzacja_id,hala,regal,miejsce,poziom,pracownik,ts,podkod,skan,uwaga) values('" + _inw_data[comboBox1.SelectedIndex] + "','" + _inw_opis[comboBox1.SelectedIndex] + "',0,'0','10101','" + _inw[comboBox1.SelectedIndex] + "','" + _inw_hala[hala_comboBox2.SelectedIndex] + "','" + _inw_regal[comboBox3.SelectedIndex] + "','" + textBox1.Text + "','" + _inw_poziom[comboBox4.SelectedIndex] + "','" + Wlasciwosci.imie_nazwisko + "',now(),'" + podkod + "','" + ops + "','byla_juz');");
                                //MessageBox.Show("Był inwentaryzowana w " + table.Rows[0]["hala_i"].ToString() + " " + table.Rows[0]["regal_i"].ToString() + "-" + table.Rows[0]["miejsce_i"].ToString() + "-" + table.Rows[0]["poziom_i"].ToString());

                                //MessageBox.Show("G");
                                    DialogResult result3 = MessageBox.Show("Był inwentaryzowana w" + table.Rows[0]["hala_i"].ToString() + " " + table.Rows[0]["regal_i"].ToString() + "-" + table.Rows[0]["miejsce_i"].ToString() + "-" + table.Rows[0]["poziom_i"].ToString()+". Inwentaryzować jeszcze raz?",
                                    "Czy ponownie?",
                                    MessageBoxButtons.YesNo,
                                    MessageBoxIcon.Question,
                                    MessageBoxDefaultButton.Button2);
                                    if (result3 == DialogResult.Yes)
                                    {
                                        etykieta_id = table.Rows[0]["etykieta_id"].ToString();
                                        podkod = table.Rows[0]["podkod"].ToString();
                                        label12.Text = table.Rows[0]["kod"].ToString();
                                        ilosc_textBox.Text = table.Rows[0]["ilosc"].ToString();
                                        hala_comboBox2.SelectedIndex = hala_comboBox2.Items.IndexOf(table.Rows[0]["hala"].ToString());
                                        comboBox3.SelectedIndex = comboBox3.Items.IndexOf(table.Rows[0]["regal"].ToString());
                                        comboBox4.SelectedIndex = comboBox4.Items.IndexOf(table.Rows[0]["poziom"].ToString());
                                        textBox1.Text = table.Rows[0]["miejsce"].ToString();
                                        etykieta_textbox.Text = ops;
                                        button1.Focus();
                                        this.ZacznijSkanowanie();
                                        return; 

                                    }
                                    else
                                    {
                                        this.ZacznijSkanowanie();
                                        return;
                                    }                                
                            }
                            else
                            {

                                //MessageBox.Show("H");
                                etykieta_id = table.Rows[0]["etykieta_id"].ToString();
                                podkod = table.Rows[0]["podkod"].ToString();
                                label12.Text = table.Rows[0]["kod"].ToString();
                                ilosc_textBox.Text = table.Rows[0]["ilosc"].ToString();
                                hala_comboBox2.SelectedIndex = hala_comboBox2.Items.IndexOf(table.Rows[0]["hala"].ToString());
                                comboBox3.SelectedIndex = comboBox3.Items.IndexOf(table.Rows[0]["regal"].ToString());
                                comboBox4.SelectedIndex = comboBox4.Items.IndexOf(table.Rows[0]["poziom"].ToString());
                                textBox1.Text = table.Rows[0]["miejsce"].ToString();
                                etykieta_textbox.Text = ops;
                                button1.Focus();
                                //kod_textbox.SelectionStart = kod_textbox.Text.Length;
                                this.ZacznijSkanowanie();
                                return; 
                            }
                            

                        }
                        */
                    }
            }
            this.ZacznijSkanowanie();
        }

        // 'External' po każdym dodaniu przelicza ile jest zrobionego
        private void stan_inwentaryzacji()
        {
            string zapytanie = "";
            zapytanie = "SELECT sum(if(ilosc_spisana is null,0,1)) as ilosc_zliczona, count(1) as stan FROM inwentaryzacja i WHERE i.inwentaryzacja_id=" + _inw[comboBox1.SelectedIndex] + " and kod!='10101';";

            object obj2 = BazaDanychExternal.Wyczytaj_Tabele(zapytanie);

            DataTable table = (DataTable)obj2;
            if (table.Rows.Count < 1)
            {
                ilosc_etykiet.Text = "Brak et.";
            }
            else
            {
                ilosc_etykiet.Text = table.Rows[0]["ilosc_zliczona"].ToString() + "/" + table.Rows[0]["stan"].ToString();
            }
        }

        private void stan_inwentaryzacji_palety(String paleta_id)
        {
            string zapytanie = "";
            zapytanie = "SELECT sum(if(ilosc_spisana is null,0,1)) as ilosc_zliczona, count(1) as stan FROM inwentaryzacja i WHERE i.inwentaryzacja_id=" + _inw[comboBox1.SelectedIndex] + " and kod!='10101' and paleta_id="+paleta_id;

            object obj2 = BazaDanychExternal.Wyczytaj_Tabele(zapytanie);

            DataTable table = (DataTable)obj2;
            if (table.Rows.Count < 1)
            {
                label13.Text = "Brak et.";
            }
            else
            {
                label13.Text = table.Rows[0]["ilosc_zliczona"].ToString() + "/" + table.Rows[0]["stan"].ToString();
            }
        }



        private string External_Count_Inwent()
        {
            string zapytanie = "";
            zapytanie = "SELECT sum(if(ilosc_spisana is null,0,1)) as ilosc_zliczona, count(1) as stan FROM inwentaryzacja i WHERE i.inwentaryzacja_id=" + _inw[comboBox1.SelectedIndex] + ";";
            string aa = (string)BazaDanychExternal.Wyczytaj_Jedna_Wartosc(zapytanie);
            return aa;
        }



        private string[] get_paleta(string paleta) //pobiera
        {
            string zapytanie = "SELECT count(1) as ile,DATE_FORMAT(i.ts,'%Y-%m-%d %H:%m:%s') as ts,m.hala,m.regal,m.miejsce,m.poziom FROM inwentaryzacja i left join miejsca m on m.id=i.miejscep  WHERE i.inwentaryzacja_id=" + _inw[comboBox1.SelectedIndex] + " AND i.paleta_id=" + paleta + ";";
            object obj2 = BazaDanychExternal.Wyczytaj_Tabele(zapytanie);
            DataTable tabela = (DataTable)obj2;
            string[] bb = new string[6];

            if (tabela.Rows.Count < 1)  //sprawdza czy jest etykieta
            {
                bb[0] = "0";
                return bb;
            }

            bb[0] = tabela.Rows[0]["ile"].ToString();
            bb[1] = tabela.Rows[0]["ts"].ToString();
            bb[2] = tabela.Rows[0]["hala"].ToString();
            bb[3] = tabela.Rows[0]["regal"].ToString();
            bb[4] = tabela.Rows[0]["miejsce"].ToString();
            bb[5] = tabela.Rows[0]["poziom"].ToString();
            //bb[2] = tabela.Rows[0]["listcontrol_id"].ToString();
            return bb;
        }

        private string[] get_etykieta(string etykieta) //pobiera
        {

            string zapytanie = "SELECT count(1) as ile,DATE_FORMAT(i.ts,'%Y-%m-%d %H:%m:%s') as ts,m.hala,m.regal,m.miejsce,m.poziom,i.paleta_id  FROM inwentaryzacja i left join miejsca m on m.id=i.miejscep WHERE i.inwentaryzacja_id=" + _inw[comboBox1.SelectedIndex] + " AND i.nrsap='" + etykieta + "' limit 1;";
            
            if (paletaid.Text != "")
            {
                zapytanie = "SELECT count(1) as ile,DATE_FORMAT(i.ts,'%Y-%m-%d %H:%m:%s') as ts,m.hala,m.regal,m.miejsce,m.poziom,i.paleta_id  FROM inwentaryzacja i left join miejsca m on m.id=i.miejscep WHERE i.inwentaryzacja_id=" + _inw[comboBox1.SelectedIndex] + " AND i.nrsap='" + etykieta + "' and i.paleta_id='" + paletaid.Text + "' limit 1;";
            }
            object obj2 = BazaDanychExternal.Wyczytaj_Tabele(zapytanie);
            DataTable tabela = (DataTable)obj2;
            string[] bb = new string[7];
            if (tabela.Rows.Count < 1)  //sprawdza czy jest etykieta
            {
                bb[0] = "0";
                return bb;
            }

            bb[0] = tabela.Rows[0]["ile"].ToString();
            bb[1] = tabela.Rows[0]["ts"].ToString();
            bb[2] = tabela.Rows[0]["hala"].ToString();
            bb[3] = tabela.Rows[0]["regal"].ToString();
            bb[4] = tabela.Rows[0]["miejsce"].ToString();
            bb[5] = tabela.Rows[0]["poziom"].ToString();
            bb[6] = tabela.Rows[0]["paleta_id"].ToString();
            return bb;
        }
































        private void czysc()
        {

            //skaner = "";
            //sap_gora_czesc2 = "";
            //kod = "";
            //podkod = "";
            //kod_textbox.Text = "";

        }



        private void timer1_Tick(object sender, EventArgs e)
        {
            this.Close();
            myParent.Show();
        }



        #region fullscreen
        private void fullscreenmode()
        {
            this.Width = Screen.PrimaryScreen.Bounds.Width;
            this.Height = Screen.PrimaryScreen.Bounds.Height;
            this.Left = 0;
            this.Top = 0;
        }
        #endregion

        private void comboBox1_SelectedIndexChanged(object sender, EventArgs e)
        {
            //nr_inw = _inw[comboBox1.SelectedIndex];

        }

        private void button4_Click(object sender, EventArgs e)
        {
            //comboBox1.
            //this.Skanowanie.Abort();

            
            string zapytanie = "SELECT last FROM docnumber d WHERE d.name='operacja_id';";

            operac_id_global = (string)BazaDanychExternal.Wyczytaj_Jedna_Wartosc(zapytanie);

            string zapyt = "update docnumber d set last=last+1 WHERE d.name='operacja_id'; ";
            BazaDanychExternal.Wyczytaj_Jedna_Wartosc(zapyt);

            comboBox1.Enabled = false;
            Skaner.Przewij_Skanowanie();

            ZacznijSkanowanie();


        }


        private void button3_Click(object sender, EventArgs e)
        {
            //kod_textbox.Text = "IT";
            //kod_textbox.Focus();
            //kod_textbox.SelectionStart = kod_textbox.Text.Length;
        }

        private void button5_Click(object sender, EventArgs e)
        {
            //kod_textbox.Text = "PL";
            //kod_textbox.Focus();
            //kod_textbox.SelectionStart = kod_textbox.Text.Length;
        }

        private void button2_Click(object sender, EventArgs e)
        {
            Skaner.Przewij_Skanowanie();
            if (etykieta_textbox.Text == "")
            {

                MessageBox.Show("Brak etykiety lub palety");
                this.ZacznijSkanowanie();
                return;
            }
            else
            {
                dodawanie(etykieta_textbox.Text);
            }
        }

        private void button7_Click(object sender, EventArgs e)
        {
            Skaner.Przewij_Skanowanie();
            if (paletaid.Text == "")
            {
                MessageBox.Show("Brak palety");
                this.ZacznijSkanowanie();
                return;
            }
            else
            {
                dodawanie("DS" + paletaid.Text);
            }
        }

        private void paleta_focus(object sender, EventArgs e)
        {
            paletaid.Text = "";
        }







    }
}