<?php

include_once 'Db.class.php';
include_once 'funkcje.inc';

$db = new Db();
$akcja = $_GET['akcja'];
$wozek = $_GET['wozek'];
$pracownik = $_GET['pracownik'];

$komunikat = "OK";

if ($akcja == "pobierz") 
{

    $dane = wyswietl_wersje($db, $wozek, $pracownik);

    return xml_from_indexed_array(array('komunikat' => $komunikat, 'dane' => $dane));
}

function wyswietl_wersje($db, $wozek, $pracownik) 
{
    $sql = "SELECT 1 as ile  FROM wozki_historia w WHERE w.wozek_id='". $wozek ."' and w.pracownik_id='". $pracownik ."' AND typ_operacji='logowanie' AND w.ts like concat(CURDATE(),'%') ORDER BY w.id desc limit 1;";
    $result = $db->mGetResultAsXML($sql);
    
    return $result;
}