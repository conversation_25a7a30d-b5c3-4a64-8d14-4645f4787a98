﻿using System;
using System.Linq;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using System.Threading;



namespace Tarczyn__Magazyn
{



    public partial class GnGNew : Form
    {
        //MainMenu myParent = null;
        ActionMenu myParent = null;
        string operac_id_global = "";
        string miejsce_id = "0";
        int numer_dokumentu;

        string awizacja_id = "0";


        public GnGNew(ActionMenu c)
        {



            FullScreenMode.OknoOFF(this);
            if (Wlasciwosci.GNG != "")
            {
                Wlasciwosci.bazaDanych = 2;
                BazaDanychExternal.ChangeHost(Wlasciwosci.GNG, Wlasciwosci.Varhost, Wlasciwosci.VarLoginMYSQL, Wlasciwosci.VarHasloMYSQL);
                //BazaDanychExternal.ChangeHost(Wlasciwosci.Varbaza, Wlasciwosci.Varhost, Wlasciwosci.VarLoginMYSQL, Wlasciwosci.VarHasloMYSQL);
            }
            InitializeComponent();
            myParent = c;
        }

        /// <summary>
        /// wyjscie z GnG
        /// </summary>
        private void button1_Click(object sender, EventArgs e)
        {


            if (Skanowanie != null)
            {
                Zakoncz_Skanowanie();

            }
            ////BazaDanychExternal.ChangeHost(Wlasciwosci.Varbaza, Wlasciwosci.Varhost, Wlasciwosci.VarLoginMYSQL, Wlasciwosci.VarHasloMYSQL);
            myParent.Show();
            this.Close();
        }




        #region Skanowanie


        bool isalive = false;
        Thread Skanowanie = null;
        /// <summary>
        /// tryb skanowania:
        /// 1 - kontener
        /// 2 - dostawa na paletach
        /// 3 - po segregacji
        /// 4 - do kompletacji wysylki
        /// 5 - z produkcji
        /// 6 - inwentaryzacja RICI
        /// 7 - z produkcji - TYP_S
        /// 8 - kompletacja dostawy
        /// </summary>
        int trybskanowania = 0;
        List<string> ListaKartonow = new List<string>();
        string grupaKartonow = "";
        string previousLot = "";

        private void ZacznijSkanowanie()
        {
            StringBuilder login = new StringBuilder();
            Skaner.UstawTryb_String(login);
            Skaner.Zacznij_Skanowanie();
            Skanowanie = new Thread(() => CheckString(login));
            Skanowanie.IsBackground = true;
            Skanowanie.Start();
            isalive = true;
        }

        private void CheckString(StringBuilder cc)
        {
            while (true)
            {
                if (cc.ToString() != "")
                {
                    this.Invoke(new EventHandler(delegate
                    {
                        dodawanie(cc.ToString());
                    }));
                    return;
                }
                Thread.Sleep(200);
            }
        }


        private void Zakoncz_Skanowanie()
        {
            {
                Skanowanie.Abort();
                Skaner.Przewij_Skanowanie();
                isalive = false;
            }
        }

        private static bool SprawdzNumer(string value)
        {
            try
            {
                Convert.ToInt32(value);
                return true;
            }
            catch
            {
                if (value == null) value = "";
                return false;
            }
        }



        string paletyzacja = "";
        private void dodawanie(string ops)
        {
            if (BazaDanychExternal.SprawdzCzyIstniejePolaczenie() == false)
            {
                MessageBox.Show("Brak połączenia z bazą danych , spróbuj ponownie.");
                ZacznijSkanowanie();
                return;
            }


            //RICI
            //////if (trybskanowania == 5)
            //////{

            //////    object sprawdz = BazaDanychExternal.Wyczytaj_Tabele("select paletaNR,paleta FROM wmsgg.importRICI where CD_CONTENITORE='" + ops + "'");
            //////    if (sprawdz == null)
            //////    {
            //////        MessageBox.Show("Ten karton nie istnieje w bazie danych.");
            //////        ZacznijSkanowanie();
            //////        return;
            //////    }
            //////    DataTable rr = (DataTable)sprawdz;
            //////    if (grupaKartonow == "")
            //////    {
            //////        grupaKartonow = rr.Rows[0]["paleta"].ToString();
            //////    }
            //////    else
            //////    {
            //////        MessageBox.Show("Ten karton pasuje do zeskanowanej wcześniej grupy.");
            //////        ZacznijSkanowanie();
            //////        return;
            //////    }
            //////    ListaKartonow.Add("'" + ops + "'");
            //////    Licznik_Liczba.Text = (Convert.ToInt32(Licznik_Liczba.Text) + 1).ToString();
            //////    ZacznijSkanowanie();
            //////    return;
            //////}

            //
            if (trybskanowania == 0)
            {
                object zap1 = BazaDanychExternal.Wyczytaj_Jedna_Wartosc("SELECT l.awizacje_id FROM list_control l where l.awizacje_id!=0 and l.id='" + ops + "'");
                if (zap1 == null)
                {
                    DialogResult dialogResult = MessageBox.Show("LK '" + ops + "' nie awizowana. Kontynuować?", "", MessageBoxButtons.YesNo, MessageBoxIcon.Question, MessageBoxDefaultButton.Button1);
                    if (dialogResult == DialogResult.No)
                    {
                        ZacznijSkanowanie();
                        return;
                    }
                }
            }


            if (trybskanowania > 0 && PAL_ETYKIETA_SYS.Text != "")
            {

                object zap1 = BazaDanychExternal.Wyczytaj_Jedna_Wartosc("SELECT l.awizacje_id FROM list_control l where l.awizacje_id!=0 and l.id='" + LISTA_KONTROLNA.Text + "'  limit 1");
                if (zap1 != null)
                {
                    //MessageBox.Show("4");
                    object zap2 = BazaDanychExternal.Wyczytaj_Jedna_Wartosc("SELECT a.etykieta_klient FROM awizacje_dostaw_dane a WHERE a.awizacje_dostaw_id='" + (string)zap1 + "' and a.etykieta_klient='" + ops + "' limit 1;");

                    if (zap2 == null)
                    {

                        DialogResult dialogResult = MessageBox.Show("Ten karton nie jest awizowany. Czy poprawna etykieta?  '" + ops + "'  ", "", MessageBoxButtons.YesNo, MessageBoxIcon.Question, MessageBoxDefaultButton.Button1);
                        if (dialogResult == DialogResult.No)
                        {
                            ZacznijSkanowanie();
                            return;
                        }
                    }
                }

            }




            if (trybskanowania == 8)
            {
                ZacznijSkanowanie();

                string sprawdz1 = "select listcontrol_id from listcontrol_palety where paleta_id = '" + PAL_ETYKIETA_SYS.Text.Replace("DS", "") + "' and listcontrol_id='" + LISTA_KONTROLNA.Text + "'";
                object id5 = BazaDanychExternal.Wyczytaj_Jedna_Wartosc(sprawdz1);

                /*
                string sprawdz99 = "select listcontrol_system_id  from list_control where id = '" + id5.ToString() + "'";
                object id99 = BazaDanychExternal.Wyczytaj_Jedna_Wartosc(sprawdz99);
                if(id99.ToString()!=""){
                    Wlasciwosci.system_id_id = id99.ToString();
                }
                */

                if (id5 == null)
                {
                    BazaDanychExternal.DokonajUpdate("insert into listcontrol_palety(listcontrol_id,paleta_id) VALUES(" + Lista + "," + ops.Replace("DS", "") + ")");
                    BazaDanychExternal.DokonajUpdate("insert into operacje(etykieta_id, doc_type, doc_nr, imie_nazwisko, typ_operacji,system_id,wozek,operac_id,ilosc) values('0','LK','" + Lista + "','" + Wlasciwosci.imie_nazwisko + "','LK_PAL','" + Wlasciwosci.system_id_id + "','" + Wlasciwosci.wozek + "','" + operac_id_global + "','1');");

                }
                return;
            }
            if (trybskanowania == 0)
            {
                LISTA_KONTROLNA.Text = ops;
                PAL_ETYKIETA_SYS.Focus();
                return;
            }
            if (trybskanowania == 1)
            {
                PAL_ETYKIETA_SYS.Text = ops;
                button2.Focus();
                return;
            }



            if (trybskanowania == 2 || trybskanowania == 6)
            {



                if (SprawdzNumer(Ilosc.Text) == false)
                {
                    MessageBox.Show("Nie poprawna wartość w polu 'ilość'");
                    ZacznijSkanowanie();
                    return;
                }


                //Przepakowywanie

                object wax = BazaDanychExternal.Wyczytaj_Jedna_Wartosc("select id from etykiety where etykieta_klient='" + ops + "' and (active=1 OR active is null) limit 1");
                if (wax != null)
                {
                    MessageBox.Show("Karton '" + ops + "' jest już w systemie. Numer wykorzysywanej etykiety to " + (string)wax);
                    ZacznijSkanowanie();
                    return;
                }

                object wax1 = BazaDanychExternal.Wyczytaj_Jedna_Wartosc("select paleta_id from etykiety where etykieta_klient='" + ops + "' and listcontrol_id=" + Lista+" limit 1");
                if (wax1 != null)
                {
                    MessageBox.Show("Karton '" + ops + "' jest już na tej liście kontrolnej , na palecie : " + (string)wax1);
                    ZacznijSkanowanie();
                    return;
                }



                if (ops.Length >= 2)
                {
                    if (ops[0] == 'D' && ops[1] == 'S')
                    {
                        //MessageBox.Show("Kartonu '" + ops + "' nie ma w systemie.");
                        ZacznijSkanowanie();
                        return;
                    }
                    if (ops[0] == '*')
                    {
                        MessageBox.Show("Uwaga wczytano karton " + ops + "  , wpisz poprawny.");
                        ZacznijSkanowanie();
                        return;
                    }
                }

                /*
                //if (trybskanowania == 6)
                {

                    object wax11 = BazaDanychExternal.Wyczytaj_Jedna_Wartosc("select grupa from awizacje_etykiety where etykieta_klient='" + ops + "' and awizacja_id=" + awizacja + " order by id desc limit 1");
                    //MessageBox.Show("select grupa from awizacje_etykiety where etykieta_klient='" + ops + "' and awizacja_id=" + awizacja + " order by id desc limit 1");
                    if (wax11 != null)
                    {

                        //if (paletyzacja != "")
                        //{
                        //    MessageBox.Show("Ten karton nie został znaleziony w awizacji , i nie należy do grupy :" + paletyzacja);
                        //    ZacznijSkanowanie();
                        //    return;
                        //}

                        if (paletyzacja == "")
                        {
                            paletyzacja = (string)wax11;
                        }
                        else
                        {
                            if (paletyzacja != (string)wax11)
                            {
                                comboBox1.Items.Clear();
                                comboBox1.Visible = true;
                                //label3.Visible = true;

                                object wax12 = BazaDanychExternal.Wyczytaj_Tabele("SELECT distinct paleta_id FROM etykiety a ,list_control b,awizacje_etykiety c where b.id = " + Lista + " and c.grupa=" + paletyzacja + " and b.awizacje_id=c.awizacja_id and a.listcontrol_id=b.id and a.active=1  and system_id=" + SystemMag);
                                MessageBox.Show("Ten karton nie należy do grupy : " + paletyzacja);
                                if (wax12 != null)
                                {
                                    DataTable t = (DataTable)wax12;
                                    if ((t).Rows.Count > 0)
                                    {
                                        for (int ww = 0; ww < t.Rows.Count; ww++)
                                        {
                                            comboBox1.Items.Add("DS" + t.Rows[ww][0].ToString());
                                        }
                                    }
                                    else
                                    {
                                        comboBox1.Items.Add("Brak utworzonego nośnika.");
                                    }
                                    comboBox1.SelectedIndex = 0;
                                }

                                ZacznijSkanowanie();
                                return;
                            }
                            else
                            {
                                comboBox1.Visible = false;
                                //label3.Visible = false;
                            }
                        }
                    }
                }

                */

                string typ1 = "";
                string typ2 = "";


                /*
                if (tryb_listy_kontrolnej == 5)
                {

                    string wartoscx = (string)BazaDanychExternal.Wyczytaj_Jedna_Wartosc("select CONCAT(sum(ilosc),',',CD_SKU) as ilosc from etykiety_RICI where etykieta_klienta_etykiety='" + ops + "'");
                    if (wartoscx == null)
                    {
                        MessageBox.Show("Brak ilości w tym kartonie.Proszę o skorygowanie go.");
                        ZacznijSkanowanie();
                        return;

                    }
                    string[] checkSAB = wartoscx.Split(',');
                    if (0 == Convert.ToInt32(checkSAB[0]))
                    {
                        MessageBox.Show("Brak ilości w tym kartonie.Proszę o skorygowanie go.");
                        ZacznijSkanowanie();
                        return;
                    }

                    if (liczba_sztuk == 0)
                    {
                        DataTable t = (DataTable)BazaDanychExternal.Wyczytaj_Tabele("select distinct color,a.CD_ARTICOLO from importRICI a,gg_anagrafiki b where a.CD_SKU=b.cd_sku and b.cd_sku='" + checkSAB[1] + "'");
                        liczba_sztuk = Convert.ToInt32(checkSAB[0]);
                        CD_ARTICOLO = t.Rows[0]["CD_ARTICOLO"].ToString();
                        color = t.Rows[0]["color"].ToString();
                    }
                    else
                    {
                        DataTable t = (DataTable)BazaDanychExternal.Wyczytaj_Tabele("select distinct color,a.CD_ARTICOLO from importRICI a,gg_anagrafiki b where a.CD_SKU=b.cd_sku and b.cd_sku='" + checkSAB[1] + "'");

                        if (color != t.Rows[0]["color"].ToString())
                        {
                            MessageBox.Show("Zeskanowany towar , nie jest jednorodny ze sztukami w kartonie.Odrzucone z powodu koloru.");
                            ZacznijSkanowanie();
                            return;
                        }

                        if (CD_ARTICOLO != t.Rows[0]["CD_ARTICOLO"].ToString())
                        {
                            MessageBox.Show("Zeskanowany towar , nie jest jednorodny ze sztukami w kartonie.Odrzucone z powodu SAB.");
                            ZacznijSkanowanie();
                            return;
                        }
                        if (checkSAB[0] != liczba_sztuk.ToString())
                        {

                            DialogResult dialogResult = MessageBox.Show("Ilości w kartonie '" + ops + "' (" + checkSAB[0] + ") nie zgadzają się z paletą.Czy chcesz przerwać?", "", MessageBoxButtons.YesNo, MessageBoxIcon.Question, MessageBoxDefaultButton.Button1);
                            if (dialogResult == DialogResult.Yes)
                            {
                                //BazaDanychExternal.DokonajUpdate("delete from etykiety where paleta_id=" + PAL_ETYKIETA_SYS.Text.Replace("DS", ""));
                                ZacznijSkanowanie();
                                return;
                            }
                        }


                    }

                    //Ilosc.Text = wartoscx;
                    typ1 = ",kartony";
                    typ2 = "," + checkSAB[0];


                } */



                /*
                if (tryb_listy_kontrolnej == 7)
                {

                    string wartoscx = (string)BazaDanychExternal.Wyczytaj_Jedna_Wartosc("select CONCAT(sum(ilosc),',',CD_SKU) as ilosc from etykiety_RICI where etykieta_klienta_etykiety='" + ops + "'");
                    if (wartoscx == null)
                    {
                        MessageBox.Show("Brak ilości w tym kartonie.Proszę o skorygowanie go.");
                        ZacznijSkanowanie();
                        return;

                    }
                    string[] checkSAB = wartoscx.Split(',');
                    if (0 == Convert.ToInt32(checkSAB[0]))
                    {
                        MessageBox.Show("Brak ilości w tym kartonie.Proszę o skorygowanie go.");
                        ZacznijSkanowanie();
                        return;
                    }

                    if (liczba_sztuk == 0)
                    {
                        DataTable t = (DataTable)BazaDanychExternal.Wyczytaj_Tabele("select distinct CD_PARLANTE from importRICI a where a.cd_sku='" + checkSAB[1] + "'");
                        liczba_sztuk = Convert.ToInt32(checkSAB[0]);
                        string typ_S = t.Rows[0]["CD_PARLANTE"].ToString().Remove(0, 3);
                        CD_ARTICOLO = typ_S[0].ToString() + typ_S[1].ToString();
                        //CD_ARTICOLO = t.Rows[0]["CD_ARTICOLO"].ToString();
                        //color = t.Rows[0]["color"].ToString();
                    }
                    else
                    {
                        DataTable t = (DataTable)BazaDanychExternal.Wyczytaj_Tabele("select distinct CD_PARLANTE from importRICI a where a.cd_sku='" + checkSAB[1] + "'");

                        string typ_S = t.Rows[0]["CD_PARLANTE"].ToString().Remove(0, 3);
                        typ_S = typ_S[0].ToString() + typ_S[1].ToString();



                        if (CD_ARTICOLO != typ_S)
                        {
                            MessageBox.Show("Zeskanowany towar , nie jest jednorodny ze sztukami w kartonie.Odrzucone z powodu SAB.");
                            ZacznijSkanowanie();
                            return;
                        }
                        if (checkSAB[0] != liczba_sztuk.ToString())
                        {

                            DialogResult dialogResult = MessageBox.Show("Ilości w kartonie '" + ops + "' (" + checkSAB[0] + ") nie zgadzają się z paletą.Czy chcesz przerwać?", "", MessageBoxButtons.YesNo, MessageBoxIcon.Question, MessageBoxDefaultButton.Button1);
                            if (dialogResult == DialogResult.Yes)
                            {
                                //BazaDanychExternal.DokonajUpdate("delete from etykiety where paleta_id=" + PAL_ETYKIETA_SYS.Text.Replace("DS", ""));
                                ZacznijSkanowanie();
                                return;
                            }
                        }


                    }

                    //Ilosc.Text = wartoscx;
                    typ1 = ",kartony";
                    typ2 = "," + checkSAB[0];


                } */


                string typ3 = "";
                string typ4 = "";

                /*
                object kod_id = BazaDanychExternal.Wyczytaj_Jedna_Wartosc("select kod_id from etykiety_produkcja where etykieta_prod='" + ops + "'");
                if (kod_id != null)
                {
                    typ3 = ",kod_id";
                    typ4 = "," + kod_id;
                }
                 */


                BazaDanychExternal.DokonajUpdate("insert into etykiety(etykieta_klient,system_id,ilosc,miejscep,paleta_id,listcontrol_id" + typ1 + typ3 + ") values" +
                    " ('" + ops + "'," + SystemMag + "," + Ilosc.Text + "," + miejsce_id + "," + PAL_ETYKIETA_SYS.Text.Replace("DS", "") + "," + Lista + typ2 + typ4 + ")");
                string et = BazaDanychExternal.Command.LastInsertedId.ToString();
                Ilosc.Text = "1";
                string ilosc_etykiet = (string)BazaDanychExternal.Wyczytaj_Jedna_Wartosc("select COUNT(*) from etykiety where listcontrol_id=" + Lista);
                string ilosc_palet = (string)BazaDanychExternal.Wyczytaj_Jedna_Wartosc("select COUNT(*) paleta_id from (select distinct paleta_id from etykiety where listcontrol_id=" + Lista + " group by paleta_id) as nowa");
                string ilosc_na_palet = (string)BazaDanychExternal.Wyczytaj_Jedna_Wartosc("select COUNT(*) from etykiety where listcontrol_id=" + Lista + " and paleta_id =" + Etykieta);
                //string grupa = (string)BazaDanychExternal.Wyczytaj_Jedna_Wartosc("select concat(paleta,'   ',CD_MARCHIO,' ',SEX) from importRICI where CD_CONTENITORE='" + ops + "' ORDER BY ID DESC LIMIT 1");
                BazaDanychExternal.DokonajUpdate("insert into operacje(etykieta_id, doc_type, doc_nr, imie_nazwisko, typ_operacji,system_id,wozek,operac_id,ilosc) values('" + et + "','LK','" + Lista + "','" + Wlasciwosci.imie_nazwisko + "','LK_PAL','" + Wlasciwosci.system_id_id + "','" + Wlasciwosci.wozek + "','" + operac_id_global + "','1');");


                //label3.Text = "Pal: " + grupa;
                Licznik_Liczba.Text = ilosc_na_palet;
                Licznik_ETYKIET.Text = ilosc_etykiet;
                LICZNIK_CUR.Text = (Convert.ToInt32(LICZNIK_CUR.Text) + 1).ToString();
                Licznik_PALET.Text = ilosc_palet;

                Etykieta_EAN.Text = "";
                label1.Visible = true;
                label2.Visible = true;
                label2.Text = ops;
                ZacznijSkanowanie();
                return;
            }

            if (trybskanowania == 3)
            {
                //Przyjęcie paletowe



                /*

                DataTable t = (DataTable)BazaDanychExternal.Wyczytaj_Tabele("select e.id,concat('DS',paleta_id) as paleta_id,doc_type,doc_nr from etykiety e left join docin d on e.docin_id=d.id where etykieta_klient='" + ops + "' and e.active=1 order by e.id desc limit 1");

                if ((t).Rows.Count == 0)
                {
                    MessageBox.Show("Kartonu '" + ops + "' nie ma w systemie.");
                    ZacznijSkanowanie();
                    return;
                }


                string etykieta_id_local = t.Rows[0]["id"].ToString();
                string paleta_id_local = t.Rows[0]["paleta_id"].ToString();
                string doc_type_local = t.Rows[0]["doc_type"].ToString();
                string doc_nr_local = t.Rows[0]["doc_nr"].ToString();

                if (doc_type_local == "PP")
                {
                    MessageBox.Show("Kartonu już znajduje się na dokumencie '" + doc_type_local + "' '" + doc_nr_local + "' na palecie '" + paleta_id_local + "'");
                    ZacznijSkanowanie();
                    return;
                }
                */

                object wax = BazaDanychExternal.Wyczytaj_Jedna_Wartosc("select concat(e.id,',',doc_type,',DS',paleta_id,',',ifnull(lot,'')) as paleta_id  from etykiety e left join docin d on e.docin_id=d.id where etykieta_klient='" + ops + "' and e.active=1 order by e.id desc limit 1");
                
                
                if (wax == null)
                {
                    MessageBox.Show("Kartonu '" + ops + "' nie ma w systemie.");
                    ZacznijSkanowanie();
                    return;
                }

                //string tmp = ;
                string[] teemp = wax.ToString().Split(',');
                string lot = teemp[3].TrimStart('0');
                
                //zabezpiecenie przed wczytywaniem kartonow z innymi lotami
                if (lot != previousLot && previousLot != "")
                {
                    MessageBox.Show("dla tej palety obowiazuje ten lot: " + previousLot);
                    ZacznijSkanowanie();
                    return;
                }
                previousLot = lot;//zapamietywanie lotu

                /*
                if (teemp[1] == "PP")
                {
                    MessageBox.Show("Kartonu '" + ops + "' zeskanowany PP na '" + teemp[2] + "'.");
                    ZacznijSkanowanie();
                    return;
                }
                */
                

                if (ops.Length >= 2)
                {
                    if (ops[0] == 'D' && ops[1] == 'S')
                    {
                        //MessageBox.Show("Kartonu '" + ops + "' nie ma w systemie.");
                        ZacznijSkanowanie();
                        return;
                    }
                    if (ops[0] == '*')
                    {
                        MessageBox.Show("Uwaga wczytano karton " + ops + "  , wpisz poprawny.");
                        ZacznijSkanowanie();
                        return;
                    }
                }


                docin_docout("PP", "PP-", ops, (string)teemp[0]);
            }

            if (trybskanowania == 4)
            {

                //Kompletacja wysyłki


                object wax = BazaDanychExternal.Wyczytaj_Jedna_Wartosc("select id from etykiety where etykieta_klient='" + ops + "' and active=1 order by id desc limit 1");
                if (wax == null)
                {
                    MessageBox.Show("Kartonu '" + ops + "' nie ma w systemie.");
                    ZacznijSkanowanie();
                    return;
                }
                if (ops.Length >= 2)
                {
                    if (ops[0] == 'D' && ops[1] == 'S')
                    {
                        //MessageBox.Show("Kartonu '" + ops + "' nie ma w systemie.");
                        ZacznijSkanowanie();
                        return;
                    }
                }
                docin_docout("KT", "KT-", ops, (string)wax);
                //string komand1 = "insert into etykiety_kartony(idetykiety,karton_ean,listcontrol_id,pracownicy_id1) VALUES(0,'" + ops + "'," + Lista + "," + Wlasciwosci.id_Pracownika + ")";
                //if (BazaDanychExternal.DokonajUpdate(komand1) == (-11))
                //{
                //    MessageBox.Show("Operacja zapisu nie była możliwa.Proszę podejść do strefy z WIFI");
                //    ZacznijSkanowanie();
                //    return;
                //}
                //object wa = BazaDanychExternal.Wyczytaj_Jedna_Wartosc("select count(*) from etykiety_kartony where listcontrol_id='" + Lista + "'");
                //Licznik_Liczba.Text = wa.ToString();
            }

            Etykieta_EAN.Text = "";
            
            ZacznijSkanowanie();

        }

        #endregion




        private void docin_docout(string Typ_Dokumentu1, string Typ_Dokumentu2, string ops, string wax)
        {

            if (doc_in_ID == "")
            {



                int max_docin = Convert.ToInt32(BazaDanychExternal.Wyczytaj_Jedna_Wartosc("select max(doc_nr) from docin din,etykiety et where et.system_id = " + SystemMag + " and et.docin_id=din.id and din.doc_type='" + Typ_Dokumentu1 + "'"));
                int max_docout = Convert.ToInt32(BazaDanychExternal.Wyczytaj_Jedna_Wartosc("select max(docout_nr) from docout din,etykiety et where et.system_id = " + SystemMag + " and et.docout_id=din.id and din.docout_type='" + Typ_Dokumentu2 + "'"));



                object wax2 = BazaDanychExternal.Wyczytaj_Jedna_Wartosc("select doc_nr from docin din,etykiety et where et.system_id = " + SystemMag + " and et.docin_id=din.id and din.doc_type='" + Typ_Dokumentu1 + "' and din.doc_ref='" + Lista + "'");
                object wac2 = null;
                if (wax2 != null)
                {
                    max_docin = Convert.ToInt32(wax2);
                    wac2 = BazaDanychExternal.Wyczytaj_Jedna_Wartosc("select din.id from docin din,etykiety et where et.system_id = " + SystemMag + " and et.docin_id=din.id and din.doc_type='" + Typ_Dokumentu1 + "' and din.doc_ref='" + Lista + "'");
                }

                object wax1 = BazaDanychExternal.Wyczytaj_Jedna_Wartosc("select docout_nr from docout din,etykiety et where et.system_id = " + SystemMag + " and et.docout_id=din.id and din.docout_type='" + Typ_Dokumentu2 + "' and din.docout_ref='" + Lista + "'");
                object wac1 = null;
                if (wax1 != null)
                {
                    max_docout = Convert.ToInt32(wax1);
                    wac1 = BazaDanychExternal.Wyczytaj_Jedna_Wartosc("select din.id from docout din,etykiety et where et.system_id = " + SystemMag + " and et.docout_id=din.id and din.docout_type='" + Typ_Dokumentu2 + "' and din.docout_ref='" + Lista + "'");
                }


                if (max_docin > max_docout)
                {
                    if (wax2 == null)
                    {
                        numer_dokumentu = max_docin + 1;
                    }
                    else
                    {
                        numer_dokumentu = max_docin;
                    }
                }
                else
                {
                    if (wax1 == null)
                    {
                        numer_dokumentu = max_docout + 1;
                    }
                    else
                    {
                        numer_dokumentu = max_docout;
                    }
                }

                //int max_docin = Convert.ToInt32(BazaDanychExternal.Wyczytaj_Jedna_Wartosc("select max(doc_nr) from docin din,etykiety et where et.system_id = " + System + " and et.docin_id=din.id and din.doc_type='PP'"));
                //BazaDanychExternal.DokonajUpdate("");


                if (wax2 != null)
                {
                    doc_in_ID = (string)wac2;
                }
                else
                {
                    BazaDanychExternal.DokonajUpdate("insert into docin(doc_internal,doc_type,doc_nr,doc_date,doc_ts,pracownik_id,kontrah_id,doc_ref,doc_uwagi)" +
                " values(1,'" + Typ_Dokumentu1 + "'," + numer_dokumentu.ToString() + ",date(now()),sysdate()," + Wlasciwosci.id_Pracownika + ",(select kontrah_wew_id from systemy where wartosc=" + SystemMag + ")," + "'" + Lista + "','')");
                    string last_docin = BazaDanychExternal.Command.LastInsertedId.ToString();
                    doc_in_ID = last_docin;
                }
                if (wax1 != null)
                {
                    doc_out_ID = (string)wac1;
                }
                else
                {
                    BazaDanychExternal.DokonajUpdate("insert into docout(docout_internal,docout_type,docout_nr,docout_date,docout_ts,pracownik_id,kontrah_id,docout_ref,docout_uwagi)" +
                " values(3,'" + Typ_Dokumentu2 + "'," + numer_dokumentu.ToString() + ",date(now()),sysdate()," + Wlasciwosci.id_Pracownika + ",(select kontrah_wew_id from systemy where wartosc=" + SystemMag + ")," + "'" + Lista + "','')");
                    string last_docout = BazaDanychExternal.Command.LastInsertedId.ToString();
                    doc_out_ID = last_docout;
                }
            }





            object test = BazaDanychExternal.Wyczytaj_Jedna_Wartosc("select id from etykiety where etykieta_klient='" + ops + "' and listcontrol_id=" + Lista);
            //DataTable Tabela2 = ((DataTable)BazaDanychExternal.Wyczytaj_Jedna_Wartosc("select id from etykiety where etykieta_klient='"+ops+"' and listcontrol_id="+Lista));
            if (test != null)
            {
                MessageBox.Show("Ta etykieta została już zeskanowana.");
                return;
            }

            object test2 = BazaDanychExternal.Wyczytaj_Jedna_Wartosc("SELECT nr_dl FROM dlcollect d WHERE d.nr_et=" + (string)wax);
            //DataTable Tabela2 = ((DataTable)BazaDanychExternal.Wyczytaj_Jedna_Wartosc("select id from etykiety where etykieta_klient='"+ops+"' and listcontrol_id="+Lista));
            if (test2 != null)
            {
                MessageBox.Show("Ta etykieta była skanowana na DL " + (string)test2);
                return;
            }

            //DataTable baza  = ((DataTable)BazaDanychExternal.Wyczytaj_Tabele("select * from etykiety where id = "+(string)wax));
            DataTable Tabela = ((DataTable)BazaDanychExternal.Wyczytaj_Tabele("select system_id,magazyn,active,miejscep,kod_id,status_id,status_id2,stat,paleta_id,kartony,DATE_FORMAT(dataprod,'%d-%m-%Y'),data_waznosci,ilosc,status,blloc,akcja_id,status_prism,lot,sscc,gtin,przeznaczenie_id,nretykiety,docin_id,docout_id,delivery_id,listcontrol_id from etykiety where id = " + (string)wax));
            if (Tabela != null)                                                
            {

                BazaDanychExternal.Inicjuj_Transakcje();
                BazaDanychExternal.Zacznij_Transakcje();

                string status_id="83";
                if (Typ_Dokumentu1=="PP")
                {                    
                    status_id="83";
                }
                else{
                    status_id=sprawdz_czy_null(Tabela.Rows[0][5].ToString());
                }
                try
                {
                    string zapytanie = "insert into etykiety(system_id,magazyn,active,miejscep,kod_id,status_id,status_id2,stat,paleta_id,kartony,dataprod,data_waznosci,ilosc,status,blloc,akcja_id,status_prism,lot,sscc,gtin,przeznaczenie_id,nretykiety,docin_id,edycja_et,etykieta_klient,ts,listcontrol_id)" +
                        " values(" +
                        sprawdz_czy_null(Tabela.Rows[0][0].ToString()) + "," +
                        sprawdz_czy_null(Tabela.Rows[0][1].ToString()) + "," +
                        sprawdz_czy_null(Tabela.Rows[0][2].ToString()) + "," +
                        miejsce_id + "," +
                        sprawdz_czy_null(Tabela.Rows[0][4].ToString()) + "," +
                        sprawdz_czy_null(status_id) +"," +
                        sprawdz_czy_null(Tabela.Rows[0][6].ToString()) + "," +
                        sprawdz_czy_null(Tabela.Rows[0][7].ToString()) + "," +
                        Etykieta + "," +
                        //sprawdz_czy_null(Tabela.Rows[0][8].ToString()) + "," +
                        sprawdz_czy_null(Tabela.Rows[0][9].ToString()) + "," +
                        DajDate(sprawdz_czy_null(Tabela.Rows[0][10].ToString())) + "," + //DATA
                        sprawdz_czy_null(Tabela.Rows[0][11].ToString()) + "," +

                        sprawdz_czy_null(Tabela.Rows[0][12].ToString()) + "," +
                        //sprawdz_czy_null(Tabela.Rows[0][13].ToString()) + "," +
                        sprawdz_czy_null(Tabela.Rows[0][13].ToString()) + "," +
                        sprawdz_czy_null(Tabela.Rows[0][14].ToString()) + "," +
                        sprawdz_czy_null(Tabela.Rows[0][15].ToString()) + "," +
                        sprawdz_czy_null(Tabela.Rows[0][16].ToString()) + "," +
                        sprawdz_czy_null(Tabela.Rows[0][17].ToString()) + "," +//lot
                        sprawdz_czy_null(Tabela.Rows[0][18].ToString()) + "," +
                        sprawdz_czy_null(Tabela.Rows[0][19].ToString()) + "," +
                        //sprawdz_czy_null(Tabela.Rows[0][21].ToString()) + "," +
                          sprawdz_czy_null(Tabela.Rows[0][20].ToString()) + "," +
                        sprawdz_czy_null(Tabela.Rows[0]["nretykiety"].ToString()) + "," +
                        doc_in_ID +
                         ",1,'" + ops + "',sysdate()," + Lista + ")";


                    //MessageBox.Show(zapytanie);


                    BazaDanychExternal.DokonajUpdate("update etykiety set active=0,docout_id=" + doc_out_ID + " where id=" + (string)wax);
                    BazaDanychExternal.DokonajUpdate(zapytanie);
                    BazaDanychExternal.Zakoncz_Transakcje();


                }
                catch (MySql.Data.MySqlClient.MySqlException t)
                {
                    BazaDanychExternal.Przerwij_Transakcje();

                    BazaDanychExternal.DokonajUpdate("insert into logi_skaner(pracownik_id, zapytanie, komunikat,system_id) values('" + Wlasciwosci.id_Pracownika + "','Etykieta: " + (string)wax + ", KARTON: " + ops + " ','" + t.ToString() + "','" + Wlasciwosci.system_id_id + "');");


                    MessageBox.Show("Nie udało się dokonać zmian dla tego kartonu.Proszę o ponowny skan.");

                    return;
                }
                string last_id = BazaDanychExternal.Command.LastInsertedId.ToString();

                BazaDanychExternal.DokonajUpdate("insert into operacje(etykieta_id, doc_type, doc_nr, imie_nazwisko, typ_operacji,system_id,wozek,operac_id,ilosc) values('" + last_id + "','" + Typ_Dokumentu1 + "','" + numer_dokumentu.ToString() + "','" + Wlasciwosci.imie_nazwisko + "','" + Typ_Dokumentu1 + "','" + Wlasciwosci.system_id_id + "','" + Wlasciwosci.wozek + "','" + operac_id_global + "','1');");


                DataTable Tabela1 = ((DataTable)BazaDanychExternal.Wyczytaj_Tabele("select * from etykiety_dod3 where id = " + (string)wax));
                if (Tabela1.Rows.Count > 0)
                {

                    BazaDanychExternal.DokonajUpdate("insert into etykiety_dod3(id,system_id,rozmiar_nr,kolor,plec,uszkodzenie) values (" +
                    last_id + "," +
                    SystemMag + "," +
                    sprawdz_czy_null(Tabela1.Rows[0][2].ToString()) + "," +
                    sprawdz_czy_null(Tabela1.Rows[0][3].ToString()) + "," +
                    sprawdz_czy_null(Tabela1.Rows[0][4].ToString()) + "," +
                    sprawdz_czy_null(Tabela1.Rows[0][5].ToString()) + ")");

                }
                string ilosc_etykiet = (string)BazaDanychExternal.Wyczytaj_Jedna_Wartosc("select COUNT(*) from etykiety where listcontrol_id=" + Lista);
                string ilosc_palet = (string)BazaDanychExternal.Wyczytaj_Jedna_Wartosc("select COUNT(*) paleta_id from (select distinct paleta_id from etykiety where listcontrol_id=" + Lista + " group by paleta_id) as nowa");
                string ilosc_na_palet = (string)BazaDanychExternal.Wyczytaj_Jedna_Wartosc("select COUNT(*) from etykiety where listcontrol_id=" + Lista + " and paleta_id =" + Etykieta);

                Licznik_Liczba.Text = ilosc_na_palet;
                Licznik_ETYKIET.Text = ilosc_etykiet;
                Licznik_PALET.Text = ilosc_palet;

                label1.Visible = true;
                label2.Visible = true;
                label2.Text = ops;

                // sprawdz
                DataTable TableSprawdz = ((DataTable)BazaDanychExternal.Wyczytaj_Tabele("SELECT id FROM etykiety e where etykieta_klient='" + ops+"' and ((e.active=1 and docin_id=" + doc_in_ID + ") or (e.active=0 and docout_id=" + doc_out_ID + ")) "));
                if (TableSprawdz.Rows.Count < 2)
                {
                    MessageBox.Show("Proszę ponownie wczytać karton:"+ops);
                    BazaDanychExternal.DokonajUpdate("update etykiety set active=1,docout_id=null where active=1 and docout_id=" + doc_out_ID + " and etykieta_klient='" + ops+"' and system_id="+SystemMag);
                    BazaDanychExternal.DokonajUpdate("delete from etykiety where active=1 and docin_id=" + doc_in_ID + " and etykieta_klient='" + ops+"' and system_id="+SystemMag);
                    return;
                }


            }
        }


        private string sprawdz_czy_null(string bb)
        {
            if (bb == "")
            {

                return "null";
            }
            else
            {

                try
                {
                    Convert.ToDouble(bb);
                    return bb;
                }
                catch
                {
                    return "'" + bb + "'";
                }
            }

        }
        private void GnG1_Load(object sender, EventArgs e)
        {
            LISTA_KONTROLNA.Focus();
        }

        private void radioFULL_CheckedChanged(object sender, EventArgs e)
        {
            RadioButton OK = (RadioButton)sender;
            operac_id_global = "";
            if (OK == przyjeciePAL)
            {
                PAL_ETYKIETA_SYS.Visible = true;
                EANLAB.Visible = true;
                //PAL_ETYKIETA_SYS.Focus();
                //EANLAB.Text = "Paleta Etykieta :";
            }
            if (OK == radioKONTR)
            {
                //PAL_ETYKIETA_SYS.Visible = false;
                //EANLAB.Visible = false;
                //EANLAB.Text = "Etykieta klienta :";
            }

            if (OK == radioEAN)
            {
                PAL_ETYKIETA_SYS.Visible = true;
                EANLAB.Visible = true;
            }
            if (OK == dostawapotw)
            {
                PAL_ETYKIETA_SYS.Visible = true;
                EANLAB.Visible = true;
            }
            if (OK == radioButton1)
            {   
                PAL_ETYKIETA_SYS.Visible = false;
                EANLAB.Visible = false;
            }

            label6.Visible = true;
            LISTA_KONTROLNA.Visible = true;
            LISTA_KONTROLNA.Focus();

            LISTA_KONTROLNA.Text = "";
            PAL_ETYKIETA_SYS.Text = "";
        }



        string SystemMag = "";
        string kontrahent = "";
        string doc_in_ID = "";
        string doc_out_ID = "";
        string awizacja = "";
        string typ_palety = "";//zmienna do zapamietywania typu palety zeby przy kolejnym DS byl poprzedni typ

        int tryb_listy_kontrolnej = 0;

        private void button2_Click(object sender, EventArgs e)
        {            
            string zapyt_nazwa_palety = "select opis from palety p left join typypalet t on p.typypalet_id=t.id where p.id = '" + PAL_ETYKIETA_SYS.Text.Replace("DS", "") + "'";
            string zapytanie_rodzaje ="SELECT t.id, t.opis as nazwa FROM typypalet t WHERE t.id!=0;";
            string ff = (string)BazaDanychExternal.Wyczytaj_Jedna_Wartosc(zapyt_nazwa_palety);


            PoleCombobox XC = new PoleCombobox(zapytanie_rodzaje, "Wybierz paletę", typ_palety);
            if (XC.ShowDialog() == DialogResult.OK)
            {
                if (XC.wybierana_nazwa == "")
                {
                    MessageBox.Show("Nie dokonano wyboru");
                    return;
                }

                typ_palety = XC.wybierana_nazwa;//zapamietywanie wyboru
                BazaDanychExternal.DokonajUpdate("update palety p  set typypalet_id='" + XC.wybierane_id + "' where p.id = '" + PAL_ETYKIETA_SYS.Text.Replace("DS", "") + "'");
            }
            else
            {
                return;
            }
             


            if (operac_id_global == "")
            {
                string zapytanie = "SELECT last FROM docnumber d WHERE d.name='operacja_id';";

                operac_id_global = (string)BazaDanychExternal.Wyczytaj_Jedna_Wartosc(zapytanie);

                string zapyt = "update docnumber d set last=last+1 WHERE d.name='operacja_id'; ";
                BazaDanychExternal.Wyczytaj_Jedna_Wartosc(zapyt);
            }



            string komand = "select id from list_control where id='" + LISTA_KONTROLNA.Text + "'";
            string sprawdz = "select id from palety where id = '" + PAL_ETYKIETA_SYS.Text.Replace("DS", "") + "'";
            string sprawdz1 = "select listcontrol_id from listcontrol_palety where paleta_id = '" + PAL_ETYKIETA_SYS.Text.Replace("DS", "") + "' and listcontrol_id='" + LISTA_KONTROLNA.Text + "'";
            string system_id = "select listcontrol_system_id from list_control where id='" + LISTA_KONTROLNA.Text + "'";
            awizacja = "";
            //string system_id = "select listcontrol_system_id from list_control where id='" + LISTA_KONTROLNA.Text + "'";
            object id = BazaDanychExternal.Wyczytaj_Jedna_Wartosc(sprawdz);
            if (id == null)
            {
                if (radioButton1.Checked == false)
                {
                    MessageBox.Show("Taka etykieta 'paletowa' nie istnieje.");
                    PAL_ETYKIETA_SYS.Focus();
                    return;
                }
            }
            Etykieta = (string)id;
            object id5 = BazaDanychExternal.Wyczytaj_Jedna_Wartosc(sprawdz1);
            //Etykieta = (string)id;
            if (id5 == null)
            {
                if (radioButton1.Checked == false)
                {
                    MessageBox.Show("Ta etykieta nie jest przypisana do tej listy kontrolnej.");
                    PAL_ETYKIETA_SYS.Focus();
                    return;
                }
            }


            object lista = BazaDanychExternal.Wyczytaj_Jedna_Wartosc(komand);
            if (lista == null)
            {
                MessageBox.Show("Nie ma takiej listy kontrolnej w systemie.");
                LISTA_KONTROLNA.Focus();
                return;
            }
            object id1 = BazaDanychExternal.Wyczytaj_Jedna_Wartosc(system_id);
            SystemMag = (string)id1;
            Wlasciwosci.system_id_id = SystemMag;

            string miejsce_id_sql = "select miejsce_id from list_control where id='" + LISTA_KONTROLNA.Text + "'";
            miejsce_id = (string)BazaDanychExternal.Wyczytaj_Jedna_Wartosc(miejsce_id_sql);

            if (przyjeciePAL.Checked == true)
            {

                Wlasciwosci.CurrentOperacja = "3";
                //object id1 = BazaDanychExternal.Wyczytaj_Jedna_Wartosc(system_id);
                LISTA_KONTROLNA.Enabled = false;
                PAL_ETYKIETA_SYS.Enabled = false;
                Lista = (string)lista;
                skasuj.Visible = true;
                trybskanowania = 2; 
                label3.Visible = true;
                //SystemMag = (string)id1;
                Ilosc.Visible = true;
                Ilosc_Label.Visible = true;



                string sprawdz61 = "select awizacje_id from list_control where id = " + LISTA_KONTROLNA.Text;
                //MessageBox.Show(sprawdz61);
                awizacja = (string)BazaDanychExternal.Wyczytaj_Jedna_Wartosc(sprawdz61);
                string sprawdz62 = "select listcontrol_type_id from list_control where id = " + LISTA_KONTROLNA.Text;
                //MessageBox.Show(sprawdz62);
                tryb_listy_kontrolnej = Convert.ToInt32((string)BazaDanychExternal.Wyczytaj_Jedna_Wartosc(sprawdz62));


                if (!(tryb_listy_kontrolnej == 1 || tryb_listy_kontrolnej == 2))
                {
                    MessageBox.Show("Nie prawidłowa lista kontrolna do tego typu operacji. Wymagana: Kontener lub dostawa na paletach");
                    return;
                }



                /*
                string sprawdz63 = "select CONCAT(sum(ilosc),',',CD_SKU) as ilo from etykiety_RICI where etykieta_klienta_etykiety=(select etykieta_klient from etykiety where paleta_id='" + PAL_ETYKIETA_SYS.Text.Replace("DS", "") + "' LIMIT 1)";
                //MessageBox.Show(sprawdz63);
                object now = BazaDanychExternal.Wyczytaj_Jedna_Wartosc(sprawdz63);
                if (now != null)
                {
                    string[] split = ((string)now).Split(',');
                    liczba_sztuk = Convert.ToInt32(split[0]);
                    DataTable t = (DataTable)BazaDanychExternal.Wyczytaj_Tabele("select distinct color,a.CD_ARTICOLO from importRICI a,gg_anagrafiki b where a.CD_SKU=b.cd_sku and b.cd_sku='" + split[1] + "'");
                    color = t.Rows[0][0].ToString();
                    CD_ARTICOLO = t.Rows[0][1].ToString();

                }
                else
                {
                    liczba_sztuk = 0;
                    CD_ARTICOLO = "";
                    color = "";
                }

                */

                LICZNIK_CUR.Text = "0";

                ////if (awizacja == "0")
                ////{
                ////    MessageBox.Show("Brak awizacji dla tej listy kontrolnej.");
                ////    return;
                ////}

                //LISTA_KONTROLNA.Enabled = false;
                //PAL_ETYKIETA_SYS.Enabled = false;
                //Etykieta = (string)id;
                //trybskanowania = 6;


            }


            if (radioKONTR.Checked == true)
            {

                string sprawdz62 = "select listcontrol_type_id from list_control where id = " + LISTA_KONTROLNA.Text;
                //MessageBox.Show(sprawdz62);
                tryb_listy_kontrolnej = Convert.ToInt32((string)BazaDanychExternal.Wyczytaj_Jedna_Wartosc(sprawdz62));


                if (tryb_listy_kontrolnej != 8)
                {
                    MessageBox.Show("Nie prawidłowa lista kontrolna do tego typu operacji. Wymagana: Kompletacja dostawy");
                    return;
                }

                Wlasciwosci.CurrentOperacja = "4";
                PAL_ETYKIETA_SYS.Enabled = false;
                Lista = (string)lista;
                //System = (string)id1;
                Etykieta = (string)id;
                trybskanowania = 3;

                //sprawdzanie czy na danym DS sa kartony i pobieranie ich kategorii
                string checkCat = "select lot from etykiety where paleta_id='" + PAL_ETYKIETA_SYS.Text + "' limit 1;";
                object lot = BazaDanychExternal.Wyczytaj_Jedna_Wartosc(checkCat);
                if (lot != null)
                {
                    previousLot = lot.ToString();
                    previousLot = previousLot.TrimStart('0');
                    //MessageBox.Show(checkCat);
                    //MessageBox.Show(previousLot);
                }
            }
            if (radioEAN.Checked == true)
            {
                Wlasciwosci.CurrentOperacja = "5";
                LISTA_KONTROLNA.Enabled = false;
                Lista = (string)lista;
                trybskanowania = 4;
                string sprawdz62 = "select listcontrol_type_id from list_control where id = " + LISTA_KONTROLNA.Text;
                int check = Convert.ToInt32((string)BazaDanychExternal.Wyczytaj_Jedna_Wartosc(sprawdz62));
                if (check != 4)
                {
                    MessageBox.Show("Nie prawidłowa lista kontrolna do tego typu operacji. Wymagana: Kompletacja wysyłki");
                    return;
                }
            }

            if (dostawapotw.Checked == true)
            {
                return;
            }

            if (radioButton1.Checked == true)
            {
                LISTA_KONTROLNA.Enabled = false;
                Lista = (string)lista;
                trybskanowania = 8;
                //BazaDanychExternal.DokonajUpdate("insert into listcontrol_palety(listcontrol_id,paleta_id) VALUES(" + Lista + "," + PAL_ETYKIETA_SYS.Text.Replace("DS", "") + ")");
                //BazaDanychExternal.DokonajUpdate("Update listcontrol_palety set listcontrol_id=" + Lista + " where paleta_id=" + PAL_ETYKIETA_SYS.Text.Replace("DS", ""));
                //BazaDanychExternal.DokonajUpdate("Update etykiety set listcontrol_id=" + Lista + " where paleta_id=" + PAL_ETYKIETA_SYS.Text.Replace("DS", ""));
            }

            radioEAN.Enabled = false;
            przyjeciePAL.Enabled = false;
            radioKONTR.Enabled = false;
            radioButton1.Enabled = false;

            button2.Text = "Zakończ Skanowanie";

            this.button2.Click -= new System.EventHandler(this.button2_Click);
            this.button2.Click += new System.EventHandler(this.button2_ClickD);


            PAL_ETYKIETA_SYS.Enabled = false;
            Etykieta_EAN_Label.Visible = true;
            EANLAB.Visible = true;
            Etykieta_EAN.Visible = true;
            button3.Visible = true;
            Licznik_Label.Visible = true;
            Licznik_Liczba.Visible = true;
            LICZNIK_CUR.Visible = true;
            Etykieta_EAN.Focus();



            LISTA_KONTROLNA.Enabled = false;
            ZacznijSkanowanie();
        }

        string Lista = "";
        string Etykieta = "";

        private void button2_ClickD(object sender, EventArgs e)
        {
            previousLot = "";
            Zakoncz_Skanowanie();
            button2.Text = "Skanuj EAN-y";


            if (ListaKartonow.Count > 0)
            {
                //////string nr_pal = (string)BazaDanychExternal.Wyczytaj_Jedna_Wartosc("select paletaNR from importRICI where CD_CONTENITORE='" + ListaKartonow[0] + "'");
                //////if (nr_pal == "")
                //////{
                //////    DialogResult dialogResult = MessageBox.Show("Czy chcesz przypisać te kartony do tej palety oraz listy kontrolnej?", "", MessageBoxButtons.YesNo, MessageBoxIcon.Question, MessageBoxDefaultButton.Button1);
                //////    if (dialogResult == DialogResult.Yes)
                //////    {
                //////        string znalezione1 = string.Join(",", ListaKartonow.ToArray());
                //////        BazaDanychExternal.DokonajUpdate("update importRICI set listcontrol_id="+Lista+",paletaNR="+Etykieta+" where CD_CONTENITORE in("+znalezione1+")");
                //////    }
                //////}
                //////else
                //////{

                //////}


                DialogResult dialogResult = MessageBox.Show("Czy chcesz przypisać te kartony do tej palety oraz listy kontrolnej?", "", MessageBoxButtons.YesNo, MessageBoxIcon.Question, MessageBoxDefaultButton.Button1);
                if (dialogResult == DialogResult.Yes)
                {
                    string znalezione1 = string.Join(",", ListaKartonow.ToArray());
                    BazaDanychExternal.DokonajUpdate("update importRICI set listcontrol_id=" + Lista + ",paletaNR=" + Etykieta + " where CD_CONTENITORE in(" + znalezione1 + ")");
                }


            }

            if (przyjeciePAL.Checked == true)
            {

                LISTA_KONTROLNA.Enabled = true;
                PAL_ETYKIETA_SYS.Enabled = true;
            }
            if (radioEAN.Checked == true)
            {
                PAL_ETYKIETA_SYS.Enabled = true;
            }
            if (radioKONTR.Checked == true)
            {
                LISTA_KONTROLNA.Enabled = true;
            }

            if (radioButton1.Checked == true)
            {

                LISTA_KONTROLNA.Enabled = true;
                PAL_ETYKIETA_SYS.Enabled = true;
            }
            Ilosc.Text = "1";
            Ilosc.Visible = false;
            Ilosc_Label.Visible = false;
            Etykieta_EAN_Label.Visible = false;
            Etykieta_EAN.Visible = false;
            button3.Visible = false;
            Etykieta_EAN.Text = "";
            //Etykieta_EAN.Focus();
            radioEAN.Enabled = true;
            przyjeciePAL.Enabled = true;
            radioKONTR.Enabled = true;
            Licznik_Label.Visible = false;
            Licznik_Liczba.Visible = false;
            Licznik_Liczba.Text = "";
            PAL_ETYKIETA_SYS.Enabled = true;
            LISTA_KONTROLNA.Enabled = true;
            radioButton1.Enabled = true;
            LICZNIK_CUR.Visible = false;
            Licznik_ETYKIET.Text = "";
            Licznik_PALET.Text = "";
            skasuj.Visible = false;
            //Etykieta_EAN.Focus();
            PAL_ETYKIETA_SYS.Text = "";
            PAL_ETYKIETA_SYS.Focus();
            this.button2.Click += new System.EventHandler(this.button2_Click);
            this.button2.Click -= new System.EventHandler(this.button2_ClickD);


        }



        private void ETYKIETA_LostFocus(object sender, EventArgs e)
        {
            Zakoncz_Skanowanie();
            Wlasciwosci.CurrentOperacja = "0";
        }
        private void LISTA_KONTROLNA_GotFocus(object sender, EventArgs e)
        {
            trybskanowania = 0;
            Wlasciwosci.CurrentOperacja = "1";
            ZacznijSkanowanie();
        }

        private void EAN_GotFocus(object sender, EventArgs e)
        {
            trybskanowania = 1;
            Wlasciwosci.CurrentOperacja = "2";
            ZacznijSkanowanie();
        }

        private void button3_Click(object sender, EventArgs e)
        {
            if (Etykieta_EAN.Text != "")
            {
                Zakoncz_Skanowanie();
                dodawanie(Etykieta_EAN.Text);
                Etykieta_EAN.Focus();
            }
        }

        private void Etykieta_EAN_KeyDown(object sender, KeyEventArgs e)
        {

        }

        private void Etykieta_EAN_KeyPress(object sender, KeyPressEventArgs e)
        {
            if (e.KeyChar == (char)13)
            {
                if (inputPanel1.Enabled == true)
                {
                    inputPanel1.Enabled = false;
                }
                else
                {
                    inputPanel1.Enabled = true;
                }
            }

        }

        private void Etykieta_EAN_LostFocus(object sender, EventArgs e)
        {
            inputPanel1.Enabled = false;
        }

        private string DajDate(string gg)
        {

            gg = gg.Replace("'", "");
            //MessageBox.Show(gg);
            if (gg.Contains("null") == true)
            {
                return "null";
            }
            if (gg == "00-00-0000")
            {
                return "null";
            }
            DateTime gh = DateTime.ParseExact(gg, "dd-MM-yyyy", null);
            if (gh.ToString("yyyy-MM-dd") == "0000-00-00")
            {
                return "null";
            }
            return "'" + gh.ToString("yyyy-MM-dd") + "'";


        }

        private void LISTA_KONTROLNA_TextChanged(object sender, EventArgs e)
        {
            doc_in_ID = "";
            doc_out_ID = "";
        }

        private void button4_Click(object sender, EventArgs e)
        {
            DialogResult dialogResult = MessageBox.Show("Czy napewno chcesz usunąć wszystkie kartony z tej palety ?", "", MessageBoxButtons.YesNo, MessageBoxIcon.Question, MessageBoxDefaultButton.Button1);
            if (dialogResult == DialogResult.Yes)
            {
                BazaDanychExternal.DokonajUpdate("delete from etykiety where paleta_id=" + PAL_ETYKIETA_SYS.Text.Replace("DS", "") + " and active is null");
            }
        }




    }
}