﻿using System;
using System.Linq;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using MySql.Data.MySqlClient;
using System.Text.RegularExpressions;
using System.Threading;
using System.Xml;

namespace Tarczyn__Magazyn
{
    public partial class TworzenieDL_najnowszeV2 : Form
    {
        ActionMenu myParent = null;
        List<string> _dl_wybor = new List<string>();
        int[] _dl = new int[100];

        int nr_dl = 0;
        private Thread Skanowanie;
        string operac_id_global = "";
        string zgodnosc = "1";

        string miejsce_kompletacji_ostatnio_wybierane = "";




        public TworzenieDL_najnowszeV2(ActionMenu MyParent)
        {
            InitializeComponent();
            this.myParent = MyParent;
            wms_wybrany.Text = Wlasciwosci.system_id;
            imie_nazwisk_label.Text = Wlasciwosci.wozek + ":" + Wlasciwosci.imie_nazwisko;
            //ok = myParent.conn;
            wyszukaj_dl();
            textBox1.Visible = false;
            button2.Visible = false;
            button3.Visible = false;
            this.ZacznijSkanowanie();
            Wlasciwosci.CurrentOperacja = "9";

            if (Wlasciwosci.system_id == "17")
            {
                textBox1.ReadOnly = true;
            }
            //skanuj();
        }

        private void wyszukaj_dl()
        {
            string zapytanie = "SELECT d.id as delivery_nr,sum(1) as palet FROM etykiety e left join delivery_et de on de.etykieta_id=e.id left join delivery d on de.delivery_id=d.id WHERE d.dl_status<4 and e.system_id=" + Wlasciwosci.system_id_id + " AND d.delivery_nr!='' and (d.dl_docout_id is null or d.dl_docout_id=0) GROUP BY d.delivery_nr order by d.id desc limit 99";
            object obj2 = BazaDanychExternal.Wyczytaj_Tabele(zapytanie);
            DataTable tabela = (DataTable)obj2;

            for (byte k = 0; k < tabela.Rows.Count; k++)
            {
                _dl_wybor.Add(tabela.Rows[k]["delivery_nr"].ToString() + " | " + tabela.Rows[k]["palet"].ToString());
                _dl[k] = Convert.ToInt32(tabela.Rows[k]["delivery_nr"]);
            }
            if (_dl_wybor.Count == 0)
            {
                MessageBox.Show("Brak dokumentów DL ");
            }
            else
            {
                comboBox1.Visible = true;
                BindingSource bs = new BindingSource();
                bs.DataSource = _dl_wybor;
                comboBox1.DataSource = bs;
            }
        }





        private void button1_Click(object sender, EventArgs e)
        {
            Skaner.Przewij_Skanowanie();
            powrot.Visible = false;
            comboBox1.Enabled = false;
            textBox1.Visible = true;
            button3.Visible = true;
            if (Wlasciwosci.system_id_id == "17")
            {
                button4.Visible = true;
            }
            //MessageBox.Show("1");

            nr_dl = _dl[comboBox1.SelectedIndex];
            //MessageBox.Show("2");
            if (nr_dl == 0 || nr_dl.ToString() == "")
            {

                MessageBox.Show("Wybierz DL ");
                return;
            }
            label5.Text = nr_dl.ToString();
            //MessageBox.Show("3");
            string zapytanie = "SELECT last FROM docnumber d WHERE d.name='operacja_id';";

            operac_id_global = (string)BazaDanychExternal.Wyczytaj_Jedna_Wartosc(zapytanie);

            string zapyt = "update docnumber d set last=last+1 WHERE d.name='operacja_id'; ";
            BazaDanychExternal.Wyczytaj_Jedna_Wartosc(zapyt);
            //MessageBox.Show("4");
            //BazaDanychExternal.DokonajUpdate("update etykiety set delivery_nr=null where active=1 and delivery_nr=" + nr_dl);
            //ilosc_etykiet.Text = "0";
            ilosc_etykiet.Text = delivery_etykiety(_dl[comboBox1.SelectedIndex].ToString()) + "/" + wczytane_licznik(_dl[comboBox1.SelectedIndex].ToString());
            this.button1.Click -= new EventHandler(this.button1_Click);
            this.button1.Click += new EventHandler(this.Zakoncz_Skanowanie);
            this.button1.Text = "Zakończ";
            this.ZacznijSkanowanie();
            //MessageBox.Show("5");

            //BazaDanychExternal.DokonajUpdate("insert into operacje(etykieta_id, doc_type, doc_nr, imie_nazwisko, typ_operacji,system_id,wozek,operac_id,ilosc) values('0','DL','" + nr_dl.ToString() + "','" + Wlasciwosci.imie_nazwisko + "','DL_SZ','" + Wlasciwosci.system_id_id + "','" + Wlasciwosci.wozek + "','" + operac_id_global + "','0');");
            zgodnosc = (string)BazaDanychExternal.Wyczytaj_Jedna_Wartosc("select zgodnosc from delivery d  where d.id='" + nr_dl + "'    "); //and e.dataprod='" + tabela.Rows[w]["dataprod"].ToString() + "' and status_prism='" + tabela.Rows[w]["status_prism"].ToString() + "' and blloc='" + tabela.Rows[w]["blloc"].ToString() + "'


            if (zgodnosc == "2")
            {
                sprawdzanie.Visible = true;
                button4.Visible = true;
            }
            else
            {
                sprawdzanie.Visible = false;
                button4.Visible = false;
            }

            if (zgodnosc == "1")
            {
                button_szukanie.Visible = true;
                button4.Visible = false;
            }
            else
            {
                button_szukanie.Visible = false;
            }
            //MessageBox.Show("6");
            string miejsce_kompletacji = (string)BazaDanychExternal.Wyczytaj_Jedna_Wartosc("select ifnull(miejsce_kompletacji,'0') as miejsce_kompletacji from delivery d  where d.id='" + nr_dl + "' limit 1;   ");
            if (miejsce_kompletacji == "0")
            {
                string zapytanie_rodzaje = "SELECT m.id AS id, concat(m.regal,'-',m.miejsce,'-',m.poziom) AS nazwa  FROM miejsca m WHERE m.widoczne = 1 AND  (length(m.regal)>2) AND (cast(m.regal as unsigned)=0) and (regal like '%RMP%' or regal like '%PROD%')   ORDER BY cast(m.regal as UNSIGNED) asc, m.miejsce asc, m.poziom asc";
                //string ff = (string)BazaDanychExternal.Wyczytaj_Jedna_Wartosc(zapyt_nazwa_palety);
                //MessageBox.Show("7");

                PoleCombobox XC = new PoleCombobox(zapytanie_rodzaje, "Wybierz miejsce kompletacji", miejsce_kompletacji_ostatnio_wybierane);
                if (XC.ShowDialog() == DialogResult.OK)
                {
                    //MessageBox.Show("8");
                    if (XC.wybierana_nazwa == "")
                    {
                        MessageBox.Show("Nie dokonano wyboru");
                        return;
                    }
                    miejsce_kompletacji_ostatnio_wybierane = XC.wybierana_nazwa;

                    BazaDanychExternal.DokonajUpdate("update delivery d  set miejsce_kompletacji='" + XC.wybierane_id + "' where d.id='" + nr_dl + "' ");
                }
                else
                {
                    return;
                }
            }


        }


        private void Zakoncz_Skanowanie(object sender, EventArgs e)
        {
            comboBox1.Enabled = true;
            label5.Text = "";
            powrot.Visible = true;
            textBox1.Visible = false;
            button3.Visible = false;
            button4.Visible = false;

            this.Skanowanie.Abort();
            Skaner.Przewij_Skanowanie();
            this.button1.Click -= new EventHandler(this.Zakoncz_Skanowanie);
            this.button1.Click += new EventHandler(this.button1_Click);
            this.button1.Text = "Wybierz";
            sprawdzanie.Visible = false;
            //MessageBox.Show("Zakończyłem tworzenie dokumentu DL:"+nr_dl);
            //this.myParent.Show();
            //base.Close();
        }



        private void powrot_Click(object sender, EventArgs e)
        {

            powrot_sprawdz();
            Wlasciwosci.CurrentOperacja = "0";
            this.Skanowanie.Abort();
            Skaner.Przewij_Skanowanie();
            this.myParent.Show();
            this.Close();
        }

        private void powrot_sprawdz()
        {

        }



        private void button1_Anuluj(object sender, EventArgs e)
        {

        }
        private void ZacznijSkanowanie()
        {
            StringBuilder login = new StringBuilder();
            Skaner.UstawTryb_String(login);
            Skaner.Zacznij_Skanowanie();
            this.Skanowanie = new Thread(() => this.CheckString(login));
            this.Skanowanie.IsBackground = true;
            this.Skanowanie.Start();
        }

        private void CheckString(StringBuilder cc)
        {
            EventHandler method = null;
            while (true)
            {
                if (cc.ToString() != "")
                {
                    if (method == null)
                    {
                        method = delegate
                        {
                            //this.vvv();                            
                            this.dodawanie(cc.ToString());
                        };
                        //method = () => this.dodawanie(cc.ToString());
                    }
                    this.Invoke(method);
                    return;
                }
                Thread.Sleep(200);
            }
        }


        private void dodawanie(string ops)
        {
            result_label.Text = "";

            Skaner.Przewij_Skanowanie();
            //MessageBox.Show("1");

            if (!BazaDanychExternal.SprawdzCzyIstniejePolaczenie())
            {
                MessageBox.Show("Brak połączenia z bazą danych , spróbuj ponownie.");

            }
            else
            {


                if (ops.Substring(0, 2) == "DL")
                {
                    if (comboBox1.Enabled == false)
                    {
                        MessageBox.Show("Rozpoczęto już DL. Nie można zmienić!");
                        return;
                    }

                    string a = ops.Trim(new Char[] { 'D', 'L' });
                    int z = 0;
                    for (byte k = 0; k < _dl.Length; k++)
                    {
                        if (_dl[k].ToString() == a)
                        {
                            comboBox1.SelectedIndex = k;
                            z++;
                        }
                    }
                    if (z == 0)
                    {
                        _dl_wybor.Clear();
                        string zapytanie = "SELECT d.id as delivery_nr,sum(1) as palet FROM etykiety e left join delivery_et de on de.etykieta_id=e.id left join delivery d on de.delivery_id=d.id WHERE d.dl_status<4 and e.system_id=" + Wlasciwosci.system_id_id + " AND d.delivery_nr!='' AND d.id=" + a + " and (d.dl_docout_id is null or d.dl_docout_id=0) GROUP BY d.delivery_nr order by d.id desc limit 99";
                        object obj4 = BazaDanychExternal.Wyczytaj_Tabele(zapytanie);
                        DataTable tabela4 = (DataTable)obj4;
                        if (tabela4 != null)
                        {
                            if (tabela4.Rows.Count == 0)
                            {
                                MessageBox.Show("Brak do zrobienia na tym kliencie numeru DL " + a);
                                return;
                            }
                            
                            _dl_wybor.Add(tabela4.Rows[0]["delivery_nr"].ToString() + " | " + tabela4.Rows[0]["palet"].ToString());
                            _dl[0] = Convert.ToInt32(tabela4.Rows[0]["delivery_nr"]);

                            if (_dl_wybor.Count == 0)
                            {
                                MessageBox.Show("Brak do zrobienia na tym kliencie numeru DL " + a);

                            }
                            else
                            {

                                comboBox1.Visible = true;
                                BindingSource bs = new BindingSource();
                                bs.DataSource = _dl_wybor;
                                comboBox1.DataSource = bs;
                            }
                        }
                        else
                        {
                            MessageBox.Show("Nie znaleziono: " + ops);
                            return;
                        }





                    }
                }
                else
                    if (ops.Length > 2 && ops.Length < 22)
                    {
                        //MessageBox.Show("2");

                        //if (ops.Substring(0, 2) != "DS")
                        //{
                        //    ops = "DS" + ops;
                        //}

                        textBox1.Text = ops;


                        //MessageBox.Show("3");
                        if (sprawdzanie.Checked == false)
                        {
                            Wlasciwosci.CurrentOperacja = "9";
                            get_dl_items_group_szykowanie(_dl[comboBox1.SelectedIndex].ToString(), textBox1.Text);
                            textBox1.Text = "";
                        }
                        else
                        {
                            
                            Wlasciwosci.CurrentOperacja = "10";
                            sprawdzanie_towaru(_dl[comboBox1.SelectedIndex].ToString(), textBox1.Text);
                            label8.Text = delivery_etykiety_paleta(_dl[comboBox1.SelectedIndex].ToString(), textBox1.Text) + "/" + wczytane_licznik_paleta(_dl[comboBox1.SelectedIndex].ToString(), textBox1.Text);
                            //label8.Text = delivery_etykiety_paleta(_dl[comboBox1.SelectedIndex].ToString(), textBox1.Text);
                        }
                        //MessageBox.Show("10");
                    }
                    else
                    {
                        MessageBox.Show("To nie jest etykieta systemu");
                    }






            }
            ZacznijSkanowanie();
        }

        private void sprawdzanie_towaru(string nr_dl, string etykieta)
        {
            string aa = (string)BazaDanychExternal.Wyczytaj_Jedna_Wartosc("select count(1) from etykiety e left join delivery_et de on de.etykieta_id=e.id where de.delivery_id='" + nr_dl + "' and e.etykieta_klient='" + etykieta + "' and e.system_id='" + Wlasciwosci.system_id_id + "'");
            string bb = (string)BazaDanychExternal.Wyczytaj_Jedna_Wartosc("select e.id from etykiety e  where e.etykieta_klient='" + etykieta + "' and e.active=1 and e.system_id='" + Wlasciwosci.system_id_id + "'");
            //(string)BazaDanychExternal.Wyczytaj_Jedna_Wartosc("select count(1) from etykiety e left join delivery_et de on de.etykieta_id=e.id left join delivery d on de.delivery_id=d.id where d.id='" + nr_dl + "' and e.system_id='" + Wlasciwosci.system_id_id + "' ");
            //MessageBox.Show("aa" + aa);
            if (bb != "") bb = "0";
            BazaDanychExternal.DokonajUpdate("insert into operacje(etykieta_id, doc_type, doc_nr, imie_nazwisko, typ_operacji,system_id,wozek,operac_id,ilosc) values('" + bb + "','DL','" + nr_dl + "','" + Wlasciwosci.imie_nazwisko + "','DL_SPR_GG','" + Wlasciwosci.system_id_id + "', '" + Wlasciwosci.wozek + "','" + operac_id_global + "','1');");


            if (aa != "0")
            {
                result_label.Text = "Pobierz " + etykieta;
                BazaDanychExternal.DokonajUpdate("update delivery_et de left join etykiety e on de.etykieta_id=e.id set de.skan=1 where de.delivery_id='" + nr_dl + "' and e.system_id='" + Wlasciwosci.system_id_id + "' and e.etykieta_klient='" + etykieta + "' and de.skan=0");

            }
            else
            {
                result_label.Text = "";
            }

        }


        public XmlNode KomunikacjaSerwerem(string nr_dl, string etykieta, string paleta_id)
        {
            XmlNodeList xmlnode_local = null;
            XmlNode node_local = null;
            etykieta = etykieta.Replace("#", "%23");
            XmlDocument doc1 = WebService.Pobierz_XmlDocument("delivery_manual_skaner_przepakowanie.php?akcja=realizacja_zadania&pracownik_id=" + Wlasciwosci.id_Pracownika + "&imie_nazwisko=" + Uri.EscapeUriString(Wlasciwosci.imie_nazwisko) + "&paleta_id=" + paleta_id + "&operac_id=" + operac_id_global + "&wozek=" + Wlasciwosci.wozek + "&system_id=" + Wlasciwosci.system_id_id + "&delivery_id=" + nr_dl + "&etykieta_scan=" + etykieta);
            xmlnode_local = doc1.GetElementsByTagName("dane");

            foreach (XmlNode wynik in xmlnode_local)
            {
                node_local = wynik;
            }
            return node_local;
        }



        public void get_dl_items_group_szykowanie(string nr_dl, string etykieta)
        {


            XmlNode node_local = KomunikacjaSerwerem(nr_dl, etykieta, "");

            if (node_local["komunikat"].InnerText != "OK")
            {
                MessageBox.Show(node_local["komunikat"].InnerText);
                //MessageBox.Show(node_local.InnerXml
                return;
                //this.ZacznijSkanowanie();
            }
            else
            {
                if (node_local["akcja"].InnerText == "przepakowanie")
                {

                    SztukiOpakowania XA = new SztukiOpakowania(node_local["ilosc"].InnerText, node_local["ilosc_zamawiana"].InnerText, node_local["ilosc_w_opakowaniu"].InnerText, node_local["kod"].InnerText, node_local["kod_nazwa"].InnerText);

                    if (XA.ShowDialog() == DialogResult.OK)
                    {
                        if (XA.komunikat != "")
                        {
                            MessageBox.Show(XA.komunikat);
                            //ZacznijSkanowanie();
                            //return;
                        }
                        else
                        {
                            XmlNode node_local2 = KomunikacjaSerwerem(nr_dl, etykieta, XA.paleta_ds);
                            if (node_local2["komunikat"].InnerText != "OK")
                            {
                                MessageBox.Show(node_local2["komunikat"].InnerText);
                                //MessageBox.Show(node_local.InnerXml
                                //return;
                                //this.ZacznijSkanowanie();
                            }
                            else
                            {
                                result_label.Text = node_local2["result_label"].InnerText;


                            }
                        }
                    }

                }
                else
                {
                    result_label.Text = node_local["result_label"].InnerText;
                    ilosc_etykiet.Text = node_local["licznik"].InnerText;
                    
                }


            }


        }


        private string DajDate(string gg)
        {

            gg = gg.Replace("'", "");
            //MessageBox.Show(gg);
            if (gg == "null")
            {
                return "null";
            }

            DateTime gh = DateTime.ParseExact(gg, "dd-MM-yyyy", null);
            return "'" + gh.ToString("yyyy-MM-dd") + "'";


        }


        private string sprawdz_czy_null(string bb)
        {
            if (bb == "")
            {

                return "null";
            }
            else
            {

                try
                {
                    Convert.ToDouble(bb);
                    return bb;
                }
                catch
                {
                    return "'" + bb + "'";
                }
            }

        }


        private string wczytane_licznik(string nr_dl)
        {
            return (string)BazaDanychExternal.Wyczytaj_Jedna_Wartosc("select count(1) from dlcollect d where nr_dl='" + nr_dl + "' and d.system_id='" + Wlasciwosci.system_id_id + "';");
        }

        private string delivery_etykiety(string nr_dl)
        {
            return (string)BazaDanychExternal.Wyczytaj_Jedna_Wartosc("select count(1) from etykiety e left join delivery_et de on de.etykieta_id=e.id left join delivery d on de.delivery_id=d.id where d.id='" + nr_dl + "' and e.system_id='" + Wlasciwosci.system_id_id + "'  ");

        }
        private string wczytane_licznik_paleta(string nr_dl, string pal)
        {
            return (string)BazaDanychExternal.Wyczytaj_Jedna_Wartosc("select count(1) from delivery_et de left join etykiety e on de.etykieta_id=e.id where de.delivery_id='" + nr_dl + "' and e.system_id='" + Wlasciwosci.system_id_id + "' and e.paleta_id in (SELECT paleta_id FROM etykiety e WHERE e.etykieta_klient='" + pal + "' and active=1) and de.skan=1");
        }

        private string delivery_etykiety_paleta(string nr_dl, string pal)
        {
            return (string)BazaDanychExternal.Wyczytaj_Jedna_Wartosc("select count(1) from delivery_et de left join etykiety e on de.etykieta_id=e.id where de.delivery_id='" + nr_dl + "' and e.system_id='" + Wlasciwosci.system_id_id + "' and e.paleta_id in (SELECT paleta_id FROM etykiety e WHERE e.etykieta_klient='" + pal + "' and active=1)  ");

        }


        private void kasuj_ostatnio_wczytane(string nr_dl)
        {
            string aa = (string)BazaDanychExternal.Wyczytaj_Jedna_Wartosc("select id from dlcollect d where nr_dl='" + nr_dl + "' and d.system_id='" + Wlasciwosci.system_id_id + "' order by d.id desc limit 1;");
            BazaDanychExternal.DokonajUpdate("delete from dlcollect where id='" + aa + "' limit 1;");
        }



        private void aktualizuj_etykiete(string etykieta, string kod, string ilosc)
        {
            //if(nr_etykiety)
            try
            {
                //ok = myParent.conn;
                //MySqlCommand cmdSel = new MySqlCommand("update etykiety set nrsap='" + Convert.ToInt64(nrsap) + "',podkod='" + podkod + "' where id=" + nr_etykiety + " limit 1;", ok);
                //cmdSel.CommandText = "update etykiety set nrsap='" + Convert.ToInt64(nrsap) + "',podkod='" + podkod + "' where id=" + nr_etykiety + " limit 1;";
                //cmdSel.ExecuteNonQuery();
                result_label.Text = "Wczytano :" + etykieta + "\n" +
                    "Kod:" + kod + "   " +
                    "Ilość: " + ilosc;
                //etykieta_text.Text = "";

            }
            catch (MySqlException ex)
            {
                MessageBox.Show(ex.ToString());
            }

            /*
             *            DialogResult result3 = MessageBox.Show("Napewno chcesz zakończyć pracę na deklaracji?",
                "Eksport danych",
                MessageBoxButtons.YesNo,
                MessageBoxIcon.Question,
                MessageBoxDefaultButton.Button2);
            if (result3 == DialogResult.Yes)
            {}
             */

        }
        private void czysc()
        {



        }



        private void timer1_Tick(object sender, EventArgs e)
        {
            this.Close();
            myParent.Show();
        }




        private void label2_ParentChanged(object sender, EventArgs e)
        {

        }



        #region fullscreen
        private void fullscreenmode()
        {
            this.Width = Screen.PrimaryScreen.Bounds.Width;
            this.Height = Screen.PrimaryScreen.Bounds.Height;
            this.Left = 0;
            this.Top = 0;
        }
        #endregion

        private void button3_Click(object sender, EventArgs e)
        {
            //kasuj_ostatnio_wczytane(_dl[comboBox1.SelectedIndex].ToString());
            //ilosc_etykiet.Text = delivery_etykiety(_dl[comboBox1.SelectedIndex].ToString()) + "/" + wczytane_licznik(_dl[comboBox1.SelectedIndex].ToString());
        }

        private void button2_Click(object sender, EventArgs e)
        {
            dodawanie(textBox1.Text);
            textBox1.Text = "";
        }

        private void checkBox1_CheckStateChanged(object sender, EventArgs e)
        {
            this.Skanowanie.Abort();
            Skaner.Przewij_Skanowanie();
            this.ZacznijSkanowanie();



            comboBox1.Visible = true;
            button1.Visible = true;
            button3.Visible = true;


        }

        private void button4_Click(object sender, EventArgs e)
        {


            //string zapyt4 = "SELECT aa.*,if(aa.ilosc=aa.ilosc_dl, 'OK', 'NO OK') AS wynik FROM (SELECT k.kod, TRIM(TRAILING '.' FROM TRIM(TRAILING '0' FROM ROUND(sum(de.ilosc_zamawiana), 3))) AS ilosc, (SELECT TRIM(TRAILING '.' FROM TRIM(TRAILING '0' FROM ROUND(sum(ee.ilosc), 3))) AS ilosc FROM dlcollect dc LEFT JOIN etykiety ee ON ee.id=dc.nr_et WHERE dc.nr_dl=de.delivery_id and ee.kod_id=k.id GROUP BY ee.kod_id ) AS ilosc_dl FROM etykiety e LEFT JOIN delivery_et de ON e.id=de.etykieta_id LEFT JOIN kody k ON e.kod_id=k.id WHERE de.delivery_id='" + _dl[comboBox1.SelectedIndex].ToString() + "' GROUP BY e.kod_id) AS aa UNION SELECT aa.*,if(aa.ilosc=aa.ilosc_dl, 'OK', 'NO OK') AS wynik FROM (SELECT k.kod, (SELECT TRIM(TRAILING '.' FROM TRIM(TRAILING '0' FROM ROUND(sum(de.ilosc_zamawiana), 3))) AS ilosc FROM etykiety ee LEFT JOIN delivery_et de ON ee.id=de.etykieta_id WHERE de.delivery_id=dc.nr_dl AND e.kod_id=ee.kod_id GROUP BY ee.kod_id) AS ilosc, TRIM(TRAILING '.' FROM TRIM(TRAILING '0' FROM ROUND(sum(e.ilosc), 3))) AS ilosc_dl FROM dlcollect dc LEFT JOIN etykiety e ON e.id=dc.nr_et LEFT JOIN kody k ON e.kod_id=k.id WHERE dc.nr_dl='" + _dl[comboBox1.SelectedIndex].ToString() + "' GROUP BY e.kod_id) AS aa having ilosc<>ilosc_dl limit 1";
            string zapyt4 = "SELECT aa.kod,aa.ilosc,IFNULL(aa.ilosc_dl,0) as ilosc_dl,if(aa.ilosc=aa.ilosc_dl, 'OK', 'NO OK') AS wynik FROM (SELECT k.kod, TRIM(TRAILING '.' FROM TRIM(TRAILING '0' FROM ROUND(sum(de.ilosc_zamawiana), 3))) AS ilosc, (SELECT TRIM(TRAILING '.' FROM TRIM(TRAILING '0' FROM ROUND(sum(ee.ilosc), 3))) AS ilosc FROM dlcollect dc LEFT JOIN etykiety ee ON ee.id=dc.nr_et WHERE dc.nr_dl=de.delivery_id and ee.kod_id=k.id GROUP BY ee.kod_id ) AS ilosc_dl FROM etykiety e LEFT JOIN delivery_et de ON e.id=de.etykieta_id LEFT JOIN kody k ON e.kod_id=k.id WHERE de.delivery_id='" + _dl[comboBox1.SelectedIndex].ToString() + "' GROUP BY e.kod_id) AS aa UNION SELECT aa.*,if(aa.ilosc=aa.ilosc_dl, 'OK', 'NO OK') AS wynik FROM (SELECT k.kod, (SELECT TRIM(TRAILING '.' FROM TRIM(TRAILING '0' FROM ROUND(sum(de.ilosc_zamawiana), 3))) AS ilosc FROM etykiety ee LEFT JOIN delivery_et de ON ee.id=de.etykieta_id WHERE de.delivery_id=dc.nr_dl AND e.kod_id=ee.kod_id GROUP BY ee.kod_id) AS ilosc, TRIM(TRAILING '.' FROM TRIM(TRAILING '0' FROM ROUND(sum(e.ilosc), 3))) AS ilosc_dl FROM dlcollect dc LEFT JOIN etykiety e ON e.id=dc.nr_et LEFT JOIN kody k ON e.kod_id=k.id WHERE dc.nr_dl='" + _dl[comboBox1.SelectedIndex].ToString() + "' GROUP BY e.kod_id) AS aa having ilosc<>ilosc_dl limit 1";

            object obj4 = BazaDanychExternal.Wyczytaj_Tabele(zapyt4);
            DataTable tabela4 = (DataTable)obj4;
            //MessageBox.Show("7");
            if (tabela4.Rows.Count == 1)
            {
                string kod_local = tabela4.Rows[0]["kod"].ToString();
                string zapytanie5 = @"select * from (SELECT concat(regal,'-',miejsce,'-',poziom) as adres FROM etykiety e left join kody k on e.kod_id=k.id
left join status_system s on e.status_id=s.id left join miejsca m on e.miejscep=m.id
left join dlcollect dc on e.id=dc.nr_et
WHERE e.system_id=" + Wlasciwosci.system_id_id + @" AND e.active=1 and s.funkcja_stat!='blokada_wyd'
and regal not like '%RMP%' and kod='" + kod_local + @"' and poziom='A' and dc.nr_et is null
order by cast(regal as unsigned),miejsce,poziom limit 1) as aa
union
select * from (SELECT concat(regal,'-',miejsce,'-',poziom) as adres FROM etykiety e left join kody k on e.kod_id=k.id
left join status_system s on e.status_id=s.id left join miejsca m on e.miejscep=m.id
left join dlcollect dc on e.id=dc.nr_et
WHERE e.system_id=" + Wlasciwosci.system_id_id + @" AND e.active=1 and s.funkcja_stat!='blokada_wyd'
and regal not like '%RMP%' and kod='" + kod_local + @"' and poziom='B' and dc.nr_et is null
order by cast(regal as unsigned),miejsce,poziom limit 1) as aa
union
select * from (SELECT concat(regal,'-',miejsce,'-',poziom) as adres FROM etykiety e left join kody k on e.kod_id=k.id
left join status_system s on e.status_id=s.id left join miejsca m on e.miejscep=m.id
left join dlcollect dc on e.id=dc.nr_et
WHERE e.system_id=" + Wlasciwosci.system_id_id + @" AND e.active=1 and s.funkcja_stat!='blokada_wyd'
and regal not like '%RMP%' and kod='" + kod_local + @"' and poziom='C' and dc.nr_et is null
order by cast(regal as unsigned),miejsce,poziom limit 1) as aa
union
select * from (SELECT concat(regal,'-',miejsce,'-',poziom) as adres FROM etykiety e left join kody k on e.kod_id=k.id
left join status_system s on e.status_id=s.id left join miejsca m on e.miejscep=m.id
left join dlcollect dc on e.id=dc.nr_et
WHERE e.system_id=" + Wlasciwosci.system_id_id + @" AND e.active=1 and s.funkcja_stat!='blokada_wyd'
and regal not like '%RMP%' and kod='" + kod_local + @"' and poziom='D' and dc.nr_et is null
order by cast(regal as unsigned),miejsce,poziom limit 1) as aa
union
select * from (SELECT concat(regal,'-',miejsce,'-',poziom) as adres FROM etykiety e left join kody k on e.kod_id=k.id
left join status_system s on e.status_id=s.id left join miejsca m on e.miejscep=m.id
left join dlcollect dc on e.id=dc.nr_et
WHERE e.system_id=" + Wlasciwosci.system_id_id + @" AND e.active=1 and s.funkcja_stat!='blokada_wyd'
and regal not like '%RMP%' and kod='" + kod_local + @"' and poziom='E' and dc.nr_et is null
order by cast(regal as unsigned),miejsce,poziom limit 1) as aa
union
select * from (SELECT concat(regal,'-',miejsce,'-',poziom) as adres FROM etykiety e left join kody k on e.kod_id=k.id
left join status_system s on e.status_id=s.id left join miejsca m on e.miejscep=m.id
left join dlcollect dc on e.id=dc.nr_et
WHERE e.system_id=" + Wlasciwosci.system_id_id + @" AND e.active=1 and s.funkcja_stat!='blokada_wyd'
and regal not like '%RMP%' and kod='" + kod_local + @"' and poziom='E' and dc.nr_et is null
order by cast(regal as unsigned),miejsce,poziom limit 1) as aa";

                object obj5 = BazaDanychExternal.Wyczytaj_Tabele(zapytanie5);
                DataTable tabela5 = (DataTable)obj5;
                //MessageBox.Show("7");
                if (tabela5.Rows.Count == 0)
                {
                    MessageBox.Show("Brak podpowiedzi");
                    //textBox1.Text = "";
                    return;
                }

                string komunikat_5 = "";

                for (int zz = 0; zz < tabela5.Rows.Count; zz++)
                {
                    komunikat_5 += @"" + string.Format(@" | " + tabela5.Rows[zz]["adres"].ToString(), Environment.NewLine);
                }
                MessageBox.Show(komunikat_5);
            }
            else
            {
                MessageBox.Show("DL kompletna");
            }










        }

        private void zmiana_sprawdzania(object sender, EventArgs e)
        {
            string zapytanie = "SELECT last FROM docnumber d WHERE d.name='operacja_id';";

            operac_id_global = (string)BazaDanychExternal.Wyczytaj_Jedna_Wartosc(zapytanie);

            string zapyt = "update docnumber d set last=last+1 WHERE d.name='operacja_id'; ";
            BazaDanychExternal.Wyczytaj_Jedna_Wartosc(zapyt);
        }

        private void button4_Click_1(object sender, EventArgs e)
        {
            if (myParent.myWlan.get_Signal_int() < 2)
            {
                MessageBox.Show("Trwa łączenie. Spróbuj ponownie...");
                return;
            }
            //DL_kontrola nowy = new DL_kontrola(this, "" + nr_dl);
            //nowy.Show();
            //this.Hide();
        }








    }
}