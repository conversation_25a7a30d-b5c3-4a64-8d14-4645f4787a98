<?php

error_reporting(E_ALL);
ini_set('display_errors', 1);
include_once '../Db.class.php';
include_once '../funkcje.inc';
$db = new Db();

// Sprawdzenie parametrów
if (empty($_GET["adres_ip"]) || empty($_GET["miejsce"])) {
    xml_from_indexed_array(
        array(
            'komunikat' => "Błąd: brak wymaganych parametrów (adres_ip lub miejsce)",
        )
    );
    exit();
}

$adres_ip = $_GET["adres_ip"];
$miejsce = $_GET["miejsce"];

// Parsowanie miejsca (format: MP-1-12-45-1)
$miejsce_parts = explode('-', $miejsce);
if (count($miejsce_parts) !== 5 || $miejsce_parts[0] !== 'MP') {
    xml_from_indexed_array(
        array(
            'komunikat' => "Błąd: nieprawidłowy format miejsca. Wymagany format: MP-1-12-45-1",
        )
    );
    exit();
}

$hala = $miejsce_parts[1];
$regal = $miejsce_parts[2];
$miejsce_nr = $miejsce_parts[3];
$poziom = $miejsce_parts[4];

// Wyszukanie etykiet dla danego miejsca
$sql = "SELECT e.id , e.system_id AS system_id, e.etykieta_klient AS etykieta_klient, e.magazyn AS magazyn, e.active AS active, e.miejscep AS miejscep, e.przeznaczenie_id AS przeznaczenie_id, e.kod_id AS kod_id, e.paleta_id AS paleta_id, e.dataprod AS dataprod, e.data_waznosci AS data_waznosci, e.status AS status, e.blloc AS blloc, e.akcja_id AS akcja_id, e.akcja_id AS akcjanr_id, e.status_prism AS status_prism, e.stat AS stat, e.status_id AS status_id, st.nazwa AS status_nazwa, st.funkcja_stat AS funkcja_stat, e.kartony AS kartony, ed3.kolor AS kolor, ed3.plec AS plec, ed3.rozmiar_nr AS rozmiar_nr, ed3.uszkodzenie AS uszkodzenie, e.lot AS lot, e.sscc AS sscc, e.gtin AS gtin, e.edycja_et AS edycja_et, TRIM(TRAILING '.' FROM TRIM(TRAILING '0' from e.ilosc)) AS ilosc, e.ts AS ts, e.nretykiety AS nretykiety, e.docin_id AS docin_id, e.docout_id AS docout_id, e.delivery_id AS delivery_id, e.listcontrol_id AS listcontrol_id, concat('Hala ',m.hala,' ',m.regal,'-',m.miejsce,'-',m.poziom) AS adres, concat(m.regal,'-',m.miejsce,'-',m.poziom) AS adres2, k.kod_nazwa AS kod_nazwa, k.jm AS jm, k.kod AS kod, k.kod2 AS kod2, (if(e.ilosc is null,1,e.ilosc)/if(k.ilosc_w_opakowaniu is null,1,ilosc_w_opakowaniu)) AS ilosc_opak, k.opakowanie_jm AS opakowanie_jm, js.nazwa AS j_skladowania_nazwa, concat(p.ilosc, ' ',tp.opis ) AS paletanazwa, p.pal_klient AS pal_klient, tp.kod AS tpkod, pdocin.imie_nazwisko AS docin_imie_nazwisko, pdout.imie_nazwisko AS pdout_imie_nazwisko, s.skrot AS skrot, m.hala AS hala, din.doc_nr AS doc_nr, din.doc_date AS doc_date, din.doc_ts AS doc_ts, din.doc_internal AS doc_internal, din.doc_type AS doc_type, din.pracownik_id AS pracownik_id_docin, din.kontrah_id AS kontrah_id_docin, din.doc_ref AS doc_ref, din.doc_uwagi AS doc_uwagi, din.dostawa_typ AS dostawa_typ, din1.nr_doc_dost AS nr_doc_dost, din1.data_wystawienia AS data_wystawienia, din1.nr_zam_mpg AS nr_zam_mpg, din1.numeroavviso AS numeroavviso, dout.docout_type AS docout_type, dout.docout_nr AS docout_nr, dout.docout_date AS docout_date, dout.docout_ts AS docout_ts, dout.pracownik_id AS pracownik_id_docout, dout.kontrah_id AS kontrah_id_docout, dout.docout_ref AS docout_ref, dout.docout_uwagi AS docout_uwagi, dout.pracownik_id_kier AS pracownik_id_kier, dout.docout_internal AS docout_internal, dout.docout_date_req AS docout_date_req, dl.delivery_nr AS delivery_nr, dl.delivery_internal AS delivery_internal, dl.delivery_date AS delivery_date, dl.pracownik_id AS pracownik_id_delivery, dl.delivery_ts AS delivery_ts, kin.logo AS kinlogo, kin.dest_code AS dest_code, kout.logo AS koutlogo, a.nazwa AS akcja_nazwa, k.ean
FROM etykiety e 
LEFT JOIN status_system AS st ON e.status_id=st.id 
LEFT JOIN etykiety_dod3 AS ed3 ON e.id=ed3.id and e.system_id=ed3.system_id 
LEFT JOIN miejsca AS m ON e.miejscep=m.id 
LEFT JOIN kody AS k ON k.id=e.kod_id 
LEFT JOIN palety AS p ON e.paleta_id=p.id 
LEFT JOIN typypalet AS tp ON p.typypalet_id=tp.id 
LEFT JOIN jednostka_skladowania AS js ON p.j_skladowania_id=js.id 
LEFT JOIN docin AS din ON e.docin_id=din.id 
LEFT JOIN docout AS dout ON e.docout_id=dout.id 
LEFT JOIN delivery AS dl ON e.delivery_id=dl.id 
LEFT JOIN kontrah AS kin ON din.kontrah_id=kin.id 
LEFT JOIN kontrah AS kout ON dout.kontrah_id=kout.id 
LEFT JOIN pracownicy AS pdocin ON din.pracownik_id=pdocin.id 
LEFT JOIN pracownicy AS pdout ON dout.pracownik_id=pdout.id 
LEFT JOIN systemy AS s ON e.system_id=s.wartosc 
LEFT JOIN docin_dod1 AS din1 ON din.id=din1.docin_id 
LEFT JOIN akcja AS a ON a.id=e.akcja_id
WHERE m.hala = '$hala'
AND m.regal = '$regal' 
AND m.miejsce = '$miejsce_nr'
AND m.poziom = '$poziom'
AND e.active = 1";

$result = $db->mGetResultAsXML($sql);

if (empty($result)) {
    xml_from_indexed_array(
        array(
            'komunikat' => "Nie znaleziono aktywnych etykiet dla miejsca $miejsce",
        )
    );
    exit();
}

// Drukowanie etykiet
foreach ($result as $etykieta) {
    // $layout = '^XA'
    //     . '^FO140,10   ^ADN,60,30   ^FD ' . $etykieta['skrot'] . '^FS '
    //     . '^FO470,80   ^ADN,30,14   ^FDH:' . $etykieta['hala'] . ' ' 
    //     . $etykieta['adres2'] . ' ^FS '
    //     . '^FO30,80    ^ADN,30,14   ^FD ILOSC: ' . $etykieta['ilosc'] . ' ' . $etykieta['jm'] . '^FS'
    //     . '^FO1,140    ^ADN,30,14   ^FD ' . substr($etykieta['kod'], 0, 25) . '^FS'
    //     . '^FO10,190   ^ADN,30,14   ^FD ' . zamianapolskich(substr($etykieta['kod_nazwa'], 0, 25)) . '^FS'
    //     . '^FO80,330^BY2    ^BCN,100,Y,N,N ^A0,60 ^FDDS' . $etykieta['paleta_id'] . '^FS'
    //     . '^FO570,160  ^ADN,30,14   ^FD ' . $etykieta['doc_type'] . ' ' . $etykieta['doc_nr'] . '^FS'
    //     . '^FO470,280  ^ADN,30,14   ^FD Data PP: ' . $etykieta['doc_date'] . '^FS'
    //     . '^FO470,350  ^ADN,30,14   ^FD LOT: ' . $etykieta['lot'] . '^FS'
    //     . ' ^XZ';

    $layout = '^XA'
    . '^FO50,10	^ADN,60,30	^FD ' . $etykieta['skrot'] . '^FS '
    . '^FO470,80	^ADN,30,20	^FDH:' . $etykieta['hala'] . ' ' . $etykieta['adres2'] . ' ^FS '
    . '^FO470,10	^ADN,30,20	^FD ' . $etykieta['doc_type'] . ' ' . $etykieta['doc_nr'] . '^FS '
    . '^FO30,80	^ADN,30,15	^FD ILOSC: ' . $etykieta['ilosc'] . ' ' . $etykieta['jm'] . '^FS'
    . '^FO1,140	^ADN,30,15	^FD ' . substr($etykieta['kod'], 0, 25) . '^FS'
    . '^FO10,190	^ADN,30,14	^FD  ' . zamianapolskich(substr($etykieta['kod_nazwa'], 0, 25)) . '^FS'
    . '^FO510,150 ^ADN,35,14 ^FD STAT: ' . $etykieta['status_nazwa'] . '^FS'
    . '^FO510,190 ^ADN,35,14 ^FD Nr poz: ' . $etykieta['id'] . '^FS'
    . '^FO470,230 ^ADN,35,14 ^FD EAN: ' . $etykieta['ean'] . '^FS'
    . '^FO80,280^BY3	^BCN,100,Y,N,N ^A0,60 ^FDDS' . $etykieta['paleta_id'] . '^FS'
    . '^FO40,242	^ADN,30,14	^FD LOT: ' . $etykieta['lot'] . '^FS'
    . '^FO470,370	^ADN,30,20	^FD';

    if (!(empty($etykieta['data_waznosci']) || $etykieta['data_waznosci'] == "1970-01-01")) {
        $layout .= 'DW: ' . date("ymd", strtotime($etykieta['data_waznosci']));
    }

    $layout .= '^FS'
        
        . ' ^XZ';

    // Drukowanie na drukarce

    //echo str_replace('IP', '', $adres_ip);
    //exit();

    // Drukowanie na drukarce
    $socket = socket_create(AF_INET, SOCK_STREAM, SOL_TCP);
    $result = socket_connect($socket, str_replace('IP', '', $adres_ip), "9100");
    socket_write($socket, $layout, strlen($layout));
    socket_close($socket);
}

// Zwróć potwierdzenie
xml_from_indexed_array(
    array(
        'komunikat' => "OK",
        'miejsce' => $miejsce,
        'adres_ip' => $adres_ip
    )
);


function zamianapolskich($tekst) {
    $tabela = Array(
        //WIN
        "\xb9" => "a", "\xa5" => "A", "\xe6" => "c", "\xc6" => "C",
        "\xea" => "e", "\xca" => "E", "\xb3" => "l", "\xa3" => "L",
        "\xf3" => "o", "\xd3" => "O", "\x9c" => "s", "\x8c" => "S",
        "\x9f" => "z", "\xaf" => "Z", "\xbf" => "z", "\xac" => "Z",
        "\xf1" => "n", "\xd1" => "N",
        //UTF
        "\xc4\x85" => "a", "\xc4\x84" => "A", "\xc4\x87" => "c", "\xc4\x86" => "C",
        "\xc4\x99" => "e", "\xc4\x98" => "E", "\xc5\x82" => "l", "\xc5\x81" => "L",
        "\xc3\xb3" => "o", "\xc3\x93" => "O", "\xc5\x9b" => "s", "\xc5\x9a" => "S",
        "\xc5\xbc" => "z", "\xc5\xbb" => "Z", "\xc5\xba" => "z", "\xc5\xb9" => "Z",
        "\xc5\x84" => "n", "\xc5\x83" => "N",
        //ISO
        "\xb1" => "a", "\xa1" => "A", "\xe6" => "c", "\xc6" => "C",
        "\xea" => "e", "\xca" => "E", "\xb3" => "l", "\xa3" => "L",
        "\xf3" => "o", "\xd3" => "O", "\xb6" => "s", "\xa6" => "S",
        "\xbc" => "z", "\xac" => "Z", "\xbf" => "z", "\xaf" => "Z",
        "\xf1" => "n", "\xd1" => "N");

    return strtr($tekst, $tabela);
}
