﻿using System;
using System.Linq;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using MySql.Data.MySqlClient;
using System.Text.RegularExpressions;
using System.Threading;

namespace Tarczyn__Magazyn
{
    public partial class Operacje : Form
    {
        ActionMenu myParent = null;



        List<string> _kl_wybor = new List<string>();
        List<string> _kl_operacje = new List<string>();
        List<string> _jm = new List<string>();


        //List<string> _kl_operacje_typ = new List<string>();

        int[] _dl = new int[100];
        string[] _kl_operacje_typ = new string[30];
        string[] _kl_operacje_opis = new string[30];
        string[] _kl_operacje_jm = new string[30];
        string[] wymagany_dokument = new string[30];
        string[] wymagana_ilosc = new string[30];

        private Thread Skanowanie;

        TextBox AktualnyTextBox = null;




        int godz, min, sek, ms = 0;
        int ostatni_wpis = 0;

        string operac_id_global = "";

        string system_id_id_local = "";







        //public MySqlConnection ok = null;

        public Operacje(ActionMenu MyParent)
        {
            InitializeComponent();
            this.myParent = MyParent;
            wms_wybrany.Text = Wlasciwosci.system_id;
            imie_nazwisk_label.Text = Wlasciwosci.wozek + ":" + Wlasciwosci.imie_nazwisko;
            //ok = myParent.conn;
            wyszukaj_kl();
            wypelnij_jm();
            wybor_operacji_klienta_funkcja();


            Wlasciwosci.CurrentOperacja = "8";

            button2.Enabled = false;

            textBox1.Enabled = false;
            textBox2.Enabled = false;
            jm_comboBox2.Enabled = false;

            //skanuj();
        }

        private void wyszukaj_kl()
        {
            string zapytanie = "SELECT s.nazwa, if(s.nazwa='prace_wew',1,0) as kolejnosc1,s.kolejnosc  FROM  systemy s left join operacje_inne o on o.system_id=s.wartosc  WHERE ((s.aktywny=1) or s.nazwa='prace_wew') group by s.nazwa order by kolejnosc1 desc,s.kolejnosc  desc,s.nazwa;";
            object obj2 = BazaDanychExternal.Wyczytaj_Tabele(zapytanie);
            DataTable tabela = (DataTable)obj2;

            for (byte k = 0; k < tabela.Rows.Count; k++)
            {
                _kl_wybor.Add(tabela.Rows[k]["nazwa"].ToString());
            }
            if (_kl_wybor.Count == 0)
            {
                MessageBox.Show("Brak klientów ");
            }
            else
            {
                comboBox1.Visible = true;
                BindingSource bs = new BindingSource();
                bs.DataSource = _kl_wybor;
                comboBox1.DataSource = bs;
            }
            //comboBox1.Sel
            comboBox1.SelectedIndex = comboBox1.Items.IndexOf(Wlasciwosci.system_id);
            /*
            if (comboBox1.SelectedIndex != Convert.ToInt32(Wlasciwosci.system_id_id))
            {
                
            }
            else
            {
                wyszukaj_operacje_kl();
            }
             */
            //wyszukaj_operacje_kl();
        }

        private void wyszukaj_operacje_kl()
        {
            _kl_operacje.Clear();
            System.Threading.Thread.Sleep(100);
            Array.Clear(_kl_operacje_typ, 0, _kl_operacje_typ.Length);

            var klient = comboBox1.SelectedItem;
            //MessageBox.Show(" " + klient);

            string zapytanie = "SELECT *,s.wartosc as system_id_id FROM systemy s left join operacje_inne o on o.system_id=s.wartosc WHERE s.aktywny=1 and o.widoczne=1 and o.aktywne=1 and s.nazwa='" + klient + "' order by o.kolejnosc;";
            //MessageBox.Show(" " + zapytanie);
            object obj2 = BazaDanychExternal.Wyczytaj_Tabele(zapytanie);
            DataTable tabela = (DataTable)obj2;

            for (byte k = 0; k < tabela.Rows.Count; k++)
            {
                _kl_operacje.Add(tabela.Rows[k]["opis"].ToString());
                _kl_operacje_typ[k] = tabela.Rows[k]["typ"].ToString();
                _kl_operacje_opis[k] = tabela.Rows[k]["dokument_opis"].ToString();
                _kl_operacje_jm[k] = tabela.Rows[k]["jednostka_domyslna"].ToString();
                wymagany_dokument[k] = tabela.Rows[k]["wymagany_dokument"].ToString();
                wymagana_ilosc[k] = tabela.Rows[k]["wymagana_ilosc"].ToString();
                system_id_id_local = tabela.Rows[k]["system_id_id"].ToString();


            }
            if (_kl_operacje.Count == 0)
            {
                //MessageBox.Show("Brak operacji dla klienta ");
                BindingSource bs = new BindingSource();
                bs.DataSource = _kl_operacje;
                comboBox2.DataSource = bs;
                button1.Visible = false;
                button2.Visible = false;
                textBox1.Visible = false;
                textBox2.Visible = false;
                jm_comboBox2.Visible = false;
                label3.Visible = false;
                label4.Visible = false;
            }
            else
            {

                BindingSource bs = new BindingSource();
                bs.DataSource = _kl_operacje;
                comboBox2.DataSource = bs;
                button1.Visible = true;
                button2.Visible = true;
            }
        }


        private void wypelnij_jm()
        {
            //_inw_status.Add("ET_OK");
            _jm.Add("");
            _jm.Add("PLT");
            _jm.Add("CT");
            //_inw_status.Add("SZT");
            //_inw_status.Add("REND");


            BindingSource bs = new BindingSource();
            bs.DataSource = _jm;
            jm_comboBox2.DataSource = bs;
        }




        private void powrot_Click(object sender, EventArgs e)
        {
            if (timer1.Enabled == true)
            {
                DialogResult result3 = MessageBox.Show("Czy chcesz przerwać operację i nie zapisyać?",
                                    "Czy przerwać?",
                                    MessageBoxButtons.YesNo,
                                    MessageBoxIcon.Question,
                                    MessageBoxDefaultButton.Button2);
                if (result3 == DialogResult.Yes)
                {
                    if (myParent.myParent.myWlan.get_Signal_int() < 2)
                    {
                        MessageBox.Show("Trwa łączenie. Spróbuj ponownie...");
                        return;
                    }

                    if (BazaDanychExternal.SprawdzCzyIstniejePolaczenie() == false)
                    {
                        MessageBox.Show("Brak Wifi. Poczekaj chwilę na ponowne połączenie.");
                        return;
                    }
                    BazaDanychExternal.DokonajUpdate("delete from operacje where id='" + ostatni_wpis + "' and imie_nazwisko='" + Wlasciwosci.imie_nazwisko + "' limit 1;");
                    timer1.Enabled = false;
                    Skaner.Przewij_Skanowanie();
                }
                else
                {
                    return;
                }
            }
            Wlasciwosci.CurrentOperacja = "0";


            this.myParent.Show();
            this.Close();
        }

        private void timer1_Tick(object sender, EventArgs e)
        {
            sek++;
            label5.Text = godz + ":" + min + ":" + sek;
            if (sek > 59)
            {
                min++;
                sek = 0;
            }
            if (min > 59)
            {
                godz++;
                min = 0;
            }

        }


        /*
        private void FERRERO_TworzenieDL_Load(object sender, EventArgs e)
        {
             try
            {
            //fullscreenmode();
            EventHandler MyReadNotifyHandler = new EventHandler(MyReader_ReadNotify);
            EventHandler MyStatusNotifyHandler = new EventHandler(MyReader_StatusNotify);
            this.MyReader.StatusNotify += MyStatusNotifyHandler;
            this.MyReader.ReadNotify += MyReadNotifyHandler;
            }
             catch (Exception ex)
             {
                 MessageBox.Show(ex.Message.ToString());
             }
        }*/

        #region fullscreen
        private void fullscreenmode()
        {
            this.Width = Screen.PrimaryScreen.Bounds.Width;
            this.Height = Screen.PrimaryScreen.Bounds.Height;
            this.Left = 0;
            this.Top = 0;
        }
        #endregion



        private void pokaz_operacje_klienta(object sender, EventArgs e)
        {
            if (myParent.myParent.myWlan.get_Signal_int() < 2)
            {
                MessageBox.Show("Trwa łączenie. Spróbuj ponownie...");
                return;
            }
            if (BazaDanychExternal.SprawdzCzyIstniejePolaczenie() == false)
            {
                MessageBox.Show("Brak Wifi. Poczekaj chwilę na ponowne połączenie.");
                return;
            }
            wyszukaj_operacje_kl();
        }

        private void button1_Click(object sender, EventArgs e)
        {

            if (myParent.myParent.myWlan.get_Signal_int() < 2)
            {
                MessageBox.Show("Trwa łączenie. Spróbuj ponownie...");
                return;
            }

            if (BazaDanychExternal.SprawdzCzyIstniejePolaczenie() == false)
            {
                MessageBox.Show("Brak Wifi. Poczekaj chwilę na ponowne połączenie.");
                return;
            }

            timer1.Enabled = true;
            button1.Enabled = false;
            button2.Enabled = true;
            textBox1.Enabled = true;
            textBox2.Enabled = true;
            jm_comboBox2.Enabled = false;


            comboBox1.Enabled = false;
            comboBox2.Enabled = false;

            //powrot.Enabled = false;




            string zapytanie = "SELECT last FROM docnumber d WHERE d.name='operacja_id';";

            operac_id_global = (string)BazaDanychExternal.Wyczytaj_Jedna_Wartosc(zapytanie);

            string zapyt = "update docnumber d set last=last+1 WHERE d.name='operacja_id'; ";
            BazaDanychExternal.Wyczytaj_Jedna_Wartosc(zapyt);


            //ostatni_wpis = BazaDanychExternal.DokonajInsertLastId("insert into operacje(etykieta_id, doc_type, doc_nr, imie_nazwisko, typ_operacji,system_id,wozek,operac_id,ilosc) values('0','','" + textBox1.Text + "','" + Wlasciwosci.imie_nazwisko + "','" + _kl_operacje_typ[comboBox2.SelectedIndex] + "','" + system_id_id_local + "','" + Wlasciwosci.wozek + "','" + operac_id_global + "','0');");

            BazaDanychExternal.DokonajUpdate("insert into operacje(etykieta_id, doc_type, doc_nr, imie_nazwisko, typ_operacji,system_id,wozek,operac_id,ilosc,jm) values('0','','" + textBox1.Text + "','" + Wlasciwosci.imie_nazwisko + "','" + _kl_operacje_typ[comboBox2.SelectedIndex] + "','" + system_id_id_local + "','" + Wlasciwosci.wozek + "','" + operac_id_global + "','0','" + _jm[jm_comboBox2.SelectedIndex] + "');");
            ostatni_wpis = Convert.ToInt32(BazaDanychExternal.Command.LastInsertedId.ToString());




        }

        private void ZacznijSkanowanie()
        {
            //MessageBox.Show("ZacznijSkanowanie");
            Skaner.Przewij_Skanowanie();
            StringBuilder login = new StringBuilder();
            Skaner.UstawTryb_String(login);
            Skaner.Zacznij_Skanowanie();
            this.Skanowanie = new Thread(() => this.CheckString(login));
            this.Skanowanie.IsBackground = true;
            this.Skanowanie.Start();
        }



        private void CheckString(StringBuilder cc)
        {
            EventHandler method = null; //to jest delegat

            while (true)
            {
                if (cc.ToString() != "")
                {
                    if (method == null)
                    {
                        method = delegate
                        {
                            this.dodawanie(cc.ToString());
                        };
                    }
                    this.Invoke(method);
                    return;
                }
                Thread.Sleep(200);
            }
        }

        private void dodawanie(string ops)
        {
            Skaner.Przewij_Skanowanie();


            if (AktualnyTextBox == null)
            {
                MessageBox.Show("Wybierz wczytanie");
                return;
            }
            //MessageBox.Show("2");
            //AktualnyTextBox.Text = ops;
            if (AktualnyTextBox == textBox2)
            {
                //MessageBox.Show(textBox2.Text);
                if (textBox2.Text == "0" || textBox2.Text == "")
                {
                    textBox2.Text = "1";
                    this.ZacznijSkanowanie();
                }
                else
                {
                    textBox2.Text = "" + (Convert.ToInt32(textBox2.Text) + 1);
                    this.ZacznijSkanowanie();
                }
                //MessageBox.Show(textBox2.Text);
            }

            if (AktualnyTextBox == textBox1)
            {
                if (ops.Substring(0, 2) == "LK")
                {
                    if (myParent.myParent.myWlan.get_Signal_int() < 2)
                    {
                        MessageBox.Show("Trwa łączenie. Spróbuj podejdź do strefy WIFI i kliknij OK");
                        return;
                    }

                    if (BazaDanychExternal.SprawdzCzyIstniejePolaczenie() == false)
                    {
                        MessageBox.Show("Brak Wifi. Poczekaj chwilę na ponowne połączenie.");
                        return;
                    }


                    textBox1.Text = ops.Trim(new Char[] { 'L', 'K' });


                    string zapytanie = "SELECT l.listcontrol_system_id as system_id,s.nazwa FROM list_control l left join systemy s on l.listcontrol_system_id=s.wartosc where l.id=" + ops.Trim(new Char[] { 'L', 'K' }) + " limit 1";
                    //MessageBox.Show(" " + zapytanie);
                    object obj2 = BazaDanychExternal.Wyczytaj_Tabele(zapytanie);
                    DataTable tabela = (DataTable)obj2;

                    if (tabela.Rows.Count > 0)
                    {
                        //tabela.Rows[k]['system_id'];                            

                        if ((string)comboBox1.SelectedItem != tabela.Rows[0]["nazwa"].ToString())
                        {
                            textBox1.Text = "";
                            MessageBox.Show("Dokument " + ops + " należy do systemu:" + tabela.Rows[0]["nazwa"].ToString() + " ");
                            return;
                        }

                    }
                    else
                    {
                        textBox1.Text = "";
                        MessageBox.Show("Nie znaleziono dokumentu w systemie: " + comboBox1.SelectedItem + " ");
                        return;
                    }
                    if (textBox2.Visible == true)
                    {
                        textBox2.Focus();
                    }
                }
                else
                    if (ops.Substring(0, 2) == "DL")
                    {

                        if (myParent.myParent.myWlan.get_Signal_int() < 2)
                        {
                            MessageBox.Show("Trwa łączenie. Spróbuj podejdź do strefy WIFI i kliknij OK");
                            return;
                        }

                        if (BazaDanychExternal.SprawdzCzyIstniejePolaczenie() == false)
                        {
                            MessageBox.Show("Brak Wifi. Poczekaj chwilę na ponowne połączenie.");
                            return;
                        }


                        textBox1.Text = ops.Trim(new Char[] { 'D', 'L' });


                        string zapytanie = "SELECT e.system_id,s.nazwa FROM delivery d  left join delivery_et de on de.delivery_id=d.id left join etykiety e on e.id=de.etykieta_id left join systemy s on e.system_id=s.wartosc where d.id=" + ops.Trim(new Char[] { 'D', 'L' }) + " limit 1";
                        //MessageBox.Show(" " + zapytanie);
                        object obj2 = BazaDanychExternal.Wyczytaj_Tabele(zapytanie);
                        DataTable tabela = (DataTable)obj2;

                        if (tabela.Rows.Count > 0)
                        {
                            //tabela.Rows[k]['system_id'];                            

                            if ((string)comboBox1.SelectedItem != tabela.Rows[0]["nazwa"].ToString())
                            {
                                textBox1.Text = "";
                                MessageBox.Show("Dokument "+ ops + " należy do systemu:" + tabela.Rows[0]["nazwa"].ToString() + " ");
                                return;
                            }

                        }
                        else
                        {
                            textBox1.Text = "";
                            MessageBox.Show("Nie znaleziono dokumentu w systemie: " + comboBox1.SelectedItem + " ");
                            return;
                        }
                        if (textBox2.Visible == true)
                        {
                            textBox2.Focus();
                        }                  


                    }
                    else
                        if (ops.Substring(0, 2) == "NK")
                        {
                            textBox1.Text = ops.Trim(new Char[] { 'N', 'K' });
                            if (textBox2.Visible == true)
                            {
                                textBox2.Focus();
                            }
                        }
                        else
                            if (ops.Substring(0, 2) == "BT")
                            {
                                if (textBox1.Text != "")
                                {
                                    textBox1.Text = textBox1.Text + ", Pobr:" + ops.Trim(new Char[] { 'B', 'T' });
                                }
                                else
                                {
                                    textBox1.Text = "Zdana:" + ops.Trim(new Char[] { 'B', 'T' });
                                }

                                this.ZacznijSkanowanie();

                            }
                            else
                            {
                                textBox1.Text = ops;
                                if (textBox2.Visible == true)
                                {
                                    textBox2.Focus();
                                }
                            }
            }







            //this.ZacznijSkanowanie();
        }

        private void button2_Click(object sender, EventArgs e)
        {

            if (myParent.myParent.myWlan.get_Signal_int() < 2)
            {
                MessageBox.Show("Trwa łączenie. Spróbuj podejdź do strefy WIFI i kliknij OK");
                return;
            }

            if (BazaDanychExternal.SprawdzCzyIstniejePolaczenie() == false)
            {
                MessageBox.Show("Brak Wifi. Poczekaj chwilę na ponowne połączenie.");
                return;
            }
            if (wymagany_dokument[comboBox2.SelectedIndex] == "1")
            {
                if (textBox1.Text == "")
                {
                    MessageBox.Show("Niewypełnione jest: dokument ");
                    textBox1.Focus();
                    return;
                }
            }

            if (wymagana_ilosc[comboBox2.SelectedIndex] == "1")
            {
                if (textBox2.Text == "")
                {
                    MessageBox.Show("Niewypełnione jest: ilosc");
                    textBox2.Focus();
                    return;
                }
                if (!Regex.IsMatch(textBox2.Text, @"\d"))
                {
                    MessageBox.Show("Pole ilość nie jest liczbą.");
                    textBox2.Focus();
                    return;
                }
                if (textBox2.Text == "0")
                {
                    MessageBox.Show("Pole ilość nie może być 0");
                    textBox2.Focus();
                    return;
                }
            }




            timer1.Enabled = false;
            godz = 0;
            min = 0;
            sek = 0;
            ms = 0;
            button1.Enabled = true;
            button2.Enabled = false;

            textBox1.Enabled = false;
            textBox2.Enabled = false;
            jm_comboBox2.Enabled = false;

            comboBox1.Enabled = true;
            comboBox2.Enabled = true;

            powrot.Enabled = true;


            BazaDanychExternal.DokonajUpdate("insert into operacje(etykieta_id, doc_type, doc_nr, imie_nazwisko, typ_operacji,system_id,wozek,operac_id,ilosc,jm) values('0','','" + textBox1.Text + "','" + Wlasciwosci.imie_nazwisko + "','" + _kl_operacje_typ[comboBox2.SelectedIndex] + "','" + system_id_id_local + "','" + Wlasciwosci.wozek + "','" + operac_id_global + "','" + Convert.ToInt32(textBox2.Text) + "','" + _jm[jm_comboBox2.SelectedIndex] + "');");



            BazaDanychExternal.DokonajUpdate("update operacje set doc_nr='" + textBox1.Text + "',jm='" + _jm[jm_comboBox2.SelectedIndex] + "' where id='" + ostatni_wpis + "' limit 1;");

            Skaner.Przewij_Skanowanie();

            textBox1.Text = "";
            textBox2.Text = "";
            //this.ZacznijSkanowanie();

        }
        private void wybor_operacji_klienta_funkcja()
        {
            System.Threading.Thread.Sleep(100);
            label3.Text = _kl_operacje_opis[comboBox2.SelectedIndex];
            jm_comboBox2.SelectedIndex = jm_comboBox2.Items.IndexOf(_kl_operacje_jm[comboBox2.SelectedIndex]);

            if (wymagany_dokument[comboBox2.SelectedIndex] == "1")
            {
                label3.Visible = true;
                textBox1.Visible = true;
            }
            else
            {
                label3.Visible = false;
                textBox1.Visible = false;
            }

            if (wymagana_ilosc[comboBox2.SelectedIndex] == "1")
            {
                textBox2.Text = "";
                label4.Visible = true;
                textBox2.Visible = true;
                jm_comboBox2.Visible = true;

            }
            else
            {
                textBox2.Text = "1";
                label4.Visible = false;
                textBox2.Visible = false;
                jm_comboBox2.Visible = false;
                jm_comboBox2.SelectedIndex = 0;
            }
        }

        private void wybor_operacji_klienta(object sender, EventArgs e)
        {
            this.wybor_operacji_klienta_funkcja();
        }

        private void GotFocus_TextBox(object sender, EventArgs e)
        {
            TextBox Pole_Tekstowe = (TextBox)sender;

            AktualnyTextBox = Pole_Tekstowe;

            ZacznijSkanowanie();
        }

        private void LostFocus_TextBox(object sender, EventArgs e)
        {
            AktualnyTextBox = null;
            Skaner.Przewij_Skanowanie();

        }




    }
}