# Zasady Rozwoju Projektu WMS dla Skanerów

## Architektura
- Windows Forms CE dla skanerów magazynowych
- użycie frameworku Windows CE .net 3.5
- Podział formularzy na plik logiki (.cs) i designer (.cs)
- Modułowa struktura z osobnymi formularzami dla funkcjonalności
- Wspólne komponenty w katalogu `Klasy`

## Konwencje Kodowania
- PascalCase dla nazw klas
- Prefiksy dla powiązanych formularzy (np. `Zad_DL_`)
- Prefiksy dla kontrolek UI (np. `btn`)

## UI/UX
- Dostosowanie do małych ekranów
- Duże kontrolki dla obsługi dotykowej
- Hierarchiczne menu
- Standardowe kontrolki Windows Forms CE

## Technologie
- Symbol SDK dla obsługi skanera
- MySQL dla bazy danych
- Windows CE jako platforma docelowa

## Bezpieczeństwo i Wydajność
- Parametryzowane zapytania SQL
- Optymalizacja użycia zasobów
- Asynchroniczne operacje
- Walidacja danych wejściowych

## Najlepsze Praktyki
- Kompleksowa obsługa błędów
- Dokumentacja XML
- Testowanie na urządzeniach docelowych
- Zachowanie kompatybilności wstecznej

## Wzorce Implementacji (na podstawie DeklaracjaProdukcji.cs)
### Komunikacja z Serwerem
- Wykorzystanie WebService do komunikacji z serwerem PHP
- Obsługa odpowiedzi w formacie XML
- Parsowanie odpowiedzi serwera przez XmlDocument
- Walidacja odpowiedzi serwera przez sprawdzanie node["komunikat"].InnerText

### Obsługa Skanera
- Implementacja w osobnym wątku (Thread)
- Wykorzystanie klasy StringBuilder do buforowania danych
- Asynchroniczne przetwarzanie zeskanowanych kodów
- Bezpieczne zakończenie wątku skanowania

### Interfejs Użytkownika
- Wykorzystanie ComboBox do list wyboru
- Dynamiczne ładowanie danych do kontrolek
- Walidacja pól wejściowych
- Obsługa zdarzeń użytkownika

### Zarządzanie Stanem
- Przechowywanie globalnych identyfikatorów sesji
- Przekazywanie referencji do formularza nadrzędnego
- Zarządzanie cyklem życia formularza
- Obsługa stanu drukarek i urządzeń peryferyjnych

### Bezpieczeństwo
- Escapowanie parametrów URL
- Walidacja danych wejściowych
- Bezpieczne zarządzanie wątkami
- Obsługa błędów i komunikatów

## Implementacja Formularzy Windows Forms (na podstawie KontrolaPaletNew.cs)
### Struktura Formularza
- Dziedziczenie po klasie Form
- Podział na plik główny i designer
- Inicjalizacja w konstruktorze
- Przekazywanie referencji do formularza nadrzędnego

### Obsługa Skanera Kodów Kreskowych
- Dedykowana klasa Skaner do obsługi urządzenia
- Wielowątkowa implementacja skanowania:
  ```csharp
  Thread Skanowanie = null;
  StringBuilder login = new StringBuilder();
  Skaner.UstawTryb_String(login);
  ```
- Bezpieczne zakończenie wątku skanowania
- Obsługa różnych formatów kodów (np. "DS", "PAL-")

### Zarządzanie Danymi
- Użycie StringBuilder dla zmiennych tekstowych
- Tablice i listy do przechowywania danych
- Dynamiczne ładowanie danych do ComboBox
- Parsowanie i walidacja kodów kreskowych

### Komunikacja z Serwerem
- Wykorzystanie klasy WebService
- Obsługa odpowiedzi XML
- Standardowa walidacja odpowiedzi
- Aktualizacja UI na podstawie odpowiedzi

### Obsługa UI
- Automatyczne ustawianie fokusa
- Tryb pełnoekranowy dla urządzeń mobilnych
- Dynamiczne aktualizowanie etykiet
- Obsługa zdarzeń użytkownika

### Obsługa Błędów
- Try-catch dla operacji skanera
- Walidacja odpowiedzi serwera
- Komunikaty dla użytkownika
- Bezpieczne zamykanie zasobów

### Konwencje Kodowania
- Prywatne metody pomocnicze
- Spójne nazewnictwo zmiennych
- Komentarze dla złożonej logiki
- Grupowanie powiązanej funkcjonalności

### Optymalizacja
- Minimalizacja operacji na stringach
- Efektywne zarządzanie pamięcią
- Asynchroniczne operacje skanowania
- Buforowanie danych XML

## Struktura Endpointów PHP
### Format Endpointu (na podstawie palety_kontrola.php)
- Endpoint przyjmuje parametry przez metodę GET
- Standardowa struktura pliku:
  1. Includowanie wymaganych zależności
  2. Konfiguracja połączenia z bazą danych
  3. Pobieranie parametrów z $_GET
  4. Walidacja parametrów
  5. Wykonanie operacji na bazie danych
  6. Zwrot odpowiedzi w formacie XML

### Obsługa Błędów
- Sprawdzanie wymaganych parametrów
- Walidacja danych pracownika
- Obsługa wyjątków SQL
- Standardowy format odpowiedzi błędów

### Format Odpowiedzi
- Zawsze w formacie XML
- Struktura odpowiedzi:
  ```xml
  <dane>
    <komunikat>OK</komunikat>
    <!-- lub -->
    <komunikat>Treść błędu</komunikat>
  </dane>
  ```

### Bezpieczeństwo
- Walidacja parametrów wejściowych
- Sprawdzanie uprawnień pracownika
- Obsługa wyjątków bazodanowych
- Logowanie operacji w bazie danych

### Konwencje Nazewnictwa
- Nazwy plików w formacie snake_case
- Nazwy tabel w formacie snake_case
- Parametry GET w formacie snake_case
- Zmienne PHP w formacie snake_case

## Konfiguracja
- Wykorzystanie settings.xml
- Parametryzacja ustawień środowiska
- Elastyczna konfiguracja
