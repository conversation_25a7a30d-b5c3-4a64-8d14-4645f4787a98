<?php

include_once 'Db.class.php';
include_once 'funkcje.inc';


$db = new Db();

$baza_danych = $_GET['db'];
$akcja = $_GET['akcja'];
$scan = $_GET['scan'];
$operac_id = $_GET['operac_id'];
$imie_nazwisko = $_GET['imie_nazwisko'];




$komunikat = "OK";

if ($akcja == "szukaj") {
    $arr = explode("-", $scan);

    if (substr($scan, 0, 2) == "MP") {


        //echo "22";
        $hala = $arr[1];
        $regal = $arr[2];
        $miejsce = $arr[3];
    }
    $wynik = wyswietl($baza_danych, $hala, $regal, $miejsce, $db);


    if (empty($operac_id)) {
        $operac_id = docnumber_increment($baza_danych, "operacja_id", $db);
    }
    $sql = "insert into operacje(etykieta_id, doc_type, doc_nr, imie_nazwisko, typ_operacji,system_id,wozek,operac_id,ilosc) values('0','','0','" . $imie_nazwisko . "','WOLN_POZ','0', '0','" . $operac_id . "','1');";
    $result8 = $db->mGetResultAsXML($sql);



    return xml_from_indexed_array(array('komunikat' => $komunikat, 'operac_id' => $operac_id, 'wynik' => $wynik));
}

function wyswietl($baza_danych, $hala, $regal, $miejsce, $db) {
    $sql = "
        
SELECT poziom,  regal,miejsce, ifnull((select sum(1) from $baza_danych.etykiety e where m.id=e.miejscep and (active=1 or active is null)),0) as ile FROM $baza_danych.miejsca m where hala='" . $hala . "' and regal='" . $regal . "' and miejsce='" . $miejsce . "' and widoczne=1 group by poziom order by poziom ASC
    ";
    //echo $sql;
    $result = $db->mGetResultAsXML($sql);

    return $result;
}
