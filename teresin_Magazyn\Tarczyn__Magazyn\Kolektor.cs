﻿using System;
using System.Linq;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using MySql.Data.MySqlClient;
using System.Text.RegularExpressions;
using System.Threading;

namespace Tarczyn__Magazyn
{
    public partial class Kolektor : Form
    {
        ActionMenu myParent = null;



        List<string> _dl_wybor = new List<string>();
        int[] _dl = new int[100];


        int nr_dl = 0;
        private Thread Skanowanie;

        //string skaner = "";
        string nr_etykiety = "";
        string nrsap = "";
        //string sap_gora = "";
        //string sap_gora_czesc2 = "";
        //string kod = "";
        string podkod = "";
        //string nrsap_baza = "";




        string operac_id_global = "";



        public MySqlConnection ok = null;

        public Kolektor(ActionMenu MyParent)
        {
            InitializeComponent();
            this.myParent = MyParent;
            wms_wybrany.Text = Wlasciwosci.system_id;
            imie_nazwisk_label.Text = Wlasciwosci.wozek + ";" + Wlasciwosci.imie_nazwisko;
            //ok = myParent.conn;
            wyszukaj_dl();
            textBox1.Visible = false;
            button2.Visible = false;
            button3.Visible = false;
            Wlasciwosci.CurrentOperacja = "14";

            //skanuj();
        }

        private void wyszukaj_dl()
        {
            string zapytanie = "(SELECT ifnull(max(nr)+1,1) as numer FROM sap_kolektor s) union (SELECT nr as numer FROM sap_kolektor s where nr!=0 GROUP BY s.nr ) order by numer desc  limit 30";
            object obj2 = BazaDanychExternal.Wyczytaj_Tabele(zapytanie);
            DataTable tabela = (DataTable)obj2;

            for (byte k = 0; k < tabela.Rows.Count; k++)
            {
                if (k == 0)
                {
                    _dl_wybor.Add("Nowy nr");
                }
                else
                {
                    _dl_wybor.Add(tabela.Rows[k]["numer"].ToString());
                }
                
                _dl[k] = Convert.ToInt32(tabela.Rows[k]["numer"]);
            }
            if (_dl_wybor.Count == 0)
            {
                MessageBox.Show("Brak dokumentów Kolektor ");
            }
            else
            {
                comboBox1.Visible = true;
                BindingSource bs = new BindingSource();
                bs.DataSource = _dl_wybor;
                comboBox1.DataSource = bs;
            }
        }





        private void button1_Click(object sender, EventArgs e)
        {
            powrot.Visible = false;
            comboBox1.Enabled = false;
            textBox1.Visible = true;
            button2.Visible = true;
            button3.Visible = true;

            nr_dl = _dl[comboBox1.SelectedIndex];

            if (nr_dl == 0 || nr_dl.ToString() == "")
            {

                MessageBox.Show("Wybierz Kolektor ");
                return;
            }
            label5.Text = nr_dl.ToString();

            string zapytanie = "SELECT last FROM docnumber d WHERE d.name='operacja_id';";

            operac_id_global = (string)BazaDanychExternal.Wyczytaj_Jedna_Wartosc(zapytanie);

            string zapyt = "update docnumber d set last=last+1 WHERE d.name='operacja_id'; ";
            BazaDanychExternal.Wyczytaj_Jedna_Wartosc(zapyt);

            //BazaDanychExternal.DokonajUpdate("update etykiety set delivery_nr=null where active=1 and delivery_nr=" + nr_dl);
            //ilosc_etykiet.Text = "0";
            ilosc_etykiet.Text = wczytane_licznik(_dl[comboBox1.SelectedIndex].ToString());
            this.button1.Click -= new EventHandler(this.button1_Click);
            this.button1.Click += new EventHandler(this.Zakoncz_Skanowanie);
            this.button1.Text = "Zakończ";
            this.ZacznijSkanowanie();
        }


        private void Zakoncz_Skanowanie(object sender, EventArgs e)
        {
            comboBox1.Enabled = true;
            label5.Text = "";
            powrot.Visible = true;
            textBox1.Visible = false;
            button2.Visible = false;
            button3.Visible = false;
            this.Skanowanie.Abort();
            Skaner.Przewij_Skanowanie();
            this.button1.Click -= new EventHandler(this.Zakoncz_Skanowanie);
            this.button1.Click += new EventHandler(this.button1_Click);

            //MessageBox.Show("Zakończyłem tworzenie dokumentu DL:"+nr_dl);
            //this.myParent.Show();
            //base.Close();
        }

        private void powrot_Click(object sender, EventArgs e)
        {
            Wlasciwosci.CurrentOperacja = "0";
            this.myParent.Show();
            base.Close();
        }

        private void ZacznijSkanowanie()
        {
            StringBuilder login = new StringBuilder();
            Skaner.UstawTryb_String(login);
            Skaner.Zacznij_Skanowanie();
            this.Skanowanie = new Thread(() => this.CheckString(login));
            this.Skanowanie.IsBackground = true;
            this.Skanowanie.Start();
        }

        private void CheckString(StringBuilder cc)
        {
            EventHandler method = null;
            while (true)
            {
                if (cc.ToString() != "")
                {
                    if (method == null)
                    {
                        method = delegate
                        {
                            //this.vvv();
                            this.dodawanie(cc.ToString());
                        };
                        //method = () => this.dodawanie(cc.ToString());
                    }
                    this.Invoke(method);
                    return;
                }
                Thread.Sleep(200);
            }
        }


        private void dodawanie(string ops)
        {

            Skaner.Przewij_Skanowanie();
            //MessageBox.Show("1");

            if (!BazaDanychExternal.SprawdzCzyIstniejePolaczenie())
            {
                MessageBox.Show("Brak połączenia z bazą danych , spróbuj ponownie.");
                //this.ZacznijSkanowanie();
            }
            else
            {
                //MessageBox.Show("2");
                textBox1.Text = ops;
                //MessageBox.Show("3");
                get_dl_items_group(_dl[comboBox1.SelectedIndex].ToString(), textBox1.Text);
                //MessageBox.Show("10");


            }
            ZacznijSkanowanie();
        }


        public void get_dl_items_group(string nr_dl, string etykieta)
        {
            string zapytanie = "";
            zapytanie = "SELECT e.id,e.active,k.kod,e.ilosc,DATE_FORMAT(e.dataprod,'%Y-%m-%d') as dataprod, e.blloc,e.lot,s.nr,ss.nazwa as status_nazwa,e.paleta_id FROM etykiety e left join kody k on e.kod_id=k.id left join sap_kolektor s on e.id=s.nrsap left join status_system ss on e.status_id=ss.id where e.etykieta_klient='" + etykieta + "' order by e.active DESC  limit 1";
            //MessageBox.Show(zapytanie);
            object obj2;
            obj2 = BazaDanychExternal.Wyczytaj_Tabele(zapytanie);
            DataTable tabela = (DataTable)obj2;
            //MessageBox.Show("4");
            int not_insert = 0;
            int ilosc_pozycji_dl = 0;

            if ((tabela).Rows.Count > 0)
            {
                if (tabela.Rows[0]["active"].ToString() == "0")
                {
                    MessageBox.Show("Etykieta " + tabela.Rows[0]["id"].ToString() + " jest nieaktywna ");
                    //textBox1.Text = "";
                    //return;
                }
                else 
                if (tabela.Rows[0]["nr"].ToString() == nr_dl)
                {
                    MessageBox.Show("Etykieta " + tabela.Rows[0]["id"].ToString() + " jest już wczytana na ten kolektor ");
                    textBox1.Text = "";
                    return;
                }
                else
                {
                    //MessageBox.Show("9");
                    //MessageBox.Show("Można dodać");
                    DateTime ts = DateTime.Now;


                    BazaDanychExternal.DokonajUpdate("insert into sap_kolektor(nrsap,kod,ilosc,blloc,dataprod,lot,nr,status,paleta_id) VALUES('" + etykieta + "', '" + tabela.Rows[0]["kod"].ToString() + "', '" + tabela.Rows[0]["ilosc"].ToString() + "', '" + tabela.Rows[0]["blloc"].ToString() + "', '" + tabela.Rows[0]["dataprod"].ToString() + "','" + tabela.Rows[0]["lot"].ToString() + "', '" + nr_dl + "','" + tabela.Rows[0]["status_nazwa"].ToString() + "','" + tabela.Rows[0]["paleta_id"].ToString() + "')");  
                    aktualizuj_etykiete(etykieta, tabela.Rows[0]["kod"].ToString(), tabela.Rows[0]["ilosc"].ToString());
                    ilosc_etykiet.Text =  wczytane_licznik(_dl[comboBox1.SelectedIndex].ToString());
                    textBox1.Text = "";

                    BazaDanychExternal.DokonajUpdate("insert into operacje(etykieta_id, doc_type, doc_nr, imie_nazwisko, typ_operacji,system_id,wozek,operac_id,ilosc) values('" + tabela.Rows[0]["id"].ToString() + "','KL','" + nr_dl + "','" + Wlasciwosci.id_Pracownika + "','KL_ET','" + Wlasciwosci.system_id_id + "','" + Wlasciwosci.wozek + "','" + operac_id_global + "','1');");
                    return;
                }
            }
            else
            {
                MessageBox.Show("Brak w WMS etykiety: " + etykieta);
                //ZacznijSkanowanie();
            }
            textBox1.Text = "";
        }
        private string wczytane_licznik(string nr_dl)
        {
            return (string)BazaDanychExternal.Wyczytaj_Jedna_Wartosc("select count(1) from sap_kolektor s where nr='" + nr_dl + "';");
        }

        private void kasuj_ostatnio_wczytane(string nr_dl)
        {
            string aa = (string)BazaDanychExternal.Wyczytaj_Jedna_Wartosc("select id from sap_kolektor d where nr='" + nr_dl + "' order by id desc limit 1;");
            BazaDanychExternal.DokonajUpdate("delete from sap_kolektor where id='" + aa + "' limit 1;");
        }

        private void aktualizuj_etykiete(string etykieta, string kod, string ilosc)
        {
            //if(nr_etykiety)
            try
            {
                //ok = myParent.conn;
                //MySqlCommand cmdSel = new MySqlCommand("update etykiety set nrsap='" + Convert.ToInt64(nrsap) + "',podkod='" + podkod + "' where id=" + nr_etykiety + " limit 1;", ok);
                //cmdSel.CommandText = "update etykiety set nrsap='" + Convert.ToInt64(nrsap) + "',podkod='" + podkod + "' where id=" + nr_etykiety + " limit 1;";
                //cmdSel.ExecuteNonQuery();
                result_label.Text = "Wczytano :" + etykieta + "\n" +
                    "Kod:" + kod + "   " +
                    "Ilość:" + ilosc;
                //etykieta_text.Text = "";

            }
            catch (MySqlException ex)
            {
                MessageBox.Show(ex.ToString());
            }

            /*
             *            DialogResult result3 = MessageBox.Show("Napewno chcesz zakończyć pracę na deklaracji?",
                "Eksport danych",
                MessageBoxButtons.YesNo,
                MessageBoxIcon.Question,
                MessageBoxDefaultButton.Button2);
            if (result3 == DialogResult.Yes)
            {}
             */

        }

        private void timer1_Tick(object sender, EventArgs e)
        {
            this.Close();
            myParent.Show();
        }

        /*
        private void FERRERO_TworzenieDL_Load(object sender, EventArgs e)
        {
             try
            {
            //fullscreenmode();
            EventHandler MyReadNotifyHandler = new EventHandler(MyReader_ReadNotify);
            EventHandler MyStatusNotifyHandler = new EventHandler(MyReader_StatusNotify);
            this.MyReader.StatusNotify += MyStatusNotifyHandler;
            this.MyReader.ReadNotify += MyReadNotifyHandler;
            }
             catch (Exception ex)
             {
                 MessageBox.Show(ex.Message.ToString());
             }
        }*/

        #region fullscreen
        private void fullscreenmode()
        {
            this.Width = Screen.PrimaryScreen.Bounds.Width;
            this.Height = Screen.PrimaryScreen.Bounds.Height;
            this.Left = 0;
            this.Top = 0;
        }
        #endregion

        private void button3_Click(object sender, EventArgs e)
        {
            kasuj_ostatnio_wczytane(_dl[comboBox1.SelectedIndex].ToString());
            ilosc_etykiet.Text =  wczytane_licznik(_dl[comboBox1.SelectedIndex].ToString());
        }

        private void button2_Click(object sender, EventArgs e)
        {
            dodawanie(textBox1.Text);
            textBox1.Text = "";
        }




    }
}