﻿using System;
using System.Linq;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using System.Threading;
using System.Net;
using System.IO;

using System.Xml.Serialization;


using System.Xml;


namespace Tarczyn__Magazyn
{
    public partial class DeklaracjaProdukcji : Form
    {
        ProdukcjaMenu myParent = null;
        Thread Skanowanie = null;

        List<string> _dp_wybor = new List<string>();
        int[] _dp = new int[100];

        string operac_id_global = "";
        List<string> drukiarki_nazwy_wyswietlane = new List<string>();


        public DeklaracjaProdukcji(ProdukcjaMenu c)
        {
            FullScreenMode.OknoOFF(this);
            InitializeComponent();
            myParent = c;


            pobierz_drukarki();
            wyszukaj_dp();

        }
        private void pobierz_drukarki()
        {

            XmlDocument doc1_etykieta = WebService.Pobierz_XmlDocument("wydruki/drukarki_lista.php");
            XmlNode node_etykieta = WebService.Pobierz_XmlNode(doc1_etykieta, "dane");

            if (node_etykieta["komunikat"].InnerText != "OK")
            {
                MessageBox.Show(node_etykieta["komunikat"].InnerText);
            }
            else
            {
                XmlNodeList xmlnode2 = WebService.Pobierz_XmlNodeList(doc1_etykieta, "drukarki");
                int k = 0;

                foreach (XmlNode wynik in xmlnode2)
                {
                    //MessageBox.Show(""+k);

                    drukiarki_nazwy_wyswietlane.Add(wynik["adres_ip"].InnerText);

                    k++;
                }

                BindingSource bs = new BindingSource();
                bs.DataSource = drukiarki_nazwy_wyswietlane;
                drukarki_comboBox2.DataSource = bs;
            }

        }


        private void wyszukaj_dp()
        {

            XmlDocument doc1_etykieta = WebService.Pobierz_XmlDocument("produkcja/deklaracja_wyrobu_skaner.php?akcja=wyszukaj_dp&system_id="+Wlasciwosci.system_id_id);
            XmlNode node_etykieta = WebService.Pobierz_XmlNode(doc1_etykieta, "dane");

            if (node_etykieta["komunikat"].InnerText != "OK")
            {
                MessageBox.Show(node_etykieta["komunikat"].InnerText);
            }
            else
            {
                XmlNodeList xmlnode2 = WebService.Pobierz_XmlNodeList(doc1_etykieta, "lista_dp");
                int k = 0;

                foreach (XmlNode wynik in xmlnode2)
                {
                    _dp_wybor.Add(wynik["nazwa"].InnerText + " | " + wynik["doc_nr"].InnerText + " | " + wynik["lot"].InnerText + " | " + wynik["status_prism"].InnerText);
                    _dp[k] = Convert.ToInt32(wynik["id"].InnerText);

                    k++;
                }
                comboBox1.Visible = true;
                BindingSource bs = new BindingSource();
                bs.DataSource = _dp_wybor;
                comboBox1.DataSource = bs;
            }

        }




        private void button1_Click(object sender, EventArgs e)
        {

            myParent.Show();
            this.Close();

        }



        private void drukowanie_etykiety()
        {
            if (textBox2.Text == "")
            {
                MessageBox.Show("Nie wpisano etykiety do wydruku");
                return;
            }

            if (drukarki_comboBox2.SelectedValue.ToString() == "")
            {
                MessageBox.Show("Nie wybrano drukarki.");
                return;
            }

            XmlNode node_local2 = KomunikacjaSerwerem("produkcja/deklaracja_wyrobu_skaner.php?akcja=drukowanie_etykiety&etykieta_id=" + textBox2.Text + "&drukarka_ip=" + drukarki_comboBox2.SelectedValue.ToString());
            if (node_local2["komunikat"].InnerText != "OK")
            {
                MessageBox.Show(node_local2["komunikat"].InnerText);
                //MessageBox.Show(@"" + string.Format(@"" + aa, Environment.NewLine));
            }
        }

        private void Deklaruj()
        {
            string data_waznosci = "";
            if (textBox3.Text.Length != 0 && textBox4.Text.Length != 0 && textBox5.Text.Length != 0)
            {
                data_waznosci = textBox3.Text + "-" + textBox4.Text + "-" + textBox5.Text;
            }
            //XmlNode node_local2 = KomunikacjaSerwerem("produkcja/deklaracja_wyrobu_skaner.php?akcja=sprawdzanie_deklaracja&pracownik_id=" + Wlasciwosci.id_Pracownika + "&imie_nazwisko=" + Uri.EscapeUriString(Wlasciwosci.imie_nazwisko) + "&operac_id=" + operac_id_global + "&wozek=" + Wlasciwosci.wozek + "&system_id=" + Wlasciwosci.system_id_id + "&miejsce_id=" + miejsce_id + "&zm_nr=" + zm_nr_global + "&etykieta_scan=" + etykieta + "&id_niezrealizowane=" + id_zmiany_miejsca_niezrealizowane);
            XmlNode node_local2 = KomunikacjaSerwerem("produkcja/deklaracja_wyrobu_skaner.php?akcja=sprawdzanie_deklaracja&docin_id=" + _dp[comboBox1.SelectedIndex].ToString() + "&ilosc_deklarowana=" + textBox1.Text + "&data_waznosci=" + data_waznosci + "&operacja_id=" + operac_id_global + "&pracownik_id=" + Wlasciwosci.id_Pracownika + "&wozek=" + Wlasciwosci.wozek + "&system_id=" + Wlasciwosci.system_id_id + "&drukarka_ip=" + drukarki_comboBox2.SelectedValue.ToString()+"&imie_nazwisko=" + Uri.EscapeUriString(Wlasciwosci.imie_nazwisko));


            if (node_local2["komunikat"].InnerText != "OK")
            {
                MessageBox.Show(node_local2["komunikat"].InnerText);
                //MessageBox.Show(@"" + string.Format(@"" + aa, Environment.NewLine));
            }
            else
            {
                textBox2.Text = node_local2["etykieta"].InnerText;
                operac_id_global = node_local2["operacja_id"].InnerText;
                label7.Text = node_local2["licznik"].InnerText;
            }
        }

        private void dodawanie(string ops)
        {
            textBox1.Text = ops;
            //ZacznijSkanowanie();
        }
        bool isalive = false;


        private void ZacznijSkanowanie()
        {
            StringBuilder login = new StringBuilder();
            Skaner.UstawTryb_String(login);
            Skaner.Zacznij_Skanowanie();
            Skanowanie = new Thread(() => CheckString(login));
            Skanowanie.IsBackground = true;
            Skanowanie.Start();
            isalive = true;
        }

        private void CheckString(StringBuilder cc)
        {
            while (true)
            {
                if (cc.ToString() != "")
                {
                    this.Invoke(new EventHandler(delegate
                    {
                        dodawanie(cc.ToString());
                    }));
                    return;
                }
                Thread.Sleep(200);
            }
        }


        private void Zakoncz_Skanowanie()
        {
            {
                Skanowanie.Abort();
                Skaner.Przewij_Skanowanie();
                isalive = false;
            }
        }





        int trybSkanowania = 0;
        private void textBox1_GotFocus(object sender, EventArgs e)
        {
            trybSkanowania = 0;
            //   inputPanel1.Enabled = true;
            ZacznijSkanowanie();
        }

        private void textBox1_LostFocus(object sender, EventArgs e)
        {
            trybSkanowania = 1;
            Zakoncz_Skanowanie();
            // inputPanel1.Enabled = true;
        }

        private void button2_Click(object sender, EventArgs e)
        {
            if (comboBox1.SelectedValue.ToString() == "")
            {
                MessageBox.Show("Nie wybrano dokumentu.");
                return;
            }

            if (drukarki_comboBox2.SelectedValue.ToString() == "")
            {
                MessageBox.Show("Nie wybrano drukarki.");
                return;
            }

            if (textBox1.Text == "")
            {
                MessageBox.Show("Nie wpisano ilości");
                return;
            }
            Deklaruj();
        }

        public XmlNode KomunikacjaSerwerem(string sciezka)
        {
            XmlNodeList xmlnode_local = null;
            XmlNode node_local = null;
            XmlDocument doc1 = WebService.Pobierz_XmlDocument(sciezka);
            xmlnode_local = doc1.GetElementsByTagName("dane");

            foreach (XmlNode wynik in xmlnode_local)
            {
                node_local = wynik;
            }
            return node_local;
        }

        private void button3_Click(object sender, EventArgs e)
        {

            if (textBox2.Text.Length == 0)
            {
                MessageBox.Show("Wpisz nr etykiety.");
                return;
            }
            drukowanie_etykiety();
        }

        private void DeklaracjaProdukcji_Load(object sender, EventArgs e)
        {

        }

    }
}