# Architektura Systemu WMS

## 1. Przegląd Architektury

System WMS oparty jest na architekturze klient-serwer, która składa się z trzech głównych komponentów:

1.  **Aplik<PERSON><PERSON> (Skaner):** Aplikacja desktopowa działająca na mobilnych terminalach z systemem operacyjnym Windows CE lub Windows Mobile.
2.  **Serwer Aplikacyjny (Backend):** Serwer udostępniający API, z którym komunikuje się aplikacja kliencka.
3.  **Baza Danych:** Centralna baza danych przechowująca wszystkie informacje o operacjach magazynowych.

  *Prosty diagram obrazujący architekturę.*

## 2. Komponenty Systemu

### Aplikacja Kliencka

- **Technologia:** Aplikacja okienkowa napisana w języku C# z wykorzystaniem .NET Compact Framework.
- **Lokalizacja:** `teresin_Ma<PERSON>zyn/Tarczyn__Magazyn`
- **Główne zadania:**
  - Prezentacja interfejsu użytkownika dostosowanego do małych ekranów skanerów.
  - Przechwytywanie danych ze skanera kodów kreskowych.
  - Implementacja logiki biznesowej dla poszczególnych operacji magazynowych.
  - Komunikacja z serwerem aplikacyjnym w celu pobierania i wysyłania danych.

### Serwer Aplikacyjny (Backend)

- **Technologia:** Skrypty PHP.
- **Lokalizacja:** `teresin_Magazyn/pikr`
- **Główne zadania:**
  - Udostępnianie punktów końcowych API (endpointów) dla klienta.
  - Przetwarzanie żądań od klienta (np. walidacja danych, aktualizacja stanów).
  - Komunikacja z bazą danych w celu zapisu i odczytu informacji.
  - Obsługa logiki biznesowej, która musi być scentralizowana.

#### Struktura API

Serwer PHP oferuje API, do którego dostęp realizowany jest przez zapytania HTTP z parametrami GET. Zidentyfikowane endpointy:

| Endpoint | Akcja | Opis |
|---------|-------|------|
| `/api.php` | `dodawanie` | Główny endpoint do obsługi dodawania i zarządzania osobami na liniach produkcyjnych/deklaracjach |

#### Funkcje API dla akcji `dodawanie`

API obsługuje następujące operacje związane z procesem produkcji:

1. **Dodawanie osoby do linii produkcyjnej**
   - Parametry: `typ_operacji` (start/stop), `godzina`, `nr_karty`, `wyrob_id`, `utworzyl`
   - Funkcjonalności:
     - Walidacja parametrów wejściowych
     - Sprawdzenie istnienia osoby na podstawie numeru karty
     - Znajdowanie/tworzenie deklaracji produkcyjnej
     - Obsługa rozpoczęcia pracy osoby przy produkcji
     - Obsługa zakończenia pracy osoby przy produkcji

2. **Obsługa przenoszenia osoby między deklaracjami**
   - Mechanizm wykrywania, gdy osoba jest już przypisana do innej deklaracji
   - System dialogowy z potwierdzeniem przeniesienia (status `confirm`)
   - Automatyczne zamykanie poprzedniej deklaracji przy przeniesieniu

#### Format odpowiedzi

API zwraca odpowiedzi w formacie XML z następującą strukturą:

- Standardowa odpowiedź z komunikatem: `<komunikat>Treść komunikatu</komunikat>`
- Rozszerzona odpowiedź z polami status/message: 
  ```xml
  <dane>
    <status>success|error|confirm</status>
    <message>Treść komunikatu</message>
    <!-- dodatkowe pola zależne od kontekstu -->
  </dane>
  ```

#### Obsługa błędów

API implementuje mechanizm obsługi błędów z trzema typami statusów:
- `error` - błąd uniemożliwiający wykonanie operacji
- `confirm` - potrzebne potwierdzenie użytkownika (np. przy przenoszeniu osoby)
- `success` - operacja zakończona powodzeniem

### Baza Danych

- **Technologia:** MySQL (na podstawie biblioteki `MySql.Data.CF.dll` oraz klasy `Dab.class.php`).
- **Bezpieczeństwo:** Dane dostępowe do bazy danych są przechowywane w pliku konfiguracyjnym `/etc/www_pass/pikr.env`.
- **Obsługa błędów:** System rejestruje błędy zapytań SQL w dedykowanej tabeli `sql_errors`.
- **Dostęp do bazy:** Realizowany z poziomu PHP przez klasę `Dab` z użyciem PDO oraz bezpośrednio z aplikacji klienckiej przez `BazaDanychExternal`.

#### Schemat bazy danych

##### Tabele związane z produkcją

| Tabela | Kluczowe pola | Przeznaczenie |
|--------|--------------|---------------|
| `deklaracje` | `id`, `numer`, `rok`, `data`, `zmiana`, `status` | Przechowuje informacje o deklaracjach produkcyjnych |
| `deklaracja_wyroby` | `id`, `deklaracja_id`, `wyrob_id`, `czas_rozpoczecia`, `czas_zakonczenia` | Relacja między deklaracjami a wyrobami, śledzi czasy rozpoczęcia i zakończenia produkcji |
| `deklaracja_osoby` | `id`, `deklaracja_id`, `osoba_id`, `czas_rozpoczecia`, `czas_zakonczenia` | Rejestruje czas pracy osób przy poszczególnych deklaracjach produkcyjnych |
| `wyroby` | `id`, `nazwa`, `kod`, `ean` | Katalog produktów/wyrobów |

##### Tabele zarządzania personelem

| Tabela | Kluczowe pola | Przeznaczenie |
|--------|--------------|---------------|
| `osoby` | `id`, `imie`, `nazwisko`, `nr_karty` | Dane pracowników z identyfikatorami kart |
| `uzytkownicy` | `id`, `login`, `haslo`, `imie`, `nazwisko` | Konta użytkowników systemu |
| `uprawnienia` | `id`, `uzytkownik_id`, `typ_uprawnienia` | Uprawnienia użytkowników w systemie |

##### Tabele magazynowe

| Tabela | Kluczowe pola | Przeznaczenie |
|--------|--------------|---------------|
| `palety` | `id`, `kod_palety`, `data_utworzenia`, `status` | Informacje o paletach magazynowych |
| `palety_kontrola` | `id`, `paleta_id`, `data_kontroli`, `osoba_id`, `wynik` | Rejestr kontroli palet |
| `lokalizacje` | `id`, `kod`, `sektor`, `regal`, `poziom` | Struktura lokalizacji magazynowych |
| `wozki` | `id`, `kod`, `typ`, `max_poziom`, `status` | Dane wózków mag. z ich parametrami |
| `wozki_historia` | `id`, `wozek_id`, `data`, `motogodziny`, `uwagi` | Historia użytkowania wózków |

##### Tabele systemowe

| Tabela | Kluczowe pola | Przeznaczenie |
|--------|--------------|---------------|
| `setup` | `nazwa`, `wartosc` | Konfiguracja systemu (np. numeracja deklaracji) |
| `sql_errors` | `id`, `zapytanie`, `opis_bledu`, `data` | Rejestr błędów zapytań SQL |
| `log` | `id`, `uzytkownik_id`, `akcja`, `data`, `ip` | Dziennik aktywności użytkowników |

#### Schemat relacji

```
+---------------+       +-------------------+       +------------+
|  deklaracje   |-------| deklaracja_wyroby |-------| wyroby     |
+---------------+       +-------------------+       +------------+
        |                                            
        |                   +-------------------+    +------------+
        +-------------------| deklaracja_osoby  |----| osoby      |
                           +-------------------+    +------------+
                                                          |
                           +-------------------+          |
                           | palety_kontrola   |----------+
                           +-------------------+          |
                                   |                      |
                                   |                      |
                           +-------------------+          |
                           | palety            |          |
                           +-------------------+          |
                                                          |
                           +-------------------+          |
                           | wozki_historia    |----------+
                           +-------------------+          |
                                   |                      |
                                   |                      |
                           +-------------------+          |
                           | wozki             |          |
                           +-------------------+          |
                                                          |
+---------------+       +-------------------+    +------------+
| uzytkownicy   |-------| uprawnienia       |----| log        |
+---------------+       +-------------------+    +------------+
```

#### Logika integralności danych

Z analizy kodu można zidentyfikować następujące mechanizmy zapewnienia integralności danych:

1. **Transakcje bazodanowe** - realizowane przez metodę `StartTransaction()` w klasie `BazaDanychExternal`.
2. **Obsługa błędów SQL** - rejestracja błędów w tabeli `sql_errors` z informacjami diagnostycznymi.
3. **Walidacja danych wejściowych** - realizowana przed wykonaniem zapytań (np. sprawdzanie numeru karty pracownika).
4. **Przygotowane zapytania (prepared statements)** - używane w klasie `Dab` w celu ochrony przed atakami SQL Injection.
5. **Historyzacja danych** - przechowywanie historii zmian dla kluczowych encji (np. `wozki_historia`, `palety_kontrola`).
6. **Statusy** - wykorzystywane do śledzenia stanu obiektów (np. status deklaracji, status palety).

## 3. Komunikacja

- **Protokół:** Komunikacja między klientem a serwerem odbywa się poprzez protokół HTTP/HTTPS.
- **Format Danych:** Klient wysyła żądania do skryptów PHP, przekazując dane w parametrach GET/POST. Serwer odpowiada w formacie XML. Analiza plików `PIKR.cs` oraz `WebService.cs` potwierdza komunikację opartą o XML.

### Szczegółowy opis komunikacji XML

Na podstawie analizy plików `PIKR.cs` (klient) oraz `api.php` (serwer), zidentyfikowano następujący proces wymiany komunikatów:

#### Format zapytań

Zapytania od klienta do serwera są wykonywane poprzez metodę HTTP GET z następującymi parametrami (przykład z modułu PIKR):

```
pikr/api.php?akcja=dodawanie&typ_operacji=$TYP&godzina=$GODZINA&nr_karty=$KARTA&wyrob_id=$ID&utworzyl=$USER&przenies_osobe=$PRZENIES
```

Gdzie:
- `akcja` - nazwa akcji API (np. "dodawanie")
- `typ_operacji` - rodzaj operacji ("start" lub "stop")
- `godzina` - czas w formacie "HH:MM"
- `nr_karty` - identyfikator karty pracownika
- `wyrob_id` - identyfikator wyrobu/produktu
- `utworzyl` - identyfikator użytkownika wykonującego operację
- `przenies_osobe` - parametr kontrolujący potwierdzenie przeniesienia osoby ("tak" lub "NIE")

#### Format odpowiedzi XML

Serwer może zwrócić odpowiedź w jednym z dwóch formatów:

1. **Nowy format** - rozszerzona odpowiedź z polami status i message:

```xml
<dane>
  <status>success|error|confirm</status>
  <message>Treść komunikatu</message>
  <!-- Opcjonalne pola dodatkowe -->
  <deklaracja_id>123</deklaracja_id>
  <!-- Inne pola zależne od kontekstu -->
</dane>
```

2. **Stary format** - prostsza odpowiedź dla zachowania kompatybilności wstecz:

```xml
<dane>
  <komunikat>OK|Treść komunikatu błędu</komunikat>
  <doc_ref>Dodatkowe informacje</doc_ref>
  <!-- Inne pola zależne od kontekstu -->
</dane>
```

#### Obsługa odpowiedzi przez klienta

Klient (`PIKR.cs`) implementuje obsługę obu formatów odpowiedzi:

1. Dla nowego formatu analizuje węzły `status` i `message`, podejmując różne działania w zależności od statusu:
   - `success` - pomyślne wykonanie operacji
   - `error` - wyświetlenie komunikatu błędu
   - `confirm` - wyświetlenie dialogu potwierdzenia dla użytkownika (np. przy przenoszeniu osoby między deklaracjami)

2. Dla starego formatu sprawdza zawartość węzła `komunikat` i obsługuje prostszą logikę sukces/błąd.

#### Aktualne modyfikacje

Niedawno wprowadzono modyfikacje w wymianie komunikatów XML między `PIKR.cs` a `api.php`, które objęły:

1. Poprawa formatowania URL z parametrami
2. Dodanie wymaganego parametru `akcja=dodawanie`
3. Naprawa parametru `utworzyl`
4. Implementacja obsługi nowego formatu odpowiedzi XML
5. Dodanie obsługi trzech różnych statusów: `error`, `confirm` i `success`
6. Implementacja obsługi przenoszenia osoby między deklaracjami

Zmiany te zapewniają lepszą obsługę błędów i interakcję z użytkownikiem, zachowując jednocześnie kompatybilność wstecz dla starszych części systemu.

### Analiza pliku `WebService.cs`

Plik `WebService.cs` zawiera kluczową statyczną klasę odpowiedzialną za komunikację HTTP między aplikacją kliencką a serwerem. Oto główne elementy tej klasy:

1. **Definicja endpointów:**
   ```csharp
   public static string PatchSerwer = "http://172.6.1.249/wmsgg/public/skaner_api/";
   public static string adresSerwer = "172.6.1.249";
   ```
   Wskazuje to na centralny serwer API pod adresem 172.6.1.249, z głównym endpointem dla aplikacji skanerów `/wmsgg/public/skaner_api/`.

2. **Obsługa XML:**
   - `Pobierz_XmlNodeList` - metoda do pobierania listy węzłów XML z dokumentu
   - `Pobierz_XmlNode` - metoda do pobierania pojedynczego węzła XML
   - `TworzXmlDocument` - metoda do tworzenia nowego dokumentu XML (głównie w celu obsługi komunikatów błędów)

3. **Komunikacja HTTP:**
   - `Pobierz_XmlDocument` - główna metoda do komunikacji HTTP, wykorzystuje `HttpWebRequest` do wysyłania zapytań GET i odbierania odpowiedzi w formacie XML
   - `Pobierz_XmlDocument_old` - starsza wersja metody oparta na zapytaniach POST z danymi JSON

4. **Obsługa specyficznych przypadków:**
   - `Pobierz_XmlDocumentWaga` - dedykowana metoda do komunikacji z serwerem wagi, działającym na porcie 8881

5. **Obsługa błędów:**
   Wszystkie metody komunikacyjne zawierają mechanizmy obsługi wyjątków `WebException` i `Exception`, które prezentują komunikaty błędów użytkownikowi i zwracają specjalnie sformatowane dokumenty XML zawierające opis błędu.

Analiza tego pliku potwierdza architekturę klient-serwer, gdzie aplikacja na skanerach komunikuje się z centralnym serwerem API poprzez protokół HTTP, a dane są wymieniane w formacie XML. Dodatkowo zidentyfikowano osobny serwer do obsługi wag magazynowych, co wskazuje na modularność systemu, gdzie poszczególne usługi mogą być udostępniane przez różne zasoby sieciowe.

## 4. Logika Biznesowa Aplikacji Klienckiej

Na podstawie analizy kodu aplikacji klienckiej C# zidentyfikowano następujące kluczowe elementy i funkcjonalności biznesowe:

### Główne moduły funkcjonalne

#### System logowania i autentykacji

Implementowany przez `MainMenu.cs`:
- Logowanie przez skanowanie kart pracowniczych (numery kart powiązane z pracownikami w bazie danych)
- Sprawdzanie uprawnień użytkownika
- Przypisywanie wózka do pracownika (identyfikatory wózków rozpoczynające się od "WK")
- Rejestracja motogodzin wózka przy logowaniu

#### Zarządzanie produkcją (PIKR)

Implementowany przez `PIKR.cs`:
- Rejestracja rozpoczęcia pracy osoby na linii produkcyjnej
- Rejestracja zakończenia pracy
- Przypisywanie osób do deklaracji produkcyjnych
- Przenoszenie osób między deklaracjami z mechanizmem potwierdzenia

#### System etykiet i identyfikacji

Implementowany przez `Etykieta.cs`:
- Obsługa standardu GS1-128 (EAN128) do identyfikacji towarów
- Parsowanie różnych identyfikatorów aplikacji (AI) z kodów kreskowych:
  - 01: Numer jednostki handlowej (GTIN)
  - 10: Numer partii/serii
  - 15/17: Data minimalnej trwałości/data przydatności
  - 21: Numer seryjny
  - 30: Ilość w sztukach
  - Oraz wiele innych identyfikatorów zgodnych z GS1
- Konwersja i walidacja formatów danych (daty, jednostki miary)

#### Aktualizacja etykiet (UpdateEtykietaMenu.cs)

Klasa `UpdateEtykietaMenu` jest jednym z kluczowych komponentów aplikacji klienckiej, odpowiedzialnym za aktualizację i zarządzanie etykietami w systemie WMS. Poniżej przedstawiono szczegółowy opis jej funkcjonalności:

1. **Inicjalizacja i konfiguracja:**
   - Pobranie i ustawienie parametrów konfiguracyjnych specyficznych dla klienta z bazy danych
   - Inicjalizacja dekoderów lotsów, które różnią się w zależności od systemu klienta
   - Generowanie unikatowych identyfikatorów operacji dla śledzenia zmian

2. **Obsługa skanowania kodów GS1-128:**
   - Dekodowanie złożonych kodów GS1-128 na poszczególne elementy (EAN, lot, data produkcji, data ważności)
   - Automatyczne wypełnianie pól formularza na podstawie zeskanowanych danych
   - Obsługa różnych formatów kodów w zależności od dostawcy/producenta
   - Automatyczna ekstrakcja dat produkcji z lotów według konfigurowalnych parametrów

3. **Walidacja danych:**
   - Kontrola poprawności dat (produkcji i ważności)
   - Walidacja formatów numerycznych dla ilości
   - Sprawdzanie wymaganych pól w zależności od typu produktu (lot, data produkcji, data ważności)
   - Automatyczne naprawianie błędnych formatów dat (np. uzupełnianie dni w miesiącu)

4. **Operacje na bazie danych:**
   - Wyszukiwanie kodów produktów w kartotece na podstawie kodów EAN
   - Sprawdzanie duplikatów etykiet
   - Zapisywanie nowych etykiet w systemie z pełnymi danymi (kod, paleta, lot, daty, ilości)
   - Logowanie operacji w systemie dla celów audytu
   - Zarządzanie paletami i przypisywanie do nich etykiet

5. **Interakcja z użytkownikiem:**
   - Interfejs do skanowania i ręcznego wprowadzania danych
   - Obsługa list kontrolnych i awizacji dostaw
   - Walidacja wprowadzonych danych i wyświetlanie komunikatów błędów
   - Możliwość powrotu do poprzednich wartości (funkcja przywracania)
   - Obsługa operacji na paletach (wybór typu palety, usuwanie zawartości)

6. **Mechanizmy biznesowe:**
   - Automatyczne przeliczanie ilości na podstawie liczby opakowań i konfiguracji produktu
   - Obliczanie dat ważności na podstawie dat produkcji i predefiniowanych okresów przydatności
   - Sprawdzanie zgodności z awizacjami dostaw
   - Kontrola limitów ilościowych według awizacji
   - Automatyczne generowanie numerów kolejnych etykiet

7. **Obsługa błędów i wyjątków:**
   - Kontrola połączenia z bazą danych i serwerem
   - Mechanizmy ponawiania operacji w przypadku problemów z połączeniem
   - Logowanie błędów w bazie danych
   - Zabezpieczenia przed nieprawidłowymi danymi wejściowymi

8. **Integracja z innymi modułami:**
   - Współpraca z modułem aktualizacji kodów produktów
   - Integracja z systemem list kontrolnych
   - Komunikacja z głównym menu aplikacji
   - Interakcja z systemem zarządzania paletami

#### Główne Menu Aplikacji (MainMenu.cs)

Klasa `MainMenu` stanowi centralny punkt wejściowy aplikacji klienckiej, odpowiedzialny za inicjalizację systemu, uwierzytelnianie użytkowników i nawigację do poszczególnych funkcjonalności. Poniżej opisano kluczowe elementy tej klasy:

1. **Inicjalizacja systemu:**
   - Konfiguracja pełnoekranowego trybu pracy na urządzeniach mobilnych
   - Inicjalizacja połączenia z bazą danych przy starcie aplikacji
   - Sprawdzanie statusu połączenia bezprzewodowego (WLAN)
   - Mechanizm wykrywania i rejestracji urządzeń (skanerów) w systemie

2. **System aktualizacji aplikacji:**
   - Automatyczne sprawdzanie dostępności nowych wersji aplikacji przy uruchomieniu
   - Porównywanie lokalnej wersji z wersją w bazie danych (`wersjamobile`)
   - Inicjowanie procesu aktualizacji poprzez uruchomienie zewnętrznego programu `UpdatePPGCE.exe`
   - Mechanizm bezpiecznego zamykania aplikacji przed aktualizacją

3. **System uwierzytelniania:**
   - Dwuetapowy proces logowania: skanowanie karty pracownika i identyfikacja wózka
   - Weryfikacja uprawnień użytkownika i pobieranie danych pracownika z bazy
   - Przechowywanie danych sesji użytkownika w klasie `Wlasciwosci`
   - Zabezpieczenia przed próbami logowania przy słabym sygnale WLAN

4. **Zarządzanie sesją wózka widlowego:**
   - Identyfikacja wózka poprzez kod w formacie "WK..." 
   - Rejestracja operacji logowania w tabeli `wozki_historia`
   - Pobieranie i zapisywanie stanu motogodzin przy pierwszym logowaniu danego dnia
   - Monitorowanie stanu technicznego wózka i rejestracja uwag

5. **Nawigacja do funkcjonalności systemu:**
   - Interfejs wyboru bazy danych i systemu klienta
   - Przekierowanie do menu akcji (`ActionMenu`) po udanym logowaniu
   - Bezpośredni dostęp do modułu PIKR (rejestracja pracowników na liniach produkcyjnych)
   - Dostęp do funkcji diagnostycznych (Info_EAN, Info_EAN_Uszk)

6. **System konfiguracji i ustawień:**
   - Zabezpieczony hasłem dostęp do panelu administracyjnego
   - Pobieranie parametrów debugowania z tabeli konfiguracji `setup`
   - Moduł zarządzania urządzeniami (`skanery_lista`)

7. **Mechanizmy bezpieczeństwa:**
   - Weryfikacja połączenia WLAN przed wykonaniem operacji
   - Rejestracja adresów MAC urządzeń w systemie
   - Walidacja poprawności danych pracownika
   - Zabezpieczenia przed błędami połączenia z bazą danych

#### Komunikacja z bazą danych

Implementowana przez `BazaDanychExternal.cs`:
- Wykonywanie zapytań SQL z obsługą błędów i ponownych prób
- Transakcje bazodanowe
- Automatyczne ponowne nawiązywanie połączenia w przypadku jego utraty
- Logowanie błędów zapytań

#### Komunikacja z serwerem API

Implementowana przez `WebService.cs`:
- Mapowanie różnych endpointów API
- Obsługa komunikatów XML
- Zarządzanie błędami połączenia i odpowiedzi
- Dedykowane metody dla różnych systemów (np. serwer wag)

### Przepływy procesów biznesowych

#### Rejestracja pracownika na linii produkcyjnej

1. Użytkownik loguje się do systemu przez skanowanie karty
2. Wybiera operację "PIKR" z menu głównego
3. Skanuje kartę pracownika, którego chce zarejestrować
4. Wybiera typ operacji (start/stop)
5. System wysyła zapytanie do API z parametrami:
   - typ operacji
   - godzina
   - numer karty
   - identyfikator wyrobu
   - osoba rejestrująca
6. API przetwarza zapytanie i dodaje/kończy pracę osoby na deklaracji
7. W przypadku gdy osoba jest już przypisana do innej deklaracji, system wyświetla dialog z pytaniem o przeniesienie

#### Aktualizacja aplikacji

1. Przy uruchomieniu aplikacji system sprawdza dostępność nowej wersji
2. Porównuje lokalną wersję z wersją w bazie danych (`wersjamobile`)
3. Jeśli dostępna jest nowsza wersja, automatycznie uruchamia proces aktualizacji
4. Aplikacja jest zamykana i uruchamiany jest program `UpdatePPGCE.exe`

#### Obsługa wózków

1. Po zalogowaniu użytkownik może zeskanować kod wózka (format "WK...")
2. System sprawdza historię wózka w bazie danych
3. Jeśli jest to pierwsze logowanie danego dnia, system prosi o wprowadzenie stanu motogodzin
4. Dodatkowo można wskazać, czy wózek jest sprawny oraz dodać uwagi
5. Informacje te są rejestrowane w tabeli `wozki_historia`

## 5. Wdrożenie

- **Instalacja:** Projekt zawiera dedykowane instalatory (`Instalator_WMS`, `InstallCAB`), co sugeruje, że aplikacja kliencka jest wdrażana na urządzeniach poprzez pliki instalacyjne CAB, typowe dla platformy Windows Mobile/CE.
