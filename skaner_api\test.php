<?php

include_once 'Db.class.php';
include_once 'funkcje.inc';
error_reporting(E_ALL);
ini_set('display_errors', 1);

$db = new Db();

// // todo do zablokowania statusy
// $naglowek_html = "";
// $temat = "Test mail Gnatowice ";
// $adresaci = array(
//     array('adres' => '<EMAIL>'),
// );
// $content = "Test mail Gnatowice ";
// wyslij_mail_func($adresaci, $temat, $naglowek_html . $content);

$imie_nazwisko = plznaki("<PERSON>");
$akcja = "wyszukiwanie";
$pracownik_id = 1259;
$wysokie = 1;
$kompletacja = 0;
$stanowisko_id = 7;


sprawdzaj_przydzielaj_zadanie_new($imie_nazwisko, $wysokie, $kompletacja, $stanowisko_id, $db);
