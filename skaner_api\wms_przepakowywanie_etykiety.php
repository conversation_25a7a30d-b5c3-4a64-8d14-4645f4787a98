<?php

//include_once 'db_pdo.php';
include_once 'Db.class.php';
include_once 'funkcje.inc';
//error_reporting(E_ALL);
//ini_set('display_errors', 1);

$db = new Db();

if (empty($argv)) {
    //$argv = array();
    $argv[1] = $_GET['db'];
    $argv[2] = $_GET['akcja'];
}

$baza_danych = $_GET['db'];
$akcja = $argv[2];
$system_id_id = $_GET['system_id'];
$imie_nazwisko = $_GET['imie_nazwisko'];
$ilosc_pobierana = $_GET['ilosc_pobierana'];
$etykieta_id = $_GET['etykieta_id'];



$komunikat = "OK";

if ($baza_danych == "wmsgg" || $baza_danych == "wmsftl") {
    $paleta_id = 0;
    if ($akcja == "przepakowanie") {
        //http://25.56.91.22/wmsgg/public/skaner_api/wms_przepakowywanie_etykiety.php?akcja=przepakowanie&db=wmsgg&system_id=6&imie_nazwisko=Lukasz%20Domanski&etykieta_id=9970475&ilosc_pobierana=2

        $numer = docnumber_increment($baza_danych, "PP", $db);
        if (empty($system_id_id))
            $system_id_id = 1;
        $ilosc_etykiety = get_ilosc_etykiety($baza_danych, $etykieta_id, $system_id_id, $db);
        $ilosc_zostawiana = ($ilosc_etykiety + 0) - ($ilosc_pobierana + 0);
        $kontrah_wew_id = get_kontrah_wew($baza_danych, $system_id_id, $db);
        $pracownik_id = get_pracownik($baza_danych, $imie_nazwisko, $db);
        //$numer = "18";
        $docout_id = tworz_dokument_docout("PP-", $numer, $pracownik_id, $kontrah_wew_id, $db);
        $docin_id = tworz_dokument_docin("PP", $numer, $pracownik_id, $kontrah_wew_id, $db);
        $id_oryginalna = przepakowanie_etykiety_step1_oryginalna($etykieta_id, $docout_id, $db);
        $id_pobierana = przepakowanie_etykiety_step2_ilosc_pobierana($etykieta_id, $docin_id, $ilosc_pobierana, $db);
        $id_zostawiana = przepakowanie_etykiety_step3_ilosc_zostawiana($etykieta_id, $docin_id, $ilosc_zostawiana, $db);

        echo $docin_id;
    }
}
?>