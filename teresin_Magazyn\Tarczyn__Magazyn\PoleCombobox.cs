﻿using System;
using System.Linq;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using System.Text.RegularExpressions;

namespace Tarczyn__Magazyn
{
    public partial class PoleCombobox : Form
    {
        public string komunikat = "";
        public string wybierane_id = "";
        public string wybierana_nazwa = "";

        List<string> nazwy_wyswietlane = new List<string>();
        int[] nazwy_id = new int[100];


        public PoleCombobox(string zapytanie, string komunikat, string domyslna_nazwa)
        {
            InitializeComponent();
            label1.Text = komunikat;
            nazwy_wyswietlane.Clear();
            nazwy_wyswietlane.Add("");
            nazwy_id[0] = 0;
            object obj2 = BazaDanychExternal.Wyczytaj_Tabele(zapytanie);
            DataTable tabela = (DataTable)obj2;
            for (byte k = 0; k < tabela.Rows.Count; k++)
            { 
                nazwy_wyswietlane.Add(tabela.Rows[k]["nazwa"].ToString());
                nazwy_id[k + 1] = Convert.ToInt32(tabela.Rows[k]["id"]);
            }
            BindingSource bs = new BindingSource();
            bs.DataSource = nazwy_wyswietlane;
            comboBox1.DataSource = bs;

            //MessageBox.Show(domyslna_nazwa);
            if(domyslna_nazwa!=""){
                comboBox1.SelectedIndex = comboBox1.Items.IndexOf(domyslna_nazwa);
            }
        }

        public PoleCombobox(string zapytanie, string komunikat)
        {
            InitializeComponent();
            label1.Text = komunikat;
            nazwy_wyswietlane.Clear();
            nazwy_wyswietlane.Add("");
            nazwy_id[0] = 0;
            object obj2 = BazaDanychExternal.Wyczytaj_Tabele(zapytanie);
            DataTable tabela = (DataTable)obj2;
            for (byte k = 0; k < tabela.Rows.Count; k++)
            {
                nazwy_wyswietlane.Add(tabela.Rows[k]["nazwa"].ToString());
                nazwy_id[k+1] = Convert.ToInt32(tabela.Rows[k]["id"]);
            }
            BindingSource bs = new BindingSource();
            bs.DataSource = nazwy_wyswietlane;
            comboBox1.DataSource = bs;            
        }

        private void button1_Click(object sender, EventArgs e)
        {
            //this.DialogResult = DialogResult.Cancel;
            //return "przerwij";
        }

        private void Dalej_Click(object sender, EventArgs e)
        {
            
            wybierane_id = nazwy_id[comboBox1.SelectedIndex].ToString();
            wybierana_nazwa = comboBox1.SelectedValue.ToString();
            if (wybierana_nazwa == "")
            {
                MessageBox.Show("Nie dokonano wyboru");
                return;
            }
     
        }

       
    }
}