<?php

//include_once 'db_pdo.php';
include_once 'Db.class.php';
include_once 'funkcje.inc';
//error_reporting(E_ALL);
//ini_set('display_errors', 1);

$db = new Db();

if (empty($argv)) {
//$argv = array();
    $argv[1] = $_GET['db'];
    $argv[2] = $_GET['akcja'];
}

$baza_danych = "wmsgg";
$akcja = $_GET['akcja'];
$pracownik_id = $_GET['pracownik_id'];
$delivery_id = $_GET["delivery_id"];

//http://25.56.91.22/wmsgg/public/skaner_api/delivery_skanowanie_etykiety.php?db=wmsgg&akcja=szukaj&system_id=6&paleta_id=547144


$komunikat = "OK";
if ($akcja == "szykowanie") {


    $sql = 'REPLACE INTO delivery_szykowanie(delivery_id, pracownik_id, czas_zakonczenia)
VALUES (' . $delivery_id . ',' . $pracownik_id . ',NOW());';

    //echo $sql;
//    if (count($result) == 0) {
//        $komunikat = "$sql";
//        return show_komunikat_xml($komunikat);
//    }
    $result = $db->mGetResultAsXML($sql);


    return xml_from_indexed_array(array('komunikat' => $komunikat, 'delivery_id' => $delivery_id));
}
?>