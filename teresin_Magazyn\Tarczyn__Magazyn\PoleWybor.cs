﻿using System;
using System.Linq;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using System.Threading;

namespace Tarczyn__Magazyn
{
    public partial class PoleWybor : Form
    {
        UpdateEtykietaMenu myParent = null;
        InwentaryzacjaMiejsca myParent2 = null;
        GS1_Test myParent3 = null;

        DataTable Tabela = null;

        List<string> wartosci = new List<string>();
        List<string> nazwy = new List<string>();

        public string wartosc_wybrana = "";

        public PoleWybor(UpdateEtykietaMenu MyParent, DataTable Tabela2)
        {
            this.myParent = MyParent;
            this.Tabela = Tabela2;
            
            
            FullScreenMode.OknoOFF(this);
            InitializeComponent();


            for(int i =0;i<Tabela2.Rows.Count;i++ )
            {
                wartosci.Add(Tabela2.Rows[i]["kod"].ToString());
            }

            listBox1.DataSource = wartosci;
            //pokaz_podglad();
            //BazaDanychExternal.Inicjalizacja(Wlasciwosci.Varbaza, Wlasciwosci.Varhost, Wlasciwosci.VarLoginMYSQL, Wlasciwosci.VarHasloMYSQL, this);  
        }
        public PoleWybor(InwentaryzacjaMiejsca MyParent, DataTable Tabela2)
        {
            this.myParent2 = MyParent;
            this.Tabela = Tabela2;


            FullScreenMode.OknoOFF(this);
            InitializeComponent();


            for (int i = 0; i < Tabela2.Rows.Count; i++)
            {
                wartosci.Add(Tabela2.Rows[i]["kod"].ToString());
            }

            listBox1.DataSource = wartosci;
            //pokaz_podglad();
            //BazaDanychExternal.Inicjalizacja(Wlasciwosci.Varbaza, Wlasciwosci.Varhost, Wlasciwosci.VarLoginMYSQL, Wlasciwosci.VarHasloMYSQL, this);  
        }

        public PoleWybor(GS1_Test myParent, DataTable Tabela3)
        {
            this.myParent3 = myParent;
            this.Tabela = Tabela3;


            FullScreenMode.OknoOFF(this);
            InitializeComponent();


            for (int i = 0; i < Tabela3.Rows.Count; i++)
            {
                wartosci.Add(Tabela3.Rows[i]["kod"].ToString());
            }

            listBox1.DataSource = wartosci;
            //pokaz_podglad();
            //BazaDanychExternal.Inicjalizacja(Wlasciwosci.Varbaza, Wlasciwosci.Varhost, Wlasciwosci.VarLoginMYSQL, Wlasciwosci.VarHasloMYSQL, this);  
        }



        Thread Skanowanie = null;


        private void ZacznijSkanowanie()
        {
            StringBuilder login = new StringBuilder();
            Skaner.UstawTryb_String(login);
            Skaner.Zacznij_Skanowanie();
            Skanowanie = new Thread(() => CheckString(login));
            Skanowanie.IsBackground = true;
            Skanowanie.Start();
        }
        private void CheckString(StringBuilder cc)
        {
            while (true)
            {
                if (cc.ToString() != "")
                {
                    this.Invoke(new EventHandler(delegate
                    {

                        dodawanie(cc.ToString());
                    }));
                    return;
                }
                Thread.Sleep(200);
            }
        }
        private void Zakoncz_Skanowanie()
        {
            try
            {
                Skanowanie.Abort();
                Skaner.Przewij_Skanowanie();
            }
            catch
            {
            }
        }

        private void Exit_Click_1(object sender, EventArgs e)
        {
            //Zakoncz_Skanowanie();
            //BazaDanychExternal.ChangeHost(Wlasciwosci.Varbaza, Wlasciwosci.Varhost, Wlasciwosci.VarLoginMYSQL, Wlasciwosci.VarHasloMYSQL);

            if (listBox1.SelectedItem.ToString() !="")
            {
                wartosc_wybrana = listBox1.SelectedItem.ToString();
            }
            else
            {
                MessageBox.Show("Nie dokonano wyboru ");
            }

            if (myParent != null) myParent.Show();
            if (myParent2 != null) myParent2.Show();
            
            this.Close();
        }


        private void button2_Click(object sender, EventArgs e)
        {


        }

        
        private void dodawanie(string gg)
        {

            
            Zakoncz_Skanowanie();
            
        }




        void pokaz_podglad()
        {

            /*
            DataTable Tabela =
            (DataTable)BazaDanychExternal.Wyczytaj_Tabele("SELECT k.kod, cast(TRIM(TRAILING '.' FROM TRIM(TRAILING '0' from sum(e.ilosc))) as char) as ilosc,count(1) as et FROM wmsrawa.etykiety e left join kody k on e.kod_id=k.id WHERE e.listcontrol_id='" + listcontrol_id + "' and e.kod_id is not null group by e.kod_id order by k.kod");
            if (Tabela.Rows.Count == 0)
            {
                MessageBox.Show("Brak wczytanego towaru.");
                return;
            }
             */


            dataGrid1.DataSource = Tabela;
            dataGrid1.TableStyles.Clear();
            DataGridTableStyle tableStyle = new DataGridTableStyle();
            tableStyle.MappingName = Tabela.TableName;

            DataGridTextBoxColumn tbcName = new DataGridTextBoxColumn();
            tbcName.Width = 150;
            tbcName.MappingName = Tabela.Columns[0].ColumnName;
            tbcName.HeaderText = Tabela.Columns[0].ColumnName;
            tableStyle.GridColumnStyles.Add(tbcName);
            DataGridTextBoxColumn tbcName1 = new DataGridTextBoxColumn();
            tbcName1.Width = 50;
            tbcName1.MappingName = Tabela.Columns[1].ColumnName;
            tbcName1.HeaderText = Tabela.Columns[1].ColumnName;
            tableStyle.GridColumnStyles.Add(tbcName1);
            DataGridTextBoxColumn tbcName2 = new DataGridTextBoxColumn();
            tbcName2.Width = 50;
            tbcName2.MappingName = Tabela.Columns[2].ColumnName;
            tbcName2.HeaderText = Tabela.Columns[2].ColumnName;
            tableStyle.GridColumnStyles.Add(tbcName2);




            dataGrid1.TableStyles.Add(tableStyle);


        }

        private void WczytywanieNaLinie_Load_1(object sender, EventArgs e)
        {
            //ZacznijSkanowanie();
        }

        private void wybranie_pozycji(object sender, EventArgs e)
        {
            //DataGrid gd = (DataGrid)sender;
            //DataGrid. 

            //DataRowView row_selected = dataGrid1.DataSource

                //DataRowView dataRow = (DataRowView)dataGrid1.sel;
//int index = dataGrid1.CurrentCell.RowNumber;
//string cellValue = dataRow.Row.ItemArray[index].ToString();

        }

        

  





    }
}