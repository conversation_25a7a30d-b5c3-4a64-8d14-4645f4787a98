# Dokumentacja Procesu Przyjęcia Towarów - System WMS

## 1. Opis Ogólny Systemu

System WMS (Warehouse Management System) służy do zarządzania przyjęciami towarów w magazynie. <PERSON><PERSON><PERSON> przyj<PERSON>ć umożliwia skanowanie etykiet produktów, weryfikację danych względem awizacji oraz rejestrację towarów na paletach.

## 2. Główne Funkcjonalności

### 2.1 Aktualizacja Etykiet (UpdateEtykietaMenu)
Główny formularz do przyjmowania towarów.

#### Funkcje:
- Skanowanie lub ręczne wprowadzanie danych etykiet
- Weryfikacja kodów produktów w kartotece
- Kontrola zgodności z awizacją
- Przypisywanie towarów do palet
- Rejestracja partii (LOT), dat produkcji i ważności

### 2.2 Zarząd<PERSON><PERSON>dam<PERSON> (KodyAktualizacja)
Moduł do zarządzania kartoteką produktów.

#### Funkcje:
- Dodawanie i edycja kodów produktów
- Przypisywanie kodów EAN (jednostki, opakowania, zbiorcze)
- Definiowanie ilości w opakowaniach
- Konfiguracja wymagań (LOT, daty)

### 2.3 Podgląd Przyjęcia (PrzyjeciePodglad)
Widok podsumowania przyjętych towarów.

#### Funkcje:
- Wyświetlanie listy przyjętych produktów
- Grupowanie według palet lub kodów
- Porównanie z awizacją
- Usuwanie błędnych pozycji

## 3. Proces Przyjęcia - Workflow

### Krok 1: Inicjalizacja
1. Operator loguje się do systemu
2. System pobiera ID operatora i numer wózka
3. Generowany jest numer operacji z sekwencji `docnumber.operacja_id`

### Krok 2: Wprowadzenie Listy Kontrolnej
1. Operator skanuje lub wprowadza numer listy kontrolnej (LK)
2. System weryfikuje istnienie LK w tabeli `list_control`
3. Pobierane są dane awizacji jeśli istnieją
4. System ustala ID klienta/systemu

### Krok 3: Określenie Palety
1. Operator skanuje lub wprowadza numer palety (format: DSxxxxxx)
2. Jeśli paleta nie istnieje, jest tworzona w tabeli `palety`
3. Operator wybiera typ palety z listy

### Krok 4: Skanowanie Produktu

#### 4.1 Skanowanie GS1
System obsługuje kody kreskowe GS1-128 z identyfikatorami aplikacji (AI):
- **AI 00**: Numer SSCC
- **AI 02**: GTIN produktu
- **AI 10/21**: Numer partii (LOT)
- **AI 11**: Data produkcji
- **AI 15/17**: Data ważności
- **AI 37/330d/3302**: Ilość opakowań
- **AI 91**: Kod wewnętrzny
- **AI 240/241**: Dodatkowy identyfikator produktu

#### 4.2 Weryfikacja Kodu
1. System szuka kodu w kartotece (`kody`) według:
   - Dokładnego kodu produktu
   - EAN jednostki
   - EAN opakowania
   - EAN opakowania zbiorczego

2. Jeśli znaleziono wiele kodów - wyświetlany jest wybór

3. Jeśli nie znaleziono - możliwość dodania przez `KodyAktualizacja`

### Krok 5: Wprowadzenie Danych Szczegółowych

#### Pola do uzupełnienia:
- **LOT/Partia** - numer partii produkcyjnej
- **Data produkcji** - format RRMMDD
- **Data ważności** - format RRMMDD  
- **Ilość opakowań** - liczba opakowań
- **Ilość sztuk** - całkowita ilość (automatycznie: opakowania × sztuki w opakowaniu)

#### Automatyzacja:
- Data ważności może być wyliczana automatycznie na podstawie daty produkcji i konfiguracji produktu
- LOT może być dekodowany na datę produkcji według konfiguracji w `skaner_lot_dataprod`

### Krok 6: Walidacja Danych

#### Sprawdzenia przed zapisem:
1. **Wymagane pola** - zgodnie z konfiguracją produktu:
   - `wymagana_partia = 1` → LOT jest wymagany
   - `wymagana_data_waznosci = 1` → data ważności wymagana
   - `wymagana_dataprod = 1` → data produkcji wymagana

2. **Zgodność z awizacją**:
   - Czy kod istnieje w awizacji
   - Czy ilość nie przekracza awizowanej

3. **Duplikaty**:
   - Czy etykieta SSC nie była już przyjęta

4. **Kompletność danych**:
   - Ilość > 0
   - Wypełnione wymagane pola

### Krok 7: Zapis Danych

1. **Utworzenie etykiety**:
   ```sql
   INSERT INTO etykiety (
     system_id, kod_id, paleta_id, 
     dataprod, data_waznosci, ilosc,
     lot, blloc, nretykiety, 
     etykieta_klient, listcontrol_id
   ) VALUES (...)
   ```

2. **Rejestracja operacji**:
   ```sql
   INSERT INTO operacje (
     etykieta_id, doc_type, doc_nr,
     imie_nazwisko, typ_operacji,
     system_id, wozek, operac_id
   ) VALUES (...)
   ```

3. **Aktualizacja numeracji**:
   - Zwiększenie licznika etykiet

### Krok 8: Kontynuacja lub Zakończenie

- **Kontynuacja**: Formularz jest czyszczony, zachowane są poprzednie wartości dla szybkiego kopiowania
- **Zakończenie**: Zapis końcowej operacji, powrót do menu głównego

## 4. Specjalne Funkcje

### 4.1 Kopiowanie Poprzednich Wartości
System zapamiętuje ostatnio wprowadzone dane:
- Kod produktu
- LOT
- Data ważności
- Data produkcji
- Ilości

Przyciski szybkiego kopiowania pozwalają na przyspieszenie pracy przy podobnych produktach.

### 4.2 Obsługa Różnych Formatów Dat
- Akceptowane formaty: RRMMDD
- Automatyczna korekta ostatniego dnia miesiąca (00 → rzeczywisty ostatni dzień)
- Walidacja poprawności dat

### 4.3 Dekodowanie LOT
System może automatycznie wyciągać datę produkcji z numeru LOT według konfiguracji:
- Pozycje roku, miesiąca, dnia w stringu LOT
- Opcjonalny prefix

## 5. Kontrola Jakości

### Statusy Produktów
- Domyślny status jakości definiowany na poziomie kodu
- Wyświetlanie ostrzeżenia dla statusów innych niż "OK"

### Śledzenie Operacji
- Każda operacja jest rejestrowana z:
  - Datą i czasem
  - ID operatora
  - Numerem wózka
  - Typem operacji

## 6. Integracje

### Skanery Kodów Kreskowych
- Obsługa skanerów przez klasę `Skaner`
- Tryb ciągłego skanowania
- Parsowanie kodów GS1-128

### Baza Danych
- Połączenie MySQL przez klasę `BazaDanychExternal`
- Transakcje dla zapewnienia spójności
- Obsługa błędów połączenia

## 7. Bezpieczeństwo

### Kontrola Dostępu
- Autoryzacja przez system logowania
- Przypisanie operacji do konkretnego użytkownika

### Walidacja Danych
- Sprawdzanie typów danych
- Ograniczenia ilościowe
- Kontrola duplikatów

## 8. Raporty i Podsumowania

### Dostępne widoki:
1. **Podgląd według palet** - produkty zgrupowane po paletach
2. **Podgląd według kodów** - ilości zgrupowane po produktach
3. **Porównanie z awizacją** - realizacja względem planu

### Liczniki:
- Ilość etykiet na liście kontrolnej
- Ilość przyjętych/do przyjęcia
- Postęp realizacji awizacji
