﻿using System;
using System.Linq;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using System.Threading;
using System.Xml;

namespace Tarczyn__Magazyn
{
    public partial class Delivery_Wymiary : Form
    {
        ActionMenu myParent = null;
        DataTable AktualneSztuki_Global = null;

        public string delivery_id_global = "";
        bool edycja = false;
        string operac_id_global = "";

        List<string> nazwy_wyswietlane = new List<string>();
        string[] dlugosc_tab = new string[50];
        string[] szerokosc_tab = new string[50];
        string[] wysokosc_tab = new string[50];



        //nazwy_id[k + 1] = Convert.ToInt32(wynik["typypalet_id"].InnerText);

        public Delivery_Wymiary(ActionMenu MyParent)
        {
            this.myParent = MyParent;

            FullScreenMode.OknoOFF(this);
            InitializeComponent();
            ZacznijSkanowanie();
            Wlasciwosci.CurrentOperacja = "28";
            //pokaz_podglad();
            //BazaDanychExternal.Inicjalizacja(Wlasciwosci.Varbaza, Wlasciwosci.Varhost, Wlasciwosci.VarLoginMYSQL, Wlasciwosci.VarHasloMYSQL, this);  
            pobranie_wymiarow_zdefiniowanych();


        }

        Thread Skanowanie = null;


        private void ZacznijSkanowanie()
        {
            StringBuilder login = new StringBuilder();
            Skaner.UstawTryb_String(login);
            Skaner.Zacznij_Skanowanie();
            Skanowanie = new Thread(() => CheckString(login));
            Skanowanie.IsBackground = true;
            Skanowanie.Start();
        }
        private void CheckString(StringBuilder cc)
        {
            while (true)
            {
                if (cc.ToString() != "")
                {
                    this.Invoke(new EventHandler(delegate
                    {

                        dodawanie(cc.ToString());
                    }));
                    return;
                }
                Thread.Sleep(200);
            }
        }
        private void Zakoncz_Skanowanie()
        {
            try
            {
                Skanowanie.Abort();
                Skaner.Przewij_Skanowanie();
            }
            catch
            {
            }
        }

        private void Exit_Click_1(object sender, EventArgs e)
        {
            if (delivery_id_global != "")
            {
                XmlDocument doc1_etykieta = WebService.Pobierz_XmlDocument("wymiary_delivery_zamykanie.php?akcja=zamykanie&delivery_id=" + delivery_id_global + "&db=wmsgg");
                XmlNode node_etykieta = WebService.Pobierz_XmlNode(doc1_etykieta, "dane");


                if (node_etykieta["komunikat"].InnerText != "OK")
                {
                    MessageBox.Show(node_etykieta["komunikat"].InnerText);
                }
                else
                {

                }
            }
            Zakoncz_Skanowanie();
            //BazaDanychExternal.ChangeHost(Wlasciwosci.Varbaza, Wlasciwosci.Varhost, Wlasciwosci.VarLoginMYSQL, Wlasciwosci.VarHasloMYSQL);
            myParent.Show();
            this.Close();
        }


        private void button2_Click(object sender, EventArgs e)
        {


        }


        private void dodawanie(string gg)
        {
            Zakoncz_Skanowanie();

            //MessageBox.Show("1");
            if (gg.Substring(0, 2) == "DL")
            {
                //MessageBox.Show("2");
                if (delivery_id_global != "" && gg.Trim(new Char[] { 'D', 'L' }) != delivery_id_global)
                {


                    DialogResult result3 = MessageBox.Show("Czy chcesz rozpocząć nową DL" + gg.Trim(new Char[] { 'D', 'L' }) + "?",
                                        "Czy rozpocząć inną DL?",
                                        MessageBoxButtons.YesNo,
                                        MessageBoxIcon.Question,
                                        MessageBoxDefaultButton.Button2);
                    if (result3 == DialogResult.No)
                    {
                        ZacznijSkanowanie();
                        return;
                    }
                }

                delivery_id_global = gg.Trim(new Char[] { 'D', 'L' });
                //MessageBox.Show("3:" + delivery_id_global);
                label1.Text = "DL " + delivery_id_global;
                refresh_view_table(delivery_id_global);

                wysokosc_textBox1.Text = "";
                szerokosc_textBox1.Text = "";
                dlugosc_textBox1.Text = "";
                waga_textBox1.Text = "";

            }
            else
            {
                XmlDocument doc1_etykieta = WebService.Pobierz_XmlDocument("wymiary_szukaj.php?akcja=szukaj&scan=" + gg + "&system_id=" + Wlasciwosci.system_id_id);
                XmlNode node_etykieta = WebService.Pobierz_XmlNode(doc1_etykieta, "dane");

                if (node_etykieta["komunikat"].InnerText != "OK")
                {
                    MessageBox.Show(node_etykieta["komunikat"].InnerText);
                }
                else
                {



                    comboBox1.SelectedIndex = comboBox1.Items.IndexOf(node_etykieta["wymiar_wyswietlany"].InnerText);
                    //MessageBox.Show(node_etykieta["wysokosc"].InnerText);
                    wysokosc_textBox1.Text = node_etykieta["wysokosc"].InnerText;
                    szerokosc_textBox1.Text = node_etykieta["szerokosc"].InnerText;
                    dlugosc_textBox1.Text = node_etykieta["dlugosc"].InnerText;
                    waga_textBox1.Text = node_etykieta["waga"].InnerText;

                }

            }
            ZacznijSkanowanie();




        }

        void pobranie_wymiarow_zdefiniowanych()
        {
            XmlDocument doc1_etykieta = WebService.Pobierz_XmlDocument("wymiary_zdefiniowane.php?akcja=pobierz");
            XmlNode node_etykieta = WebService.Pobierz_XmlNode(doc1_etykieta, "dane");

            if (node_etykieta["komunikat"].InnerText != "OK")
            {
                MessageBox.Show(node_etykieta["komunikat"].InnerText);
            }
            else
            {
                XmlNodeList xmlnode2 = WebService.Pobierz_XmlNodeList(doc1_etykieta, "wymiary");
                int k = 0;
                //dlugosc_tab[0] = "";
                //szerokosc_tab[0] = "";
                //wysokosc_tab[0] = "";
                //nazwy_wyswietlane.Add("");

                foreach (XmlNode wynik in xmlnode2)
                {
                    //MessageBox.Show(""+k);
                    if (wynik["opis"].InnerText == "")
                    {
                        nazwy_wyswietlane.Add("");
                    }
                    else
                    {
                        nazwy_wyswietlane.Add(wynik["wyswietlana_nazwa"].InnerText);
                    }

                    dlugosc_tab[k] = wynik["dlugosc"].InnerText;
                    szerokosc_tab[k] = wynik["szerokosc"].InnerText;
                    wysokosc_tab[k] = wynik["wysokosc"].InnerText;
                    k++;
                }

                BindingSource bs = new BindingSource();
                bs.DataSource = nazwy_wyswietlane;
                comboBox1.DataSource = bs;
            }

        }

        private static DataTable Konwersja_XmlNodeList_DataTable(XmlNodeList xmlnode2)
        {
            DataTable dt = new DataTable();
            dt.Columns.Add("nosnik", typeof(string));
            dt.Columns.Add("ile", typeof(string));
            dt.Columns.Add("wymiar", typeof(string));
            dt.Columns.Add("mpal", typeof(string));
            dt.Columns.Add("kg", typeof(string));
            foreach (XmlNode wynik in xmlnode2)
            {
                DataRow dtrow = dt.NewRow();
                dtrow["nosnik"] = wynik["nosnik"].InnerText;
                dtrow["ile"] = wynik["ile"].InnerText;
                dtrow["wymiar"] = wynik["wymiar"].InnerText;
                dtrow["mpal"] = wynik["mpal"].InnerText;
                dtrow["kg"] = wynik["kg"].InnerText;
                dt.Rows.Add(dtrow);
            }
            return dt;
        }



        void refresh_view_table(string delivery_id)
        {

            XmlDocument doc1_etykieta = WebService.Pobierz_XmlDocument("wymiary_delivery.php?akcja=wyswietl&delivery_id=" + delivery_id + "&db=wmsgg");
            XmlNode node_etykieta = WebService.Pobierz_XmlNode(doc1_etykieta, "dane");


            if (node_etykieta["komunikat"].InnerText != "OK")
            {
                MessageBox.Show(node_etykieta["komunikat"].InnerText);
            }
            else
            {

                XmlNodeList xmlnode2 = WebService.Pobierz_XmlNodeList(doc1_etykieta, "wymiary_lista");
                DataTable AktualneSztuki = Konwersja_XmlNodeList_DataTable(xmlnode2);
                //MessageBox.Show("2");
                //MessageBox.Show("refresh_view_table");
                dataGrid1.DataSource = AktualneSztuki;
                dataGrid1.TableStyles.Clear();
                DataGridTableStyle tableStyle = new DataGridTableStyle();
                tableStyle.MappingName = AktualneSztuki.TableName;

                DataGridTextBoxColumn tbcName = new DataGridTextBoxColumn();
                tbcName.Width = 70;
                tbcName.MappingName = AktualneSztuki.Columns[0].ColumnName;
                tbcName.HeaderText = AktualneSztuki.Columns[0].ColumnName;
                tableStyle.GridColumnStyles.Add(tbcName);
                DataGridTextBoxColumn tbcName1 = new DataGridTextBoxColumn();
                tbcName1.Width = 25;
                tbcName1.MappingName = AktualneSztuki.Columns[1].ColumnName;
                tbcName1.HeaderText = AktualneSztuki.Columns[1].ColumnName;
                tableStyle.GridColumnStyles.Add(tbcName1);

                DataGridTextBoxColumn tbcName2 = new DataGridTextBoxColumn();
                tbcName2.Width = 80;
                tbcName2.MappingName = AktualneSztuki.Columns[2].ColumnName;
                tbcName2.HeaderText = AktualneSztuki.Columns[2].ColumnName;
                tableStyle.GridColumnStyles.Add(tbcName2);
                dataGrid1.TableStyles.Add(tableStyle);

                DataGridTextBoxColumn tbcName3 = new DataGridTextBoxColumn();
                tbcName3.Width = 35;
                tbcName3.MappingName = AktualneSztuki.Columns[3].ColumnName;
                tbcName3.HeaderText = AktualneSztuki.Columns[3].ColumnName;
                tableStyle.GridColumnStyles.Add(tbcName3);
                dataGrid1.TableStyles.Add(tableStyle);

                DataGridTextBoxColumn tbcName4 = new DataGridTextBoxColumn();
                tbcName4.Width = 40;
                tbcName4.MappingName = AktualneSztuki.Columns[4].ColumnName;
                tbcName4.HeaderText = AktualneSztuki.Columns[4].ColumnName;
                tableStyle.GridColumnStyles.Add(tbcName4);
                dataGrid1.TableStyles.Add(tableStyle);
            }





            //MessageBox.Show("Koniec refresh_view_table");
        }







        private void WczytywanieNaLinie_Load_1(object sender, EventArgs e)
        {
            //ZacznijSkanowanie();
        }

        private void button1_Click(object sender, EventArgs e)
        {
            if (delivery_id_global == "")
            {
                MessageBox.Show("Nie wczytano Nr Delivery");
                return;
            }

            DialogResult result3 = MessageBox.Show("Czy chcesz usunąć ostatni wymiar dla DL " + delivery_id_global + "?",
                                    "Czy usunąć?",
                                    MessageBoxButtons.YesNo,
                                    MessageBoxIcon.Question,
                                    MessageBoxDefaultButton.Button2);
            if (result3 == DialogResult.Yes)
            {
                XmlDocument doc1_etykieta = WebService.Pobierz_XmlDocument("wymiary_delivery_usun.php?akcja=usun_ostatni&delivery_id=" + delivery_id_global + "&db=wmsgg");
                XmlNode node_etykieta = WebService.Pobierz_XmlNode(doc1_etykieta, "dane");


                if (node_etykieta["komunikat"].InnerText != "OK")
                {
                    MessageBox.Show(node_etykieta["komunikat"].InnerText);
                }
                refresh_view_table(delivery_id_global);
            }
        }

        private void button2_Click_1(object sender, EventArgs e)
        {
            //akceptacja_manualna();
            //pokaz_podglad(delivery_id_global);
            //edycja = false;
        }

        private void wybieranie_wymiaru(object sender, EventArgs e)
        {


            if (comboBox1.SelectedValue.ToString() != "")
            {
                dlugosc_textBox1.Text = dlugosc_tab[comboBox1.SelectedIndex].ToString();
                szerokosc_textBox1.Text = szerokosc_tab[comboBox1.SelectedIndex].ToString();
                wysokosc_textBox1.Text = wysokosc_tab[comboBox1.SelectedIndex].ToString();
                //ile_textBox.Focus();
            }
        }

        private void button3_Click(object sender, EventArgs e)
        {
            if (delivery_id_global == "")
            {
                MessageBox.Show("Nie wczytano Nr Delivery");
                return;
            }
            if (ile_textBox.Text == "")
            {
                MessageBox.Show("Podaj ile");
                return;
            }

            if (nazwy_wyswietlane[comboBox1.SelectedIndex].ToString() == "")
            {
                MessageBox.Show("Wybierz nosnik");
                return;
            }


            XmlDocument doc1_etykieta = WebService.Pobierz_XmlDocument("wymiary_dodawanie.php?akcja=dodaj&delivery_id=" + delivery_id_global + "&wysokosc=" + wysokosc_textBox1.Text + "&szerokosc=" + szerokosc_textBox1.Text + "&dlugosc=" + dlugosc_textBox1.Text + "&waga=" + waga_textBox1.Text + "&nazwa_nosnika=" + nazwy_wyswietlane[comboBox1.SelectedIndex].ToString() + "&ile=" + ile_textBox.Text + "&miejsc_paletowych=" + miejsc_pal_textBox1.Text + "&db=wmsgg" + "&pracownik_id=" + Wlasciwosci.id_Pracownika);
            XmlNode node_etykieta = WebService.Pobierz_XmlNode(doc1_etykieta, "dane");


            if (node_etykieta["komunikat"].InnerText != "OK")
            {
                MessageBox.Show(node_etykieta["komunikat"].InnerText);
            }
            else
            {
                refresh_view_table(delivery_id_global);

                wysokosc_textBox1.Text = "";
                szerokosc_textBox1.Text = "";
                dlugosc_textBox1.Text = "";
                waga_textBox1.Text = "";
            }
            //ZacznijSkanowanie();

        }









    }
}