<?php

include_once '../Db.class.php';
include_once '../funkcje.inc';


error_reporting(E_ALL);
ini_set('display_errors', 1);

$baza_danych = "wmsgg";
$db = new Db("localhost", $baza_danych);


if (empty($_GET['delivery_id'])) {
    return xml_from_indexed_array(
        array('komunikat' => "Brak numeru dostawy")
    );
}





// Pobieranie parametrów
$delivery_id = $_GET['delivery_id'];
$paleta_id = $_GET['paleta_id'];
$imie_nazwisko = $_GET['imie_nazwisko'];
$typ_palety_old_id = $_GET['typ_palety_old_id'];
$typ_palety_new_id = $_GET['typ_palety_new_id'];

$typypalet_array = get_typy_palet($db);


$typ_palety_new_nazwa = "";
$typ_palety_old_nazwa = "";
foreach ($typypalet_array as $typypalet) {

    if ($typypalet['id'] == $typ_palety_new_id) {
        $typ_palety_new_nazwa = $typypalet['opis'];
    }
    if ($typypalet['id'] == $typ_palety_old_id) {
        $typ_palety_old_nazwa = $typypalet['opis'];
    }
}




// Sprawdzenie etykiety
$paleta = get_paleta($paleta_id, $db);
if (empty($paleta)) {
    return xml_from_indexed_array(
        array('komunikat' => "Brak palety w bazie. Przerywam operację")
    );
}


// Sprawdzenie pracownika
$pracownik_id = get_pracownik($baza_danych, $imie_nazwisko, $db);
if (empty($pracownik_id)) {
    return xml_from_indexed_array(
        array('komunikat' => "Brak pracownika w bazie. Przerywam operację")
    );
}

// Sprawdzenie wymaganych parametrów
if (empty($delivery_id) || empty($typ_palety_old_id) || empty($typ_palety_new_id)) {
    return xml_from_indexed_array(
        array('komunikat' => "Brak wymaganych parametrów")
    );
}


// Wykonanie zapytania
try {
    // sprawdź czy wpis istnieje w bazie w przeciwnym wypadku zapisz
    $sql_check = "SELECT COUNT(*) as cnt, p.id FROM palety_kontrola p
                  WHERE delivery_id = " . $delivery_id . "
                  AND paleta_id = " . $paleta_id;
    $result = $db->mGetResultAsXML($sql_check);



    if (empty($result[0]['cnt'])) {
        $sql = "INSERT INTO palety_kontrola 
        (delivery_id, paleta_id, data, ts, pracownik_id, typ_palety_old_id, typ_palety_new_id) 
        VALUES 
        (" . $delivery_id . ", 
         " . $paleta_id . ", 
         CURDATE(), 
         NOW(), 
         " . $pracownik_id . ", 
         " . $typ_palety_old_id . ",    
         " . $typ_palety_new_id . ")";
        $db->mGetResultAsXML($sql);
    } else {
        $sql = "UPDATE palety_kontrola 
                SET data = CURDATE(), 
                    ts = NOW(), 
                    pracownik_id = " . $pracownik_id . ", 
                    typ_palety_old_id = " . $typ_palety_old_id . ", 
                    typ_palety_new_id = " . $typ_palety_new_id . " 
                WHERE id = " . $result[0]['id'];
        $db->mGetResultAsXML($sql);
    }





    $licznik = licznik_palet($delivery_id, $db);

    return xml_from_indexed_array(
        array('komunikat' => "OK", 'licznik' => $licznik, 'rezultat' => $typ_palety_old_nazwa . " na " . $typ_palety_new_nazwa)
    );
} catch (Exception $e) {
    return xml_from_indexed_array(
        array('komunikat' => "Błąd podczas zapisu: " . $e->getMessage())
    );
}




function get_paleta($paleta_id, $db)
{
    $sql = "SELECT * FROM etykiety WHERE paleta_id=" . $paleta_id;
    $result = $db->mGetResultAsXML($sql);
    return $result;
}

function get_typy_palet($db)
{
    $query = "SELECT id,opis FROM typypalet;";
    $result = $db->mGetResultAsXML($query);
    return $result;
}






function  licznik_palet($delivery_id, $db)
{
    $sql = "SELECT count(distinct e.paleta_id) as do_skanowania FROM wmsgg.dlcollect dl
            left join etykiety e on e.id=dl.nr_et
            where nr_dl=" . $delivery_id;
    $result = $db->mGetResultAsXML($sql);



    $sql = "SELECT count(distinct paleta_id) as zeskanowane FROM wmsgg.palety_kontrola
            where delivery_id=" . $delivery_id;
    $result2 = $db->mGetResultAsXML($sql);

    $licznik = $result2[0]['zeskanowane'] . "/" . $result[0]['do_skanowania'];

    return $licznik;
}
