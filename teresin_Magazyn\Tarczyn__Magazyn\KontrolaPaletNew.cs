using System;
using System.Linq;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using System.Threading;
using System.Xml;

namespace Tarczyn__Magazyn
{
    public partial class KontrolaPaletNew : Form
    {
        ActionMenu myParent = null;
        XmlDocument typy_palet_xml = null;
        String typpalety_nazwa_poprzedni = "EURO";

        StringBuilder delivery_id = new StringBuilder("0");
        StringBuilder typ_palety_old_id = new StringBuilder("0");
        StringBuilder typ_palety_new_id = new StringBuilder("0");
        StringBuilder paleta_id = new StringBuilder("0");

        int[] nazwy_palet_id = new int[100];
        List<string> nazwy_wyswietlane = new List<string>();


        public KontrolaPaletNew(ActionMenu MyParent)
        {
            this.myParent = MyParent;

            FullScreenMode.OknoOFF(this);
            InitializeComponent();
            typy_palet_xml = InicjalizujTypyPalet();
            inicjalizacja_typy_palet(typy_palet_xml);

            skanowana_paleta.Focus();
            imie_nazwisk_label.Text = Wlasciwosci.imie_nazwisko;

            //ZacznijSkanowanie();
            //Wlasciwosci.CurrentOperacja = "28";
            //pokaz_podglad();
            //BazaDanychExternal.Inicjalizacja(Wlasciwosci.Varbaza, Wlasciwosci.Varhost, Wlasciwosci.VarLoginMYSQL, Wlasciwosci.VarHasloMYSQL, this);  
            //pobranie_wymiarow_zdefiniowanych;


        }

        Thread Skanowanie = null;


        private void ZacznijSkanowanie()
        {
            StringBuilder login = new StringBuilder();
            Skaner.UstawTryb_String(login);
            Skaner.Zacznij_Skanowanie();
            Skanowanie = new Thread(() => CheckString(login));
            Skanowanie.IsBackground = true;
            Skanowanie.Start();
        }

        private void CheckString(StringBuilder cc)
        {
            while (true)
            {
                if (cc.ToString() != "")
                {
                    this.Invoke(new EventHandler(delegate
                    {
                        dodawanie(cc.ToString());
                    }));
                    return;
                }
                Thread.Sleep(200);
            }
        }
        private void Zakoncz_Skanowanie()
        {
            try
            {
                Skanowanie.Abort();
                Skaner.Przewij_Skanowanie();
            }
            catch
            {
            }
        }


        private bool WczytajNaLinie()
        {
            //XmlDocument doc2_etykieta = WebService.Pobierz_XmlDocument("wczytanie_na_linie.php?etykieta_id=" + etykietyDoUzupelnienia[currentIndex - 1].ID+"&pracownik_id="+Wlasciwosci.id_Pracownika);
            //XmlNode node2_etykieta = WebService.Pobierz_XmlNode(doc2_etykieta, "dane");
            //if (node2_etykieta["komunikat"].InnerText != "OK")
            //{
            //    MessageBox.Show(node2_etykieta["komunikat"].InnerText);
            //    return false;
            //} 

            return false;
        }




        private void dodawanie(string gg)
        {
            Zakoncz_Skanowanie();

            if (gg.Substring(0, 2) == "DS")
            {
                string paleta_id_local = gg.Replace("DS", ""); // Usuń prefix 'DS'
                XmlDocument doc = WebService.Pobierz_XmlDocument("palety/palety_skanowanie.php?paleta_id=" + paleta_id_local);
                XmlNode node = WebService.Pobierz_XmlNode(doc, "dane");
                
                if (node != null && node["opis"] != null)
                {
                    if (node["byla_kontrolowana"] != null && node["byla_kontrolowana"].InnerText == "1")
                    {

                      


                        DialogResult result = MessageBox.Show(node["komunikat"].InnerText + "Czy chcesz kontrolować paletę ponownie?", "Uwaga",
                            MessageBoxButtons.YesNo,
                            MessageBoxIcon.Question,
                            MessageBoxDefaultButton.Button2
                        );
                        
                        if (result == DialogResult.No)
                        {
                            skanowana_paleta.Focus();
                            return;
                        }
                    }

                    typ_palety_nazwa.Text = node["opis"].InnerText;                    
                    typ_palety_old_id.Length=0; // Usuwa istniejącą zawartość
                    typ_palety_old_id.Append(node["typypalet_id"].InnerText); // Dodaje nową wartość
                    delivery_id.Length = 0; // Usuwa istniejącą zawartość
                    delivery_id.Append(node["delivery_id"].InnerText);

                    label2.Text = "DL"+delivery_id;
                    scan_label.Text = gg;                    
                    paleta_id.Length = 0;
                    paleta_id.Append(gg.Substring(2));
                    result_label.Text = "";

                    comboBox1.Focus();
                    skanowana_paleta.Focus();
                }
                else
                {
                    MessageBox.Show("Nie znaleziono informacji o palecie");
                }
            }
            else if(gg.Substring(0, 4) == "PAL-")
            {

                if(gg=="PAL-PRZEMYSLOWA")
                {
                    gg = "PAL-PRZEMYSŁOWA";
                }
                //MessageBox.Show(gg.Replace("PAL-", ""));
                //MessageBox.Show("IndexOf:"+comboBox1.Items.IndexOf(gg.Replace("PAL-", "")));

                comboBox1.SelectedIndex = comboBox1.Items.IndexOf(gg.Replace("PAL-", ""));
                
                skanowana_paleta.Focus();
                //MessageBox.Show("typ_palety_new_id:" + typ_palety_new_id);
            }

            //ZacznijSkanowanie();
        }


        public void inicjalizacja_typy_palet(XmlDocument doc1)
        {
            XmlNode node = doc1.SelectSingleNode("//dane");
            
            if (node["komunikat"].InnerText != "OK")
            {
                MessageBox.Show(node["komunikat"].InnerText);
                return;
            }

            XmlNodeList typy_palet = doc1.SelectNodes("//typy_palet");
            
            nazwy_wyswietlane.Clear();
            nazwy_wyswietlane.Add("");
            nazwy_palet_id[0] = 0;
            
            int k = 0;
            foreach (XmlNode paleta in typy_palet)
            {
                nazwy_wyswietlane.Add(paleta["nazwa"].InnerText);
                nazwy_palet_id[k + 1] = Convert.ToInt32(paleta["id"].InnerText);
                k++;
            }


            BindingSource bs = new BindingSource();
            bs.DataSource = nazwy_wyswietlane;
            comboBox1.DataSource = bs;
        }



        private void wyjscie()
        {
            this.Skanowanie.Abort();
            Skaner.Przewij_Skanowanie();
            this.myParent.Show();
            this.Close();
        }
        private void button1_Click(object sender, EventArgs e)
        {
            wyjscie();
        }

        private void button2_Click_1(object sender, EventArgs e)
        {
            //akceptacja_manualna();
            //pokaz_podglad(delivery_id_global);
            //edycja = false;
        }


        private void GotFocus_TextBox(object sender, EventArgs e)
        {
            Skaner.Przewij_Skanowanie();
            //TextBox Pole_Tekstowe = (TextBox)sender;

            //AktualnyTextBox = Pole_Tekstowe;

            ZacznijSkanowanie();
        }

        private void LostFocus_TextBox(object sender, EventArgs e)
        {
            //AktualnyTextBox = null;
            Skaner.Przewij_Skanowanie();

        }

        private void zmien_Click(object sender, EventArgs e)
        {

            PoleComboboxXml  XC = new PoleComboboxXml(typy_palet_xml, "Wybierz paletę", typpalety_nazwa_poprzedni);

            if (XC.ShowDialog() == DialogResult.OK)
            {
                if (XC.wybierana_nazwa == "")
                {
                    MessageBox.Show("Nie dokonano wyboru");
                    return;
                }
                typpalety_nazwa_poprzedni = XC.wybierana_nazwa;

                typ_palety_new_id = new StringBuilder(XC.wybierane_id);

                //MessageBox.Show("XC.wybierana_nazwa: " + XC.wybierana_nazwa);
                //MessageBox.Show("XC.wybierane_id: " + XC.wybierane_id);
                palety_kontrola(delivery_id, paleta_id, new StringBuilder(Wlasciwosci.imie_nazwisko), typ_palety_old_id, typ_palety_new_id);


                
            }
            else
            {
                return;
            }


            
        }



        //private void button6_Click(object sender, EventArgs e)
        //{
        //    etykietyDoUzupelnienia[currentIndex - 1].Status_Prism = textBox2.Text;
        //}


        private XmlDocument PobierzTypyPalet()
        {
            XmlDocument doc = WebService.Pobierz_XmlDocument("palety/palety_pobietranie_typow.php");
            return doc;
        }

        private XmlDocument InicjalizujTypyPalet()
        {
            try
            {
                return PobierzTypyPalet();
            }
            catch (Exception ex)
            {
                // TODO: Dodaj logowanie błędu jeśli system logowania jest dostępny
                
                return null;
            }
        }

        private void palety_kontrola(StringBuilder delivery_id, StringBuilder paleta_id, StringBuilder imie_nazwisko, StringBuilder typ_palety_old_id, StringBuilder typ_palety_new_id)
        {
            try
            {
                XmlDocument doc = WebService.Pobierz_XmlDocument("palety/palety_kontrola.php?delivery_id=" + delivery_id + "&paleta_id=" + paleta_id + "&imie_nazwisko=" + imie_nazwisko + "&typ_palety_old_id=" + typ_palety_old_id + "&typ_palety_new_id=" + typ_palety_new_id);
                XmlNode node = WebService.Pobierz_XmlNode(doc, "dane");

                if (node != null && node["komunikat"] != null)
                {
                    //MessageBox.Show(node["komunikat"].InnerText);
                    if (node["komunikat"].InnerText == "OK")
                    {

                        //MessageBox.Show("typ_palety_old_id:" + typ_palety_old_id);
                        //MessageBox.Show("typ_palety_new_id:" + typ_palety_new_id);

                        if (typ_palety_old_id.ToString() != typ_palety_new_id.ToString())
                        {
                            result_label.Text = "Zmieniono: " + Environment.NewLine + node["rezultat"].InnerText;
                        }
                        else
                        {
                            result_label.Text = "Potwierdzono: " + Environment.NewLine + node["rezultat"].InnerText;
                        }


                        typ_palety_nazwa.Text = "";
                        //typ_palety_old_id.Length = 0;  // lub typ_palety_old_id.Clear();
                        //delivery_id.Length = 0;        // lub delivery_id.Clear();
                        //paleta_id.Length = 0;          // lub paleta_id.Clear();
                        label2.Text = "";

                        scan_label.Text = "";
                        
                    }
                }
                else
                {
                    MessageBox.Show("Błąd podczas komunikacji z serwerem");
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show("Wystąpił błąd: " + ex.Message);
            }
        }
 
        private void zatwierdz_Click(object sender, EventArgs e)
        {
            typ_palety_new_id = new StringBuilder(nazwy_palet_id[comboBox1.SelectedIndex].ToString());

            palety_kontrola(delivery_id, paleta_id, new StringBuilder(Wlasciwosci.imie_nazwisko), typ_palety_old_id, typ_palety_new_id);

            


            //MessageBox.Show("zatwierdz_Click: ");
            
            
            skanowana_paleta.Focus();
        }
    }
}