# Workflow: Proces Przyjęć Magazynowych

## Opis Procesu
Proces przyjęć magazynowych obejmuje przyjmowanie dostaw, we<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>ów, alokację miejsc magazynowych oraz generowanie i drukowanie etykiet magazynowych. Jest to kluczowy proces wejściowy systemu WMS.

## Aktorzy Procesu
- **Magazynier** - osoba wykonująca przyjęcie fizyczne
- **System WMS** - aplikacja mobilna i backend
- **Skaner** - urządzenie Zebra z DataWedge
- **Drukarka etykiet** - drukarka Zebra do etykiet magazynowych

## Mapowanie Systemowe

### Obecny System
- **Backend**: `skaner_api/awizacja_dostawy_ukladanie.php`
- **Frontend - Główny proces przyjęć**: `teresin_Magazyn/Tarczyn__Magazyn/UpdateEtykietaMenu.cs`
- **Frontend - Awizacje**: `teresin_Magazyn/AwizacjaDostawRozkladanie.cs`
- **Funkcje pomocnicze**: `skaner_api/funkcje.inc`
- **Kody produktów**: `KodyAktualizacja.cs` (zarządzanie kodami produktów)

### Nowy System
- **Backend**: `WMS.Application/Receiving/`
- **Frontend**: `WMS.Mobile/Views/Receiving/`
- **API Endpoints**: 
  - `POST /api/v1/receiving/start`
  - `POST /api/v1/receiving/scan-item`
  - `POST /api/v1/receiving/allocate-location`
  - `POST /api/v1/receiving/confirm`
  - `POST /api/v1/receiving/print-label`

## Kroki Procesu

### 1. Inicjacja Przyjęcia
**Input**: 
- Numer dokumentu dostawy (PZ)
- ID użytkownika

**Akcja**:
1. Magazynier wybiera opcję "Przyjęcie towaru" w menu
2. System wyświetla listę oczekujących dostaw
3. Magazynier wybiera dostawę do przyjęcia lub skanuje dokument

**Output**:
- ID sesji przyjęcia
- Lista pozycji do przyjęcia

**Walidacja**:
- Dokument musi istnieć w systemie
- Status dokumentu = "Oczekujący"
- Użytkownik musi mieć uprawnienia do przyjęć

### 2. Skanowanie Towaru
**Input**:
- Kod kreskowy towaru (EAN/GTIN)
- Ilość (ręczna lub ze skanera)

**Akcja**:
1. Magazynier skanuje kod towaru
2. System identyfikuje produkt
3. Magazynier wprowadza ilość (lub potwierdza sugerowaną)
4. System weryfikuje zgodność z dokumentem awizacji dostawy i ostrzega jeśli wartość dodana będzie przekroczona ilość awizowaną. 



**Output**:
- ID pozycji przyjętej
- Status weryfikacji

**Walidacja**:
- Kod musi istnieć w bazie produktów
- Ilość nie może przekroczyć ilości z dokumentu
- Sprawdzenie daty ważności (jeśli wymagane)

### 3. Alokacja Miejsca Magazynowego
**Input**:
- ID produktu
- Ilość
- Parametry produktu (wymiary, waga)

**Akcja**:
1. System sugeruje optymalne miejsce magazynowe
2. Magazynier może zaakceptować lub wybrać inne
3. Magazynier skanuje etykietę miejsca dla potwierdzenia
4. System rezerwuje miejsce

**Output**:
- ID miejsca magazynowego
- Potwierdzenie alokacji

**Walidacja**:
- Miejsce musi być wolne lub mieć wystarczającą pojemność
- Zgodność typu miejsca z typem towaru
- Weryfikacja strefy magazynowej

### 4. Generowanie Etykiety
**Input**:
- ID pozycji przyjętej
- ID miejsca
- Dane produktu

**Akcja**:
1. System generuje unikalny numer etykiety
2. Przygotowanie danych do wydruku:
   - Kod kreskowy etykiety
   - Nazwa produktu
   - Ilość
   - Data przyjęcia
   - Data ważności
   - Lokalizacja
3. Wysłanie do drukarki

**Output**:
- ID etykiety
- PDF/ZPL do wydruku

**Walidacja**:
- Unikalność numeru etykiety
- Kompletność danych

### 5. Naklejenie i Potwierdzenie
**Input**:
- ID etykiety
- Skan potwierdzający

**Akcja**:
1. Magazynier nakleja etykietę na towar
2. Skanuje etykietę dla potwierdzenia
3. System aktualizuje status

**Output**:
- Potwierdzenie przyjęcia pozycji

**Walidacja**:
- Etykieta musi być zeskanowana
- Zgodność z oczekiwaną etykietą

### 6. Finalizacja Przyjęcia
**Input**:
- ID sesji przyjęcia
- Lista przyjętych pozycji

**Akcja**:
1. System sprawdza kompletność przyjęcia
2. Generowanie dokumentu przyjęcia (PZ)
3. Aktualizacja stanów magazynowych
4. Powiadomienie systemów zewnętrznych

**Output**:
- Dokument PZ
- Raport rozbieżności (jeśli występują)

**Walidacja**:
- Wszystkie pozycje muszą być przetworzone
- Suma kontrolna ilości

## Reguły Biznesowe

1. **Zgodność ilościowa**
   - Tolerancja: +/- 5% dla ilości powyżej 100 szt
   - Bezwzględna zgodność dla ilości poniżej 100 szt

2. **Alokacja miejsc**
   - Produkty o krótkim terminie ważności - strefa FIFO
   - Produkty ciężkie - dolne poziomy regałów
   - Produkty szybkorotujące - strefa kompletacji

3. **Etykietowanie**
   - Każda przyjęta partia musi mieć unikalną etykietę
   - Etykieta musi zawierać kod SSCC dla palet
   - Dla produktów spożywczych obowiązkowa data ważności

4. **Kontrola jakości**
   - Losowa kontrola 10% przyjmowanych pozycji
   - 100% kontrola dla produktów wysokiej wartości
   - Dokumentacja fotograficzna uszkodzeń

5. **Uprawnienia**
   - Tylko użytkownicy z rolą "Przyjęcia" mogą inicjować proces
   - Anulowanie przyjęcia wymaga uprawnień kierownika

## Wyjątki i Obsługa Błędów

### Niezgodność ilościowa
**Scenariusz**: Dostarczona ilość różni się od dokumentu
**Obsługa**:
1. System oznacza pozycję jako niezgodną
2. Wymaga potwierdzenia kierownika
3. Generuje protokół rozbieżności
4. Opcja przyjęcia częściowego

### Brak miejsca magazynowego
**Scenariusz**: Brak wolnych miejsc dla towaru
**Obsługa**:
1. Alert do kierownika magazynu
2. Możliwość tymczasowej lokalizacji (strefa buforowa)
3. Priorytetyzacja zwolnienia miejsca

### Uszkodzony towar
**Scenariusz**: Towar jest uszkodzony
**Obsługa**:
1. Dokumentacja fotograficzna
2. Oznaczenie jako "Uszkodzony"
3. Alokacja do strefy reklamacji
4. Generowanie protokołu szkody

### Błąd skanowania
**Scenariusz**: Kod kreskowy nieczytelny
**Obsługa**:
1. Możliwość ręcznego wprowadzenia kodu
2. Weryfikacja wizualna
3. Generowanie nowej etykiety

### Awaria drukarki
**Scenariusz**: Drukarka nie drukuje etykiet
**Obsługa**:
1. Kolejkowanie wydruków
2. Możliwość wydruku na drukarce zapasowej
3. Tymczasowe etykiety ręczne

## Integracje

### System ERP
- Import dokumentów dostaw (PZ)
- Aktualizacja stanów magazynowych
- Synchronizacja danych produktów

### System kurierski
- Powiadomienia o dostawach
- Śledzenie przesyłek
- Dokumenty przewozowe

### Drukarki etykiet
- Protokół: ZPL/EPL
- Komunikacja: TCP/IP lub USB
- Format: 100x50mm lub 100x150mm

## Metryki i KPI

1. **Czas przyjęcia**
   - Cel: <30 min dla standardowej dostawy
   - Mierzony od otwarcia do zamknięcia dokumentu

2. **Dokładność przyjęć**
   - Cel: >99.5% zgodności
   - Liczba błędów / liczba przyjętych pozycji

3. **Wykorzystanie miejsc**
   - Cel: >85% wykorzystania kubatury
   - Optymalizacja alokacji

4. **Czas do dostępności**
   - Cel: <2h od przyjęcia do dostępności w systemie
   - Włączając etykietowanie i alokację

## Wymagania Techniczne

### Skaner
- Obsługa kodów: EAN-13, EAN-128, QR, DataMatrix
- Integracja: DataWedge Intent API
- Tryb batch dla offline

### Drukarka
- Rozdzielczość: min. 203 DPI
- Szerokość etykiety: 100mm
- Protokół: ZPL II

### Sieć
- WiFi 5GHz dla stabilności
- Offline mode z synchronizacją
- Backup 4G/LTE

## Migracja z Obecnego Systemu

### Mapowanie pól
| Stare pole (PHP) | Nowe pole (.NET) | Uwagi |
|------------------|------------------|-------|
| etykieta_id | LabelId | GUID zamiast INT |
| magazyn | WarehouseCode | Enum zamiast string |
| ilosc | Quantity | Decimal z precyzją |
| miejscep | LocationId | Relacja do Location |
| docin_id | ReceiptId | Relacja do Receipt |

### Różnice w logice
1. **Stary system**: Synchroniczne przetwarzanie
   **Nowy system**: Asynchroniczne z kolejkowaniem

2. **Stary system**: Pojedyncze skanowanie
   **Nowy system**: Batch scanning z buforem

3. **Stary system**: Sztywna alokacja miejsc
   **Nowy system**: Dynamiczna optymalizacja z AI

### Dane do migracji
- Historia przyjęć (ostatnie 2 lata)
- Aktywne etykiety
- Mapowanie miejsc magazynowych
- Szablony etykiet

## Testy Akceptacyjne

### Scenariusz 1: Standardowe przyjęcie
1. Zaloguj jako magazynier
2. Wybierz dostawę z listy
3. Zeskanuj 5 różnych produktów
4. Zaakceptuj sugerowane miejsca
5. Wydrukuj etykiety
6. Potwierdź przyjęcie
**Oczekiwany rezultat**: Dokument zamknięty, stany zaktualizowane

### Scenariusz 2: Przyjęcie z rozbieżnościami
1. Zaloguj jako magazynier
2. Wybierz dostawę
3. Wprowadź ilość różną od dokumentu
4. System wymaga potwierdzenia
5. Zaakceptuj rozbieżność
6. Finalizuj z protokołem
**Oczekiwany rezultat**: Protokół rozbieżności wygenerowany

### Scenariusz 3: Przyjęcie offline
1. Wyłącz WiFi na skanerze
2. Rozpocznij przyjęcie
3. Zeskanuj produkty
4. Włącz WiFi
5. Synchronizuj dane
**Oczekiwany rezultat**: Dane zsynchronizowane poprawnie

## Szczegóły Implementacji w UpdateEtykietaMenu.cs

### Architektura Klasy UpdateEtykietaMenu

#### Główne komponenty:
1. **Zmienne globalne stanu**:
   - `operac_id_global` - ID operacji przyjęcia dla całej sesji
   - `listcontrol_id` - ID aktywnej listy kontrolnej (LK)
   - `awizacje_id` - ID powiązanej awizacji dostawy
   - `prev_*` - zapamiętywanie poprzednich wartości dla przyspieszenia pracy
   - `wymagaj_lot`, `wymagaj_dataprod`, `wymagaj_data_waznosci` - flagi wymagalności pól

2. **Zarządzanie numeracją**:
   - Palety numerowane jako "DS" + sekwencyjny numer z tabeli `docnumber`
   - Etykiety numerowane sekwencyjnie z tabeli `docnumber` (pole `nretykiety`)
   - System automatycznego inkrementowania przez funkcję `increment_docnumber()`

3. **Tryby skanowania** (zmienna `TrybSkanu`):
   - 0 = Etykieta
   - 1 = GTIN 
   - 2 = SSC
   - 3 = LOT
   - 4 = DW (Data Ważności)
   - 5 = QT (Ilość)

### Kluczowe funkcjonalności:

#### 1. Parsowanie kodów GS1-128
- **Obsługiwane identyfikatory aplikacyjne (AI)**:
  - `00` - SSCC (numer jednostki logistycznej)
  - `02` - GTIN produktu zawartego
  - `10` - Numer partii/batch
  - `11` - Data produkcji (RRMMDD)
  - `15` - Data minimalnej trwałości
  - `17` - Data ważności (RRMMDD)
  - `21` - Numer seryjny
  - `37` - Liczba jednostek handlowych
  - `91` - Kod wewnętrzny
  - `240` - Dodatkowa identyfikacja produktu
  - `241` - Numer części klienta
  - `330d`, `3302` - Waga netto (kg)

#### 2. Zarządzanie paletami
- Automatyczne generowanie numeru palety przy nowym przyjęciu
- Format: "DS" + numer sekwencyjny (np. DS1234)
- Możliwość kontynuowania na tej samej palecie (checkbox `poprzednia_pal_checkBox1`)
- Tworzenie nowego rekordu w tabeli `palety` gdy paleta nie istnieje

#### 3. Integracja z listami kontrolnymi (LK)
- Wczytanie LK po zeskanowaniu numeru
- Pobranie powiązanej awizacji dostawy
- Automatyczne uzupełnienie `system_id` z listy kontrolnej
- Pobranie lokalizacji domyślnej (`blloc`) z awizacji
- Licznik etykiet przyjętych/oczekiwanych dla danej LK

#### 4. Walidacja danych produktu
- **Weryfikacja w kartotece kodów** (tabela `kody`):
  - Sprawdzanie po kodzie produktu, EAN, EAN jednostki, EAN opakowania zbiorczego
  - Usuwanie wiodących zer przy porównaniu (TRIM LEADING '0')
  - Priorytetyzacja dopasowania: najpierw kod produktu, potem EAN-y
  - Dialog wyboru przy wieloznaczności (`PoleWybor`)

- **Kontrola wymagalności pól**:
  - Partia (LOT) - flaga `wymagana_partia`
  - Data produkcji - flaga `wymagana_dataprod`
  - Data ważności - flaga `wymagana_data_waznosci`
  - Specyficzne wymagania dla systemów (np. system_id=23 wymaga SSC)

#### 5. Automatyczne obliczenia dat
- **Dekodowanie daty produkcji z LOT**:
  - Konfiguracja pozycji w tabeli `skaner_lot_dataprod`
  - Parametry: rok (pozycja_start,długość), miesiąc, dzień
  - Opcjonalny prefix LOT
  
- **Obliczanie daty ważności**:
  - Na podstawie daty produkcji + dni przydatności (z kartoteki kodów)
  - Automatyczna korekta ostatniego dnia miesiąca (funkcja `checkLastDay()`)
  - Format dat: RRMMDD (rok 2-cyfrowy)

#### 6. Kontrola ilości względem awizacji
- Porównanie przyjmowanej ilości z ilością awizowaną
- Dialog potwierdzenia przy rozbieżności
- Możliwość przyjęcia kodu nieujętego w awizacji
- Agregacja ilości na poziomie kodu produktu

#### 7. Sprawdzenie duplikatów etykiet
- Weryfikacja czy etykieta klienta (SSC) nie istnieje już w systemie
- Informacja o palecie, na której znajduje się duplikat
- Możliwość świadomego dodania duplikatu po potwierdzeniu

### Przepływ pracy użytkownika:

1. **Inicjalizacja sesji**:
   - Pobranie i inkrementacja `operac_id` dla sesji
   - Wyświetlenie aktualnego użytkownika i systemu
   - Załadowanie konfiguracji dekodowania LOT dla systemu

2. **Wczytanie listy kontrolnej**:
   - Skanowanie numeru LK
   - Weryfikacja istnienia w bazie
   - Pobranie powiązanych danych (awizacja, lokalizacja)
   - Aktualizacja liczników

3. **Wprowadzenie danych produktu**:
   - **Opcja A - Skanowanie kodu GS1**:
     - Automatyczne parsowanie wszystkich AI
     - Uzupełnienie pól formularza
     - Sprawdzenie w awizacji (jeśli kod nieznany)
   
   - **Opcja B - Ręczne wprowadzanie**:
     - Kod produktu → weryfikacja w kartotece
     - LOT → dekodowanie daty produkcji
     - Daty → walidacja formatu i logiki
     - Ilość → przeliczenie sztuk z opakowań

4. **Alokacja na paletę**:
   - Wybór nowej palety lub kontynuacja obecnej
   - Skanowanie/wprowadzenie numeru DS
   - Utworzenie rekordu palety w bazie

5. **Zapis etykiety**:
   - Walidacja kompletności danych
   - Sprawdzenie wymagalności pól
   - Kontrola ilości względem awizacji
   - Generowanie numeru etykiety
   - Zapis do tabeli `etykiety`
   - Logowanie operacji w tabeli `operacje`

6. **Przygotowanie do kolejnej etykiety**:
   - Zapamiętanie użytecznych danych (prev_*)
   - Wyczyszczenie pól formularza
   - Przejście fokusa na pierwszy input

### Integracje i zależności:

#### Klasy pomocnicze:
- **`BazaDanychExternal`** - warstwa dostępu do bazy MySQL
  - `Wyczytaj_Jedna_Wartosc()` - pojedyncza wartość
  - `Wyczytaj_Tabele()` - DataTable
  - `DokonajUpdate()` - DML operations
  - `SprawdzCzyIstniejePolaczenie()` - kontrola połączenia

- **`Etykieta`** - parser kodów kreskowych GS1
  - `Parse()` - parsowanie stringa na Dictionary<AII, string>
  - `Inicjalizacja()` - setup parsera

- **`PoleWybor`** - dialog wyboru przy wieloznaczności
  - Wyświetla DataTable z opcjami
  - Zwraca wybraną wartość w `wartosc_wybrana`

- **`KodyAktualizacja`** - formularz zarządzania kartoteką kodów
  - Dodawanie nowych kodów
  - Mapowanie EAN na kod wewnętrzny

- **`Skaner`** - integracja ze skanerem
  - `UstawTryb_String()` - tryb tekstowy
  - `Zacznij_Skanowanie()` - start
  - `Przewij_Skanowanie()` - stop/restart

#### Tabele bazodanowe:
- **`etykiety`** - główna tabela przyjęć
- **`kody`** - kartoteka produktów
- **`list_control`** - listy kontrolne
- **`listcontrol_palety`** - powiązanie LK z paletami
- **`awizacje_dostaw_head`** - nagłówki awizacji
- **`awizacje_dostaw_dane`** - pozycje awizacji
- **`palety`** - rejestr palet
- **`operacje`** - log operacji
- **`docnumber`** - sekwencje numerów
- **`skaner_lot_dataprod`** - konfiguracja dekodowania LOT

### Specyficzne zachowania dla systemów:

1. **System 9 (prawdopodobnie spożywczy)**:
   - Wymaga uzupełnienia LOT
   - Wymaga daty ważności
   - Dekodowanie dat z numeru partii

2. **System 23**:
   - Wymaga numeru SSC (SSCC)
   - Prawdopodobnie dla palet standardowych

3. **System 17, 19**:
   - Specjalne traktowanie w menu (PZ LKP)
   - Różne formularze przyjęć

## Uwagi Implementacyjne dla Migracji

### Wydajność i optymalizacje:
1. **Cache poprzednich wartości**: System zapamiętuje ostatnie wartości (prev_*) dla przyspieszenia kolejnych skanowań
2. **Priorytety w zapytaniach SQL**: Użycie UNION ALL z sortowaniem dla optymalnego dopasowania kodów
3. **Batch scanning**: Możliwość ciągłego skanowania bez przerywania flow
4. **Lazy loading**: Dane awizacji ładowane tylko gdy potrzebne

### Bezpieczeństwo i audyt:
1. **Logowanie operacji**: Każda operacja zapisywana w tabeli `operacje` z pełnym kontekstem
2. **Kontrola uprawnień**: Weryfikacja użytkownika na poziomie akcji
3. **Walidacja danych**: Wielopoziomowa walidacja przed zapisem
4. **Transakcyjność**: Obsługa rollback przy błędach

### UX i ergonomia:
1. **Smart focus management**: Automatyczne przenoszenie fokusa po wypełnieniu pola
2. **Kontekstowe podpowiedzi**: Różne komunikaty w zależności od system_id
3. **Obsługa błędów skanowania**: Możliwość ręcznego wprowadzenia przy nieczytelnym kodzie
4. **Minimalizacja interakcji**: Auto-uzupełnianie pól na podstawie konfiguracji produktu

### Integracje systemowe:
1. **DataWedge dla Zebra**: Tryb Intent z broadcast receiver
2. **Multi-threading dla skanowania**: Osobny wątek nasłuchujący na skany
3. **Real-time validation**: Natychmiastowa weryfikacja w bazie po skanowaniu
4. **Kolejkowanie operacji**: Buforowanie przy braku połączenia

### Różnice do uwzględnienia w nowej architekturze:

#### Backend (.NET Core):
1. **Asynchroniczność**: Wszystkie operacje I/O jako async/await
2. **CQRS**: Rozdzielenie komend (zapis) od zapytań (odczyt)
3. **Domain Events**: Publikowanie zdarzeń przy zapisie etykiety
4. **Repository Pattern**: Abstrakcja dostępu do danych
5. **Unit of Work**: Transakcje obejmujące wiele tabel

#### Frontend (MAUI):
1. **MVVM**: Separacja logiki od widoku
2. **Data Binding**: Dwukierunkowe wiązanie danych
3. **Dependency Injection**: Wstrzykiwanie serwisów
4. **Observable Collections**: Reaktywne aktualizacje UI
5. **Platform-specific**: Kod specyficzny dla Android (DataWedge)

### Mapowanie komponentów:

| Stary komponent | Nowy komponent | Uwagi |
|-----------------|----------------|-------|
| UpdateEtykietaMenu.cs | ReceivingViewModel.cs | Logika przeniesiona do ViewModel |
| BazaDanychExternal | IReceivingRepository | Interface + implementacja |
| Etykieta parser | IGS1ParserService | Serwis parsowania jako DI |
| PoleWybor dialog | SelectionDialog.xaml | MAUI ContentPage |
| Thread skanowania | IScannerService | Serwis z event handlers |
| DataTable | ObservableCollection | Reaktywne kolekcje |
| MessageBox | IDialogService | Abstrakcja dialogów |

### Kluczowe decyzje architektoniczne:

1. **Offline-first**: SQLite lokalne + sync z API
2. **Event Sourcing dla operacji**: Pełna historia zmian
3. **Mediator Pattern**: Dla komunikacji między ViewModels
4. **Specification Pattern**: Dla złożonych walidacji
5. **Factory Pattern**: Dla tworzenia etykiet różnych typów

---

**Wersja**: 1.0
**Data**: 2024-12-30
**Autor**: System Migracji WMS
**Status**: Do weryfikacji z zespołem
