<?php

include_once 'db.php';


if ($argv[1] == '1') {
    $baza_danych = 'wmsgg';
}
if ($argv[1] == '2') {
    $baza_danych = 'wmsftl';
}
if ($argv[1] == '3') {
    $baza_danych = 'prod';
}

$tabela = $argv[2];
$id = $argv[3];


//if ($_GET['db'] == '1') {
//    $baza_danych = 'wmsgg';
//}
//
//$tabela = $_GET['tb'];
//$id = $_GET['id'];




    if ($tabela == "delivery") {

        $typ = "4";
        $doc_type = "2";

        $sql = "SELECT e.system_id,d.id FROM $baza_danych.delivery d
left join $baza_danych.delivery_et de on de.delivery_id=d.id
left join $baza_danych.etykiety e on de.etykieta_id=e.id where d.id=$id and e.id is not null group by d.id limit 1 ";
        echo "<br>" . $sql;
        $result = mysql_query($sql, $conn);

        while ($aRow = mysql_fetch_assoc($result)) {
            $system_id_id = $aRow['system_id'];

            $doc_id = $aRow['id'];

            $sql = "SELECT id from wmsrawa.zadania_head zh where zh.baza_danych='$baza_danych' and system_id=$system_id_id and zh.typ='$typ' and doc_id=$doc_id limit 1";
            echo "<br>" . $sql;
            $result24 = mysql_query($sql, $conn);
            $ile = mysql_num_rows($result24);
            echo "$ile";
            if ($ile == "0") {
                echo "bbbbbbb";


                //echo "asdasd" . $aRow2;
                $sql_ins_zad = "insert into wmsrawa.zadania_head(system_id,baza_danych,typ,doc_id,doc_type,ts) values"
                        . " ('" . $system_id_id . "','$baza_danych','$typ','" . $id . "','$doc_type',NOW())";
                echo "<br>" . $sql_ins_zad;
                $result_zadanie = mysql_query($sql_ins_zad, $conn);

                $query_zap1 = "SELECT id FROM wmsrawa.zadania_head z where system_id='$system_id_id' and baza_danych='$baza_danych' "
                        . "and typ='$typ' and doc_id='" . $id . "' and doc_type='$doc_type'";
                echo "<br>" . $query_zap1;
                $result_zadanie2 = mysql_query($query_zap1, $conn);
                while ($row_zad = mysql_fetch_array($result_zadanie2)) {
                    $zadanie_id = $row_zad['id'];
                }
                $sql_ins_zad = "insert into wmsrawa.zadania_dane(zadanie_head_id,status) values ('" . $zadanie_id . "','1')";
                echo "<br>" . $sql_ins_zad;
                $result_zadanie = mysql_query($sql_ins_zad, $conn);
            }
        }
    }
    
    
    
    



//
//$komunikat.=//mysql_error();
////echo $komunikat;
//header('Content-type: text/xml');
//echo '<dane>';
//echo '<komunikat>', htmlentities($komunikat), '</komunikat>';
////echo '<ilosc_pozostala>', htmlentities($ilosc_pozostala), '</ilosc_pozostala>';
////echo '<nowa_etykieta>', htmlentities($nowa_etykieta), '</nowa_etykieta>';
////echo '<kod>', htmlentities($kod), '</kod>';
//echo '</dane>';
?>