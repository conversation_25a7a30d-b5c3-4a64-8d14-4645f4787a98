﻿using System;
using System.Linq;
using System.Collections.Generic;
using System.Text;
using Terranova.API;
using System.Windows.Forms;
using System.Net;
using System.IO;
using System.Xml;

namespace Tarczyn__Magazyn
{
    public static class WebService
    {
        public static string PatchSerwer = "http://172.6.1.249/wmsgg/public/skaner_api/";
        public static string adresSerwer = "172.6.1.249";

        public static XmlNodeList Pobierz_XmlNodeList(XmlDocument doc, string wezel)
        {
            return doc.GetElementsByTagName(wezel);
        }


        public static XmlNode Pobierz_XmlNode(XmlDocument doc, string wezel)
        {
            XmlNodeList xmlnode_etykieta = Pobierz_XmlNodeList(doc, wezel);
            XmlNode node = null;
            foreach (XmlNode wynik in xmlnode_etykieta)
            {
                node = wynik;
            }
            return node;
        }

        public static XmlDocument Pobierz_XmlDocument_old(string URL)
        {
            const string data = @"{""object"":{""name"":""Title""}}";
            XmlDocument doc1 = new XmlDocument();


            HttpWebRequest request = (HttpWebRequest)WebRequest.Create(PatchSerwer + URL);
            request.Method = "POST";
            request.ContentType = "application/json";

            // dodawane 
            //request.KeepAlive = true;
            request.KeepAlive = false;
            //request.ProtocolVersion = HttpVersion.Version11;
            //request.ServicePoint.ConnectionLimit = 24;


            request.ContentLength = data.Length;
            StreamWriter requestWriter = new StreamWriter(request.GetRequestStream(), System.Text.Encoding.ASCII);
            requestWriter.Write(data);
            requestWriter.Close(); // zakomentowałem by sprawdzić czy lepiej chodzi.

            int x = 0;
            string komunikat1 = "";
            string komunikat2 = "";
            while (x < 4)
            {

                try
                {
                    // get the response
                    WebResponse webResponse = request.GetResponse();
                    Stream webStream = webResponse.GetResponseStream();
                    StreamReader responseReader = new StreamReader(webStream);
                    string response = responseReader.ReadToEnd();
                    responseReader.Close();
                    x = 4;
                    komunikat1 = "";
                    komunikat2 = "";

                    doc1.Load(new StringReader(response));
                    

                }
                catch (WebException we)
                {
                    komunikat1 = we.Message;
                    x++;
                    continue;
                    //MessageBox.Show(we.Message);

                    //doc1 = TworzXmlDocument(we.Message);
                    //return a2; //string webExceptionMessage = we.Message;
                }
                catch (Exception ex)
                {
                    komunikat2 = ex.Message;
                    x++;
                    continue;
                    //MessageBox.Show(ex.Message);
                    //doc1 = TworzXmlDocument(ex.Message);
                    // no need to do anything special here....
                    //return a2;
                }
            }
            if (komunikat1 != "")
            {
                doc1 = TworzXmlDocument(komunikat1);
            }
            if (komunikat2 != "")
            {
                doc1 = TworzXmlDocument(komunikat2);
            }
            return doc1;

        }




















        public static XmlDocument Pobierz_XmlDocument(string URL)
        {
             XmlDocument doc1 = new XmlDocument();


            HttpWebRequest request = (HttpWebRequest)WebRequest.Create(PatchSerwer + URL);
            request.ContentType = "text/html";
            request.Method = "GET";
            request.Timeout = 20000;
            request.Proxy = null;


            /*
            HttpWebRequest request = (HttpWebRequest)WebRequest.Create(PatchSerwer + URL);
            request.Method = "POST";
            request.ContentType = "application/json";

            // dodawane 
            //request.KeepAlive = true;
            request.KeepAlive = false;
            //request.ProtocolVersion = HttpVersion.Version11;
            //request.ServicePoint.ConnectionLimit = 24;


            request.ContentLength = data.Length;

            

            StreamWriter requestWriter = new StreamWriter(request.GetRequestStream(), System.Text.Encoding.ASCII);
            requestWriter.Write(data);
            requestWriter.Close(); // zakomentowałem by sprawdzić czy lepiej chodzi.

            */

            





            try
            {
                WebResponse myResponse = request.GetResponse();

                Stream rebut = myResponse.GetResponseStream();

                StreamReader readStream = new StreamReader(rebut, Encoding.UTF8);

                string response = readStream.ReadToEnd();

                myResponse.Close();

                readStream.Close();


                /*
                // get the response
                WebResponse webResponse = request.GetResponse();
                Stream webStream = webResponse.GetResponseStream();
                StreamReader responseReader = new StreamReader(webStream);
                string response = responseReader.ReadToEnd();
                responseReader.Close();
                webResponse.Close();
                */

                doc1.Load(new StringReader(response));
                //XmlNode node = doc1.SelectSingleNode("/dane/komunikat/");
                //doc1= new XmlDocument();

                //return doc1;

                /*
                XmlNodeList xmlnode;
                xmlnode = doc1.GetElementsByTagName("dane");

                XmlNode wynik = null;
                foreach (XmlNode node in xmlnode)
                {
                    wynik = node;

                }
                */
                //MessageBox.Show(wynik.InnerXml);


            }
            catch (WebException we)
            {

                MessageBox.Show(we.Message);
                doc1 = TworzXmlDocument(we.Message);
                //return a2; //string webExceptionMessage = we.Message;
            }
            catch (Exception ex)
            {
                MessageBox.Show(ex.Message);
                doc1 = TworzXmlDocument(ex.Message);
                // no need to do anything special here....
                //return a2;

            }
            return doc1;

        }


        public static XmlDocument Pobierz_XmlDocumentWaga()
        {
            XmlDocument doc1 = new XmlDocument();
            HttpWebRequest request = (HttpWebRequest)WebRequest.Create("http://" + adresSerwer+":8881/");
            request.ContentType = "text/html";
            request.Method = "GET";
            request.Timeout = 20000;
            request.Proxy = null;

            try
            {
                WebResponse myResponse = request.GetResponse();

                Stream rebut = myResponse.GetResponseStream();

                StreamReader readStream = new StreamReader(rebut, Encoding.UTF8);

                string response = readStream.ReadToEnd();

                myResponse.Close();

                readStream.Close();


                doc1.Load(new StringReader(response));


            }
            catch (WebException we)
            {

                MessageBox.Show(we.Message);
                doc1 = TworzXmlDocument(we.Message);
            }
            catch (Exception ex)
            {
                MessageBox.Show(ex.Message);
                doc1 = TworzXmlDocument(ex.Message);

            }
            return doc1;

        }

        public static XmlDocument TworzXmlDocument(string wiadomosc)
        {
            XmlDocument doc = new XmlDocument();
            doc.LoadXml("<dane></dane>");
            XmlNode newElem;
            newElem = doc.CreateNode(XmlNodeType.Element, "komunikat", "");
            newElem.InnerText = wiadomosc;

            XmlElement root = doc.DocumentElement;
            root.AppendChild(newElem);
            return doc;
        }



    }
}