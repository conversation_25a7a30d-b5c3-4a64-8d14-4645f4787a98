﻿using System;
using System.Linq;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using System.Threading;
using System.Text.RegularExpressions;

namespace Tarczyn__Magazyn
{
    public partial class Info_Paleta : Form
    {

        ActionMenu parent = null;

        static string miejsce="";
        static string poziom="";
        static string ostatnia_paleta = "";


        string NR_DOK = "";

        string operac_id_global = "";


        List<string> _regal = new List<string>();

        static int miejsce_id = 0;

        public Info_Paleta(ActionMenu myParent)
        {
            if (Wlasciwosci.GNG != "")
            {
                BazaDanychExternal.ChangeHost(Wlasciwosci.GNG, Wlasciwosci.Varhost, Wlasciwosci.VarLoginMYSQL, Wlasciwosci.VarHasloMYSQL);
            }
            FullScreenMode.OknoOFF(this);
            InitializeComponent();
            parent = myParent;
            textBox1.Focus();

            //ZacznijSkanowanie();

        }
        
        List<string> Etykiety = new List<string>();
        Thread Skanowanie = null;


        private void ZacznijSkanowanie()
        {

            StringBuilder login = new StringBuilder();
            Skaner.UstawTryb_String(login);
            Skaner.Zacznij_Skanowanie();
            Skanowanie = new Thread(() => CheckString(login));
            Skanowanie.IsBackground = true;
            Skanowanie.Start();
        }
        private void CheckString(StringBuilder cc)
        {
            while (true)
            {
                if (cc.ToString() != "")
                {
                    this.Invoke(new EventHandler(delegate
                    {
                        
                        dodawanie(cc.ToString());
                    }));
                    return;
                }
                Thread.Sleep(200);
            }
        }
        private void Zakoncz_Skanowanie()
        {
            try
            {
                Skanowanie.Abort();
                Skaner.Przewij_Skanowanie();
            }
            catch
            {
            }
        }

        private void Exit_Click_1(object sender, EventArgs e)
        {
            Zakoncz_Skanowanie();
            BazaDanychExternal.ChangeHost(Wlasciwosci.Varbaza, Wlasciwosci.Varhost, Wlasciwosci.VarLoginMYSQL, Wlasciwosci.VarHasloMYSQL);
            parent.Show();
            this.Close();
        }

        private void dodawanie(string gg)
        {

            Zakoncz_Skanowanie();


            //MessageBox.Show(gg);
            //MessageBox.Show(gg.Substring(2));
            ostatnia_paleta = gg;
            label1.Text = (string)BazaDanychExternal.Wyczytaj_Jedna_Wartosc("SELECT cast(TRIM(TRAILING '.' FROM TRIM(TRAILING '0' from sum(e.ilosc))) as char) as ilosc FROM etykiety e left join palety p on e.paleta_id=p.id left join kody k on e.kod_id=k.id where (e.paleta_id='" + gg.Replace("DS", "") + "' or p.pal_klient='" + gg.Replace("DS", "") + "') and e.active=1");

            //object test1 = BazaDanychExternal.Wyczytaj_Tabele("SELECT b.id,b.active FROM wmsgg.etykiety_kartony a,wmsgg.etykiety b where karton_ean like'"+gg+"' and a.idetykiety=b.id order by b.id desc LIMIT 1");

            object test1 = BazaDanychExternal.Wyczytaj_Tabele("SELECT k.kod, cast(TRIM(TRAILING '.' FROM TRIM(TRAILING '0' from sum(e.ilosc))) as char) as ilosc, k.kod_nazwa FROM etykiety e left join palety p on e.paleta_id=p.id left join kody k on e.kod_id=k.id where (e.paleta_id='" + gg.Replace("DS", "") + "' or p.pal_klient='" + gg.Substring(2) + "') and e.active=1 GROUP BY e.kod_id order by k.kod");
            //SELECT cast(concat (concat(count(1),'---',i.typ_cd_marchio),'__', (SELECT group_concat(ee.etykieta_klient) FROM etykiety ee left join importRICIdomi ii on ee.etykieta_klient=ii.CD_CONTENITORE where ee.paleta_id=e.paleta_id and ii.typ_cd_marchio=i.typ_cd_marchio GROUP BY i.typ_cd_marchio)) as char) as aa FROM etykiety e left join importRICIdomi i on e.etykieta_klient=i.CD_CONTENITORE where e.paleta_id='" + gg.Replace("DS", "") + " GROUP BY i.typ_cd_marchio  ");
            //MessageBox.Show("2");
            DataTable Wynik = (DataTable)test1;
            if (test1 == null || Wynik.Rows.Count < 1)
            {
                //MessageBox.Show("3");
                label2.Text = "Brak w bazie";

                ZacznijSkanowanie();
                return;
            }
            else
            {
                //MessageBox.Show("5");
                ZacznijSkanowanie();

                dataGrid1.DataSource = Wynik;
                dataGrid1.TableStyles.Clear();
                DataGridTableStyle tableStyle = new DataGridTableStyle();
                tableStyle.MappingName = Wynik.TableName;

                DataGridTextBoxColumn tbcName = new DataGridTextBoxColumn();
                tbcName.Width = 80;
                tbcName.MappingName = Wynik.Columns[0].ColumnName;
                tbcName.HeaderText = Wynik.Columns[0].ColumnName;
                tableStyle.GridColumnStyles.Add(tbcName);
                DataGridTextBoxColumn tbcName1 = new DataGridTextBoxColumn();
                tbcName1.Width = 50;
                tbcName1.MappingName = Wynik.Columns[1].ColumnName;
                tbcName1.HeaderText = Wynik.Columns[1].ColumnName;
                tableStyle.GridColumnStyles.Add(tbcName1);
                DataGridTextBoxColumn tbcName2 = new DataGridTextBoxColumn();
                tbcName2.Width = 150;
                tbcName2.MappingName = Wynik.Columns[2].ColumnName;
                tbcName2.HeaderText = Wynik.Columns[2].ColumnName;
                tableStyle.GridColumnStyles.Add(tbcName2);




                dataGrid1.TableStyles.Add(tableStyle);

                /*
                for (int t = 0; t < Wynik.Rows.Count; t++)
                {
                    string.Format(@"" + aa, Environment.NewLine);
                }
                
                 */
                return;


            }
            //MessageBox.Show("5");


            //object test2 = BazaDanychExternal.Wyczytaj_Jedna_Wartosc("select wartosc4 from kolektor where wartosc1='" + gg + "' order by id desc LIMIT 1");


            ZacznijSkanowanie();
        }

        private void WczytywanieNaLinie_Load_1(object sender, EventArgs e)
        {
            ZacznijSkanowanie();
        }

        private void button1_Click(object sender, EventArgs e)
        {
            dodawanie(textBox1.Text);
            textBox1.Text = "";
        }

        private void button2_Click_2(object sender, EventArgs e)
        {
            if (ostatnia_paleta.Trim() == "" || ostatnia_paleta == null)
            {
                MessageBox.Show("Brak palet!");
                return;
            }

            BazaDanychExternal.DokonajUpdate("update zlecenia_palety z set z.active1=1 WHERE z.paleta_id=" + ostatnia_paleta.Replace("DS", ""));
            BazaDanychExternal.DokonajUpdate("update zlecenia_palety z set z.active1=0 WHERE z.paleta_id="+ostatnia_paleta.Replace("DS", ""));
        }
    }
}
