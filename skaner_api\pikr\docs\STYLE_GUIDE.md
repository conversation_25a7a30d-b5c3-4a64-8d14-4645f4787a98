# Przewodnik stylu kodowania

## Konwencje nazewnictwa

### Pliki
- Nazwy plików PHP powinny być pisane małymi literami z podkreśleniami, np. `api.php`, `funkcje.inc`
- Pliki klas powinny mieć nazwę klasy z pierwszą wielką literą i rozszerzenie `.class.php`, np. `Dab.class.php`

### Funkcje i metody
- Funkcje powinny być nazwane przy użyciu małych liter i podkreśleń (snake_case), np. `dodaj_osobe_do_linii()`
- Nazwy funkcji powinny jasno opisywać ich działanie

### Zmienne
- Zmienne powinny być nazwane przy użyciu małych liter i podkreśleń (snake_case)
- Nazwy zmiennych powinny być jasne i opisowe, np. `$deklaracja_id`, `$imie_nazwisko`
- Identyfikatory powinny mieć sufiks `_id`, np. `$wyrob_id`, `$osoba_id`

### Stałe
- Stałe powinny być pisane wielkimi literami z podkreśleniami, np. `MAX_LICZBA_OSOB`

## Formatowanie kodu

### Wcięcia
- Używaj 4 spacji do wcięć (nie tabulatorów)
- Bloki kodu powinny być otaczane nawiasami klamrowymi i wcięte o jeden poziom

### Nawiasy klamrowe
- Nawias otwierający powinien być w tej samej linii co instrukcja otwierająca
```php
if ($warunek) {
    // kod
}
```

### Spacje
- Operatory powinny być otoczone spacjami: `$a = $b + $c;`
- Średniki nie powinny być poprzedzone spacją
- Umieszczaj spację po przecinkach w liście argumentów

## Dobre praktyki

### Dokumentacja
- Każda funkcja powinna mieć dokumentację w formacie PHPDoc
- Dokumentacja powinna zawierać opis funkcji, parametrów i zwracanej wartości
```php
/**
 * Opis funkcji.
 *
 * @param type $param Opis parametru
 * @return type Opis zwracanej wartości
 */
```

### Obsługa błędów
- Używaj funkcji `show_komunikat_xml()` do wyświetlania komunikatów błędów
- Zawsze sprawdzaj poprawność danych wejściowych przed ich użyciem

### Zapytania SQL
- Każde zapytanie SQL powinno być czytelnie sformatowane i podzielone na wiersze
- Używaj funkcji klasy `Dab` do wykonywania zapytań: `mGetResult()` lub `mGetResultAsXML()`

### Bezpieczeństwo
- Zawsze waliduj i sanityzuj dane wejściowe przed użyciem
- Unikaj bezpośredniego wstawiania zmiennych do zapytań SQL

## Struktura API

### Format odpowiedzi XML
- Odpowiedzi API powinny być zwracane w formacie XML
- Używaj funkcji `show_komunikat_xml()` dla prostych komunikatów
- Używaj funkcji `xml_from_indexed_array()` dla złożonych odpowiedzi
- Format XML powinien być spójny w całym API

### Obsługa błędów w API
- Każda funkcja API powinna sprawdzać poprawność parametrów
- Komunikaty błędów powinny być jasne i informować o przyczynie problemu
- W przypadku błędu, zawsze zwracaj odpowiedni kod HTTP i komunikat XML
