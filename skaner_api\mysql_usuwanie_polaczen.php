<?php

//include_once 'db_pdo.php';
include_once 'Db.class.php';
include_once 'funkcje.inc';
//error_reporting(E_ALL);
//ini_set('display_errors', 1);

$db = new Db();
//
error_reporting(E_ALL);
ini_set('display_errors', 1);


//$query = "SHOW FULL PROCESSLIST";
//
//$result3 = $db->mGetResult($query);
//
//
//
////
//foreach ($result3 as $key => $row) {
//    $process_id = $row["Id"];
//    if ($row["Time"] > 1000 && $row["Host"] == "root") {
//        $sql = "KILL $process_id";
//        $result4 = $db->mGetResult($sql);
//        echo "<pre>";
//        print_r($row);
//        echo "</pre>";
//    }
//}
//        
//while ($row=mysql_fetch_array($result)) {
//  $process_id=$row["Id"];
//  if ($row["Time"] > 200 ) {
//    $sql="KILL $process_id";
//    mysql_query($sql);
//  }
//}
//exit();
//
//
$query = "select id,SUBSTRING_INDEX(host, ':', 1) as host,time,user
from INFORMATION_SCHEMA.PROCESSLIST
having  (host like '172-7-1%' or host like '172.7.1%') and user='root'
order by time asc
";
//
$result3 = $db->mGetResult($query);

print_r($result3);
$hosts = array();

foreach ($result3 as $key => $row) {
    $process_id = $row["id"];
    if ($row["user"] == "root") {
        if (empty($hosts[$row["host"]])) {
            $hosts[$row["host"]] = 0;
        }
        $hosts[$row["host"]] += 1;

        if ($hosts[$row["host"]] > 1) {
            $sql = "KILL $process_id";
            echo "<br>".$sql;
            $db->mGetResult($sql);
        }

        if ($row["time"] > 4000) {
            $sql = "KILL $process_id";
            //$db->mGetResult($sql);
        }
    }
}
?>