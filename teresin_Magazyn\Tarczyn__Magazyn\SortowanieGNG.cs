﻿using System;
using System.Linq;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using System.Threading;
using System.Net;
using System.IO;
using System.Xml;

namespace Tarczyn__Magazyn
{



    public partial class SortowanieGNG : Form
    {
        ActionMenu myParent = null;

        string operac_id_global = "";

        public SortowanieGNG(ActionMenu c)
        {

            if (Wlasciwosci.GNG != "")
            {
                Wlasciwosci.bazaDanych = 2;
                BazaDanychExternal.ChangeHost(Wlasciwosci.GNG, Wlasciwosci.Varhost, Wlasciwosci.VarLoginMYSQL, Wlasciwosci.VarHasloMYSQL);
            }
            FullScreenMode.OknoOFF(this);
            InitializeComponent();
            myParent = c;
            Wlasciwosci.CurrentOperacja = "19";

            string zapytanie = "SELECT last FROM docnumber d WHERE d.name='operacja_id';";

            operac_id_global = (string)BazaDanychExternal.Wyczytaj_Jedna_Wartosc(zapytanie);

            string zapyt = "update docnumber d set last=last+1 WHERE d.name='operacja_id'; ";
            BazaDanychExternal.Wyczytaj_Jedna_Wartosc(zapyt);


            //BazaDanychExternal.Inicjalizacja(Wlasciwosci.Varbaza, Wlasciwosci.Varhost, Wlasciwosci.VarLoginMYSQL, Wlasciwosci.VarHasloMYSQL, this);  
        }


        List<string> Etykiety = new List<string>();
        Thread Skanowanie = null;


        private void ZacznijSkanowanie()
        {
            StringBuilder login = new StringBuilder();
            Skaner.UstawTryb_String(login);
            Skaner.Zacznij_Skanowanie();
            Skanowanie = new Thread(() => CheckString(login));
            Skanowanie.IsBackground = true;
            Skanowanie.Start();
        }
        private void CheckString(StringBuilder cc)
        {
            while (true)
            {
                if (cc.ToString() != "")
                {
                    this.Invoke(new EventHandler(delegate
                    {

                        dodawanie(cc.ToString());
                    }));
                    return;
                }
                Thread.Sleep(200);
            }
        }
        private void Zakoncz_Skanowanie()
        {
            try
            {
                Skanowanie.Abort();
                Skaner.Przewij_Skanowanie();
            }
            catch
            {
            }
        }

        private void Exit_Click_1(object sender, EventArgs e)
        {
            Wlasciwosci.CurrentOperacja = "0";
            Zakoncz_Skanowanie();
            //BazaDanychExternal.ChangeHost(Wlasciwosci.Varbaza, Wlasciwosci.Varhost, Wlasciwosci.VarLoginMYSQL, Wlasciwosci.VarHasloMYSQL);
            myParent.Show();
            this.Close();
        }


        private void button2_Click(object sender, EventArgs e)
        {


        }


        private void dodawanie(string gg)
        {

            typsex.Text = "";
            typarticolo.Text = "";
            kodarticolo.Text = "";
            grupapal.Text = "";
            paleta.Text = "";
            sklep.Text = "";

            string wydruk = "TAK";

            if(checkBox1_wydruk.Checked == false)
            {
                wydruk="NIE";
            }



            XmlDocument doc1_etykieta = WebService.Pobierz_XmlDocument("gg_wydruk_segregacja.php?adres_ip=" + comboBox2.Text + "&karton=" + gg + "&camion=" + textBox2.Text + "&operacja_id=" + operac_id_global+"&wydruk"+wydruk);
            XmlNode node_etykieta = WebService.Pobierz_XmlNode(doc1_etykieta, "dane");

            if (node_etykieta["komunikat"].InnerText != "OK")
            {
                MessageBox.Show(node_etykieta["komunikat"].InnerText);
            }
            else
            {
                grupapal.Text = node_etykieta["paleta"].InnerText;
                typarticolo.Text = node_etykieta["karton"].InnerText;
                ilosc.Text = node_etykieta["licznik"].InnerText;
            }

            
            ZacznijSkanowanie();

        }


        private void Zakoncz_Click(object sender, EventArgs e)
        {

        }

        private void WczytywanieNaLinie_Load_1(object sender, EventArgs e)
        {
            //ZacznijSkanowanie();
        }

        private void button2_Click_1(object sender, EventArgs e)
        {

        }

        private void button1_Click(object sender, EventArgs e)
        {
            dodawanie(textBox1.Text);
            textBox1.Text = "";
        }

        private void button2_Click_2(object sender, EventArgs e)
        {
            Wlasciwosci.CurrentOperacja = "6";
            ZacznijSkanowanie();
            bool ok = true;

            textBox1.Visible = ok;
            label1.Visible = ok;
            button1.Visible = ok;
            //textBox2.Enabled = !ok;
            button2.Enabled = !ok;
            sklep.Visible = ok;
            button2.Text = "Anuluj";
            button2.Click -= button2_Click_2;
            button2.Click += button2_Click_3;
        }

        private void button2_Click_3(object sender, EventArgs e)
        {
            ZacznijSkanowanie();
            bool ok = false;
            button2.Text = "OK";
            textBox1.Visible = ok;
            label1.Visible = ok;
            button1.Visible = ok;
            textBox2.Enabled = !ok;
            button2.Enabled = !ok;
            sklep.Visible = ok;
            button2.Click -= button2_Click_3;
            button2.Click += button2_Click_2;

        }



    }
}