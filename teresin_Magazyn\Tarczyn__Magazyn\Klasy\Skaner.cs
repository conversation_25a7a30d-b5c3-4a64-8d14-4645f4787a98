﻿using System;
using System.Linq;
using System.Collections.Generic;
using System.Text;
using Symbol;
using Symbol.Barcode;
using Symbol.Barcode2;
using System.Windows.Forms;

namespace Tarczyn__Magazyn
    
{ 
   public static class Skaner
    {

        private static int Tryb = 0;
        public static StringBuilder Teskt_Zmienna;
        public static StringBuilder Teskt_Do_Zmiany;
        private static TextBox Pole_DoSkanowania;
        private static EventHandler MyReadNotifyHandler;
        private static EventHandler MyStatusNotifyHandler;
        private static Symbol.Barcode.Reader MyReader = new Symbol.Barcode.Reader();
        private static Symbol.Barcode.ReaderData MyReaderData = new Symbol.Barcode.ReaderData(Symbol.Barcode.ReaderDataTypes.Text, Symbol.Barcode.ReaderDataLengths.MaximumLabel);

        public static void Przewij_Skanowanie()
        {

            MyReader.StatusNotify -= MyStatusNotifyHandler;
            MyReader.ReadNotify -= MyReadNotifyHandler;
            MyReader.Actions.Disable();
        }

        public static void Zacznij_Skanowanie()
        {
            podczepianieeventow();
            MyReader.Actions.Enable();
            MyReader.Actions.Read(MyReaderData);
        }

        public static void UstawTryb_TextBox(TextBox abc)
        {
            Pole_DoSkanowania=abc;
            Tryb = 1;

        }

        public static void UstawTryb_String(StringBuilder abc)
        {

            // Teskt_Do_Zmiany = abc;
            Teskt_Zmienna = (StringBuilder)abc;
            Tryb = 2;
        }

        private static void podczepianieeventow()
        {
            MyReadNotifyHandler = new EventHandler(MyReader_ReadNotify);
            MyStatusNotifyHandler = new EventHandler(MyReader_StatusNotify);
            MyReader.StatusNotify += MyStatusNotifyHandler;
            MyReader.ReadNotify += MyReadNotifyHandler;
        }



        private static void MyReader_ReadNotify(object sender, EventArgs e)
        {
            // Get ReaderData
            Symbol.Barcode.ReaderData TheReaderData = MyReader.GetNextReaderData();
            switch (TheReaderData.Result)
            {
                case Symbol.Results.SUCCESS:

                    switch (Tryb)
                    {
                        case 1:
                            Pole_DoSkanowania.Text = TheReaderData.Text;
                            break;
                        case 2:
                            //Teskt_Do_Zmiany.Append(TheReaderData.Text);
                            Teskt_Zmienna.Insert(0, TheReaderData.Text);
                            //Teskt_Zmienna = TheReaderData.Text;

                            break;
                    }
                    
                    MyReader.Actions.Read(MyReaderData);
                    
                    Przewij_Skanowanie();

                    if (Wlasciwosci.historia_skaner == "TAK")
                    {
                        BazaDanychExternal.DokonajUpdate("insert into historia_skanowania(wartosc,stamp,Serial,prac_id,operacja) values('" + TheReaderData.Text + "',sysdate(),'" + Wlasciwosci.SerialNumber + "','" + Wlasciwosci.id_Pracownika + "','" + Wlasciwosci.CurrentOperacja + "')");
                    }
           
                    
                    
                    
                    if (Wlasciwosci.id_Pracownika == "0" && Wlasciwosci.CurrentOperacja!="7")
                    {
                        MessageBox.Show("Nie rozpoznany użytkownik, aplikacja wymaga ponownego zalogowania się!!!");
                    }
                    

                    //Dodanie do listy

                    break;


                case Symbol.Results.CANCELED:
                    //MessageBox.Show("Canceled");
                    break;

                default:
                    //MessageBox.Show("");
                    string sMsg = "Read Failed\n"
                    + "Result = " + ((int)TheReaderData.Result).ToString("X8");
                    MessageBox.Show(sMsg);
                    break;
            }
        }
        private static void MyReader_StatusNotify(object sender, EventArgs e)
        {

            Symbol.Barcode.BarcodeStatus TheEvent = MyReader.GetNextStatus();

        }



        public static void Wyczysc()
        {
            MyReader.Dispose();
            MyReaderData.Dispose();
        }

    }
}
