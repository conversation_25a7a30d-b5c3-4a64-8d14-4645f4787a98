<?php

//include_once 'db_pdo.php';
include_once 'Db.class.php';
include_once 'funkcje.inc';
//error_reporting(E_ALL);
//ini_set('display_errors', 1);

$db = new Db();
//
error_reporting(E_ALL);
ini_set('display_errors', 1);


//czyszczenie automatycznych
$sql = "SELECT zh.id,max(zd.start) as start_max,zd.stop,now(),DATE_ADD(NOW(), INTERVAL -30 MINUTE),czas_przydzielenia FROM zadania_dane zd
left join zadania_head zh on zh.id=zd.zadanie_head_id
where zd.stop is null and zh.id is not null and czas_przydzielenia is null
GROUP BY zd.zadanie_head_id
having start_max<DATE_ADD(NOW(), INTERVAL -60 MINUTE);";


$result3 = $db->mGetResult($sql);

//print_r($result3);


foreach ($result3 as $key => $row) {

    $sql = "update zadania_dane
set
przydzielenie_pracownik_id=0,
 start=null
where
stop is null
and zadanie_head_id=" . $row['id'];
    echo $sql;
    $db->mGetResult($sql);
}
echo "GOTOWE ...";
?>