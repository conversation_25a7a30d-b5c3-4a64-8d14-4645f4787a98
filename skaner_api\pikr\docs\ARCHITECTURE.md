# Architektura systemu API Skanera

## Struktura systemu

### Komponenty główne
1. **API REST** - Punkty końcowe do komunikacji z zewnętrznymi aplikacjami
   - `api.php` - API do zarządzania osobami na liniach produkcyjnych (formatowanie XML)
   - `zadania_typy_pracy.php` - API do pobierania stanowisk pracy (formatowanie XML)

2. **Warstwa bazy danych**
   - Klasa `Dab` - Abstrakcja dostępu do bazy danych
   - Tabele główne:
     - `osoby` - Informacje o pracownikach
     - `deklaracje` - Informacje o deklaracjach produkcyjnych
     - `deklaracja_osoby` - Powiązania między osobami a deklaracjami
     - `deklaracja_wyroby` - Powiązania między deklaracjami a wyrobami

3. **Biblioteki pomocnicze**
   - `funkcje.inc` - Funkcje wspólne dla wszystkich modułów API
     - Funkcje formatowania XML
     - Funkcje pomocnicze do pracy z bazą danych

## Przepływ danych

### Rejestracja rozpoczęcia pracy
1. Klient wysyła zapytanie do API (`api.php?akcja=dodawanie`)
2. System waliduje dane wejściowe (format czasu, wymagane parametry)
3. System sprawdza, czy pracownik istnieje w bazie
4. System sprawdza, czy pracownik nie pracuje już na innej deklaracji
5. System znajduje lub tworzy deklarację dla wyrobu
6. System dodaje pracownika do deklaracji
7. System zwraca potwierdzenie XML

### Rejestracja zakończenia pracy
1. Klient wysyła zapytanie do API (`api.php?akcja=dodawanie&typ_operacji=stop`)
2. System waliduje dane wejściowe
3. System znajduje aktywną pracę pracownika
4. System kończy pracę pracownika
5. System aktualizuje czasy w deklaracji wyrobów
6. System zwraca potwierdzenie XML

## Struktura bazy danych

### Najważniejsze tabele i relacje

- **osoby**
  - `id` - Identyfikator osoby
  - `imie`, `nazwisko` - Dane personalne
  - `numer_karty` - Unikalny numer karty identyfikacyjnej

- **deklaracje**
  - `id` - Identyfikator deklaracji
  - `numer` - Numer deklaracji
  - `data_produkcji` - Data produkcji
  - `utworzyl` - Kto utworzył deklarację

- **deklaracja_osoby**
  - `id` - Identyfikator wpisu
  - `deklaracja_id` - Powiązanie z deklaracją
  - `osoba_id` - Powiązanie z osobą
  - `start` - Czas rozpoczęcia pracy
  - `stop` - Czas zakończenia pracy

- **deklaracja_wyroby**
  - `id` - Identyfikator wpisu
  - `deklaracja_id` - Powiązanie z deklaracją
  - `wyrob_id` - Powiązanie z wyrobem
  - `start` - Czas rozpoczęcia pracy na wyrobie
  - `stop` - Czas zakończenia pracy na wyrobie

## Zasady walidacji danych

- Walidacja formatu czasu: HH:MM lub H:MM
- Weryfikacja istnienia osoby o podanym numerze karty
- Weryfikacja poprawności typu operacji (start/stop)

## Przyszłe kierunki rozwoju

1. **Refaktoryzacja**
   - Przejście na bardziej obiektowy model
   - Wprowadzenie warstwy abstrakcji dla operacji na bazie danych

2. **Rozszerzenia**
   - Dodanie nowych punktów końcowych API
   - Wprowadzenie autentykacji i autoryzacji
   - Dodanie bardziej zaawansowanych raportów i statystyk
