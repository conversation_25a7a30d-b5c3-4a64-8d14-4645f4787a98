using System;
using System.Linq;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using System.Threading;
using System.Xml;

namespace Tarczyn__Magazyn
{
    public partial class KontrolaPalet : Form
    {
        ActionMenu myParent = null;
        XmlDocument typy_palet_xml = null;
        String typpalety_nazwa_poprzedni = "EURO";

        StringBuilder delivery_id = new StringBuilder("0");
        StringBuilder typ_palety_old_id = new StringBuilder("0");
        StringBuilder typ_palety_new_id = new StringBuilder("0");
        StringBuilder paleta_id = new StringBuilder("0");



        public KontrolaPalet(ActionMenu MyParent)
        {
            this.myParent = MyParent;

            FullScreenMode.OknoOFF(this);
            InitializeComponent();
            typy_palet_xml = InicjalizujTypyPalet();

            //ZacznijSkanowanie();
            //Wlasciwosci.CurrentOperacja = "28";
            //pokaz_podglad();
            //BazaDanychExternal.Inicjalizacja(Wlasciwosci.Varbaza, Wlasciwosci.Varhost, Wlasciwosci.VarLoginMYSQL, Wlasciwosci.VarHasloMYSQL, this);  
            //pobranie_wymiarow_zdefiniowanych;


        }

        Thread Skanowanie = null;


        private void ZacznijSkanowanie()
        {
            StringBuilder login = new StringBuilder();
            Skaner.UstawTryb_String(login);
            Skaner.Zacznij_Skanowanie();
            Skanowanie = new Thread(() => CheckString(login));
            Skanowanie.IsBackground = true;
            Skanowanie.Start();
        }

        private void CheckString(StringBuilder cc)
        {
            while (true)
            {
                if (cc.ToString() != "")
                {
                    this.Invoke(new EventHandler(delegate
                    {
                        dodawanie(cc.ToString());
                    }));
                    return;
                }
                Thread.Sleep(200);
            }
        }
        private void Zakoncz_Skanowanie()
        {
            try
            {
                Skanowanie.Abort();
                Skaner.Przewij_Skanowanie();
            }
            catch
            {
            }
        }


        private bool WczytajNaLinie()
        {
            //XmlDocument doc2_etykieta = WebService.Pobierz_XmlDocument("wczytanie_na_linie.php?etykieta_id=" + etykietyDoUzupelnienia[currentIndex - 1].ID+"&pracownik_id="+Wlasciwosci.id_Pracownika);
            //XmlNode node2_etykieta = WebService.Pobierz_XmlNode(doc2_etykieta, "dane");
            //if (node2_etykieta["komunikat"].InnerText != "OK")
            //{
            //    MessageBox.Show(node2_etykieta["komunikat"].InnerText);
            //    return false;
            //} 

            return false;
        }




        private void dodawanie(string gg)
        {
            Zakoncz_Skanowanie();

            if (gg.Substring(0, 2) == "DS")
            {
                string paleta_id_local = gg.Replace("DS", ""); // Usuń prefix 'DS'
                XmlDocument doc = WebService.Pobierz_XmlDocument("palety/palety_skanowanie.php?paleta_id=" + paleta_id_local);
                XmlNode node = WebService.Pobierz_XmlNode(doc, "dane");
                
                if (node != null && node["opis"] != null)
                {
                    typ_palety_nazwa.Text = node["opis"].InnerText;
                    typ_palety_old_id = new StringBuilder(node["typypalet_id"].InnerText);
                    delivery_id = new StringBuilder(node["delivery_id"].InnerText);
                    label2.Text = "DL"+delivery_id;
                    paleta_id = new StringBuilder(gg.Substring(2));
                }
                else
                {
                    MessageBox.Show("Nie znaleziono informacji o palecie");
                }
            }

            ZacznijSkanowanie();
        }



        private void wyjscie()
        {
            this.Skanowanie.Abort();
            Skaner.Przewij_Skanowanie();
            this.myParent.Show();
            this.Close();
        }
        private void button1_Click(object sender, EventArgs e)
        {
            wyjscie();
        }

        private void button2_Click_1(object sender, EventArgs e)
        {
            //akceptacja_manualna();
            //pokaz_podglad(delivery_id_global);
            //edycja = false;
        }


        private void GotFocus_TextBox(object sender, EventArgs e)
        {
            Skaner.Przewij_Skanowanie();
            //TextBox Pole_Tekstowe = (TextBox)sender;

            //AktualnyTextBox = Pole_Tekstowe;

            ZacznijSkanowanie();
        }

        private void LostFocus_TextBox(object sender, EventArgs e)
        {
            //AktualnyTextBox = null;
            Skaner.Przewij_Skanowanie();

        }

        private void zmien_Click(object sender, EventArgs e)
        {

            PoleComboboxXml  XC = new PoleComboboxXml(typy_palet_xml, "Wybierz paletę", typpalety_nazwa_poprzedni);

            if (XC.ShowDialog() == DialogResult.OK)
            {
                if (XC.wybierana_nazwa == "")
                {
                    MessageBox.Show("Nie dokonano wyboru");
                    return;
                }
                typpalety_nazwa_poprzedni = XC.wybierana_nazwa;

                typ_palety_new_id = new StringBuilder(XC.wybierane_id);

                //MessageBox.Show("XC.wybierana_nazwa: " + XC.wybierana_nazwa);
                //MessageBox.Show("XC.wybierane_id: " + XC.wybierane_id);
                palety_kontrola(delivery_id, paleta_id, new StringBuilder(Wlasciwosci.imie_nazwisko), typ_palety_old_id, typ_palety_new_id);


                
            }
            else
            {
                return;
            }


            
        }



        //private void button6_Click(object sender, EventArgs e)
        //{
        //    etykietyDoUzupelnienia[currentIndex - 1].Status_Prism = textBox2.Text;
        //}


        private XmlDocument PobierzTypyPalet()
        {
            XmlDocument doc = WebService.Pobierz_XmlDocument("palety/palety_pobietranie_typow.php");
            return doc;
        }

        private XmlDocument InicjalizujTypyPalet()
        {
            try
            {
                return PobierzTypyPalet();
            }
            catch (Exception ex)
            {
                // TODO: Dodaj logowanie błędu jeśli system logowania jest dostępny
                
                return null;
            }
        }

        private void palety_kontrola(StringBuilder delivery_id, StringBuilder paleta_id, StringBuilder imie_nazwisko, StringBuilder typ_palety_old_id, StringBuilder typ_palety_new_id)
        {
            try
            {
                XmlDocument doc = WebService.Pobierz_XmlDocument("palety/palety_kontrola.php?delivery_id=" + delivery_id + "&paleta_id=" + paleta_id + "&imie_nazwisko=" + imie_nazwisko + "&typ_palety_old_id=" + typ_palety_old_id + "&typ_palety_new_id=" + typ_palety_new_id);
                XmlNode node = WebService.Pobierz_XmlNode(doc, "dane");

                if (node != null && node["komunikat"] != null)
                {
                    MessageBox.Show(node["komunikat"].InnerText);
                    if (node["komunikat"].InnerText == "OK")
                    {
                        typ_palety_nazwa.Text = "";
                        typ_palety_old_id.Length = 0;  // lub typ_palety_old_id.Clear();
                        delivery_id.Length = 0;        // lub delivery_id.Clear();
                        label2.Text = "";
                        
                    }
                }
                else
                {
                    MessageBox.Show("Błąd podczas komunikacji z serwerem");
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show("Wystąpił błąd: " + ex.Message);
            }
        }
 
        private void zatwierdz_Click(object sender, EventArgs e)
        {

            palety_kontrola(delivery_id, paleta_id, new StringBuilder(Wlasciwosci.imie_nazwisko), typ_palety_old_id, typ_palety_old_id);
        }
    }
}