﻿using System;
using System.Linq;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using MySql.Data.MySqlClient;
using System.Text.RegularExpressions;
using System.Threading;
//using System.Data.SqlServerCe;
using System.Collections;


namespace Tarczyn__Magazyn
{
    public partial class InwentaryzacjaMiejsca : Form
    {
        ActionMenu myParent = null;


        List<string> _inw_wybor = new List<string>();
        List<string> _inw_data = new List<string>();
        List<string> _inw_opis = new List<string>();
        int[] _inw = new int[100];

        List<string> _inw_hala = new List<string>();
        List<string> _inw_regal = new List<string>();
        List<string> _inw_poziom = new List<string>();
        List<string> _inw_status = new List<string>();

        int nr_inw = 0;
        int inwentaryzacja_id = 0;
        private Thread Skanowanie;
        string etykieta_id = "0";

        string sap_gora_czesc2 = "";
        string kod = "";
        string podkod = "";
        double ilosc = 0;

        public string hala = "";
        public string regal = "";
        public string miejsce = "";
        public string poziom = "";


        string operac_id_global = "";

        TextBox AktualnyTextBox = null;


        Dictionary<string, string> rec = new Dictionary<string, string>();

        int ile_do_inw = 0;


        //public static SqlCeConnection con = new SqlCeConnection(@"Data Source =.\Program Files\moto1\Northwind.sdf");







        public MySqlConnection ok = null;

        public InwentaryzacjaMiejsca(ActionMenu MyParent)
        {
            InitializeComponent();
            this.myParent = MyParent;
            Wlasciwosci.CurrentOperacja = "25";
            Etykieta.Inicjalizacja();
            //wms_wybrany.Text = this.myParent.system_id;
            imie_nazwisk_label.Text = Wlasciwosci.wozek + ":" + Wlasciwosci.imie_nazwisko;
            //ok = myParent.conn;
            wyszukaj_inwentaryzacje();
            wypelnij_status();


            string zapytanie = "SELECT last FROM docnumber d WHERE d.name='operacja_id';";

            operac_id_global = (string)BazaDanychExternal.Wyczytaj_Jedna_Wartosc(zapytanie);

            string zapyt = "update docnumber d set last=last+1 WHERE d.name='operacja_id'; ";
            BazaDanychExternal.Wyczytaj_Jedna_Wartosc(zapyt);

        }


        // podczas wchodzenia do inwentaryzacji
        private void wyszukaj_inwentaryzacje()
        {
            string zapytanie = "SELECT DATE_FORMAT(i.data,'%Y-%m-%d') as data,opis,inwentaryzacja_id FROM inwentaryzacja i  where active=1 group by i.data,opis,inwentaryzacja_id order by inwentaryzacja_id desc limit 40";
            object obj2 = BazaDanychExternal.Wyczytaj_Tabele(zapytanie);
            DataTable tabela = (DataTable)obj2;

            _inw_wybor.Add("Nowy nr INW");
            _inw[0] = 0;
            for (byte k = 0; k < tabela.Rows.Count; k++)
            {
                _inw_wybor.Add(tabela.Rows[k]["inwentaryzacja_id"].ToString() + " | " + tabela.Rows[k]["data"].ToString());
                _inw[k + 1] = Convert.ToInt32(tabela.Rows[k]["inwentaryzacja_id"]);
            }

            if (_inw_wybor.Count == 0)
            {
                MessageBox.Show("Brak Inwentaryzacji ");
            }
            else
            {
                comboBox1.Visible = true;
                BindingSource bs = new BindingSource();
                bs.DataSource = _inw_wybor;
                comboBox1.DataSource = bs;
            }

        }


        private void wypelnij_status()
        {
            //_inw_status.Add("ET_OK");
            _inw_status.Add("");
            _inw_status.Add("PAL");
            _inw_status.Add("CT");
            //_inw_status.Add("SZT");
            //_inw_status.Add("REND");


            BindingSource bs = new BindingSource();
            bs.DataSource = _inw_status;
            status_comboBox2.DataSource = bs;
        }






















         

        // zatwierdzanie wczytanej etykiety
        private void button1_Click(object sender, EventArgs e)
        {
            //powrot.Visible = false;
            //comboBox1.Enabled = false;
            //kod_textbox.Focus();
            //kod_textbox.SelectionStart = kod_textbox.Text.Length;
            if (myParent.myParent.myWlan.get_Signal_int() < 2)
            {
                MessageBox.Show("Trwa łączenie. Spróbuj ponownie...");
                return;
            }


            if (!BazaDanychExternal.SprawdzCzyIstniejePolaczenie())
            {
                MessageBox.Show("Brak połączenia z bazą danych , spróbuj ponownie.");
                this.ZacznijSkanowanie();
            }
            else
            {

                if (kod_textbox.Text == "")
                {
                    MessageBox.Show("Pole kod jest puste.");
                    return;
                }
                if (ilosc_textBox.Text == "")
                {
                    MessageBox.Show("Pole kod jest puste.");
                    return;
                }
                if (hala == "")
                {
                    MessageBox.Show("Pole miejsce jest puste.");
                    return;
                }
                if (_inw_status[status_comboBox2.SelectedIndex] == "")
                {
                    MessageBox.Show("Pole jednostka miary jest puste");
                    return;
                }

                Skaner.Przewij_Skanowanie();

                BazaDanychExternal.DokonajUpdate("Insert into inwentaryzacja(data,ilosc_spisana,kod,inwentaryzacja_id,hala,regal,miejsce,poziom,pracownik,ts,jm,system_id) values(CURDATE(),'" + ilosc_textBox.Text + "','" + kod_textbox.Text + "','" + inwentaryzacja_id + "','" + hala + "','" + regal + "','" + miejsce + "','" + poziom + "','" + Wlasciwosci.imie_nazwisko + "',now(),'" + _inw_status[status_comboBox2.SelectedIndex] + "','" + Wlasciwosci.system_id_id + "');");
                stan_inwentaryzacji();
                //BazaDanychExternal.DokonajUpdate("insert into operacje(etykieta_id, doc_type, doc_nr,  imie_nazwisko, typ_operacji,wozek) values('0','INW','" + inwentaryzacja_id + "','" + Wlasciwosci.imie_nazwisko + "','INW','" + Wlasciwosci.wozek + "');");

                BazaDanychExternal.DokonajUpdate("insert into operacje(etykieta_id, doc_type, doc_nr, imie_nazwisko, typ_operacji,system_id,wozek,operac_id,ilosc) values('0','INWM','" + inwentaryzacja_id + "','" + Wlasciwosci.imie_nazwisko + "','INWM','" + Wlasciwosci.system_id_id + "','" + Wlasciwosci.wozek + "','" + operac_id_global + "','1');");


                label8.Text = "Ostatnio: H:" + hala + " " + regal + "-" + miejsce + "-" + poziom + Environment.NewLine + kod_textbox.Text + " " + ilosc_textBox.Text + " " + _inw_status[status_comboBox2.SelectedIndex];

                ilosc_textBox.Text = "";
                kod_textbox.Text = "";
                miejsce_textBox1.Text = "";
                kod = "";
                hala = "";
                regal = "";
                miejsce = "";
                poziom = "";
                ilosc = 0;
                miejsce_textBox1.Focus();


                //this.button1.Click -= new EventHandler(this.button1_Click);
                //this.button1.Click += new EventHandler(this.Zakoncz_Skanowanie);
                //this.button1.Text = "Zakończ";
                this.ZacznijSkanowanie();
            }
        }


        private void Zakoncz_Skanowanie(object sender, EventArgs e)
        {
            //powrot.Visible = true;
            this.Skanowanie.Abort();
            Skaner.Przewij_Skanowanie();
            //this.button1.Click -= new EventHandler(this.Zakoncz_Skanowanie);
            //this.button1.Click += new EventHandler(this.button1_Click);

            //MessageBox.Show("Zakończyłem tworzenie dokumentu DL:"+nr_inw);
            //this.myParent.Show();
            //base.Close();
        }

        private void powrot_Click(object sender, EventArgs e)
        {
            //this.Skanowanie.Abort();
            Wlasciwosci.CurrentOperacja = "21";
            Skaner.Przewij_Skanowanie();
            this.myParent.Show();
            this.Hide();

        }



        private void button1_Anuluj(object sender, EventArgs e)
        {

        }
        private void ZacznijSkanowanie()
        {
            StringBuilder login = new StringBuilder();
            Skaner.UstawTryb_String(login);
            Skaner.Zacznij_Skanowanie();
            this.Skanowanie = new Thread(() => this.CheckString(login));
            this.Skanowanie.IsBackground = true;
            this.Skanowanie.Start();
        }



        private void CheckString(StringBuilder cc)
        {
            EventHandler method = null;
            while (true)
            {
                if (cc.ToString() != "")
                {
                    if (method == null)
                    {
                        method = delegate
                        {
                            //this.vvv();

                            this.dodawanie(cc.ToString());
                        };
                        //method = () => this.dodawanie(cc.ToString());
                    }
                    this.Invoke(method);
                    return;
                }
                Thread.Sleep(200);
            }
        }








        private void GotFocus_TextBox(object sender, EventArgs e)
        {
            Skaner.Przewij_Skanowanie();
            TextBox Pole_Tekstowe = (TextBox)sender;

            AktualnyTextBox = Pole_Tekstowe;

            if (Pole_Tekstowe.Text == "RRMMDD")
            {
                Pole_Tekstowe.Text = "";
            }


            ZacznijSkanowanie();
        }


        private void LostFocus_TextBox(object sender, EventArgs e)
        {
            AktualnyTextBox = null;
            Skaner.Przewij_Skanowanie();

            TextBox Pole_Tekstowe = (TextBox)sender;

            if (kod_textbox == Pole_Tekstowe && kod_textbox.Text != "")
            {
                string zapytanie = "SELECT k.id,k.kod, k.ean, k.ilosc_w_opakowaniu, k.gln, k.ean_jednostki, k.ilosc_dni_przydatnosci,k.wymagana_partia, k.wymagana_data_waznosci, k.wymagana_dataprod FROM kody k WHERE k.system_id=\"" + Wlasciwosci.system_id_id + "\" and (TRIM(LEADING '0' FROM ean)=\"" + kod_textbox.Text.TrimStart('0') + "\" or TRIM(LEADING '0' FROM ean_jednostki)=\"" + kod_textbox.Text.TrimStart('0') + "\" or TRIM(LEADING '0' FROM kod)=\"" + kod_textbox.Text.TrimStart('0') + "\") and k.active=1 ORDER BY k.id asc ;";
                object obj2 = BazaDanychExternal.Wyczytaj_Tabele(zapytanie);
                DataTable tabela1 = (DataTable)obj2;

                if (tabela1.Rows.Count == 0)
                {
                    MessageBox.Show("Brak kodu w kartotece. Należy dodać lub pzypisać EAN do kodu. ");
                    return;
                }
                else
                {
                    if (tabela1.Rows.Count == 1)
                    {
                        kod_textbox.Text = tabela1.Rows[0]["kod"].ToString();
                        ilosc_textBox.Focus();
                    }
                    else
                    {

                        PoleWybor XA = new PoleWybor(this, tabela1);

                        if (XA.ShowDialog() == DialogResult.OK)
                        {
                            if (XA.wartosc_wybrana != "")
                            {
                                kod_textbox.Text = XA.wartosc_wybrana;
                                ilosc_textBox.Focus();
                            }
                        }
                    }

                    zapytanie = "SELECT k.id,k.kod, k.ean, k.ilosc_w_opakowaniu, k.gln, k.ean_jednostki, k.ilosc_dni_przydatnosci,k.wymagana_partia, k.wymagana_data_waznosci, k.wymagana_dataprod FROM kody k WHERE k.system_id=\"" + Wlasciwosci.system_id_id + "\" and (TRIM(LEADING '0' FROM ean)=\"" + kod_textbox.Text.TrimStart('0') + "\" or TRIM(LEADING '0' FROM ean_jednostki)=\"" + kod_textbox.Text.TrimStart('0') + "\" or TRIM(LEADING '0' FROM kod)=\"" + kod_textbox.Text.TrimStart('0') + "\") and k.active=1 ORDER BY k.id desc limit 1;";
                    object obj3 = BazaDanychExternal.Wyczytaj_Tabele(zapytanie);
                    DataTable tabela = (DataTable)obj3;

                    if (tabela.Rows.Count == 0)
                    {
                        MessageBox.Show("Brak kodu w kartotece. Należy dodać lub pzypisać EAN do kodu. ");
                        return;
                    }
                    else
                    {

                        /*
                        if (!(tabela.Rows[0]["ilosc_w_opakowaniu"].ToString() == "" || tabela.Rows[0]["ilosc_w_opakowaniu"].ToString() == "0"))
                        {
                            ilo = tabela.Rows[0]["ilosc_w_opakowaniu"].ToString();
                        }
                        ilosc_dni_przydatnosci = tabela.Rows[0]["ilosc_dni_przydatnosci"].ToString();
                        wymagaj_data_waznosci = tabela.Rows[0]["wymagana_data_waznosci"].ToString();
                        wymagaj_dataprod = tabela.Rows[0]["wymagana_dataprod"].ToString();
                        wymagaj_lot = tabela.Rows[0]["wymagana_partia"].ToString();
                        kod.Text = tabela.Rows[0]["kod"].ToString();
                        */
                    }
                }
            }

        }





        // 'External' - wyszukuje w bazie etykiete i wypełnia pola
        private void dodawanie(string ops)
        {

            Skaner.Przewij_Skanowanie();


            if (!BazaDanychExternal.SprawdzCzyIstniejePolaczenie())
            {
                MessageBox.Show("Brak połączenia z bazą danych , spróbuj ponownie.");
                this.ZacznijSkanowanie();
            }
            else
            {
                if (ops.Substring(0, 2) == "MP")
                {
                    Regex regex = new Regex("-");
                    string[] words = null;
                    words = regex.Split(ops.Substring(3, ops.Length - 3));
                    hala = words[0].ToString();
                    if (words[1].ToString() != "0")// wyjątek jak miejsce jest 0
                    {
                        regal = words[1].ToString().TrimStart(new Char[] { '0' });
                    }
                    else
                    {
                        regal = words[1].ToString();
                    }
                    if (words[2].ToString() != "0")// wyjątek jak miejsce jest 0
                    {
                        miejsce = words[2].ToString().TrimStart(new Char[] { '0' });
                    }
                    else
                    {
                        miejsce = words[2].ToString();
                    }

                    string zapytanie = "";
                    zapytanie = "SELECT m.poziom FROM miejsca m where  poziom!='' and hala='" + hala + "' and regal='" + regal + "'  and miejsce='" + miejsce + "' and m.widoczne=1  GROUP BY m.poziom order by poziom DESC;";
                    object obj2 = BazaDanychExternal.Wyczytaj_Tabele(zapytanie);
                    DataTable tabela = (DataTable)obj2;

                    if (tabela.Rows.Count < 1)
                    {
                        MessageBox.Show("Brak takiego miejsca w bazie. Spróbuj ponownie.");
                        ZacznijSkanowanie();
                        return;

                    }
                    else if (tabela.Rows.Count == 1)
                    {
                        this.ustaw_poziom(tabela.Rows[0]["poziom"].ToString());                        
                    }
                    else
                    {
                        Wybierz_Poziom_ZM qw = new Wybierz_Poziom_ZM(this, hala, regal, miejsce);
                        qw.ShowDialog();
                        qw.Close();
                    }



                    
                    kod_textbox.Focus();



                    this.ZacznijSkanowanie();
                    return;
                    //poziom_comboBox4.SelectedIndex = poziom_comboBox4.Items.IndexOf(words[3].ToString());
                }
                //else
                    //MessageBox.Show("1");
                //AktualnyTextBox.Text = ops;


                if (ops.Length > 9)
                {
                    //string cleanString = Regex.Replace(ops, @"[^a-zA-Z0-9\-\.\,]", "");
                    //ops = ops.Replace("\r\n", string.Empty);
                    //MessageBox.Show("2");
                    Dictionary<Etykieta.AII, string> ff = Etykieta.Parse(ops, false);
                    //MessageBox.Show("3");
                    foreach (Etykieta.AII tt in ff.Keys)
                    {
                        //MessageBox.Show("Dla kodu:" + tt.AI + "   ,   Otrzymujemy wartość:" + ff[tt].ToString());
                        //Console.WriteLine("Dla kodu:" + tt.AI + "   ,   Otrzymujemy wartość:" + ff[tt].ToString());
                        if (tt.AI == "91") { kod_textbox.Focus(); kod_textbox.Text = Regex.Replace(ff[tt].ToString(), @"[^a-zA-Z0-9\-\.\,\\\/]", ""); ilosc_textBox.Focus(); }
                        if (tt.AI == "02") { kod_textbox.Focus(); kod_textbox.Text = Regex.Replace(ff[tt].ToString(), @"[^a-zA-Z0-9\-\.\,\\\/]", ""); ilosc_textBox.Focus(); }
                        //MessageBox.Show("4");



                    }
                }


                /*
                if (AktualnyTextBox == kod_textbox)
                {
                    AktualnyTextBox.Text = "";
                    MessageBox.Show("5");


                    if (kod_textbox.Text == "")
                    {
                        MessageBox.Show("6");
                        ops = ops.TrimStart('0');

                        string zapytanie = "SELECT k.id,k.kod, k.ean, k.ilosc_w_opakowaniu, k.gln, k.ean_jednostki, k.ilosc_dni_przydatnosci,k.wymagana_partia, k.wymagana_data_waznosci, k.wymagana_dataprod FROM kody k WHERE k.system_id=\"" + Wlasciwosci.system_id_id + "\" and (TRIM(LEADING '0' FROM ean)=\"" + ops + "\" or TRIM(LEADING '0' FROM ean_jednostki)=\"" + ops + "\" or TRIM(LEADING '0' FROM kod)=\"" + ops + "\") and k.active=1 ORDER BY k.id asc ;";
                        object obj2 = BazaDanychExternal.Wyczytaj_Tabele(zapytanie);
                        DataTable tabela1 = (DataTable)obj2;

                        if (tabela1.Rows.Count == 0)
                        {
                            MessageBox.Show("Brak kodu w kartotece. Należy dodać lub pzypisać EAN do kodu. ");
                            return;
                        }
                        else
                        {
                            if (tabela1.Rows.Count == 1)
                            {
                                kod_textbox.Text = tabela1.Rows[0]["kod"].ToString();
                            }
                        }
                    }
                }

                */



            }
        }

        public void ustaw_poziom(string aa)
        {
            poziom = aa;
            miejsce_textBox1.Text = "H:" + hala + " " + regal + "-" + miejsce + "-" + poziom;
        }

        // 'External' po każdym dodaniu przelicza ile jest zrobionego
        private void stan_inwentaryzacji()
        {
            string zapytanie = "";
            zapytanie = "SELECT sum(if(ilosc_spisana is null,0,1)) as ilosc_zliczona, count(1) as stan FROM inwentaryzacja i WHERE i.inwentaryzacja_id=" + inwentaryzacja_id + " and kod!='10101';";

            object obj2 = BazaDanychExternal.Wyczytaj_Tabele(zapytanie);

            DataTable table = (DataTable)obj2;
            if (table.Rows.Count < 1)
            {
                ilosc_etykiet.Text = "Brak et.";
            }
            else
            {
                ilosc_etykiet.Text = table.Rows[0]["ilosc_zliczona"].ToString() + "/" + table.Rows[0]["stan"].ToString();
            }
        }


        private string External_Count_Inwent()
        {
            string zapytanie = "";
            zapytanie = "SELECT sum(if(ilosc_spisana is null,0,1)) as ilosc_zliczona, count(1) as stan FROM inwentaryzacja i WHERE i.inwentaryzacja_id=" + _inw[comboBox1.SelectedIndex] + ";";
            string aa = (string)BazaDanychExternal.Wyczytaj_Jedna_Wartosc(zapytanie);
            return aa;
        }
































        private void czysc()
        {

            //skaner = "";
            //sap_gora_czesc2 = "";
            //kod = "";
            //podkod = "";
            kod_textbox.Text = "";

        }



        private void timer1_Tick(object sender, EventArgs e)
        {
            this.Close();
            myParent.Show();
        }



        #region fullscreen
        private void fullscreenmode()
        {
            this.Width = Screen.PrimaryScreen.Bounds.Width;
            this.Height = Screen.PrimaryScreen.Bounds.Height;
            this.Left = 0;
            this.Top = 0;
        }
        #endregion

        private void comboBox1_SelectedIndexChanged(object sender, EventArgs e)
        {
            //nr_inw = _inw[comboBox1.SelectedIndex];

        }

        private void button4_Click(object sender, EventArgs e)
        {
            Skaner.Przewij_Skanowanie();

            if (_inw[comboBox1.SelectedIndex].ToString() == "0")
            {
                object wynik = BazaDanychExternal.Wyczytaj_Jedna_Wartosc("select max(inwentaryzacja_id)from inwentaryzacja");
                //BazaDanychExternal.DokonajUpdate("insert into delivery(delivery_nr,delivery_internal,delivery_date,pracownik_id,delivery_ts) VALUES(" + (Convert.ToInt32((string)wynik) + 1).ToString() + ",1,date(sysdate())," + Wlasciwosci.id_Pracownika + ",sysdate())");
                inwentaryzacja_id = Convert.ToInt32((string)wynik) + 1;
            }
            else
            {
                inwentaryzacja_id = _inw[comboBox1.SelectedIndex];
                //DLNumber.Text = _inw[comboBox1.SelectedIndex].ToString();
            }
            comboBox1.Enabled = false;
            label5.Text = inwentaryzacja_id+"";
            button1.Visible = true;
            miejsce_textBox1.Focus();
            this.ZacznijSkanowanie();
        }








    }
}