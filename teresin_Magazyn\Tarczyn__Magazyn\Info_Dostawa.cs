﻿using System;
using System.Linq;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using System.Threading;
using System.Text.RegularExpressions;

namespace Tarczyn__Magazyn
{
    public partial class InfoDostawa : Form
    {

        ActionMenu parent = null;

        static string hala="";
        static string regal="";
        static string miejsce="";
        static string poziom="";


        string NR_DOK = "";

        string operac_id_global = "";


        List<string> _regal = new List<string>();

        static int miejsce_id = 0;

        public InfoDostawa(ActionMenu myParent)
        {
            if (Wlasciwosci.GNG != "")
            {
                BazaDanychExternal.ChangeHost(Wlasciwosci.GNG, Wlasciwosci.Varhost, Wlasciwosci.VarLoginMYSQL, Wlasciwosci.VarHasloMYSQL);
            }
            FullScreenMode.OknoOFF(this);
            InitializeComponent();
            parent = myParent;


            _regal.Add("");
            _regal.Add("STR");
            _regal.Add("RMP_A");
            _regal.Add("RMP_B");

            BindingSource bs = new BindingSource();
            bs.DataSource = _regal;
            comboBox1.DataSource = bs;

            string zapytanie = "SELECT last FROM docnumber d WHERE d.name='operacja_id';";

            operac_id_global = (string)BazaDanychExternal.Wyczytaj_Jedna_Wartosc(zapytanie);

            string zapyt = "update docnumber d set last=last+1 WHERE d.name='operacja_id'; ";
            BazaDanychExternal.Wyczytaj_Jedna_Wartosc(zapyt);

        }
        
        List<string> Etykiety = new List<string>();
        Thread Skanowanie = null;


        private void ZacznijSkanowanie()
        {

            StringBuilder login = new StringBuilder();
            Skaner.UstawTryb_String(login);
            Skaner.Zacznij_Skanowanie();
            Skanowanie = new Thread(() => CheckString(login));
            Skanowanie.IsBackground = true;
            Skanowanie.Start();
        }
        private void CheckString(StringBuilder cc)
        {
            while (true)
            {
                if (cc.ToString() != "")
                {
                    this.Invoke(new EventHandler(delegate
                    {
                        
                        dodawanie(cc.ToString());
                    }));
                    return;
                }
                Thread.Sleep(200);
            }
        }
        private void Zakoncz_Skanowanie()
        {
            try
            {
                Skanowanie.Abort();
                Skaner.Przewij_Skanowanie();
            }
            catch
            {
            }
        }

        private void Exit_Click_1(object sender, EventArgs e)
        {
            Zakoncz_Skanowanie();
            BazaDanychExternal.ChangeHost(Wlasciwosci.Varbaza, Wlasciwosci.Varhost, Wlasciwosci.VarLoginMYSQL, Wlasciwosci.VarHasloMYSQL);
            parent.Show();
            this.Close();
        }


        private void button2_Click(object sender, EventArgs e)
        {
          

        }


        private void dodawanie(string gg)
        {

            Zakoncz_Skanowanie();
            string test11 = "";

            if (textBox2.Text != "")
            {
                try
                {
                    Convert.ToInt32(textBox2.Text);
                    test11 = " and kolektor_id =" + textBox2.Text + " ";
                }
                catch
                {
                    MessageBox.Show("Błędny kolektor ID.");
                    ZacznijSkanowanie();
                    return;
                }
            }

            if (_regal[comboBox1.SelectedIndex].ToString() != "")
            {
                if (textBox4.Text == "")
                {
                    MessageBox.Show("Wypełnij nr regal");
                    ZacznijSkanowanie();
                    return;
                }
                else
                {
                    hala = "1";
                    regal = _regal[comboBox1.SelectedIndex].ToString();
                    miejsce = textBox4.Text;
                    poziom = "1";
                    miejsce_id = pobierz_id_miejsca(hala, regal, miejsce, poziom);
                }
            }
            else
            {
                miejsce_id = 0;
            }







            //MessageBox.Show("select wartosc4 from kolektor where wartosc1='" + gg + "' order by id desc LIMIT 1");
           
            object test = BazaDanychExternal.Wyczytaj_Jedna_Wartosc("select wartosc4 from kolektor where wartosc1='" + gg + "' and wartosc4 is not null and wartosc4!='' "+ test11+"  order by id desc LIMIT 1");
            if (test == null)
            {
                label2.Text = "";
                Magazyn_Label.Text = "Brak etykiety w bazie danych od klienta.";
                ZacznijSkanowanie();
                return;
            }
            if (test.ToString() == "")
            {
                label2.Text = "";
                Magazyn_Label.Text = "Brak etykiety w bazie danych od klienta.";
                ZacznijSkanowanie();
                return;
            }
            //object test1 = BazaDanychExternal.Wyczytaj_Tabele("SELECT b.id,b.active FROM wmsgg.etykiety_kartony a,wmsgg.etykiety b where karton_ean like'"+gg+"' and a.idetykiety=b.id order by b.id desc LIMIT 1");
            object test1 = BazaDanychExternal.Wyczytaj_Tabele("SELECT b.id,b.active,b.system_id,b.miejscep  FROM etykiety b where b.etykieta_klient like'" + gg + "' order by b.id desc LIMIT 1");

            if(test1==null)
            {
                label2.Text = "";
                Magazyn_Label.Text = "Brak etykiety w bazie danych od magazynu.";
                ZacznijSkanowanie();
                return;
            }
            DataTable Wynik = (DataTable)test1;
            if(Wynik.Rows.Count<1)
            {
                label2.Text = "";
                Magazyn_Label.Text = "Brak etykiety w bazie danych od magazynu.";
                ZacznijSkanowanie();
                return;
            }
            if(Wynik.Rows[0]["active"].ToString()=="0")
            {
                label2.Text = "Etykieta system_idowa ' "+Wynik.Rows[0]["id"].ToString()+"  ' jest nieaktywna.";
                Magazyn_Label.Text = "Etykieta klienta ' "+gg+" ' jest nieaktywna.";
                ZacznijSkanowanie();
                return;
            }


            //object test2 = BazaDanychExternal.Wyczytaj_Jedna_Wartosc("select wartosc4 from kolektor where wartosc1='" + gg + "' order by id desc LIMIT 1");




            if (_regal[comboBox1.SelectedIndex].ToString() != "")
            {
                DataTable temp = (DataTable)BazaDanychExternal.Wyczytaj_Tabele("select id,system_id,miejscep  from etykiety where etykieta_klient='" + gg + "' and (active=1 or active is null) order by id desc limit 1");


                if (NR_DOK == "") { NR_DOK = (string)BazaDanychExternal.Wyczytaj_Jedna_Wartosc("select max(doc_nr)+1 FROM zmianym"); }


                if (miejsce_id != 0)
                {
                    //
                    for (int t = 0; t < Wynik.Rows.Count; t++)
                    {
                        BazaDanychExternal.DokonajUpdate("insert into zmianym(typ,doc_nr,pracownik_id,data,etykieta,system_id,stare_m,nowe_m,doc_internal,stat,tszm) values(" +
                            "'ZM'," + NR_DOK + "," + Wlasciwosci.id_Pracownika + ",sysdate()," + Wynik.Rows[t]["id"].ToString() + "," + Wynik.Rows[t]["system_id"].ToString() + "," + Wynik.Rows[t]["miejscep"].ToString() + "," + miejsce_id + ",3,1,sysdate())");
                        BazaDanychExternal.DokonajUpdate("update etykiety set miejscep = " + miejsce_id + " where id=" + Wynik.Rows[t]["id"].ToString() + " and (active=1 or active is null) limit 1");

                        label6.Text = "ZM na " + regal + "-" + miejsce + "-" + poziom;
                    }

                }
                else
                {
                    MessageBox.Show("Brak miejsca w bazie");
                    ZacznijSkanowanie();
                    return;
                }

            }
            else
            {
                label6.Text = "";
            }
            Wlasciwosci.system_id_id = Wynik.Rows[0]["system_id"].ToString();

           
            label2.Text = "Etykieta '   "+gg+"   ' powinna trafić na magazyn : ";
            Magazyn_Label.Text = test.ToString();

            string doc_nr = "0";
            if (textBox2.Text != "")
            {
                doc_nr = textBox2.Text;
            }
            BazaDanychExternal.DokonajUpdate("insert into operacje(etykieta_id, doc_type, doc_nr, imie_nazwisko, typ_operacji,system_id,wozek,operac_id,ilosc) values('" + Wynik.Rows[0]["id"].ToString() + "','INF','" + doc_nr + "','" + Wlasciwosci.imie_nazwisko + "','INFO_WY','" + Wlasciwosci.system_id_id + "','" + Wlasciwosci.wozek + "','" + operac_id_global + "','1');");


            ZacznijSkanowanie();
        }


        private int pobierz_id_miejsca(string hala_local, string regal_local, string miejsce_local, string poziom_local) //pobiera
        {
            //if(nr_etykiety)
            string zapytanie = "";
            zapytanie = "select id from miejsca m where hala='" + hala_local + "' and regal='" + regal_local + "' and miejsce='" + miejsce_local + "' and poziom='" + poziom_local + "' limit 1;";

            object obj2 = BazaDanychExternal.Wyczytaj_Tabele(zapytanie);

            DataTable tabela = (DataTable)obj2;
            if (tabela.Rows.Count < 1)
            {  //sprawdza 
                return 0;
            }
            else
            {
                return Convert.ToInt32(tabela.Rows[0]["id"]);
            }


        }

        private string DajDate(string gg)
        {

            gg = gg.Replace("'", "");
            //MessageBox.Show(gg);
            if (gg == "null")
            {
                return "null";
            }

            DateTime gh = DateTime.ParseExact(gg,"dd-MM-yyyy",null);
            return "'"+gh.ToString("yyyy-MM-dd")+"'";
                
            
        }
    

        private string sprawdz_czy_null(string bb)
        {
            if (bb == "")
            {

                return "null";
            }
            else
            {

                try
                {
                    Convert.ToDouble(bb);
                    return bb;
                }
                catch
                {
                    return "'" + bb + "'";
                }
            }

        }

        private void Zakoncz_Click(object sender, EventArgs e)
        {
     
        }

        private void WczytywanieNaLinie_Load_1(object sender, EventArgs e)
        {
            ZacznijSkanowanie();
        }

        private void button2_Click_1(object sender, EventArgs e)
        {
           
        }

        private void button1_Click(object sender, EventArgs e)
        {
            dodawanie(textBox1.Text);
            textBox1.Text = "";
        }





    }
}
