<?php

include_once '../Db.class.php';
include_once '../funkcje.inc';

$baza_danych = "wmsgg";
$db = new Db("localhost", $baza_danych);

$paleta_id = $_GET['paleta_id'];

if (empty($paleta_id)) {
    return xml_from_indexed_array(
        array('komunikat' => "Nie podano ID palety")
    );
}

$sql = "SELECT typypalet_id, tp.opis, de.delivery_id
        FROM wmsgg.etykiety e
        left join palety p on e.paleta_id=p.id
        left join typypalet tp on tp.id=p.typypalet_id
        left join delivery_et de on e.id=de.etykieta_id
        where paleta_id=" . $paleta_id;

$result = $db->mGetResultAsXML($sql);

if (empty($result)) {
    return xml_from_indexed_array(
        array('komunikat' => "Nie znaleziono palety o podanym ID")
    );
}

// Sprawdzenie czy paleta była już kontrolowana
$sql_kontrola = "SELECT id, ts, pracownik_id 
                 FROM palety_kontrola 
                 WHERE paleta_id = " . $paleta_id . "
                 ORDER BY ts DESC LIMIT 1";
$kontrola = $db->mGetResultAsXML($sql_kontrola);

if (!empty($kontrola)) {
    $data_kontroli = date('Y-m-d H:i:s', strtotime($kontrola[0]['ts']));
    $result[0]['komunikat'] = "Paleta była już kontrolowana w czasie " . $data_kontroli;
    $result[0]['byla_kontrolowana'] = "1";
} else {
    $result[0]['komunikat'] = "OK";
    $result[0]['byla_kontrolowana'] = "0";
}

return xml_from_indexed_array($result[0]);
