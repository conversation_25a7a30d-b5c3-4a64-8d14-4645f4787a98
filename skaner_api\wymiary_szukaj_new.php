<?php

//include_once 'db_pdo.php';
include_once 'Db.class.php';
include_once 'funkcje.inc';
include_once 'ArtsanaDb.class.php';
error_reporting(E_ALL);
ini_set('display_errors', 1);
$artsanaDb = new ArtsanaDb();
$db = new Db();
//
//if (empty($argv)) {
//    //$argv = array();
//    $argv[1] = $_GET['db'];
//    $argv[2] = $_GET['akcja'];
//}
//
$baza_danych = $_GET['db'];
$akcja = $_GET['akcja'];
$scan = $_GET['scan'];
$doc_ref = $_GET['doc_ref'];


$response = array();




$komunikat = "OK";

if ($akcja == "szukaj") {
    $wymiar_wyswietlany = "";
    $delivery_wymiary_head_id = "";
    if (strlen($scan) > 16) {

        $response = wyswietl_doc_ref($scan, $artsanaDb);
        //print_r($response);
        if (empty($response)) {
            return xml_from_indexed_array(array('komunikat' => "Nie znaleziono kodu"));
        }
        $szukany_doc_ref = "";
        if (strpos($doc_ref, $response[0]['delivery_number']) !== false) {
            $szukany_doc_ref = $doc_ref;
        } else {
            $szukany_doc_ref = sortuj_doc_ref($response[0]['delivery_number'] . "," . $doc_ref);
        }

        $result2 = szukaj_head($szukany_doc_ref, $db);
        //print_r()

        if (!empty($result2)) {
            $delivery_wymiary_head_id = $result2[0]['id'];
        }
        $wymiar_wyswietlany = 'KARTON';
        $response = array_merge(array('komunikat' => $komunikat, 'wymiar_wyswietlany' => $wymiar_wyswietlany, 'delivery_wymiary_head_id' => $delivery_wymiary_head_id), $response[0]);
        
        
    } else if (substr($scan, 0, 2) == "DL") {


        $szukany_doc_ref = "";
        if (strpos($doc_ref, $scan) !== false) {
            $szukany_doc_ref = $doc_ref;
        } else {
            $szukany_doc_ref = sortuj_doc_ref($scan . "," . $doc_ref);
        }


        $result2 = szukaj_head($szukany_doc_ref, $db);
        //print_r($result2);

        if (!empty($result2)) {
            $delivery_wymiary_head_id = $result2[0]['id'];
        }
        $response = array('komunikat' => $komunikat,  'delivery_wymiary_head_id' => $delivery_wymiary_head_id);
        
        
    } else {
        $response = wyswietl_ean("wmsgg", $scan, $db);
        $wymiar_wyswietlany = 'KARTON';

        $szukany_doc_ref = "";
//        if (strpos($doc_ref, $response[0]['delivery_number']) !== false) {
//            $szukany_doc_ref = $doc_ref;
//        } else {
//            $szukany_doc_ref = sortuj_doc_ref($response[0]['delivery_number'] . "," . $doc_ref);
//        }
        
        $szukany_doc_ref = $doc_ref;



        $result2 = szukaj_head($szukany_doc_ref, $db);


        if (!empty($result2)) {
            $delivery_wymiary_head_id = $result2[0]['id'];
        }
        
        $response = array_merge(array('komunikat' => $komunikat, 'wymiar_wyswietlany' => $wymiar_wyswietlany, 'delivery_wymiary_head_id' => $delivery_wymiary_head_id), $response[0]);
    }



    


    return xml_from_indexed_array($response);
}

function wyswietl_ds($baza_danych, $scan, $db) {
    $sql = "SELECTtp.opis,de.delivery_id,'' as wysokosc, '',pal_dlugosc as dlugosc, pal_szerokosc as szerokosc,
'' as waga,
'' as  wysokosc FROM $baza_danych.palety p
left join $baza_danych.typypalet tp on typypalet_id=tp.id
left join $baza_danych.etykiety e on e.paleta_id=p.id
left join $baza_danych.delivery_et de on e.id=de.etykieta_id where p.id=$scan limit 1; ";
    $result = $db->mGetResultAsXML($sql);

    return $result;
}

function wyswietl_ean($baza_danych, $scan, $db) {
    $sql = "SELECT  TRIM(TRAILING '.' FROM TRIM(TRAILING '0' FROM k.dlugosc_szt_mm/10))  as dlugosc, TRIM(TRAILING '.' FROM TRIM(TRAILING '0' FROM k.szerokosc_szt_mm/10))  as szerokosc, TRIM(TRAILING '.' FROM TRIM(TRAILING '0' FROM k.wysokosc_szt_mm/10)) as wysokosc,

if(TRIM(LEADING '0' FROM k.ean_jednostki)='$scan',TRIM(TRAILING '.' FROM TRIM(TRAILING '0' FROM k.waga_szt_kg)) ,
if(TRIM(LEADING '0' FROM k.ean)='$scan',TRIM(TRAILING '.' FROM TRIM(TRAILING '0' FROM (waga_szt_kg*ilosc_w_opakowaniu)))  ,
if(TRIM(LEADING '0' FROM k.ean_opakowanie_zbiorcze)='$scan',TRIM(TRAILING '.' FROM TRIM(TRAILING '0' FROM (waga_szt_kg*ilosc_szt_w_zbiorczym))) ,''))) as waga,
    '' as delivery_id

FROM $baza_danych.kody k
WHERE (TRIM(LEADING '0' FROM k.ean_jednostki)='$scan'
OR  TRIM(LEADING '0' FROM k.ean)='$scan'
OR TRIM(LEADING '0' FROM k.ean_opakowanie_zbiorcze)='$scan')
 order by k.id desc limit 1 ;";
    //echo $sql;
    $result = $db->mGetResultAsXML($sql);
    if (empty($result)) {
        $result[0]['waga'] = $result[0]['waga'] = "";
    }
//    print_r($result);
//    if($result['waga']=="0"){$result['waga']="";}

    return $result;
}

function wyswietl_doc_ref($scan, $artsanaDb) {
    $sql = "SELECT delivery_number,_hucontent.Lunghezza as dlugosc,
_hucontent.Larghezza as szerokosc, _hucontent.Altezza as wysokosc,
Lordo_KG AS waga
 FROM artsana._hu
left join artsana._hucontent on _hu.hu_sscc_number=_hucontent.Handling_Unit
where _hu.hu_sscc_number='" . ltrim($scan, '0') . "' limit 1";
    //echo $sql;
    $result = $artsanaDb->mGetResultAsXML($sql);

    return $result;
}

function szukaj_head($scan, $db) {
    $sql = 'SELECT id FROM wmsgg.delivery_wymiary_head
where dokument_dl="' . $scan . '" limit 1';
    //echo $sql;
    $result = $db->mGetResultAsXML($sql);

    return $result;
}

function sortuj_doc_ref($doc_ref) {
    $dane = "";
    $array = explode(',', $doc_ref);
    //print_r($array);
    sort($array);
    foreach ($array as $value) {
        if ($dane == "") {
            $dane = $value;
        } else {
            $dane .= "," . $value;
        }
    }
    return $dane;
}
