<?php

//include_once 'db_pdo.php';
include_once 'Db.class.php';
include_once 'funkcje.inc';
//error_reporting(E_ALL);
//ini_set('display_errors', 1);

$db = new Db();

if (empty($argv)) {
//$argv = array();
    //$argv[1] = $_GET['db'];
    $argv[2] = $_GET['akcja'];
}
$baza_danych = "wmsgg";
//$baza_danych = $_GET['db'];
$akcja = $_GET['akcja'];
$system_id_id = $_GET['system_id'];

$pob_et_id = $_GET["pob_et_id"];
$zos_et_id = $_GET["zos_et_id"];
$imie_nazwisko = $_GET["imie_nazwisko"];
$docin_id = $_GET["docin_id"];
$docout_id = $_GET["docout_id"];
//$ilosc_przenoszona = $_GET["ilosc_przenoszona"];

//http://25.56.91.22/wmsgg/public/skaner_api/delivery_skanowanie_etykiety.php?db=wmsgg&akcja=szukaj&system_id=6&paleta_id=547144


$komunikat = "OK";


if ($akcja == "konsolidacja") {
    //http://25.56.91.22/wmsgg/public/skaner_api/wms_przepakowywanie_etykiety.php?akcja=przepakowanie&db=wmsgg&system_id=6&imie_nazwisko=Lukasz%20Domanski&etykieta_id=9970475&ilosc_pobierana=2
    $etykieta1 = sprawdz_szukana_etykiete_WMS($pob_et_id, $system_id_id, $baza_danych, $db);
    $etykieta2 = sprawdz_szukana_etykiete_WMS($zos_et_id, $system_id_id, $baza_danych, $db);
    
    if ($pob_et_id == $zos_et_id) {
        $komunikat = "Etykieta pobierana i zostawiana nie mogą być takie same";
        return xml_from_indexed_array(array('komunikat' => $komunikat));
    }
    
    
    if (empty($etykieta1)) {
        $komunikat = "Nie powiodło się, Brak w bazie katywnej etykiety " . $pob_et_id." DS".$etykieta1[0]['paleta_id'];
        return xml_from_indexed_array(array('komunikat' => $komunikat));
    }
    if (empty($etykieta1)) {
        $komunikat = "Nie powiodło się, Brak w bazie katywnej etykiety " . $pob_et_id." DS".$etykieta1[0]['paleta_id'];
        return xml_from_indexed_array(array('komunikat' => $komunikat));
    }
    if (empty($etykieta2)) {
        $komunikat = "Nie powiodło się, Brak w bazie katywnej etykiety " . $zos_et_id." DS".$etykieta2[0]['paleta_id'];
        return xml_from_indexed_array(array('komunikat' => $komunikat));
    }
    if ($etykieta1[0]['kod'] != $etykieta2[0]['kod']) {
        $komunikat = "Nie powiodło się, Różne kody " . $etykieta1[0]['kod'] . ";  " . $etykieta2[0]['kod'];
        return xml_from_indexed_array(array('komunikat' => $komunikat));
    }
    if ($etykieta1[0]['lot'] != $etykieta2[0]['lot']) {
        $komunikat = "Nie powiodło się, Różne loty " . $etykieta1[0]['lot'] . ";  " . $etykieta2[0]['lot'];
        return xml_from_indexed_array(array('komunikat' => $komunikat));
    }
    if ($etykieta1[0]['status_system_nazwa'] != $etykieta2[0]['status_system_nazwa']) {
        $komunikat = "Nie powiodło się, Różne statusy " . $etykieta1[0]['status_system_nazwa'] . ";  " . $etykieta2[0]['status_system_nazwa'];
        return xml_from_indexed_array(array('komunikat' => $komunikat));
    }
    if ($etykieta1[0]['nr_dl'] != $etykieta1[0]['delivery_id']) {
        $komunikat = "Nie powiodło się, DS".$etykieta1[0]['paleta_id']." zarezerwowana na DL " . $etykieta1[0]['nr_dl'] . ";  " . $etykieta1[0]['delivery_id'];
        return xml_from_indexed_array(array('komunikat' => $komunikat));
    }
    if ($etykieta2[0]['nr_dl'] != $etykieta2[0]['delivery_id']) {
        $komunikat = "Nie powiodło się, DS".$etykieta2[0]['paleta_id']." zarezerwowana na DL " . $etykieta2[0]['nr_dl'] . ";  " . $etykieta2[0]['delivery_id'];
        return xml_from_indexed_array(array('komunikat' => $komunikat));
    }


    //print_r($etykieta1);

    //return;
    $numer = docnumber_increment($baza_danych, "RP", $db);
    $kontrah_wew_id = get_kontrah_wew($baza_danych, $system_id_id, $db);
    
    $pracownik_id = get_pracownik($baza_danych, $imie_nazwisko, $db);
    $ilosc_pobierana = get_ilosc_etykiety($baza_danych, $pob_et_id, $system_id_id, $db);
    $ilosc_zostawiana = get_ilosc_etykiety($baza_danych, $zos_et_id, $system_id_id, $db);


//    if ($ilosc_przenoszona > $ilosc_pobierana || $ilosc_przenoszona < 0) {
//        $komunikat = "Niewłaściwa ilość przenoszona";
//        return xml_from_indexed_array(array('komunikat' => $komunikat));
//    }
//    echo $ilosc_zostawiana;
//    return;
//    if (empty($system_id_id))
//        $system_id_id = 1;

    $ilosc_zostawiana = ($ilosc_pobierana + 0) + ($ilosc_zostawiana + 0);
    if (empty($docout_id)) {
        $docout_id = tworz_dokument_docout($baza_danych, "RP-", $numer, $pracownik_id, $kontrah_wew_id, $db);
    }
    if (empty($docin_id)) {
        $docin_id = tworz_dokument_docin($baza_danych, "RP", $numer, $pracownik_id, $kontrah_wew_id, $db);
    }



    $id_pobierana = przepakowanie_etykiety_step2_ilosc_pobierana($baza_danych, $zos_et_id, $docin_id, $ilosc_zostawiana, $db);
    przepakowanie_etykiety_dezaktywowana($baza_danych, $pob_et_id, $docout_id, $db);
    przepakowanie_etykiety_dezaktywowana($baza_danych, $zos_et_id, $docout_id, $db);

//
//    echo "<br>" . $ilosc_zostawiana;
//    echo "<br>" . $docout_id;
//    echo "<br>" . $docin_id;
//    echo "<br>" . $id_pobierana;
    $nowa = sprawdz_szukana_etykiete_WMS($id_pobierana, $system_id_id, $baza_danych, $db);

    return xml_from_indexed_array(array('komunikat' => $komunikat, 'docin_id' => $docin_id, 'docout_id' => $docout_id, 'kod' => $nowa[0]['kod'], 'ilosc' => $nowa[0]['ilosc'],'paleta_id' => $nowa[0]['paleta_id']));
}

function sprawdz_szukana_etykiete_WMS($etykieta_id, $system_id, $baza_danych, $db) {
    $sql = 'select e.id,e.etykieta_klient,k.kod,k.ilosc_w_opakowaniu,k.kod_nazwa,e.lot,e.active,TRIM(TRAILING "." FROM TRIM(TRAILING "0" from e.ilosc)) as ilosc,dl.nr_dl,de.delivery_id,e.paleta_id,ss.nazwa as status_system_nazwa, ss.funkcja_stat, e.miejscep from ' . $baza_danych . '.etykiety e left join ' . $baza_danych . '.kody k on e.kod_id=k.id left join ' . $baza_danych . '.dlcollect dl ' . ' on dl.nr_et=e.id left join ' . $baza_danych . '.status_system ss  on e.status_id=ss.id left join ' . $baza_danych . '.delivery_et de  on de.etykieta_id=e.id left join ' . $baza_danych . '.delivery d  on de.delivery_id=d.id  where e.active=1 and   (e.id="' . $etykieta_id . '" )  and e.system_id=' . $system_id . '  order by nr_dl asc,e.ilosc asc limit 1'; //having nr_dl is null
    //echo $sql;
    $result2 = $db->mGetResultAsXML($sql);
    return $result2;
}


?>