# Diagramy Przepływów Procesów Inwentaryzacji

## Przegląd

Dokument zawiera szczegółowe diagramy przepływów procesów inwentaryzacji w formacie Mermaid, które można wykorzystać do implementacji aplikacji MAUI.

## 1. Główny Przepływ Inwentaryzacji

```mermaid
graph TD
    A[Start Aplikacji] --> B[Logowanie Pracownika]
    B --> C{Sprawdzenie Połączenia}
    C -->|Brak| D[Tryb Offline]
    C -->|OK| E[Tryb Online]
    
    D --> F[Załaduj Cache Lokalny]
    E --> G[Pobierz Aktywne Inwentaryzacje]
    F --> H[Wybór Typu Inwentaryzacji]
    G --> H
    
    H --> I{Typ Inwentaryzacji}
    I -->|Ogólna| J[Inwentaryzacja Ogólna]
    I -->|Produktowa| K[Inwentaryzacja Produktowa]
    I -->|GG| L[Inwentaryzacja GG]
    I -->|Miejsc| M[Inwentaryzacja Miejsc]
    
    J --> N[Wybór z Listy]
    K --> N
    L --> N
    M --> O[Nowa lub Istniejąca]
    
    O --> N
    N --> P[Inicjalizacja Formularza]
    P --> Q[Start Skanowania]
    Q --> R[Pętla Skanowania]
```

## 2. Proces Skanowania i Walidacji

```mermaid
graph TD
    A[Skanowanie Kodu] --> B{Typ Kodu}
    B -->|Etykieta| C[Wyszukaj w Inwentaryzacji]
    B -->|Paleta DS| D[Wyszukaj Paletę]
    B -->|Kod Produktu| E[Wyszukaj w Kartotece]
    B -->|GS1-128| F[Dekoduj GS1]
    
    C --> G{Znaleziono?}
    D --> G
    E --> G
    F --> H[Wyodrębnij Dane]
    H --> G
    
    G -->|Tak| I[Wypełnij Pola]
    G -->|Nie| J{Typ Inwentaryzacji}
    
    J -->|Produktowa| K[Dodaj Nowy Rekord]
    J -->|Inne| L[Komunikat: Brak w Inwentaryzacji]
    
    I --> M[Sprawdź Lokalizację]
    K --> M
    L --> N[Powrót do Skanowania]
    
    M --> O{Lokalizacja Różna?}
    O -->|Tak| P[Zaznacz Zmianę Miejsca]
    O -->|Nie| Q[Wprowadź Ilość]
    P --> Q
    
    Q --> R[Walidacja Danych]
    R --> S{Dane Poprawne?}
    S -->|Nie| T[Komunikat Błędu]
    S -->|Tak| U[Zapis do Bazy]
    
    T --> Q
    U --> V{Sukces Zapisu?}
    V -->|Tak| W[Aktualizuj Stan]
    V -->|Nie| X[Komunikat Błędu]
    
    W --> Y[Reset Formularza]
    X --> Q
    Y --> N
```

## 3. Proces Zapisu Danych

```mermaid
graph TD
    A[Zatwierdzenie Danych] --> B[Walidacja Lokalna]
    B --> C{Dane Poprawne?}
    C -->|Nie| D[Wyświetl Błędy]
    C -->|Tak| E{Tryb Pracy}
    
    E -->|Online| F[Sprawdź Połączenie]
    E -->|Offline| G[Zapis do SQLite]
    
    F --> H{Połączenie OK?}
    H -->|Nie| I[Przełącz na Offline]
    H -->|Tak| J[Wyślij do API]
    
    I --> G
    J --> K{Odpowiedź API}
    K -->|Success| L[Aktualizuj UI]
    K -->|Error| M[Wyświetl Błąd]
    K -->|Conflict| N[Rozwiąż Konflikt]
    
    G --> O[Dodaj do Kolejki Sync]
    L --> P[Rejestruj Operację]
    M --> Q[Opcja Retry]
    N --> R[Dialog Użytkownika]
    
    O --> P
    P --> S[Aktualizuj Stan Inwentaryzacji]
    Q --> T{Retry?}
    R --> U{Wybór Użytkownika}
    
    T -->|Tak| J
    T -->|Nie| G
    U -->|Zachowaj Lokalne| V[Użyj Danych Lokalnych]
    U -->|Użyj Serwer| W[Użyj Danych Serwera]
    U -->|Anuluj| X[Powrót do Edycji]
    
    V --> P
    W --> P
    X --> A
    S --> Y[Reset Formularza]
    Y --> Z[Powrót do Skanowania]
```

## 4. Synchronizacja Offline

```mermaid
graph TD
    A[Wykrycie Połączenia] --> B[Sprawdź Kolejkę Sync]
    B --> C{Dane do Sync?}
    C -->|Nie| D[Koniec]
    C -->|Tak| E[Pobierz Pierwszą Pozycję]
    
    E --> F[Wyślij do API]
    F --> G{Odpowiedź}
    G -->|Success| H[Oznacz jako Zsync]
    G -->|Error| I{Typ Błędu}
    G -->|Conflict| J[Rozwiąż Konflikt]
    
    I -->|Temporary| K[Zachowaj w Kolejce]
    I -->|Permanent| L[Oznacz jako Błąd]
    
    H --> M[Usuń z Kolejki]
    J --> N[Aktualizuj Dane]
    K --> O[Następna Pozycja]
    L --> O
    M --> O
    N --> M
    
    O --> P{Więcej Danych?}
    P -->|Tak| E
    P -->|Nie| Q[Raport Synchronizacji]
    Q --> R[Powiadom Użytkownika]
    R --> D
```

## 5. Obsługa Zmian Lokalizacji

```mermaid
graph TD
    A[Wykryto Zmianę Lokalizacji] --> B[Pobierz Starą Lokalizację]
    B --> C[Pobierz Nową Lokalizację]
    C --> D{Nowa Lokalizacja Istnieje?}
    
    D -->|Nie| E[Komunikat: Nie znaleziono miejsca]
    D -->|Tak| F[Sprawdź ID Miejsc]
    
    E --> G[Powrót do Edycji]
    F --> H[Utwórz Rekord Zmiany]
    H --> I[Zapisz do Tabeli zmianym]
    I --> J[Aktualizuj Etykietę]
    J --> K[Rejestruj Operację]
    K --> L[Powiadom o Zmianie]
    L --> M[Kontynuuj Zapis]
```

## 6. Workflow Inwentaryzacji Produktowej

```mermaid
graph TD
    A[Start Inwentaryzacji Produktowej] --> B[Załaduj Aktywne Inwentaryzacje]
    B --> C[Wybierz Inwentaryzację]
    C --> D[Inicjalizuj Combo Boxy]
    D --> E[Włącz Skanowanie]
    
    E --> F[Skanuj Kod/Etykietę]
    F --> G{Sprawdź w Inwentaryzacji}
    G -->|Znaleziono| H[Załaduj Dane Etykiety]
    G -->|Nie znaleziono| I{Tryb Auto-Add?}
    
    I -->|Tak| J[Wyszukaj w Kartotece]
    I -->|Nie| K[Komunikat: Brak w Inwentaryzacji]
    
    J --> L{Znaleziono w Kartotece?}
    L -->|Tak| M[Przygotuj Nowy Rekord]
    L -->|Nie| N[Komunikat: Brak w Kartotece]
    
    H --> O[Sprawdź Czy Już Spisane]
    M --> P[Wprowadź Dane]
    K --> Q[Opcja Dodania]
    N --> R[Powrót do Skanowania]
    
    O --> S{Już Spisane?}
    S -->|Tak| T[Aktualizuj Istniejące]
    S -->|Nie| P
    
    P --> U[Wprowadź Ilość]
    T --> U
    Q --> V{Dodać?}
    V -->|Tak| M
    V -->|Nie| R
    
    U --> W[Wybierz Lokalizację]
    W --> X[Waliduj Dane]
    X --> Y{Dane OK?}
    Y -->|Nie| Z[Pokaż Błędy]
    Y -->|Tak| AA[Zapisz Pozycję]
    
    Z --> U
    AA --> BB{Tryb Offline?}
    BB -->|Tak| CC[Zapis Lokalny]
    BB -->|Nie| DD[Zapis Online]
    
    CC --> EE[Dodaj do Kolejki]
    DD --> FF{Sukces?}
    FF -->|Tak| GG[Aktualizuj Stan]
    FF -->|Nie| HH[Komunikat Błędu]
    
    EE --> GG
    GG --> II[Reset Formularza]
    HH --> JJ[Opcja Retry]
    JJ --> KK{Retry?}
    KK -->|Tak| DD
    KK -->|Nie| CC
    
    II --> R
    R --> F
```

## 7. Workflow Inwentaryzacji Miejsc

```mermaid
graph TD
    A[Start Inwentaryzacji Miejsc] --> B[Załaduj Listę + Opcja Nowa]
    B --> C{Wybór}
    C -->|Istniejąca| D[Wybierz z Listy]
    C -->|Nowa| E[Generuj Nowy ID]
    
    D --> F[Załaduj Dane Inwentaryzacji]
    E --> G[Ustaw Nową Inwentaryzację]
    F --> H[Inicjalizuj Formularz]
    G --> H
    
    H --> I[Skanuj Kod Produktu]
    I --> J[Wyszukaj w Kartotece]
    J --> K{Znaleziono?}
    K -->|Nie| L[Komunikat: Brak w Kartotece]
    K -->|Tak| M[Załaduj Dane Produktu]
    
    L --> N[Powrót do Skanowania]
    M --> O[Wprowadź Ilość]
    O --> P[Wybierz Lokalizację]
    P --> Q[Wprowadź Status/JM]
    Q --> R[Zapisz Pozycję]
    R --> S[Aktualizuj Stan]
    S --> T[Reset Formularza]
    T --> N
    N --> I
```

## 8. Obsługa Błędów i Wyjątków

```mermaid
graph TD
    A[Wystąpił Błąd] --> B{Typ Błędu}
    B -->|Połączenie| C[Błąd Sieci]
    B -->|Walidacja| D[Błąd Danych]
    B -->|Autoryzacja| E[Błąd Uprawnień]
    B -->|System| F[Błąd Aplikacji]
    
    C --> G[Sprawdź Dostępność Sieci]
    G --> H{Sieć Dostępna?}
    H -->|Nie| I[Przełącz na Offline]
    H -->|Tak| J[Ponów Próbę]
    
    D --> K[Wyświetl Komunikat]
    K --> L[Podświetl Błędne Pola]
    L --> M[Czekaj na Poprawkę]
    
    E --> N[Wyloguj Użytkownika]
    N --> O[Przekieruj do Logowania]
    
    F --> P[Zapisz Log Błędu]
    P --> Q[Wyświetl Komunikat]
    Q --> R[Opcja Restart]
    
    I --> S[Zapisz w Kolejce Offline]
    J --> T{Sukces?}
    T -->|Tak| U[Kontynuuj]
    T -->|Nie| V[Eskaluj Błąd]
    
    M --> W[Waliduj Ponownie]
    R --> X{Restart?}
    X -->|Tak| Y[Restart Aplikacji]
    X -->|Nie| Z[Kontynuuj z Błędem]
    
    S --> AA[Powiadom o Trybie Offline]
    V --> BB[Komunikat Krytyczny]
    W --> CC{Dane Poprawne?}
    CC -->|Tak| U
    CC -->|Nie| K
    
    AA --> U
    BB --> DD[Opcje Odzyskiwania]
    Y --> EE[Koniec]
    Z --> U
    U --> FF[Normalny Przepływ]
```

## 9. Proces Dekodowania GS1-128

```mermaid
graph TD
    A[Zeskanowano Kod GS1-128] --> B[Sprawdź Format]
    B --> C{Poprawny GS1?}
    C -->|Nie| D[Traktuj jako Zwykły Kod]
    C -->|Tak| E[Parsuj Identyfikatory AI]
    
    E --> F[Wyodrębnij AI 01 - GTIN]
    F --> G[Wyodrębnij AI 10 - Lot]
    G --> H[Wyodrębnij AI 15/17 - Daty]
    H --> I[Wyodrębnij AI 21 - Serial]
    I --> J[Wyodrębnij AI 30 - Ilość]
    
    J --> K[Waliduj Wyodrębnione Dane]
    K --> L{Dane Poprawne?}
    L -->|Nie| M[Komunikat o Błędzie]
    L -->|Tak| N[Wypełnij Pola Formularza]
    
    N --> O[GTIN → Kod Produktu]
    O --> P[Lot → Pole Lot]
    P --> Q[Data → Pola Dat]
    Q --> R[Serial → Pole Serial]
    R --> S[Ilość → Pole Ilość]
    
    S --> T[Wyszukaj Produkt po GTIN]
    T --> U{Znaleziono Produkt?}
    U -->|Tak| V[Załaduj Dane Produktu]
    U -->|Nie| W[Komunikat: Nieznany GTIN]
    
    V --> X[Kontynuuj Proces]
    W --> Y[Opcja Ręcznego Wprowadzenia]
    M --> Z[Powrót do Skanowania]
    D --> AA[Standardowy Proces]
    
    Y --> BB{Wprowadzić Ręcznie?}
    BB -->|Tak| CC[Formularz Ręczny]
    BB -->|Nie| Z
    CC --> X
    AA --> X
    Z --> DD[Nowe Skanowanie]
```

## 10. Monitoring i Raportowanie

```mermaid
graph TD
    A[Żądanie Raportu] --> B{Typ Raportu}
    B -->|Stan Inwentaryzacji| C[Pobierz Stan Bieżący]
    B -->|Postęp Pracowników| D[Pobierz Statystyki Pracowników]
    B -->|Rozbieżności| E[Analiza Różnic]
    B -->|Historia Operacji| F[Pobierz Logi]
    
    C --> G[Oblicz Procent Wykonania]
    G --> H[Grupuj po Lokalizacjach]
    H --> I[Generuj Wykres Postępu]
    
    D --> J[Zlicz Operacje na Pracownika]
    J --> K[Oblicz Wydajność]
    K --> L[Ranking Pracowników]
    
    E --> M[Porównaj Teoretyczne vs Spisane]
    M --> N[Identyfikuj Duże Rozbieżności]
    N --> O[Kategoryzuj Błędy]
    
    F --> P[Filtruj po Dacie/Pracowniku]
    P --> Q[Sortuj Chronologicznie]
    Q --> R[Dodaj Kontekst Operacji]
    
    I --> S[Wyświetl Raport]
    L --> S
    O --> S
    R --> S
    
    S --> T[Opcje Eksportu]
    T --> U{Format Eksportu}
    U -->|PDF| V[Generuj PDF]
    U -->|Excel| W[Generuj XLSX]
    U -->|Email| X[Wyślij Raport]
    
    V --> Y[Zapisz Lokalnie]
    W --> Y
    X --> Z[Konfiguruj Email]
    Z --> AA[Wyślij]
    Y --> BB[Powiadom o Zapisie]
    AA --> CC[Potwierdź Wysłanie]
```

## Legenda Diagramów

### Symbole
- **Prostokąt** - Proces/Akcja
- **Romb** - Decyzja/Warunek
- **Koło** - Start/Koniec
- **Równoległobok** - Dane wejściowe/wyjściowe
- **Sześciokąt** - Przygotowanie/Inicjalizacja

### Kolory (w implementacji)
- **Zielony** - Sukces/Pozytywny przepływ
- **Czerwony** - Błąd/Negatywny przepływ
- **Żółty** - Ostrzeżenie/Uwaga
- **Niebieski** - Proces normalny
- **Szary** - Proces pomocniczy

### Konwencje Nazewnictwa
- **CamelCase** - Nazwy procesów
- **UPPER_CASE** - Stałe/Konfiguracja
- **snake_case** - Zmienne/Parametry
