﻿using System;
using System.Linq;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using System.Threading;
using System.Text.RegularExpressions;
using System.Net;
using System.IO;
using System.Xml;

namespace Tarczyn__Magazyn
{
    public partial class AwizacjaDostawRozkladanie : Form
    {
        //MainMenu myParent = null;
        ActionMenu myParent = null;
        TextBox[] TextBoxArray = null;

        TextBox AktualnyTextBox = null;

        string operac_id_global = "";




        List<string> _paleta_wybor = new List<string>();
        int[] _paleta = new int[100];



        public string listcontrol_id = "";





        int licznik_local = 1;

        string delivery_number = "";


        public AwizacjaDostawRozkladanie(ActionMenu c)
        {
            FullScreenMode.OknoOFF(this);
            InitializeComponent();
            TextBoxArray = new TextBox[] { ETYKIETA, kod };
            myParent = c;
            Etykieta.Inicjalizacja();


            ETYKIETA.Focus();


        }





        public void RemoveText(object sender, EventArgs e)
        {
            TextBox Pole_Tekstowe = (TextBox)sender;
            if (Pole_Tekstowe.Text == "RRMMDD")
            {
                Pole_Tekstowe.Text = "";
            }

        }

        public void AddText(object sender, EventArgs e)
        {

        }

        private void button1_Click(object sender, EventArgs e)
        {
            Skaner.Przewij_Skanowanie();
            myParent.Show();
            this.Close();
        }


        int TrybSkanu = 0;

        private void GotFocus_TextBox(object sender, EventArgs e)
        {
            Skaner.Przewij_Skanowanie();
            TextBox Pole_Tekstowe = (TextBox)sender;

            AktualnyTextBox = Pole_Tekstowe;

            //if (DW == Pole_Tekstowe)
            //{

            //    if (!(dataprod.Text == "" || dataprod.Text == "RRMMDD"))
            //    {
            //        // sprawdź poprawność daty

            //        try
            //        {
            //            DateTime gg = DateTime.ParseExact("20" + dataprod.Text, "yyyyMMdd", null);
            //            if (ilosc_dni_przydatnosci != "0")
            //            {
            //                DW.Text = gg.AddDays(Convert.ToDouble(ilosc_dni_przydatnosci)).ToString("yyMMdd"); 
            //                //MessageBox.Show("DW:" + DW.Text);
            //                opak_real.Focus();
            //            }
            //        }
            //        catch (Exception ex)
            //        {
            //            MessageBox.Show("Błędna data produkcji.");
            //            dataprod.Text = "";
            //        }
            //    }

            //}
            if (Pole_Tekstowe.Text == "RRMMDD")
            {
                Pole_Tekstowe.Text = "";
            }


            ZacznijSkanowanie();
        }

        private void LostFocus_TextBox(object sender, EventArgs e)
        {
            AktualnyTextBox = null;
            Zakoncz_Skanowanie();

            TextBox Pole_Tekstowe = (TextBox)sender;



            if (kod == Pole_Tekstowe && kod.Text != "" && ETYKIETA.Text != "")
            {


                //szukaj();




            }

        }





        private void button2_Click(object sender, EventArgs e)
        {



        }



        #region Skanowanie

        Thread Skanowanie = null;



        private void ZacznijSkanowanie()
        {
            StringBuilder login = new StringBuilder();
            Skaner.UstawTryb_String(login);
            Skaner.Zacznij_Skanowanie();
            Skanowanie = new Thread(() => CheckString(login));
            Skanowanie.IsBackground = true;
            Skanowanie.Start();
        }

        private void CheckString(StringBuilder cc)
        {
            while (true)
            {
                if (cc.ToString() != "")
                {
                    this.Invoke(new EventHandler(delegate
                    {
                        dodawanie(cc.ToString());
                    }));
                    return;
                }
                Thread.Sleep(200);
            }
        }


        private void Zakoncz_Skanowanie()
        {
            {
                Skanowanie.Abort();
                Skaner.Przewij_Skanowanie();
            }
        }


        private void wyslij_zapytanie(string ops)
        {
            if (ops == "")
            {
                MessageBox.Show("Kody nie może być pusty");
                return;
            } if (ETYKIETA.Text == "")
            {
                MessageBox.Show("LK nie może być puste");
                return;
            }
            XmlDocument doc1_etykieta = WebService.Pobierz_XmlDocument("awizacja_dostawy_ukladanie.php?system_id=" + Wlasciwosci.system_id_id + "&listcontrol_id=" + ETYKIETA.Text + "&scan=" + ops);
            XmlNode node_etykieta = WebService.Pobierz_XmlNode(doc1_etykieta, "dane");
            if (node_etykieta["komunikat"].InnerText != "OK")
            {
                //ETYKIETA.Text = "";
                kod.Text = "";
                MessageBox.Show(node_etykieta["komunikat"].InnerText);
                return;
            }
            else
            {
                kod_label2.Text = node_etykieta["kod"].InnerText;
                kod_nazwa_label2.Text = node_etykieta["kod_nazwa"].InnerText;
                label1.Text = node_etykieta["paletyzacja"].InnerText;

                //etykieta_text.Focus();
            }
        }

        private void dodawanie(string ops)
        {
            //MessageBox.Show("TrybSkanu:" + TrybSkanu + "   ,   ops.Length:" + ops.Length);
            Skaner.Przewij_Skanowanie();

            //






            if (AktualnyTextBox == kod)
            {
                    wyslij_zapytanie(ops);
                    ZacznijSkanowanie();

            }

            if (AktualnyTextBox == ETYKIETA)
            {
                if (ETYKIETA.Text.Substring(0, 2) == "DL")
                {

                }
                ETYKIETA.Text = ops.Replace("LK", "");
                kod.Focus();
            }


            

        }

        #endregion





        private void button6_Click(object sender, EventArgs e)
        {


        }








        private void QT_KeyDown(object sender, KeyEventArgs e)
        {
            if (e.KeyCode == Keys.Enter)
            {
                button2_Click(this, new EventArgs());
            }
        }

        private void opak_real_KeyDown(object sender, KeyEventArgs e)
        {
            if (e.KeyCode == Keys.Enter)
            {
                button2_Click(this, new EventArgs());
            }
        }

        private void button9_Click(object sender, EventArgs e)
        {
            if (delivery_number != "")
            {
                //PrzyjeciePodglad nowy = new PrzyjeciePodglad(this);
                //nowy.Show();
                //this.Hide();

            }
            else
            {
                MessageBox.Show("Wczytaj etykietę");
            }


        }






    }
}