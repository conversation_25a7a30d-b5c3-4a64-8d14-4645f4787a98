<?php

//error_reporting(E_ALL);
//ini_set('display_errors', 1);


include_once 'DbArtsana.class.php';
$db = new DbArtsana();



$hu_sscc_number="";
$komunikat = "OK";
$nr_palety="";
$delivery_number="";
$ilosc_hu_dostawa="";
        $ilosc_hu_dostawa_scan="";
        $ilosc_tr_dostawa_scan="";
        $transport_number="";
        $ilosc_tr_dostawa="";


if (!empty($_GET['hu_sscc_number'])) {
    $hu_sscc_number = ltrim($_GET['hu_sscc_number'], '0');
    //$query = "select tt.id,tt.m_palet as lp,tt.transport_number,tt.delivery_number,CAST(hu_sscc_number as unsigned) as hu_sscc_number, (select count(*) from _hu ww where ww.delivery_number=tt.delivery_number and ww.transport_number=tt.transport_number) as ilosc_hu_dostawa, (select count(*) from _hu ww where ww.transport_number=tt.transport_number) as ilosc_tr_dostawa,(CASE WHEN tt.scan is null then 0 else 1 END) as zeskanowany, (CASE WHEN tt.scanout is null then 0 else 1 END) as zeskanowanyout, (select count(*) from _hu ww where ww.delivery_number=tt.delivery_number and ww.transport_number=tt.transport_number and ww.scan is not null) as ilosc_hu_dostawa_scan,(select count(*) from _hu ww where ww.transport_number=tt.transport_number and ww.scan is not null) as ilosc_tr_dostawa_scan from _hu tt,_dhd dh where TRIM(LEADING '0' FROM hu_sscc_number) ='" . $hu_sscc_number . "' and dh.delivery_number=tt.delivery_number";
    $query = "select tt.id,tt.m_palet as lp,tt.transport_number,tt.delivery_number,CAST(hu_sscc_number as unsigned) as hu_sscc_number,  (select count(*) from _hu ww where ww.m_palet=tt.m_palet and ww.transport_number=tt.transport_number) as ilosc_hu_dostawa, (select count(*) from _hu ww where ww.transport_number=tt.transport_number) as ilosc_tr_dostawa,(CASE WHEN tt.scan is null then 0 else 1 END) as zeskanowany, (CASE WHEN tt.scanout is null then 0 else 1 END) as zeskanowanyout, (select count(*) from _hu ww where ww.m_palet=tt.m_palet and ww.transport_number=tt.transport_number and ww.scan is not null) as ilosc_hu_dostawa_scan,(select count(*) from _hu ww where ww.transport_number=tt.transport_number and ww.scan is not null) as ilosc_tr_dostawa_scan from _hu tt,_dhd dh where hu_sscc_number ='" . $hu_sscc_number . "' and dh.delivery_number=tt.delivery_number";    //TRIM(LEADING '0' FROM 
    //echo $query;
    $result = $db->mGetResult($query);  //wy_wietla jakie kody b_dziemy zdejmowa_
    $przerwanie = 0;

    if (empty($result)) {
        $komunikat = "Brak HU o numerze : " . $hu_sscc_number;
    } else {
            foreach ($result as $aRow) {
                
            if ($aRow['zeskanowany'] == "1") {
                $komunikat = "Ten karton zostal juz zeskanowany.  ";
                $nr_palety = $aRow['lp'];
                $transport_number = $aRow['transport_number'];
                $delivery_number = $aRow['delivery_number'];
                $hu_sscc_number = $aRow['hu_sscc_number'];
                $ilosc_hu_dostawa = $aRow['ilosc_hu_dostawa'];
                $ilosc_tr_dostawa = $aRow['ilosc_tr_dostawa'];
                $ilosc_hu_dostawa_scan = $aRow['ilosc_hu_dostawa_scan'];
                $ilosc_tr_dostawa_scan = $aRow['ilosc_tr_dostawa_scan'];
            } else {
                $sql2 = 'update _hu set scan=now() where scan is null and id="' . $aRow['id'] . '";   ';
                //$komunikat.="" . $sql2;
                $result2 = $db->mGetResult($sql2);

                $query3 = "select tt.id,tt.m_palet as lp,tt.transport_number,tt.delivery_number,CAST(hu_sscc_number as unsigned) as hu_sscc_number, (select count(*) from _hu ww where ww.m_palet=tt.m_palet and ww.transport_number=tt.transport_number) as ilosc_hu_dostawa, (select count(*) from _hu ww where ww.transport_number=tt.transport_number) as ilosc_tr_dostawa,(CASE WHEN tt.scan is null then 0 else 1 END) as zeskanowany, (CASE WHEN tt.scanout is null then 0 else 1 END) as zeskanowanyout, (select count(*) from _hu ww where ww.m_palet=tt.m_palet and ww.transport_number=tt.transport_number and ww.scan is not null) as ilosc_hu_dostawa_scan,(select count(*) from _hu ww where ww.transport_number=tt.transport_number and ww.scan is not null) as ilosc_tr_dostawa_scan from _hu tt,_dhd dh where hu_sscc_number='" . $hu_sscc_number . "' and dh.delivery_number=tt.delivery_number";

                //echo $query;
                $result3 = $db->mGetResult($query3);
                
                
                    foreach ($result3 as $aRow3) {
                        
                    $nr_palety = $aRow3['lp'];
                    $transport_number = $aRow3['transport_number'];
                    $delivery_number = $aRow3['delivery_number'];
                    $hu_sscc_number = $aRow3['hu_sscc_number'];
                    $ilosc_hu_dostawa = $aRow3['ilosc_hu_dostawa'];
                    $ilosc_tr_dostawa = $aRow3['ilosc_tr_dostawa'];
                    $ilosc_hu_dostawa_scan = $aRow3['ilosc_hu_dostawa_scan'];
                    $ilosc_tr_dostawa_scan = $aRow3['ilosc_tr_dostawa_scan'];
                }
            }
        }
    }
} else {
    $komunikat = " Podaj nr HU!";
}
//$komunikat.=//mysql_error();
//echo $komunikat;
header('Content-type: text/xml');
echo '<dane>';
echo '<komunikat>', htmlentities($komunikat), '</komunikat>';
echo '<nr_palety>', htmlentities($nr_palety), '</nr_palety>';
echo '<transport_number>', htmlentities($transport_number), '</transport_number>';
echo '<delivery_number>', htmlentities($delivery_number), '</delivery_number>';
echo '<hu_sscc_number>', htmlentities($hu_sscc_number), '</hu_sscc_number>';
echo '<ilosc_hu_dostawa>', htmlentities($ilosc_hu_dostawa), '</ilosc_hu_dostawa>';
echo '<ilosc_tr_dostawa>', htmlentities($ilosc_tr_dostawa), '</ilosc_tr_dostawa>';
echo '<ilosc_hu_dostawa_scan>', htmlentities($ilosc_hu_dostawa_scan), '</ilosc_hu_dostawa_scan>';
echo '<ilosc_tr_dostawa_scan>', htmlentities($ilosc_tr_dostawa_scan), '</ilosc_tr_dostawa_scan>';
echo '</dane>';

//zdejmowanie towaru
?>