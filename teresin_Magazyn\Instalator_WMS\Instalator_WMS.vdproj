﻿"DeployProject"
{
"VSVersion" = "3:800"
"ProjectType" = "8:{3EA9E505-35AC-4774-B492-AD1749C4943A}"
"IsWebType" = "8:FALSE"
"ProjectName" = "8:Instalator_WMS"
"LanguageId" = "3:1033"
"CodePage" = "3:1252"
"UILanguageId" = "3:1033"
"SccProjectName" = "8:"
"SccLocalPath" = "8:"
"SccAuxPath" = "8:"
"SccProvider" = "8:"
    "Hierarchy"
    {
    }
    "Configurations"
    {
        "Debug"
        {
        "DisplayName" = "8:Debug"
        "IsDebugOnly" = "11:TRUE"
        "IsReleaseOnly" = "11:FALSE"
        "OutputFilename" = "8:Debug\\Instalator_WMS.cab"
        "PackageFilesAs" = "3:2"
        "PackageFileSize" = "3:-**********"
        "CabType" = "3:1"
        "Compression" = "3:2"
        "SignOutput" = "11:FALSE"
        "CertificateFile" = "8:"
        "PrivateKeyFile" = "8:"
        "TimeStampServer" = "8:"
        "InstallerBootstrapper" = "3:1"
        }
        "Release"
        {
        "DisplayName" = "8:Release"
        "IsDebugOnly" = "11:FALSE"
        "IsReleaseOnly" = "11:TRUE"
        "OutputFilename" = "8:Release\\Instalator_WMS.cab"
        "PackageFilesAs" = "3:2"
        "PackageFileSize" = "3:-**********"
        "CabType" = "3:1"
        "Compression" = "3:2"
        "SignOutput" = "11:FALSE"
        "CertificateFile" = "8:"
        "PrivateKeyFile" = "8:"
        "TimeStampServer" = "8:"
        "InstallerBootstrapper" = "3:1"
        }
    }
    "Deployable"
    {
        "OSD"
        {
        "FriendlyName" = "8:Instalator_WMS"
        "Version" = "8:1.0.0.0"
        "PreBuildEvent" = "8:"
        "PostBuildEvent" = "8:"
        "RunPostBuildEvent" = "3:0"
            "Dependencies"
            {
            }
        }
        "SimpleFile"
        {
        }
        "SimpleProjectOutput"
        {
        }
    }
}
