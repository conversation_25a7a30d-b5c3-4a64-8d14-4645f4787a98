﻿using System;
using System.Linq;
using System.Collections.Generic;
using System.Text;
using System.Xml;
using System.Xml.Linq;
using System.Reflection;
using System.IO;
using System.Windows.Forms;

namespace Tarczyn__Magazyn
{
    public static class Wlasciwosci
    {
        #region Właściwości_Programu
        public static string Varhost = "";
        public static string Varhasloadmina = "";
        public static string Varbaza = "";
        public static double Varwersja = 0.0;
        public static string VarSerwerUpdate = "";
        public static string VarLoginMYSQL = "";
        public static string VarHasloMYSQL = "";
        public static string GNG = "";
        public static string id_Pracownika = "0";
        public static string system_id;
        public static string system_id_id;
        public static string imie_nazwisko;
        public static string wozek;
        public static int wozek_ilosc_nosnikow = 1;
        public static string wozek_maksymalny_poziom = "";
        public static int bazaDanych = 0;
        public static int debugowanie_skaner = 0;
		public static string nazwaUdzialu = "";

        public static string historia_skaner = "TAK";


        

        //do zmiany error
        public static string Error = "error";
        public static string LastChoose = "";
        public static int TrybActionMenu = 0;
        public static string[] Message;
        public static string SerialNumber = "";
        public static string CurrentOperacja = "0";
        #endregion

        private static string pathtoxml = "";

        


        public static void Inicjalizacja()
        {
            Symbol.ResourceCoordination.TerminalInfo Version = new Symbol.ResourceCoordination.TerminalInfo();
            SerialNumber = Version.ESN;
            Message = new string[]  {
        "Błędny kod PIN",
        "Kod Pin",
        "Błędny kod administratora",
        "Kod administratora",
        "Brak numeru karty.",
        "Błąd połączenia MYSQL. Spróbuj ponownie"
    };
            pathtoxml = (Path.GetDirectoryName(Assembly.GetExecutingAssembly().GetName().CodeBase)) + "/settings.xml";
            wczytanieXML();
        }

        private static void wczytanieXML()
        {try
        {
                //Load xml
                XDocument xdoc = XDocument.Load((pathtoxml));
                //Run query
                var ustawienia = from r in xdoc.Descendants("Product")
                                 select new
                                 {
                                     IP = r.Element("IP").Value,
                                     Haslo = r.Element("Haslo").Value,
                                     Baza = r.Element("Baza").Value,
                                     Wersja = r.Element("Wersja").Value,
                                     Serwer = r.Element("SerwerUpdate").Value,
                                     gng = r.Element("GNGBaza").Value,
                                     Loginmysql = r.Element("Loginmysql").Value,
                                     haslomysql= r.Element("Haslomysql").Value,                                     
                                     LastBaza = r.Element("LastBaza").Value,
                                     nazwaUdzialu = r.Element("nazwaUdzialu").Value,
                                     
                                 };
                foreach (var r in ustawienia)
                {
                    Varhost = r.IP.ToString();
                    Varhasloadmina = r.Haslo.ToString();
                    Varbaza = r.Baza.ToString();
                    Varwersja = Convert.ToDouble(r.Wersja.ToString());
                    VarSerwerUpdate = r.Serwer.ToString();
                    VarLoginMYSQL = r.Loginmysql.ToString();
                    VarHasloMYSQL = r.haslomysql.ToString();
                    GNG = r.gng.ToString();
                    LastChoose = r.LastBaza.ToString();
                    nazwaUdzialu = r.nazwaUdzialu.ToString();
                }
}
            catch (Exception ex)
            {
               MessageBox.Show(ex.Message);
            }
        }

        public static void ZapisXML(string serwerIP, string BazaDanych, string UpdateServer, string MYSQLUser, string MYSQLPwd, string nazwa_udzial, string DrukarkaAdres)
        {
            Varhost = serwerIP;
            Varbaza = BazaDanych;
            VarSerwerUpdate = UpdateServer;
            VarLoginMYSQL = MYSQLUser;
            VarHasloMYSQL = MYSQLPwd;
            nazwaUdzialu = nazwa_udzial;

            
            try
            {
                string element = "IP";
                string fileLoc = pathtoxml;
                XmlDocument doc = new XmlDocument();
                doc.Load(fileLoc);
                XmlNode node = doc.SelectSingleNode("/Product/" + element);
                if (node != null)
                {
                    node.InnerText = serwerIP;
                }
                element = "Baza";
                node = doc.SelectSingleNode("/Product/" + element);
                if (node != null)
                {
                    node.InnerText = BazaDanych;
                }
                element = "SerwerUpdate";
                node = doc.SelectSingleNode("/Product/" + element);
                if (node != null)
                {
                    node.InnerText = UpdateServer;
                }
                element = "Loginmysql";
                node = doc.SelectSingleNode("/Product/" + element);
                if (node != null)
                {
                    node.InnerText = MYSQLUser;
                }
                element = "Haslomysql";
                node = doc.SelectSingleNode("/Product/" + element);
                if (node != null)
                {
                    node.InnerText = MYSQLPwd;
                }
                element = "nazwaUdzialu";
                node = doc.SelectSingleNode("/Product/" + element);
                if (node != null)
                {
                    node.InnerText = nazwaUdzialu;
                }
                element = "DrukarkaAdres";
                node = doc.SelectSingleNode("/Product/" + element);
                if (node != null)
                {
                    node.InnerText = DrukarkaAdres;
                }

                doc.Save(fileLoc);
                doc = null;
            }
            catch (Exception ex)
            {
                MessageBox.Show(ex.Message);
            }

            BazaDanychExternal.ChangeHost(Wlasciwosci.Varbaza, Wlasciwosci.Varhost, Wlasciwosci.VarLoginMYSQL, Wlasciwosci.VarHasloMYSQL);
        }


        public static string[] wczytanieZobele()
        {
            try
            {
                //Load xml
                XDocument xdoc = XDocument.Load((pathtoxml));
                //Run query
                string[] Wartosc_doZwrotu = new string[7];
                var ustawienia = from r in xdoc.Descendants("Zobele")
                                 select new
                                 {
                                     Etykieta = r.Element("Etykieta").Value,
                                     GTIN = r.Element("GTIN").Value,
                                     SSC = r.Element("SSC").Value,
                                     LOT = r.Element("LOT").Value,
                                     DW = r.Element("DW").Value,
                                     QT = r.Element("QT").Value,
                                     USER = r.Element("USER").Value,
                                 };
                foreach (var r in ustawienia)
                {
                    Wartosc_doZwrotu[0] = r.Etykieta.ToString();
                    Wartosc_doZwrotu[1] = r.GTIN.ToString();
                    Wartosc_doZwrotu[2] = r.SSC.ToString();
                    Wartosc_doZwrotu[3] = r.LOT.ToString();
                    Wartosc_doZwrotu[4] = r.DW.ToString();
                    Wartosc_doZwrotu[5] = r.QT.ToString();
                    Wartosc_doZwrotu[6] = r.USER.ToString();
                }
                return Wartosc_doZwrotu;
            }
            catch (Exception ex)
            {
                MessageBox.Show(ex.Message);
                return null;
            }
        }


        private static void ZapisElementu(string Path, string element, XmlDocument doc, string update)
        {
            XmlNode node = doc.SelectSingleNode(Path + @"/" + element);
            if (node != null)
            {
                node.InnerText = update;
            }
        }

        public static void ZapisXMLZobele(string a1, string a2, string a3, string a4, string a5, string a6, string a7)
        {
            try
            {
                string pathing = "//Product/Zobele";
                string fileLoc = pathtoxml;
                XmlDocument doc = new XmlDocument();
                doc.Load(fileLoc);
                ZapisElementu(pathing, "Etykieta", doc, a1);
                ZapisElementu(pathing, "GTIN", doc, a2);
                ZapisElementu(pathing, "SSC", doc, a3);
                ZapisElementu(pathing, "LOT", doc, a4);
                ZapisElementu(pathing, "DW", doc, a5);
                ZapisElementu(pathing, "QT", doc, a6);
                ZapisElementu(pathing, "USER", doc, a7);
                //string cvv = fileLoc.Replace(@"file:\", "");
                doc.Save(fileLoc);
                doc = null;
            }
            catch (Exception ex)
            {
                MessageBox.Show(ex.Message);
            }

            //BazaDanychExternal.ChangeHost(Wlasciwosci.Varbaza, Wlasciwosci.Varhost, Wlasciwosci.VarLoginMYSQL, Wlasciwosci.VarHasloMYSQL);
        }

        public static void ZapisLast(string a1)
        {
            try
            {
                string pathing = "//Product/";
                string fileLoc = pathtoxml;
                XmlDocument doc = new XmlDocument();
                doc.Load(fileLoc);
                ZapisElementu(pathing, "LastBaza", doc, a1);
                //string cvv = fileLoc.Replace(@"file:\", "");
                doc.Save(fileLoc);
                doc = null;
            }
            catch (Exception ex)
            {
                MessageBox.Show(ex.Message);
            }
            //BazaDanychExternal.ChangeHost(Wlasciwosci.Varbaza, Wlasciwosci.Varhost, Wlasciwosci.VarLoginMYSQL, Wlasciwosci.VarHasloMYSQL);
        }

        public static string wczytajLastBaze()
        {
            try
            {
                //Load xml
                XDocument xdoc = XDocument.Load((pathtoxml));
                //Run query
                string Wartosc_doZwrotu = "";
                var ustawienia = from r in xdoc.Descendants("Product")
                                 select new
                                 {
                                     Etykieta = r.Element("LastBaza").Value,
                                 };
                foreach (var r in ustawienia)
                {
                    Wartosc_doZwrotu = r.Etykieta.ToString();
                }
                return Wartosc_doZwrotu;
            }
            catch (Exception ex)
            {
                MessageBox.Show(ex.Message);
                return null;
            }
        }


        public static string[] wczytanieEtykietNaLinie()
        {
            try
            {
                //Load xml
                XDocument xdoc = XDocument.Load((pathtoxml));
                //Run query
                string[] Wartosc_doZwrotu = new string[3];
                var ustawienia = from r in xdoc.Descendants("WczytywanieNaLinie")
                                 select new
                                 {
                                     Etykieta = r.Element("Etykiety").Value,
                                     System = r.Element("System").Value,
                                     Pracownik = r.Element("Pracownik").Value,
                                 };
                foreach (var r in ustawienia)
                {
                    Wartosc_doZwrotu[0] = r.Etykieta.ToString();
                    Wartosc_doZwrotu[1] = r.System.ToString();
                    Wartosc_doZwrotu[2] = r.Pracownik.ToString();

                }
                return Wartosc_doZwrotu;
            }
            catch (Exception ex)
            {
                MessageBox.Show(ex.Message);
                return null;
            }
        }



        public static void ZapisXMLNaLinie(string a1, string a2, string a3)
        {
            try
            {
                string pathing = "//Product/WczytywanieNaLinie";
                string fileLoc = pathtoxml;
                XmlDocument doc = new XmlDocument();
                doc.Load(fileLoc);
                ZapisElementu(pathing, "Etykiety", doc, a1);
                ZapisElementu(pathing, "System", doc, a2);
                ZapisElementu(pathing, "Pracownik", doc, a3);

                //string cvv = fileLoc.Replace(@"file:\", "");
                doc.Save(fileLoc);
                doc = null;
            }
            catch (Exception ex)
            {
                MessageBox.Show(ex.Message);
            }

            //BazaDanychExternal.ChangeHost(Wlasciwosci.Varbaza, Wlasciwosci.Varhost, Wlasciwosci.VarLoginMYSQL, Wlasciwosci.VarHasloMYSQL);
        }
    }
}
