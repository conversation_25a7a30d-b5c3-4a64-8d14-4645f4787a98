<?php

include_once '../Db.class.php';
include_once '../funkcje.inc';

// show errors  
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

$db = new Db();

$baza_danych = "wmsgg";
$komunikat = "OK";

$response = array();

// Pobierz dane z $_GET
$sscc = $_GET['sscc']; // wymagany
$system_id = $_GET['system_id'];  
$adres_ip = $_GET['adres_ip'];


$tmp_arr1 = sprawdz_szukana_etykiete_WMS($sscc, $system_id, $komunikat, $baza_danych, $db);




if(empty($tmp_arr1['aRowEtWms'])) {
    $komunikat = "Nie znaleziono aktywnej etykietyty $sscc !!!! ";
    return show_komunikat_xml($komunikat);
}

if(empty($adres_ip)) {
    $komunikat = " Nie podano adresu IP drukarki !!!";
    return show_komunikat_xml($komunikat);
}


$aRowEtWms = $tmp_arr1['aRowEtWms'];    
// echo "<pre>";
// print_r($aRowEtWms);
// echo "</pre>";
 
// dodaj opóżnienie sekundowe i wydrukuj ponownie
if($adres_ip == '**********') 
    {
        reprint_label200dpi($aRowEtWms[0],$adres_ip)  ;
        sleep(2);
reprint_label200dpi($aRowEtWms[0],$adres_ip)  ; 
    }
    else
    {
        reprint_label300dpi($aRowEtWms[0],$adres_ip)  ;
        sleep(2);
reprint_label300dpi($aRowEtWms[0],$adres_ip)  ; 
    }




return show_komunikat_xml($komunikat);

//exit();

function reprint_label200dpi($dane,$adres_ip)
{
    $zpl_layout = "
^XA
^PW1012
^LL1200

^FO100,50^A0N,30,30^FDZleceniodawca:^FS
^FO100,90^A0N,30,30^FD" . zamianapolskich($dane['zlec_logo']) . "^FS
^FO100,130^A0N,30,30^FD " .  zamianapolskich( $dane['zlec_ulica'] . " ". $dane['zlec_lokal'] ).  "^FS ^FS
^FO100,170^A0N,30,30^FD " .  zamianapolskich($dane['zlec_kod'] . " ". $dane['zlec_miasto'] ).  "^FS

^FO400,50^A0N,30,30^FDNr zam. Zlecen.: " . zamianapolskich( $dane['docout_ref']) . "^FS
^FO400,90^A0N,30,30^FDNr zam. odb.: " . zamianapolskich( $dane['docout_ref_klient']) . "^FS

^FO100,250^A0N,30,30^FD Odbiorca:^FS
^FO100,290^A0N,30,30^FD" . zamianapolskich( $dane['koutlogo'] ). "^FS
^FO100,330^A0N,30,30^FD" . zamianapolskich( $dane['koutulica'] ). "^FS
^FO100,370^A0N,30,30^FD" . zamianapolskich( $dane['koutmiasto']) . "^FS
^FO100,410^A0N,30,30^FD" .  zamianapolskich($dane['kod_nazwa'] ). "^FS
^FO600,370^A0N,30,30^FD " .  $dane['ilosc_w_opakowaniu'] . "x" .  $dane['ilosc_opak'] . "=" .  $dane['ilosc_opak']*$dane['ilosc_w_opakowaniu'] . " ^FS
^FO600,410^A0N,30,30^FDSzt. x jedn.^FS

^FO100,490^GB700,3,3^FS

^FO100,520^A0N,30,30^FDCONTENT:^FS
^FO250,520^A0N,30,30^FD".$dane['ean']."^FS
^FO100,560^A0N,30,30^FDBATCH/LOT:^FS
^FO250,560^A0N,30,30^FD".$dane['lot']."^FS
^FO100,600^A0N,30,30^FDCOUNT:^FS
^FO250,600^A0N,30,30^FD".$dane['ilosc_opak']."^FS
^FO100,640^A0N,30,30^FDBEST BEFORE:^FS
^FO300,640^A0N,30,30^FD".date("Y.m.d", strtotime($dane['data_waznosci']))."^FS
^FO450,640^A0N,30,30^FD(YYYY.MM.DD)^FS

^FO100,700^A0N,30,30^FDSSCC:^FS
^FO200,700^A0N,30,30^FD".$dane['etykieta_klient']."^FS

^FO100,750^GB700,3,3^FS

^FO100,800^BY3^BCN,120,Y,N,N^FD>:02".str_pad($dane['ean'], 14, '0', STR_PAD_LEFT).">:37".$dane['ilosc_opak']."^FS
^FO100,1000^BY3^BCN,120,Y,N,N^FD>:15".date("ymd", strtotime($dane['data_waznosci'])).">:10".$dane['lot']."^FS
^FO100,1180^BY3^BCN,120,Y,N,N^FD>:00".$dane['etykieta_klient']."^FS

^XZ


";

//echo $zpl_layout;

$file = fopen("/tmp/reprint_sscc.zbr", "w");
        fputs($file, $zpl_layout);
        fclose($file);
        //echo $zpl_layout;

        $socket = socket_create(AF_INET, SOCK_STREAM, SOL_TCP);
        $result = socket_connect($socket, $adres_ip, "9100");
        socket_write($socket, $zpl_layout, strlen($zpl_layout));
        socket_close($socket);


}

function reprint_label300dpi($dane,$adres_ip)
{
    $zpl_layout = "
^XA
^PW1518
^LL1800

^FO150,75^A0N,45,45^FDZleceniodawca:^FS
^FO150,135^A0N,45,45^FD" . zamianapolskich($dane['zlec_logo']) . "^FS
^FO150,195^A0N,45,45^FD " .  zamianapolskich( $dane['zlec_ulica'] . " ". $dane['zlec_lokal'] ).  "^FS ^FS
^FO150,255^A0N,45,45^FD " .  zamianapolskich($dane['zlec_kod'] . " ". $dane['zlec_miasto'] ).  "^FS

^FO600,75^A0N,45,45^FDNr zam. Zlecen.: " . zamianapolskich( $dane['docout_ref']) . "^FS
^FO600,135^A0N,45,45^FDNr zam. odb.: " . zamianapolskich( $dane['docout_ref_klient']) . "^FS

^FO150,375^A0N,45,45^FD Odbiorca:^FS
^FO150,435^A0N,45,45^FD" . zamianapolskich( $dane['koutlogo'] ). "^FS
^FO150,495^A0N,45,45^FD" . zamianapolskich( $dane['koutulica'] ). "^FS
^FO150,555^A0N,45,45^FD" . zamianapolskich( $dane['koutmiasto']) . "^FS
^FO150,615^A0N,45,45^FD" .  zamianapolskich($dane['kod_nazwa'] ). "^FS
^FO900,555^A0N,45,45^FD " .  $dane['ilosc_w_opakowaniu'] . "x" .  $dane['ilosc_opak'] . "=" .  $dane['ilosc_opak']*$dane['ilosc_w_opakowaniu'] . " ^FS
^FO900,615^A0N,45,45^FDSzt. x jedn.^FS

^FO150,735^GB1050,4,4^FS

^FO150,780^A0N,45,45^FDCONTENT:^FS
^FO375,780^A0N,45,45^FD".$dane['ean']."^FS
^FO150,840^A0N,45,45^FDBATCH/LOT:^FS
^FO375,840^A0N,45,45^FD".$dane['lot']."^FS
^FO150,900^A0N,45,45^FDCOUNT:^FS
^FO375,900^A0N,45,45^FD".$dane['ilosc_opak']."^FS
^FO150,960^A0N,45,45^FDBEST BEFORE:^FS
^FO450,960^A0N,45,45^FD".date("Y.m.d", strtotime($dane['data_waznosci']))."^FS
^FO675,960^A0N,45,45^FD(YYYY.MM.DD)^FS

^FO150,1050^A0N,45,45^FDSSCC:^FS
^FO300,1050^A0N,45,45^FD".$dane['etykieta_klient']."^FS

^FO150,1125^GB1050,4,4^FS

^FO150,1200^BY4^BCN,180,Y,N,N^FD>:02".str_pad($dane['ean'], 14, '0', STR_PAD_LEFT).">:37".$dane['ilosc_opak']."^FS
^FO150,1500^BY4^BCN,180,Y,N,N^FD>:15".date("ymd", strtotime($dane['data_waznosci'])).">:10".$dane['lot']."^FS
^FO150,1770^BY4^BCN,180,Y,N,N^FD>:00".$dane['etykieta_klient']."^FS

^XZ
";

    $socket = socket_create(AF_INET, SOCK_STREAM, SOL_TCP);
    if ($socket === false) {
        echo "socket_create() failed: reason: " . socket_strerror(socket_last_error()) . "\n";
    }

    $result = socket_connect($socket, $adres_ip, "9100");
    if ($result === false) {
        echo "socket_connect() failed.\nReason: ($result) " . socket_strerror(socket_last_error($socket)) . "\n";
    }

    $socket = socket_create(AF_INET, SOCK_STREAM, SOL_TCP);
    $result = socket_connect($socket, $adres_ip, "9100");
    socket_write($socket, $zpl_layout, strlen($zpl_layout));
    socket_close($socket);
}

// create function reprint_label200dpi





function sprawdz_szukana_etykiete_WMS($etykieta_scan, $system_id, $komunikat, $baza_danych, $db)
{

        // if (substr($etykieta_scan, 0, 2) == "DS") {
        //         $sql = 'select e.id,e.etykieta_klient,k.kod,k.ilosc_w_opakowaniu,k.kod_nazwa,e.lot,e.active,TRIM(TRAILING "." FROM TRIM(TRAILING "0" from e.ilosc)) as ilosc,de.delivery_id,e.paleta_id,ss.nazwa as status_system_nazwa, ss.funkcja_stat , TRIM(TRAILING "." FROM TRIM(TRAILING "0" from de.ilosc_zamawiana))  as ilosc_zamawiana, d.dl_docin_id_wew, d.dl_docout_id_wew, d.miejsce_kompletacji,e.miejscep from ' . $baza_danych . '.etykiety e left join ' . $baza_danych . '.kody k on e.kod_id=k.id left join ' . $baza_danych . '.dlcollect dl ' . ' on dl.nr_et=e.id left join ' . $baza_danych . '.status_system ss  on e.status_id=ss.id left join ' . $baza_danych . '.delivery_et de ' . ' on de.etykieta_id=e.id left join ' . $baza_danych . '.delivery d ' . ' on de.delivery_id=d.id  where e.active=1  and e.paleta_id=' . str_replace("DS", "", $etykieta_scan) . ' and e.system_id=' . $system_id . '  order by nr_dl asc,e.ilosc asc limit 1'; //having nr_dl is null
        // } else {
        //         $sql = 'select e.id,e.etykieta_klient,k.kod,k.ilosc_w_opakowaniu,k.kod_nazwa,e.lot,e.active,TRIM(TRAILING "." FROM TRIM(TRAILING "0" from e.ilosc)) as ilosc,de.delivery_id,e.paleta_id,ss.nazwa as status_system_nazwa, ss.funkcja_stat , TRIM(TRAILING "." FROM TRIM(TRAILING "0" from de.ilosc_zamawiana))  as ilosc_zamawiana , d.dl_docin_id_wew, d.dl_docout_id_wew,d.miejsce_kompletacji,e.miejscep from ' . $baza_danych . '.etykiety e left join ' . $baza_danych . '.kody k on e.kod_id=k.id left join ' . $baza_danych . '.dlcollect dl ' . ' on dl.nr_et=e.id left join ' . $baza_danych . '.status_system ss  on e.status_id=ss.id left join ' . $baza_danych . '.delivery_et de ' . ' on de.etykieta_id=e.id left join ' . $baza_danych . '.delivery d ' . ' on de.delivery_id=d.id  where e.active=1  and  (e.id="' . $etykieta_scan . '" or e.etykieta_klient="' . $etykieta_scan . '")  and e.system_id=' . $system_id . '  order by nr_dl asc,e.ilosc asc limit 1'; //having nr_dl is null
        // }

        if (substr($etykieta_scan, 0, 2) == "DS") {
            $warunek = ' e.system_id = ' . $system_id . ' and e.paleta_id="' . substr($etykieta_scan, 0, 2) == "DS" . '"';
        } else {
            $warunek = ' e.system_id = ' . $system_id . ' and (e.id="' . $etykieta_scan . '" or e.etykieta_klient="' . $etykieta_scan . '")';
        }


        $sql = "SELECT 
    e.id,
    e.system_id,
    e.etykieta_klient,
    e.magazyn,
    e.active,
    e.miejscep,
    e.przeznaczenie_id,
    e.kod_id,
    e.paleta_id,
    e.dataprod,
    e.data_waznosci,
    e.status,
    e.blloc,
    e.akcja_id AS akcjanr_id,
    e.status_prism,
    e.stat,
    e.status_id,
    st.nazwa AS status_nazwa,
    st.funkcja_stat,
    e.kartony,
    ed3.kolor,
    ed3.plec,
    ed3.rozmiar_nr,
    ed3.uszkodzenie,
    e.lot,
    e.sscc,
    e.gtin,
    e.edycja_et,
    e.ilosc,
    e.ts,
    e.nretykiety,
    e.docin_id,
    e.docout_id,
    e.delivery_id,
    e.listcontrol_id,
    CONCAT('Hala ', m.hala, ' ', m.regal, '-', m.miejsce, '-', m.poziom) AS adres,
    CONCAT(m.regal, '-', m.miejsce, '-', m.poziom) AS adres2,
    k.kod_nazwa,
    k.jm,
    k.kod,
    k.kod2,
    TRIM(TRAILING '.' FROM TRIM(TRAILING '0' FROM (IF(e.ilosc IS NULL, 1, e.ilosc) / IF(k.ilosc_w_opakowaniu=0, 1, ilosc_w_opakowaniu)))) AS ilosc_opak,
    k.opakowanie_jm,
    k.ean,
    k.ean_jednostki,
    k.ilosc_w_opakowaniu,
    js.nazwa AS j_skladowania_nazwa,
    CONCAT(p.ilosc, ' ', tp.opis) AS paletanazwa,
    p.pal_klient,
    tp.kod AS tpkod,
    kin.logo AS kinlogo,
    dout.kontrah_id AS kontrah_id_docout,
    dout.docout_type,
    dout.docout_nr,
    dout.docout_date,
    dout.docout_ts,
    dd.dl_doc_ref as docout_ref,
    dd.dl_doc_ref_klient as docout_ref_klient,
    dout.docout_uwagi,
    dout.pracownik_id_kier,
    dout.docout_internal,
    dout.docout_date_req,
    ROUND(SUM(e.ilosc) * k.waga_szt_kg, 1) AS waga_suma,
    s.skrot,
    m.hala,
    din.doc_nr,
    din.doc_date,
    din.doc_ts,
    din.doc_internal,
    din.doc_type,
    din.kontrah_id AS kontrah_id_docin,
    din.doc_ref,
    din.doc_uwagi,
    din.dostawa_typ,
    din1.nr_doc_dost,
    din1.data_wystawienia,
    din1.nr_zam_mpg,
    din1.numeroavviso,
    kout.logo AS koutlogo,
    CONCAT(kout.ulica, ' ', kout.lokal) AS koutulica,
    CONCAT(kout.kod, ' ', kout.miasto) AS koutmiasto,
    a.nazwa AS akcja_nazwa,
    kodg.nazwa AS kody_grupa_nazwa,
    kwew.logo as zlec_logo,
    kwew.firma as zlec_firma,
    kwew.miasto as zlec_miasto,
    kwew.ulica as zlec_ulica,
    kwew.lokal as zlec_lokal,
    kwew.kod as zlec_kod,
    kwew.gln_kontrah as zlec_gln_kontrah
FROM 
    etykiety e
left JOIN 
    status_system st ON e.status_id = st.id
left JOIN 
    etykiety_dod3 ed3 ON e.id = ed3.id AND e.system_id = ed3.system_id
left JOIN 
    miejsca m ON e.miejscep = m.id
left JOIN 
    kody k ON k.id = e.kod_id
left JOIN 
    palety p ON e.paleta_id = p.id
left JOIN 
    typypalet tp ON p.typypalet_id = tp.id
left JOIN 
    jednostka_skladowania js ON p.j_skladowania_id = js.id
left JOIN 
    docin din ON e.docin_id = din.id
left JOIN 
    docin_dod1 din1 ON din.id = din1.docin_id
left JOIN 
    docout dout ON e.docout_id = dout.id
left JOIN 
    delivery dl ON e.delivery_id = dl.id
left JOIN 
    delivery_et de ON de.etykieta_id=e.id
    left JOIN 
    delivery dd ON de.delivery_id=dd.id
left JOIN 
    kontrah kin ON din.kontrah_id = kin.id
left JOIN 
    kontrah kout ON dd.dl_kontrah_id = kout.id
left JOIN 
    pracownicy pdocin ON din.pracownik_id = pdocin.id
left JOIN 
    pracownicy pdout ON dout.pracownik_id = pdout.id
left JOIN 
    systemy s ON e.system_id = s.wartosc
left JOIN 
    kontrah kwew ON s.kontrah_wew_id=kwew.id
left JOIN 
    akcja a ON a.id = e.akcja_id
left JOIN 
    kody_grupy kodg ON k.kody_grupy_id = kodg.id
WHERE 


  $warunek  
  
  order by de.delivery_id  asc,e.ilosc asc limit 1

;
";
        //echo $sql;
        $result2 = $db->mGetResultAsXML($sql);
        $aRowEtWms = array();

        // //    if (count($result2) > 1) {
        // //        return array('komunikat' => $komunikat, 'aRowEtWms' => $result2, 'ilosc_pozycji_etykiety' => count($result2));
        // //    }
        // //echo "<br>" . $sql;
        // foreach ($result2 as $index => $aRowEtWms) {
        //         if ($aRowEtWms['active'] != "1") {
        //                 $komunikat = "Etykieta jest nieaktywna. Przerywam operacje";
        //                 //echo "<br>" . $komunikat;
        //                 return show_komunikat_xml($komunikat);
        //         }

        //         if (!empty($aRowEtWms['funkcja_stat'])) {
        //                 $komunikat = "Etykieta jest w statusie: " . $aRowEtWms['status_system_nazwa'] . ". Przerywam operacje";
        //                 //echo "<br>" . $komunikat;
        //                 return show_komunikat_xml($komunikat);
        //         }

        //         // jeśli ilosc_zamawiana jest rózna od ilosc
        //         if ($aRowEtWms['ilosc_zamawiana'] != $aRowEtWms['ilosc']) {
        //                 $komunikat = "Etykieta zamawiana " . $aRowEtWms['ilosc_zamawiana'] . " jest różna od ilości etykiety: " . $aRowEtWms['ilosc'] . ". Przerywam operacje";
        //                 //echo "<br>" . $komunikat;
        //                 return show_komunikat_xml($komunikat);
        //         }
        // }
        return array('komunikat' => $komunikat, 'aRowEtWms' => $result2);
}




    function zamianapolskich($tekst)
    {
        $tabela = array(
            //WIN
            "\xb9" => "a",
            "\xa5" => "A",
            "\xe6" => "c",
            "\xc6" => "C",
            "\xea" => "e",
            "\xca" => "E",
            "\xb3" => "l",
            "\xa3" => "L",
            "\xf3" => "o",
            "\xd3" => "O",
            "\x9c" => "s",
            "\x8c" => "S",
            "\x9f" => "z",
            "\xaf" => "Z",
            "\xbf" => "z",
            "\xac" => "Z",
            "\xf1" => "n",
            "\xd1" => "N",
            //UTF
            "\xc4\x85" => "a",
            "\xc4\x84" => "A",
            "\xc4\x87" => "c",
            "\xc4\x86" => "C",
            "\xc4\x99" => "e",
            "\xc4\x98" => "E",
            "\xc5\x82" => "l",
            "\xc5\x81" => "L",
            "\xc3\xb3" => "o",
            "\xc3\x93" => "O",
            "\xc5\x9b" => "s",
            "\xc5\x9a" => "S",
            "\xc5\xbc" => "z",
            "\xc5\xbb" => "Z",
            "\xc5\xba" => "z",
            "\xc5\xb9" => "Z",
            "\xc5\x84" => "n",
            "\xc5\x83" => "N",
            //ISO
            "\xb1" => "a",
            "\xa1" => "A",
            "\xe6" => "c",
            "\xc6" => "C",
            "\xea" => "e",
            "\xca" => "E",
            "\xb3" => "l",
            "\xa3" => "L",
            "\xf3" => "o",
            "\xd3" => "O",
            "\xb6" => "s",
            "\xa6" => "S",
            "\xbc" => "z",
            "\xac" => "Z",
            "\xbf" => "z",
            "\xaf" => "Z",
            "\xf1" => "n",
            "\xd1" => "N"
        );

        return strtr($tekst, $tabela);
    }
