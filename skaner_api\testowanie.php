<?php
error_reporting(E_ALL);
ini_set('display_errors', 1);
include_once 'Dab.class.php';
include_once 'funkcje.inc';


$db = new Dab();
//echo "<head><meta charset='UTF-8'> </head>";
//phpinfo();

$komunikat = "OK";
//echo "OK";

//$delivery_id = $_GET['delivery_id'];
//$baza_danych = $_GET['baza_danych'];
//$adres_ip_drukarki = $_GET['adres_ip_drukarki'];
//$ilosc_etykiet_fizyczna = $_GET['ilosc_etykiet'];
//$sql = 'select * from systemy;
//';
//
//$result = $db->mGetResult($sql);
////echo $db->count;
////if($db->count >= 0){echo "OK"; } else {echo "Brak update";}
//print_r($result);
$miejsce = inwentaryzacja_szukaj(5823423, 1006, $db);
print_r($miejsce[0]['regal']);


$etykieta_id = 5823423;
        $hala_old = $miejsce[0]['hala'];
        $regal_old = $miejsce[0]['regal'];
        $miejsce_old = $miejsce[0]['miejsce'];
        $poziom_old = $miejsce[0]['poziom'];

        $hala_new = $_GET['hala'];
        $regal_new = $_GET['regal'];
        $miejsce_new = $_GET['miejsce'];
        $poziom_new = $_GET['poziom'];
        
        
        


        $system_id="1";
        $pracownik_id = "43";
        $zm_nr_global = "0";
        sprawdz_zmien_miejsce($hala_old, $regal_old, $miejsce_old, $poziom_old, $hala_new, $regal_new, $miejsce_new, $poziom_new, $system_id, $etykieta_id, $pracownik_id, $zm_nr_global, $db);
        
        
        

exit();
function inwentaryzacja_szukaj($etykieta, $inwentaryzacja_id, $db) {
    $sql = "select kod,podkod,TRIM(TRAILING '.' FROM TRIM(TRAILING '0' from ROUND(sum(i.ilosc),3))) as ilosc,i.paleta_id,m.hala,m.regal,m.miejsce,m.poziom,i.hala as hala_i,i.regal as regal_i,i.miejsce as miejsce_i,i.poziom as poziom_i,i.etykieta_id from inwentaryzacja i left join miejsca m on m.id=i.miejscep where (etykieta_id='" . $etykieta . "' ) and inwentaryzacja_id=" . $inwentaryzacja_id . " order by  hala_i  limit 1";
    //echo "<br>" . $sql . "<br>";
    $result = $db->mGetResultAsXML($sql);
    return $result;
}

function sprawdz_zmien_miejsce($hala_old, $regal_old, $miejsce_old, $poziom_old, $hala_new, $regal_new, $miejsce_new, $poziom_new, $system_id, $etykieta_id, $pracownik_id, $zm_nr_global, $db) {
    if ($hala_old != $hala_new || $regal_old != $regal_new || $miejsce_old != $miejsce_new || $poziom_old != $poziom_new) {
        $miejsce_id_old = pobierz_miejsce($hala_old, $regal_old, $miejsce_old, $poziom_old, $db);
        $miejsce_id_new = pobierz_miejsce($hala_new, $regal_new, $miejsce_new, $poziom_new, $db);
        //print_r($miejsce_id_old);
        print_r($miejsce_id_new);
        if(empty($miejsce_id_new))
        {
            echo "Nie znaleziono miejsca";
        }
        if(empty($miejsce_id_old))
        {
            $miejsce_id_old['id'] = 0;
        }
//print_r($miejsce_id_old);
        $sql = "insert into zmianym(typ,doc_nr,pracownik_id,data,etykieta,system_id,stare_m,nowe_m,doc_internal,stat,tszm) values(" .
                "'ZM'," . $zm_nr_global . "," . $pracownik_id . ",curdate()," . $etykieta_id . "," . $system_id . "," . $miejsce_id_old['id'] . "," . $miejsce_id_new['id'] . ",3,1,NOW())";
        echo "\n" . $sql;
        //$result2 = $db->mGetResultAsXML($sql);
//        if ($index == 0) {
//            $sql = "insert into operacje(etykieta_id, doc_type, doc_nr, imie_nazwisko, typ_operacji,system_id,wozek,operac_id,ilosc) "
//                    . "values('" . $value['id'] . "','" . $typ_operacji . "','" . $zm_nr_global . "','" . $imie_nazwisko . "','" . $typ_operacji . "','" . $value['system_id'] . "','" . $wozek . "','" . $operacja_id . "','1');";
//            //echo "\n" . $sql;
//            $resul3 = $db->mGetResultAsXML($sql);
//        }
        $sql = "update etykiety set miejscep = " . $miejsce_id_new['id'] . " where id=" . $etykieta_id . " limit 1; ";
        echo "\n" . $sql;
        //$resul3 = $db->mGetResultAsXML($sql);
    }
}

function pobierz_miejsce($hala, $regal, $miejsce, $poziom, $db) {
    $sql = 'select m.*  from miejsca m where m.hala="' . $hala . '" and m.regal="' . $regal . '" and m.miejsce="' . $miejsce . '" and m.poziom="' . $poziom . '" limit 1 ';
    //echo "<br>".$sql;
    $result2 = $db->mGetResultAsXML($sql);
    return $result2[0];
}

function wyswietl_wymary($baza_danych, $db) {
    $sql = 'SELECT w.nazwa, w.dlugosc, w.szerokosc, w.wysokosc FROM '.$baza_danych.'.wymiary w
WHERE w.aktywne=1;';
    //echo $sql;
    $result = $db->mGetResultAsXML($sql);
    return $result;
}

$wymiary = wyswietl_wymary("wmsgg", $db);

return xml_from_indexed_array(array('komunikat' => $komunikat, 'dodatkowa_akcja' => 'wybierz_wymiar', 'wymiary' => $wymiary));
echo "<pre>";
print_r($poziomy);
echo "</pre>";

exit();






//echo generuj_tabelke_array();
//
//
//echo generuj_tabelke_array(stock_pod_towarem_na_dzien($params,$db));
//
//
//$tabela = array_merge($tabela, generuj_tabelke_array(stan_wg_obrotow_na_dzien($params,$db)));

$tabela = array();

$pusty_wiersz = array();
$linijka=0;

$stan_wg_obrotow_na_dzien = stan_wg_obrotow_na_dzien($params,$db);
foreach ($stan_wg_obrotow_na_dzien as $key => $value) {
    //$tabela = array_merge($tabela, $value);
    $tabela[$linijka]=$value;
    $linijka++;
    
    foreach ($value as $key2 => $value2) {
        $pusty_wiersz[$key2]=' ';
    }
}

$params['data1']="2021-05-01";


$bilans_z_zakresu_dat = bilans_z_zakresu_dat($params,$db);


foreach ($bilans_z_zakresu_dat as $key => $value) {
    $tabela[$linijka]=$value;
    $linijka++;
}
//print_r($tabela) ;
echo "<br><br><br>";


$params['data1']="2021-05-31";
$stan_wg_obrotow_na_dzien = stan_wg_obrotow_na_dzien($params,$db);
foreach ($stan_wg_obrotow_na_dzien as $key => $value) {
    //$tabela = array_merge($tabela, $value);
    $tabela[$linijka]=$value;
    $linijka++;
}


$stock_pod_towarem_na_dzien = stock_pod_towarem_na_dzien($params,$db);
foreach ($stock_pod_towarem_na_dzien as $key => $value) {
    $tabela[$linijka]=$value;
    $linijka++;
}

$tabela[$linijka]=$pusty_wiersz;
    $linijka++;



$params['data1']="2021-05-01";

$obroty_zakres_dat_szczegolowy = obroty_zakres_dat_szczegolowy($params,$db);
foreach ($obroty_zakres_dat_szczegolowy as $key => $value) {
    $tabela[$linijka]=$value;
    $linijka++;
}

echo generuj_tabelke_array($tabela);



//echo generuj_tabelke_array(obroty_zakres_dat($params,$db));

//
//echo "<pre>";
//print_r(obroty_zakres_dat($params,$db));
//echo "</pre>";

exit();

function generuj_tabelke_array($rows) {
    $content = "";
    $lp = 1;
    $content.="<table border='1'>";
    foreach ($rows as $index => $row) {
        // First row: Column headers
        
        if ($index == 0) {
            $content.= '<tr>';
            $content.= '<th>LP.</th>';
            foreach (array_keys($row) as $fieldName) {
                $content.= '<th>' . $fieldName . '</th>';
                // Init rowspan value for each column
            }
            $content.= '</tr>';
        }
        $content.= '<tr>';
        // Other rows: Values
        $content.= '<td>' . $lp . '</td>';
        foreach ($row as $fieldName => $value) {
            $content.= '<td>' . $value . '</td>';
        }
        $content.= '</tr>';
        $lp++;
    }
    $content.= '</table>';
    return $content;
}


function generuj_tabelke($result) {
    $content = "";
    while ($row = mysql_fetch_assoc($result))
        $rows[] = $row;
    $lp = 1;
    $content.="<table border='1'>";
    foreach ($rows as $index => $row) {
        // First row: Column headers
        if ($index == 0) {
            $content.= '<tr>';
            $content.= '<th>LP.</th>';
            foreach (array_keys($row) as $fieldName) {
                $content.= '<th>' . $fieldName . '</th>';
                // Init rowspan value for each column
            }
            $content.= '</tr>';
        }
        $content.= '<tr>';
        // Other rows: Values
        $content.= '<td>' . $lp . '</td>';
        foreach ($row as $fieldName => $value) {
            $content.= '<td>' . $value . '</td>';
        }
        $content.= '</tr>';
        $lp++;
    }
    $content.= '</table>';
    return $content;
}




function stock_pod_towarem_na_dzien($params,$db)
{
    
    $sql ="select b.lp,
b.doc_type,
b.doc_nr,
b.data,
b.magazyn,
sum(b.euro_pod_towarem) as euro_pod_towarem,
sum(b.euro_podlozone) as euro_podlozone,
sum(b.przemyslowych_pod_towarem) as przemyslowych_pod_towarem,
sum(b.przemyslowych_podlozone) as przemyslowych_podlozone,
sum(b.jednorazowych_pod_towarem) as jednorazowych_pod_towarem,
sum(b.jednorazowych_podlozone) as jednorazowych_podlozone,
sum(b.chep_pod_towarem) as chep_pod_towarem,
sum(b.chep_podlozone) as chep_podlozone,
sum(b.chep_1_2_pod_towarem) as chep_1_2_pod_towarem,
sum(b.chep_1_2_podlozone) as chep_1_2_podlozone,
sum(b.chep_1_4_pod_towarem) as chep_1_4_pod_towarem,
sum(b.chep_1_4_podlozone) as chep_1_4_podlozone,
sum(b.dhp_pod_towarem) as dhp_pod_towarem,
sum(b.dhp_podlozone) as dhp_podlozone,
sum(b.lpr_pod_towarem) as lpr_pod_towarem,
sum(b.lpr_podlozone) as lpr_podlozone,
sum(b.lpr_1_4_pod_towarem) as lpr_1_4_pod_towarem,
sum(b.lpr_1_4_podlozone) as lpr_1_4_podlozone,
sum(b.kartonow) as kartonow,


  '' as doc_ref,
  '' as doc_ref_klient,
  '' as logo
 from
(
select
  'stok_ds_towarem_na_dzien ".$params['data2']."' as lp,
  '' as doc_type,
  '' as doc_nr,
  '' as data,
  e.magazyn,
if(tp.id=1 ,count(distinct paleta_id),0) as euro_pod_towarem,
0 as euro_podlozone

,
if(tp.id=2 ,sum(1),0) as przemyslowych_pod_towarem,
0 as przemyslowych_podlozone
,
if(tp.id=3 ,count(distinct paleta_id),0) as jednorazowych_pod_towarem,
0 as jednorazowych_podlozone
,
if(tp.id=7 ,count(distinct paleta_id),0) as chep_pod_towarem,
0 as chep_podlozone
,
if(tp.id=15 ,count(distinct paleta_id),0) as chep_1_2_pod_towarem,
0 as chep_1_2_podlozone
,
if(tp.id=5 ,count(distinct paleta_id),0) as chep_1_4_pod_towarem,
0 as chep_1_4_podlozone
,
if(tp.id=8 ,count(distinct paleta_id),0) as dhp_pod_towarem,
0 as dhp_podlozone
,
if(tp.id=11 ,count(distinct paleta_id),0) as lpr_pod_towarem,
0 as lpr_podlozone
,
if(tp.id=12 ,count(distinct paleta_id),0) as lpr_1_4_pod_towarem,
0 as lpr_1_4_podlozone
,
if(tp.id=14 ,count(distinct paleta_id),0) as kartonow
,

  '' as doc_ref,
  '' as doc_ref_klient,
  '' as logo
FROM
      etykiety e
left join palety p on e.paleta_id = p.id
      left join docin d on e.docin_id = d.id
      left join docout dd on e.docout_id = dd.id
  left join systemy s on s.wartosc = e.system_id
  left join typypalet tp on tp.id = p.typypalet_id
    WHERE
      e.system_id = ".$params['system_id']." and
doc_date<='".$params['data2']."' and (docout_date>'".$params['data2']."' or docout_date is null)
group by tp.id) as b";
     $result = $db->mGetResult($sql);
    return $result;
}


function bilans_z_zakresu_dat($params,$db)
{
    
    $sql ="
        select 'bilans_z_zakresu_dat od ".$params['data1']." do ".$params['data2']."' as lp,
'' as doc_type,
'' as doc_nr,
'' as data,
b.magazyn,
sum(b.euro_pod_towarem) as euro_pod_towarem,
sum(b.euro_podlozone) as euro_podlozone,
sum(b.przemyslowych_pod_towarem) as przemyslowych_pod_towarem,
sum(b.przemyslowych_podlozone) as przemyslowych_podlozone,
sum(b.jednorazowych_pod_towarem) as jednorazowych_pod_towarem,
sum(b.jednorazowych_podlozone) as jednorazowych_podlozone,
sum(b.chep_pod_towarem) as chep_pod_towarem,
sum(b.chep_podlozone) as chep_podlozone,
sum(b.chep_1_2_pod_towarem) as chep_1_2_pod_towarem,
sum(b.chep_1_2_podlozone) as chep_1_2_podlozone,
sum(b.chep_1_4_pod_towarem) as chep_1_4_pod_towarem,
sum(b.chep_1_4_podlozone) as chep_1_4_podlozone,
sum(b.dhp_pod_towarem) as dhp_pod_towarem,
sum(b.dhp_podlozone) as dhp_podlozone,
sum(b.lpr_pod_towarem) as lpr_pod_towarem,
sum(b.lpr_podlozone) as lpr_podlozone,
sum(b.lpr_1_4_pod_towarem) as lpr_1_4_pod_towarem,
sum(b.lpr_1_4_podlozone) as lpr_1_4_podlozone,
sum(b.kartonow) as kartonow
,

  '' as doc_ref,
  '' as doc_ref_klient,
  '' as logo
 from
(
select
  'wejscia' as lp,
  doc_type as doc_type, 
  doc_nr, 
  doc_date as data,
  d2.magazyn,
if(tp.id=1 and pd.wlasnosc_id=1,pd.ilosc,0) as euro_pod_towarem,
if(tp.id=1 and pd.wlasnosc_id=2,pd.ilosc,0) as euro_podlozone
,
if(tp.id=2 and pd.wlasnosc_id=1,pd.ilosc,0) as przemyslowych_pod_towarem,
if(tp.id=2 and pd.wlasnosc_id=2,pd.ilosc,0) as przemyslowych_podlozone
,
if(tp.id=3 and pd.wlasnosc_id=1,pd.ilosc,0) as jednorazowych_pod_towarem,
if(tp.id=3 and pd.wlasnosc_id=2,pd.ilosc,0) as jednorazowych_podlozone
,
if(tp.id=7 and pd.wlasnosc_id=1,pd.ilosc,0) as chep_pod_towarem,
if(tp.id=7 and pd.wlasnosc_id=2,pd.ilosc,0) as chep_podlozone
,
if(tp.id=15 and pd.wlasnosc_id=1,pd.ilosc,0) as chep_1_2_pod_towarem,
if(tp.id=15 and pd.wlasnosc_id=2,pd.ilosc,0) as chep_1_2_podlozone
,
if(tp.id=5 and pd.wlasnosc_id=1,pd.ilosc,0) as chep_1_4_pod_towarem,
if(tp.id=5 and pd.wlasnosc_id=2,pd.ilosc,0) as chep_1_4_podlozone
,
if(tp.id=8 and pd.wlasnosc_id=1,pd.ilosc,0) as dhp_pod_towarem,
if(tp.id=8 and pd.wlasnosc_id=2,pd.ilosc,0) as dhp_podlozone
,
if(tp.id=11 and pd.wlasnosc_id=1,pd.ilosc,0) as lpr_pod_towarem,
if(tp.id=11 and pd.wlasnosc_id=2,pd.ilosc,0) as lpr_podlozone
,
if(tp.id=12 and pd.wlasnosc_id=1,pd.ilosc,0) as lpr_1_4_pod_towarem,
if(tp.id=12 and pd.wlasnosc_id=2,pd.ilosc,0) as lpr_1_4_podlozone
,
if(tp.id=14 ,pd.ilosc,0) as kartonow
,
  
  cast(d2.doc_ref as char) as doc_ref,
  cast(d2.doc_ref_klient as char) as doc_ref_klient,
  k.logo
FROM
  docin d 
  left join (
    SELECT
      d.id,
      min(e.magazyn) as magazyn,
      e.system_id,
      doc_ref,
      doc_ref_klient 
    FROM 
      etykiety e 
      left join docin d on e.docin_id = d.id 
    WHERE
      e.system_id = ".$params['system_id']."
      and doc_type = 'PZ' 
      and d.doc_date between '".$params['data1']."'  and '".$params['data2']."'
      and d.id is not null
    group by
      d.id
  ) as d2 on d2.id = d.id
  left join systemy s on s.wartosc = d2.system_id
  left join kontrah k on d.kontrah_id = k.id
  left join palety_docin_docout pd on d.id = pd.doc_id
  and pd.doc_typ = 1
  left join typypalet tp on tp.id = pd.typypalet_id
where
  d2.id is not null
  and pd.id is not null
union
select
  'wyjscia' as lp,
  docout_type as doc_type,
  docout_nr as doc_nr,
  docout_date as data,
  d2.magazyn,
if(tp.id=1 and pd.wlasnosc_id=1,-pd.ilosc,0) as euro_pod_towarem,
if(tp.id=1 and pd.wlasnosc_id=2,-pd.ilosc,0) as euro_podlozone
,
if(tp.id=2 and pd.wlasnosc_id=1,-pd.ilosc,0) as przemyslowych_pod_towarem,
if(tp.id=2 and pd.wlasnosc_id=2,-pd.ilosc,0) as przemyslowych_podlozone
,
if(tp.id=3 and pd.wlasnosc_id=1,-pd.ilosc,0) as jednorazowych_pod_towarem,
if(tp.id=3 and pd.wlasnosc_id=2,-pd.ilosc,0) as jednorazowych_podlozone
,
if(tp.id=7 and pd.wlasnosc_id=1,-pd.ilosc,0) as chep_pod_towarem,
if(tp.id=7 and pd.wlasnosc_id=2,-pd.ilosc,0) as chep_podlozone
,
if(tp.id=15 and pd.wlasnosc_id=1,-pd.ilosc,0) as chep_1_2_pod_towarem,
if(tp.id=15 and pd.wlasnosc_id=2,-pd.ilosc,0) as chep_1_2_podlozone
,
if(tp.id=5 and pd.wlasnosc_id=1,-pd.ilosc,0) as chep_1_4_pod_towarem,
if(tp.id=5 and pd.wlasnosc_id=2,-pd.ilosc,0) as chep_1_4_podlozone
,
if(tp.id=8 and pd.wlasnosc_id=1,-pd.ilosc,0) as dhp_pod_towarem,
if(tp.id=8 and pd.wlasnosc_id=2,-pd.ilosc,0) as dhp_podlozone
,
if(tp.id=11 and pd.wlasnosc_id=1,-pd.ilosc,0) as lpr_pod_towarem,
if(tp.id=11 and pd.wlasnosc_id=2,-pd.ilosc,0) as lpr_podlozone
,
if(tp.id=12 and pd.wlasnosc_id=1,-pd.ilosc,0) as lpr_1_4_pod_towarem,
if(tp.id=12 and pd.wlasnosc_id=2,-pd.ilosc,0) as lpr_1_4_podlozone
,
if(tp.id=14 ,-pd.ilosc,0) as kartonow
,

  cast(d2.doc_ref as char) as doc_ref,
  cast(d2.doc_ref_klient as char) as doc_ref_klient,
  k.logo
FROM
  docout d
  left join (
    SELECT
      d.id,
      min(e.magazyn) as magazyn,
      e.system_id,
      docout_ref as doc_ref,
      docout_ref_klient as doc_ref_klient
    FROM
      etykiety e
      left join docout d on e.docout_id = d.id
    WHERE
      e.system_id = ".$params['system_id']."
      and docout_type = 'WZ'
      and d.docout_date between '".$params['data1']."'  and '".$params['data2']."'
      and d.id is not null
    group by
      d.id
  ) as d2 on d2.id = d.id
  left join systemy s on s.wartosc = d2.system_id
  left join kontrah k on d.kontrah_id = k.id
  left join palety_docin_docout pd on d.id = pd.doc_id
  and pd.doc_typ = 2
  left join typypalet tp on tp.id = pd.typypalet_id
where
  d2.id is not null
  and pd.id is not null
union
SELECT
  '1' as lp,
  'FP-' as docout_type,
  doc_nr as docout_nr,
  doc_date as data,
  magazyn,
if(tp.id=1 and pd.wlasnosc_id=1,-pd.ilosc,0) as euro_pod_towarem,
if(tp.id=1 and pd.wlasnosc_id=2,-pd.ilosc,0) as euro_podlozone
,
if(tp.id=2 and pd.wlasnosc_id=1,-pd.ilosc,0) as przemyslowych_pod_towarem,
if(tp.id=2 and pd.wlasnosc_id=2,-pd.ilosc,0) as przemyslowych_podlozone
,
if(tp.id=3 and pd.wlasnosc_id=1,-pd.ilosc,0) as jednorazowych_pod_towarem,
if(tp.id=3 and pd.wlasnosc_id=2,-pd.ilosc,0) as jednorazowych_podlozone
,
if(tp.id=7 and pd.wlasnosc_id=1,-pd.ilosc,0) as chep_pod_towarem,
if(tp.id=7 and pd.wlasnosc_id=2,-pd.ilosc,0) as chep_podlozone
,
if(tp.id=15 and pd.wlasnosc_id=1,-pd.ilosc,0) as chep_1_2_pod_towarem,
if(tp.id=15 and pd.wlasnosc_id=2,-pd.ilosc,0) as chep_1_2_podlozone
,
if(tp.id=5 and pd.wlasnosc_id=1,-pd.ilosc,0) as chep_1_4_pod_towarem,
if(tp.id=5 and pd.wlasnosc_id=2,-pd.ilosc,0) as chep_1_4_podlozone
,
if(tp.id=8 and pd.wlasnosc_id=1,-pd.ilosc,0) as dhp_pod_towarem,
if(tp.id=8 and pd.wlasnosc_id=2,-pd.ilosc,0) as dhp_podlozone
,
if(tp.id=11 and pd.wlasnosc_id=1,-pd.ilosc,0) as lpr_pod_towarem,
if(tp.id=11 and pd.wlasnosc_id=2,-pd.ilosc,0) as lpr_podlozone
,
if(tp.id=12 and pd.wlasnosc_id=1,-pd.ilosc,0) as lpr_1_4_pod_towarem,
if(tp.id=12 and pd.wlasnosc_id=2,-pd.ilosc,0) as lpr_1_4_podlozone
,
if(tp.id=14,-pd.ilosc,0) as kartonow
,
  nr_kwitu as doc_ref,
  uwagi as doc_ref_klient,
  k.logo
FROM
  palety_rozliczenie p
  left join palety_docin_docout pd on pd.doc_id = p.doc_id
  and pd.doc_typ = p.typ
  left join kontrah k on p.kontrah_id = k.id
  left join typypalet tp on tp.id = pd.typypalet_id
WHERE
  p.system_id = ".$params['system_id']." and p.typ = 5 and doc_date between '".$params['data1']."'  and '".$params['data2']."'
  and pd.id is not null
union
SELECT
  '1' as lp,
  'FP+' as docout_type,
  doc_nr as docout_nr,
  doc_date as data,
  magazyn,
if(tp.id=1 and pd.wlasnosc_id=1,pd.ilosc,0) as euro_pod_towarem,
if(tp.id=1 and pd.wlasnosc_id=2,pd.ilosc,0) as euro_podlozone
,
if(tp.id=2 and pd.wlasnosc_id=1,pd.ilosc,0) as przemyslowych_pod_towarem,
if(tp.id=2 and pd.wlasnosc_id=2,pd.ilosc,0) as przemyslowych_podlozone
,
if(tp.id=3 and pd.wlasnosc_id=1,pd.ilosc,0) as jednorazowych_pod_towarem,
if(tp.id=3 and pd.wlasnosc_id=2,pd.ilosc,0) as jednorazowych_podlozone
,
if(tp.id=7 and pd.wlasnosc_id=1,pd.ilosc,0) as chep_pod_towarem,
if(tp.id=7 and pd.wlasnosc_id=2,pd.ilosc,0) as chep_podlozone
,
if(tp.id=15 and pd.wlasnosc_id=1,pd.ilosc,0) as chep_1_2_pod_towarem,
if(tp.id=15 and pd.wlasnosc_id=2,pd.ilosc,0) as chep_1_2_podlozone
,
if(tp.id=5 and pd.wlasnosc_id=1,pd.ilosc,0) as chep_1_4_pod_towarem,
if(tp.id=5 and pd.wlasnosc_id=2,pd.ilosc,0) as chep_1_4_podlozone
,
if(tp.id=8 and pd.wlasnosc_id=1,pd.ilosc,0) as dhp_pod_towarem,
if(tp.id=8 and pd.wlasnosc_id=2,pd.ilosc,0) as dhp_podlozone
,
if(tp.id=11 and pd.wlasnosc_id=1,pd.ilosc,0) as lpr_pod_towarem,
if(tp.id=11 and pd.wlasnosc_id=2,pd.ilosc,0) as lpr_podlozone
,
if(tp.id=12 and pd.wlasnosc_id=1,pd.ilosc,0) as lpr_1_4_pod_towarem,
if(tp.id=12 and pd.wlasnosc_id=2,pd.ilosc,0) as lpr_1_4_podlozone
,
if(tp.id=14 ,pd.ilosc,0) as kartonow
,
  nr_kwitu as doc_ref,
  uwagi as doc_ref_klient,
  k.logo
FROM
  palety_rozliczenie p
  left join palety_docin_docout pd on pd.doc_id = p.doc_id
  and pd.doc_typ = p.typ
  left join kontrah k on p.kontrah_id = k.id
  left join typypalet tp on tp.id = pd.typypalet_id
WHERE
  p.system_id = ".$params['system_id']." and  p.typ = 4 and doc_date between '".$params['data1']."'  and '".$params['data2']."'
  and pd.id is not null ) as b";
    $result = $db->mGetResult($sql);
    return $result;
    
}




function obroty_zakres_dat_szczegolowy($params,$db)
{
    
    $sql ="select
  'szczegółowy - wejścia' as lp,
  doc_type as doc_type, 
  doc_nr, 
  doc_date as data,
  d2.magazyn,
if(tp.id=1 and pd.wlasnosc_id=1,pd.ilosc,0) as euro_pod_towarem,
if(tp.id=1 and pd.wlasnosc_id=2,pd.ilosc,0) as euro_podlozone
,
if(tp.id=2 and pd.wlasnosc_id=1,pd.ilosc,0) as przemyslowych_pod_towarem,
if(tp.id=2 and pd.wlasnosc_id=2,pd.ilosc,0) as przemyslowych_podlozone
,
if(tp.id=3 and pd.wlasnosc_id=1,pd.ilosc,0) as jednorazowych_pod_towarem,
if(tp.id=3 and pd.wlasnosc_id=2,pd.ilosc,0) as jednorazowych_podlozone
,
if(tp.id=7 and pd.wlasnosc_id=1,pd.ilosc,0) as chep_pod_towarem,
if(tp.id=7 and pd.wlasnosc_id=2,pd.ilosc,0) as chep_podlozone
,
if(tp.id=15 and pd.wlasnosc_id=1,pd.ilosc,0) as chep_1_2_pod_towarem,
if(tp.id=15 and pd.wlasnosc_id=2,pd.ilosc,0) as chep_1_2_podlozone
,
if(tp.id=5 and pd.wlasnosc_id=1,pd.ilosc,0) as chep_1_4_pod_towarem,
if(tp.id=5 and pd.wlasnosc_id=2,pd.ilosc,0) as chep_1_4_podlozone
,
if(tp.id=8 and pd.wlasnosc_id=1,pd.ilosc,0) as dhp_pod_towarem,
if(tp.id=8 and pd.wlasnosc_id=2,pd.ilosc,0) as dhp_podlozone
,
if(tp.id=11 and pd.wlasnosc_id=1,pd.ilosc,0) as lpr_pod_towarem,
if(tp.id=11 and pd.wlasnosc_id=2,pd.ilosc,0) as lpr_podlozone
,
if(tp.id=12 and pd.wlasnosc_id=1,pd.ilosc,0) as lpr_1_4_pod_towarem,
if(tp.id=12 and pd.wlasnosc_id=2,pd.ilosc,0) as lpr_1_4_podlozone
,
if(tp.id=14 ,pd.ilosc,0) as kartonow
,
  
  cast(d2.doc_ref as char) as doc_ref,
  cast(d2.doc_ref_klient as char) as doc_ref_klient,
  k.logo
FROM
  docin d 
  left join (
    SELECT
      d.id,
      min(e.magazyn) as magazyn,
      e.system_id,
      doc_ref,
      doc_ref_klient 
    FROM 
      etykiety e 
      left join docin d on e.docin_id = d.id 
    WHERE
      e.system_id = ".$params['system_id']."
      and doc_type = 'PZ' 
      and d.doc_date between '".$params['data1']."'  and '".$params['data2']."'
      and d.id is not null
    group by
      d.id
  ) as d2 on d2.id = d.id
  left join systemy s on s.wartosc = d2.system_id
  left join kontrah k on d.kontrah_id = k.id
  left join palety_docin_docout pd on d.id = pd.doc_id
  and pd.doc_typ = 1
  left join typypalet tp on tp.id = pd.typypalet_id
where
  d2.id is not null
  and pd.id is not null
union
select
  'szczegółowy - wyjścia' as lp,
  docout_type as doc_type,
  docout_nr as doc_nr,
  docout_date as data,
  d2.magazyn,
if(tp.id=1 and pd.wlasnosc_id=1,-pd.ilosc,0) as euro_pod_towarem,
if(tp.id=1 and pd.wlasnosc_id=2,-pd.ilosc,0) as euro_podlozone
,
if(tp.id=2 and pd.wlasnosc_id=1,-pd.ilosc,0) as przemyslowych_pod_towarem,
if(tp.id=2 and pd.wlasnosc_id=2,-pd.ilosc,0) as przemyslowych_podlozone
,
if(tp.id=3 and pd.wlasnosc_id=1,-pd.ilosc,0) as jednorazowych_pod_towarem,
if(tp.id=3 and pd.wlasnosc_id=2,-pd.ilosc,0) as jednorazowych_podlozone
,
if(tp.id=7 and pd.wlasnosc_id=1,-pd.ilosc,0) as chep_pod_towarem,
if(tp.id=7 and pd.wlasnosc_id=2,-pd.ilosc,0) as chep_podlozone
,
if(tp.id=15 and pd.wlasnosc_id=1,-pd.ilosc,0) as chep_1_2_pod_towarem,
if(tp.id=15 and pd.wlasnosc_id=2,-pd.ilosc,0) as chep_1_2_podlozone
,
if(tp.id=5 and pd.wlasnosc_id=1,-pd.ilosc,0) as chep_1_4_pod_towarem,
if(tp.id=5 and pd.wlasnosc_id=2,-pd.ilosc,0) as chep_1_4_podlozone
,
if(tp.id=8 and pd.wlasnosc_id=1,-pd.ilosc,0) as dhp_pod_towarem,
if(tp.id=8 and pd.wlasnosc_id=2,-pd.ilosc,0) as dhp_podlozone
,
if(tp.id=11 and pd.wlasnosc_id=1,-pd.ilosc,0) as lpr_pod_towarem,
if(tp.id=11 and pd.wlasnosc_id=2,-pd.ilosc,0) as lpr_podlozone
,
if(tp.id=12 and pd.wlasnosc_id=1,-pd.ilosc,0) as lpr_1_4_pod_towarem,
if(tp.id=12 and pd.wlasnosc_id=2,-pd.ilosc,0) as lpr_1_4_podlozone
,
if(tp.id=14 ,-pd.ilosc,0) as kartonow
,

  cast(d2.doc_ref as char) as doc_ref,
  cast(d2.doc_ref_klient as char) as doc_ref_klient,
  k.logo
FROM
  docout d
  left join (
    SELECT
      d.id,
      min(e.magazyn) as magazyn,
      e.system_id,
      docout_ref as doc_ref,
      docout_ref_klient as doc_ref_klient
    FROM
      etykiety e
      left join docout d on e.docout_id = d.id
    WHERE
      e.system_id = ".$params['system_id']."
      and docout_type = 'WZ'
      and d.docout_date between '".$params['data1']."'  and '".$params['data2']."'
      and d.id is not null
    group by
      d.id
  ) as d2 on d2.id = d.id
  left join systemy s on s.wartosc = d2.system_id
  left join kontrah k on d.kontrah_id = k.id
  left join palety_docin_docout pd on d.id = pd.doc_id
  and pd.doc_typ = 2
  left join typypalet tp on tp.id = pd.typypalet_id
where
  d2.id is not null
  and pd.id is not null
union
SELECT
  'szczegółowy - wyjścia' as lp,
  'FP-' as docout_type,
  doc_nr as docout_nr,
  doc_date as data,
  magazyn,
if(tp.id=1 and pd.wlasnosc_id=1,-pd.ilosc,0) as euro_pod_towarem,
if(tp.id=1 and pd.wlasnosc_id=2,-pd.ilosc,0) as euro_podlozone
,
if(tp.id=2 and pd.wlasnosc_id=1,-pd.ilosc,0) as przemyslowych_pod_towarem,
if(tp.id=2 and pd.wlasnosc_id=2,-pd.ilosc,0) as przemyslowych_podlozone
,
if(tp.id=3 and pd.wlasnosc_id=1,-pd.ilosc,0) as jednorazowych_pod_towarem,
if(tp.id=3 and pd.wlasnosc_id=2,-pd.ilosc,0) as jednorazowych_podlozone
,
if(tp.id=7 and pd.wlasnosc_id=1,-pd.ilosc,0) as chep_pod_towarem,
if(tp.id=7 and pd.wlasnosc_id=2,-pd.ilosc,0) as chep_podlozone
,
if(tp.id=15 and pd.wlasnosc_id=1,-pd.ilosc,0) as chep_1_2_pod_towarem,
if(tp.id=15 and pd.wlasnosc_id=2,-pd.ilosc,0) as chep_1_2_podlozone
,
if(tp.id=5 and pd.wlasnosc_id=1,-pd.ilosc,0) as chep_1_4_pod_towarem,
if(tp.id=5 and pd.wlasnosc_id=2,-pd.ilosc,0) as chep_1_4_podlozone
,
if(tp.id=8 and pd.wlasnosc_id=1,-pd.ilosc,0) as dhp_pod_towarem,
if(tp.id=8 and pd.wlasnosc_id=2,-pd.ilosc,0) as dhp_podlozone
,
if(tp.id=11 and pd.wlasnosc_id=1,-pd.ilosc,0) as lpr_pod_towarem,
if(tp.id=11 and pd.wlasnosc_id=2,-pd.ilosc,0) as lpr_podlozone
,
if(tp.id=12 and pd.wlasnosc_id=1,-pd.ilosc,0) as lpr_1_4_pod_towarem,
if(tp.id=12 and pd.wlasnosc_id=2,-pd.ilosc,0) as lpr_1_4_podlozone
,
if(tp.id=14,-pd.ilosc,0) as kartonow
,
  nr_kwitu as doc_ref,
  uwagi as doc_ref_klient,
  k.logo
FROM
  palety_rozliczenie p
  left join palety_docin_docout pd on pd.doc_id = p.doc_id
  and pd.doc_typ = p.typ
  left join kontrah k on p.kontrah_id = k.id
  left join typypalet tp on tp.id = pd.typypalet_id
WHERE
  p.system_id = ".$params['system_id']." and p.typ = 5 and doc_date between '".$params['data1']."'  and '".$params['data2']."'
  and pd.id is not null
union
SELECT
  'szczegółowy - wejścia' as lp,
  'FP+' as docout_type,
  doc_nr as docout_nr,
  doc_date as data,
  magazyn,
if(tp.id=1 and pd.wlasnosc_id=1,pd.ilosc,0) as euro_pod_towarem,
if(tp.id=1 and pd.wlasnosc_id=2,pd.ilosc,0) as euro_podlozone
,
if(tp.id=2 and pd.wlasnosc_id=1,pd.ilosc,0) as przemyslowych_pod_towarem,
if(tp.id=2 and pd.wlasnosc_id=2,pd.ilosc,0) as przemyslowych_podlozone
,
if(tp.id=3 and pd.wlasnosc_id=1,pd.ilosc,0) as jednorazowych_pod_towarem,
if(tp.id=3 and pd.wlasnosc_id=2,pd.ilosc,0) as jednorazowych_podlozone
,
if(tp.id=7 and pd.wlasnosc_id=1,pd.ilosc,0) as chep_pod_towarem,
if(tp.id=7 and pd.wlasnosc_id=2,pd.ilosc,0) as chep_podlozone
,
if(tp.id=15 and pd.wlasnosc_id=1,pd.ilosc,0) as chep_1_2_pod_towarem,
if(tp.id=15 and pd.wlasnosc_id=2,pd.ilosc,0) as chep_1_2_podlozone
,
if(tp.id=5 and pd.wlasnosc_id=1,pd.ilosc,0) as chep_1_4_pod_towarem,
if(tp.id=5 and pd.wlasnosc_id=2,pd.ilosc,0) as chep_1_4_podlozone
,
if(tp.id=8 and pd.wlasnosc_id=1,pd.ilosc,0) as dhp_pod_towarem,
if(tp.id=8 and pd.wlasnosc_id=2,pd.ilosc,0) as dhp_podlozone
,
if(tp.id=11 and pd.wlasnosc_id=1,pd.ilosc,0) as lpr_pod_towarem,
if(tp.id=11 and pd.wlasnosc_id=2,pd.ilosc,0) as lpr_podlozone
,
if(tp.id=12 and pd.wlasnosc_id=1,pd.ilosc,0) as lpr_1_4_pod_towarem,
if(tp.id=12 and pd.wlasnosc_id=2,pd.ilosc,0) as lpr_1_4_podlozone
,
if(tp.id=14 ,pd.ilosc,0) as kartonow
,
  nr_kwitu as doc_ref,
  uwagi as doc_ref_klient,
  k.logo
FROM
  palety_rozliczenie p
  left join palety_docin_docout pd on pd.doc_id = p.doc_id
  and pd.doc_typ = p.typ
  left join kontrah k on p.kontrah_id = k.id
  left join typypalet tp on tp.id = pd.typypalet_id
WHERE
  p.system_id = ".$params['system_id']." and  p.typ = 4 and doc_date between '".$params['data1']."'  and '".$params['data2']."'
  and pd.id is not null";
    
    $result = $db->mGetResult($sql);
    return $result;
    
}













function stan_wg_obrotow_na_dzien($params,$db)
{
    
    $sql ="select
'stan_wg_obrotow_na_dzien ".$params['data1']."' as lp,
' ' as doc_type,
' ' as doc_nr,
'' as data,
b.magazyn,
sum(b.euro_pod_towarem) as euro_pod_towarem,
sum(b.euro_podlozone) as euro_podlozone,
sum(b.przemyslowych_pod_towarem) as przemyslowych_pod_towarem,
sum(b.przemyslowych_podlozone) as przemyslowych_podlozone,
sum(b.jednorazowych_pod_towarem) as jednorazowych_pod_towarem,
sum(b.jednorazowych_podlozone) as jednorazowych_podlozone,
sum(b.chep_pod_towarem) as chep_pod_towarem,
sum(b.chep_podlozone) as chep_podlozone,
sum(b.chep_1_2_pod_towarem) as chep_1_2_pod_towarem,
sum(b.chep_1_2_podlozone) as chep_1_2_podlozone,
sum(b.chep_1_4_pod_towarem) as chep_1_4_pod_towarem,
sum(b.chep_1_4_podlozone) as chep_1_4_podlozone,
sum(b.dhp_pod_towarem) as dhp_pod_towarem,
sum(b.dhp_podlozone) as dhp_podlozone,
sum(b.lpr_pod_towarem) as lpr_pod_towarem,
sum(b.lpr_podlozone) as lpr_podlozone,
sum(b.lpr_1_4_pod_towarem) as lpr_1_4_pod_towarem,
sum(b.lpr_1_4_podlozone) as lpr_1_4_podlozone,
sum(b.kartonow) as kartonow
,

  '' as doc_ref,
  '' as doc_ref_klient,
  '' as logo

from (
select
  'wejscia' as lp,
  doc_type as doc_type, 
  doc_nr, 
  doc_date as data,
  d2.magazyn,
if(tp.id=1 and pd.wlasnosc_id=1,pd.ilosc,0) as euro_pod_towarem,
if(tp.id=1 and pd.wlasnosc_id=2,pd.ilosc,0) as euro_podlozone
,
if(tp.id=2 and pd.wlasnosc_id=1,pd.ilosc,0) as przemyslowych_pod_towarem,
if(tp.id=2 and pd.wlasnosc_id=2,pd.ilosc,0) as przemyslowych_podlozone
,
if(tp.id=3 and pd.wlasnosc_id=1,pd.ilosc,0) as jednorazowych_pod_towarem,
if(tp.id=3 and pd.wlasnosc_id=2,pd.ilosc,0) as jednorazowych_podlozone
,
if(tp.id=7 and pd.wlasnosc_id=1,pd.ilosc,0) as chep_pod_towarem,
if(tp.id=7 and pd.wlasnosc_id=2,pd.ilosc,0) as chep_podlozone
,
if(tp.id=15 and pd.wlasnosc_id=1,pd.ilosc,0) as chep_1_2_pod_towarem,
if(tp.id=15 and pd.wlasnosc_id=2,pd.ilosc,0) as chep_1_2_podlozone
,
if(tp.id=5 and pd.wlasnosc_id=1,pd.ilosc,0) as chep_1_4_pod_towarem,
if(tp.id=5 and pd.wlasnosc_id=2,pd.ilosc,0) as chep_1_4_podlozone
,
if(tp.id=8 and pd.wlasnosc_id=1,pd.ilosc,0) as dhp_pod_towarem,
if(tp.id=8 and pd.wlasnosc_id=2,pd.ilosc,0) as dhp_podlozone
,
if(tp.id=11 and pd.wlasnosc_id=1,pd.ilosc,0) as lpr_pod_towarem,
if(tp.id=11 and pd.wlasnosc_id=2,pd.ilosc,0) as lpr_podlozone
,
if(tp.id=12 and pd.wlasnosc_id=1,pd.ilosc,0) as lpr_1_4_pod_towarem,
if(tp.id=12 and pd.wlasnosc_id=2,pd.ilosc,0) as lpr_1_4_podlozone
,
if(tp.id=14 ,pd.ilosc,0) as kartonow
,
  
  cast(d2.doc_ref as char) as doc_ref,
  cast(d2.doc_ref_klient as char) as doc_ref_klient,
  k.logo
FROM
  docin d 
  left join (
    SELECT
      d.id,
      min(e.magazyn) as magazyn,
      e.system_id,
      doc_ref,
      doc_ref_klient 
    FROM 
      etykiety e 
      left join docin d on e.docin_id = d.id 
    WHERE
      e.system_id = ".$params['system_id']."
      and doc_type = 'PZ' 
      and d.doc_date <= '".$params['data1']."'
      and d.id is not null
    group by
      d.id
  ) as d2 on d2.id = d.id
  left join systemy s on s.wartosc = d2.system_id
  left join kontrah k on d.kontrah_id = k.id
  left join palety_docin_docout pd on d.id = pd.doc_id
  and pd.doc_typ = 1
  left join typypalet tp on tp.id = pd.typypalet_id
where
  d2.id is not null
  and pd.id is not null
union
select
  'wyjscia' as lp,
  docout_type as doc_type,
  docout_nr as doc_nr,
  docout_date as data,
  d2.magazyn,
if(tp.id=1 and pd.wlasnosc_id=1,-pd.ilosc,0) as euro_pod_towarem,
if(tp.id=1 and pd.wlasnosc_id=2,-pd.ilosc,0) as euro_podlozone
,
if(tp.id=2 and pd.wlasnosc_id=1,-pd.ilosc,0) as przemyslowych_pod_towarem,
if(tp.id=2 and pd.wlasnosc_id=2,-pd.ilosc,0) as przemyslowych_podlozone
,
if(tp.id=3 and pd.wlasnosc_id=1,-pd.ilosc,0) as jednorazowych_pod_towarem,
if(tp.id=3 and pd.wlasnosc_id=2,-pd.ilosc,0) as jednorazowych_podlozone
,
if(tp.id=7 and pd.wlasnosc_id=1,-pd.ilosc,0) as chep_pod_towarem,
if(tp.id=7 and pd.wlasnosc_id=2,-pd.ilosc,0) as chep_podlozone
,
if(tp.id=15 and pd.wlasnosc_id=1,-pd.ilosc,0) as chep_1_2_pod_towarem,
if(tp.id=15 and pd.wlasnosc_id=2,-pd.ilosc,0) as chep_1_2_podlozone
,
if(tp.id=5 and pd.wlasnosc_id=1,-pd.ilosc,0) as chep_1_4_pod_towarem,
if(tp.id=5 and pd.wlasnosc_id=2,-pd.ilosc,0) as chep_1_4_podlozone
,
if(tp.id=8 and pd.wlasnosc_id=1,-pd.ilosc,0) as dhp_pod_towarem,
if(tp.id=8 and pd.wlasnosc_id=2,-pd.ilosc,0) as dhp_podlozone
,
if(tp.id=11 and pd.wlasnosc_id=1,-pd.ilosc,0) as lpr_pod_towarem,
if(tp.id=11 and pd.wlasnosc_id=2,-pd.ilosc,0) as lpr_podlozone
,
if(tp.id=12 and pd.wlasnosc_id=1,-pd.ilosc,0) as lpr_1_4_pod_towarem,
if(tp.id=12 and pd.wlasnosc_id=2,-pd.ilosc,0) as lpr_1_4_podlozone
,
if(tp.id=14 ,-pd.ilosc,0) as kartonow
,

  cast(d2.doc_ref as char) as doc_ref,
  cast(d2.doc_ref_klient as char) as doc_ref_klient,
  k.logo
FROM
  docout d
  left join (
    SELECT
      d.id,
      min(e.magazyn) as magazyn,
      e.system_id,
      docout_ref as doc_ref,
      docout_ref_klient as doc_ref_klient
    FROM
      etykiety e
      left join docout d on e.docout_id = d.id
    WHERE
      e.system_id = ".$params['system_id']."
      and docout_type = 'WZ'
      and d.docout_date <= '".$params['data1']."'
      and d.id is not null
    group by
      d.id
  ) as d2 on d2.id = d.id
  left join systemy s on s.wartosc = d2.system_id
  left join kontrah k on d.kontrah_id = k.id
  left join palety_docin_docout pd on d.id = pd.doc_id
  and pd.doc_typ = 2
  left join typypalet tp on tp.id = pd.typypalet_id
where
  d2.id is not null
  and pd.id is not null
union
SELECT
  '1' as lp,
  'FP-' as docout_type,
  doc_nr as docout_nr,
  doc_date as data,
  magazyn,
if(tp.id=1 and pd.wlasnosc_id=1,-pd.ilosc,0) as euro_pod_towarem,
if(tp.id=1 and pd.wlasnosc_id=2,-pd.ilosc,0) as euro_podlozone
,
if(tp.id=2 and pd.wlasnosc_id=1,-pd.ilosc,0) as przemyslowych_pod_towarem,
if(tp.id=2 and pd.wlasnosc_id=2,-pd.ilosc,0) as przemyslowych_podlozone
,
if(tp.id=3 and pd.wlasnosc_id=1,-pd.ilosc,0) as jednorazowych_pod_towarem,
if(tp.id=3 and pd.wlasnosc_id=2,-pd.ilosc,0) as jednorazowych_podlozone
,
if(tp.id=7 and pd.wlasnosc_id=1,-pd.ilosc,0) as chep_pod_towarem,
if(tp.id=7 and pd.wlasnosc_id=2,-pd.ilosc,0) as chep_podlozone
,
if(tp.id=15 and pd.wlasnosc_id=1,-pd.ilosc,0) as chep_1_2_pod_towarem,
if(tp.id=15 and pd.wlasnosc_id=2,-pd.ilosc,0) as chep_1_2_podlozone
,
if(tp.id=5 and pd.wlasnosc_id=1,-pd.ilosc,0) as chep_1_4_pod_towarem,
if(tp.id=5 and pd.wlasnosc_id=2,-pd.ilosc,0) as chep_1_4_podlozone
,
if(tp.id=8 and pd.wlasnosc_id=1,-pd.ilosc,0) as dhp_pod_towarem,
if(tp.id=8 and pd.wlasnosc_id=2,-pd.ilosc,0) as dhp_podlozone
,
if(tp.id=11 and pd.wlasnosc_id=1,-pd.ilosc,0) as lpr_pod_towarem,
if(tp.id=11 and pd.wlasnosc_id=2,-pd.ilosc,0) as lpr_podlozone
,
if(tp.id=12 and pd.wlasnosc_id=1,-pd.ilosc,0) as lpr_1_4_pod_towarem,
if(tp.id=12 and pd.wlasnosc_id=2,-pd.ilosc,0) as lpr_1_4_podlozone
,
if(tp.id=14,-pd.ilosc,0) as kartonow
,
  nr_kwitu as doc_ref,
  uwagi as doc_ref_klient,
  k.logo
FROM
  palety_rozliczenie p
  left join palety_docin_docout pd on pd.doc_id = p.doc_id
  and pd.doc_typ = p.typ
  left join kontrah k on p.kontrah_id = k.id
  left join typypalet tp on tp.id = pd.typypalet_id
WHERE
  p.system_id = ".$params['system_id']." and p.typ = 5 and doc_date <= '".$params['data1']."'
  and pd.id is not null
union
SELECT
  '1' as lp,
  'FP+' as docout_type,
  doc_nr as docout_nr,
  doc_date as data,
  magazyn,
if(tp.id=1 and pd.wlasnosc_id=1,pd.ilosc,0) as euro_pod_towarem,
if(tp.id=1 and pd.wlasnosc_id=2,pd.ilosc,0) as euro_podlozone
,
if(tp.id=2 and pd.wlasnosc_id=1,pd.ilosc,0) as przemyslowych_pod_towarem,
if(tp.id=2 and pd.wlasnosc_id=2,pd.ilosc,0) as przemyslowych_podlozone
,
if(tp.id=3 and pd.wlasnosc_id=1,pd.ilosc,0) as jednorazowych_pod_towarem,
if(tp.id=3 and pd.wlasnosc_id=2,pd.ilosc,0) as jednorazowych_podlozone
,
if(tp.id=7 and pd.wlasnosc_id=1,pd.ilosc,0) as chep_pod_towarem,
if(tp.id=7 and pd.wlasnosc_id=2,pd.ilosc,0) as chep_podlozone
,
if(tp.id=15 and pd.wlasnosc_id=1,pd.ilosc,0) as chep_1_2_pod_towarem,
if(tp.id=15 and pd.wlasnosc_id=2,pd.ilosc,0) as chep_1_2_podlozone
,
if(tp.id=5 and pd.wlasnosc_id=1,pd.ilosc,0) as chep_1_4_pod_towarem,
if(tp.id=5 and pd.wlasnosc_id=2,pd.ilosc,0) as chep_1_4_podlozone
,
if(tp.id=8 and pd.wlasnosc_id=1,pd.ilosc,0) as dhp_pod_towarem,
if(tp.id=8 and pd.wlasnosc_id=2,pd.ilosc,0) as dhp_podlozone
,
if(tp.id=11 and pd.wlasnosc_id=1,pd.ilosc,0) as lpr_pod_towarem,
if(tp.id=11 and pd.wlasnosc_id=2,pd.ilosc,0) as lpr_podlozone
,
if(tp.id=12 and pd.wlasnosc_id=1,pd.ilosc,0) as lpr_1_4_pod_towarem,
if(tp.id=12 and pd.wlasnosc_id=2,pd.ilosc,0) as lpr_1_4_podlozone
,
if(tp.id=14 ,pd.ilosc,0) as kartonow
,
  nr_kwitu as doc_ref,
  uwagi as doc_ref_klient,
  k.logo
FROM
  palety_rozliczenie p
  left join palety_docin_docout pd on pd.doc_id = p.doc_id
  and pd.doc_typ = p.typ
  left join kontrah k on p.kontrah_id = k.id
  left join typypalet tp on tp.id = pd.typypalet_id
WHERE
  p.system_id = ".$params['system_id']." and  p.typ = 4 and doc_date <= '".$params['data1']."' 
  and pd.id is not null) b ";
    
    $result = $db->mGetResult($sql);
    return $result;
}




function sql_test($params, $db) {
    $sql_ins_zad = "SELECT '','"."c"."',\"d\" FROM sql_errorss s;";


    


    //echo "<br>" . $sql_ins_zad;
    $result_zadanie4 = $db->mGetResultAsXML($sql_ins_zad);
    $wynik = empty($result_zadanie4) ? 0 : $result_zadanie4[0]['query'];
    return $wynik;
}


exit();


//sprawdzaj_przydzielaj_zadanie_new("Domański Łukasz", "0", "0", "5", $db);

function sprawdzaj_przydzielaj_zadanie_new($imie_nazwisko, $wysokie, $kompletacja, $stanowisko_id, $db) {




    //$imie_nazwisko = "Badowski Marcinnn";

    $sql = 'SELECT z.id,
             "tak" AS przydzial,
             kompletacja,
             zadanie_head_id,
             wysokie,
             z.przydzielenie_pracownik_id as pracownik_id
      FROM zadania_dane z
      left join zadania_head zh on z.zadanie_head_id=zh.id
      left join pracownicy p on p.id=z.przydzielenie_pracownik_id
      
      WHERE (((z.status=1 or z.status=5 or z.status=8 ) and z.wysokie=' . $wysokie . ' and z.stanowisko_id=' . $stanowisko_id . ' ) or z.status=2 or z.status=21 ) 
      and (p.imie_nazwisko="' . $imie_nazwisko . '" )
        
         and zh.status_dokumentu>0 
        order by IF(z.status=2 or z.status=21,1,0) desc
      LIMIT 1
';
    //echo "<br><br>" . $sql;

    $result = $db->mGetResultAsXML($sql);
//print_r($result);

    if (count($result) == 0) {


        $sql = "SELECT z.id,
             'nie' AS przydzial,
             z.status,zh.typ,
             kompletacja,
             zadanie_head_id,
             wysokie,
             stanowisko_id,
             (select pp.id as pracownik_id from pracownicy pp where pp.imie_nazwisko='" . $imie_nazwisko . "' limit 1) as pracownik_id
      FROM zadania_dane z
left join zadania_head zh on z.zadanie_head_id=zh.id and zh.id is not null
left join pracownicy p on p.id=z.przydzielenie_pracownik_id
      WHERE (z.status=1 or z.status=5 or z.status=8 )
        AND przydzielenie_pracownik_id=0
        AND z.wysokie=$wysokie AND z.stanowisko_id=$stanowisko_id and zh.status_dokumentu>0
        ORDER BY if(z.status=5,1,0) desc,  zh.priorytet DESC,z.priorytet_zadania DESC, z.status desc,
        

        if(zh.planowany_czas_realizacji is null,DATE_ADD(zh.ts, INTERVAL +24 HOUR),
        zh.planowany_czas_realizacji) ASC,
        zh.zadania_head_rodzic_id ASC, 
        z.id ASC   limit 100     
      ";
        //echo "<br><br>".$sql;
        $result2 = $db->mGetResultAsXML($sql);
//    echo "<pre>";
//    print_r($result2);
//    echo "</pre>";

        if (count($result2) > 0) {

            foreach ($result2 as $key => $value) {
                if ($value['typ'] == "4" && $value['status'] == "5") {
                    $sql = "UPDATE zadania_dane zd SET przydzielenie_pracownik_id=" . $value['pracownik_id'] . " where przydzielenie_pracownik_id=0 and zd.id=" . $value['id'] . " ";

                    //echo "<br><br>" . $sql;

                    $result = $db->mGetResultAsXML($sql);
                    if ($db->count > 0) {
                        break;
                    }
                }
                if ($value['typ'] == "4" && $value['status'] != "5") {
                    $sql = "UPDATE zadania_dane zd SET przydzielenie_pracownik_id=" . $value['pracownik_id'] . " where przydzielenie_pracownik_id=0 and zd.status!=5 and zd.zadanie_head_id=" . $value['zadanie_head_id'] . " ";

                    //echo "<br><br>" . $sql;

                    $result = $db->mGetResultAsXML($sql);
                    if ($db->count > 0) {
                        break;
                    }
                }
                if ($value['typ'] == "7") {
                    $sql = "UPDATE zadania_dane zd SET przydzielenie_pracownik_id=" . $value['pracownik_id'] . " where przydzielenie_pracownik_id=0 and zd.id=" . $value['id'] . " ";

                    //echo "<br><br>" . $sql;

                    $result = $db->mGetResultAsXML($sql);
                    if ($db->count > 0) {
                        break;
                    }
                }
            }
        }
    }
}

//$ile = mysql_num_rows($result);
//echo "<br>" . $sql . "<br>";
//if (empty($ilosc_etykiet_fizyczna)) {
//    
//}
//$ilosc_etykiet_fizyczna = count($result);
exit();



if (count($result) > 0) {
    //$ilosc_etykiet = count($result);

    $k = 0;

    for ($i = 0; $i < $ilosc_etykiet_fizyczna; $i++) {
        //echo "<br>".$ilosc_etykiet."<br>";
        //$dl_doc_ref = $aRow['dl_doc_ref'];
        if (!empty($result[$i]['paleta_id'])) {
            $k = $i;
        }


        $dl_doc_ref_klient = $result[$k]['dl_doc_ref_klient'];
        $logo = $result[$k]['logo'];
        //$dl_doc_ref = $aRow['dl_doc_ref'];
        if (($k % 3) == 0) { // zabezpieczenie, bo nie drukowało wszystkich etykiet
            sleep(1);
        }
        $opis = array(
            'wiersz1' => zamianapolskich_zpl($result[$k]['dl_doc_ref']),
            'wiersz2' => zamianapolskich_zpl($result[$k]['dl_doc_ref_klient']),
            'wiersz3' => zamianapolskich_zpl($result[$k]['firma']),
            'wiersz4' => zamianapolskich_zpl($result[$k]['miasto'] . " " . $result[$k]['ulica'] . " " . $result[$k]['lokal']),
            'wiersz5' => $result[$k]['id'],
            'wiersz6' => $result[$k]['paleta_id'],
            'licznik' => ($k + 1) . " z " . $ilosc_etykiet_fizyczna,
        );
        echo "<pre>";
        print_r($opis);
        echo "</pre>";
        //printlabelsmall_opis($opis, $adres_ip_drukarki);
        $komunikat .= $db->errors;
    }





//$komunikat=$sql;
} else {
    $komunikat = "Brak ilości etykiet";
}

show_komunikat_xml($komunikat);


exit();

foreach ($result as $index => $aRow) {
    //$typ = $aRow['typ'];
    //echo "aaa".$aRow['id'];
    $arr = array_merge($arr, $aRow);

    //echo "aaa";
}


if (!empty($arr['typ'])) {
    if ($arr['typ'] == "4") {
        $sql = 'SELECT logo FROM ' . $arr['baza_danych'] . '.delivery d
left join ' . $arr['baza_danych'] . '.kontrah k on d.dl_kontrah_id=k.id
where d.id=' . $arr['doc_id'] . '';
        $result2 = $db->mGetResultAsXML($sql);
        foreach ($result2 as $index => $aRow2) {
            //$typ = $aRow['typ'];
            //echo "aaa".$aRow['id'];
            $arr = array_merge($arr, $aRow2);
            //echo "aaa";
        }
    }

    //blokada by nie wymagał EAN
//        $arr["ean"] = "";
//        $arr["ean_jednostki"] = "";
//        $arr["ean_opakowanie_zbiorcze"] = "";
}
if (!empty($arr['typ'])) {
    if ($arr['typ'] == "7") {
        $arr['baza_danych'] = "wmsgg";
        $sql = 'SELECT concat(doc_type," ",doc_nr) as dokument,doc_nr FROM ' . $arr['baza_danych'] . '.docin d
where d.id=' . $arr['doc_id'] . ' limit 1;';
        //echo $sql;
//            $tmpp = array('komunikat' => $sql);
//        return xml_from_indexed_array($tmpp);

        $result2 = $db->mGetResultAsXML($sql);
        foreach ($result2 as $index => $aRow2) {
            $arr = array_merge($arr, $aRow2);
        }
    }
    if ($arr['typ'] == "6") {
        $arr = array_merge($arr, array('dokument' => $arr['doc_type_nazwa'] . " " . $arr['doc_id']));
    }
}


if (!empty($arr['status'])) {
    if ($arr['status'] == '1' || $arr['status'] == '2' || $arr['status'] == '8') {
        $arr["rodzaj_wymaganej_etykiety_odkladczej"] = "miejsce";
        $sql = 'SELECT sum(if(status=3,1,0)) as ile_zrealizowanych FROM zadania_dane z WHERE z.zadanie_head_id=' . $arr['zadanie_head_id'];
        //echo sql;
        $result2 = $db->mGetResultAsXML($sql);
        //print_r($result2);
        if ($result2[0]['ile_zrealizowanych'] != "0") {
            $arr["rodzaj_wymaganej_etykiety_odkladczej"] = "poprzednia_etykieta";
        }
    }
}

// End clock time in seconds 
// Calculate script execution time 
//$start_time = microtime(true);




xml_from_indexed_array($arr);

function sprawdzaj_przydzielaj_zadanie12312($imie_nazwisko, $wysokie, $kompletacja, $stanowisko_id, $db) {

    $sql = "SELECT z.id,
             'nie' AS przydzial,
             kompletacja,
             zadanie_head_id,
             wysokie,
             (select pp.id as pracownik_id from pracownicy pp where pp.imie_nazwisko='" . $imie_nazwisko . "' limit 1) as pracownik_id
      FROM zadania_dane z
left join zadania_head zh on z.zadanie_head_id=zh.id and zh.id is not null
left join pracownicy p on p.id=z.przydzielenie_pracownik_id
      WHERE (z.status=1 or z.status=5 or z.status=8 )
        AND przydzielenie_pracownik_id=0
        AND z.wysokie=$wysokie AND z.kompletacja=$kompletacja and zh.status_dokumentu>0
        ORDER BY if(z.status=5,1,0) desc,  zh.priorytet DESC,z.priorytet_zadania DESC, z.status desc,
        CASE
          WHEN z.stanowisko_id = $stanowisko_id THEN 1
          ELSE 0
        END DESC,
        if(zh.planowany_czas_realizacji is null,DATE_ADD(zh.ts, INTERVAL +24 HOUR),
        zh.planowany_czas_realizacji) ASC,
        zh.zadania_head_rodzic_id ASC, 
        z.id ASC        
      LIMIT 1";
}

function sprawdzaj_przydzielaj_zadanie_old_2($imie_nazwisko, $wysokie, $kompletacja, $stanowisko_id, $db) {

    $sql_ins_zad = "UPDATE zadania_dane zd
LEFT JOIN
  (SELECT a.*
   FROM
     (SELECT z.id,
             'tak' AS przydzial,
             kompletacja,
             zadanie_head_id,
             wysokie,
             z.przydzielenie_pracownik_id as pracownik_id
      FROM zadania_dane z
      left join zadania_head zh on z.zadanie_head_id=zh.id
      left join pracownicy p on p.id=z.przydzielenie_pracownik_id
      
      WHERE (((z.status=1 or z.status=5 or z.status=8 ) and z.wysokie=$wysokie and z.kompletacja=$kompletacja) or z.status=2 or z.status=21 ) 
      and (p.imie_nazwisko='" . $imie_nazwisko . "' )
        
         and zh.status_dokumentu>0 
        order by IF(z.status=2 or z.status=21,1,0) desc
      LIMIT 1) AS a
   UNION SELECT b.*
   FROM
     (SELECT z.id,
             'nie' AS przydzial,
             kompletacja,
             zadanie_head_id,
             wysokie,
             (select pp.id as pracownik_id from pracownicy pp where pp.imie_nazwisko='" . $imie_nazwisko . "' limit 1) as pracownik_id
      FROM zadania_dane z
left join zadania_head zh on z.zadanie_head_id=zh.id and zh.id is not null
left join pracownicy p on p.id=z.przydzielenie_pracownik_id
      WHERE (z.status=1 or z.status=5 or z.status=8 )
        AND przydzielenie_pracownik_id=0
        AND z.wysokie=$wysokie AND z.kompletacja=$kompletacja and zh.status_dokumentu>0
        ORDER BY if(z.status=5,1,0) desc,  zh.priorytet DESC,z.priorytet_zadania DESC, z.status desc,
        CASE
          WHEN z.stanowisko_id = $stanowisko_id THEN 1
          ELSE 0
        END DESC,
        if(zh.planowany_czas_realizacji is null,DATE_ADD(zh.ts, INTERVAL +24 HOUR),
        zh.planowany_czas_realizacji) ASC,
        zh.zadania_head_rodzic_id ASC, 
        z.id ASC        
      LIMIT 1) AS b
   LIMIT 1) AS b ON (b.id=zd.id      OR zd.zadanie_head_id=b.zadanie_head_id                      AND b.kompletacja=1)
AND b.przydzial='nie'
AND b.wysokie=zd.wysokie
AND b.kompletacja=zd.kompletacja
SET przydzielenie_pracownik_id=b.pracownik_id
WHERE (zd.status=1 or zd.status=5 or zd.status=8  )
  AND if(b.kompletacja=0,(zd.id=b.id),(zd.zadanie_head_id=b.zadanie_head_id
                                       AND b.kompletacja=1))
  AND przydzielenie_pracownik_id=0
  AND zd.wysokie=$wysokie   AND zd.kompletacja=$kompletacja  ";
    //echo "<br>" . $sql_ins_zad;
    // co Autor miał na myśli: najpierw realizuje zadania, które są przydzielone do danej osoby,
    // następnie, jeśli nie ma, to szukaj według wskazanuch parametrów
    // wyszukuje jedno ale sprawdza i robi left join w zależności czy jedno czy wiele

    $result_zadanie4 = $db->mGetResultAsXML($sql_ins_zad);


    $sql_ins_zad = "update  zadania_dane z
left join  zadania_dane zz on zz.zadanie_head_id=z.zadanie_head_id
set z.przydzielenie_pracownik_id=zz.przydzielenie_pracownik_id,z.priorytet_zadania=15
where z.status=6 and zz.status=1 and z.przydzielenie_pracownik_id=0 and zz.przydzielenie_pracownik_id!=0;";
    $result_zadanie15 = $db->mGetResultAsXML($sql_ins_zad);
}



function scenariusz1_ilosc_realizowana_mniejsza($params, $db) {
    // podchodzi do której są dwie dl i jego część palety i stwierdza że jest ilość 0


    $result = pobierz_zadanie_global($params['zadanie_dane_id'], "", $db);
    $aRowZadanie = $value;
    $ilosc_zadania = $aRowZadanie['ilosc'];
    $ilosc_realizowana = $params['ilosc_realizowana'];
    if (count($result) == 0) {
        $komunikat = "Brak informacji o zadaniu";
        return show_komunikat_xml($komunikat);
    }
    if ($aRowZadanie['status'] != "1") {
        $komunikat = "Zadanie zostalo zrealizowane . Przerywam operacje";
        return show_komunikat_xml($komunikat);
    }
    if ($ilosc_realizowana > $ilosc_zadania) {
        $komunikat = "Za duza ilosc pobierana. Przerywam operacje";
        return show_komunikat_xml($komunikat);
    }


    $sql = "SELECT d.id FROM delivery_et d left join dlcollect dc on d.etykieta_id=dc.nr_et and d.delivery_id=nr_dl WHERE d.delivery_id=" . $params['delivery_id'] . " AND d.etykieta_id=" . $aRowZadanie['etykieta_id'] . " AND d.ilosc_zamawiana=" . $aRowZadanie['ilosc'] . " and dc.id is null limit 1;";
    //echo $sql;
    $result_de = $db->mGetResultAsXML($sql);


    if (empty($result_de)) {
        $komunikat = "Brak etykiety " . $aRowZadanie['etykieta_id'] . " w bazie delivery_et, by mozna bylo podmienic";
        return show_komunikat_xml($komunikat);
    }



    $ilosc_niezrealizowana = $ilosc_zadania - $ilosc_realizowana;
    if ($ilosc_niezrealizowana > 0) {
        $szukany_kod_dostepny = szukaj_kod_dostepny_OK($aRowZadanie, $db);
    }
    $ilosc_dostepna_suma = 0;
    foreach ($szukany_kod_dostepny as $key => $value) {
        $ilosc_dostepna_suma += $value['ilosc_dostepna'];
    }

    if ($ilosc_dostepna_suma >= $ilosc_niezrealizowana) {
        foreach ($szukany_kod_dostepny as $key => $value) {
            if ($ilosc_niezrealizowana > 0) {

                if ($value['ilosc_dostepna'] > $ilosc_niezrealizowana && $value['ilosc_dostepna'] > 0) {
                    //$komunikat.="\nPotrzeba: " . $ilosc_niezrealizowana . ", Dodano et:" . $kod[$key]['id'] . " w ilosci:" . ($ilosc_niezrealizowana);
                    $record = array('delivery_id' => $params['delivery_id'], 'etykieta_id' => $value['id'], 'ilosc_zamawiana' => $ilosc_niezrealizowana);
                    print_r($record);
                    $sql = "insert into delivery_et(delivery_id, etykieta_id, ilosc_zamawiana) values (" . $params['delivery_id'] . ", " . $value['id'] . "," . $ilosc_niezrealizowana . ")";
                    echo "<br>" . $sql;
                    $aRowZadanie['etykieta_id'] = $value['id'];
                    $aRowZadanie['paleta_id'] = $value['paleta_id'];
                    $aRowZadanie['stare_m'] = $value['miejscep'];
                    $aRowZadanie['ilosc'] = $ilosc_niezrealizowana;
                    insert_zadanie_dane($aRowZadanie, $db);

                    $ilosc_niezrealizowana = ($ilosc_niezrealizowana - ($value['ilosc_dostepna'] + 0));
                }

                if ($value['ilosc_dostepna'] <= $ilosc_niezrealizowana && $value['ilosc_dostepna'] > 0) {
                    //$komunikat.="\nPotrzeba: " . $ilosc_niezrealizowana . ",Dodano et:" . $kod[$key]['id'] . " w ilosci:" . $kod[$key]['ilosc_dostepna'];
                    $record = array('delivery_id' => $params['delivery_id'], 'etykieta_id' => $value['id'], 'ilosc_zamawiana' => $value['ilosc_dostepna']);
                    print_r($record);
                    $sql = "insert into delivery_et(delivery_id, etykieta_id, ilosc_zamawiana) values (" . $params['delivery_id'] . ", " . $value['id'] . "," . $value['ilosc_dostepna'] . ")";
                    echo "<br>" . $sql;
                    $aRowZadanie['etykieta_id'] = $value['id'];
                    $aRowZadanie['paleta_id'] = $value['paleta_id'];
                    $aRowZadanie['stare_m'] = $value['miejscep'];
                    $aRowZadanie['ilosc'] = $ilosc_niezrealizowana;
                    insert_zadanie_dane($aRowZadanie, $db);


                    $ilosc_niezrealizowana = ($ilosc_niezrealizowana - ($value['ilosc_dostepna'] + 0));
                }
                echo "<br>" . "<br>";
            }
        }
        $sql = "update zadania_dane z set z.ilosc=" . $params['ilosc_realizowana'] . " WHERE z.id=" . $params['zadanie_dane_id'] . " limit 1;";
        echo "<br>" . $sql;







        if ($params['ilosc_realizowana'] == "0") {
            $sql = "delete delivery_et WHERE d.id=" . $result_de[0]['id'] . " limit 1;";
            echo $sql;
        } else {
            $sql = "update delivery_et d set d.ilosc_zamawiana=" . $params['ilosc_realizowana'] . " WHERE d.id=" . $result_de[0]['id'] . " limit 1;";
            echo $sql;
        }
        //$result = $db->mGetResultAsXML($sql);
        //$result = $db->mGetResultAsXML($sql);
    } else {
        echo "Nie można zrealizowac, brak alternatywnych palet";
    }





    echo "<PRE>";
    print_r($szukany_kod_dostepny);
    echo "</PRE>";
}

//$params = array('kod_id' => '22189',  'status_id' => '1', 'lot' => '', 'system_id' => '6');
//$result = szukaj_kod_dostepny_OK($params, $db);
//

function insert_zadanie_dane($params, $db) {
    $sql = "insert into zadania_dane(zadanie_head_id, status, przydzielenie_pracownik_id, stare_m, nowe_m, paleta_id, etykieta_id, kod_id, lot, ilosc, "
            . "stanowisko_id, kompletacja, wysokie) "
            . "values('" . $params['zadanie_head_id'] . "','" . $params['status'] . "','" . $params['przydzielenie_pracownik_id'] . "','" . $params['stare_m'] . "',"
            . " '" . $params['nowe_m'] . "','" . $params['paleta_id'] . "','" . $params['etykieta_id'] . "','" . $params['kod_id'] . "','" . $params['lot'] . "'"
            . ",'" . $params['ilosc'] . "','" . $params['stanowisko_id'] . "','" . $params['kompletacja'] . "','" . $params['wysokie'] . "'); ";

    echo "<br>" . $sql;
}

//echo "<PRE>";
//print_r($result);
//echo "</PRE>";
//return;



function szukaj_kod_dostepny_OK($params, $db) {
    $sql = 'SELECT
(SELECT group_concat(distinct d.delivery_id) as aa FROM delivery_et d left join delivery dd on d.delivery_id=dd.id WHERE d.etykieta_id=e.id and (dd.dl_status<4)) as delivery_nr_grup,
       e.id AS id,
       e.magazyn AS magazyn,
       e.active AS active,
       k.kod_nazwa AS kod_nazwa,
       k.kod AS kod,
       e.miejscep,
       e.dataprod AS dataprod,
       e.data_waznosci AS data_waznosci,
       s.nazwa AS status_nazwa,
       e.status_id AS status_id,
       e.lot AS lot,
       e.paleta_id AS paleta_id,
if(din2.doc_date is null,din.doc_date,din2.doc_date) as data_przyjecia,
       TRIM(TRAILING "."
            FROM TRIM(TRAILING "0"
                      FROM (e.ilosc - sum(IFNULL(IFNULL(ee.ilosc, de.ilosc_zamawiana), 0))))) AS ilosc_dostepna
FROM etykiety e
LEFT JOIN kody AS k ON k.id=e.kod_id
LEFT JOIN status_system AS s ON e.status_id=s.id
LEFT JOIN delivery AS dl ON e.delivery_id=dl.id
LEFT JOIN delivery_et AS de ON e.id=de.etykieta_id
LEFT JOIN dlcollect AS dlc ON e.id=dlc.nr_et
LEFT JOIN etykiety AS ee ON dlc.nr_et=ee.id
left join docin din on e.docin_id=din.id
left join miejsca m on e.miejscep=m.id
left join docin din2 on e.docin_id=din2.id
WHERE e.magazyn!=2 and e.kod_id=' . $params['kod_id'] . ' and e.active=1 and s.nazwa="OK" and ifnull(e.lot,"")="' . $params['lot'] . '"
  AND e.system_id = ' . $params['system_id'] . '
GROUP BY e.id
having ilosc_dostepna>0
order by delivery_nr_grup DESC,
e.ilosc ASC,
m.zbiorka DESC,
data_przyjecia ASC';

    $result = $db->mGetResultAsXML($sql);


    if (empty($result)) {
        $komunikat = "Nie znaleziono pasujacych etykiet";
        return show_komunikat_xml($komunikat);
    }



    return $result;
}

function podmiana_etykiety_zadania($params, $db) {
    $sql = "SELECT z.etykieta_id, z.paleta_id, z.stare_m FROM zadania_dane z
WHERE z.id=" . $params['zadania_dane_id'] . " AND z.id AND z.status=1 limit 1;";
    //echo $sql;
    $result = $db->mGetResultAsXML($sql);


    if (empty($result)) {
        $komunikat = "Brak zadania dla " . $params['etykieta_id'] . " w bazie zadania_dane";
        return show_komunikat_xml($komunikat);
    }

    $sql = "update zadania_dane z set z.etykieta_id=" . $params['etykieta_id_cel'] . ", z.paleta_id=" . $params['paleta_id_cel'] . ",z.stare_m=" . $params['stare_m_cel'] . "   WHERE z.id=" . $params['zadania_dane_id'] . " limit 1;";
    //echo $sql;
    $result = $db->mGetResultAsXML($sql);
}

function podmiana_delivery_et($params, $db) {
    $sql = "SELECT d.id FROM delivery_et d left join dlcollect dc on d.etykieta_id=dc.nr_et and d.delivery_id=nr_dl WHERE d.delivery_id=" . $params['delivery_id'] . " AND d.etykieta_id=" . $params['etykieta_id_zrodlo'] . " AND d.ilosc_zamawiana=" . $params['ilosc_zamawiana'] . " and dc.id is null limit 1;";
    //echo $sql;
    $result = $db->mGetResultAsXML($sql);


    if (empty($result)) {
        $komunikat = "Brak etykiety " . $params['etykieta_id_zrodlo'] . " w bazie delivery_et";
        return show_komunikat_xml($komunikat);
    }

    $sql = "update delivery_et d set d.etykieta_id=" . $params['etykieta_id_cel'] . " WHERE d.id=" . $value['id'] . " limit 1;";
    //echo $sql;
    $result = $db->mGetResultAsXML($sql);
}

function podmiana_etykiety_w_zadaniu($params, $db) {
    
}

function sprawdzanie_czy_ilosc_dostepna($params, $db) {
    $sql = 'SELECT
       e.id AS id,
       e.magazyn AS magazyn,
       e.active AS active,
       k.kod_nazwa AS kod_nazwa,
       k.kod AS kod,
       e.dataprod AS dataprod,
       e.data_waznosci AS data_waznosci,
       s.nazwa AS status_nazwa,
       e.status_id AS status_id,
       e.lot AS lot,
       concat("DS", e.paleta_id) AS paleta_id,
       TRIM(TRAILING "."
            FROM TRIM(TRAILING "0"
                      FROM (e.ilosc - sum(IFNULL(IFNULL(ee.ilosc, de.ilosc_zamawiana), 0))))) AS ilosc_dostepna
FROM etykiety e
LEFT JOIN kody AS k ON k.id=e.kod_id
LEFT JOIN status_system AS s ON e.status_id=s.id
LEFT JOIN delivery AS dl ON e.delivery_id=dl.id
LEFT JOIN delivery_et AS de ON e.id=de.etykieta_id
LEFT JOIN dlcollect AS dlc ON e.id=dlc.nr_et
LEFT JOIN etykiety AS ee ON dlc.nr_et=ee.id
WHERE e.id=' . $params['etykieta_id'] . '
  AND e.system_id = ' . $params['system_id'] . '
GROUP BY e.id';

    $result = $db->mGetResultAsXML($sql);


    if (empty($result)) {
        $komunikat = "Brak takiej etykiety w bazie";
        return show_komunikat_xml($komunikat);
    }

    if (empty($value['active'])) {
        $komunikat = "Etykieta nieaktywna";
        return show_komunikat_xml($komunikat);
    }



    return $value;
}

function sprawdzanie_dl_akceptacja($params, $db) {

    $sql = "SELECT d.zgodnosc FROM delivery d   WHERE d.id='" . $params['delivery_id'] . "' limit 1;";
    echo "<br>" . $sql;
    $result = $db->mGetResultAsXML($sql);
    if (count($result) > 0) {
        if ($value['zgodnosc'] == 1) {


            $sql = "select cc.*,if( cc.ilosc_zam=cc.ilosc_dl,'OK', 'NO OK') as wynik, if(cc.ilosc_dl=cc.ilosc_skaner ,'OK', 'NO OK') as wynik2 from ( select bb.kod,sum(ilosc_zam) as ilosc_zam, sum(ilosc_dl) as ilosc_dl, sum(ilosc_skaner) as ilosc_skaner from ( SELECT a.kod, TRIM(TRAILING '.' FROM TRIM(TRAILING '0' from ROUND(sum(a.ilosc),3))) as ilosc_zam, 0 as ilosc_dl, 0 AS ilosc_skaner FROM zlecenia_dane a left join zlecenia_head ah on ah.id=a.zlecenie_id WHERE ah.delivery_id='" . $params['delivery_id'] . "' GROUP BY a.kod union SELECT k.kod, 0 as ilosc_zam, TRIM(TRAILING '.' FROM TRIM(TRAILING '0' FROM ROUND(sum(de.ilosc_zamawiana), 3))) AS ilosc_dl, (0 ) AS ilosc_skaner FROM etykiety e LEFT JOIN delivery_et de ON e.id=de.etykieta_id LEFT JOIN kody k ON e.kod_id=k.id WHERE de.delivery_id='" . $params['delivery_id'] . "' GROUP BY e.kod_id union SELECT k.kod, 0 as ilosc_zam, (0) AS ilosc_dl, TRIM(TRAILING '.' FROM TRIM(TRAILING '0' FROM ROUND(sum(e.ilosc), 3))) AS ilosc_skaner FROM dlcollect dc LEFT JOIN etykiety e ON e.id=dc.nr_et LEFT JOIN kody k ON e.kod_id=k.id WHERE dc.nr_dl='" . $params['delivery_id'] . "' GROUP BY e.kod_id) as bb group by bb.kod ) as cc;";


            $no_ok = 0;
            $result2 = $db->mGetResultAsXML($sql);

            foreach ($result2 as $key => $value) {

                echo "<PRE>";
                print_r($value);
                echo "</PRE>";

                if ($value['wynik2'] == "NO OK") {
                    $no_ok++;
                }
                if (!empty($params['zlecenie_id'])) {
                    if ($value['wynik'] == "NO OK") {
                        $no_ok++;
                    }
                }
            }

            $sql = "SELECT z.id FROM zlecenia_head z WHERE z.delivery_id='" . $params['delivery_id'] . "' limit 1;";
            echo "<br>" . $sql;
            $result3 = $db->mGetResultAsXML($sql);

//            echo "<PRE>";
//            print_r($result3);
//            echo "</PRE>";
//            return;


            if (count($result3) && count($result2) > 0) {
                $sql = "update delivery d set dl_status=3  WHERE d.id='" . $params['delivery_id'] . "' limit 1;";
                echo "<br>" . $sql;
                $db->mGetResultAsXML($sql);
            }
        }
    }
}

//printlabel("***********",$_GET);
//printlabel("**********",$_GET);


function printlabel($adres_ip, $params) {



    $params['firma'] = "Oriflame";

    $dataprod = $params['dataprod'];
    if (!empty($dataprod)) {
        $params['dataprod_code'] = "11" . date("ymd", strtotime($dataprod));
        $params['dataprod_visible'] = date("y.m.d", strtotime($dataprod));
    } else {
        $params['dataprod_code'] = "";
        $params['dataprod_visible'] = " . . ";
    }

    $data_waznosci = $params['data_waznosci'];

    if (!empty($data_waznosci)) {
        $params['data_waznosci_code'] = "17" . date("ymd", strtotime($data_waznosci));
        $params['data_waznosci_visible'] = date("y.m.d", strtotime($data_waznosci));
    } else {
        $params['data_waznosci_code'] = "";
        $params['data_waznosci_visible'] = " . . ";
    }




    $layout = '
        
^XA
^MMT
^PW815
^LL1119
^LS0
^FO0,55^GB815,0,2^FS
^FT308,47^A0N,39,56^FH\^CI28^FD' . $params['firma'] . '^FS^CI27
^FO8,184^GB799,152,2^FS

^CF0,75,90
^FO20,070^FD' . $params['kod'] . '^FS

^CF1,40,22
^FO010,140^FD' . $params['kod_nazwa'] . '^FS


^FO0,343^GB815,0,2^FS


^FO200,352^BY2^BCN,80,Y,N,Y,N^FD(92)' . $params['kod'] . '(37)' . $params['ilosc'] . '^FS



^FO0,463^GB815,0,2^FS
^CF0,35,40
^FO5,475^FDSSCC^FS
^FO0,615^GB815,0,2^FS
^CF0,35,40
^FO5,520^FDBatch^FS

^CF0,55,65
^FO020,560^FD' . $params['lot'] . '^FS
^CF0,55,65
^FO170,470^FD' . $params['etykieta_klient'] . '^FS


^CF0,35,40
^FO530,530^FDUNITS^FS

^CF0,55,65
^FO520,570^FD' . $params['ilosc'] . '^FS


^FO0,790^GB815,0,2^FS
^FO263,791^GB0,104,2^FS
^FO527,791^GB0,104,2^FS
^FO0,894^GB815,0,2^FS


^CF0,35,35
^FO5,800^FDExpiry^FS


^CF0,25,27
^FO120,815^FD(YY.MM.DD)^FS

^CF0,55
^FO20,845^FD' . $params['data_waznosci_visible'] . '^FS

^CF0,35,40
^FO290,800^FDProd Date^FS

^CF0,55
^FO290,845^FD' . $params['dataprod_visible'] . '^FS

^CF0,35,40
^FO540,800^FDOrgin^FS



^CF0,55,67
^FO540,840^FD' . $params['kraj_symbol'] . ' (' . $params['kraj_kod'] . ')^FS

^BY4,3,120^FT86,743^BCN,,N,N


^FH\^FD>;>800' . $params['etykieta_klient'] . '^FS


^CF0,25,28
^FO250,750^FD(00)' . $params['etykieta_klient'] . '^FS

^FT306,1141^BXN,6,200,0,0,1,_,1
^FH\^FD_1020000000000000000' . $params['etykieta_klient'] . '' . $params['data_waznosci_code'] . '' . $params['dataprod_code'] . '422' . $params['kraj_kod'] . '_1241' . $params['kod'] . '_110' . $params['lot'] . '_137' . $params['ilosc'] . '^FS
^PQ1,0,1,Y
^XZ

';
    $file = fopen("/tmp/etykietakarton.zbr", "w");
    fputs($file, $layout);
    fclose($file);
    //flush();
    //sleep(1);

    $socket = socket_create(AF_INET, SOCK_STREAM, SOL_TCP);
    $result = socket_connect($socket, $adres_ip, "9100");
    socket_write($socket, $layout, strlen($layout));
    socket_close($socket);
}

function delivery_insert_palety_docin_docout($etykieta_id, $nowy_kod, $nowy_kod_nazwa, $db) {
    $sql_ins_zad = 'SELECT e.id AS id, e.system_id AS system_id, e.etykieta_klient AS etykieta_klient, e.magazyn AS magazyn, e.active AS active,
e.miejscep AS miejscep, e.przeznaczenie_id AS przeznaczenie_id, e.kod_id AS kod_id, e.paleta_id AS paleta_id, e.dataprod AS dataprod,
e.data_waznosci AS data_waznosci, e.status AS status, e.blloc AS blloc, e.akcja_id AS akcja_id, e.akcja_id AS akcjanr_id, e.status_prism AS status_prism,
e.stat AS stat, e.status_id AS status_id, st.nazwa AS status_nazwa, st.funkcja_stat AS funkcja_stat,
e.kartony AS kartony, ed3.kolor AS kolor, ed3.plec AS plec, ed3.rozmiar_nr AS rozmiar_nr, ed3.uszkodzenie AS uszkodzenie,
e.lot AS lot, e.sscc AS sscc, e.gtin AS gtin, e.edycja_et AS edycja_et, e.ilosc AS ilosc, e.ts AS ts, e.nretykiety AS nretykiety,
e.docin_id AS docin_id, e.docout_id AS docout_id, e.delivery_id AS delivery_id, e.listcontrol_id AS listcontrol_id,
concat("Hala ",m.hala," ",m.regal,"-",m.miejsce,"-",m.poziom) AS adres, concat(m.regal,"-",m.miejsce,"-",m.poziom) AS adres2,
k.kod_nazwa AS kod_nazwa, k.jm AS jm, k.kod AS kod, k.kod2 AS kod2, (if(e.ilosc is null,1,e.ilosc)/if(k.ilosc_w_opakowaniu=0,1,ilosc_w_opakowaniu)) AS ilosc_opak,
k.opakowanie_jm AS opakowanie_jm, k.ean AS ean, k.ean_jednostki AS ean_jednostki, k.ilosc_w_opakowaniu AS ilosc_w_opakowaniu, js.nazwa AS j_skladowania_nazwa,
concat(p.ilosc, " ",tp.opis ) AS paletanazwa, p.pal_klient AS pal_klient, tp.kod AS tpkod, kout.logo AS koutlogo,
concat(kout.ulica," ",kout.lokal) AS koutulica, concat(kout.kod," ",kout.miasto) AS koutmiasto,
pdocin.imie_nazwisko AS docin_imie_nazwisko, pdout.imie_nazwisko AS pdout_imie_nazwisko, s.skrot AS skrot, m.hala AS hala,
din.doc_nr AS doc_nr, din.doc_date AS doc_date, din.doc_ts AS doc_ts, TRIM(TRAILING "." FROM TRIM(TRAILING "0" from e.ilosc)) AS ilosc,
din.doc_internal AS doc_internal, din.doc_type AS doc_type, din.pracownik_id AS pracownik_id_docin, din.kontrah_id AS kontrah_id_docin,
din.doc_ref AS doc_ref, din.doc_uwagi AS doc_uwagi, din.dostawa_typ AS dostawa_typ, din1.nr_doc_dost AS nr_doc_dost,
din1.data_wystawienia AS data_wystawienia, din1.nr_zam_mpg AS nr_zam_mpg, din1.numeroavviso AS numeroavviso, dout.docout_type AS docout_type,
dout.docout_nr AS docout_nr, dout.docout_date AS docout_date, dout.docout_ts AS docout_ts, dout.pracownik_id AS pracownik_id_docout,
dout.kontrah_id AS kontrah_id_docout, dout.docout_ref AS docout_ref, dout.docout_ref_klient AS docout_ref_klient, dout.docout_uwagi AS docout_uwagi,
dout.pracownik_id_kier AS pracownik_id_kier, dout.docout_internal AS docout_internal, dout.docout_date_req AS docout_date_req, kin.logo AS kinlogo,
kin.dest_code AS dest_code, a.nazwa AS akcja_nazwa,
(select kod from kody kkk where kod="' . $nowy_kod . '" and system_id=48 limit 1) as nowy_kod,
(select kod_nazwa from kody kkk where kod="' . $nowy_kod . '" and system_id=48 limit 1) as nowy_kod_nazwa,
(select kp.kraj_kod from kody kkk left join kraj_pochodzenia kp on kkk.kraj_pochodzenia_id=kp.id where kod="' . $nowy_kod . '" and system_id=48 limit 1) as kraj_kod,
(select kp.kraj_symbol from kody kkk left join kraj_pochodzenia kp on kkk.kraj_pochodzenia_id=kp.id where kod="' . $nowy_kod . '" and system_id=48 limit 1) as kraj_symbol
FROM etykiety e LEFT JOIN status_system AS st ON e.status_id=st.id
LEFT JOIN etykiety_dod3 AS ed3 ON e.id=ed3.id and e.system_id=ed3.system_id LEFT JOIN miejsca AS m ON e.miejscep=m.id
LEFT JOIN kody AS k ON k.id=e.kod_id LEFT JOIN palety AS p ON e.paleta_id=p.id LEFT JOIN typypalet AS tp ON p.typypalet_id=tp.id
LEFT JOIN jednostka_skladowania AS js ON p.j_skladowania_id=js.id LEFT JOIN docin AS din ON e.docin_id=din.id
LEFT JOIN docout AS dout ON e.docout_id=dout.id LEFT JOIN delivery AS dl ON e.delivery_id=dl.id LEFT JOIN
kontrah AS kin ON din.kontrah_id=kin.id LEFT JOIN kontrah AS kout ON dout.kontrah_id=kout.id
LEFT JOIN pracownicy AS pdocin ON din.pracownik_id=pdocin.id LEFT JOIN pracownicy AS pdout ON dout.pracownik_id=pdout.id
LEFT JOIN systemy AS s ON e.system_id=s.wartosc LEFT JOIN docin_dod1 AS din1 ON din.id=din1.docin_id
LEFT JOIN akcja AS a ON a.id=e.akcja_id
WHERE e.id = ' . $etykieta_id . ' AND e.system_id = 48 LIMIT 0, 1';
    //echo "<br>" . $sql_ins_zad;
    $result = $db->mGetResultAsXML($sql_ins_zad);
    foreach ($result as $key => $value) {
        $value['kod'] = $nowy_kod;
        $value['kod_nazwa'] = $nowy_kod_nazwa;
        $value['etykieta_klient'] = get_nowy_sscc("wmsgg", $db);

        print_r($value);

        //printlabel('***********', $value);
        printlabel('**********', $value);

//        if(!empty($result[$key]['ilosc']))
//        {
//            $sql_ins_zad = "insert into palety_docin_docout(doc_id, doc_typ, typypalet_id, ilosc, wlasnosc_id)
//                        values ('" . $delivery_id . "', '5', '" . $result[$key]['typypalet_id'] . "', '" . $result[$key]['ilosc'] . "','1' );";
//        //echo "<br>" . $sql_ins_zad;
//        $result2 = $db->mGetResultAsXML($sql_ins_zad);
//        }        
    }
}

function docnumber_get($baza_danych, $name, $db) {
    $sql = "SELECT last FROM $baza_danych.docnumber d
WHERE d.name='$name' limit 1 "; //,dc_prac_id
    //echo "<br>" . $sql;
    $result = $db->mGetResultAsXML($sql);

    foreach ($result as $index => $aRow) {
        $wynik = $aRow['last'];
    }
    return $wynik;
}

function get_nowy_sscc($baza_danych, $db) {
    $sscc_serial_num = docnumber_increment($baza_danych, 'sscc_serial_num', $db);
    $sscc_company_nu = docnumber_get($baza_danych, 'sscc_company_nu', $db);
    $sscc_prefix = docnumber_get($baza_danych, 'sscc_prefix', $db);
    $sscc = generate_checkdigit($sscc_prefix . $sscc_company_nu . str_pad($sscc_serial_num, 6, '0', STR_PAD_LEFT));
    return $sscc;
}

function generate_checkdigit($upc_code) {
    $odd_total = 0;
    $even_total = 0;

    for ($i = 0; $i < 17; $i++) {
        if ((($i + 1) % 2) == 0) {
            /* Sum even digits */
            $even_total += $upc_code[$i];
        } else {
            /* Sum odd digits */
            $odd_total += $upc_code[$i];
        }
    }

    $sum = (3 * $odd_total) + $even_total;

    /* Get the remainder MOD 10 */
    $check_digit = $sum % 10;

    /* If the result is not zero, subtract the result from ten. */
    $aa = ($check_digit > 0) ? 10 - $check_digit : $check_digit;
    return $upc_code . $aa;
}

function sprawdz_czy_dokument_zawiera_kompletacje($zadanie_head_id, $db) {
    $wynik = "NIE";
    $sql_ins_zad = "SELECT sum(if(kompletacja=1,1,0)) as kompletacja FROM zadania_dane z WHERE zadanie_head_id=" . $zadanie_head_id;
    echo "<br>" . $sql_ins_zad;
    $result_zadanie4 = $db->mGetResult($sql_ins_zad);
    print_r($result_zadanie4);
    if (!empty($result_zadanie4)) {
        if (!empty($result_zadanie4[0]['kompletacja'])) {
            $wynik = "TAK";
        }
    }
    //print_r($result_zadanie4);
//    $wynik = empty($result_zadanie4) ? 0 : count($result_zadanie4[0]);
    return $wynik;
}

function sprawdz_czy_wszystkie_zadanie_dokumentu_sa_wykonane($zadanie_head_id, $db) {
    $wynik = "NIE";
    $sql_ins_zad = "SELECT sum(if(z.status=3,1,0)) as gotowe, sum(1) as wszystkie FROM zadania_dane z WHERE !(status=5 or status=9) and zadanie_head_id= " . $zadanie_head_id;
    //echo "<br>" . $sql_ins_zad;
    $result_zadanie4 = $db->mGetResult($sql_ins_zad);
    if (!empty($result_zadanie4)) {
        if ($result_zadanie4[0]['gotowe'] == $result_zadanie4[0]['wszystkie']) {
            $wynik = "TAK";
        }
    }
    return $wynik;
}

function debug($db) {

    $sql_ins_zad = "update zadania_dane z set kierunek= where id=73; ";
    //echo "<br>" . $sql_ins_zad;
    $result_zadanie4 = $db->mGetResultAsXML($sql_ins_zad);
}

function sprawdz_czy_wszystkie_kompletowane_sa_skompletowane($zadanie_head_id, $db) {
    $wynik = "NIE";
    $sql_ins_zad = "SELECT sum(if(z.status=3,1,0)) as kompletacja, sum(1) as wszystkie FROM zadania_dane z WHERE kompletacja=1 and zadanie_head_id=" . $zadanie_head_id;
    //echo "<br>" . $sql_ins_zad;
    $result_zadanie4 = $db->mGetResult($sql_ins_zad);
    if (!empty($result_zadanie4)) {
        if ($result_zadanie4[0]['kompletacja'] == $result_zadanie4[0]['wszystkie']) {
            $wynik = "TAK";
        }
    }
    //print_r($result_zadanie4);
//    $wynik = empty($result_zadanie4) ? 0 : count($result_zadanie4[0]);
    return $wynik;
}

//echo "<pre>";
//    print_r($result_zadanie4);
//    echo "</pre>";
//    return false;
//echo sprawdz_czy_wszystkie_kompletowane_sa_dostepne_na_dole(5, $db);
//$aa = sprawdz_czy_istnieje_miejsce(1, 110, 74, "A", "wmsgg", $db);


    