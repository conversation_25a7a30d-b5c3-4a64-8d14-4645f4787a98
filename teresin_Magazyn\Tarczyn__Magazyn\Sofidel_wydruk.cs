﻿using System;
using System.Linq;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using MySql.Data.MySqlClient;
using System.Threading;
using System.Net;
using System.IO;
using System.Xml;

namespace Tarczyn__Magazyn
{
    public partial class Sofidel_wydruk : Form
    {
        ActionMenu myParent = null;
        string nr_etykiety = null;
        string aktywna = null;
        private Thread Skanowanie;
        string operac_id_global = "";

        public Sofidel_wydruk(ActionMenu MyParent)
        {

            InitializeComponent();
            this.myParent = MyParent;
            wms_wybrany.Text = Wlasciwosci.system_id;
            imie_nazwisk_label.Text = Wlasciwosci.wozek + ":" + Wlasciwosci.imie_nazwisko;
            Wlasciwosci.CurrentOperacja = "3";
            this.ZacznijSkanowanie();


        }

        private void czysc()
        {
            nr_etykiety_opis.Text = "";
           
        }

        private void CheckString(StringBuilder cc)
        {
            EventHandler method = null;
            while (true)
            {
                if (cc.ToString() != "")
                {
                    if (method == null)
                    {
                        method = delegate
                        {
                            this.wyszukaj_etykiete(cc.ToString());
                        };
                    }
                    this.Invoke(method);
                    return;
                }
                Thread.Sleep(200);
            }
        }

        private void wyszukaj_etykiete(string ops)
        {

            



        }



        private void powrot_Click(object sender, EventArgs e)
        {
            this.Skanowanie.Abort();
            Skaner.Przewij_Skanowanie();
            //BazaDanychExternal.DokonajUpdate("insert into operacje(etykieta_id, doc_type, doc_nr, imie_nazwisko, typ_operacji,system_id,wozek,operac_id,ilosc) values('0','','','" + Wlasciwosci.imie_nazwisko + "','PR_AR','" + Wlasciwosci.system_id_id + "','" + Wlasciwosci.wozek + "','" + operac_id_global + "','0');");

            this.myParent.Show();
            this.Close();
            //BazaDanychExternal.ChangeHost(Wlasciwosci.Varbaza, Wlasciwosci.Varhost, Wlasciwosci.VarLoginMYSQL, Wlasciwosci.VarHasloMYSQL);
        }




        private void button1_Click(object sender, EventArgs e)
        {
            this.button1.Click -= new EventHandler(this.button1_Click);
            this.button1.Click += new EventHandler(this.Zakoncz_Skanowanie);
            this.button1.Text = "Zakończ";
            this.ZacznijSkanowanie();
        }
        private void ZacznijSkanowanie()
        {
            StringBuilder login = new StringBuilder();
            Skaner.UstawTryb_String(login);
            Skaner.Zacznij_Skanowanie();
            this.Skanowanie = new Thread(() => this.CheckString(login));
            this.Skanowanie.IsBackground = true;
            this.Skanowanie.Start();
        }

        private void Zakoncz_Skanowanie(object sender, EventArgs e)
        {
            this.Skanowanie.Abort();
            Skaner.Przewij_Skanowanie();
            this.button1.Click -= new EventHandler(this.Zakoncz_Skanowanie);
            this.button1.Click += new EventHandler(this.button1_Click);
            this.myParent.Show();
            base.Close();
        }

        private void button2_Click(object sender, EventArgs e)
        {
            if (etykieta_text.Text != "")
            {

                string URL = "http://172.6.1.249/wmsgg/public/skaner_api/sofidel_wydruki.php?wz=" + wz_textBox1.Text + "&kod=" + kod_textBox3.Text + "&qt=" + ilosc_textBox2.Text + "&sscc=" + etykieta_text.Text;
                    const string data = @"{""object"":{""name"":""Title""}}";

                    HttpWebRequest request = (HttpWebRequest)WebRequest.Create(URL);
                    request.Method = "POST";
                    request.ContentType = "application/json";
                    request.ContentLength = data.Length;
                    StreamWriter requestWriter = new StreamWriter(request.GetRequestStream(), System.Text.Encoding.ASCII);
                    requestWriter.Write(data);
                    requestWriter.Close();

                    try
                    {
                        // get the response
                        WebResponse webResponse = request.GetResponse();
                        Stream webStream = webResponse.GetResponseStream();
                        StreamReader responseReader = new StreamReader(webStream);
                        string response = responseReader.ReadToEnd();
                        responseReader.Close();


                    }
                    catch (WebException we)
                    {
                        string webExceptionMessage = we.Message;
                    }
                    catch (Exception ex)
                    {
                        // no need to do anything special here....
                    }
                }
            }


        



    }
}