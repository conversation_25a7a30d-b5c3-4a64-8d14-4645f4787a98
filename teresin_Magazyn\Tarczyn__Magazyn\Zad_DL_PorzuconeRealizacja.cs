﻿using System;
using System.Linq;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using System.Threading;
using System.Text.RegularExpressions;
using System.Xml;

namespace Tarczyn__Magazyn
{



    public partial class Zad_DL_PorzuconeRealizacja : Form, IZad_DL
    {
        //MainMenu myParent = null;
        Zad_Main myParent = null;
        TextBox[] TextBoxArray = null;
        XmlNode node = null;
        TextBox AktualnyTextBox = null;


        List<string> _paleta_wybor = new List<string>();
        int[] _paleta = new int[100];


        //string ilosc_w_opakowaniu = "1";
        string ean_jednostki = "";
        string ean = "";
        string ilosc_w_opakowaniu = "";
        string ean_opakowanie_zbiorcze = "";
        string ilosc_szt_w_zbiorczym = "";
        string nosnik_numer = "";




        public Zad_DL_PorzuconeRealizacja(Zad_Main c, XmlNode node2)
        {

            FullScreenMode.OknoOFF(this);
            InitializeComponent();
            TextBoxArray = new TextBox[] { ETYKIETA };
            myParent = c;
            Etykieta.Inicjalizacja();
            wms_wybrany.Text = Wlasciwosci.system_id;
            imie_nazwisk_label.Text = Wlasciwosci.wozek + ":" + Wlasciwosci.imie_nazwisko;
            //Wlasciwosci.system_id_id = "";
            wypelnij_dane(node2);
        }

        void wypelnij_dane(XmlNode node2)
        {

            node = node2;
            label7.Text = node["doc_type_nazwa"].InnerText + " " + node["doc_id"].InnerText;
            label12.Text = "DS" + node["paleta_id"].InnerText;
            textBox4.Text = node["stare_m_nazwa"].InnerText;

            if (node["rodzaj_wymaganej_etykiety_odkladczej"].InnerText == "poprzednia_etykieta")
            {
                label3.Text = "Wczytaj pop. et";
            }

            //licznik_label.Text = "" + node["ile_wszystkich"].InnerText + "/" + node["ile_gotowe"].InnerText + " ; " + node["ile_wszystkich_paleta"].InnerText + "/" + node["ile_gotowe_paleta"].InnerText;
            
            
            if (node["nowe_m"].InnerText == "0")
            {
                miejsce_docelowe_textBox1.Text = "Dowolne";
            }
            else
            {
                miejsce_docelowe_textBox1.Text = node["nowe_m_nazwa"].InnerText;
            }
            
            textBox4.Focus();
            ETYKIETA.Focus();
            message_label.Text = "";
        }


        private void TworzenieDL_Load(object sender, EventArgs e)
        {
            //ETYKIETA.Focus();
        }



        private void GotFocus_TextBox(object sender, EventArgs e)
        {
            Skaner.Przewij_Skanowanie();
            TextBox Pole_Tekstowe = (TextBox)sender;
            AktualnyTextBox = Pole_Tekstowe;
            ZacznijSkanowanie();
        }

        private void LostFocus_TextBox(object sender, EventArgs e)
        {
            AktualnyTextBox = null;
            Skaner.Przewij_Skanowanie();

            TextBox Pole_Tekstowe = (TextBox)sender;

            if (ETYKIETA == Pole_Tekstowe)
            {


            }
        }







        #region Skanowanie

        Thread Skanowanie = null;



        private void ZacznijSkanowanie()
        {
            StringBuilder login = new StringBuilder();
            Skaner.UstawTryb_String(login);
            Skaner.Zacznij_Skanowanie();
            Skanowanie = new Thread(() => CheckString(login));
            Skanowanie.IsBackground = true;
            Skanowanie.Start();
        }

        private void CheckString(StringBuilder cc)
        {
            while (true)
            {
                if (cc.ToString() != "")
                {
                    this.Invoke(new EventHandler(delegate
                    {
                        dodawanie(cc.ToString());
                    }));
                    return;
                }
                Thread.Sleep(200);
            }
        }


        private void Zakoncz_Skanowanie()
        {
            {
                Skanowanie.Abort();
                Skaner.Przewij_Skanowanie();
            }
        }

        private void dodawanie(string ops)
        {
            //MessageBox.Show("TrybSkanu:" + TrybSkanu + "   ,   ops.Length:" + ops.Length);


            AktualnyTextBox.Text = ops;
            if (ops == "")
            {
                MessageBox.Show("Nie wypełniono etykiety");
                return;
            }



            if (AktualnyTextBox == MIEJSCE)
            {
                XmlNode node_etykieta = KomunikacjaSerwer(node, ops, 2);
                if (node_etykieta["komunikat"].InnerText != "OK")
                {
                    //ETYKIETA.Text = "";
                    message_label.BackColor = System.Drawing.Color.Red;

                    ETYKIETA.Focus();
                    MIEJSCE.Focus();
                    MIEJSCE.Text = "";

                    if (node_etykieta["komunikat"].InnerText.Length > 40)
                    {
                        MessageBox.Show(node_etykieta["komunikat"].InnerText);
                    }
                    else
                    {
                        message_label.Text = node_etykieta["komunikat"].InnerText;
                    }
                }
                else
                {
                    node = node_etykieta;

                    //message_label.BackColor = System.Drawing.Color.Lime;




                    if (node["czy_koniec_zadania"].InnerText == "TAK")
                    {

                        string adres_ip_print = adres_ip_drukarki();
                        //MessageBox.Show("" + aa);

                        if (adres_ip_print != "")
                        {
                            XmlDocument doc2 = WebService.Pobierz_XmlDocument("delivery_wydruk_wysylka.php?baza_danych=" + node["baza_danych"].InnerText + "&delivery_id=" + node["doc_id"].InnerText + "&adres_ip_drukarki=" + adres_ip_print + "");
                        }



                        myParent.zadanie_head_id = "";
                    }

                    myParent.Show();
                    myParent.ZacznijNasluchiwanie("", "");
                    this.Close();




                    /*
                    //licznik_label.Text = "" + node_etykieta["ile_wszystkich"].InnerText + " / " + node_etykieta["ile_gotowe"].InnerText;
                    if (node_etykieta["czy_koniec_kompletacji"].InnerText == "NIE")
                    {
                        node_etykieta = myParent.PobieranieZadaniaZserwera("1");
                        this.wypelnij_dane(node_etykieta);

                    }
                    else
                    {
                        myParent.Show();
                        myParent.ZacznijNasluchiwanie("11");
                        this.Close();
                    }
                    */


                }
            }


            if (AktualnyTextBox == ETYKIETA)
            {
                if (ETYKIETA.Text.Replace("DS", "") != node["paleta_id"].InnerText)
                {
                    MessageBox.Show("Niepoprawna etykieta");
                    ETYKIETA.Text = "";
                    //return;
                }
                else
                {
                    MIEJSCE.Focus();
                }                
            }

            


            //this.ZacznijSkanowanie();
        }


        public string adres_ip_drukarki()
        {
            for (int i = 0; i < 3; i++)
            {
                PoleTekstowe XB = new PoleTekstowe("Wczytaj drukarkę do " + Environment.NewLine + "etykiet wysyłkowych","");
                if (XB.ShowDialog() == DialogResult.OK)
                {

                    if (XB.wartosc_wpisana.Substring(0, 2) == "IP")
                    {

                        return XB.wartosc_wpisana.Substring(2, XB.wartosc_wpisana.Length - 2);
                    }
                    else
                    {
                        MessageBox.Show("To nie jest etykieta drukarki");
                    }

                }
                else
                {
                    return "";
                }

            }
            return "";
        }

        private XmlNode KomunikacjaSerwer(XmlNode node_src, string etykieta_scan, int proba)
        {
            XmlDocument doc1_etykieta = WebService.Pobierz_XmlDocument("porzucone_realizacja.php?akcja=realizacja_zadania&zadanie_dane_id=" + node_src["id"].InnerText + "&imie_nazwisko=" + Uri.EscapeUriString(Wlasciwosci.imie_nazwisko) + "&skan=" + etykieta_scan + "&pracownik_id=" + Wlasciwosci.id_Pracownika  );
            XmlNode node_etykieta = WebService.Pobierz_XmlNode(doc1_etykieta, "dane");
            return node_etykieta;
        }




        #endregion






        private void QT_KeyDown(object sender, KeyEventArgs e)
        {

        }





        private void powrot_Click(object sender, EventArgs e)
        {
            Skaner.Przewij_Skanowanie();
            myParent.Show();
            this.Close();
        }

        private void odkladanie_Click(object sender, EventArgs e)
        {
            //myParent.delivery_odkladanie("TAK");
            myParent.Show();
            myParent.ZacznijNasluchiwanie("21", "");

            this.Close();
        }

        private void podglad_button_Click(object sender, EventArgs e)
        {
            Zad_DL_Podglad okno_nowy_nosnik = new Zad_DL_Podglad(this, node);
            okno_nowy_nosnik.Show();
            this.Hide();
        }

        private void Etykieta_KeyDown(object sender, KeyEventArgs e)
        {
            if (e.KeyCode == Keys.Enter)
            {
                this.dodawanie(ETYKIETA.Text);
                //odkladanie_Click(this, new EventArgs());
            }
        }






    }
}