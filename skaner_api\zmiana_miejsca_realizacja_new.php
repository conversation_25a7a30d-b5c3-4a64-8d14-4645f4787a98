<?php

//include_once 'db_pdo.php';
include_once 'Db.class.php';
include_once 'funkcje.inc';
//error_reporting(E_ALL);
//ini_set('display_errors', 1);

$db = new Db();

$baza_danych = $_GET['db'];
$akcja = $_GET['akcja'];
//$system_id = $_GET['system_id'];
$skan = $_GET["etykieta_scan"];
$miejsce_id = $_GET['miejsce_id'];
$zadanie_dane_id = $_GET['zadanie_dane_id'];
$imie_nazwisko = $_GET['imie_nazwisko'];

//sprawdź blokadę wyższych poziomów z więcej niż jedna etykieta ponad poziomem A
//http://25.56.91.22/wmsgg/public/skaner_api/delivery_skanowanie_etykiety.php?db=wmsgg&akcja=szukaj&system_id=6&paleta_id=547144





exit();
$komunikat = "OK";
$czy_koniec_zadania = "NIE";
if ($akcja == "pobranie_miejsca") {
    //echo "11";
    if (substr($skan, 0, 2) == "MP" && empty($miejsce_id)) {
        $arr = explode("-", $skan);
        //echo "22";
        $hala = $arr[1];
        $regal = $arr[2];
        $miejsce = $arr[3];

        $poziomy = wyswietl_poziomy($baza_danych, $hala, $regal, $miejsce, $db);
//            echo "<pre>";
//            echo print_r($poziomy);
//            echo "</pre>";
////            echo "asd:" . count($poziomy);
////            xml_from_indexed_array($poziomy); 
//            return false;
        if (count($poziomy) == 0) {
            $komunikat = "Brak miejsc";
            return show_komunikat_xml($komunikat);
        }



        if (count($poziomy) > 1) {
            return xml_from_indexed_array(array('komunikat' => $komunikat, 'ilosc_pozycji_poziomow' => count($poziomy), 'poziomy' => $poziomy));
        } else {
            $miejsce_id = $poziomy[0]['id'];
        }
    } else if (substr($skan, 0, 2) == "DS") {

        // chwilowo zablokowane wstawianie po DS

        $komunikat = "To nie jest miejsce";
        return show_komunikat_xml($komunikat);


        $sql = 'select dl.nr_dl,hala,regal,miejsce,poziom,e.active,e.miejscep
                            from ' . $baza_danych . '.etykiety e
                            left join ' . $baza_danych . '.miejsca m on e.miejscep=m.id
                            left join ' . $baza_danych . '.dlcollect dl  on dl.nr_et=e.id
                            where e.paleta_id=' . str_replace("DS", "", $skan) . " order by nr_dl desc limit 1 ";
        $result2 = $db->mGetResultAsXML($sql);
        //echo "<br>" . $sql;
        if (empty($result2)) {
            $komunikat = "Brak w bazie etykiety";
            return show_komunikat_xml($komunikat);
        }


        foreach ($result2 as $index => $aRowEtWms) {

            if (!empty($aRowEtWms['nr_dl'])) {
                $komunikat = "Etykieta zarezerwowana DL " . $aRowEtWms['nr_dl'] . "";
                //echo "<br>" . $komunikat;
                //return;
                return show_komunikat_xml($komunikat);
            }
            if ($aRowEtWms['regal'] == "RMP") {
                $komunikat = "Etykieta jest na rampie";
                //echo "<br>" . $komunikat;
                //return;
                return show_komunikat_xml($komunikat);
            }
            $miejsce_id = $aRowEtWms['miejscep'];
        }
    }
//        else if (!empty($skan)) {
//            // gdy etykieta WMS
//
//            $sql = 'select dl.nr_dl,hala,regal,miejsce,poziom,e.miejscep
//                            from ' . $baza_danych . '.etykiety e
//                            left join ' . $baza_danych . '.miejsca m on e.miejscep=m.id
//                            left join ' . $baza_danych . '.dlcollect dl  on dl.nr_et=e.id
//                            where (e.id=' . $skan . ' or e.etykieta_klient="' . $skan . '") order by nr_dl desc limit 1';
//
//            $result2 = $db->mGetResultAsXML($sql);
//            if (empty($result2)) {
//                $komunikat = "Brak w bazie etykiety";
//                return show_komunikat_xml($komunikat);
//            }
//            //echo "<br>" . $sql;
//            foreach ($result2 as $index => $aRowEtWms) {
//
//                if (!empty($aRowEtWms['nr_dl'])) {
//                    $komunikat = "Etykieta zarezerwowana DL " . $aRowEtWms['nr_dl'] . "";
//                    //echo "<br>" . $komunikat;
//                    //return;
//                    return show_komunikat_xml($komunikat);
//                }
//                if ($aRowEtWms['regal'] == "RMP") {
//                    $komunikat = "Etykieta jest na rampie";
//                    //echo "<br>" . $komunikat;
//                    //return;
//                    return show_komunikat_xml($komunikat);
//                }
//                $miejsce_id = $aRowEtWms['miejscep'];
//            }
//        }
}











if (!empty($miejsce_id)) {
    if ($komunikat == "OK") {

        $miejsce_sprawdzone = pobierz_miejsce($baza_danych, $miejsce_id, $db);
        if (count($miejsce_sprawdzone) == 0) {
            $komunikat = "Brak informacji o miejscu " . $miejsce_sprawdzone;
            return show_komunikat_xml($komunikat);
        }
        //$message_info = "Wstawione: H ".$result[0]['hala'] ." ".$result[0]['regal']."-".$result[0]['miejsce']."-".$result[0]['poziom'];

        $result = pobierz_zadanie_global($zadanie_dane_id, "", $db);


        //$komunikat = $sql;
        $pracownik_id = get_pracownik($baza_danych, $imie_nazwisko, $db);


        if (count($result) == 0) {
            $komunikat = "Brak informacji o zadaniu";
            return show_komunikat_xml($komunikat);
        }




        $aRowZadanie = $result[0];
        $zadanie_head_id = $aRowZadanie['zadanie_head_id'];
        $wynik = pobierz_etykiety_id_palety($baza_danych, $aRowZadanie['paleta_id'], $db);
        
        
        $sprawdz_dopuszczalna_wage_palety_miejsca = sprawdz_dopuszczalna_wage_palety_miejsca($aRowZadanie['paleta_id'], $miejsce_id, $db);
        if (!empty($sprawdz_dopuszczalna_wage_palety_miejsca)) {
            $komunikat = $sprawdz_dopuszczalna_wage_palety_miejsca;
            return show_komunikat_xml($komunikat);
        }


        $etykieta_id_realizowana = 0;

        foreach ($wynik as $key => $valued) {
            $sql = "insert into $baza_danych.zmianym 
                        (typ, doc_nr, pracownik_id, data, etykieta, system_id, stare_m, nowe_m, doc_internal, stat, tszm)
                        values('ZM',0, '$pracownik_id',CURDATE(), " . $valued['id'] . "," . $aRowZadanie['system_id'] . "," . $valued['miejscep'] . ", " . $miejsce_id . ", 'Z', 1, NOW())";
            //echo "<br>" . $sql;
            $result2 = $db->mGetResultAsXML($sql);

            $sql = "update $baza_danych.etykiety set miejscep=" . $miejsce_id . " where id=" . $valued['id'] . " limit 1";

            //echo "<br>" . $sql;
            $result2 = $db->mGetResultAsXML($sql);
            $etykieta_id_realizowana = $valued['id'];
        }





        $sql = ' update  zadania_dane z set z.realizacja_pracownik_id=' . $pracownik_id . ', z.stop=NOW(),status=22 WHERE z.id=' . $zadanie_dane_id;
        //echo "<br>" . $sql;
        $result = $db->mGetResultAsXML($sql);




        $operac_id = docnumber_increment($baza_danych, "operacja_id", $db);
        $sql = "insert into operacje(ts_ins,etykieta_id, doc_type, doc_nr, imie_nazwisko, typ_operacji,system_id,wozek,operac_id,ilosc) values('" . $aRowZadanie['start'] . "','" . $etykieta_id_realizowana . "','" . $aRowZadanie['docin_type'] . "','" . $aRowZadanie['doc_nr'] . "','" . $imie_nazwisko . "','DOC_WSTAW','" . $aRowZadanie['system_id'] . "', '0','" . $operac_id . "','0');";
        $result8 = $db->mGetResultAsXML($sql);

        $sql = "insert into operacje(ts_ins,etykieta_id, doc_type, doc_nr, imie_nazwisko, typ_operacji,system_id,wozek,operac_id,ilosc) values(NOW(),'" . $etykieta_id_realizowana . "','" . $aRowZadanie['docin_type'] . "','" . $aRowZadanie['doc_nr'] . "','" . $imie_nazwisko . "','DOC_WSTAW','" . $aRowZadanie['system_id'] . "', '0','" . $operac_id . "','1');";
        $result8 = $db->mGetResultAsXML($sql);

//         $operac_id = docnumber_increment($baza_danych, "operacja_id", $db);
//        $sql = "insert into operacje(ts_ins,etykieta_id, doc_type, doc_nr, imie_nazwisko, typ_operacji,system_id,wozek,operac_id,ilosc) values('" . $aRowZadanie['start'] . "','" . $etykieta_id_realizowana . "','PZ','" . $aRowZadanie['doc_id'] . "','" . $imie_nazwisko . "','DOC_WSTAW','" . $aRowZadanie['system_id'] . "', '0','" . $operac_id . "','0');";
//        $result8 = $db->mGetResultAsXML($sql);
//
//        $sql = "insert into operacje(ts_ins,etykieta_id, doc_type, doc_nr, imie_nazwisko, typ_operacji,system_id,wozek,operac_id,ilosc) values(NOW(),'" . $etykieta_id_realizowana . "','PZ','" . $aRowZadanie['doc_id'] . "','" . $imie_nazwisko . "','DOC_WSTAW','" . $aRowZadanie['system_id'] . "', '0','" . $operac_id . "','1');";
//        $result8 = $db->mGetResultAsXML($sql);
        //zamykanie statusu dokumentu
        $sql = 'SELECT min(status) as status_min,max(status) as status_max FROM zadania_dane z WHERE z.zadanie_head_id=' . $zadanie_head_id;
        $result4 = $db->mGetResultAsXML($sql);
        foreach ($result4 as $index => $aRowZadanie4) {
            if ($aRowZadanie4["status_min"] != "1") { //to jest koniec kompletacji
                $czy_koniec_zadania = "TAK";
                $sql = 'update zadania_head  set status_dokumentu=22 where id=' . $aRowZadanie['zadanie_head_id'] . ' and status_dokumentu!=22 limit 1';
                $result5 = $db->mGetResultAsXML($sql);
            }
        }
    }


    $komunikat .= $db->errors;
    return xml_from_indexed_array(array('komunikat' => $komunikat, 'czy_koniec_zadania' => $czy_koniec_zadania, 'ilosc_pozycji_poziomow' => 1));
}

function wyswietl_poziomy($baza_danych, $hala, $regal, $miejsce, $db) {
    $sql = 'SELECT m.id,m.poziom as nazwa_wyswietlana FROM ' . $baza_danych . '.miejsca m WHERE m.hala="' . $hala . '" AND m.regal="' . $regal . '" AND m.miejsce="' . $miejsce . '" AND m.widoczne=1 
ORDER BY m.poziom DESC;  ';
    //echo $sql;
    $result = $db->mGetResultAsXML($sql);
    return $result;
}

function pobierz_etykiety_id_palety($baza_danych, $paleta_id, $db) {
    $sql = 'select e.id,e.miejscep  from ' . $baza_danych . '.etykiety e  where e.paleta_id=' . $paleta_id . "  ";
    $result2 = $db->mGetResultAsXML($sql);
    return $result2;
}

function pobierz_miejsce($baza_danych, $miejsce_id, $db) {
    $sql = 'select m.*  from ' . $baza_danych . '.miejsca m where m.id=' . $miejsce_id . " limit 1 ";
    $result2 = $db->mGetResultAsXML($sql);
    return $result2;
}

function sprawdz_dopuszczalna_wage_palety_miejsca($paleta_id, $miejsce_id, $db) {
    $komunikat_local = "";
    $sql = 'SELECT m.max_udzwig_kg FROM miejsca m WHERE m.id="' . $miejsce_id . '" limit 1;';
    $result1 = $db->mGetResultAsXML($sql);
//    echo "<pre>";
//    print_r($result1);
//    echo "</pre>";
    $sql = 'select
 round(sum(kk.waga_szt_kg*ee.ilosc),0) as waga_palety from etykiety ee
left join kody kk on ee.kod_id=kk.id
where ee.paleta_id="' . str_replace("DS", "", $paleta_id) . '" and (ee.active=1 or ee.active is null )';
    $result2 = $db->mGetResultAsXML($sql);




    if (!empty($result1) && !empty($result2)) {
//        echo "<pre>";
//        print_r($result2);
//        echo "</pre>";
        $waga_palety = $result2[0]['waga_palety'];
        $waga_miejsca = $result1[0]['max_udzwig_kg'];
        if (($waga_palety + 30) > $waga_miejsca) {
            $komunikat_local = "Nie można wstawić. Waga palety " . ($waga_palety + 30) . " kg przekracza dopuszczalną wagę miejsca $waga_miejsca kg !!!";
        }
    }
    return $komunikat_local;
}
