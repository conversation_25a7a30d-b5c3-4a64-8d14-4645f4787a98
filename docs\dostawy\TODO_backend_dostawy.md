# TODO Backend – <PERSON><PERSON><PERSON>stawy” (.NET API, JSON)

Cel: Zaimplementować API dla procesu rejestracji dostaw zgodnie z PRD_backend_dostawy.md.

## Etap 0 – Przygotowanie (0%)
- [ ] <PERSON><PERSON><PERSON><PERSON><PERSON> źródło listy drukarek (tabela/konfiguracja). (0%)
- [ ] Zweryfikować istnienie docnumber(name='nrpalety'). (0%)

## Etap 1 – Migracje DB (0%)
- [ ] Dodać list_control.realizujacy_pracownik_id INT UNSIGNED NULL + indeks. (0%)
- [ ] Przygotować procedurę atomowego pobierania numeru z docnumber('nrpalety') (transakcja). (0%)

## Etap 2 – Endpointy bazowe (0%)
- [ ] GET /api/dostawy/listy (filtry: docin_id_man IS NULL; opcjonalnie pomijaj zajęte). (0%)
- [ ] POST /api/dostawy/{id}/claim (blokada na pracownika). (0%)
- [ ] GET /api/drukarki (źródło listy drukarek). (0%)

## Etap 3 – Generowanie DS (0%)
- [ ] POST /api/dostawy/{id}/palety/generuj (docnumber + palety + listcontrol_palety). (0%)
- [ ] POST /api/druk/ds (druk Code 128 „DS{nr}”, walidacja IP). (0%)
- [ ] Jednostkowe testy transakcyjności i numeracji. (0%)

## Etap 4 – Skan GS1 i rejestracja pozycji (0%)
- [ ] POST /api/dostawy/{id}/skan (parsowanie AIs 00, 02, 10, 17, 37). (0%)
- [ ] POST /api/dostawy/{id}/nosniki (nowy DS dla trybu „Auto”). (0%)
- [ ] POST /api/dostawy/{id}/pozycje (INSERT do etykiety z walidacjami). (0%)
- [ ] GET /api/dostawy/{id}/nosniki/{paletaId}/pozycje (podgląd). (0%)
- [ ] POST /api/dostawy/{id}/nosniki/{paletaId}/complete. (0%)

## Etap 5 – Zakończenie sesji i spójność (0%)
- [ ] POST /api/dostawy/{id}/koniec (zwolnienie claim, sugestia zamknięcia). (0%)
- [ ] Agregacje ilości vs awizacja – confirm przy niespójnościach. (0%)

## Etap 6 – Testy i logowanie (0%)
- [ ] Pokrycie testami ścieżek pozytywnych i błędów (error/confirm). (0%)
- [ ] Logowanie operacji (claim, DS, inserty etykiet, druk). (0%)

---
Postęp: 0%
