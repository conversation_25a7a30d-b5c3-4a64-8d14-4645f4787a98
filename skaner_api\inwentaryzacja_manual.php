<?php

//include_once 'db_pdo.php';
include_once 'Db.class.php';
include_once 'funkcje.inc';
//error_reporting(E_ALL);
//ini_set('display_errors', 1);
$baza_danych = "wmsgg";
$db = new Db("localhost", $baza_danych);

$akcja = $_GET['akcja'];
$system_id = $_GET['system_id'];

$operac_id = $_GET['operac_id'];
$zm_nr = empty($_GET['zm_nr']) ? 0 : $_GET['zm_nr'];

$inw_data = $_GET['inw_data'];
$inw_opis = $_GET['inw_opis'];
$ilosc = $_GET['ilosc'];
$kod = $_GET['kod'];
$inwentaryzacja_id = $_GET['inwentaryzacja_id'];
$hala = $_GET['hala'];
$regal = $_GET['regal'];
$miejsce = $_GET['miejsce'];
$poziom = $_GET['poziom'];
$imie_nazwisko = $_GET['imie_nazwisko'];
$podkod = $_GET['podkod'];
$inw_status = $_GET['inw_status'];
$proba = $_GET['proba'];
$nrwspolny = $_GET['nrwspolny'];
$etykieta_id = $_GET['etykieta_id'] ? $_GET['etykieta_id'] : 0;
$etykieta_scan = $_GET['etykieta_scan'] ? $_GET['etykieta_scan'] : '';
$wozek = $_GET['wozek'];
$etykieta_klient = $_GET['etykieta_klient'];

if (empty($operac_id)) {
    $operac_id = docnumber_increment($baza_danych, "operacja_id", $db);
}

$pracownik_id = get_pracownik($baza_danych, $imie_nazwisko, $db);
if (empty($pracownik_id)) {
    return xml_from_indexed_array(
            array('komunikat' => "Brak pracownika w bazie. Przerywam operację",
                'operac_id' => $operac_id,
                'ilosc_zliczona' => $stan_inwentaryzacji[0]['ilosc_zliczona'],
                'stan' => $stan_inwentaryzacji[0]['stan'],
            )
    );
}


$komunikat = "OK";
if ($akcja == "zapisz") {
    $etykieta_szukana = inwentaryzacja_szukaj($etykieta_id, $inwentaryzacja_id, $db);
    if (empty($etykieta_szukana[0]['etykieta_id'])) {
        inwentaryzacja_insert($inw_data, $inw_opis, $ilosc, $kod, $inwentaryzacja_id, $hala, $regal, $miejsce, $poziom, $imie_nazwisko, $podkod, $etykieta_scan, $etykieta_klient, $inw_status, $proba, $nrwspolny, $system_id, $db);
    } else {
        $hala_old = $etykieta_szukana[0]['hala'];
        $regal_old = $etykieta_szukana[0]['regal'];
        $miejsce_old = $etykieta_szukana[0]['miejsce'];
        $poziom_old = $etykieta_szukana[0]['poziom'];

        $hala_new = $_GET['hala'];
        $regal_new = $_GET['regal'];
        $miejsce_new = $_GET['miejsce'];
        $poziom_new = $_GET['poziom'];

        $zm_nr_global = "0";

        inwentaryzacja_update($ilosc, $inwentaryzacja_id, $hala, $regal, $miejsce, $poziom, $imie_nazwisko, $podkod, $etykieta_scan, $inw_status, $etykieta_id, $db);

        sprawdz_zmien_miejsce($hala_old, $regal_old, $miejsce_old, $poziom_old, $hala_new, $regal_new, $miejsce_new, $poziom_new, $system_id, $etykieta_id, $pracownik_id, $zm_nr_global, $db);
    }
    operacje_insert($etykieta_id, $inwentaryzacja_id, $imie_nazwisko, $system_id, $wozek, $operac_id, $db);
    $stan_inwentaryzacji = stan_inwentaryzacji($inwentaryzacja_id, $db);
    //print_r($stan_inwentaryzacji[0]);

    return xml_from_indexed_array(
            array('komunikat' => $komunikat,
                'operac_id' => $operac_id,
                'ilosc_zliczona' => $stan_inwentaryzacji[0]['ilosc_zliczona'],
                'stan' => $stan_inwentaryzacji[0]['stan'],
            )
    );

    //print_r($etykieta_szukana[0]);
}

//return xml_from_indexed_array(
//        array('komunikat' => $komunikat,
//            'operac_id' => $operac_id,
//            'ilosc_zliczona' => $stan_inwentaryzacji[0]['ilosc_zliczona'],
//            'stan' => $stan_inwentaryzacji[0]['stan'],
//        )
//);

function stan_inwentaryzacji($inwentaryzacja_id, $db) {
    $sql = "SELECT sum(if(ilosc_spisana is null,0,1)) as ilosc_zliczona, count(1) as stan FROM inwentaryzacja i WHERE i.inwentaryzacja_id=" . $inwentaryzacja_id . " and kod!='10101';";
    //echo "<br>" . $sql . "<br>";
    $result = $db->mGetResultAsXML($sql);
    return $result;
}

function operacje_insert($etykieta_id, $inwentaryzacja_id, $imie_nazwisko, $system_id, $wozek, $operac_id_global, $db) {
    $sql = "insert into operacje(etykieta_id, doc_type, doc_nr,  imie_nazwisko, typ_operacji,system_id, wozek,operac_id,ilosc) values('" . $etykieta_id . "','INW','" . $inwentaryzacja_id . "','" . $imie_nazwisko . "','INW','" . $system_id . "','" . $wozek . "','" . $operac_id_global . "','1');";
    //echo "<br>" . $sql . "<br>";
    $result = $db->mGetResultAsXML($sql);
    return $result;
}

function inwentaryzacja_update($ilosc, $inwentaryzacja_id, $hala, $regal, $miejsce, $poziom, $imie_nazwisko, $podkod, $etykieta_scan, $inw_status, $etykieta_id, $db) {
    $sql = "update inwentaryzacja set ilosc_spisana=" . $ilosc . ",pracownik='" . $imie_nazwisko . "',ts=now(),hala='" . $hala . "',regal='" . $regal . "',miejsce='" . $miejsce . "',poziom='" . $poziom . "',stat='" . $inw_status . "',podkod='" . $podkod . "',skan='" . $etykieta_scan . "' where active=1 and inwentaryzacja_id=" . $inwentaryzacja_id . " and (etykieta_id='" . $etykieta_id . "' ) limit 1;";
    //echo "<br>" . $sql . "<br>";
    $result = $db->mGetResultAsXML($sql);
    return $result;
}

function inwentaryzacja_insert($inw_data, $inw_opis, $ilosc, $kod, $inwentaryzacja_id, $hala, $regal, $miejsce, $poziom, $imie_nazwisko, $podkod, $etykieta_scan, $etykieta_klient, $inw_status, $proba, $nrwspolny, $system_id, $db) {
    $sql = "insert into inwentaryzacja(data,opis,ilosc,ilosc_spisana,kod,inwentaryzacja_id,hala,regal,miejsce,poziom,pracownik,ts,podkod,skan,stat,nrsap,proba,nr_wspolny,system_id) values('" . $inw_data . "','" . $inw_opis . "',0,'" . $ilosc . "','" . $kod . "','" . $inwentaryzacja_id . "','" . $hala . "','" . $regal . "','" . $miejsce . "','" . $poziom . "','" . $imie_nazwisko . "',now(),'" . $podkod . "','" . $etykieta_scan . "','" . $inw_status . "','" . $etykieta_klient . "','" . $proba . "','" . $nrwspolny . "','" . $system_id . "');";
    //echo "<br>" . $sql . "<br>";
    $result = $db->mGetResultAsXML($sql);
    return $result;
}

function inwentaryzacja_szukaj($etykieta, $inwentaryzacja_id, $db) {
    $sql = "select i.kod,i.podkod,TRIM(TRAILING '.' FROM TRIM(TRAILING '0' from ROUND(sum(i.ilosc),3))) as ilosc,i.paleta_id,m.hala,m.regal,m.miejsce,m.poziom,i.hala as hala_i,i.regal as regal_i,i.miejsce as miejsce_i,i.poziom as poziom_i,i.etykieta_id from inwentaryzacja i left join etykiety e on e.id=i.etykieta_id left join miejsca m on m.id=e.miejscep  where (etykieta_id='" . $etykieta . "' ) and inwentaryzacja_id=" . $inwentaryzacja_id . " order by  hala_i  limit 1";
    //echo "<br>" . $sql . "<br>";
    $result = $db->mGetResultAsXML($sql);
    return $result;
}

function sprawdz_zmien_miejsce($hala_old, $regal_old, $miejsce_old, $poziom_old, $hala_new, $regal_new, $miejsce_new, $poziom_new, $system_id, $etykieta_id, $pracownik_id, $zm_nr_global, $db) {
    if ($hala_old != $hala_new || $regal_old != $regal_new || $miejsce_old != $miejsce_new || $poziom_old != $poziom_new) {
        $miejsce_id_old = pobierz_miejsce($hala_old, $regal_old, $miejsce_old, $poziom_old, $db);
        $miejsce_id_new = pobierz_miejsce($hala_new, $regal_new, $miejsce_new, $poziom_new, $db);
        //print_r($miejsce_id_old);
        //print_r($miejsce_id_new);
        if (empty($miejsce_id_new)) {
            return xml_from_indexed_array(
            array('komunikat' => "Nie znaleziono miejsca",
                'operac_id' => 0,
                'ilosc_zliczona' => 0,
                'stan' => 0,
            )
    );
            
            
        }
        if (empty($miejsce_id_old)) {
            $miejsce_id_old['id'] = 0;
        }
//print_r($miejsce_id_old);
        $sql = "insert into zmianym(typ,doc_nr,pracownik_id,data,etykieta,system_id,stare_m,nowe_m,doc_internal,stat,tszm) values(" .
                "'ZM'," . $zm_nr_global . "," . $pracownik_id . ",curdate()," . $etykieta_id . "," . $system_id . "," . $miejsce_id_old['id'] . "," . $miejsce_id_new['id'] . ",3,1,NOW())";
        //echo "\n" . $sql;
        $result2 = $db->mGetResultAsXML($sql);
//        if ($index == 0) {
//            $sql = "insert into operacje(etykieta_id, doc_type, doc_nr, imie_nazwisko, typ_operacji,system_id,wozek,operac_id,ilosc) "
//                    . "values('" . $value['id'] . "','" . $typ_operacji . "','" . $zm_nr_global . "','" . $imie_nazwisko . "','" . $typ_operacji . "','" . $value['system_id'] . "','" . $wozek . "','" . $operacja_id . "','1');";
//            //echo "\n" . $sql;
//            $resul3 = $db->mGetResultAsXML($sql);
//        }
        $sql = "update etykiety set miejscep = " . $miejsce_id_new['id'] . " where id=" . $etykieta_id . " limit 1; ";
        //echo "\n" . $sql;
        $resul3 = $db->mGetResultAsXML($sql);
    }
}

function pobierz_miejsce($hala, $regal, $miejsce, $poziom, $db) {
    $sql = 'select m.*  from miejsca m where m.hala="' . $hala . '" and m.regal="' . $regal . '" and m.miejsce="' . $miejsce . '" and m.poziom="' . $poziom . '" limit 1 ';
    //echo "<br>".$sql;
    $result2 = $db->mGetResultAsXML($sql);
    return $result2[0];
}
