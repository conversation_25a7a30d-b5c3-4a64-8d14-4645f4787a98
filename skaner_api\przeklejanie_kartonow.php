<?php

include_once 'Db.class.php';
include_once 'funkcje.inc';
//error_reporting(E_ALL);
//ini_set('display_errors', 1);

$db = new Db();

//sprawdzanie_wszystkich_zadan zwiazanych z tą paletą, czy_moga_rozpoczac_kompletacje($paleta_id)
////do przetestowania
//$sql = "update $baza_danych.etykiety e set e.paleta_id=$paleta_id WHERE id=" . $id_pobierana . "  ";
//            $result6 = $db->mGetResultAsXML($sql);

delivery_insert_palety_docin_docout($_GET['etykieta_scan'], $_GET['kod_id'], $_GET['ilosc_etykiet'], $db);

//printlabel("***********",$_GET);
//printlabel("**********",$_GET);


function printlabel($adres_ip, $params) {



    $params['firma'] = "Oriflame";

    $dataprod = $params['dataprod'];
    if (!empty($dataprod)) {
        $params['dataprod_code'] = "11" . date("ymd", strtotime($dataprod));
        $params['dataprod_visible'] = date("y.m.d", strtotime($dataprod));
    } else {
        $params['dataprod_code'] = "";
        $params['dataprod_visible'] = " . . ";
    }

    $data_waznosci = $params['data_waznosci'];

    if (!empty($data_waznosci)) {
        $params['data_waznosci_code'] = "17" . date("ymd", strtotime($data_waznosci));
        $params['data_waznosci_visible'] = date("y.m.d", strtotime($data_waznosci));
    } else {
        $params['data_waznosci_code'] = "";
        $params['data_waznosci_visible'] = " . . ";
    }




    $layout = '
        
^XA
^MMT
^PW815
^LL1119
^LS0
^FO0,55^GB815,0,2^FS
^FT308,47^A0N,39,56^FH\^CI28^FD' . $params['firma'] . '^FS^CI27

^CI27 ^FO8,174^GB799,22,2^FS 



^CF0,75,90
^FO20,070^FD' . $params['nowy_kod'] . '^FS

^CF1,40,17
^FO010,140^FD' . $params['nowy_kod_nazwa'] . '^FS


^FO0,203^GB815,0,2^FS 


^FO160,212^BY2^BCN,80,Y,N,Y,N^FD(92)' . $params['nowy_kod'] . '(37)' . $params['ilosc_w_opakowaniu'] . '^FS



^FO0,323^GB815,0,2^FS 
^CF0,35,40 ^FO5,335^FDSSCC^FS 
^FO0,475^GB815,0,2^FS 
^CF0,35,40 ^FO5,380^FDBatch^FS 

^CF0,50,65 ^FO020,420^FD' . $params['lot'] . '^FS
^CF0,50,65 ^FO170,330^FD' . $params['etykieta_klient'] . '^FS


^CF0,30,40 ^FO530,390^FDUNITS^FS

^CF0,50,65 ^FO520,430^FD' . $params['ilosc_w_opakowaniu'] . '^FS


^FO0,640^GB805,0,2^FS 
^FO263,641^GB0,95,2^FS 
^FO527,641^GB0,95,2^FS 
^FO0,735^GB800,0,2^FS  


^CF0,30,35 ^FO5,650^FDExpiry^FS 
^CF0,23,27 ^FO120,665^FD(YY.MM.DD)^FS 

^CF0,45 ^FO20,695^FD' . $params['data_waznosci_visible'] . '^FS

^CF0,30,40 ^FO290,650^FDProd date^FS 

^CF0,45 ^FO290,695^FD' . $params['dataprod_visible'] . '^FS

^CF0,30,40 ^FO540,650^FDOrigin^FS 



^CF0,45,57 ^FO540,695^FD' . $params['kraj_symbol'] . ' (' . $params['kraj_kod'] . ')^FS

^BY4,3,120^FT86,603^BCN,,N,N ^FH\^FD>;>800' . $params['etykieta_klient'] . '^FS


^CF0,25,28 ^FO250,610^FD(00)' . $params['etykieta_klient'] . '^FS

^FT306,948^BXN,6,200,0,0,1,_,1 ^FH\^FD_1020000000000000000' . $params['etykieta_klient'] . '' . $params['data_waznosci_code'] . '' . $params['dataprod_code'] . '422' . $params['kraj_kod'] . '_1241' . $params['nowy_kod'] . '_110' . $params['lot'] . '_137' . $params['ilosc_w_opakowaniu'] . '^FS
^PQ1,0,1,Y
^XZ

';
    //echo $layout;
    $file = fopen("/tmp/etykietakarton.zbr", "w");
    fputs($file, $layout);
    fclose($file);
    //flush();
    //sleep(1);

    $socket = socket_create(AF_INET, SOCK_STREAM, SOL_TCP);
    $result = socket_connect($socket, $adres_ip, "9100");
    socket_write($socket, $layout, strlen($layout));
    socket_close($socket);
}

function delivery_insert_palety_docin_docout($etykieta_id, $nowy_kod_id, $ilosc_etykiet, $db) {
    
    $sql = 'e.id = ' . str_replace("DS", "", $etykieta_id)  . ' AND';
            
    if(strpos($etykieta_id, 'DS') !== false) 
    {
    $sql = ' e.paleta_id = ' . str_replace("DS", "", $etykieta_id)  . ' AND ';
    }
    
    

    $komunikat = "OK";
    $sql_ins_zad = 'SELECT e.id AS id, e.system_id AS system_id, e.etykieta_klient AS etykieta_klient, e.magazyn AS magazyn, e.active AS active,
e.miejscep AS miejscep, e.przeznaczenie_id AS przeznaczenie_id, e.kod_id AS kod_id, e.paleta_id AS paleta_id, e.dataprod AS dataprod,
e.data_waznosci AS data_waznosci, e.status AS status, e.blloc AS blloc, e.akcja_id AS akcja_id, e.akcja_id AS akcjanr_id, e.status_prism AS status_prism,
e.stat AS stat, e.status_id AS status_id, st.nazwa AS status_nazwa, st.funkcja_stat AS funkcja_stat,
e.kartony AS kartony, ed3.kolor AS kolor, ed3.plec AS plec, ed3.rozmiar_nr AS rozmiar_nr, ed3.uszkodzenie AS uszkodzenie,
e.lot AS lot, e.sscc AS sscc, e.gtin AS gtin, e.edycja_et AS edycja_et, e.ilosc AS ilosc, e.ts AS ts, e.nretykiety AS nretykiety,
e.docin_id AS docin_id, e.docout_id AS docout_id, e.delivery_id AS delivery_id, e.listcontrol_id AS listcontrol_id,
concat("Hala ",m.hala," ",m.regal,"-",m.miejsce,"-",m.poziom) AS adres, concat(m.regal,"-",m.miejsce,"-",m.poziom) AS adres2,
k.kod_nazwa AS kod_nazwa, k.jm AS jm, k.kod AS kod, k.kod2 AS kod2, (TRIM(TRAILING "." FROM TRIM(TRAILING "0" from (if(e.ilosc is null,1,e.ilosc)/if(k.ilosc_w_opakowaniu=0,1,ilosc_w_opakowaniu))))) AS ilosc_opak,
k.opakowanie_jm AS opakowanie_jm, k.ean AS ean, k.ean_jednostki AS ean_jednostki, k.ilosc_w_opakowaniu AS ilosc_w_opakowaniu, js.nazwa AS j_skladowania_nazwa,
concat(p.ilosc, " ",tp.opis ) AS paletanazwa, p.pal_klient AS pal_klient, tp.kod AS tpkod, kout.logo AS koutlogo,
concat(kout.ulica," ",kout.lokal) AS koutulica, concat(kout.kod," ",kout.miasto) AS koutmiasto,
pdocin.imie_nazwisko AS docin_imie_nazwisko, pdout.imie_nazwisko AS pdout_imie_nazwisko, s.skrot AS skrot, m.hala AS hala,
din.doc_nr AS doc_nr, din.doc_date AS doc_date, din.doc_ts AS doc_ts, TRIM(TRAILING "." FROM TRIM(TRAILING "0" from e.ilosc)) AS ilosc,
din.doc_internal AS doc_internal, din.doc_type AS doc_type, din.pracownik_id AS pracownik_id_docin, din.kontrah_id AS kontrah_id_docin,
din.doc_ref AS doc_ref, din.doc_uwagi AS doc_uwagi, din.dostawa_typ AS dostawa_typ, din1.nr_doc_dost AS nr_doc_dost,
din1.data_wystawienia AS data_wystawienia, din1.nr_zam_mpg AS nr_zam_mpg, din1.numeroavviso AS numeroavviso, dout.docout_type AS docout_type,
dout.docout_nr AS docout_nr, dout.docout_date AS docout_date, dout.docout_ts AS docout_ts, dout.pracownik_id AS pracownik_id_docout,
dout.kontrah_id AS kontrah_id_docout, dout.docout_ref AS docout_ref, dout.docout_ref_klient AS docout_ref_klient, dout.docout_uwagi AS docout_uwagi,
dout.pracownik_id_kier AS pracownik_id_kier, dout.docout_internal AS docout_internal, dout.docout_date_req AS docout_date_req, kin.logo AS kinlogo,
kin.dest_code AS dest_code, a.nazwa AS akcja_nazwa,
(select kod from kody kkk where kkk.id="' . $nowy_kod_id . '" and system_id=48 limit 1) as nowy_kod,
(select kod_nazwa from kody kkk where kkk.id="' . $nowy_kod_id . '" and system_id=48 limit 1) as nowy_kod_nazwa,
(select kp.kraj_kod from kody kkk left join kraj_pochodzenia kp on kkk.kraj_pochodzenia_id=kp.id where kkk.id="' . $nowy_kod_id . '" and system_id=48 limit 1) as kraj_kod,
(select kp.kraj_symbol from kody kkk left join kraj_pochodzenia kp on kkk.kraj_pochodzenia_id=kp.id where kkk.id="' . $nowy_kod_id . '" and system_id=48 limit 1) as kraj_symbol
FROM etykiety e LEFT JOIN status_system AS st ON e.status_id=st.id
LEFT JOIN etykiety_dod3 AS ed3 ON e.id=ed3.id and e.system_id=ed3.system_id LEFT JOIN miejsca AS m ON e.miejscep=m.id
LEFT JOIN kody AS k ON k.id=e.kod_id LEFT JOIN palety AS p ON e.paleta_id=p.id LEFT JOIN typypalet AS tp ON p.typypalet_id=tp.id
LEFT JOIN jednostka_skladowania AS js ON p.j_skladowania_id=js.id LEFT JOIN docin AS din ON e.docin_id=din.id
LEFT JOIN docout AS dout ON e.docout_id=dout.id LEFT JOIN delivery AS dl ON e.delivery_id=dl.id LEFT JOIN
kontrah AS kin ON din.kontrah_id=kin.id LEFT JOIN kontrah AS kout ON dout.kontrah_id=kout.id
LEFT JOIN pracownicy AS pdocin ON din.pracownik_id=pdocin.id LEFT JOIN pracownicy AS pdout ON dout.pracownik_id=pdout.id
LEFT JOIN systemy AS s ON e.system_id=s.wartosc LEFT JOIN docin_dod1 AS din1 ON din.id=din1.docin_id
LEFT JOIN akcja AS a ON a.id=e.akcja_id
WHERE '.$sql.' e.system_id = 48 LIMIT 0, 1';
    //echo "<br>" . $sql_ins_zad;
    $result = $db->mGetResultAsXML($sql_ins_zad);
    
        if (empty($ilosc_etykiet)) {
            $ilosc_etykietttt = 1;
            
        }
        else{
            $ilosc_etykietttt = $ilosc_etykiet;
        }
        
    foreach ($result as $key => $value) {
//        $value['kod'] = $nowy_kod;
//        $value['kod_nazwa'] = $nowy_kod_nazwa;
        //print_r($value);
        //printlabel('***********', $value);

        
        for ($i = 0; $i < $ilosc_etykietttt; $i++) {
            $value['etykieta_klient'] = get_nowy_sscc("wmsgg", $db);
            printlabel('172.5.1.247', $value);
            //$komunikat="$i;$ilosc_etykiet,";
        }


//        if(!empty($result[$key]['ilosc']))
//        {
//            $sql_ins_zad = "insert into palety_docin_docout(doc_id, doc_typ, typypalet_id, ilosc, wlasnosc_id)
//                        values ('" . $delivery_id . "', '5', '" . $result[$key]['typypalet_id'] . "', '" . $result[$key]['ilosc'] . "','1' );";
//        //echo "<br>" . $sql_ins_zad;
//        $result2 = $db->mGetResultAsXML($sql_ins_zad);
//        }        
    }
    xml_from_indexed_array(array(
        'komunikat' => $komunikat)
    );
}

function docnumber_get($baza_danych, $name, $db) {
    $sql = "SELECT last FROM $baza_danych.docnumber d
WHERE d.name='$name' limit 1 "; //,dc_prac_id
    //echo "<br>" . $sql;
    $result = $db->mGetResultAsXML($sql);

    foreach ($result as $index => $aRow) {
        $wynik = $aRow['last'];
    }
    return $wynik;
}

function get_nowy_sscc($baza_danych, $db) {
    $sscc_serial_num = docnumber_increment($baza_danych, 'sscc_serial_num', $db);
    $sscc_company_nu = docnumber_get($baza_danych, 'sscc_company_nu', $db);
    $sscc_prefix = docnumber_get($baza_danych, 'sscc_prefix', $db);
    $sscc = generate_checkdigit($sscc_prefix . $sscc_company_nu . str_pad($sscc_serial_num, 6, '0', STR_PAD_LEFT));
    return $sscc;
}

function generate_checkdigit($upc_code) {
    $odd_total = 0;
    $even_total = 0;

    for ($i = 0; $i < 17; $i++) {
        if ((($i + 1) % 2) == 0) {
            /* Sum even digits */
            $even_total += $upc_code[$i];
        } else {
            /* Sum odd digits */
            $odd_total += $upc_code[$i];
        }
    }

    $sum = (3 * $odd_total) + $even_total;

    /* Get the remainder MOD 10 */
    $check_digit = $sum % 10;

    /* If the result is not zero, subtract the result from ten. */
    $aa = ($check_digit > 0) ? 10 - $check_digit : $check_digit;
    return $upc_code . $aa;
}
