<?php
// Set error reporting
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Camera configuration
$username = 'admin';
$password = 'taganaga123';
$cameraIP = '***********';
$url = "http://{$cameraIP}/Video/inputs/channels/1/overlays/text/1";

// XML body
$xmlBody = '<?xml version="1.0" encoding="UTF-8"?>
<TextOverlay version="1.0" xmlns="http://www.hikvision.com/ver10/XMLSchema">
<id>1</id>
<enabled>true</enabled>
<posX>56</posX>
<posY>352</posY>
<message>DL12345</message>
</TextOverlay>';

// Initialize cURL session
$ch = curl_init();

// Set cURL options
curl_setopt_array($ch, [
    CURLOPT_URL => $url,
    CURLOPT_RETURNTRANSFER => true,
    CURLOPT_CUSTOMREQUEST => 'PUT',
    CURLOPT_POSTFIELDS => $xmlBody,
    CURLOPT_USERPWD => "$username:$password",
    CURLOPT_HTTPAUTH => CURLAUTH_DIGEST | CURLAUTH_BASIC,
    CURLOPT_HTTPHEADER => [
        'Content-Type: application/xml',
        'Content-Length: ' . strlen($xmlBody)
    ]
]);

// Execute the request
$response = curl_exec($ch);

// Check for errors
if (curl_errno($ch)) {
    echo 'Error: ' . curl_error($ch);
} else {
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    echo 'HTTP Status Code: ' . $httpCode . "\n";
    echo 'Response: ' . $response;
}

// Close cURL session
curl_close($ch);
?>