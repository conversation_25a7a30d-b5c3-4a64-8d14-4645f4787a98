# Dokumentacja funkcjonalności przesunięć magazynowych (WMS) - Część 3

## 3. GŁÓWNE PROCESY BIZNESOWE (WORKFLOW)

### Proces przesunięć magazynowych

#### Krok 1: Inicjalizacja procesu
- Otwarcie formularza przesunięcia
- Pobranie identyfikatora operacji z tabeli docnumber
- Zwiększenie licznika identyfikatora operacji
- Uruchomienie skanowania

#### Krok 2: Skanowanie etykiety
- Przechwycenie zeskanowanego kodu
- Rozpoznanie typu kodu (MP - miejsce, DS - paleta, inne - etykieta)
- Weryfikacja poprawności etykiety w bazie danych

#### Krok 3: Walidacja etykiety
- Sprawdzenie czy etykieta istnieje w systemie
- Sprawdzenie statusu etykiety (czy nie jest zablokowana)
- Sprawdzenie czy towar nie jest przygotowany na wysyłkę
- Zapisanie referencji do etykiety w tabeli zmiany_miejsca_niezrealizowane
- Pobranie informacji o grupie produktu

#### Krok 4: Wybór nowego miejsca
- Skanowanie kodu miejsca (format MP-HALA-REGAL-MIEJSCE-POZIOM)
- Walidacja miejsca (czy istnieje w systemie)
- Wybór poziomu (jeśli jest wiele możliwości)
- Sprawdzenie czy miejsce nie jest zajęte

#### Krok 5: Realizacja przesunięcia
- Wywołanie API przez endpoint zmiana_miejsca_regal_manual.php
- Przekazanie parametrów: etykieta, pracownik, miejsce docelowe
- Obsługa odpowiedzi z serwera (komunikaty, aktualizacja liczników)
- W przypadku sukcesu: wyczyszczenie pól formularza, aktualizacja statystyk

#### Krok 6: Obsługa wyjątków
- Brak etykiety w systemie - komunikat i powrót do skanowania
- Status blokujący etykiety - komunikat i powrót do skanowania
- Etykieta przygotowana na wysyłkę - komunikat i powrót do skanowania
- Błąd podczas komunikacji z API - wyświetlenie komunikatu z API

## 4. SCENARIUSZE UŻYTKOWNIKA

### Standardowy przepływ przesunięcia

**Ekran**: Formularz ZmianaMiejscRegalNoActiveApiOne

1. **Akcja użytkownika**: Skanowanie etykiety produktu
   - **System**: Weryfikacja etykiety w bazie danych
   - **System**: Wyświetlenie aktualnej lokalizacji w polu "Aktualnie:"
   - **System**: Podpowiedź zalecanej grupy miejsc

2. **Akcja użytkownika**: Skanowanie kodu miejsca docelowego
   - **System**: Weryfikacja poprawności miejsca
   - **System**: Wyświetlenie opcji wyboru poziomu (jeśli potrzebne)

3. **Akcja użytkownika**: Wybór poziomu
   - **System**: Wysłanie żądania zmiany miejsca do API
   - **System**: Wyświetlenie komunikatu o powodzeniu operacji
   - **System**: Wyczyszczenie pola etykiety i przygotowanie do kolejnego skanowania

4. **Akcja użytkownika**: Opcjonalnie naciśnięcie "Powtórz to samo miejsce" dla kolejnej etykiety

### Obsługa błędów

1. **Brak etykiety w bazie**
   - **Komunikat**: "Brak etykiety w bazie danych lub jeszcze nie przyjęta!"
   - **Efekt**: Wyczyszczenie pola etykiety

2. **Etykieta zablokowana**
   - **Komunikat**: "Etykieta [numer] ma status [nazwa statusu]"
   - **Efekt**: Wyczyszczenie pola etykiety

3. **Towar przygotowany na wysyłkę**
   - **Komunikat**: "Towar przygotowany na wysyłkę DL [numer]"
   - **Efekt**: Wyczyszczenie pola etykiety

4. **Brak połączenia z bazą danych**
   - **Komunikat**: "Brak połączenia z bazą danych. Proszę o przemieszczenie się do miejsca z zasięgiem WI-FI i jeszcze raz Powrót"

## 5. MAPOWANIE DANYCH

### Pola UI → Kolumny w bazie

| Pole UI | Kolumna w bazie | Format danych |
|--------|----------------|---------------|
| etykieta_textbox | etykiety.id lub etykiety.etykieta_klient | string |
| miejsce_poprzednie | Concat(miejsca.hala, miejsca.regal, miejsca.miejsce, miejsca.poziom) | string |
| miejsce_textBox1 | Concat(hala, regal, miejsce, poziom) | string |
| ilosc_etykiet | COUNT z zapytań Count_PZ i Count_PZ_Wstawione | string |
| last_result_text | Zwracane z API | string |
| licznik_label | Zwracane z API | string |

### Transformacje danych

- **Numer etykiety** → **ID etykiety**:
  - Jeśli zaczyna się od "DS": wyszukiwanie po paleta_id (etykiety.paleta_id = etykieta.Replace("DS", ""))
  - W przeciwnym wypadku: wyszukiwanie po ID lub etykieta_klient (etykiety.id = etykieta OR etykiety.etykieta_klient = etykieta)

- **Kod miejsca (MP-H-R-M-P)** → **Komponenty miejsca**:
  - hala = words[0]
  - regal = words[1] (usunięcie wiodących zer)
  - miejsce = words[2] (usunięcie wiodących zer)
  - poziom = words[3] (usunięcie wiodących zer)
