﻿using System;
using System.Linq;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using System.Threading;
using System.Xml;

namespace Tarczyn__Magazyn
{
    
    


    public partial class Zad_DL_Podglad : Form
    {
        IZad_DL myParent = null;

        XmlNode node_myParent = null;
        XmlNodeList xmlnode = null;
        XmlNode node = null;

        List<string> _zadanie_id = new List<string>();
        List<string> _adres = new List<string>();
        List<string> _kod = new List<string>();
        List<string> _ilosc = new List<string>();
        List<string> _gabaryt = new List<string>();


        public Zad_DL_Podglad(IZad_DL MyParent, XmlNode node2)
        {
            this.myParent = MyParent;
            this.node_myParent = node2;
            
            FullScreenMode.OknoOFF(this);
            InitializeComponent();
            pokaz_podglad();
            //BazaDanychExternal.Inicjalizacja(Wlasciwosci.Varbaza, Wlasciwosci.Varhost, Wlasciwosci.VarLoginMYSQL, Wlasciwosci.VarHasloMYSQL, this);  
        }

        Thread Skanowanie = null;


        private void ZacznijSkanowanie()
        {
            StringBuilder login = new StringBuilder();
            Skaner.UstawTryb_String(login);
            Skaner.Zacznij_Skanowanie();
            Skanowanie = new Thread(() => CheckString(login));
            Skanowanie.IsBackground = true;
            Skanowanie.Start();
        }
        private void CheckString(StringBuilder cc)
        {
            while (true)
            {
                if (cc.ToString() != "")
                {
                    this.Invoke(new EventHandler(delegate
                    {

                        dodawanie(cc.ToString());
                    }));
                    return;
                }
                Thread.Sleep(200);
            }
        }
        private void Zakoncz_Skanowanie()
        {
            try
            {
                Skanowanie.Abort();
                Skaner.Przewij_Skanowanie();
            }
            catch
            {
            }
        }

        private void Exit_Click_1(object sender, EventArgs e)
        {
            //Zakoncz_Skanowanie();
            //BazaDanychExternal.ChangeHost(Wlasciwosci.Varbaza, Wlasciwosci.Varhost, Wlasciwosci.VarLoginMYSQL, Wlasciwosci.VarHasloMYSQL);
            myParent.Show();
            this.Close();
        }


        private void button2_Click(object sender, EventArgs e)
        {


        }

        
        private void dodawanie(string gg)
        {

            
            Zakoncz_Skanowanie();
            
        }


        

        void pokaz_podglad()
        {

            XmlNodeList xmlnode2 = null;
            XmlDocument doc1 = WebService.Pobierz_XmlDocument("delivery_podglad_do_zrealizowania.php?db=" + node_myParent["baza_danych"].InnerText + "&akcja=podglad&delivery_id=" + node_myParent["doc_id"].InnerText);

            xmlnode = doc1.GetElementsByTagName("dane");




            //foreach (XmlNode wynik in xmlnode)
            //{
            //    node = wynik;
            //}

            //if (node["komunikat"].InnerText != "OK")
            //{
            //    MessageBox.Show(node["komunikat"].InnerText);
            //    return;
            //}


            //xmlnode2 = doc1.GetElementsByTagName("etykiety");



            //_adres.Add("adres");
            //_gabaryt.Add("wiel.");
            //_ilosc.Add("ilosc");
            //_kod.Add("kod");
            //_zadanie_id.Add("");

            //foreach (XmlNode NodeXml in xmlnode2)
            //{
            //    DataRow dtrow = dt.NewRow();
            //    XmlElement companyElement = (XmlElement)NodeXml;
            //    dtrow["id"] = NodeXml["id"].InnerText;
            //    dtrow["kod"] = NodeXml["kod"].InnerText;
            //    dtrow["adres"] = NodeXml["adres"].InnerText;

            //    dt.Rows.Add(dtrow);
            //}

            //for (byte k = 0; k < tabela.Rows.Count; k++)
            //{
            //    _poziom.Add(tabela.Rows[k]["poziom"].ToString());
            //    _miejsce_id.Add(tabela.Rows[k]["id"].ToString());
            //}
            //listBox1.Focus();
            //listBox1.DataSource = _poziom;

            








        }

        private void WczytywanieNaLinie_Load_1(object sender, EventArgs e)
        {
            //ZacznijSkanowanie();
        }

        private void button1_Click(object sender, EventArgs e)
        {

        }

        private void listBox1_SelectedIndexChanged(object sender, EventArgs e)
        {

        }

        private void wybor_klawiatura(object sender, KeyPressEventArgs e)
        {

        }

        

  





    }
}