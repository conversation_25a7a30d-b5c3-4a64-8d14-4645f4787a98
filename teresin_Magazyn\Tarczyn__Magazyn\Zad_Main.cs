﻿using System;
using System.Linq;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using MySql.Data.MySqlClient;
using System.Text.RegularExpressions;
using System.Threading;
using System.Net;


using System.Collections;

using System.IO;
using System.Xml;


namespace Tarczyn__Magazyn
{
    public partial class Zad_Main : Form, IZad_Main
    {
        //ActionMenu myParent = null;
        IZad_Parent myParent = null;
        private Thread Skanowanie;
        private Thread Nasluchiwanie;


        public string czas_przydzielenia = "";
        public string czas_pobrania = "";
        public string czas_realizacji = "";
        public int ilosc_realizanych_nosnikow = 0;
        public string stanowisko_id = "0";
        public string kierunek = "IN";


        public string zadanie_typ = "";
        public string info = "";
        public string wysokie = "0";
        public string kompletacja = "0";
        static List<string> _stanowisko_id = new List<string>();
        static List<string> _nazwa_pracy = new List<string>();

        public string praca_ciagla = "TAK";

        enum tryb_pracy
        {
            wysoki_sklad,
            //niski_sklad,
            paleciak,
        }




        public string nasluchiwanie_status = "";


        public string nr_nosnika_kompletowany = "";

        //public string zadanie_head_id = "";

        public string zadanie_head_id_local = "";

        public string zadanie_head_id
        {
            get
            {
                return zadanie_head_id_local;
            }
            set
            {
                zadanie_head_id_local = value;
            }
        }





        StringBuilder next = null;


        Dictionary<string, string> rec = new Dictionary<string, string>();


        public Zad_Main(IZad_Parent MyParent) //ActionMenu MyParent
        {
            //
            InitializeComponent();
            //this.etykieta_textbox.KeyPress += new KeyPressEventHandler(etykieta_textbox_KeyPress);
            this.myParent = MyParent;
            wms_wybrany.Text = Wlasciwosci.system_id;
            imie_nazwisk_label.Text = Wlasciwosci.wozek + ":" + Wlasciwosci.imie_nazwisko;
            //FERRERO_Synchronizacja.SprawdzWolnaNazwePliku();
            timer1.Enabled = false;
            Wlasciwosci.CurrentOperacja = "11";

            Wlasciwosci.historia_skaner = "NIE";

            pobieranie_typow_pracy();

            if (_stanowisko_id.Count > 0)
            {
                stanowisko_id = _stanowisko_id[0];
                label_miejsce_pracy.Text = _nazwa_pracy[0];
            }
            BazaDanychExternal.Zamknij_polaczenie();


        //    Console.WriteLine("Hello World");
        //_nazwa_pracy.Add("Wszystkie");
        //_nazwa_pracy.Add("Produkcja");
        //_nazwa_pracy.Add("Dystrybucja");
        ////_nazwa_pracy.Add("Rampy 12-25");
        //_nazwa_pracy.Add("Rampy 26-60");
        //_stanowisko_id.Add("0");
        //_stanowisko_id.Add("1");
        //_stanowisko_id.Add("2");
        ////_stanowisko_id.Add("3");
        //_stanowisko_id.Add("4");
        //nastepny_typ_pracy(_nazwa_pracy,"Dystrybucja");
	


            /*
            PoleCombobox XC = new PoleCombobox("SELECT id,nazwa FROM zadania_stanowiska", "Wybierz stanowisko", "");
            if (XC.ShowDialog() == DialogResult.OK)
            {
                if (XC.wybierana_nazwa == "")
                {
                    MessageBox.Show("Nie dokonano wyboru");
                    this.Nasluchiwanie.Abort();
                    Wlasciwosci.CurrentOperacja = "0";
                    //myParent.wczytwanieEtykiet = false;
                    this.myParent.Show();
                    timer1.Enabled = false;
                    this.Close();

                    return;
                }
                stanowisko_id = XC.wybierane_id;
                stanowisko_nazwa = XC.wybierana_nazwa;
                //label5.Text = "Stanowisko:" + stanowisko_nazwa;
            }
            else
            {
                

            }
            */

            
             

            //this.ZacznijNasluchiwanie("");
        }

        private void pobieranie_typow_pracy()
        {
            XmlDocument doc1_etykieta = WebService.Pobierz_XmlDocument("zadania_typy_pracy.php?akcja=pobierz_stanowiska_pracy");
            XmlNode node_etykieta = WebService.Pobierz_XmlNode(doc1_etykieta, "dane");
            if (node_etykieta["komunikat"].InnerText != "OK")
            {
                MessageBox.Show(node_etykieta["komunikat"].InnerText);
            }
            else
            {
                XmlNodeList xmlnode2 = WebService.Pobierz_XmlNodeList(doc1_etykieta, "pozycje");

                foreach (XmlNode wynik in xmlnode2)
                {
                    _stanowisko_id.Add(wynik["id"].InnerText);
                    _nazwa_pracy.Add(wynik["nazwa_wyswietlana"].InnerText);
                }
            }
        }



        public void nastepny_typ_pracy(List<string> _nazwa_pracy_local, string obecna_praca)
        {
            int ile = _nazwa_pracy_local.Count - 1;
            int index_obecnej_pracy = _nazwa_pracy_local.IndexOf(obecna_praca);
            if (ile == index_obecnej_pracy)
            {
                stanowisko_id = _stanowisko_id[0];
                label_miejsce_pracy.Text = _nazwa_pracy_local[0];
            }
            else
            {
                stanowisko_id = _stanowisko_id[index_obecnej_pracy + 1];
                label_miejsce_pracy.Text = _nazwa_pracy_local[index_obecnej_pracy + 1];
            }
        }



        public void Nasluchowanie_metoda(String qq,String zadanie_id)
        {
            EventHandler method = null; //to jest delegat

            while (true)
            {

                if (method == null)
                {
                    method = delegate
                    {
                        //bb += 1;
                        this.Nasluchowanie_wykonawca(qq, zadanie_id);
                    };
                }
                this.Invoke(method);
                //return;
                Thread.Sleep(5000);
            }

        }




        XmlNodeList xmlnode = null;
        XmlNode node = null;


        public XmlNode PobieranieZadaniaZserwera(String status_wymagany, String zadanie_id)
        {
            if (tryb_pracy_label.Text == "wysoki_sklad") { wysokie = "1"; kompletacja = "0"; }
            if (tryb_pracy_label.Text == "piesek") { wysokie = "0"; kompletacja = "0"; }
            if (tryb_pracy_label.Text == "paleciak") { wysokie = "0"; kompletacja = "1"; }


            XmlNode tmp_node = null;
            XmlDocument doc1 = WebService.Pobierz_XmlDocument("zadania_rozladunek.php?akcja=wyszukiwanie&imie_nazwisko=" + Uri.EscapeUriString(Wlasciwosci.imie_nazwisko) + "&status_wymagany=" + status_wymagany + "&zadanie_head_id_wymagany=" + this.zadanie_head_id_local + "&wysokie=" + wysokie + "&pracownik_id=" + Wlasciwosci.id_Pracownika + "&kompletacja=" + kompletacja + "&zadania_dane_id=" + zadanie_id + "&stanowisko_id="+stanowisko_id);
            xmlnode = doc1.GetElementsByTagName("dane");
            foreach (XmlNode wynik in xmlnode) 
            {
                tmp_node = wynik;
            }
            return tmp_node;
        }
        private void Nasluchowanie_wykonawca(String status_wymagany,String zadanie_id)
        {
            Thread.Sleep(2000);
            //MessageBox.Show("Szukam zadan na serwerze");
            node = PobieranieZadaniaZserwera(status_wymagany, zadanie_id);
            
            if (node["komunikat"].InnerText != "OK")
            {
                MessageBox.Show(node["komunikat"].InnerText);
            }
            else
            {
                label2.Text = "Sprawdzano: " + node["czas_aktualny"].InnerText;
                if (node["zadan"].InnerText == "1")
                {
                    //button2.Text = "Stop";
                    label8_do_realizacj.Text = "";
                    //MessageBox.Show("typ:" + node["typ"].InnerText + ";status:" + node["status"].InnerText + ";kompletacja:" + node["kompletacja"].InnerText);

                    if (node["typ"].InnerText == "2" || node["typ"].InnerText == "3")
                    {
                        this.Nasluchiwanie.Abort();
                        //ZadanieCzasowe nowy = new ZadanieCzasowe(this, node["zadanie_typ_nazwa"].InnerText, "Numer karty: " + node["numer_karty"].InnerText, node["info"].InnerText, "Palet:" + node["ilosc_jednostek"].InnerText, node["id"].InnerText);

                        //nowy.Show();
                        this.Hide();
                    }






                    if (node["typ"].InnerText == "4" && node["status"].InnerText == "1")
                    {
                        if (node["kompletacja"].InnerText == "1")
                        {
                            this.Nasluchiwanie.Abort();
                            delivery_kompletacja(node);
                        }
                        if (node["kompletacja"].InnerText == "0")
                        {
                            this.Nasluchiwanie.Abort();
                            delivery_pelne(node);
                        }
                        //if (node["kompletacja"].InnerText == "2") // kompletacja kartonów jak GG
                        //{
                        //    this.Nasluchiwanie.Abort();
                        //    delivery_kompletacja(node);
                        //}

                    }
                    // delivery odkladanie
                    if (node["typ"].InnerText == "4" && node["status"].InnerText == "2" && node["kompletacja"].InnerText == "1")
                    {
                        this.Nasluchiwanie.Abort();
                        Zad_DL_Odkl nowy = new Zad_DL_Odkl(this, node, nr_nosnika_kompletowany);
                        nowy.Show();
                        this.Hide();
                    }

                    if (node["typ"].InnerText == "4" && node["status"].InnerText == "2" && node["kompletacja"].InnerText == "0")
                    {
                        //MessageBox.Show("Jest  typ:" + node["typ"].InnerText + ";status:" + node["status"].InnerText + ";kompletacja:" + node["kompletacja"].InnerText);
                        this.Nasluchiwanie.Abort();
                        Zad_DL_Pelna_Odkl okno_kompletacja = new Zad_DL_Pelna_Odkl(this, node, nr_nosnika_kompletowany);
                        okno_kompletacja.Show();
                        this.Hide();
                    }
                    if (node["typ"].InnerText == "4" && node["status"].InnerText == "2" && node["kompletacja"].InnerText == "2")
                    {
                        //MessageBox.Show("Jest  typ:" + node["typ"].InnerText + ";status:" + node["status"].InnerText + ";kompletacja:" + node["kompletacja"].InnerText);
                        this.Nasluchiwanie.Abort();
                        Zad_DL_Odkl okno_kompletacja = new Zad_DL_Odkl(this, node, nr_nosnika_kompletowany);
                        okno_kompletacja.Show();
                        this.Hide();
                    }

                    if ((node["typ"].InnerText == "6" || node["typ"].InnerText == "7") && node["status"].InnerText == "1")
                    {
                        //MessageBox.Show("Jest  typ:" + node["typ"].InnerText + ";status:" + node["status"].InnerText + ";kompletacja:" + node["kompletacja"].InnerText);
                        this.Nasluchiwanie.Abort();
                        Zad_ZM_Pobr okno_kompletacja = new Zad_ZM_Pobr(this, node);
                        okno_kompletacja.Show();
                        this.Hide();
                    }
                    if ((node["typ"].InnerText == "6" || node["typ"].InnerText == "7") && node["status"].InnerText == "21")
                    {
                        this.Nasluchiwanie.Abort();
                        Zad_ZM_Odkl nowy = new Zad_ZM_Odkl(this, node);
                        nowy.Show();
                        this.Hide();
                    }

                    if (node["typ"].InnerText == "4" && node["status"].InnerText == "5") // 
                    {
                        this.Nasluchiwanie.Abort();
                        Zad_DL_Zdejmowanie nowy = new Zad_DL_Zdejmowanie(this, node);
                        nowy.Show();
                        this.Hide();
                    }
                    if (node["typ"].InnerText == "4" && node["status"].InnerText == "8") // 
                    {
                        this.Nasluchiwanie.Abort();
                        Zad_DL_PorzuconeRealizacja nowy = new Zad_DL_PorzuconeRealizacja(this, node);
                        nowy.Show();
                        this.Hide();
                    }



                }
                else
                {
                    label8_do_realizacj.Text = "Brak zadań";
                    //this.zadanie_head_id_local = "";
                    this.Nasluchiwanie.Abort();
                    button2.Text = "Start";
                }

            }
            
            

        }

        public void delivery_pelne(XmlNode node_local)
        {
            Zad_DL_Pelna_Pobr okno_nowy_nosnik = new Zad_DL_Pelna_Pobr(this, node);
            okno_nowy_nosnik.Show();
            this.Hide();
            //return;
        }


        public void delivery_kompletacja(XmlNode node_local)
        {
            //MessageBox.Show("delivery_kompletacja");


            if (nr_nosnika_kompletowany == "")
            {
                Zad_DL_RoczpNos okno_nowy_nosnik = new Zad_DL_RoczpNos(this, node);
                okno_nowy_nosnik.Show();
                this.Hide();
                //return;
            }
            else
            {
                if (node["kompletacja"].InnerText == "1")
                {
                    Zad_DL_Komp okno_kompletacja = new Zad_DL_Komp(this, node, nr_nosnika_kompletowany);
                    okno_kompletacja.Show();
                    this.Hide();
                }
                else
                {
                    Zad_DL_Komp_Etykiet okno_kompletacja = new Zad_DL_Komp_Etykiet(this, node, nr_nosnika_kompletowany);
                    okno_kompletacja.Show();
                    this.Hide();
                }
                
            }


        }


        public void delivery_odkladanie(string odkladanie_wymuszone)
        {
            //MessageBox.Show("delivery_odkladanie, ile_zostalo_zadan:" + node["ile_zostalo_zadan"].InnerText);
            //MessageBox.Show("delivery_odkladanie, nr_nosnika_kompletowany:" + nr_nosnika_kompletowany);
            if (node["ile_zostalo_zadan"].InnerText == "1" || odkladanie_wymuszone == "TAK")
            {
                //node["kompletowana_paleta_id"] = nr_nosnika_kompletowany;
                //MessageBox.Show("2");



                //nr_nosnika_kompletowany = "";
                Zad_DL_Odkl nowy = new Zad_DL_Odkl(this, node, nr_nosnika_kompletowany);
                nowy.Show();
                this.Hide();
            }
            else
            {
                this.Show();
                this.ZacznijNasluchiwanie("", "");
            }


        }







        private void etykieta_textbox_KeyPress(object sender, KeyPressEventArgs e)
        {
            if (e.KeyChar == (char)Keys.Back)
            {
                e.Handled = true;
                //etykieta_textbox.Text = "";
            }
            else
            {
                e.Handled = false;
                return;
            }
        }





        private void Zakoncz_Skanowanie(object sender, EventArgs e)
        {
            this.Skanowanie.Abort();
            Skaner.Przewij_Skanowanie();
        }

        private void powrot_Click(object sender, EventArgs e)
        {
            //BazaDanychExternal.DokonajUpdate("REPLACE INTO zadania_zgloszenia(pracownik_id, nr_wozka, ts, status, kierunek, stanowisko_id) values(" + Wlasciwosci.id_Pracownika + "," + Wlasciwosci.wozek + ",NOW(),'opuszczenie_zadan','" + kierunek + "','" + stanowisko_id + "'); ");

            
            //if (BazaDanychExternal.SprawdzCzyIstniejePolaczenie() == false)
            //{
                
            //    //BazaDanychExternal.Inicjalizacja(Wlasciwosci.Varbaza, Wlasciwosci.Varhost, Wlasciwosci.VarLoginMYSQL, Wlasciwosci.VarHasloMYSQL, null);
            //}
            BazaDanychExternal.ChangeHost(Wlasciwosci.Varbaza, Wlasciwosci.Varhost, Wlasciwosci.VarLoginMYSQL, Wlasciwosci.VarHasloMYSQL);


            Wlasciwosci.historia_skaner = "TAK";
            try
            {
                if (nasluchiwanie_status == "TAK")
                {
                    this.Nasluchiwanie.Abort();
                }
                
                Wlasciwosci.CurrentOperacja = "0";
                //myParent.wczytwanieEtykiet = false;
                timer1.Enabled = false;
            }
            catch (Exception ex)
            {
                MessageBox.Show(ex.ToString());
            }


            this.myParent.Show();
            
            this.Close();



        }


        public void ZacznijNasluchiwanie(String aa, String zadanie_id)
        {
            StringBuilder login = new StringBuilder();
            this.Nasluchiwanie = new Thread(() => this.Nasluchowanie_metoda(aa, zadanie_id));
            this.Nasluchiwanie.IsBackground = true;
            this.Nasluchiwanie.Start();
        }

        private void Zakoncz_Nasluchiwanie(object sender, EventArgs e)
        {
            this.Nasluchiwanie.Abort();
        }


        public void ZacznijSkanowanie()
        {
            StringBuilder login = new StringBuilder();
            Skaner.UstawTryb_String(login);
            Skaner.Zacznij_Skanowanie();
            this.Skanowanie = new Thread(() => this.CheckString(login));
            this.Skanowanie.IsBackground = true;
            this.Skanowanie.Start();
        }



        private void CheckString(StringBuilder cc)
        {
            EventHandler method = null; //to jest delegat

            while (true)
            {
                if (cc.ToString() != "")
                {
                    if (method == null)
                    {
                        method = delegate
                        {
                            this.dodawanie(cc.ToString());
                        };
                    }
                    this.Invoke(method);
                    return;
                }
                Thread.Sleep(200);
            }
        }






        // 'Internal' - wyszukuje w bazie etykiete i wypełnia pola
        private void dodawanie(string ops)
        {

        }

        private void timer1_Tick(object sender, EventArgs e)
        {
            this.Skanowanie.Abort();
            Skaner.Przewij_Skanowanie();

            //myParent.wczytwanieEtykiet = false;
            this.myParent.Show();
            timer1.Enabled = false;
            this.Close();


        }



        #region fullscreen
        private void fullscreenmode()
        {
            this.Width = Screen.PrimaryScreen.Bounds.Width;
            this.Height = Screen.PrimaryScreen.Bounds.Height;
            this.Left = 0;
            this.Top = 0;
        }
        #endregion

        private void button1_Click(object sender, EventArgs e)
        {
            if (tryb_pracy_label.Text == "wysoki_sklad") { tryb_pracy_label.Text = "paleciak";  return; }
            if (tryb_pracy_label.Text == "piesek") { tryb_pracy_label.Text = "wysoki_sklad";   return; }
            if (tryb_pracy_label.Text == "paleciak") { tryb_pracy_label.Text = "piesek"; return; }
        }

        private void button2_Click(object sender, EventArgs e)
        {

            if (button2.Text == "Start")
            {
                label8_do_realizacj.Text = "";
                last_result_text.Text = "";
                //button2.Text = "Stop";
                ZacznijNasluchiwanie("", "");
                nasluchiwanie_status = "TAK";
                return;
            }

            //if (button2.Text == "Stop")
            //{
            //    button2.Text = "Start";
            //    this.Nasluchiwanie.Abort();
            //    //return;
            //}
            
        }

        private void button3_Click(object sender, EventArgs e)
        {
            if (_nazwa_pracy.Count==0)
            {
                MessageBox.Show("Nie można zmienić, nie ma innych stanowisk pracy");
                return;
            }
            nastepny_typ_pracy(_nazwa_pracy, label_miejsce_pracy.Text);          

                
        }

        //private void button1_Click(object sender, EventArgs e)
        //{
        //    PoleCombobox XC = new PoleCombobox("SELECT id,nazwa FROM zadania_stanowiska", "Wybierz paletę", "");
        //    if (XC.ShowDialog() == DialogResult.OK)
        //    {
        //        if (XC.wybierana_nazwa == "")
        //        {
        //            MessageBox.Show("Nie dokonano wyboru");
        //            return;
        //        }

        //    }
        //    else
        //    {

        //    }
        //}











    }
}