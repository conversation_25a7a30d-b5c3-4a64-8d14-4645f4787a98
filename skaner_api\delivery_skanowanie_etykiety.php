<?php

//include_once 'db_pdo.php';
include_once 'Db.class.php';
include_once 'funkcje.inc';
//error_reporting(E_ALL);
//ini_set('display_errors', 1);

$db = new Db();

if (empty($argv)) {
//$argv = array();
    $argv[1] = $_GET['db'];
    $argv[2] = $_GET['akcja'];
}

$baza_danych = $_GET['db'];
$akcja = $_GET['akcja'];
$system_id = $_GET['system_id'];

$skan = $_GET["skan"];
$etykieta_id = $_GET["etykieta_id"];

$kod_id = $_GET["kod_id"];

//$lot = $_GET["lot"];
$doc_id = $_GET["doc_id"];

//http://25.56.91.22/wmsgg/public/skaner_api/delivery_skanowanie_etykiety.php?db=wmsgg&akcja=szukaj&system_id=6&paleta_id=547144
//echo $skan;
if (empty($doc_id)) {
    $komunikat = "Zaktualizuj skaner";
    return show_komunikat_xml($komunikat);
}


$komunikat = "OK";
if ($akcja == "szukaj") {
    if (substr($skan, 0, 2) == "MP") {
        $arr = explode("-", $skan);
        //echo "22";
        $hala = $arr[1];
        $regal = $arr[2];
        $miejsce = $arr[3];


        $sql = 'SELECT e.id,TRIM(TRAILING "." FROM TRIM(TRAILING "0" from e.ilosc)) as ilosc, k.kod,k.kod_nazwa, ifnull(k.ean_jednostki,"") as ean_jednostki, ifnull(k.ean,"") as ean,
                         k.ilosc_w_opakowaniu, ifnull(k.ean_opakowanie_zbiorcze,"") as ean_opakowanie_zbiorcze, k.ilosc_szt_w_zbiorczym,e.active,
                         concat(e.id,", ",TRIM(TRAILING "." FROM TRIM(TRAILING "0" from de.ilosc_zamawiana))," ",k.jm,", ",k.kod) as nazwa_wyswietlana
                         FROM ' . $baza_danych . '.etykiety e left join ' . $baza_danych . '.dlcollect dc on e.id=dc.nr_et left join ' . $baza_danych . '.kody k on e.kod_id=k.id
                             left join ' . $baza_danych . '.miejsca m on e.miejscep=m.id
                                 left join ' . $baza_danych . '.delivery_et de on de.etykieta_id=e.id 
                        WHERE e.system_id=' . $system_id . ' and hala="' . $hala . '" and regal="' . $regal . '" and miejsce="' . $miejsce . '" and m.zbiorka=1 AND e.active=1 and e.kod_id="' . $kod_id . '"  and dc.nr_et is null and de.delivery_id="' . $doc_id . '" order by e.active DESC  '; //and e.kod_id="' . $kod_id . '" 
        //echo $sql;
        //return;
    } else if (substr($skan, 0, 2) == "DS") {
        $sql = 'SELECT e.id,TRIM(TRAILING "." FROM TRIM(TRAILING "0" from e.ilosc)) as ilosc, k.kod,k.kod_nazwa, ifnull(k.ean_jednostki,"") as ean_jednostki, ifnull(k.ean,"") as ean,
                         k.ilosc_w_opakowaniu, ifnull(k.ean_opakowanie_zbiorcze,"") as ean_opakowanie_zbiorcze, k.ilosc_szt_w_zbiorczym,e.active,
                         concat(e.id,", ",TRIM(TRAILING "." FROM TRIM(TRAILING "0" from de.ilosc_zamawiana))," ",k.jm,", ",k.kod) as nazwa_wyswietlana
                         FROM ' . $baza_danych . '.etykiety e left join ' . $baza_danych . '.dlcollect dc on e.id=dc.nr_et left join ' . $baza_danych . '.kody k on e.kod_id=k.id
                             left join ' . $baza_danych . '.delivery_et de on de.etykieta_id=e.id 
                        WHERE e.system_id=' . $system_id . '  and e.paleta_id=' . str_replace("DS", "", $skan) . '  and dc.nr_et is null AND e.active=1 and e.kod_id="' . $kod_id . '" and de.delivery_id="' . $doc_id . '"  order by e.active DESC  '; //
        //echo $sql;
    } else {

        $komunikat = "Etykieta $skan nie spelnia wymagan";
        return show_komunikat_xml($komunikat);
    }
//    else {
//        $sql = 'SELECT e.id,TRIM(TRAILING "." FROM TRIM(TRAILING "0" from e.ilosc)) as ilosc, k.kod,k.kod_nazwa, ifnull(k.ean_jednostki,"") as ean_jednostki, ifnull(k.ean,"") as ean,
//                         k.ilosc_w_opakowaniu, ifnull(k.ean_opakowanie_zbiorcze,"") as ean_opakowanie_zbiorcze, k.ilosc_szt_w_zbiorczym,e.active
//                        FROM ' . $baza_danych . '.etykiety e left join ' . $baza_danych . '.dlcollect dc on e.id=dc.nr_et left join ' . $baza_danych . '.kody k on e.kod_id=k.id
//                        WHERE e.system_id=' . $system_id . ' AND e.id=' . $etykieta_id . ' AND (e.id="' . $skan . '" or e.etykieta_klient="' . $skan . '" )  and dc.nr_et is null AND e.active=1  and e.kod_id="' . $kod_id . '"  order by e.active DESC';
//    }
    //echo $sql;
//    if (count($result) == 0) {
//        $komunikat = "$sql";
//        return show_komunikat_xml($komunikat);
//    }
    $result = $db->mGetResultAsXML($sql);

    if (count($result) == 0) {
        $komunikat = "Etykieta nie spelnia wymagan lub już zrealizowana";
        return show_komunikat_xml($komunikat);
    }
    if ($result[0]['active'] != "1") {
        $komunikat = "WMS nr et:" . $result[0]['id'] . " dla etykiety DS" . $result[0]['paleta_id'] . " nieaktywna";
        return show_komunikat_xml($komunikat);
    }

    return xml_from_indexed_array(array('komunikat' => $komunikat, 'etykiety' => $result, 'ilosc_pozycji' => count($result)));
}
?>