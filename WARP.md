# WARP.md

This file provides guidance to WARP (warp.dev) when working with code in this repository.

## Przegląd Projektu

System WMS (Warehouse Management System) to aplikacja do zarządzania magazynem z użyciem skanerów mobilnych. Składa się z trzech głównych komponentów:

1. **Ap<PERSON><PERSON><PERSON> (C# .NET Compact Framework)** - Aplikacja na skanery Windows CE/Mobile
2. **Backend API (PHP)** - Serwer aplikacyjny udostępniający REST API  
3. **Baza Danych (MySQL)** - Centralna baza danych magazynowych

## Struktura Projektu

```
wms_skanery/
├── docs/                           # Dokumentacja projektu
│   ├── PRD.md                      # Wymagania produktowe
│   ├── ARCHITECTURE.md             # Architektura systemu
│   ├── TODO.md                     # Lista zadań
│   └── STYLE_GUIDE.md             # Przewodnik stylów
├── teresin_Magazyn/               # Główny projekt C#
│   ├── WMS.sln                    # Solution Visual Studio 2008
│   ├── Tarczyn__Magazyn/          # Projekt aplikacji klienckiej
│   │   ├── WMS.csproj             # Plik projektu C#
│   │   ├── MainMenu.cs            # Menu główne i logowanie
│   │   ├── PIKR.cs                # Moduł rejestracji produkcji
│   │   ├── UpdateEtykietaMenu.cs  # Zarządzanie etykietami GS1-128
│   │   ├── WebService.cs          # Komunikacja HTTP/XML z API
│   │   ├── BazaDanychExternal.cs  # Warstwa dostępu do bazy
│   │   └── Etykieta.cs           # Obsługa kodów GS1-128
│   └── Instalator_WMS/            # Projekt instalatora CAB
└── skaner_api/                    # Backend PHP
    ├── api.php                    # Główny endpoint API
    ├── Dab.class.php              # Klasa dostępu do bazy (PDO)
    ├── funkcje.inc                # Funkcje pomocnicze
    └── [inne pliki PHP]           # Endpoints operacji magazynowych
```

## Kluczowe Komendy Deweloperskie

### Kompilacja Projektu C#
```powershell
# Otwórz solution w Visual Studio 2008 lub nowszym
devenv "teresin_Magazyn\WMS.sln"

# Kompilacja z linii komend (jeśli MSBuild dostępny)
msbuild "teresin_Magazyn\WMS.sln" /p:Configuration=Release
```

### Praca z PHP Backend
```powershell
# Uruchomienie lokalnego serwera PHP (testowanie)
php -S localhost:8080 -t skaner_api/

# Sprawdzenie składni PHP
php -l skaner_api/api.php
```

### Git Workflow
```powershell
# Synchronizacja z GitLab
git remote add origin http://gitlab.partners.pl/pro_deweloperzy/wms_skanery.git
git push -u origin main
```

## Architektura Komunikacji

### Protokół Komunikacyjny
- **Klient → Serwer**: HTTP GET z parametrami w URL
- **Serwer → Klient**: Odpowiedzi XML w dwóch formatach:
  - Nowy format: `<dane><status>success|error|confirm</status><message>...</message></dane>`
  - Stary format: `<dane><komunikat>OK|Błąd</komunikat></dane>`

### Główne Endpointy API
- `skaner_api/api.php?akcja=dodawanie` - Rejestracja pracowników na liniach produkcyjnych
- Pozostałe pliki PHP w `skaner_api/` obsługują różne operacje magazynowe

### Przykład Wywołania API
```
GET /skaner_api/api.php?akcja=dodawanie&typ_operacji=start&godzina=08:00&nr_karty=12345&wyrob_id=67&utworzyl=user1
```

## Kluczowe Klasy i Moduły

### Aplikacja Kliencka (C#)

#### MainMenu.cs
- System logowania przez karty pracownicze
- Zarządzanie wózkami widlowymi
- Automatyczne aktualizacje aplikacji
- Nawigacja do funkcji systemu

#### PIKR.cs  
- Rejestracja pracowników na liniach produkcyjnych
- Obsługa rozpoczęcia/zakończenia pracy
- Przenoszenie osób między deklaracjami produkcyjnymi

#### UpdateEtykietaMenu.cs
- Dekodowanie kodów GS1-128 (EAN128)
- Aktualizacja etykiet produktów
- Walidacja dat produkcji i ważności
- Zarządzanie paletami

#### WebService.cs
- Komunikacja HTTP z serwerem API (172.6.1.249)
- Parsowanie odpowiedzi XML
- Obsługa błędów połączenia
- Specjalne endpointy (np. serwer wagi na porcie 8881)

#### BazaDanychExternal.cs
- Wykonywanie zapytań SQL z retry logic
- Transakcje bazodanowe  
- Logowanie błędów do tabeli `sql_errors`

### Backend PHP

#### Dab.class.php
- Klasa PDO do komunikacji z MySQL
- Przygotowane zapytania (prepared statements)
- Obsługa błędów SQL

## Baza Danych - Kluczowe Tabele

### Produkcja
- `deklaracje` - Deklaracje produkcyjne
- `deklaracja_wyroby` - Relacja deklaracji z wyrobami
- `deklaracja_osoby` - Czas pracy osób przy deklaracjach

### Magazyn  
- `palety` - Dane palet magazynowych
- `lokalizacje` - Struktura lokalizacji
- `wozki` - Dane wózków widlowych
- `wozki_historia` - Historia użytkowania wózków

### System
- `osoby` - Pracownicy z numerami kart
- `uzytkownicy` - Konta systemowe
- `setup` - Konfiguracja systemu
- `sql_errors` - Rejestr błędów SQL

## Środowisko Deweloperskie

### Wymagania
- **Klient**: Visual Studio 2008+, .NET Compact Framework, Windows CE/Mobile SDK
- **Serwer**: PHP 7.0+, MySQL 5.7+, Apache/Nginx
- **Narzędzia**: Git, opcjonalnie MSBuild

### Konfiguracja Połączeń
- Dane dostępowe do bazy: `/etc/www_pass/pikr.env`
- Serwer API: `http://172.6.1.249/wmsgg/public/skaner_api/`

## Specyficzne Uwagi Deweloperskie

### Obsługa Kodów GS1-128
System intensywnie wykorzystuje standard GS1-128 dla identyfikacji towarów:
- AI 01: GTIN (kod produktu)
- AI 10: Numer partii/serii  
- AI 15/17: Daty ważności
- AI 21: Numer seryjny
- AI 30: Ilość w sztukach

### Legacy System
- Aplikacja kliencka oparta na .NET Compact Framework (Windows CE/Mobile)
- Visual Studio 2008 jako środowisko deweloperskie
- Pliki instalacyjne CAB dla wdrożeń na urządzeniach mobilnych

### Bezpieczeństwo
- Prepared statements w PHP zapobiegają SQL Injection
- Walidacja parametrów wejściowych w API
- Rejestracja błędów i audyt operacji

## Dokumentacja

Kompletna dokumentacja znajduje się w folderze `docs/`:
- `PRD.md` - Szczegółowe wymagania funkcjonalne
- `ARCHITECTURE.md` - Głęboka analiza architektury i bazy danych  
- `TODO.md` - Lista zadań i postęp prac
- `STYLE_GUIDE.md` - Konwencje kodowania

## Uwagi o Komunikacji

Pamiętaj o używaniu języka polskiego w komunikacji, zgodnie z regułami projektu. Zawsze sprawdzaj dokumentację w folderze `docs/` przed wprowadzaniem zmian.
