﻿using System;
using System.Linq;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using MySql.Data.MySqlClient;
using System.Text.RegularExpressions;
using System.Threading;
using System.Net;


using System.Collections;  

using System.IO;
using System.Xml;


namespace Ta<PERSON>zyn__Magazyn
{
    public partial class KonsolidacjaEtykiet : Form, IGlobal
    {
        ActionMenu myParent = null;
        List<string> _hala = new List<string>();
        List<string> _regal = new List<string>();
        List<string> _poziom = new List<string>();
        List<string> Etykiety_dodane = new List<string>();
        int z = 0;
        int licznik = 0;
        public string poziom = "";
        public string prawa = "";
        public string lewa = "";

        public string hala = "";
        public string regal = "";
        public string miejsce = "";
        public string paleta_id = "";

        public string regal_ostatnio = "";

        public string wysokosc_opis = "";
        public string grupa_id = "";

        string operac_id_global = "";
        int nowe_m = 0;

        public string ostatnio_miejsce = "";
        public string id_zmiany_miejsca_niezrealizowane = "0";

        TextBox AktualnyTextBox = null;



        private Thread Skanowanie;
        Dictionary<string, string> rec = new Dictionary<string, string>();

        string zm_nr_global = "";
        public static string etykieta_ostatnia = "";
        public static string docin_ostatnie = "";

        static string ilosc_Count_PZ = "0";
        static string ilosc_Count_PZ_Wstawione = "0";

        string typ_operacji = "ZM";

        string zrodlo_kod_id = "";
        string zrodlo_etykieta_id = "";

        string cel_kod_id = "";
        string cel_etykieta_id = "";

        string docin_id = "";
        string docout_id = "";



        public KonsolidacjaEtykiet(ActionMenu MyParent)
        {
            InitializeComponent();
            this.myParent = MyParent;
            wms_wybrany.Text = Wlasciwosci.system_id;
            imie_nazwisk_label.Text = Wlasciwosci.wozek + ":" + Wlasciwosci.imie_nazwisko;
            timer1.Enabled = false;
            Wlasciwosci.CurrentOperacja = "11";
            this.ZacznijSkanowanie();
            zrodlo_etykieta_ds.Focus();
        }

        private void etykieta_textbox_KeyPress(object sender, KeyPressEventArgs e)
        {
            if (e.KeyChar == (char)Keys.Back)
            {
                e.Handled = true;
                zrodlo_etykieta_ds.Text = "";
            }
            else
            {
                e.Handled = false;
                return;
            }
        }





        public int Get_Signal_Int()
        {
            return myParent.myWlan.get_Signal_int();
        }

        private void Zakoncz_Skanowanie(object sender, EventArgs e)
        {
            this.Skanowanie.Abort();
            Skaner.Przewij_Skanowanie();
        }

        private void powrot_Click(object sender, EventArgs e)
        {


            if (docin_ostatnie != "")
            {
                /*  // by nie przychodził mail jak nie wstawione
                if (kontrola_zmian_miejsc_przyjecie() == true)
                {

                }
                else
                {
                    return;
                }
                 * */
            }

            this.Skanowanie.Abort();
            Skaner.Przewij_Skanowanie();

            Wlasciwosci.CurrentOperacja = "0";
            //myParent.wczytwanieEtykiet = false;
            this.myParent.Show();
            timer1.Enabled = false;
            this.Close();



        }



        public void ZacznijSkanowanie()
        {
            StringBuilder login = new StringBuilder();
            Skaner.UstawTryb_String(login);
            Skaner.Zacznij_Skanowanie();
            this.Skanowanie = new Thread(() => this.CheckString(login));
            this.Skanowanie.IsBackground = true;
            this.Skanowanie.Start();
        }



        private void CheckString(StringBuilder cc)
        {
            EventHandler method = null; //to jest delegat

            while (true)
            {
                if (cc.ToString() != "")
                {
                    if (method == null)
                    {
                        method = delegate
                        {
                            this.dodawanie(cc.ToString());
                        };
                    }
                    this.Invoke(method);
                    return;
                }
                Thread.Sleep(200);
            }
        }







        // 'Internal' - wyszukuje w bazie etykiete i wypełnia pola
        private void dodawanie(string ops)
        {
            //Skanowanie.Abort();
            AktualnyTextBox.Text = ops;



            if (AktualnyTextBox == cel_etykieta_ds)
            {

                if (cel_etykieta_ds.Text != "")
                {
                    XmlDocument doc1_etykieta = WebService.Pobierz_XmlDocument("konsolidacja_skanowanie.php?skan=" + ops + "&akcja=szukaj&system_id=" + Wlasciwosci.system_id_id);
                    XmlNode node_etykieta = WebService.Pobierz_XmlNode(doc1_etykieta, "dane");
                    if (node_etykieta["komunikat"].InnerText != "OK")
                    {
                        AktualnyTextBox.Text = "";
                        MessageBox.Show(node_etykieta["komunikat"].InnerText);
                    }
                    else
                    {
                        cel_kod_id = node_etykieta["kod_id"].InnerText;
                        cel_etykieta_id = node_etykieta["id"].InnerText;
                        cel_nazwa.Text = node_etykieta["kod"].InnerText + " - " + node_etykieta["kod_nazwa"].InnerText;
                        cel_ilosc.Text = node_etykieta["ilosc"].InnerText;
                        button4.Focus();
                    }
                }
            }




            if (AktualnyTextBox == zrodlo_etykieta_ds)
            {

                if (zrodlo_etykieta_ds.Text != "")
                {
                    XmlDocument doc1_etykieta = WebService.Pobierz_XmlDocument("konsolidacja_skanowanie.php?skan="+ops+"&akcja=szukaj&system_id="+Wlasciwosci.system_id_id);
                    XmlNode node_etykieta = WebService.Pobierz_XmlNode(doc1_etykieta, "dane");
                    if (node_etykieta["komunikat"].InnerText != "OK")
                    {
                        AktualnyTextBox.Text = "";
                        MessageBox.Show(node_etykieta["komunikat"].InnerText);

                    }
                    else
                    {
                        zrodlo_kod_id = node_etykieta["kod_id"].InnerText;
                        zrodlo_etykieta_id = node_etykieta["id"].InnerText;
                        zrodlo_nazwa.Text =  node_etykieta["kod"].InnerText+" - "+node_etykieta["kod_nazwa"].InnerText;
                        zrodlo_ilosc.Text = node_etykieta["ilosc"].InnerText;
                        cel_etykieta_ds.Focus();
                    }
                }
            }




            //if (ops.Substring(0, 2) == "MP")
            //{
            //    if (zrodlo_etykieta_ds.Text == "")
            //    {
            //        MessageBox.Show("Zeskanuj najpierw etykietę towaru");
            //        ZacznijSkanowanie();
            //        return;
            //    }
            //    ostatnio_miejsce = ops;

            //    Regex regex = new Regex("-");
            //    string[] words = null;
            //    words = regex.Split(ops.Substring(3, ops.Length - 3));
            //    hala = words[0].ToString();
            //    if (words[1].ToString() != "0")// wyjątek jak miejsce jest 0
            //    {
            //        regal = words[1].ToString().TrimStart(new Char[] { '0' });
            //    }
            //    else
            //    {
            //        regal = words[1].ToString();
            //    }
            //    if (words[2].ToString() != "0")// wyjątek jak miejsce jest 0
            //    {
            //        miejsce = words[2].ToString().TrimStart(new Char[] { '0' });
            //    }
            //    else
            //    {
            //        miejsce = words[2].ToString();
            //    }


            //    Serwer_Komunikacja("", ops, zrodlo_etykieta_ds.Text);
                
            //}
            //else
            //{

            //    if (ops.Length <= 3)
            //    {
            //        MessageBox.Show("To nie jest numer etykiety");
            //        ZacznijSkanowanie();
            //        return;
            //    }


            //    if (zrodlo_etykieta_ds.Text.Length == 0)
            //    {
            //        zrodlo_etykieta_ds.Text = ops;


            //        if (Etykiety_dodane.Contains(zrodlo_etykieta_ds.Text))
            //        {
            //            DialogResult dialogresult = MessageBox.Show("Ta etykieta byla juz skanowana.Czy chcesz ponowic operacje ?", "", MessageBoxButtons.YesNo, MessageBoxIcon.None, MessageBoxDefaultButton.Button1);
            //            if (dialogresult == DialogResult.Yes)
            //            {
            //                DodajEtykiete(ops);
            //            }
            //            else
            //            {
            //                zrodlo_etykieta_ds.Text = "";
            //            }
            //        }
            //        else
            //        {
            //            DodajEtykiete(ops);
            //        }

            //        get_etykieta(ops);

            //    }
            //    else
            //    {
            //        MessageBox.Show("Nie można wczytać nowej. Nie wczytano miejsca do poprzedniej " + ops);
            //    }

            //}
            //ZacznijSkanowanie();
        }













        private void DodajEtykiete(string etykieta)
        {
            zrodlo_etykieta_ds.Text = etykieta;
        }







        private void czysc()
        {
            zrodlo_etykieta_ds.Text = "";
            zrodlo_etykieta_id = "";
            zrodlo_ilosc.Text = "";
            zrodlo_kod_id = "";
            zrodlo_nazwa.Text = "";
            cel_etykieta_ds.Text = "";
            cel_etykieta_id = "";
            cel_ilosc.Text = "";
            cel_nazwa.Text = "";
            cel_kod_id = "";
            zrodlo_etykieta_ds.Focus();
        }



        private void timer1_Tick(object sender, EventArgs e)
        {
            this.Skanowanie.Abort();
            Skaner.Przewij_Skanowanie();

            //myParent.wczytwanieEtykiet = false;
            this.myParent.Show();
            timer1.Enabled = false;
            this.Close();


        }



        #region fullscreen
        private void fullscreenmode()
        {
            this.Width = Screen.PrimaryScreen.Bounds.Width;
            this.Height = Screen.PrimaryScreen.Bounds.Height;
            this.Left = 0;
            this.Top = 0;
        }
        #endregion

        private void Konsoliduj_Click(object sender, EventArgs e)
        {
            XmlDocument doc1_etykieta = WebService.Pobierz_XmlDocument("konsolidacja_realizacja.php?akcja=konsolidacja&pob_et_id=" + zrodlo_etykieta_id + "&zos_et_id=" + cel_etykieta_id + "&imie_nazwisko=" + Wlasciwosci.imie_nazwisko + "&docin_id=" + docin_id + "&docout_id=" + docout_id + "&system_id=" + Wlasciwosci.system_id_id);
            XmlNode node_etykieta = WebService.Pobierz_XmlNode(doc1_etykieta, "dane");
            if (node_etykieta["komunikat"].InnerText != "OK")
            {


                

                //MessageBox.Show("1");
                //MessageBox.Show(node_etykieta["komunikat"].InnerXml);
                AktualnyTextBox = null;
                //MessageBox.Show("1a");
                MessageBox.Show(node_etykieta["komunikat"].InnerText);
                //MessageBox.Show("2");
                czysc();

            }
            else
            {
                label4.Text = "Aktualnie: DS" + node_etykieta["paleta_id"].InnerText + " Ilość:" + node_etykieta["ilosc"].InnerText + Environment.NewLine + " " + node_etykieta["kod"].InnerText;
                docin_id = node_etykieta["docin_id"].InnerText;
                docout_id = node_etykieta["docout_id"].InnerText;
                czysc();
            }
            //MessageBox.Show("3");
        }



        private void button2_Click(object sender, EventArgs e)
        {
            this.Skanowanie.Abort();
            Skaner.Przewij_Skanowanie();
            //Wolne_Miejsca dlg = new Wolne_Miejsca((object)this);
            //dlg.Show();
            //this.Hide();
        }

        private void button3_Click(object sender, EventArgs e)
        {
            zrodlo_etykieta_ds.Text = "";

        }

        private void button5_Click(object sender, EventArgs e)
        {

            Skaner.Przewij_Skanowanie();
            if (myParent.myWlan.get_Signal_int() < 2)
            {
                MessageBox.Show("Trwa łączenie. Spróbuj ponownie...");
                return;
            }
            SzukanieKodu tt = null;
            if (zrodlo_kod_id != "")
            {
                tt = new SzukanieKodu(this, zrodlo_kod_id);
            }
            else
            {
                tt = new SzukanieKodu(this, "");
            }
            //IZad_Parent
            tt.Show();
            this.Hide();
        }

        private void GotFocus_TextBox(object sender, EventArgs e)
        {
            Skaner.Przewij_Skanowanie();
            TextBox Pole_Tekstowe = (TextBox)sender;

            AktualnyTextBox = Pole_Tekstowe;

            //if (DW == Pole_Tekstowe)
            //{

            //    }

            //}
            ZacznijSkanowanie();
        }

        private void LostFocus_TextBox(object sender, EventArgs e)
        {
            AktualnyTextBox = null;
            Skaner.Przewij_Skanowanie();
             
            TextBox Pole_Tekstowe = (TextBox)sender;

            //if (LISTA_KONTROLNA_testbox == Pole_Tekstowe)
            //{

            //}
        }













    }
}