# API Endpoints dla Aplikacji MAUI - System Inwentaryzacji

## Przegląd

Dokumentacja endpoints API potrzebnych do implementacji systemu inwentaryzacji w aplikacji MAUI. API bazuje na istniejącym `inwentaryzacja_manual.php` z rozszerzeniami.

## Architektura API

### Base URL
```
https://[server]/wmsgg/public/skaner_api/
```

### Format Odpowiedzi
Wszystkie odpowiedzi w formacie XML:
```xml
<dane>
    <komunikat>OK|Error message</komunikat>
    <status>success|error|confirm</status>
    <message>Detailed message</message>
    <!-- Dodatkowe pola zależne od endpoint -->
</dane>
```

## Endpoints Inwentaryzacji

### 1. Pobieranie Listy Aktywnych Inwentaryzacji

**Endpoint:** `GET /inwentaryzacja_lista.php`

**Parametry:**
- `system_id` (int) - ID systemu klienta
- `typ_inwentaryzacji` (string) - "ogolna"|"produktowa"|"gg"|"miejsca"

**Przykład zapytania:**
```
GET /inwentaryzacja_lista.php?system_id=1&typ_inwentaryzacji=produktowa
```

**Odpowiedź:**
```xml
<dane>
    <komunikat>OK</komunikat>
    <inwentaryzacje>
        <inwentaryzacja>
            <id>1001</id>
            <data>2024-01-15</data>
            <opis>Inwentaryzacja Q1 2024</opis>
            <nr_wspolny>INW001</nr_wspolny>
            <proba>1</proba>
            <status>aktywna</status>
        </inwentaryzacja>
        <inwentaryzacja>
            <id>1002</id>
            <data>2024-01-16</data>
            <opis>Inwentaryzacja magazyn A</opis>
            <status>aktywna</status>
        </inwentaryzacja>
    </inwentaryzacje>
</dane>
```

### 2. Wyszukiwanie Etykiety/Kodu

**Endpoint:** `GET /inwentaryzacja_szukaj.php`

**Parametry:**
- `inwentaryzacja_id` (int) - ID sesji inwentaryzacji
- `kod_scan` (string) - Zeskanowany kod/etykieta
- `system_id` (int) - ID systemu klienta
- `typ_wyszukiwania` (string) - "etykieta"|"paleta"|"kod"

**Przykład zapytania:**
```
GET /inwentaryzacja_szukaj.php?inwentaryzacja_id=1001&kod_scan=1234567890&system_id=1&typ_wyszukiwania=etykieta
```

**Odpowiedź - Znaleziono:**
```xml
<dane>
    <komunikat>OK</komunikat>
    <status>found</status>
    <etykieta>
        <id>5823423</id>
        <kod>ABC123</kod>
        <kod_nazwa>Produkt testowy</kod_nazwa>
        <ilosc_teoretyczna>100.000</ilosc_teoretyczna>
        <ilosc_spisana>95.000</ilosc_spisana>
        <lokalizacja>
            <hala>1</hala>
            <regal>A01</regal>
            <miejsce>15</miejsce>
            <poziom>2</poziom>
        </lokalizacja>
        <lokalizacja_inwentaryzacja>
            <hala>1</hala>
            <regal>A01</regal>
            <miejsce>15</miejsce>
            <poziom>2</poziom>
        </lokalizacja_inwentaryzacja>
        <paleta_id>12345</paleta_id>
        <nrsap>SAP001</nrsap>
        <ostatnia_aktualizacja>2024-01-15 10:30:00</ostatnia_aktualizacja>
        <pracownik>Jan Kowalski</pracownik>
    </etykieta>
</dane>
```

**Odpowiedź - Nie znaleziono:**
```xml
<dane>
    <komunikat>Brak w tej inwentaryzacji etykiety: 1234567890</komunikat>
    <status>not_found</status>
    <sugestie>
        <kod_kartoteka>
            <id>456</id>
            <kod>ABC123</kod>
            <nazwa>Produkt podobny</nazwa>
            <ean>1234567891</ean>
        </kod_kartoteka>
    </sugestie>
</dane>
```

### 3. Zapisywanie/Aktualizacja Pozycji Inwentaryzacji

**Endpoint:** `POST /inwentaryzacja_zapis.php`

**Parametry:**
- `akcja` (string) - "zapisz"
- `inwentaryzacja_id` (int) - ID sesji inwentaryzacji
- `etykieta_id` (int) - ID etykiety (0 dla nowej)
- `kod` (string) - Kod produktu
- `ilosc_spisana` (decimal) - Ilość spisana
- `hala` (string) - Hala
- `regal` (string) - Regał
- `miejsce` (string) - Miejsce
- `poziom` (string) - Poziom
- `imie_nazwisko` (string) - Pracownik
- `wozek` (string) - ID wózka
- `system_id` (int) - ID systemu
- `etykieta_scan` (string) - Zeskanowany kod
- `stat` (string) - Status pozycji
- `uwagi` (string) - Dodatkowe uwagi

**Przykład zapytania:**
```
POST /inwentaryzacja_zapis.php
Content-Type: application/x-www-form-urlencoded

akcja=zapisz&inwentaryzacja_id=1001&etykieta_id=5823423&kod=ABC123&ilosc_spisana=95.5&hala=1&regal=A01&miejsce=15&poziom=2&imie_nazwisko=Jan%20Kowalski&wozek=WK001&system_id=1&etykieta_scan=1234567890&stat=OK
```

**Odpowiedź:**
```xml
<dane>
    <komunikat>OK</komunikat>
    <status>success</status>
    <operac_id>OP123456</operac_id>
    <stan_inwentaryzacji>
        <ilosc_zliczona>150</ilosc_zliczona>
        <stan_calkowity>200</stan_calkowity>
        <procent_wykonania>75.0</procent_wykonania>
    </stan_inwentaryzacji>
    <zmiana_lokalizacji>
        <wykonano>true</wykonano>
        <stare_miejsce>A01-14-1</stare_miejsce>
        <nowe_miejsce>A01-15-2</nowe_miejsce>
    </zmiana_lokalizacji>
</dane>
```

### 4. Pobieranie Stanu Inwentaryzacji

**Endpoint:** `GET /inwentaryzacja_stan.php`

**Parametry:**
- `inwentaryzacja_id` (int) - ID sesji inwentaryzacji

**Przykład zapytania:**
```
GET /inwentaryzacja_stan.php?inwentaryzacja_id=1001
```

**Odpowiedź:**
```xml
<dane>
    <komunikat>OK</komunikat>
    <stan>
        <inwentaryzacja_id>1001</inwentaryzacja_id>
        <data>2024-01-15</data>
        <opis>Inwentaryzacja Q1 2024</opis>
        <ilosc_zliczona>150</ilosc_zliczona>
        <stan_calkowity>200</stan_calkowity>
        <procent_wykonania>75.0</procent_wykonania>
        <ostatnia_aktualizacja>2024-01-15 14:30:00</ostatnia_aktualizacja>
        <pracownicy>
            <pracownik>
                <imie_nazwisko>Jan Kowalski</imie_nazwisko>
                <ilosc_pozycji>45</ilosc_pozycji>
                <ostatnia_aktywnosc>2024-01-15 14:25:00</ostatnia_aktywnosc>
            </pracownik>
            <pracownik>
                <imie_nazwisko>Anna Nowak</imie_nazwisko>
                <ilosc_pozycji>105</ilosc_pozycji>
                <ostatnia_aktywnosc>2024-01-15 14:30:00</ostatnia_aktywnosc>
            </pracownik>
        </pracownicy>
    </stan>
</dane>
```

## Endpoints Pomocnicze

### 5. Pobieranie Lokalizacji Magazynowych

**Endpoint:** `GET /miejsca_lista.php`

**Parametry:**
- `system_id` (int) - ID systemu klienta
- `hala` (string, opcjonalne) - Filtr hali
- `typ` (string) - "hale"|"regaly"|"miejsca"|"poziomy"

**Przykład zapytania:**
```
GET /miejsca_lista.php?system_id=1&typ=hale
```

**Odpowiedź:**
```xml
<dane>
    <komunikat>OK</komunikat>
    <hale>
        <hala>
            <id>1</id>
            <nazwa>Hala A</nazwa>
            <opis>Główna hala magazynowa</opis>
        </hala>
        <hala>
            <id>2</id>
            <nazwa>Hala B</nazwa>
            <opis>Hala chłodnicza</opis>
        </hala>
    </hale>
</dane>
```

### 6. Wyszukiwanie Kodów w Kartotece

**Endpoint:** `GET /kody_szukaj.php`

**Parametry:**
- `system_id` (int) - ID systemu klienta
- `kod_szukany` (string) - Szukany kod/EAN
- `limit` (int, opcjonalne) - Limit wyników (domyślnie 10)

**Przykład zapytania:**
```
GET /kody_szukaj.php?system_id=1&kod_szukany=123456&limit=5
```

**Odpowiedź:**
```xml
<dane>
    <komunikat>OK</komunikat>
    <kody>
        <kod>
            <id>123</id>
            <kod>ABC123</kod>
            <kod_nazwa>Produkt testowy ABC</kod_nazwa>
            <ean>1234567890123</ean>
            <ean_jednostki>1234567890124</ean_jednostki>
            <ilosc_w_opakowaniu>12</ilosc_w_opakowaniu>
            <jm>szt</jm>
            <wymagana_partia>1</wymagana_partia>
            <wymagana_data_waznosci>1</wymagana_data_waznosci>
        </kod>
    </kody>
</dane>
```

### 7. Synchronizacja Offline

**Endpoint:** `POST /inwentaryzacja_sync.php`

**Parametry:**
- `dane_offline` (JSON) - Dane zebrane w trybie offline
- `system_id` (int) - ID systemu klienta
- `pracownik_id` (int) - ID pracownika

**Przykład zapytania:**
```
POST /inwentaryzacja_sync.php
Content-Type: application/json

{
    "system_id": 1,
    "pracownik_id": 15,
    "dane_offline": [
        {
            "inwentaryzacja_id": 1001,
            "etykieta_scan": "1234567890",
            "kod": "ABC123",
            "ilosc_spisana": 95.5,
            "hala": "1",
            "regal": "A01",
            "miejsce": "15",
            "poziom": "2",
            "timestamp_local": "2024-01-15T14:30:00Z",
            "wozek": "WK001"
        }
    ]
}
```

**Odpowiedź:**
```xml
<dane>
    <komunikat>OK</komunikat>
    <status>success</status>
    <zsynchronizowane>
        <pozycja>
            <timestamp_local>2024-01-15T14:30:00Z</timestamp_local>
            <status>success</status>
            <operac_id>OP123456</operac_id>
        </pozycja>
    </zsynchronizowane>
    <bledy>
        <!-- Ewentualne błędy synchronizacji -->
    </bledy>
</dane>
```

## Obsługa Błędów

### Standardowe Kody Błędów

| Kod | Status | Opis |
|-----|--------|------|
| 200 | success | Operacja zakończona sukcesem |
| 400 | error | Błędne parametry zapytania |
| 401 | error | Brak autoryzacji |
| 404 | not_found | Nie znaleziono zasobu |
| 409 | conflict | Konflikt danych (np. duplikat) |
| 500 | error | Błąd serwera |

### Przykład Odpowiedzi Błędu

```xml
<dane>
    <komunikat>Brak wymaganych parametrów</komunikat>
    <status>error</status>
    <kod_bledu>400</kod_bledu>
    <szczegoly>
        <brakujace_parametry>
            <parametr>inwentaryzacja_id</parametr>
            <parametr>system_id</parametr>
        </brakujace_parametry>
    </szczegoly>
</dane>
```

## Autoryzacja i Bezpieczeństwo

### Headers Wymagane

```
Authorization: Bearer [token]
X-System-ID: [system_id]
X-Device-ID: [unique_device_id]
Content-Type: application/x-www-form-urlencoded
```

### Walidacja Parametrów

1. **Wszystkie endpoints:**
   - Sprawdzenie autoryzacji
   - Walidacja `system_id`
   - Sprawdzenie uprawnień pracownika

2. **Endpoints zapisu:**
   - Walidacja typów danych
   - Sprawdzenie wymaganych pól
   - Kontrola limitów ilościowych

3. **Bezpieczeństwo:**
   - Escape SQL injection
   - Walidacja XSS
   - Rate limiting

## Optymalizacja dla MAUI

### Caching

1. **Lokalne cache:**
   - Lista inwentaryzacji (TTL: 1 godzina)
   - Struktura lokalizacji (TTL: 24 godziny)
   - Kartoteka kodów (TTL: 12 godzin)

2. **Strategia offline:**
   - Lokalne SQLite dla danych offline
   - Automatyczna synchronizacja przy połączeniu
   - Kolejka operacji do wykonania

### Kompresja Danych

```
Accept-Encoding: gzip, deflate
```

### Paginacja

Dla dużych zbiorów danych:
```
GET /inwentaryzacja_lista.php?page=1&limit=50&system_id=1
```

Odpowiedź z metadanymi:
```xml
<dane>
    <komunikat>OK</komunikat>
    <meta>
        <page>1</page>
        <limit>50</limit>
        <total>150</total>
        <pages>3</pages>
    </meta>
    <inwentaryzacje>
        <!-- dane -->
    </inwentaryzacje>
</dane>
```
