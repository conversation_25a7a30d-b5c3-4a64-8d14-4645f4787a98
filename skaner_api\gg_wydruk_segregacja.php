<?php

error_reporting(E_ALL);
ini_set('display_errors', 1);
include_once 'Db.class.php';
include_once 'funkcje.inc';
$db = new Db();

// echo "<pre>";
// print_r($_GET);
// echo "</pre>";
// exit();

// jeśli nie ma camion to zwróć błąd
if (empty($_GET["camion"])) {   

    xml_from_indexed_array(
        array(
            'komunikat' => "Błąd: brak parametru camion",
        )
    );
    exit();
}

// jeśli nie ma karton to zwróć błąd
if (empty($_GET["karton"])) {
    xml_from_indexed_array(
        array(
            'komunikat' => "Błąd: brak parametru karton",
        )
    );
    exit();
}

// jeśli nie ma adres_ip to zwróć błąd
// if (empty($_GET["adres_ip"])) {
//     xml_from_indexed_array(
//         array(
//             'komunikat' => "Błąd: brak parametru adres_ip",
//         )
//     );
//     exit();
// }



$karton = $_GET["karton"];

$karton = trim($_GET["karton"], " ");
$karton = trim($_GET["karton"], "\t");

$adres_ip = $_GET["adres_ip"];
$camion = $_GET["camion"];
$pracownik_id_scan = 1198;
if (!empty($_GET["pracownik_id"])) {
    $pracownik_id_scan = $_GET["pracownik_id"];
}


if (empty($adres_ip)) {
    $adres_ip = "***********";
}
$komunikat = "OK";

if (!empty($karton)) {
    $sql = 'SELECT i.paleta,i.CD_CONTENITORE,paleta_new,i_docin_id,i_paleta_id FROM importRICI i
WHERE i.CD_CONTENITORE="' . $karton . '" and camion="' . $camion . '" limit 1 ';
    $result1 = $db->mGetResultAsXML($sql);
    //echo $sql;
    if (empty($result1)) {
        xml_from_indexed_array(
            array(
                'komunikat' => "Nie znaleziono kartonu " . $karton . " w dostawie " . $camion,
            )
        );
    }

    foreach ($result1 as $key => $aRow1) {


        //echo $aRow1['last'];
        //$paleta = $aRow1['paleta'];
        $paleta = str_replace("OCK", "", $aRow1['paleta']);
        $paleta_new = $aRow1['paleta_new'];
        $i_docin_id = $aRow1['i_docin_id'];
        $i_paleta_id = $aRow1['i_paleta_id'];
        if (empty($i_docin_id)) {
            $doc_nr = max_doc_nr_local($db);
            $i_paleta_id = docnumber_increment("wmsgg", "nrpalety", $db);

            $kontrah_wew_id = 590;
            $pracownik_id_dokument = 1198;
            $i_paleta_id = tworz_palete_local($i_paleta_id, $db);
            $doc_ref = $camion;
            $i_docin_id = tworz_dokument_docin_local("PZ", $doc_nr, $pracownik_id_dokument, $kontrah_wew_id, $doc_ref, $db);
            $aa = $db->mGetResultAsXML("update importRICI set i_docin_id=$i_docin_id,i_paleta_id = $i_paleta_id where  camion='" . $camion . "'  ");
        }


        $karton2 = $aRow1['CD_CONTENITORE'];

        //echo $karton2;
        $aa = $db->mGetResultAsXML("update importRICI set zeskanowane=1, pracownik_id=$pracownik_id_scan,ts_inwentaryzacja=NOW() where  CD_CONTENITORE='" . $karton2 . "'  ");

        $sql = "select count(1) as ile, sum(zeskanowane) as zeskanowanych  from  wmsgg.importRICI 
where  camion='" . $camion . "'";
        $licznik = "";
        $result2 = $db->mGetResultAsXML($sql);
        foreach ($result2 as $key => $aRow2) {
            $licznik = $aRow2['ile'] . "/" . $aRow2['zeskanowanych'];
        }

        $sql = "SELECT 1 FROM wmsgg.etykiety
where etykieta_klient='$karton2' and active=1 limit 1";
        //echo $sql;
        $result3 = $db->mGetResultAsXML($sql);
        if (empty($result3)) {

            $nretykiety = docnumber_increment("wmsgg", "nretykiety", $db);

            $sql = "insert into etykiety( system_id, etykieta_klient, magazyn, active, miejscep, kod_id, status_id, paleta_id, "
                . " ilosc, ts, akcja_id, lot, przeznaczenie_id, nretykiety, docin_id,  "
                . "  edycja_et) values(17, '$karton2',1,1,27631, 10,83,'$i_paleta_id',1,NOW(),534,'$paleta',1,$nretykiety,$i_docin_id,1); ";
            //echo $sql;
            $result4 = $db->mGetResultAsXML($sql);
        }

        //print_paleta($paleta, $karton2, $adres_ip);
        xml_from_indexed_array(
            array(
                'komunikat' => $komunikat,
                'paleta' => $paleta, //$paleta_new . " / " .
                'karton' => $karton2,
                'licznik' => $licznik,
            )
        );
        //
    }
}

function tworz_palete_local($i_paleta_id, $db)
{
    $sql = "insert into palety(id,typypalet_id,ilosc,j_skladowania_id,ts_utworzenia)
values ($i_paleta_id,1,1,1,NOW())";
    $paleta_id = $db->mGetResultAsXML($sql);
    //echo "<br>" . $sql;

    return $paleta_id;
}

function tworz_dokument_docin_local($doc_type, $doc_nr, $pracownik_id, $kontrah_wew_id, $doc_ref, $db)
{
    $sql = "insert into docin(doc_internal,doc_type,doc_nr,doc_date,doc_ts,pracownik_id,kontrah_id,doc_ref,doc_uwagi)" .
        " values(1,'" . $doc_type . "'," . $doc_nr . ",date(now()),sysdate(),'" . $pracownik_id . "','$kontrah_wew_id','$doc_ref','')";
    $docin_id = $db->mGetResultAsXML($sql);
    //echo "<br>" . $sql;

    return $docin_id;
}

function max_doc_nr_local($db)
{
    $sql = "SELECT max(doc_nr) as last FROM etykiety e
left join docin d on e.docin_id=d.id
where doc_type='PZ' and e.system_id=17";
    $result = $db->mGetResultAsXML($sql);
    //echo "<br>" . $sql;
    $numer = 0;
    foreach ($result as $index => $aRow) {
        $numer = $aRow['last'] + 1;
        //$dc_prac_id = $aRow['dc_prac_id'];
    }
    return $numer;
}

function print_paleta($paleta, $karton3, $adres_id)
{

    //    $layout = '^XA
    //
    //^FO83,20	^ADN,80,40	^FD '.$karton.' ^FS
    //^FO93,180	^ADN,80,45	^FD' . $paleta . '^FS
    //
    //
    //^XZ
    //            ';

    $layout = '^XA 
^FO13,20   ^ADN,30,15 ^FD ' . $karton3 . ' ^FS 
^FO313,20 ^ADN,90,29 ^FD' . $paleta . '^FS ^XZ 
            ';
    //echo $layout;
    //^FD>;>800' . $params['sscc'] . '^FS
    $file = fopen("/tmp/etykietakarton.zbr", "w");
    fputs($file, $layout);
    fclose($file);
    //flush();
    //sleep(1);

    $socket = socket_create(AF_INET, SOCK_STREAM, SOL_TCP);
    $result = socket_connect($socket, $adres_id, "9100");
    socket_write($socket, $layout, strlen($layout));
    socket_close($socket);
}

// koniec funkcji printlabel
