﻿using System;
using System.Linq;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using System.Threading;
using System.Xml;

namespace Tarczyn__Magazyn
{
    
    


    public partial class Zad_DL_Podglad_oldGrid : Form
    {
        IZad_DL myParent = null;

        XmlNode node_myParent = null;
        XmlNodeList xmlnode = null;
        XmlNode node = null;


        public Zad_DL_Podglad_oldGrid(IZad_DL MyParent, XmlNode node2)
        {
            this.myParent = MyParent;
            this.node_myParent = node2;
            
            FullScreenMode.OknoOFF(this);
            InitializeComponent();
            pokaz_podglad();
            //BazaDanychExternal.Inicjalizacja(Wlasciwosci.Varbaza, Wlasciwosci.Varhost, Wlasciwosci.VarLoginMYSQL, Wlasciwosci.VarHasloMYSQL, this);  
        }

        Thread Skanowanie = null;


        private void ZacznijSkanowanie()
        {
            StringBuilder login = new StringBuilder();
            Skaner.UstawTryb_String(login);
            Skaner.Zacznij_Skanowanie();
            Skanowanie = new Thread(() => CheckString(login));
            Skanowanie.IsBackground = true;
            Skanowanie.Start();
        }
        private void CheckString(StringBuilder cc)
        {
            while (true)
            {
                if (cc.ToString() != "")
                {
                    this.Invoke(new EventHandler(delegate
                    {

                        dodawanie(cc.ToString());
                    }));
                    return;
                }
                Thread.Sleep(200);
            }
        }
        private void Zakoncz_Skanowanie()
        {
            try
            {
                Skanowanie.Abort();
                Skaner.Przewij_Skanowanie();
            }
            catch
            {
            }
        }

        private void Exit_Click_1(object sender, EventArgs e)
        {
            //Zakoncz_Skanowanie();
            //BazaDanychExternal.ChangeHost(Wlasciwosci.Varbaza, Wlasciwosci.Varhost, Wlasciwosci.VarLoginMYSQL, Wlasciwosci.VarHasloMYSQL);
            myParent.Show();
            this.Close();
        }


        private void button2_Click(object sender, EventArgs e)
        {


        }

        
        private void dodawanie(string gg)
        {

            
            Zakoncz_Skanowanie();
            
        }


        

        void pokaz_podglad()
        {

            XmlNodeList xmlnode2 = null;
            XmlDocument doc1 = WebService.Pobierz_XmlDocument("delivery_podglad_do_zrealizowania.php?db=" + node_myParent["baza_danych"].InnerText + "&akcja=podglad&delivery_id=" + node_myParent["doc_id"].InnerText);

            xmlnode = doc1.GetElementsByTagName("dane");




            foreach (XmlNode wynik in xmlnode)
            {
                node = wynik;
            }

            if (node["komunikat"].InnerText != "OK")
            {
                MessageBox.Show(node["komunikat"].InnerText);
                return;
            }


            xmlnode2 = doc1.GetElementsByTagName("etykiety");




            DataTable dt = new DataTable();

            dt.Columns.Add("id", typeof(string));
            dt.Columns.Add("kod", typeof(string));
            dt.Columns.Add("adres", typeof(string));

            foreach (XmlNode NodeXml in xmlnode2)
            {
                DataRow dtrow = dt.NewRow();
                XmlElement companyElement = (XmlElement)NodeXml;
                dtrow["id"] = NodeXml["id"].InnerText;
                dtrow["kod"] = NodeXml["kod"].InnerText;
                dtrow["adres"] = NodeXml["adres"].InnerText;

                dt.Rows.Add(dtrow);
            }
            


            if (dt.Rows.Count > 0)
            {
                dataGrid1.DataSource = dt;
                //dataGrid1.DataBind();
            }
            else
            {
                dataGrid1.DataSource = "";
                //dataGrid1.DataBind();
            }







            dataGrid1.TableStyles.Clear();
            DataGridTableStyle tableStyle = new DataGridTableStyle();
            tableStyle.MappingName = dt.TableName;

            DataGridTextBoxColumn tbcName = new DataGridTextBoxColumn();
            tbcName.Width = 60;
            tbcName.MappingName = "id";
            tbcName.HeaderText = "id";
            tableStyle.GridColumnStyles.Add(tbcName);
            DataGridTextBoxColumn tbcName1 = new DataGridTextBoxColumn();
            tbcName1.Width = 110;
            tbcName1.MappingName = "kod";
            tbcName1.HeaderText = "kod";
            tableStyle.GridColumnStyles.Add(tbcName1);
            DataGridTextBoxColumn tbcName2 = new DataGridTextBoxColumn();
            tbcName2.Width = 100;
            tbcName2.MappingName = "adres";
            tbcName2.HeaderText = "adres";
            tableStyle.GridColumnStyles.Add(tbcName2);

            dataGrid1.TableStyles.Add(tableStyle);



        }

        private void WczytywanieNaLinie_Load_1(object sender, EventArgs e)
        {
            //ZacznijSkanowanie();
        }

        private void button1_Click(object sender, EventArgs e)
        {

        }

        

  





    }
}