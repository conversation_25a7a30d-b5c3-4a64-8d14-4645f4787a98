# TODO Frontend – <PERSON><PERSON><PERSON>” (MAUI)

Cel: Zaimplementować dwa widoki i logikę skanowania/rejestracji zgodnie z PRD_frontend_dostawy.md.

## Etap 1 – Infrastruktura (0%)
- [ ] Konfiguracja klienta HTTP (bazowy URL, timeouty, retry krytycznych akcji). (0%)
- [ ] Warstwa usług: DostawyService, DrukService, GS1Service (wywołania API). (0%)

## Etap 2 – Widok 1: Dostawa wybór (0%)
- [ ] Pole „LK” (walidacja prefiksu i formatu, bez zer wiodących). (0%)
- [ ] „Lista dostaw” – pobieranie i prezentacja (id, system, miejsce, data). (0%)
- [ ] „Generuj DS” – UI (typ palety, ilość 1..100, IP drukarki, checkbox druk). (0%)
- [ ] Wywołania: claim, generuj DS (+ druk). (0%)
- [ ] Obsługa błędów/confirm i komunikatów. (0%)

## Etap 3 – Widok 2: Dostawa rejestracja (0%)
- [ ] Pole „Skanuj” z autofocus i cyklem: skan → API → wynik → focus powrót. (0%)
- [ ] Prezentacja bieżącego nośnika (DS/SSCC) lub „(Auto)”. (0%)
- [ ] Sekcja towaru (Szukaj; selekcja przy wielu wynikach). (0%)
- [ ] Pola dat/partii/ilości z przeliczeniami (AI 37 × ilosc_w_opakowaniu). (0%)
- [ ] Zmiana typu palety (edytowalna). (0%)
- [ ] „Podgląd” pozycji DS. (0%)
- [ ] „Nośnik kompletny” (zamyka bieżący DS). (0%)
- [ ] „Koniec” – zakończenie sesji i zwolnienie claim. (0%)

## Etap 4 – UX i stabilność (0%)
- [ ] Spójne komunikaty success/error/confirm (toast/modal). (0%)
- [ ] Walidacja IP vs lista drukarek. (0%)
- [ ] Testy ręczne przepływów (scenariusze nominalne i błędne). (0%)

---
Postęp: 0%
