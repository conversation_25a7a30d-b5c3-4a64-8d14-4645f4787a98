﻿using System;
using System.Linq;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using System.Threading;
using System.Xml;


namespace Tarczyn__Magazyn
{
    public partial class PoleWyborListBox : Form
    {

        List<string> wartosci = new List<string>();
        List<string> nazwy = new List<string>();
        public string wartosc_wybrana = "";
        public string bez_potwierdzenia_przyciskiem = "";

        public PoleWyborListBox(DataTable bb, System.Single sizeFont, string bez_potwierdzenia)
        {

            this.bez_potwierdzenia_przyciskiem = bez_potwierdzenia;
            FullScreenMode.OknoOFF(this);
            InitializeComponent();
            this.listBox1.Font = new System.Drawing.Font("Tahoma", sizeFont, System.Drawing.FontStyle.Bold);
            pokaz_podglad(bb);            
        }

        Thread Skanowanie = null;


        private void ZacznijSkanowanie()
        {
            StringBuilder login = new StringBuilder();
            Skaner.UstawTryb_String(login);
            Skaner.Zacznij_Skanowanie();
            Skanowanie = new Thread(() => CheckString(login));
            Skanowanie.IsBackground = true;
            Skanowanie.Start();
        }
        private void CheckString(StringBuilder cc)
        {
            while (true)
            {
                if (cc.ToString() != "")
                {
                    this.Invoke(new EventHandler(delegate
                    {

                        dodawanie(cc.ToString());
                    }));
                    return;
                }
                Thread.Sleep(200);
            }
        }
        private void Zakoncz_Skanowanie()
        {
            try
            {
                Skanowanie.Abort();
                Skaner.Przewij_Skanowanie();
            }
            catch
            {
            }
        }

        private void Exit_Click_1(object sender, EventArgs e)
        {
            //Zakoncz_Skanowanie();
            //BazaDanychExternal.ChangeHost(Wlasciwosci.Varbaza, Wlasciwosci.Varhost, Wlasciwosci.VarLoginMYSQL, Wlasciwosci.VarHasloMYSQL);

            if (listBox1.SelectedItem.ToString() !="")
            {
                wartosc_wybrana =  wartosci[listBox1.SelectedIndex].ToString();
                this.DialogResult = DialogResult.OK;
            }
            else
            {
                MessageBox.Show("Nie dokonano wyboru ");
            }
            
        }



        
        private void dodawanie(string gg)
        {

            
            Zakoncz_Skanowanie();
            
        }
        void pokaz_podglad(DataTable dt)
        {
            if (dt.Rows.Count > 0)
            {
                for (int i = 0; i < dt.Rows.Count; i++)
                {
                    wartosci.Add(dt.Rows[i]["id"].ToString());
                    nazwy.Add(dt.Rows[i]["nazwa_wyswietlana"].ToString());
                }
                listBox1.DataSource = nazwy;
            }
        }

        private void listBox1_SelectedIndexChanged(object sender, EventArgs e)
        {
            if (bez_potwierdzenia_przyciskiem == "TAK")
            {
                System.Threading.Thread.Sleep(50);
                if (listBox1.SelectedItem.ToString() != "")
                {

                    wartosc_wybrana = wartosci[listBox1.SelectedIndex].ToString();
                    this.DialogResult = DialogResult.OK;

                }
            }
        }
    }
}