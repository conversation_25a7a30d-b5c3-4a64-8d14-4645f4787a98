﻿using System;
using System.Linq;
using System.Collections.Generic;
using System.Text;
using Terranova.API;
using System.Windows.Forms;

namespace Tarczyn__Magazyn
{
    public static class WyjscieProgram
    {
        public static void Wyjdz(string nazwa_Exe, Form myParent)
        {

            FullScreenMode.OknoON();
            ProcessInfo[] list = ProcessCE.GetProcesses();
            foreach (ProcessInfo pinfo in list)
            {
                if (pinfo.FullPath.EndsWith(nazwa_Exe))
                { pinfo.Kill(); }
            }
        }

    }
}
