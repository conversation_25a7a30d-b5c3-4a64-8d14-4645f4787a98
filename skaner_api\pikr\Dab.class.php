<?php

//TOD<PERSON>‡ obsĹ‚ugÄ™ configa.


class Dab {

    private $config;
    public $errors;
    public $lastQuery;
    public $count;

    public function __construct() {


        $app_config_file = "/etc/www_pass/pikr.env";

        if (file_exists($app_config_file)) {
            $CONF = parse_ini_file($app_config_file);
        } else {
            die("Brak pliku konfiguracji: $app_config_file");
        }


        try {
            $this->connection = new PDO("mysql:host=".$CONF['DB_HOST'].";dbname=".$CONF['DB_NAME']."", $CONF['DB_USER'], $CONF['DB_PASSWORD']);
            $this->connection->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_WARNING); // PDO::ERRMODE_SILENT PDO::ERRMODE_WARNING	PDO::ERRMODE_EXCEPTION
        } catch (PDOException $e) {
            show_komunikat_xml($e->getMessage());
            exit(); 
            $arr = $this->connection->errorInfo();
            if (!empty($arr[1])) {
                $this->errors = $arr[1] . ":" . $arr[2];
            }
            if (!empty($this->errors)) {
                show_komunikat_xml($this->errors);
                exit();
            }
        }
        $this->exec('SET NAMES "UTF8"');
    }

    public function query($query) {
        try {
            $this->sth = $this->connection->query($query);
        } catch (PDOException $e) {
            $arr = $this->connection->errorInfo();
            if (!empty($arr[1])) {
                $this->errors = $arr[1] . ": ". substr($this->lastQuery, 0, 50).".. :" . $arr[2];
            }
            //exit();
        }
        $arr = $this->connection->errorInfo();
        if (!empty($arr[1])) {
            $this->errors = $arr[1] . ": ". substr($this->lastQuery, 0, 50).".. :" . $arr[2];
        } else {
            $this->result = $this->sth->fetchAll(PDO::FETCH_ASSOC);
            $this->nrows = count($this->result) > 0 ? count($this->result) : $this->sth->rowCount();
            $this->ncols = $this->sth->columnCount();
        }
        return $this->result;
    }

    public function reset() {
        $this->stmt = null;
        $this->params = array();
        $this->result = array();
        $this->nrows = 0;
        $this->ncols = 0;
        $this->errorCode = 0;
        $this->columns = array();
    }

    public function prepare($proc, $params = array()) {
        try {
            $this->reset();
            $this->stmt = $this->connection->prepare($proc);
            $this->params = array();
            foreach ($params as $name => $value) {
                $this->stmt->bindParam(':' . $name, $this->params[$name]);
                $this->params[$name] = $value;
            }
        } catch (PDOException $e) {
            $arr = $this->connection->errorInfo();
            if (!empty($arr[1])) {
                $this->errors = $arr[1] . ": ". substr($this->lastQuery, 0, 50).".. :" . $arr[2];
            }
            //echo $e->getMessage();
            //exit();
        }
    }

    public function exec($proc, $params = array()) {
        if (isset($proc)) {
            $this->prepare($proc, $params);
        }
        try {
            $result = $this->stmt->execute();
            $arr = $this->stmt->errorInfo();
            $this->count = $this->stmt->rowCount();
            if (!empty($arr[1])) {
                $this->errors = $arr[1] . ": ". substr($this->lastQuery, 0, 50).".. :" . $arr[2];
            }
            //exit();
            if ($result == 1) {
                $result = $this->connection->lastInsertId();
            }
        } catch (PDOException $e) {
            $arr = $this->stmt->errorInfo();
            if (!empty($arr[1])) {
                $this->errors = $arr[1] . ": ". substr($this->lastQuery, 0, 50).".. :" . $arr[2];
            }
            //exit();
        }
        return $result;
    }

    public function fetch($proc, $params = array()) {
        if (isset($proc)) {
            $this->prepare($proc, $params);
        }
        try {
            $this->stmt->execute();
        } catch (PDOException $e) {
            //echo "$e";
            $arr = $this->connection->errorInfo();
            if (!empty($arr[1])) {
                $this->errors = $arr[1] . ": ". substr($this->lastQuery, 0, 50).".. :" . $arr[2];
            }
            //exit();
        }
        $this->result = $this->stmt->fetchAll(PDO::FETCH_ASSOC);
        $this->nrows = count($this->result) > 0 ? count($this->result) : $this->stmt->rowCount();
        $this->ncols = $this->stmt->columnCount();
        return $this->result;
    }

    public function send($proc, $params = array()) {
        $this->exec($proc, $params);
        $this->sendResult();
    }

    public function sendResult() {
        echo json_encode($this->result);
    }

    public function getColumn($name) {
        $result = array();
        for ($i = 0; $i < $this->nrows; $i++) {
            array_push($result, $this->result[$i][$name]);
        }
        return $result;
    }

    public function mGetResult($mquery) {
        $tmp = explode(" ", ltrim($mquery));
        $mresult = null;
        if (strtoupper($tmp[0]) == "INSERT" || strtoupper($tmp[0]) == "UPDATE") {
            $mresult = $this->exec($mquery); // po to by zwracal Last insert ID
        } else {
            $mresult = $this->query($mquery);
        }
        return $mresult;
    }

    public function mGetResultAsXML($mquery) {
        $this->lastQuery = $mquery;
        $tmp = explode(" ", ltrim($mquery));
        $mresult = null;
        if (strtoupper($tmp[0]) == "INSERT" || strtoupper($tmp[0]) == "UPDATE" || strtoupper($tmp[0]) == "DELETE" || strtoupper($tmp[0]) == "KILL") {
            $mresult = $this->exec($mquery); // po to by zwracal Last insert ID
        } else {
            $mresult = $this->query($mquery);
        }
        if (!empty($this->errors)) {
            $mquery = addslashes($mquery); //stripslashes( htmlentities( $mquery));
            $sql = "insert into sql_errors(ts,query) values (NOW(),'Skaner_api: " . $mquery . "')";
            //echo $sql;
            $ss = $this->exec($sql);
            show_komunikat_xml($this->errors);
            exit();
        }
        return $mresult;
    }

    public function insertInto($TABLE, $DATA) {
        $SQL = "INSERT INTO {$TABLE}";
        $SQL .= " (";
        foreach ($DATA as $key => $val) {
            $SQL .= $key . ",";
        }
        $SQL = rtrim($SQL, ",");
        $SQL .= ") ";
        $SQL .= "VALUES";
        $SQL .= " (";
        foreach ($DATA as $val) {
            $SQL .= "'" . $val . "',";
        }
        $SQL = rtrim($SQL, ',');
        $SQL .= ")";
        echo $SQL;
        $result = $this->exec($SQL);
        return $result;
    }

//
//    public function error_xml_fromat($error) {
//        echo "<komunikat>" . $error . "</komunikat>";
//    }
}

?>