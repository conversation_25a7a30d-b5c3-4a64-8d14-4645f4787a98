<?php

//include_once 'db_pdo.php';
include_once 'Db.class.php';
include_once 'funkcje.inc';
//error_reporting(E_ALL);
//ini_set('display_errors', 1);

$db = new Db();
//
//if (empty($argv)) {
//    //$argv = array();
//    $argv[1] = $_GET['db'];
//    $argv[2] = $_GET['akcja'];
//}
//
$baza_danych = $_GET['db'];
$akcja = $_GET['akcja'];
$scan = $_GET['scan'];
$system_id = $_GET['system_id'];




$komunikat = "OK";

if ($akcja == "szukaj") {
    $wymiar_wyswietlany = "";

    if (substr($scan, 0, 2) == "DS") {
        $wymiary = wyswietl_ds("wmsgg", str_replace("DS", "", $scan), $db);
        $wymiar_wyswietlany = $wymiary[0]['opis'];
    } else {
        $wymiary = wyswietl_ean("wmsgg", $scan, $system_id, $db);
        $wymiar_wyswietlany = 'KARTON';
    }



    if (empty($wymiary)) {
        $komunikat = "Nie znaleziono kodu";
        $wymiary = array('komunikat' => $komunikat);
    } else {
        $wymiary = array_merge(array('komunikat' => $komunikat, 'wymiar_wyswietlany' => $wymiar_wyswietlany), $wymiary[0]);
    }

    return xml_from_indexed_array($wymiary);
}

function wyswietl_ds($baza_danych, $scan, $db) {
    $sql = "SELECT tp.opis,de.delivery_id FROM $baza_danych.palety p
left join $baza_danych.typypalet tp on typypalet_id=tp.id
left join $baza_danych.etykiety e on e.paleta_id=p.id
left join $baza_danych.delivery_et de on e.id=de.etykieta_id where p.id=$scan limit 1; ";
    $result = $db->mGetResultAsXML($sql);

    return $result;
}

function wyswietl_ean($baza_danych, $scan, $system_id, $db) {
    $sql = "SELECT  TRIM(TRAILING '.' FROM TRIM(TRAILING '0' FROM k.dlugosc_szt_mm/10))  as dlugosc, TRIM(TRAILING '.' FROM TRIM(TRAILING '0' FROM k.szerokosc_szt_mm/10))  as szerokosc, TRIM(TRAILING '.' FROM TRIM(TRAILING '0' FROM k.wysokosc_szt_mm/10)) as wysokosc,

if(TRIM(LEADING '0' FROM k.ean_jednostki)='$scan',TRIM(TRAILING '.' FROM TRIM(TRAILING '0' FROM k.waga_szt_kg)) ,
if(TRIM(LEADING '0' FROM k.ean)='$scan',TRIM(TRAILING '.' FROM TRIM(TRAILING '0' FROM (waga_szt_kg*ilosc_w_opakowaniu)))  ,
if(TRIM(LEADING '0' FROM k.ean_opakowanie_zbiorcze)='$scan',TRIM(TRAILING '.' FROM TRIM(TRAILING '0' FROM (waga_szt_kg*ilosc_szt_w_zbiorczym))) ,''))) as waga,
    '' as delivery_id

FROM $baza_danych.kody k
WHERE (TRIM(LEADING '0' FROM k.ean_jednostki)='$scan'
OR  TRIM(LEADING '0' FROM k.ean)='$scan'
OR TRIM(LEADING '0' FROM k.ean_opakowanie_zbiorcze)='$scan')
AND k.system_id=$system_id order by k.id desc limit 1 ;";
    //echo $sql;
    $result = $db->mGetResultAsXML($sql);
    if(empty($result))
    {
        $result[0]['waga'] =$result[0]['waga']="";
    }
//    print_r($result);
//    if($result['waga']=="0"){$result['waga']="";}

    return $result;
}
