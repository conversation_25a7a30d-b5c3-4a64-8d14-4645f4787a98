﻿using System;
using System.Linq;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using System.Threading;
using System.Text.RegularExpressions;
using System.Xml;

namespace Tarczyn__Magazyn
{



    public partial class PrzeklejanieKartonow : Form, IZad_DL
    {
        //MainMenu myParent = null;
        ActionMenu myParent = null;
        TextBox[] TextBoxArray = null;
        XmlNode node = null;
        TextBox AktualnyTextBox = null;

        List<string> nazwy_wyswietlane = new List<string>();
        int[] nazwy_id = new int[100];


        List<string> _paleta_wybor = new List<string>();
        int[] _paleta = new int[100];


        //string ilosc_w_opakowaniu = "1";
        string ean_jednostki = "";
        string ean = "";
        string ilosc_w_opakowaniu = "";
        string ean_opakowanie_zbiorcze = "";
        string ilosc_szt_w_zbiorczym = "";
        string nosnik_numer = "";




        public PrzeklejanieKartonow(ActionMenu c)
        {

            FullScreenMode.OknoOFF(this);
            InitializeComponent();
            TextBoxArray = new TextBox[] { ETYKIETA };
            myParent = c;
            Etykieta.Inicjalizacja();
            wms_wybrany.Text = Wlasciwosci.system_id;
            imie_nazwisk_label.Text = Wlasciwosci.wozek + ":" + Wlasciwosci.imie_nazwisko;
            //Wlasciwosci.system_id_id = "";
            wypelnij_dane();
        }

        void wypelnij_dane()
        {
            nazwy_wyswietlane.Clear();
            nazwy_wyswietlane.Add("");
            nazwy_id[0] = 0;
            object obj2 = BazaDanychExternal.Wyczytaj_Tabele("select kkk.id as id, concat(kkk.kod,' ',ifnull(kp.kraj_symbol,'')) as nazwa from kody kkk left join kraj_pochodzenia kp on kkk.kraj_pochodzenia_id=kp.id where system_id=" + Wlasciwosci.system_id_id + " and brand_id=1 order by kod asc");
            DataTable tabela = (DataTable)obj2;
            for (byte k = 0; k < tabela.Rows.Count; k++)
            {
                nazwy_wyswietlane.Add(tabela.Rows[k]["nazwa"].ToString());
                nazwy_id[k + 1] = Convert.ToInt32(tabela.Rows[k]["id"]);
            }
            BindingSource bs = new BindingSource();
            bs.DataSource = nazwy_wyswietlane;
            comboBox1.DataSource = bs;

            //MessageBox.Show(domyslna_nazwa);
            //if (domyslna_nazwa != "")
            //{
            //    comboBox1.SelectedIndex = comboBox1.Items.IndexOf(domyslna_nazwa);
            //}
            
            ETYKIETA.Focus();
            message_label.Text = "";
        }


        private void TworzenieDL_Load(object sender, EventArgs e)
        {
            //ETYKIETA.Focus();
        }



        private void GotFocus_TextBox(object sender, EventArgs e)
        {
            Skaner.Przewij_Skanowanie();
            TextBox Pole_Tekstowe = (TextBox)sender;
            AktualnyTextBox = Pole_Tekstowe;
            ZacznijSkanowanie();
        }

        private void LostFocus_TextBox(object sender, EventArgs e)
        {
            AktualnyTextBox = null;
            Skaner.Przewij_Skanowanie();

            TextBox Pole_Tekstowe = (TextBox)sender;
        }







        #region Skanowanie

        Thread Skanowanie = null;



        private void ZacznijSkanowanie()
        {
            StringBuilder login = new StringBuilder();
            Skaner.UstawTryb_String(login);
            Skaner.Zacznij_Skanowanie();
            Skanowanie = new Thread(() => CheckString(login));
            Skanowanie.IsBackground = true;
            Skanowanie.Start();
        }

        private void CheckString(StringBuilder cc)
        {
            while (true)
            {
                if (cc.ToString() != "")
                {
                    this.Invoke(new EventHandler(delegate
                    {
                        dodawanie(cc.ToString());
                    }));
                    return;
                }
                Thread.Sleep(200);
            }
        }


        private void Zakoncz_Skanowanie()
        {
            {
                Skanowanie.Abort();
                Skaner.Przewij_Skanowanie();
            }
        }

        private void dodawanie(string ops)
        {
            //MessageBox.Show("TrybSkanu:" + TrybSkanu + "   ,   ops.Length:" + ops.Length);


            AktualnyTextBox.Text = ops;
            if (ops == "")
            {
                MessageBox.Show("Nie wypełniono etykiety");
                return;
            }



            

            if (AktualnyTextBox == ETYKIETA)
            {
                if (!(ops[0] == 'D' && ops[1] == 'S'))
                {
                
                    MessageBox.Show("Niepoprawna etykieta");
                    ETYKIETA.Text = "";
                    //return;
                }                
            }

            


            //this.ZacznijSkanowanie();
        }

        private XmlNode KomunikacjaSerwer(XmlNode node_src, string etykieta_scan, int proba)
        {
            
            XmlDocument doc1_etykieta = WebService.Pobierz_XmlDocument("przeklejanie_kartonow.php?akcja=drukowanie&imie_nazwisko=" + Uri.EscapeUriString(Wlasciwosci.imie_nazwisko) + "&etykieta_scan=" + etykieta_scan + "&kod_id=" + nazwy_id[comboBox1.SelectedIndex].ToString() + "&ilosc_etykiet=" + textBox1.Text);
            XmlNode node_etykieta = WebService.Pobierz_XmlNode(doc1_etykieta, "dane");

            return node_etykieta;
        }




        #endregion






        private void QT_KeyDown(object sender, KeyEventArgs e)
        {

        }





        private void powrot_Click(object sender, EventArgs e)
        {
            Skaner.Przewij_Skanowanie();
            myParent.Show();
            this.Close();
        }

        private void odkladanie_Click(object sender, EventArgs e)
        {
            //myParent.delivery_odkladanie("TAK");

            if (textBox1.Text == "" || ETYKIETA.Text == "" || nazwy_id[comboBox1.SelectedIndex].ToString() == "")
            {
                MessageBox.Show("NIe wszystkie pola są uzupełnione");
                return;
            }


            XmlNode node_etykieta = KomunikacjaSerwer(node, ETYKIETA.Text, 2);

            if (node_etykieta["komunikat"].InnerText != "OK")
            {
                //ETYKIETA.Text = "";
                message_label.BackColor = System.Drawing.Color.Red;

                ETYKIETA.Focus();

                if (node_etykieta["komunikat"].InnerText.Length > 40)
                {
                    MessageBox.Show(node_etykieta["komunikat"].InnerText);
                }
                else
                {
                    message_label.Text = node_etykieta["komunikat"].InnerText;
                }
            }


            //myParent.Show();
            
            //this.Close();
        }

        private void podglad_button_Click(object sender, EventArgs e)
        {
            Zad_DL_Podglad okno_nowy_nosnik = new Zad_DL_Podglad(this, node);
            okno_nowy_nosnik.Show();
            this.Hide();
        }

        private void Etykieta_KeyDown(object sender, KeyEventArgs e)
        {
            if (e.KeyCode == Keys.Enter)
            {
                this.dodawanie(ETYKIETA.Text);
                //odkladanie_Click(this, new EventArgs());
            }
        }






    }
}