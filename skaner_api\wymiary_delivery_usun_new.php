<?php

//include_once 'db_pdo.php';
include_once 'Db.class.php';
include_once 'funkcje.inc';
error_reporting(E_ALL);
ini_set('display_errors', 1);

$db = new Db();
//
//if (empty($argv)) {
//    //$argv = array();
//    $argv[1] = $_GET['db'];
//    $argv[2] = $_GET['akcja'];
//}
//
$baza_danych = $_GET['db'];
$akcja = $_GET['akcja'];
$delivery_wymiary_head_id = $_GET['delivery_wymiary_head_id'];




$komunikat = "OK";

if ($akcja == "usun_ostatni") {



    usun("wmsgg", $delivery_wymiary_head_id, $db);
    return xml_from_indexed_array(array('komunikat' => $komunikat));
}

function usun($baza_danych, $delivery_wymiary_head_id, $db) {
    $sql = "delete  FROM " . $baza_danych . ".delivery_wymiary_dane where delivery_wymiary_head_id=" . $delivery_wymiary_head_id . " order by id desc limit 1;";
    //echo $sql;
    $result = $db->mGetResultAsXML($sql);

    $sql = 'SELECT id FROM wmsgg.delivery_wymiary_dane
where delivery_wymiary_head_id="' . $delivery_wymiary_head_id . '"';
    //echo $sql;

    $result2 = $db->mGetResultAsXML($sql);

    if (empty($result2)) {
        $sql = "delete  FROM " . $baza_danych . ".delivery_wymiary_head where id=" . $delivery_wymiary_head_id . " ";
        //echo $sql;
        $result3 = $db->mGetResultAsXML($sql);
    }
}
