﻿using System;
using System.Linq;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using MySql.Data.MySqlClient;
using System.Text.RegularExpressions;
using System.Threading;
using System.Net;


using System.Collections;

using System.IO;
using System.Xml;


namespace Tarczyn__Magazyn
{
    public partial class Zad_DL_RoczpNos : Form
    {
        public string komunikat = "";
        public string wybierane_id = "";
        public string wybierana_nazwa = "";

        Thread Skanowanie = null;

        List<string> nazwy_wyswietlane = new List<string>();
        int[] nazwy_id = new int[100];

        Zad_Main myParent = null;
        XmlNodeList xmlnode = null;
        XmlNode node = null;
        XmlNode node_myParent = null;

        TextBox AktualnyTextBox = null;




        public Zad_DL_RoczpNos(Zad_Main c, XmlNode node2)
        {
            //MessageBox.Show("Zadania_Delivery_RoczpoczynanieNosnika");
            FullScreenMode.OknoOFF(this);
            InitializeComponent();


            textBox2.Text = node2["system_id_nazwa"].InnerText + "; DL" + node2["doc_id"].InnerText;
            textBox3_kontrah.Text = node2["logo"].InnerText;
            myParent = c;
            node_myParent = node2;

            inicjalizacja();
            adres_id_drukarka_textBox3.Focus();



        }

        void inicjalizacja()
        {



            XmlDocument doc1 = WebService.Pobierz_XmlDocument("delivery_tworzenie_nosnika.php?db=" + node_myParent["baza_danych"].InnerText + "&akcja=insert");

            xmlnode = doc1.GetElementsByTagName("dane");




            foreach (XmlNode wynik in xmlnode)
            {
                node = wynik;
            }

            if (node["komunikat"].InnerText != "OK")
            {
                MessageBox.Show(node["komunikat"].InnerText);
                return;
            }

            //nosnik_numer = node["sscc"].InnerText;
            XmlNodeList xmlnode2 = null;
            textBox1.Text = "DS" + node["paleta_id"].InnerText;
            //MessageBox.Show("2b");





            //MessageBox.Show("2c");
            //MessageBox.Show("3");


            xmlnode2 = doc1.GetElementsByTagName("palety_rodzaje");

            nazwy_wyswietlane.Clear();
            nazwy_wyswietlane.Add("");

            nazwy_id[0] = 0;
            int k = 0;

            foreach (XmlNode wynik in xmlnode2)
            {
                //MessageBox.Show("4" + wynik.InnerXml);

                nazwy_wyswietlane.Add(wynik["opis"].InnerText);
                nazwy_id[k + 1] = Convert.ToInt32(wynik["typypalet_id"].InnerText);
                k += 1;
            }
            //MessageBox.Show("5");

            BindingSource bs = new BindingSource();
            bs.DataSource = nazwy_wyswietlane;
            comboBox1.DataSource = bs;





        }



        #region fullscreen
        private void fullscreenmode()
        {
            this.Width = Screen.PrimaryScreen.Bounds.Width;
            this.Height = Screen.PrimaryScreen.Bounds.Height;
            this.Left = 0;
            this.Top = 0;
        }
        #endregion

        private void button1_Click(object sender, EventArgs e)
        {

        }

        private void Dalej_Click(object sender, EventArgs e)
        {

        }

        private void GotFocus_TextBox(object sender, EventArgs e)
        {
            Skaner.Przewij_Skanowanie();
            TextBox Pole_Tekstowe = (TextBox)sender;
            AktualnyTextBox = Pole_Tekstowe;
            ZacznijSkanowanie();
        }

        private void LostFocus_TextBox(object sender, EventArgs e)
        {
            AktualnyTextBox = null;
            Skaner.Przewij_Skanowanie();

            TextBox Pole_Tekstowe = (TextBox)sender;


        }

        private void ZacznijSkanowanie()
        {
            StringBuilder login = new StringBuilder();
            Skaner.UstawTryb_String(login);
            Skaner.Zacznij_Skanowanie();
            Skanowanie = new Thread(() => CheckString(login));
            Skanowanie.IsBackground = true;
            Skanowanie.Start();
        }

        private void CheckString(StringBuilder cc)
        {
            while (true)
            {
                if (cc.ToString() != "")
                {
                    this.Invoke(new EventHandler(delegate
                    {
                        dodawanie(cc.ToString());
                    }));
                    return;
                }
                Thread.Sleep(200);
            }
        }


        private void Zakoncz_Skanowanie()
        {
            {
                Skanowanie.Abort();
                Skaner.Przewij_Skanowanie();
            }
        }

        private void dodawanie(string ops)
        {
            //MessageBox.Show("TrybSkanu:" + TrybSkanu + "   ,   ops.Length:" + ops.Length);
            Zakoncz_Skanowanie();
            if (ops.Substring(0, 2) == "IP")
            {
                AktualnyTextBox.Text = ops;
                Zakoncz_Skanowanie();
                comboBox1.Focus();
            }
            else if (ops.Substring(0, 2) == "DS")
            {
                XmlDocument doc1 = WebService.Pobierz_XmlDocument("delivery_tworzenie_nosnika.php?db=" + node_myParent["baza_danych"].InnerText + "&akcja=sprawdz_palete&paleta_id=" + ops.Replace("DS", "") + "&delivery_id=" + node_myParent["doc_id"].InnerText);
                XmlNode node = WebService.Pobierz_XmlNode(doc1, "dane");

                if (node["komunikat"].InnerText != "OK")
                {
                    MessageBox.Show(node["komunikat"].InnerText);
                    ZacznijSkanowanie();
                    return;
                }
                else
                {
                    textBox1.Text = ops;
                    myParent.nr_nosnika_kompletowany = node["paleta_id"].InnerText;
                    myParent.delivery_kompletacja(node);
                    this.Close();
                }
            }
            else
            {
                MessageBox.Show("To nie jest drukarka ani paleta");
                ZacznijSkanowanie();
                return;
            }
        }

        private void Dalej_Click_1(object sender, EventArgs e)
        {
            if (comboBox1.SelectedValue.ToString() == "")
            {
                MessageBox.Show("Nie dokonano wyboru");
                return;
            }
            // zapis nośnika do bazy z typem
            //myParent.typypalet_id = nazwy_id[comboBox1.SelectedIndex].ToString();
            wybierana_nazwa = comboBox1.SelectedValue.ToString();
            XmlDocument doc1 = WebService.Pobierz_XmlDocument("delivery_tworzenie_nosnika.php?db=" + node_myParent["baza_danych"].InnerText + "&akcja=update&paleta_id=" + textBox1.Text.Replace("DS", "") + "&typypalet_id=" + nazwy_id[comboBox1.SelectedIndex].ToString() + "&delivery_id=" + node_myParent["doc_id"].InnerText + "&system_id_nazwa=" + node_myParent["system_id_nazwa"].InnerText + "&adres_ip=" + adres_id_drukarka_textBox3.Text + "&paleta_nazwa=" + nazwy_wyswietlane[comboBox1.SelectedIndex].ToString());
            xmlnode = doc1.GetElementsByTagName("dane");

            foreach (XmlNode wynik in xmlnode)
            {
                node = wynik;
            }
            if (node["komunikat"].InnerText != "OK")
            {
                MessageBox.Show(node["komunikat"].InnerText);
                return;
            }

            try
            {
                myParent.nr_nosnika_kompletowany = textBox1.Text.Replace("DS", "");
            }
            catch (MySqlException ex)
            {
                MessageBox.Show(ex.ToString());
            }

            myParent.delivery_kompletacja(node);
            this.Close();
        }

        private void button1_Click_1(object sender, EventArgs e)
        {
            Skaner.Przewij_Skanowanie();
            myParent.Show();
            this.Close();
        }











    }
}