<?php

//include_once 'db_pdo.php';
include_once 'Db.class.php';
include_once 'funkcje.inc';
include_once './../skrypty/utils.php';
//error_reporting(E_ALL);
//ini_set('display_errors', 1);
$baza_danych = "wmsgg";
$db = new Db("localhost", $baza_danych);


$akcja = $_GET['akcja'];
$system_id = $_GET['system_id'];
$etykieta_scan = $_GET["etykieta_scan"];

$operac_id = $_GET['operac_id'];
$miejsce_id = $_GET['miejsce_id'];
$imie_nazwisko = $_GET['imie_nazwisko'];
$pracownik_id = $_GET['pracownik_id'];
//$paleta_id = $_GET['paleta_id'];

$miejsce_id = $_GET['miejsce_id'];
$wozek = $_GET['wozek'];
$id_niezrealizowane = $_GET['id_niezrealizowane'];



$zm_nr = $_GET['zm_nr'];
$komunikat = "OK";



if (empty($operac_id)) {
    $operac_id = docnumber_increment($baza_danych, "operacja_id", $db);
}

if (empty($zm_nr)) {
    $zm_nr = get_nr_zm($db);
}
//print_r($zm_nr);
//return;
//$asdf = pobierz_miejsce($miejsce_id, $db);
//print_r($asdf);
//return;

if ($akcja == "realizacja_zmiany") {
    Realizuj_Zmiane_Miejsca($etykieta_scan, $zm_nr, $pracownik_id, $miejsce_id, $operac_id, $wozek, $regal = "", $imie_nazwisko, $id_niezrealizowane, $komunikat, $db);
}



//print_r(pobierz_id_miejsca($hala_local, $regal_local, $miejsce_local, $poziom_local, $db)
//print_r(get_nr_zm($db));
return;

//sprawdź blokadę wyższych poziomów z więcej niż jedna etykieta ponad poziomem A




if ($akcja == "pobranie_palety") {
    $tmp_arr = sprawdz_szukana_etykiete_WMS($skan, $komunikat, $db);
    //print_r($tmp_arr);
    if (empty($tmp_arr['komunikat']) || $tmp_arr['komunikat'] != "OK") {
        $komunikat = $tmp_arr['komunikat'];
        return show_komunikat_xml($komunikat);
    } else {
        $tmp_arr['operac_id'] = $operac_id;
        xml_from_indexed_array($tmp_arr);
    }
}

function Realizuj_Zmiane_Miejsca($etykieta, $zm_nr_global, $pracownik_id, $miejsce_id, $operacja_id, $wozek, $regal, $imie_nazwisko, $id_niezrealizowane, $komunikat, $db) {
    $typ_operacji = "ZM";

//    if ($regal == "POD")
//        $typ_operacji = "ZM_POD";
    $nowe_miejsce = pobierz_miejsce($miejsce_id, $db);
    $waga_miejsca = 0;
    //print_r($nowe_miejsce);
    if (!empty($nowe_miejsce)) {
        $waga_miejsca = $nowe_miejsce['max_udzwig_kg'];
        //echo $waga_miejsca;
    }

    if (substr($etykieta, 0, 2) == "DS") {
        $sql = "select e.id,e.system_id,miejscep,paleta_id,regal,miejsce,poziom,e.magazyn,ifnull(e.active,3) as active,"
                . "( select  ifnull(round(sum(kk.waga_szt_kg*ee.ilosc),0),0) as waga_palety from etykiety ee
left join kody kk on ee.kod_id=kk.id where ee.paleta_id=" . str_replace("DS", '', $etykieta) . " and (ee.active=1 or ee.active is null ) )
as waga_palety

from etykiety e left join miejsca m on e.miejscep=m.id where e.paleta_id=" . str_replace("DS", '', $etykieta) . " and (active=1 or active is null )";
    } else {
        $sql = "select e.id,e.system_id,miejscep,paleta_id,regal,miejsce,poziom,e.magazyn,ifnull(e.active,3) as active,"
                . "( select  ifnull(round(sum(kk.waga_szt_kg*ee.ilosc),0),0) as waga_palety from etykiety ee
left join kody kk on ee.kod_id=kk.id where (e.id='" . $etykieta . "' or e.etykieta_klient='" . $etykieta . "') and (ee.active=1 or ee.active is null ) )
as waga_palety  from etykiety e left join miejsca m on e.miejscep=m.id where (e.id='" . $etykieta . "' or e.etykieta_klient='" . $etykieta . "') and (active=1 or active is null )";
    }
    //echo "\n" . $sql;
    $result = $db->mGetResultAsXML($sql);
    //print_r($result);
    $regal_old = "";
    $miejsce_old = "";
    $poziom_old = "";
    foreach ($result as $index => $value) {
        $regal_old = $value['regal'];
        $miejsce_old = $value['miejsce'];
        $poziom_old = $value['poziom'];

        $waga_palety = $value['waga_palety'];

        if ($value['system_id'] == "4" && $value['magazyn'] == "41") {
            $waga_palety = 0;
        }
        //echo $waga_palety.":".$waga_miejsca;
        //return xml_from_indexed_array(array('komunikat' => "Nie można wstawić. Waga palety $waga_palety kg przekracza dopuszczalną wagę miejsca $waga_miejsca kg !!!"));

        if (!empty($waga_palety) && !empty($waga_miejsca)) {
            if (($waga_palety + 30) > $waga_miejsca) {
                return xml_from_indexed_array(array('komunikat' => "Nie można wstawić. Waga palety " . ($waga_palety + 30) . " kg przekracza dopuszczalną wagę miejsca $waga_miejsca kg !!!"));
            }
        }


        $sql = "insert into zmianym(typ,doc_nr,pracownik_id,data,etykieta,system_id,stare_m,nowe_m,doc_internal,stat,tszm) values(" .
                "'ZM'," . $zm_nr_global . "," . $pracownik_id . ",sysdate()," . $value['id'] . "," . $value['system_id'] . "," . $value['miejscep'] . "," . $miejsce_id . ",3,1,sysdate())";
        //echo "\n" . $sql;
        $result2 = $db->mGetResultAsXML($sql);
        if ($index == 0) {
            $sql = "insert into operacje(etykieta_id, doc_type, doc_nr, imie_nazwisko, typ_operacji,system_id,wozek,operac_id,ilosc) "
                    . "values('" . $value['id'] . "','" . $typ_operacji . "','" . $zm_nr_global . "','" . $imie_nazwisko . "','" . $typ_operacji . "','" . $value['system_id'] . "','" . $wozek . "','" . $operacja_id . "','1');";
            //echo "\n" . $sql;
            $resul3 = $db->mGetResultAsXML($sql);
        }
        $sql = "update etykiety set miejscep = " . $miejsce_id . " where id=" . $value['id'] . " limit 1; ";
        //echo "\n" . $sql;
        $resul3 = $db->mGetResultAsXML($sql);

        if (!empty($id_niezrealizowane)) {
            $sql = "delete from zmiany_miejsca_niezrealizowane where id='" . $id_niezrealizowane . "';";
            //echo "\n" . $sql;
            $db->mGetResultAsXML($sql);
        }
    }
    $ile_dokument = Count_PZ($etykieta, $db);
    $ile_wstawione = Count_PZ_Wstawione($etykieta, $db);
    $licznik = licznik($zm_nr_global, $db);

    $nowe_miejsce = pobierz_miejsce($miejsce_id, $db);
    return xml_from_indexed_array(array('komunikat' => $komunikat,
        'operac_id' => $operacja_id,
        'zm_nr' => $zm_nr_global,
        'ile_dokument' => $ile_dokument,
        'ile_wstawione' => $ile_wstawione,
        'licznik' => $licznik,
        'last_result_text' => $regal_old . "-" . $miejsce_old . "-" . $poziom_old . "->" . $nowe_miejsce['regal'] . "-" . $nowe_miejsce['miejsce'] . "-" . $nowe_miejsce['poziom'],
            )
    );
}

function licznik($zm_nr, $db) {
    $sql = 'SELECT count(distinct e.paleta_id) as licznik FROM zmianym z
left join etykiety e on e.id=z.etykieta
WHERE z.doc_nr=' . $zm_nr;
    $result2 = $db->mGetResultAsXML($sql);
    return $result2[0]['licznik'];
}

function pobierz_miejsce($miejsce_id, $db) {
    $sql = 'select m.*  from miejsca m where m.id=' . $miejsce_id . " limit 1 ";
    $result2 = $db->mGetResultAsXML($sql);
    return $result2[0];
}

function get_nr_zm($db) {
    $sql = "select max(doc_nr)+1 as zm_nr FROM zmianym";
    $result2 = $db->mGetResultAsXML($sql);

    foreach ($result2 as $index => $value) {
        return $value['zm_nr'];
    }
}

function Count_PZ($etykieta, $db) {

    $sql = "select (SELECT count(distinct ee.paleta_id) as ile FROM etykiety ee left join miejsca m on ee.miejscep=m.id where ee.docin_id=e.docin_id and regal!='RMP' and (ee.active=1 or ee.active is null)) as PZ from etykiety e left join docout d on e.docout_id=d.id where (e.etykieta_klient='" . $etykieta . "' or e.id='" . $etykieta . "' ) and (e.active=1 or e.active is null)  order by e.id desc limit 1 ";
    if (substr($etykieta, 0, 2) == "DS") {
        $sql = "select (SELECT count(distinct ee.paleta_id) as ile FROM etykiety ee left join miejsca m on ee.miejscep=m.id where ee.docin_id=e.docin_id and regal!='RMP' and (ee.active=1 or ee.active is null)) as PZ from etykiety e left join docout d on e.docout_id=d.id where (e.paleta_id='" . str_replace("DS", "", $etykieta) . "') and (e.active=1 or e.active is null)  order by e.id desc limit 1 ";
    }
    $result2 = $db->mGetResultAsXML($sql);
    if (count($result2) == 0) {
        return 0;
    }
    foreach ($result2 as $index => $value) {
        return $value['PZ'];
    }
}

function Count_PZ_Wstawione($etykieta, $db) {
    $sql = "select (SELECT count(distinct ee.paleta_id) as ile FROM etykiety ee left join miejsca m on ee.miejscep=m.id where ee.docin_id=e.docin_id and regal!='RMP' and (ee.active=1 or ee.active is null)) as PZ from etykiety e left join docout d on e.docout_id=d.id where (e.etykieta_klient='" . $etykieta . "' or e.id='" . $etykieta . "' ) and (e.active=1 or e.active is null)  order by e.id desc limit 1 ";
    if (substr($etykieta, 0, 2) == "DS") {
        $sql = "select (SELECT count(distinct ee.paleta_id) as ile FROM etykiety ee left join miejsca m on ee.miejscep=m.id where ee.docin_id=e.docin_id and regal!='RMP' and (ee.active=1 or ee.active is null)) as PZ from etykiety e left join docout d on e.docout_id=d.id where (e.paleta_id='" . str_replace("DS", "", $etykieta) . "') and (e.active=1 or e.active is null)  order by e.id desc limit 1 ";
    }
    $result2 = $db->mGetResultAsXML($sql);
    if (count($result2) == 0) {
        return 0;
    }
    foreach ($result2 as $index => $value) {
        return $value['PZ'];
    }
}

function sprawdz_szukana_etykiete_WMS($skan, $komunikat, $db) {




    $sql = "select ifnull(m.hala,'') as hala,ifnull(m.regal,'') as regal,ifnull(m.miejsce,'') as miejsce,e.docin_id, ifnull(concat(m.hala,'-',m.regal,'-',m.miejsce,'-',m.poziom),'')  as adres,e.paleta_id, ifnull(delivery_nr,'') as delivery_nr , ifnull((select nr_dl from dlcollect dd where nr_et=e.id order by dd.id desc limit 1 ),'') as dlcollect,ifnull(g.nazwa,kg.nazwa) as nazwa,ifnull(g.id,kg.id) as grupa_id, ifnull(g.zakres_opis,kg.zakres_opis) as zakres,(SELECT count(1) as ile FROM miejsca_wolne w left join miejsca_grupy m on m.miejsce_id=w.miejsce_id left join miejsca mm on mm.id=w.miejsce_id WHERE m.kody_grupy_id=g.id and w.miejsce_id is not null and w.ts is null and mm.widoczne=1) as miejsc_przydzielonych,  s.nazwa as status_nazwa,s.funkcja_stat, s.nazwa as status_system_nazwa from etykiety e left join miejsca m on e.miejscep=m.id left join delivery d on e.delivery_id=d.id left join kody k on e.kod_id=k.id left join kody_grupy g on k.kody_grupy_id=g.id left join kody_grupy kg on e.system_id=kg.system_id left join status_system s on e.status_id=s.id where   (e.id='" . $skan . "' OR e.etykieta_klient='" . $skan . "' ) and (e.active=1 or e.active is null) order by e.id desc limit 1 ";
    if (substr($skan, 0, 2) == "DS") {
        $sql = "select ifnull(m.hala,'') as hala,ifnull(m.regal,'') as regal,ifnull(m.miejsce,'') as miejsce,e.docin_id, ifnull(concat(m.hala,'-',m.regal,'-',m.miejsce,'-',m.poziom),'')  as adres,e.paleta_id, ifnull(delivery_nr,'') as delivery_nr , ifnull((select nr_dl from dlcollect dd where nr_et=e.id order by dd.id desc limit 1 ),'') as dlcollect,ifnull(g.nazwa,kg.nazwa) as nazwa,ifnull(g.id,kg.id) as grupa_id, ifnull(g.zakres_opis,kg.zakres_opis) as zakres,(SELECT count(1) as ile FROM miejsca_wolne w left join miejsca_grupy m on m.miejsce_id=w.miejsce_id left join miejsca mm on mm.id=w.miejsce_id WHERE m.kody_grupy_id=g.id and w.miejsce_id is not null and w.ts is null and mm.widoczne=1) as miejsc_przydzielonych,  s.nazwa as status_nazwa,s.funkcja_stat, s.nazwa as status_system_nazwa from etykiety e left join miejsca m on e.miejscep=m.id left join delivery d on e.delivery_id=d.id left join kody k on e.kod_id=k.id left join kody_grupy g on k.kody_grupy_id=g.id left join kody_grupy kg on e.system_id=kg.system_id left join status_system s on e.status_id=s.id where   (e.paleta_id='" . str_replace("DS", "", $skan) . "') and (e.active=1 or e.active is null) order by e.id desc limit 1 ";
    }




    $result2 = $db->mGetResultAsXML($sql);
    $aRowEtWms = array();

    if (count($result2) == 0) {
        return array('komunikat' => "Brak etykiety w systemie");
    }
    //echo "<br>" . $sql;
    foreach ($result2 as $index => $aRowEtWms) {
//        if ($aRowEtWms['active'] != "1") {
//            $komunikat = "Etykieta jest nieaktywna. Przerywam operacje";
//            //echo "<br>" . $komunikat;
//            //return show_komunikat_xml($komunikat);
//        }

        if (!empty($aRowEtWms['delivery_nr']) || !empty($aRowEtWms['dlcollect'])) {
            $komunikat = "Etykieta jest wczytana na DL" . $aRowEtWms['delivery_nr'] . "," . $aRowEtWms['dlcollect'];
            //echo "<br>" . $komunikat;
            //return show_komunikat_xml($komunikat);
        }
    }
    return array('komunikat' => $komunikat, 'docin_id' => $aRowEtWms['docin_id'], 'adres' => $aRowEtWms['adres']);
}

if ($akcja == "pobranie_miejsca") {
    //echo "11";
    if (substr($skan, 0, 2) == "MP") {
        $arr = explode("-", $skan);
        //echo "22";
        $hala = $arr[1];
        $regal = $arr[2];
        $miejsce = $arr[3];

        $poziomy = wyswietl_poziomy($baza_danych, $hala, $regal, $miejsce, $db);
//            echo "<pre>";
//            echo print_r($poziomy);
//            echo "</pre>";
////            echo "asd:" . count($poziomy);
////            xml_from_indexed_array($poziomy); 
//            return false;
        if (count($poziomy) == 0) {
            $komunikat = "Brak miejsc";
            return show_komunikat_xml($komunikat);
        }



        if (count($poziomy) > 1) {
            return xml_from_indexed_array(array('komunikat' => $komunikat, 'ilosc_pozycji_poziomow' => count($poziomy), 'poziomy' => $poziomy));
        } else {
            $miejsce_id = $poziomy[0]['id'];
        }
    }
}











if (!empty($miejsce_id)) {
    if ($komunikat == "OK") {

        $miejsce_sprawdzone = pobierz_miejsce($baza_danych, $miejsce_id, $db);
        if (count($miejsce_sprawdzone) == 0) {
            $komunikat = "Brak informacji o miejscu " . $miejsce_sprawdzone;
            return show_komunikat_xml($komunikat);
        }
        //$message_info = "Wstawione: H ".$result[0]['hala'] ." ".$result[0]['regal']."-".$result[0]['miejsce']."-".$result[0]['poziom'];
        //$komunikat = $sql;
        $pracownik_id = get_pracownik($baza_danych_local, $imie_nazwisko, $db);

        if (empty($paleta_id)) {
            $komunikat = "Brak w bazie etykiet na tej palecie ";
            return show_komunikat_xml($komunikat);
        }

        $wynik = pobierz_etykiety_id_palety($baza_danych, $paleta_id, $db);

        foreach ($wynik as $key => $valued) {
            $sql = "insert into $baza_danych.zmianym 
                        (typ, doc_nr, pracownik_id, data, etykieta, system_id, stare_m, nowe_m, doc_internal, stat, tszm)
                        values('ZM'," . $zm_nr . ", '$pracownik_id',CURDATE(), " . $valued['id'] . "," . $system_id . "," . $valued['miejscep'] . ", " . $miejsce_id . ", 'Z', 1, NOW())";
            //echo "<br>" . $sql;
            $result2 = $db->mGetResultAsXML($sql);

            $sql = "update $baza_danych.etykiety set miejscep=" . $miejsce_id . " where id=" . $valued['id'] . " limit 1";

            //echo "<br>" . $sql;
            $result2 = $db->mGetResultAsXML($sql);
        }
    }


    $komunikat .= $db->errors;
    return xml_from_indexed_array(array('komunikat' => $komunikat, 'czy_koniec_zadania' => $czy_koniec_zadania, 'ilosc_pozycji_poziomow' => 1));
}

function wyswietl_poziomy($baza_danych, $hala, $regal, $miejsce, $db) {
    $sql = 'SELECT m.id,m.poziom as nazwa_wyswietlana FROM ' . $baza_danych . '.miejsca m WHERE m.hala="' . $hala . '" AND m.regal="' . $regal . '" AND m.miejsce="' . $miejsce . '" AND m.widoczne=1 
ORDER BY m.poziom DESC;  ';
    //echo $sql;
    $result = $db->mGetResultAsXML($sql);
    return $result;
}

function pobierz_etykiety_id_palety($baza_danych, $paleta_id, $db) {
    $sql = 'select e.id,e.miejscep  from ' . $baza_danych . '.etykiety e  where e.paleta_id=' . $paleta_id . "  ";

    $result2 = $db->mGetResultAsXML($sql);
    return $result2;
}

?>