﻿using System;
using System.Collections.Generic;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using System.Threading;
using System.Text.RegularExpressions;
using System.Xml;
using System.Web;

namespace Tarczyn__Magazyn
{
    public partial class Wolne_Miejsca : Form
    {
        private Thread Skanowanie;
        int limit = 0;
        public string hala = "";
        public string regal = "";
        public string miejsce = "";
        ActionMenu myParent = null;
        string operac_id_global = "";

        public Wolne_Miejsca(ActionMenu MyParent)
        {
            InitializeComponent();
            this.myParent = MyParent;
            Wlasciwosci.CurrentOperacja = "23";

            XmlDocument doc = WebService.Pobierz_XmlDocument("wolne_miejsca.php?akcja=szukaj&db=wmsgg");
            XmlNode node = WebService.Pobierz_XmlNode(doc, "dane");
            operac_id_global = node["operac_id"].InnerText;

            this.ZacznijSkanowanie();
        }

        private void ZacznijSkanowanie()
        {
            StringBuilder login = new StringBuilder();
            Skaner.UstawTryb_String(login);
            Skaner.Zacznij_Skanowanie();
            this.Skanowanie = new Thread(delegate() { this.CheckString(login); });
            this.Skanowanie.IsBackground = true;
            this.Skanowanie.Start();
        }

        private void CheckString(StringBuilder cc)
        {
            EventHandler method = null;

            while (true)
            {
                if (cc.ToString() != "")
                {
                    if (method == null)
                    {
                        method = delegate
                        {
                            this.dodawanie(cc.ToString());
                        };
                    }
                    this.Invoke(method);
                    return;
                }
                Thread.Sleep(200);
            }
        }

        private void dodawanie(string ops)
        {
            if (ops.Substring(0, 2) == "MP")
            {
                Regex regex = new Regex("-");
                string[] words = regex.Split(ops.Substring(3, ops.Length - 3));
                hala = words[0];
                regal = words[1].TrimStart('0');

                label9.Text = regal;
                miejsce = words[2];

                ResetLabels();
                pobierz_wolne(hala, regal, limit);
            }
            else
            {
                MessageBox.Show("To nie jest etykieta miejsca. Spróbuj ponownie.");
            }
            ZacznijSkanowanie();
        }

        private void ResetLabels()
        {
            Label[] labels = new Label[] { label8, label7, label6, label5, label4, label3, label2, label1 };
            foreach (Label label in labels)
            {
                label.Text = "";
                label.Font = new Font("Arial", 18F, FontStyle.Bold);
            }
        }

        private void pobierz_wolne(string halaa, string regall, int limitt)
        {
            string url = string.Format("wolne_miejsca.php?akcja=szukaj&db=wmsgg&hala={0}&regal={1}&limit={2}&operac_id={3}&imie_nazwisko={4}",
                halaa, regall, limitt, operac_id_global, Wlasciwosci.imie_nazwisko);
            XmlDocument doc = WebService.Pobierz_XmlDocument(url);
            XmlNodeList xmlnode = WebService.Pobierz_XmlNodeList(doc, "wynik");

            Label[] labels = new Label[] { label8, label7, label6, label5, label4, label3, label2, label1 };

            for (int i = 0; i < xmlnode.Count && i < labels.Length; i++)
            {
                labels[i].Text = xmlnode[i]["miejsce"].InnerText + "-" + xmlnode[i]["poziom"].InnerText;
            }
        }

        private void button1_Click(object sender, EventArgs e)
        {
            Wlasciwosci.CurrentOperacja = "0";
            this.Skanowanie.Abort();
            Skaner.Przewij_Skanowanie();
            this.myParent.Show();
            this.Close();
        }

        private void button2_Click(object sender, EventArgs e)
        {
            if (regal != "")
            {
                limit += 8;
                ResetLabels();
                pobierz_wolne(hala, regal, limit);
            }
            else
            {
                MessageBox.Show("Wczytaj regal");
            }
        }

        private void button3_Click(object sender, EventArgs e)
        {
            if (regal != "" && limit > 0)
            {
                limit -= 8;
                ResetLabels();
                pobierz_wolne(hala, regal, limit);
            }
            else if (regal == "")
            {
                MessageBox.Show("Wczytaj regal");
            }
        }

        private void wyslij_przycisk(Label labelek, string komunikat)
        {

        }

        private void button4_Click(object sender, EventArgs e)
        {
            wyslij_przycisk(label1, "Miejsce zajete");
        }

        private void button5_Click(object sender, EventArgs e)
        {
            wyslij_przycisk(label2, "Miejsce zajete");
        }

        private void button6_Click(object sender, EventArgs e)
        {
            wyslij_przycisk(label3, "Miejsce zajete");
        }

        private void button7_Click(object sender, EventArgs e)
        {
            wyslij_przycisk(label4, "Miejsce zajete");
        }

        private void button8_Click(object sender, EventArgs e)
        {
            wyslij_przycisk(label5, "Miejsce zajete");
        }

        private void button9_Click(object sender, EventArgs e)
        {
            wyslij_przycisk(label6, "Miejsce zajete");
        }

        private void button10_Click(object sender, EventArgs e)
        {
            wyslij_przycisk(label7, "Miejsce zajete");
        }

        private void button11_Click(object sender, EventArgs e)
        {
            wyslij_przycisk(label8, "Miejsce zajete");
        }

        private void button12_Click(object sender, EventArgs e)
        {
            wyslij_przycisk(label1, "Miejsce podwójne");
        }

        private void button13_Click(object sender, EventArgs e)
        {
            wyslij_przycisk(label2, "Miejsce podwójne");
        }

        private void button14_Click(object sender, EventArgs e)
        {
            wyslij_przycisk(label3, "Miejsce podwójne");
        }

        private void button15_Click(object sender, EventArgs e)
        {
            wyslij_przycisk(label4, "Miejsce podwójne");
        }

        private void button16_Click(object sender, EventArgs e)
        {
            wyslij_przycisk(label5, "Miejsce podwójne");
        }

        private void button17_Click(object sender, EventArgs e)
        {
            wyslij_przycisk(label6, "Miejsce podwójne");
        }

        private void button18_Click(object sender, EventArgs e)
        {
            wyslij_przycisk(label7, "Miejsce podwójne");
        }

        private void button19_Click(object sender, EventArgs e)
        {
            wyslij_przycisk(label8, "Miejsce podwójne");
        }
    }
}