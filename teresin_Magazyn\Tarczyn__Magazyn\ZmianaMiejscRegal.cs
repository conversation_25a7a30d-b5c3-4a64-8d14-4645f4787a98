﻿using System;
using System.Linq;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using MySql.Data.MySqlClient;
using System.Text.RegularExpressions;
using System.Threading;
using System.Net;


using System.Collections;

using System.IO;


namespace Tarczyn__Magazyn
{
    public partial class ZmianaMiejscRegal : Form
    {
        ActionMenu myParent = null;
        List<string> _hala = new List<string>();
        List<string> _regal = new List<string>();
        List<string> _poziom = new List<string>();
        private static string zm_date = "";
        List<string> Etykiety_dodane = new List<string>();
        int z = 0;
        int licznik = 0;
        public string poziom = "";
        public string prawa = "";
        public string lewa = "";

        public string hala = "";
        public string regal = "";
        public string miejsce = "";
        public string paleta_id = "";

        public string regal_ostatnio = "";

        public string wysokosc_opis = "";
        public string grupa_id = "";

        string operac_id_global = "";
        int nowe_m = 0;

        public string ostatnio_hala = "";
        public string ostatnio_regal = "";
        public string ostatnio_miejsce = "";
        public string id_zmiany_miejsca_niezrealizowane = "0";



        private Thread Skanowanie;
        Dictionary<string, string> rec = new Dictionary<string, string>();

        bool czy_buffor = false;
        string zm_nr_global = "";
        string zm_data_global = "";
        public static string etykieta_ostatnia = "";
        public static string docin_ostatnie = "";
        string order_search = "";

        static string ilosc_Count_PZ = "0";
        static string ilosc_Count_PZ_Wstawione = "0";

        string typ_operacji = "ZM";
        
        


        public ZmianaMiejscRegal(ActionMenu MyParent)
        {
            //
            InitializeComponent();
            //this.etykieta_textbox.KeyPress += new KeyPressEventHandler(etykieta_textbox_KeyPress);
            this.myParent = MyParent;
            wms_wybrany.Text = Wlasciwosci.system_id;
            imie_nazwisk_label.Text = Wlasciwosci.wozek + ":" + Wlasciwosci.imie_nazwisko;
            //FERRERO_Synchronizacja.SprawdzWolnaNazwePliku();
            timer1.Enabled = false;
            Wlasciwosci.CurrentOperacja = "11";

            if (BazaDanychExternal.SprawdzCzyIstniejePolaczenie())
            {
                //    Sprawdz_zgodnosc_opisow();

                string zapytanie = "SELECT last FROM docnumber d WHERE d.name='operacja_id';";

                operac_id_global = (string)BazaDanychExternal.Wyczytaj_Jedna_Wartosc(zapytanie);

                string zapyt = "update docnumber d set last=last+1 WHERE d.name='operacja_id'; ";
                BazaDanychExternal.Wyczytaj_Jedna_Wartosc(zapyt);
            }



            // jeśli jest połączenie to niech sprawdzi opisy hal i ewentualnie zsynchronizuje




            //ilosc_etykiet.Text = "" + Internal_Select_Count_Zmianym();
            this.ZacznijSkanowanie();
        }

        private void etykieta_textbox_KeyPress(object sender, KeyPressEventArgs e)
        {
            if (e.KeyChar == (char)Keys.Back)
            {
                e.Handled = true;
                etykieta_textbox.Text = "";
            }
            else
            {
                e.Handled = false;
                return;
            }
        }





        private static string[] get_dokument(string etykieta) //pobiera
        {
            string zapytanie = "select d.doc_type,d.doc_nr,e.listcontrol_id from etykiety e left join docin d on e.docin_id=d.id  where e.id=" + etykieta + " limit 1";
            object obj2 = BazaDanychExternal.Wyczytaj_Tabele(zapytanie);
            DataTable tabela = (DataTable)obj2;
            string[] bb = new string[3];
            bb[0] = tabela.Rows[0]["doc_type"].ToString();
            bb[1] = tabela.Rows[0]["doc_nr"].ToString();
            bb[2] = tabela.Rows[0]["listcontrol_id"].ToString();
            return bb;
        }


        private static string[] get_max_zmianym_date() //pobiera
        {
            string zapytanie = "select IFNULL(max(doc_nr)+1,1) as max_doc_nr, DATE_FORMAT(now(),'%Y-%m-%d') as data_zm from zmianym;";
            object obj2 = BazaDanychExternal.Wyczytaj_Tabele(zapytanie);
            DataTable tabela = (DataTable)obj2;
            string[] bb = new string[2];
            bb[0] = tabela.Rows[0]["max_doc_nr"].ToString();
            bb[1] = tabela.Rows[0]["data_zm"].ToString();
            return bb;
        }





        public static string Count_zm(string doc_nr)
        {

            string zapytanie = "SELECT count(distinct e.paleta_id) FROM zmianym z left join etykiety e on e.id=z.etykieta where z.doc_nr='" + doc_nr + "'   ;";
            string aa = (string)BazaDanychExternal.Wyczytaj_Jedna_Wartosc(zapytanie);
            return aa;
        }

        public static string Count_PZ(string etykieta)
        {
            //MessageBox.Show("Count_PZ");
            string zapytanie = "select (SELECT count(distinct ee.paleta_id) as ile FROM etykiety ee left join miejsca m on ee.miejscep=m.id where ee.docin_id=e.docin_id and ee.active=1) as PZ from etykiety e left join docout d on e.docout_id=d.id where (e.etykieta_klient='" + etykieta + "' or e.id='" + etykieta + "' ) and (e.active=1)  order by e.id desc limit 1";
            if (etykieta.Substring(0, 2) == "DS")
            {
                zapytanie = "select (SELECT count(distinct ee.paleta_id) as ile FROM etykiety ee left join miejsca m on ee.miejscep=m.id where ee.docin_id=e.docin_id and ee.active=1) as PZ from etykiety e left join docout d on e.docout_id=d.id where (  e.paleta_id='" + etykieta.Replace("DS", "") + "') and (e.active=1)  order by e.id desc limit 1";
            }


            
            string aa = (string)BazaDanychExternal.Wyczytaj_Jedna_Wartosc(zapytanie);
            return aa;
        }


        public static string Count_PZ_Wstawione(string etykieta)
        {
            //MessageBox.Show("Count_PZ_Wstawione");
            string zapytanie = "select (SELECT count(distinct ee.paleta_id) as ile FROM etykiety ee left join miejsca m on ee.miejscep=m.id where ee.docin_id=e.docin_id and regal not like '%RMP%' and ee.active=1) as PZ from etykiety e left join docout d on e.docout_id=d.id where (e.etykieta_klient='" + etykieta + "' or e.id='" + etykieta + "' ) and (e.active=1)  order by e.id desc limit 1";
            if (etykieta.Substring(0, 2) == "DS")
            {
                zapytanie = "select (SELECT count(distinct ee.paleta_id) as ile FROM etykiety ee left join miejsca m on ee.miejscep=m.id where ee.docin_id=e.docin_id and regal not like '%RMP%' and ee.active=1) as PZ from etykiety e left join docout d on e.docout_id=d.id where (  e.paleta_id='" + etykieta.Replace("DS", "") + "') and (e.active=1)  order by e.id desc limit 1";
            }
            string aa = (string)BazaDanychExternal.Wyczytaj_Jedna_Wartosc(zapytanie);
            return aa;
        }

        public static string Docin_id_PZ(string etykieta)
        {
            string zapytanie = "select ee.docin_id FROM etykiety ee  where (ee.etykieta_klient='" + etykieta + "' or ee.id='" + etykieta + "' or  ee.paleta_id='" + etykieta.Replace("DS", "") + "')  limit 1";
            string aa = (string)BazaDanychExternal.Wyczytaj_Jedna_Wartosc(zapytanie);
            return aa;
        }

        public static string Czy_miejsce_zajete(string hala_local, string regal_local, string miejsce_local, string poziom_local)
        {
            string zapytanie = "SELECT count(1) FROM etykiety e left join miejsca m on e.miejscep=m.id where (e.active=1) and hala='" + hala_local + "' and regal='" + regal_local + "' and miejsce='" + miejsce_local + "' and poziom='" + poziom_local + "'  and poziom!='A' and !(regal between 105 and 109);";
            string aa = (string)BazaDanychExternal.Wyczytaj_Jedna_Wartosc(zapytanie);
            return aa;
        }
        /*
        public static string miejsce_aktualne(string etykieta)
        {
            string zapytanie = "select ifnull(concat(m.regal,'-',m.miejsce,'-',m.poziom),'')  as adres from etykiety e left join miejsca m on e.miejscep=m.id where (e.id='" + etykieta + "' OR e.etykieta_klient='" + etykieta + "' OR e.paleta_id='" + etykieta.Replace("DS", "") + "') and e.active=1 order by e.id desc limit 1   ;";
            string aa = (string)BazaDanychExternal.Wyczytaj_Jedna_Wartosc(zapytanie);
            return aa;
        }
        */

        private static string[] miejsce_aktualne(string etykieta) //pobiera
        {
            string zapytanie = "select m.hala,m.regal,m.miejsce,m.poziom from etykiety e left join miejsca m on e.miejscep=m.id where (e.id='" + etykieta + "' OR e.etykieta_klient='" + etykieta + "' ) and e.active=1 order by e.id desc limit 1";
            if (etykieta.Substring(0, 2) == "DS")
            {
                zapytanie = "select m.hala,m.regal,m.miejsce,m.poziom from etykiety e left join miejsca m on e.miejscep=m.id where (e.paleta_id='" + etykieta.Replace("DS", "") + "') and e.active=1 order by e.id desc limit 1";
            }
            
            object obj2 = BazaDanychExternal.Wyczytaj_Tabele(zapytanie);
            DataTable tabela = (DataTable)obj2;
            string[] bb = new string[4];
            bb[0] = tabela.Rows[0]["hala"].ToString();
            bb[1] = tabela.Rows[0]["regal"].ToString();
            bb[2] = tabela.Rows[0]["miejsce"].ToString();
            bb[3] = tabela.Rows[0]["poziom"].ToString();
            return bb;
        }



        //ok
        private static int pobierz_id_miejsca(string hala_local, string regal_local, string miejsce_local, string poziom_local) //pobiera
        {
            //if(nr_etykiety) 
            string zapytanie = "";
            zapytanie = "select id, widoczne from miejsca m where hala='" + hala_local + "' and regal='" + regal_local + "' and miejsce='" + miejsce_local + "' and poziom='" + poziom_local + "' limit 1;";

            object obj2 = BazaDanychExternal.Wyczytaj_Tabele(zapytanie);

            DataTable tabela = (DataTable)obj2;


            if (tabela.Rows.Count < 1)
            {  //sprawdza 
                return 0;
            }
            else
            {
                if (tabela.Rows[0]["widoczne"] == "0") return -1;
                return Convert.ToInt32(tabela.Rows[0]["id"]);
            }
        }

        private bool get_etykieta(string etykieta) //pobiera
        {
            
            string zapytanie = "select ifnull(m.hala,'') as hala,ifnull(m.regal,'') as regal,ifnull(m.miejsce,'') as miejsce,e.docin_id, ifnull(concat(m.hala,'-',m.regal,'-',m.miejsce,'-',m.poziom),'')  as adres,e.paleta_id, ifnull(delivery_nr,'') as delivery_nr , ifnull((select nr_dl from dlcollect dd where nr_et=e.id order by dd.id desc limit 1 ),'') as dlcollect,ifnull(g.nazwa,kg.nazwa) as nazwa,ifnull(g.id,kg.id) as grupa_id, ifnull(g.zakres_opis,kg.zakres_opis) as zakres,(SELECT count(1) as ile FROM miejsca_wolne w left join miejsca_grupy m on m.miejsce_id=w.miejsce_id left join miejsca mm on mm.id=w.miejsce_id WHERE m.kody_grupy_id=g.id and w.miejsce_id is not null and w.ts is null and mm.widoczne=1) as miejsc_przydzielonych,  s.nazwa as status_nazwa,s.funkcja_stat from etykiety e left join miejsca m on e.miejscep=m.id left join delivery d on e.delivery_id=d.id left join kody k on e.kod_id=k.id left join kody_grupy g on k.kody_grupy_id=g.id left join kody_grupy kg on e.system_id=kg.system_id left join status_system s on e.status_id=s.id where   (e.id='" + etykieta + "' OR e.etykieta_klient='" + etykieta + "' ) and e.active=1 order by e.id desc limit 1 ";
            if (etykieta.Substring(0, 2) == "DS")
            {
                zapytanie = "select ifnull(m.hala,'') as hala,ifnull(m.regal,'') as regal,ifnull(m.miejsce,'') as miejsce,e.docin_id, ifnull(concat(m.hala,'-',m.regal,'-',m.miejsce,'-',m.poziom),'')  as adres,e.paleta_id, ifnull(delivery_nr,'') as delivery_nr , ifnull((select nr_dl from dlcollect dd where nr_et=e.id order by dd.id desc limit 1 ),'') as dlcollect,ifnull(g.nazwa,kg.nazwa) as nazwa,ifnull(g.id,kg.id) as grupa_id, ifnull(g.zakres_opis,kg.zakres_opis) as zakres,(SELECT count(1) as ile FROM miejsca_wolne w left join miejsca_grupy m on m.miejsce_id=w.miejsce_id left join miejsca mm on mm.id=w.miejsce_id WHERE m.kody_grupy_id=g.id and w.miejsce_id is not null and w.ts is null and mm.widoczne=1) as miejsc_przydzielonych,  s.nazwa as status_nazwa,s.funkcja_stat from etykiety e left join miejsca m on e.miejscep=m.id left join delivery d on e.delivery_id=d.id left join kody k on e.kod_id=k.id left join kody_grupy g on k.kody_grupy_id=g.id left join kody_grupy kg on e.system_id=kg.system_id left join status_system s on e.status_id=s.id where   (e.paleta_id='" + etykieta.Replace("DS", "") + "') and e.active=1 order by e.id desc limit 1 ";
            }
            //MessageBox.Show("etykieta:" + etykieta);
            
            object obj2 = BazaDanychExternal.Wyczytaj_Tabele(zapytanie);



            DataTable table = (DataTable)obj2;
            if (table.Rows.Count < 1)  //sprawdza czy jest etykieta
            {
                MessageBox.Show("Brak etykiety w bazie danych lub jeszcze nie przyjęta!");
                etykieta_textbox.Text = "";
                miejsce_poprzednie.Text = "";
                //etykieta_ostatnia = "";
                return false;
            }

            //MessageBox.Show("docin_ostatnie:" + table.Rows[0]["docin_id"].ToString());

            //MessageBox.Show("docin_ostatnie2:" + docin_ostatnie);
            if (docin_ostatnie != "")
            {

                if (docin_ostatnie != table.Rows[0]["docin_id"].ToString())
                {
                    if (kontrola_zmian_miejsc_przyjecie() == false)
                    {
                        //MessageBox.Show("kontrola_zmian_miejsc_przyjecie daje FALSE");
                        etykieta_textbox.Text = "";
                        return false;
                    }

                }
            }

            if (table.Rows[0]["funkcja_stat"].ToString() == "blokada_wyd" && table.Rows[0]["regal"].ToString() != "PROD")
            {
                MessageBox.Show("Etykieta " + etykieta + " ma status " + table.Rows[0]["status_nazwa"].ToString());
                etykieta_textbox.Text = "";
                miejsce_poprzednie.Text = "";
                //etykieta_ostatnia = "";
                return false;
            }



            if (table.Rows[0]["delivery_nr"].ToString() != "" || table.Rows[0]["dlcollect"].ToString() != "")
            {
                MessageBox.Show("Towar przygotowany na wysyłkę DL " + table.Rows[0]["delivery_nr"].ToString() + "," + table.Rows[0]["dlcollect"].ToString());
                etykieta_textbox.Text = "";
                miejsce_poprzednie.Text = "";
                //etykieta_ostatnia = "";
                return false;
            }

            ilosc_etykiet.Text = "" + Count_PZ_Wstawione(etykieta).ToString() + " z " + Count_PZ(etykieta).ToString();


            docin_ostatnie = table.Rows[0]["docin_id"].ToString();
            etykieta_ostatnia = etykieta;




            grupa_id = table.Rows[0]["grupa_id"].ToString();

            if (table.Rows[0]["regal"].ToString() == "RMP_A" || table.Rows[0]["regal"].ToString() == "RMP_B")
            {
                if (Convert.ToInt32(table.Rows[0]["miejsce"]) <= 25)
                {
                    order_search = "DESC";
                }
                else
                {
                    order_search = "ASC";
                }

            }
            else
            {
                if (ostatnio_miejsce != "")
                {
                    if (Convert.ToInt32(ostatnio_miejsce) <= 70) order_search = "ASC";
                    else order_search = "ASC";
                }

            }

            podpowadanie_miejsca(grupa_id, order_search);
            /*
            if (table.Rows[0]["miejsc_przydzielonych"].ToString() != "0")
            {
                //MessageBox.Show("miejsc_przydzielonych");
                
            }
             * */


            //label2.Text = table.Rows[0]["zakres"].ToString();
            miejsce_poprzednie.Text = "Aktualnie:" + Environment.NewLine + "" + table.Rows[0]["adres"].ToString();
            paleta_id = table.Rows[0]["paleta_id"].ToString();
            last_result_text.Text = "";
            //MessageBox.Show("Count_PZ_Wstawione");
            //ilosc_etykiet.Text = "" + ilosc_Count_PZ_Wstawione + " z " + ilosc_Count_PZ;


            return true;
        } 


        private static bool kontrola_zmian_miejsc_przyjecie() //pobiera
        {
            if (BazaDanychExternal.SprawdzCzyIstniejePolaczenie())
            {

                System.Threading.Thread.Sleep(1000);
                try
                {

                    //MessageBox.Show("etykieta_ostatnia:" + etykieta_ostatnia);
                    if (etykieta_ostatnia != "")
                    {
                        
                        if (ilosc_Count_PZ_Wstawione != ilosc_Count_PZ)
                        {

                            DialogResult result3 = MessageBox.Show(" Nie wstawiono wszystkich palet z rampy. Czy chcesz zakończyć ten dokument",
                            "Czy chcesz powrócić?",
                            MessageBoxButtons.YesNo,
                            MessageBoxIcon.Question,
                            MessageBoxDefaultButton.Button2);
                            if (result3 == DialogResult.Yes)
                            {
                                string URL = "http://172.6.1.249/wmsgg/public/skrypty/kontrola_zmian_miejsc_przyjecie.php?docin_id=" + Docin_id_PZ(etykieta_ostatnia);
                                const string data = @"{""object"":{""name"":""Title""}}";

                                HttpWebRequest request = (HttpWebRequest)WebRequest.Create(URL);
                                request.Method = "POST";
                                request.ContentType = "application/json";
                                request.ContentLength = data.Length;
                                StreamWriter requestWriter = new StreamWriter(request.GetRequestStream(), System.Text.Encoding.ASCII);
                                requestWriter.Write(data);
                                requestWriter.Close();

                                try
                                {
                                    // get the response
                                    WebResponse webResponse = request.GetResponse();
                                    Stream webStream = webResponse.GetResponseStream();
                                    StreamReader responseReader = new StreamReader(webStream);
                                    string response = responseReader.ReadToEnd();
                                    responseReader.Close();
                                    return true;
                                }
                                catch (WebException we)
                                {
                                    string webExceptionMessage = we.Message;
                                    return false;
                                }
                                catch (Exception ex)
                                {
                                    // no need to do anything special here....
                                    return false;
                                }
                            }
                            else
                            {
                                return false;
                            }
                        }
                        else
                        {
                            return true;
                        }
                        
                    }
                    else
                    {
                        return false;
                    }
                }
                catch (Exception exception2)
                {
                    MessageBox.Show(exception2.ToString());
                    return false;
                }



            }
            else
            {
                MessageBox.Show("Brak połączenia z bazą danych.Proszę o przemieszczenie się do miejsca z zasięgiem WI-FI i jeszcze raz Powrót");
                return false;

            }
        }




        private void Zakoncz_Skanowanie(object sender, EventArgs e)
        {
            this.Skanowanie.Abort();
            Skaner.Przewij_Skanowanie();
        }

        private void powrot_Click(object sender, EventArgs e)
        {

            if (etykieta_textbox.Text != "")
            {
                MessageBox.Show("Nie przydzielono palecie miejsca");
                return;
            }

            if (docin_ostatnie != "" )
            {
                if (kontrola_zmian_miejsc_przyjecie() == true)
                {

                }
                else
                {
                    return;
                }
            }

            this.Skanowanie.Abort();
            Skaner.Przewij_Skanowanie();

            Wlasciwosci.CurrentOperacja = "0";
            //myParent.wczytwanieEtykiet = false;
            this.myParent.Show();
            timer1.Enabled = false;
            this.Close();
                
            

        }



        private void button1_Anuluj(object sender, EventArgs e)
        {

        }
        public void ZacznijSkanowanie()
        {
            StringBuilder login = new StringBuilder();
            Skaner.UstawTryb_String(login);
            Skaner.Zacznij_Skanowanie();
            this.Skanowanie = new Thread(() => this.CheckString(login));
            this.Skanowanie.IsBackground = true;
            this.Skanowanie.Start();
        }



        private void CheckString(StringBuilder cc)
        {
            EventHandler method = null; //to jest delegat

            while (true)
            {
                if (cc.ToString() != "")
                {
                    if (method == null)
                    {
                        method = delegate
                        {
                            this.dodawanie(cc.ToString());
                        };
                    }
                    this.Invoke(method);
                    return;
                }
                Thread.Sleep(200);
            }
        }




        public void ustaw_poziom(string aa, string prawa_local, string lewa_local)
        {
            //MessageBox.Show("FUN ustaw_poziom:"+aa);
            poziom = aa;
            prawa = prawa_local;
            lewa = lewa_local;
            //MessageBox.Show("Wybrane miejsce: Hala" + hala+" "+regal+"-"+miejsce+"-"+poziom);
            miejsce_textBox1.Text = "" + hala + " " + regal + "-" + miejsce + "-" + poziom;

            if (etykieta_textbox.Text.Length > 0)
            {
                //if (BazaDanychExternal.SprawdzCzyIstniejePolaczenie())
                {



                    string zapytanie = "insert into historia_skanowania(wartosc,stamp,Serial,prac_id,operacja) values('" + poziom + "',sysdate(),'" + Wlasciwosci.SerialNumber + "','" + Wlasciwosci.id_Pracownika + "','" + Wlasciwosci.CurrentOperacja + "')";
                    BazaDanychExternal.DokonajUpdate(zapytanie);

                    if (hala.Replace(" ", "") == "" || regal.Replace(" ", "") == "" || miejsce.Replace(" ", "") == "" || poziom.Replace(" ", "") == "")
                    {
                        MessageBox.Show("Błąd formatowania miejsca paletowego. Proszę powtórzyć operację. [" + hala + " " + regal + " " + miejsce + " " + poziom);
                        return;
                    }


                    if (Czy_miejsce_zajete(hala, regal, miejsce, poziom) != "0")
                    {
                        MessageBox.Show("Miejsce w systemie zajęte. Paletę wstaw w inne");
                        miejsce_textBox1.Text = "";
                        return;
                    }

                    Realizuj_Zmiane_Miejsca(etykieta_textbox.Text, "ZM", "", Wlasciwosci.imie_nazwisko, hala, regal, miejsce, poziom, paleta_id);
                    miejsce_poprzednie.Text = "";




                    if (czy_buffor == false)
                    {
                        label_head.Text = " ZM " + zm_nr_global + " , " + zm_data_global;
                        //ilosc_etykiet.Text = "" + Count_zm(zm_nr_global);
                    }
                    ostatnio_hala = hala;
                    ostatnio_regal =regal;
                    ostatnio_miejsce = miejsce;
                    hala = "";
                    regal = "";
                    miejsce = "";
                    poziom = "";
                    miejsce_textBox1.Text = "";
                }
                //else
                //{
                //    MessageBox.Show("Brak zasiągu WIFI!!!");
                //}
            }
            else
            {
                MessageBox.Show("Pole etykiety jest puste");
            }
        }




        // 'Internal' - wyszukuje w bazie etykiete i wypełnia pola
        private void dodawanie(string ops)
        {
            //Skanowanie.Abort();
            Skaner.Przewij_Skanowanie();

            bool jestLiczba = Regex.IsMatch(ops, @"\d");

            if (ops.Substring(0, 2) == "MP")
            {
                if (etykieta_textbox.Text == "")
                {
                    MessageBox.Show("Zeskanuj najpierw etykietę towaru");
                    ZacznijSkanowanie();
                    return;
                }

                Regex regex = new Regex("-");
                string[] words = null;
                words = regex.Split(ops.Substring(3, ops.Length - 3));
                hala = words[0].ToString();
                if (words[1].ToString() != "0")// wyjątek jak miejsce jest 0
                {
                    regal = words[1].ToString().TrimStart(new Char[] { '0' });
                }
                else
                {
                    regal = words[1].ToString();
                }
                if (words[2].ToString() != "0")// wyjątek jak miejsce jest 0
                {
                    miejsce = words[2].ToString().TrimStart(new Char[] { '0' });
                }
                else
                {
                    miejsce = words[2].ToString();
                }

                string zapytanie = "";
                zapytanie = "SELECT m.poziom FROM miejsca m where  poziom!='' and hala='" + hala + "' and regal='" + regal + "'  and miejsce='" + miejsce + "' and m.widoczne=1  GROUP BY m.poziom order by poziom DESC;";
                object obj2 = BazaDanychExternal.Wyczytaj_Tabele(zapytanie);
                DataTable tabela = (DataTable)obj2;

                if (tabela.Rows.Count < 1)
                {
                    MessageBox.Show("Brak takiego miejsca w bazie. Spróbuj ponownie.");
                    ZacznijSkanowanie();
                    return;
                    
                }
                else if (tabela.Rows.Count == 1)
                {
                    this.ustaw_poziom(tabela.Rows[0]["poziom"].ToString(), "", "");
                }
                else
                {
                    Wybierz_Poziom_ZM qw = new Wybierz_Poziom_ZM(this, hala, regal, miejsce);
                    qw.ShowDialog();
                    qw.Close();                    
                }



               
                //qw.Close();

            }
            else
            {

                if (ops.Length <= 3)
                {
                    MessageBox.Show("To nie jest numer etykiety");
                    ZacznijSkanowanie();
                    return;
                }


                if (ops.Substring(0, 2) == "DS")
                {

                    //if (ops.Length < 23 && jestLiczba == true)  //bool isNumeric = int.TryParse("123", out n);

                    if (etykieta_textbox.Text.Length == 0)
                    {
                        /*
                        try
                        {
                            etykieta_textbox.Text = Convert.ToInt64(ops).ToString();
                            etykieta_ostatnia = Convert.ToInt64(ops).ToString();
                        }
                        catch
                        {
                            ZacznijSkanowanie();
                            return;
                        }
                        */
                        etykieta_textbox.Text = ops;
                        


                        if (BazaDanychExternal.SprawdzCzyIstniejePolaczenie() == false)
                        {
                            MessageBox.Show("Brak połączenia z bazą danych.Proszę o przemieszczenie się do miejsca z zasięgiem WI-FI.");
                            etykieta_textbox.Text = "";
                            ZacznijSkanowanie();
                            return;
                        }

                        if (Etykiety_dodane.Contains(etykieta_textbox.Text))
                        {
                            DialogResult dialogresult = MessageBox.Show("Ta etykieta byla juz skanowana.Czy chcesz ponowic operacje ?", "", MessageBoxButtons.YesNo, MessageBoxIcon.None, MessageBoxDefaultButton.Button1);
                            if (dialogresult == DialogResult.Yes)
                            {
                                DodajEtykiete(ops);
                            }
                            else
                            {
                                etykieta_textbox.Text = "";
                            }
                        }
                        else
                        {
                            DodajEtykiete(ops);
                        }

                    }
                    else
                    {
                        MessageBox.Show("Nie można wczytać nowej. Nie wczytano miejsca do poprzedniej " + ops);
                    }
                }

                else
                {
                    MessageBox.Show("To nie jest numer etykiety" + ops + "    ,      " + ops.Length);
                }
            }
            //ilosc_etykiet.Text = Etykiety_dodane.Count.ToString();
            ZacznijSkanowanie();
        }


        private void DodajEtykiete(string etykieta)
        {
        Poczatek_dodawania:
            if (BazaDanychExternal.SprawdzCzyIstniejePolaczenie() == true)
            {
                if (get_etykieta(etykieta) == true)
                {
                    etykieta_textbox.Text = etykieta;
                    BazaDanychExternal.DokonajUpdate("insert into zmiany_miejsca_niezrealizowane(etykieta) values('" + etykieta + "');");
                    id_zmiany_miejsca_niezrealizowane = BazaDanychExternal.Command.LastInsertedId.ToString();
                    // Etykiety_dodane.Add(etykieta_textbox.Text);
                }
            }
            else
            {
                DialogResult result3 = MessageBox.Show("Brak połączenia z bazą. Jeśli to towar z rampy wciśnij Yes,i ponów próbę połączenia się z bazą danych.",
                "Ponowne sprawdzanie?",
                MessageBoxButtons.YesNo,
                MessageBoxIcon.Question,
                MessageBoxDefaultButton.Button1);
                if (result3 == DialogResult.Yes)
                {
                    goto Poczatek_dodawania;
                }
                else
                {

                }

            }

        }




        


        private void czysc()
        {
            etykieta_textbox.Text = "";
        }



        private void timer1_Tick(object sender, EventArgs e)
        {
            this.Skanowanie.Abort();
            Skaner.Przewij_Skanowanie();

            //myParent.wczytwanieEtykiet = false;
            this.myParent.Show();
            timer1.Enabled = false;
            this.Close();


        }



        #region fullscreen
        private void fullscreenmode()
        {
            this.Width = Screen.PrimaryScreen.Bounds.Width;
            this.Height = Screen.PrimaryScreen.Bounds.Height;
            this.Left = 0;
            this.Top = 0;
        }
        #endregion

        private void button4_Click(object sender, EventArgs e)
        {
            //////if (BazaDanychExternal.SprawdzCzyIstniejePolaczenie())
            //////{
            //////    ustaw_poziom(poziom);
            //////}
            //////else
            //////{
            //////    MessageBox.Show("Brak zasiągu WIFI!!!");
            //////}
            if (ostatnio_hala == "" || etykieta_ostatnia == "" || ostatnio_regal=="")
            {
                return;
            }
            etykieta_textbox.Text = etykieta_ostatnia;
            hala = ostatnio_hala;
            regal = ostatnio_regal;
            miejsce = ostatnio_miejsce;
            Wybierz_Poziom_ZM qw = new Wybierz_Poziom_ZM(this, hala, regal, miejsce);
            qw.ShowDialog();
            qw.Close();


        }



        private void button2_Click(object sender, EventArgs e)
        {
            this.Skanowanie.Abort();
            Skaner.Przewij_Skanowanie();
            //Wolne_Miejsca dlg = new Wolne_Miejsca((object)this);
            //dlg.Show();
            //this.Hide();
        }

        private void button3_Click(object sender, EventArgs e)
        {
            etykieta_textbox.Text = "";

        }

        private void button5_Click(object sender, EventArgs e)
        {
            etykieta_textbox.Text = etykieta_ostatnia;
        }

        

        private void button7_Click(object sender, EventArgs e)
        {
            podpowadanie_miejsca(grupa_id, order_search);
        }

        private void podpowadanie_miejsca(string grupa_local, string order_search_local)
        {
            //MessageBox.Show("podpowadanie_miejsca");
            //MessageBox.Show(grupa_local);
            //MessageBox.Show(order_search_local);
            //MessageBox.Show(regal_ostatnio);
            string zapytanie2 = " SELECT mm.*, '1' as pierwsze FROM miejsca_wolne w left join miejsca_grupy m on m.miejsce_id=w.miejsce_id left join miejsca mm on mm.id=w.miejsce_id WHERE m.kody_grupy_id='" + grupa_local + "' and w.miejsce_id is not null and w.ts is null and mm.widoczne=1 and regal='" + regal_ostatnio + "' union SELECT mm.*, '2' as pierwsze FROM miejsca_wolne w left join miejsca_grupy m on m.miejsce_id=w.miejsce_id left join miejsca mm on mm.id=w.miejsce_id WHERE m.kody_grupy_id='" + grupa_local + "' and w.miejsce_id is not null and w.ts is null  and mm.widoczne=1 and regal!='" + regal_ostatnio + "' and regal!='" + regal_ostatnio + "' order by pierwsze,hala,regal,miejsce " + order_search_local  + " limit 1;  ";
            object obj2 = BazaDanychExternal.Wyczytaj_Tabele(zapytanie2);



            DataTable table2 = (DataTable)obj2;
            if (table2.Rows.Count < 1)  //sprawdza czy jest etykieta
            {
                //MessageBox.Show("Brak wysokosci w bazie w bazie danych !");
                //last_result_text.Text = "Brak wysokości :" + wysokosc_opis + "cm." + Environment.NewLine + "Zmień wysokość i Szuk. wolne";
                label4.Text = "Brak wolnych";
                //return;
            }
            else
            {
                //MessageBox.Show("Grupa2:" + grupa_local + ";wys:" + wysokosc_opis);
                //wysokosc_opis = table.Rows[0]["wysokosc"].ToString();
                //button6.Text = wysokosc_opis + "cm";
                last_result_text.Text = "";

                label4.Text = "Zalecane: " + Environment.NewLine + "" + table2.Rows[0]["regal"].ToString() + "-" + table2.Rows[0]["miejsce"].ToString() + "-" + table2.Rows[0]["poziom"].ToString();
                BazaDanychExternal.DokonajUpdate("update miejsca_wolne w set ts=now()  where miejsce_id='" + table2.Rows[0]["id"].ToString() + "' limit 1;");


            }

        }









        public void Realizuj_Zmiane_Miejsca(string etykieta,  string zm_type, string zm_date, string login, string hala, string regal, string miejsce, string poziom, string paleta_id)
        {
            //MessageBox.Show(etykieta[0] + ";" + etykieta[1]);
            //MessageBox.Show("Zmiana miejsca Gnatowice");
            typ_operacji = "ZM";

            if (regal == "POD") typ_operacji = "ZM_POD";

            if (etykieta.Substring(0, 2) == "DS")
            {
                DataTable temp = (DataTable)BazaDanychExternal.Wyczytaj_Tabele("select e.id,e.system_id,miejscep,paleta_id,regal,miejsce,poziom,ifnull(e.active,3) as active  from etykiety e left join miejsca m on e.miejscep=m.id where e.paleta_id=" + etykieta.Replace("DS", "") + " and (active=1 )"); //or active is null


                if (zm_nr_global == "") { zm_nr_global = (string)BazaDanychExternal.Wyczytaj_Jedna_Wartosc("select max(doc_nr)+1 FROM zmianym"); }
                if (temp.Rows.Count > 0)
                {
                    Wlasciwosci.system_id_id = temp.Rows[0][1].ToString();
                    //string nowemiejsce = miejscepaletowe2();
                    nowe_m = 0;
                    nowe_m = pobierz_id_miejsca(hala, regal, miejsce, poziom);
                    int licz = 0;


                    if (nowe_m != 0)
                    {
                        for (int t = 0; t < temp.Rows.Count; t++)
                        {

                            BazaDanychExternal.DokonajUpdate("insert into zmianym(typ,doc_nr,pracownik_id,data,etykieta,system_id,stare_m,nowe_m,doc_internal,stat,tszm) values(" +
                                "'ZM'," + zm_nr_global + "," + Wlasciwosci.id_Pracownika + ",sysdate()," + temp.Rows[t][0].ToString() + "," + temp.Rows[t][1].ToString() + "," + temp.Rows[t][2].ToString() + "," + nowe_m + ",3,1,sysdate())");

                            if (t == 0)
                            {

                                BazaDanychExternal.DokonajUpdate("insert into operacje(etykieta_id, doc_type, doc_nr, imie_nazwisko, typ_operacji,system_id,wozek,operac_id,ilosc) values('" + temp.Rows[t][0].ToString() + "','" + typ_operacji + "','" + zm_nr_global + "','" + Wlasciwosci.imie_nazwisko + "','" + typ_operacji + "','" + Wlasciwosci.system_id_id + "','" + Wlasciwosci.wozek + "','" + operac_id_global + "','1');");

                            }

                            if (temp.Rows[t]["active"].ToString() == "1")
                            {
                                licz += 1;
                            }

                        }
                        

                        BazaDanychExternal.DokonajUpdate("update etykiety set miejscep = " + nowe_m + " where paleta_id=" + etykieta.Replace("DS", "") + " and (active=1 or active is null) ");
                        licznik++;

                        if (id_zmiany_miejsca_niezrealizowane != "0")
                        {
                            BazaDanychExternal.DokonajUpdate("delete from zmiany_miejsca_niezrealizowane where id='" + id_zmiany_miejsca_niezrealizowane + "';");
                            id_zmiany_miejsca_niezrealizowane = "0";
                        }

                        string[] kk = new string[4];
                        kk = miejsce_aktualne(etykieta);
                        
                         //MessageBox.Show("Hala:"+kk[0]+";"+hala);
                         //MessageBox.Show("regal:" + kk[1] + ";" + regal);
                         //MessageBox.Show("miejsce:" + kk[2] + ";" + miejsce);
                         //MessageBox.Show("poziom:" + kk[3] + ";" + poziom);
                        if (kk[0] != hala || kk[1] != regal || kk[2] != miejsce || kk[3] != poziom)
                        {
                            licznik--; 

                            MessageBox.Show("Nie udało się zmienić miejsca !!. " + Environment.NewLine + "Obecnie jest: H:" + kk[0] + " " + kk[1] + "-" + kk[2] + "-" + kk[3]);
                            return;
                        }

                        licznik_label.Text = licznik.ToString();

                        label4.Text = "";
                        last_result_text.Text = "Zmieniono: " + Environment.NewLine + "" + temp.Rows[0]["regal"] + "-" + temp.Rows[0]["miejsce"] + "-" + temp.Rows[0]["poziom"] + " -> H:" + kk[0] + " " + kk[1] + "-" + kk[2] + "-" + kk[3];
                        etykieta_ostatnia = etykieta;
                        etykieta_textbox.Text = "";

                        ilosc_Count_PZ = Count_PZ(etykieta).ToString();
                        ilosc_Count_PZ_Wstawione = Count_PZ_Wstawione(etykieta).ToString();
                        ilosc_etykiet.Text = "" + ilosc_Count_PZ_Wstawione + " z " + ilosc_Count_PZ;


                    }
                    else
                    {
                        MessageBox.Show("Brak takiego miejsca w bazie. Spróbuj ponownie.");
                        return;
                    }


                }

                else
                {
                    MessageBox.Show("Brak takiej palety w systemie lub jest nieaktywna , spróbuj ponownie.");
                }
            }
            else
            {

                string zapytanie = "";
                zapytanie = "select IFNULL(e.miejscep,3) as miejscep ,IFNULL(e.active,3) as active,docout_type,docout_nr,e.id,m.hala,m.regal,m.miejsce,m.poziom,drobnicowe_miejsca from etykiety e left join docout d on e.docout_id=d.id left join miejsca m on e.miejscep=m.id left join systemy s on e.system_id=s.wartosc where (etykieta_klient='" + etykieta + "' or e.id='" + etykieta + "')  order by e.id desc limit 1";

                object obj2 = BazaDanychExternal.Wyczytaj_Tabele(zapytanie);
                if (obj2 == null)
                {
                    this.ZacznijSkanowanie();
                }
                DataTable table = (DataTable)obj2;
                nowe_m = pobierz_id_miejsca(hala, regal, miejsce, poziom);
                DateTime ts = DateTime.Now;

                if (table.Rows.Count < 1)  //sprawdza czy jest etykieta
                {
                    MessageBox.Show("Brak w bazie et: " + etykieta + ".");

                }
                else                     // czy jest wydana z systemu
                    if (table.Rows[0]["active"].ToString() == "0")
                    {
                        MessageBox.Show("Et" + etykieta + " wydana : " + table.Rows[0]["docout_type"].ToString() + " - " + table.Rows[0]["docout_nr"].ToString() + ". Dodana do offline");

                    }
                    else
                    {

                        if (nowe_m == 0)
                        {
                            MessageBox.Show("Brak w bazie miejsca: Hala: " + hala + " " + regal + "-" + miejsce + "-" + poziom + ". Spróbuj jeszze raz.");

                        }
                        else
                        {

                            if (nowe_m.ToString() != table.Rows[0]["miejscep"].ToString())
                            {
                                if (table.Rows[0]["drobnicowe_miejsca"].ToString() == "1")
                                {
                                    //MessageBox.Show("drobnicowe_miejsca");
                                    string zap2 = "";
                                    zap2 = "SELECT ifnull(cast(max(paleta_id) as char),'brak') as paleta_id FROM etykiety e  where active=1 and system_id=" + Wlasciwosci.system_id_id + " and miejscep=" + nowe_m + ";";
                                    string pal_id_loc = (string)BazaDanychExternal.Wyczytaj_Jedna_Wartosc(zap2);
                                    //MessageBox.Show(pal_id_loc);
                                    if (pal_id_loc != "brak")
                                    {
                                        //MessageBox.Show("update etykiety e set e.paleta_id=" + pal_id_loc + " where e.id=" + table.Rows[0]["id"] + ";");
                                        BazaDanychExternal.DokonajUpdate("update etykiety e set e.paleta_id=" + pal_id_loc + " where e.id=" + table.Rows[0]["id"] + ";");
                                    }

                                }

                                BazaDanychExternal.DokonajUpdate("insert into operacje(etykieta_id, doc_type, doc_nr, imie_nazwisko, typ_operacji,system_id,wozek,operac_id,ilosc) values('" + table.Rows[0]["id"] + "','" + zm_type + "','" + zm_nr_global + "','" + Wlasciwosci.imie_nazwisko + "','ZM','" + Wlasciwosci.system_id_id + "','" + Wlasciwosci.wozek + "','" + operac_id_global + "','1');");

                                BazaDanychExternal.DokonajUpdate("update etykiety e set e.miejscep=" + nowe_m + " where e.id=" + table.Rows[0]["id"] + ";");

                                BazaDanychExternal.DokonajUpdate("insert into zmianym(typ,doc_nr,pracownik_id,data,etykieta,stare_m,nowe_m,doc_internal,stat,system_id) values ('" + zm_type + "','" + zm_nr_global + "','" + Wlasciwosci.id_Pracownika + "','" + zm_date + "','" + table.Rows[0]["id"] + "','" + table.Rows[0]["miejscep"].ToString() + "','" + nowe_m + "','Z','1'," + Wlasciwosci.system_id_id + ");");



                                label2.Text = "Zmieniono: " + table.Rows[0]["regal"] + "-" + table.Rows[0]["miejsce"] + "-" + table.Rows[0]["poziom"] + " -> " + regal + "-" + miejsce + "-" + poziom;
                            }
                            else
                            {
                                label2.Text = "Jest:" + table.Rows[0]["regal"] + " - " + table.Rows[0]["miejsce"] + " - " + table.Rows[0]["poziom"];
                            }

                        }
                    }
            }
        }



    }
}