﻿using System;
using System.Linq;
using System.Collections.Generic;
using System.Text;
using MySql;
using MySql.Data;
using MySql.Data.MySqlClient;
using System.Data;
using System.Threading;
using System.Windows.Forms;

namespace Tarczyn__Magazyn
{

    public static class BazaDanychExternal
    {

        static MySqlConnection Connection;
        static public MySqlCommand Command;
        static MainMenu myParent = null;
        static MySqlTransaction Transaction = null;

        public static string polaczenie_mysql = "";




        public static void Inicjalizacja(string BazaDanych, string AdresIP, string Uzytkownik, string Haslo, MainMenu Parent)
        {
            myParent = Parent;
            //SprawdzCzyIstniejePolaczenie();
            //MessageBox.Show("Server=" + AdresIP + ";userid=" + Uzytkownik + ";password=" + Haslo + ";database=" + BazaDanych);
            polaczenie_mysql = "Server=" + AdresIP + ";userid=" + Uzytkownik + ";password=" + Haslo + ";database=" + BazaDanych + "";
            Connection = new MySqlConnection(polaczenie_mysql); //;CharSet=utf8
            //MessageBox.Show("A");
            //Connection.StateChange += ConnectionStateChanged;
            Command = new MySqlCommand("", Connection);
            //MessageBox.Show("B");
            SprawdzCzyIstniejePolaczenie();
            //MessageBox.Show("C");
        }

        public static void ChangeHost(string BazaDanych, string AdresIP, string Uzytkownik, string Haslo)
        {
            //Connection.StateChange -= ConnectionStateChanged;
            Connection = new MySqlConnection("server=" + AdresIP + ";userid=" + Uzytkownik + ";password=" + Haslo + ";database=" + BazaDanych);
            //Connection.StateChange += ConnectionStateChanged;
            Command = new MySqlCommand("", Connection);
            SprawdzCzyIstniejePolaczenie();
        }


        private static void ConnectionStateChanged(Object sender, StateChangeEventArgs e)
        {
            try
            {
                switch (e.CurrentState)
                {
                    case ConnectionState.Open:
                        myParent.MYSQLbar.Value = 1;
                        return;
                    case ConnectionState.Closed:
                        myParent.MYSQLbar.Value = 0;
                        return;
                }
            }
            catch
            {
                myParent.MYSQLbar.Value = 0;
                return;

            }

        }

        public static bool SprawdzCzyIstniejePolaczenie()
        {
            int x = 0;
            while (x < 4)
            {
                if (myParent.myWlan.get_Signal_int() < 2)
                {
                    //return false;
                    x++;
                    Thread.Sleep(4000);
                }
                else
                {
                    break;
                }
            }
            try
            {
                if (Connection.State == ConnectionState.Open)
                {
                    return true;
                }
                else
                {

                    while (x < 4)
                    {
                        try
                        {
                            Connection.Open();
                            return true;
                        }
                        catch
                        {
                            x++;
                            Thread.Sleep(2000);
                            //MessageBox.Show(ex.ToString());
                        }
                    }
                    return false;
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show(ex.ToString());
                return false;
            }
        }





        public static int DokonajUpdate(string Zapytanie)
        {

            int x = 0;
            while (x < 4)
            {
                if (SprawdzCzyIstniejePolaczenie() == true)
                {
                    /*
                    if (Wlasciwosci.debugowanie_skaner == 1)
                    {
                        try
                        {
                            Command.CommandText = Zapytanie;
                            return Command.ExecuteNonQuery();
                        }
                        catch (Exception ex)
                        {
                            Zapytanie.Replace("@", "");
                            Command.CommandText = "insert into logi_skaner(pracownik_id, zapytanie, komunikat,system_id) values('" + Wlasciwosci.id_Pracownika + "','" + Zapytanie + "','" + ex.ToString() + "','" + Wlasciwosci.system_id_id + "');";
                            Command.ExecuteNonQuery();
                            MessageBox.Show(ex.ToString());
                            return Command.ExecuteNonQuery();
                        }
                    }
                    else
                    {
                            
                    }
                    */

                    Command.CommandText = Zapytanie;
                    int result = 0;
                    try
                    {
                        result = Command.ExecuteNonQuery();
                    }
                    catch
                    {
                        x++;
                        continue;
                    }
                    return result;

                }

            }


            {
                //myParent.Komunikat("Nie mogłem wykonać zadania z powodu braku połączenia z MYSQL");
                MessageBox.Show(Wlasciwosci.Message[5]);
                return -11;
            }
        }

        public static object Wyczytaj_Jedna_Wartosc(string Zapytanie)
        {
            if (SprawdzCzyIstniejePolaczenie() == true)
            {

                if (Wlasciwosci.debugowanie_skaner == 1)
                {
                    try
                    {
                        Command.CommandText = Zapytanie;
                        if (Command.ExecuteScalar() == null)
                        {
                            return null;
                        }
                        if (Command.ExecuteScalar().ToString() != "")
                        {
                            return Command.ExecuteScalar().ToString();
                        }
                        else
                        {
                            return null;
                        }

                    }
                    catch (Exception ex)
                    {
                        Zapytanie.Replace("@", "");
                        Command.CommandText = "insert into logi_skaner(pracownik_id, zapytanie, komunikat,system_id) values('" + Wlasciwosci.id_Pracownika + "','" + Zapytanie + "','" + ex.ToString() + "','" + Wlasciwosci.system_id_id + "');";
                        Command.ExecuteNonQuery();
                        MessageBox.Show(ex.ToString());
                        return null;
                    }
                }
                else
                {
                    Command.CommandText = Zapytanie;
                    if (Command.ExecuteScalar() == null)
                    {
                        return null;
                    }
                    if (Command.ExecuteScalar().ToString() != "")
                    {
                        return Command.ExecuteScalar().ToString();
                    }
                    else
                    {
                        return null;
                    }
                }
            }
            else
            {
                //myParent.Komunikat("Nie mogłem wykonać zadania z powodu braku połączenia z MYSQL");
                MessageBox.Show(Wlasciwosci.Message[5]);
                return null;
            }

        }

        public static object Wyczytaj_Tabele(string Zapytanie)
        {
            if (SprawdzCzyIstniejePolaczenie() == true)
            {

                if (Wlasciwosci.debugowanie_skaner == 1)
                {
                    try
                    {
                        Command.CommandText = Zapytanie;
                        MySqlDataAdapter Adapter = new MySqlDataAdapter(Command);
                        DataTable Ex = new DataTable();
                        Adapter.Fill(Ex);
                        return Ex;

                    }
                    catch (Exception ex)
                    {
                        Zapytanie.Replace("@", "");
                        Command.CommandText = "insert into logi_skaner(pracownik_id, zapytanie, komunikat,system_id) values('" + Wlasciwosci.id_Pracownika + "','" + Zapytanie + "','" + ex.ToString() + "','" + Wlasciwosci.system_id_id + "');";
                        Command.ExecuteNonQuery();
                        MessageBox.Show(ex.ToString());
                        return null;
                    }
                }
                else
                {
                    Command.CommandText = Zapytanie;
                    MySqlDataAdapter Adapter = new MySqlDataAdapter(Command);
                    DataTable Ex = new DataTable();
                    Adapter.Fill(Ex);
                    return Ex;
                }

            }
            else
            {
                //MessageBox.Show(Wlasciwosci.Message[5]);
                return null;
            }

        }
        public static void Zamknij_polaczenie()
        {
            try
            {
                Connection.Dispose();
                Connection.Close();
                Command = null;
            }
            catch
            {

            }
        }

        public static void Wyczysc()
        {
            try
            {
                Command.Dispose();
                Connection.Dispose();
            }
            catch
            {

            }
        }



        public static void Inicjuj_Transakcje()
        {
            Transaction = null;
            Transaction = Connection.BeginTransaction();

        }

        public static void Zacznij_Transakcje()
        {

            Command.Transaction = Transaction;


        }
        public static void Przerwij_Transakcje()
        {
            Transaction.Rollback();
            //Command.Transaction = Transaction;
            //Connection.BeginTransaction();
        }

        public static void Zakoncz_Transakcje()
        {
            Transaction.Commit();
            //Command.Transaction = Transaction;
            //Connection.BeginTransaction();
        }
    }

}
