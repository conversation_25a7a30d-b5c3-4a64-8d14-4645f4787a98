﻿using System;
using System.Linq;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using MySql.Data.MySqlClient;
using System.Text.RegularExpressions;
using System.Threading;

namespace Tarczyn__Magazyn
{
    public partial class TworzenieDLnew : Form
    {
        ActionMenu myParent = null;



        List<string> _dl_wybor = new List<string>();
        int[] _dl = new int[100];

        bool czy_buffor = false;

        int nr_dl = 0;
        private Thread Skanowanie;

        //string skaner = "";
        string nr_etykiety = "";
        string nrsap = "";
        //string sap_gora = "";
        //string sap_gora_czesc2 = "";
        //string kod = "";
        string podkod = "";

        string regal = "";
        string miejsce = "";
        //string nrsap_baza = "";

        string delivery_id_global = "";
        string docout_type_global = "";

        string docout_nr_global = "";
        string delivery_nr_global = "";

        string rampa_nr = "";


        string operac_id_global = "";
        string zgodnosc = "";






        //public MySqlConnection ok = null;

        public TworzenieDLnew(ActionMenu MyParent)
        {
            InitializeComponent();
            this.myParent = MyParent;
            wms_wybrany.Text = Wlasciwosci.system_id;
            imie_nazwisk_label.Text = Wlasciwosci.wozek + ":" + Wlasciwosci.imie_nazwisko;
            //ok = myParent.conn;
            wyszukaj_dl();
            textBox1.Visible = false;
            button2.Visible = false;
            button3.Visible = false;
            this.ZacznijSkanowanie();
            Wlasciwosci.CurrentOperacja = "2";

            //skanuj();
        }

        private void wyszukaj_dl()
        {
            string zapytanie = "SELECT d.delivery_nr,sum(1) as palet FROM etykiety e left join delivery_et de on de.etykieta_id=e.id left join delivery d on de.delivery_id=d.id WHERE d.dl_status<4 and e.system_id=" + Wlasciwosci.system_id_id + " AND d.delivery_nr!='' GROUP BY d.delivery_nr order by d.id desc limit 30";
            object obj2 = BazaDanychExternal.Wyczytaj_Tabele(zapytanie);
            DataTable tabela = (DataTable)obj2;

            for (byte k = 0; k < tabela.Rows.Count; k++)
            {
                _dl_wybor.Add(tabela.Rows[k]["delivery_nr"].ToString() + " | " + tabela.Rows[k]["palet"].ToString());
                _dl[k] = Convert.ToInt32(tabela.Rows[k]["delivery_nr"]);
            }
            if (_dl_wybor.Count == 0)
            {
                MessageBox.Show("Brak dokumentów DL ");
            }
            else
            {
                comboBox1.Visible = true;
                BindingSource bs = new BindingSource();
                bs.DataSource = _dl_wybor;
                comboBox1.DataSource = bs;
            }
        }





        private void button1_Click(object sender, EventArgs e)
        {
            Skaner.Przewij_Skanowanie();
            powrot.Visible = false;
            comboBox1.Enabled = false;
            textBox1.Visible = true;
            button2.Visible = true;
            button3.Visible = true;

            nr_dl = _dl[comboBox1.SelectedIndex];

            zgodnosc = (string)BazaDanychExternal.Wyczytaj_Jedna_Wartosc("select zgodnosc from delivery d  where d.delivery_nr='" + nr_dl + "'    "); //and e.dataprod='" + tabela.Rows[w]["dataprod"].ToString() + "' and status_prism='" + tabela.Rows[w]["status_prism"].ToString() + "' and blloc='" + tabela.Rows[w]["blloc"].ToString() + "'


            if (nr_dl == 0 || nr_dl.ToString() == "")
            {

                MessageBox.Show("Wybierz DL ");
                return;
            }

            string zapytanie = "SELECT last FROM docnumber d WHERE d.name='operacja_id';";

            operac_id_global = (string)BazaDanychExternal.Wyczytaj_Jedna_Wartosc(zapytanie);

            string zapyt = "update docnumber d set last=last+1 WHERE d.name='operacja_id'; ";
            BazaDanychExternal.Wyczytaj_Jedna_Wartosc(zapyt);


            //BazaDanychExternal.DokonajUpdate("update etykiety set delivery_nr=null where active=1 and delivery_nr=" + nr_dl);
            //ilosc_etykiet.Text = "0";
            ilosc_etykiet.Text = delivery_etykiety(_dl[comboBox1.SelectedIndex].ToString()) + "/" + wczytane_licznik(_dl[comboBox1.SelectedIndex].ToString());
            this.button1.Click -= new EventHandler(this.button1_Click);
            this.button1.Click += new EventHandler(this.Zakoncz_Skanowanie);
            this.button1.Text = "Zakończ";
            this.ZacznijSkanowanie();

            
                BazaDanychExternal.DokonajUpdate("insert into operacje(etykieta_id, doc_type, doc_nr, imie_nazwisko, typ_operacji,system_id,wozek,operac_id,ilosc) values('0','DL','" + nr_dl.ToString() + "','" + Wlasciwosci.imie_nazwisko + "','DL_SZ','" + Wlasciwosci.system_id_id + "','" + Wlasciwosci.wozek + "','" + operac_id_global + "','0');");
            
            

            
        }


        private void Zakoncz_Skanowanie(object sender, EventArgs e)
        {
            comboBox1.Enabled = true;
            powrot.Visible = true;
            textBox1.Visible = false;
            button2.Visible = false;
            button3.Visible = false;
            this.Skanowanie.Abort();
            Skaner.Przewij_Skanowanie();
            this.button1.Click -= new EventHandler(this.Zakoncz_Skanowanie);
            this.button1.Click += new EventHandler(this.button1_Click);
            this.button1.Text = "Wybierz";
            //MessageBox.Show("Zakończyłem tworzenie dokumentu DL:"+nr_dl);
            //this.myParent.Show();
            //base.Close();
        }

        

        private void powrot_Click(object sender, EventArgs e)
        {

            powrot_sprawdz();

            this.Skanowanie.Abort();
            Skaner.Przewij_Skanowanie();
            this.myParent.Show();
            this.Close();
        }

        private void powrot_sprawdz()
        {
            
        }



        private void button1_Anuluj(object sender, EventArgs e)
        {

        }
        private void ZacznijSkanowanie()
        {
            StringBuilder login = new StringBuilder();
            Skaner.UstawTryb_String(login);
            Skaner.Zacznij_Skanowanie();
            this.Skanowanie = new Thread(() => this.CheckString(login));
            this.Skanowanie.IsBackground = true;
            this.Skanowanie.Start();
        }

        private void CheckString(StringBuilder cc)
        {
            EventHandler method = null;
            while (true)
            {
                if (cc.ToString() != "")
                {
                    if (method == null)
                    {
                        method = delegate
                        {
                            //this.vvv();                            
                            this.dodawanie(cc.ToString());
                        };
                        //method = () => this.dodawanie(cc.ToString());
                    }
                    this.Invoke(method);
                    return;
                }
                Thread.Sleep(200);
            }
        }


        private void dodawanie(string ops)
        {

            Skaner.Przewij_Skanowanie();
            //MessageBox.Show("1");

            if (!BazaDanychExternal.SprawdzCzyIstniejePolaczenie())
            {
                MessageBox.Show("Brak połączenia z bazą danych , spróbuj ponownie.");

            }
            else
            {
                

                    if (ops.Substring(0, 2) == "DL")
                    {
                        string a = ops.Trim(new Char[] { 'D', 'L' });
                        for (byte k = 0; k < _dl.Length; k++)
                        {
                            if (_dl[k].ToString() == a)
                            {
                                comboBox1.SelectedIndex = k;
                            }
                        }
                    }
                    else
                        if (ops.Length > 2 && ops.Length < 22)
                        {
                            //MessageBox.Show("2");
                            textBox1.Text = ops;//Convert.ToInt64(ops).ToString();
                            Wlasciwosci.CurrentOperacja = "2";
                            //MessageBox.Show("3");
                            get_dl_items_group_szykowanie(_dl[comboBox1.SelectedIndex].ToString(), textBox1.Text);
                            //MessageBox.Show("10");
                        }
                        else
                        {
                            MessageBox.Show("To nie jest etykieta systemu");
                            ZacznijSkanowanie();
                        }

                

                



            }
            //ZacznijSkanowanie();
        }


        public static string Ile_Pozostalo_szykowanie(string delivery_id, string docout_type, string docout_nr)
        {
            string zapytanie = "SELECT count(1)-(select count(1) from operacje o  where doc_type='DL' and doc_nr=delivery_nr and typ_operacji='DL_ZAL') as dl_zal FROM etykiety e left join docout d on e.docout_id=d.id left join delivery_et de on de.etykieta_id=e.id left join delivery dd on de.delivery_id=dd.id   where (dd.id='" + delivery_id + "' or (d.docout_type='" + docout_type + "' and d.docout_nr='" + docout_nr + "')) and  e.system_id='" + Wlasciwosci.system_id_id + "' ;";
            string aa = (string)BazaDanychExternal.Wyczytaj_Jedna_Wartosc(zapytanie);
            return aa;
        }

        public static string Progres_szykowanie1(string delivery_id, string docout_type, string docout_nr)
        {
            string zapytanie = "SELECT count(1) as dl_zal FROM etykiety e left join docout d on e.docout_id=d.id left join delivery_et de on de.etykieta_id=e.id left join delivery dd on de.delivery_id=dd.id   where (dd.id='" + delivery_id + "' or (d.docout_type='" + docout_type + "' and d.docout_nr='" + docout_nr + "')) and  e.system_id='" + Wlasciwosci.system_id_id + "' ;";
            string aa = (string)BazaDanychExternal.Wyczytaj_Jedna_Wartosc(zapytanie);
            return aa;
        }
        public static string Progres_szykowanie2(string delivery_id, string docout_type, string docout_nr)
        {
            string zapytanie = "SELECT (select count(1) from operacje o  where doc_type='DL' and doc_nr=delivery_nr and typ_operacji='DL_ZAL') as dl_zal FROM etykiety e left join docout d on e.docout_id=d.id left join delivery_et de on de.etykieta_id=e.id left join delivery dd on de.delivery_id=dd.id   where (dd.id='" + delivery_id + "' or (d.docout_type='" + docout_type + "' and d.docout_nr='" + docout_nr + "')) and  e.system_id='" + Wlasciwosci.system_id_id + "' limit 1;";
            string aa = (string)BazaDanychExternal.Wyczytaj_Jedna_Wartosc(zapytanie);
            return aa;
        }

        private static string[] get_dokument_szykowanie(string etykieta) //pobiera
        {
            string zapytanie = "select d.docout_type,d.docout_nr,nr_dl as delivery_id,nr_dl as delivery_nr from etykiety e left join docout d on e.docout_id=d.id left join dlcollect dd on e.id=dd.nr_et  where e.id=" + etykieta + "  limit 1";
            object obj2 = BazaDanychExternal.Wyczytaj_Tabele(zapytanie);
            DataTable tabela = (DataTable)obj2;
            string[] bb = new string[4];
            bb[0] = "" + tabela.Rows[0]["docout_type"].ToString();
            bb[1] = tabela.Rows[0]["docout_nr"].ToString();
            bb[2] = tabela.Rows[0]["delivery_id"].ToString();
            bb[3] = "" + tabela.Rows[0]["delivery_nr"].ToString();
            return bb;
        }

        public static string Sprawdz_dokument_etykieta(string etykieta, string delivery_id, string docout_type, string docout_nr)
        {
            string zapytanie = "SELECT count(1) FROM etykiety e left join docout d on e.docin_id=d.id left join dlcollect dd on e.id=dd.nr_et where (dd.nr_dl='" + delivery_id + "' or (d.docout_type='" + docout_type + "' and d.docout_nr='" + docout_nr + "'))  and e.id='" + etykieta + "' and e.system_id='" + Wlasciwosci.system_id_id + "'  ;";
            string aa = (string)BazaDanychExternal.Wyczytaj_Jedna_Wartosc(zapytanie);
            return aa;
        }

        public static string Sprawdz_dokument_etykieta_22222(string etykieta, string delivery_id, string docout_type, string docout_nr)
        {
            string zapytanie = "SELECT count(1) FROM etykiety e left join docout d on e.docin_id=d.id left join dlcollect dd on e.id=dd.nr_et where (dd.nr_dl='" + delivery_id + "' or (d.docout_type='" + docout_type + "' and d.docout_nr='" + docout_nr + "'))  and e.id='" + etykieta + "' and e.system_id='" + Wlasciwosci.system_id_id + "'  ;";
            string aa = (string)BazaDanychExternal.Wyczytaj_Jedna_Wartosc(zapytanie);
            return aa;
        }

        public static string Sprawdz_Czy_Mozna(string etykieta)
        {
            string zapytanie = "";
            zapytanie = "SELECT count(1) FROM etykiety e left join operacje o on e.id=o.etykieta_id where (e.id='" + etykieta + "' or  e.etykieta_klient='" + etykieta + "') and typ_operacji!='DL_ZAL' order by e.id desc,o.id desc limit 1 ;";
            string aa = (string)BazaDanychExternal.Wyczytaj_Jedna_Wartosc(zapytanie);
            return aa;
        }

        public static string CzyWczytana_Szyk(string etykieta_klient)
        {
            string zapytanie = "SELECT count(1) FROM etykiety e where (e.etykieta_klient='" + etykieta_klient + "' or e.id='" + etykieta_klient + "') and (active=1 or active is null) and system_id='" + Wlasciwosci.system_id_id + "'  ;";
            string aa = (string)BazaDanychExternal.Wyczytaj_Jedna_Wartosc(zapytanie);
            return aa;
        }

        public void get_dl_items_group_szykowanie(string nr_dl, string etykieta)
        {
            Skaner.Przewij_Skanowanie();

            //MessageBox.Show("1");
            string zapytanie = ""; 
            //if (zgodnosc == "1")
            //{
                //zapytanie = "select k.kod,DATE_FORMAT(e.dataprod,'%Y-%m-%d') as dataprod,e.blloc ,e.status_prism,sum(ilosc) as ilosc_delivery,e.magazyn,e.id from etykiety e left join delivery_et de on de.etykieta_id=e.id left join delivery d on de.delivery_id=d.id left join kody k on e.kod_id=k.id where d.delivery_nr=" + nr_dl + " group by k.kod order by k.kod,e.status_prism,e.dataprod,e.blloc;"; //,e.status_prism,e.dataprod,e.blloc
                //zapytanie = "select de.etykieta_id,k.kod,sum(de.ilosc_zamawiana) as ilosc_zamawiana,e.ilosc,e.active from etykiety e left join delivery_et de on de.etykieta_id=e.id left join delivery d on de.delivery_id=d.id left join kody k on e.kod_id=k.id where d.delivery_nr='" + nr_dl + "' and de.etykieta_id='" + etykieta + "' group by de.etykieta_id";


            //zapytanie = "select e.system_id,magazyn,e.active,miejscep,kod_id,status_id,status_id2,stat,paleta_id,kartony,DATE_FORMAT(dataprod,'%d-%m-%Y') as dataprod,data_waznosci,cast(TRIM(TRAILING '.' FROM TRIM(TRAILING '0' from e.ilosc)) as char) as ilosc,ts,status,blloc,akcja_id,status_prism,lot,sscc,gtin,przeznaczenie_id,nretykiety,docin_id,docout_id,e.delivery_id,listcontrol_id,k.kod,k.ilosc_w_opakowaniu,d.nr_dl,e.id,cast(TRIM(TRAILING '.' FROM TRIM(TRAILING '0' from sum(de.ilosc_zamawiana))) as char) as ilosc_zamawiana,dd.docout_type,dd.docout_nr, s.nazwa,s.funkcja_stat,e.magazyn,IFNULL(delivery_nr,0) as delivery_nr,IFNULL((select nr_dl from dlcollect d where e.id=d.nr_et limit 1),0) as dl_skan    from  etykiety e left join dlcollect d on e.id=d.nr_et left join docout dd on dd.id=e.docout_id left join kody k on e.kod_id=k.id left join status_system s on e.status_id=s.id left join delivery_et de on de.etykieta_id=e.id left join delivery ddd on de.delivery_id=ddd.id  where (e.id='" + etykieta + "' or e.etykieta_klient='" + etykieta + "') and ddd.delivery_nr='" + nr_dl + "' order by e.id desc limit 1;";


                if (etykieta.Substring(0, 2) == "DS")
                {
                    zapytanie = "select e.system_id,magazyn,e.active,miejscep,kod_id,status_id,status_id2,stat,paleta_id,kartony,DATE_FORMAT(dataprod,'%d-%m-%Y') as dataprod,data_waznosci,cast(TRIM(TRAILING '.' FROM TRIM(TRAILING '0' from e.ilosc)) as char) as ilosc,ts,status,blloc,akcja_id,status_prism,lot,sscc,gtin,przeznaczenie_id,nretykiety,docin_id,docout_id,e.delivery_id,listcontrol_id,k.kod,k.ilosc_w_opakowaniu,d.nr_dl,e.id,cast(TRIM(TRAILING '.' FROM TRIM(TRAILING '0' from sum(de.ilosc_zamawiana))) as char) as ilosc_zamawiana,dd.docout_type,dd.docout_nr, s.nazwa,s.funkcja_stat,e.magazyn,IFNULL(delivery_nr,0) as delivery_nr,IFNULL((select nr_dl from dlcollect d where e.id=d.nr_et limit 1),0) as dl_skan,k.kod_nazwa    from  etykiety e left join dlcollect d on e.id=d.nr_et left join docout dd on dd.id=e.docout_id left join kody k on e.kod_id=k.id left join status_system s on e.status_id=s.id left join delivery_et de on de.etykieta_id=e.id left join delivery ddd on de.delivery_id=ddd.id  where  (e.paleta_id='" + etykieta.Replace("DS", "") + "') and ddd.delivery_nr='" + nr_dl + "'  and e.active=1 order by e.id desc limit 1;";
                }
                else
                {
                    zapytanie = "select e.system_id,magazyn,e.active,miejscep,kod_id,status_id,status_id2,stat,paleta_id,kartony,DATE_FORMAT(dataprod,'%d-%m-%Y') as dataprod,data_waznosci,cast(TRIM(TRAILING '.' FROM TRIM(TRAILING '0' from e.ilosc)) as char) as ilosc,ts,status,blloc,akcja_id,status_prism,lot,sscc,gtin,przeznaczenie_id,nretykiety,docin_id,docout_id,e.delivery_id,listcontrol_id,k.kod,k.ilosc_w_opakowaniu,d.nr_dl,e.id,cast(TRIM(TRAILING '.' FROM TRIM(TRAILING '0' from sum(de.ilosc_zamawiana))) as char) as ilosc_zamawiana,dd.docout_type,dd.docout_nr, s.nazwa,s.funkcja_stat,e.magazyn,IFNULL(delivery_nr,0) as delivery_nr,IFNULL((select nr_dl from dlcollect d where e.id=d.nr_et limit 1),0) as dl_skan ,k.kod_nazwa   from  etykiety e left join dlcollect d on e.id=d.nr_et left join docout dd on dd.id=e.docout_id left join kody k on e.kod_id=k.id left join status_system s on e.status_id=s.id left join delivery_et de on de.etykieta_id=e.id left join delivery ddd on de.delivery_id=ddd.id  where (e.id='" + etykieta + "' or e.etykieta_klient='" + etykieta + "') and ddd.delivery_nr='" + nr_dl + "' order by e.id desc ";
                }
            //}

            object obj2;
            obj2 = BazaDanychExternal.Wyczytaj_Tabele(zapytanie);
            DataTable tabela = (DataTable)obj2;

            if (tabela == null)
            {
                ZacznijSkanowanie();
                return;
            }

            //MessageBox.Show("2");
            if ((tabela).Rows.Count > 0)
            {
                //MessageBox.Show("3");
                string aa = "";
                aa = (string)BazaDanychExternal.Wyczytaj_Jedna_Wartosc("select sum(ilosc) as ilosc from dlcollect d left join etykiety e on d.nr_et=e.id left join kody k on k.id=e.kod_id where d.nr_dl='" + nr_dl + "'  and d.nr_et='" + tabela.Rows[0]["id"].ToString() + "'  limit 1"); 
                //MessageBox.Show("4");

                if (aa == "") aa = "0";
                //MessageBox.Show(aa);
                double ilosc_dlcollect=0;
                double ilosc_potrzebna=0;
                try
                {
                    ilosc_dlcollect = Math.Round(Convert.ToDouble(aa), 3);
                    //MessageBox.Show(ilosc_dlcollect.ToString());
                    //MessageBox.Show("ilosc_zamawiana:" + tabela.Rows[0]["ilosc_zamawiana"].ToString());
                    //MessageBox.Show("ilosc_dlcollect:" + ilosc_dlcollect);

                    ilosc_potrzebna = Math.Round(Convert.ToDouble(tabela.Rows[0]["ilosc_zamawiana"].ToString()), 3) - ilosc_dlcollect;
                    //MessageBox.Show(ilosc_potrzebna.ToString());

                }
                catch (Exception e)
                {
                    MessageBox.Show(e.Message);
                }

                
                if (ilosc_potrzebna != 0)
                {
                    //MessageBox.Show("5");
                    if (tabela.Rows[0]["active"].ToString() == "0")
                    {
                        MessageBox.Show("Etykieta " + etykieta + " jest nieaktywna " + tabela.Rows[0]["docout_type"].ToString() + " / " + tabela.Rows[0]["docout_nr"].ToString());
                        textBox1.Text = "";
                        return;
                    }
                    //MessageBox.Show("6");
                    if (tabela.Rows[0]["funkcja_stat"].ToString() == "blokada_wyd")
                    {
                        MessageBox.Show("Etykieta " + etykieta + " ma status " + tabela.Rows[0]["nazwa"].ToString());
                        textBox1.Text = "";
                        return;
                    }
                    //MessageBox.Show("7");


                    if (tabela.Rows[0]["nr_dl"].ToString() == nr_dl)
                    {
                        MessageBox.Show("Etykieta " + etykieta + " jest już wczytana na tą DL ");
                        textBox1.Text = "";
                        return;
                    }
                    //MessageBox.Show("8");
                    if ((Convert.ToInt32(tabela.Rows[0]["dl_skan"].ToString()) > 0))  //tabela2.Rows[0]["delivery_nr"].ToString() != nr_dl && (Convert.ToInt32(tabela2.Rows[0]["delivery_nr"].ToString()) > 0 ||
                    {
                        MessageBox.Show("Etykiety nie można wczytać. " + tabela.Rows[0]["id"].ToString() + " jest już zarezerwowana na DL " + tabela.Rows[0]["delivery_nr"].ToString() + " lub " + tabela.Rows[0]["dl_skan"].ToString());
                        textBox1.Text = "";
                        ZacznijSkanowanie();
                        return;
                    }
                    //MessageBox.Show("9");

                    // co jak już nie ma na etykiecie tej ilości??


                    if (Math.Round(Convert.ToDouble(tabela.Rows[0]["ilosc_zamawiana"].ToString()), 3) > Math.Round(Convert.ToDouble(tabela.Rows[0]["ilosc"].ToString()), 3))
                    {
                        //MessageBox.Show("10");
                        MessageBox.Show("Na tej etykiecie nie ma ilości zamawianej. Jest tylko" + tabela.Rows[0]["ilosc"].ToString());
                        textBox1.Text = "";
                        ZacznijSkanowanie();
                        return;

                    }
                    //MessageBox.Show("11");

                    if (tabela.Rows[0]["ilosc_zamawiana"].ToString() != tabela.Rows[0]["ilosc"].ToString())
                    {
                        //MessageBox.Show("12");
                        // wyświetl okienko do przepakowania
                        double nowa_ilosc = 0;
                        SztukiOpakowania XA = new SztukiOpakowania(tabela.Rows[0]["ilosc"].ToString(), tabela.Rows[0]["ilosc_zamawiana"].ToString(), tabela.Rows[0]["ilosc_w_opakowaniu"].ToString(), tabela.Rows[0]["kod"].ToString(), tabela.Rows[0]["kod_nazwa"].ToString());

                        if (XA.ShowDialog() == DialogResult.OK)
                        {
                            nowa_ilosc = XA.ilosc_wpisana;
                            if (XA.komunikat != "")
                            {
                                MessageBox.Show(XA.komunikat);
                                ZacznijSkanowanie();
                                return;
                            }


                            long nowa_etykieta1 = 0;
                            //MessageBox.Show("GGG:" + GGG.Rows[0][0].ToString());
                            //MessageBox.Show("13");

                            BazaDanychExternal.DokonajUpdate("insert into etykiety(system_id,magazyn,active,miejscep,kod_id,status_id,status_id2,stat,paleta_id,kartony,dataprod,data_waznosci,ilosc,ts,status,blloc,akcja_id,status_prism,lot,sscc,gtin,przeznaczenie_id,nretykiety,docin_id,delivery_id,listcontrol_id,etykieta_klient)" +
                                        " values(" +
                                        sprawdz_czy_null(tabela.Rows[0][0].ToString()) + "," +
                                        sprawdz_czy_null(tabela.Rows[0][1].ToString()) + "," +
                                        sprawdz_czy_null(tabela.Rows[0][2].ToString()) + "," +
                                        sprawdz_czy_null(tabela.Rows[0][3].ToString()) + "," +
                                        sprawdz_czy_null(tabela.Rows[0][4].ToString()) + "," +
                                        sprawdz_czy_null(tabela.Rows[0][5].ToString()) + "," +
                                        sprawdz_czy_null(tabela.Rows[0][6].ToString()) + "," +
                                        sprawdz_czy_null(tabela.Rows[0][7].ToString()) + "," +
                                        sprawdz_czy_null(tabela.Rows[0][8].ToString()) + "," +
                                        sprawdz_czy_null(tabela.Rows[0][9].ToString()) + "," +
                                        DajDate(sprawdz_czy_null(tabela.Rows[0][10].ToString())) + "," + //DATA
                                        sprawdz_czy_null(tabela.Rows[0][11].ToString()) + "," +
                                        sprawdz_czy_null(nowa_ilosc.ToString()) + "," +
                                        "NOW()," +//NOW()
                                        sprawdz_czy_null(tabela.Rows[0][14].ToString()) + "," +
                                        sprawdz_czy_null(tabela.Rows[0][15].ToString()) + "," +
                                        sprawdz_czy_null(tabela.Rows[0][16].ToString()) + "," +
                                        sprawdz_czy_null(tabela.Rows[0][17].ToString()) + "," +
                                        sprawdz_czy_null(tabela.Rows[0][18].ToString()) + "," +
                                        sprawdz_czy_null(tabela.Rows[0][19].ToString()) + "," +
                                        sprawdz_czy_null(tabela.Rows[0][20].ToString()) + "," +
                                //sprawdz_czy_null(GGG.Rows[0][21].ToString()) + "," +
                                        sprawdz_czy_null(tabela.Rows[0][21].ToString()) + "," +
                                        sprawdz_czy_null(tabela.Rows[0]["nretykiety"].ToString()) + "," +
                                        sprawdz_czy_null(tabela.Rows[0][23].ToString()) + "," +
                                        nr_dl + "," +
                                        sprawdz_czy_null(tabela.Rows[0][24].ToString()) + "," +
                                        sprawdz_czy_null(tabela.Rows[0][25].ToString())
                                         + ")");

                            nowa_etykieta1 = BazaDanychExternal.Command.LastInsertedId;
                            //MessageBox.Show("nowa_etykieta1:" + nowa_etykieta1);
                            try
                            {
                                //MessageBox.Show("14");
                                BazaDanychExternal.DokonajUpdate("update etykiety set ilosc=" + (Convert.ToDouble(tabela.Rows[0][12].ToString()) - Convert.ToDouble(nowa_ilosc)).ToString() + " where id=" + tabela.Rows[0]["id"].ToString());
                                //MessageBox.Show("15");
                                BazaDanychExternal.DokonajUpdate("delete from delivery_et where etykieta_id=" + tabela.Rows[0]["id"].ToString() + " and delivery_id=" + nr_dl);
                                //MessageBox.Show("16");
                                BazaDanychExternal.DokonajUpdate("insert into delivery_et(etykieta_id,delivery_id,ilosc_zamawiana) values ('" + nowa_etykieta1 + "','" + nr_dl + "','" + tabela.Rows[0]["ilosc_zamawiana"].ToString() + "')");
                                //MessageBox.Show("17");
                                BazaDanychExternal.DokonajUpdate("insert into dlcollect (nr_dl,nr_et,system_id) VALUES('" + nr_dl + "', '" + nowa_etykieta1 + "','" + Wlasciwosci.system_id_id + "')");
                            }
                            catch (Exception e)
                            {
                                MessageBox.Show(e.Message);
                            }

                            BazaDanychExternal.DokonajUpdate("insert into operacje(etykieta_id, doc_type, doc_nr, imie_nazwisko, typ_operacji,system_id,wozek,operac_id,ilosc) values('" + nowa_etykieta1.ToString() + "','DL','" + nr_dl + "','" + Wlasciwosci.imie_nazwisko + "','DL_SZYK','" + Wlasciwosci.system_id_id + "','" + Wlasciwosci.wozek + "','" + operac_id_global + "','1');");


                        }
                        else
                        {
                            ZacznijSkanowanie();
                            return;
                        }

                    }
                    else
                    {
                        //MessageBox.Show("18");
                        //jeśli ilości równe
                        BazaDanychExternal.DokonajUpdate("insert into dlcollect (nr_dl,nr_et,system_id) VALUES('" + nr_dl + "', '" + tabela.Rows[0]["id"].ToString() + "','" + Wlasciwosci.system_id_id + "')");
                        BazaDanychExternal.DokonajUpdate("insert into operacje(etykieta_id, doc_type, doc_nr, imie_nazwisko, typ_operacji,system_id,wozek,operac_id,ilosc) values('" + tabela.Rows[0]["id"].ToString() + "','DL','" + nr_dl + "','" + Wlasciwosci.imie_nazwisko + "','DL_SZYK','" + Wlasciwosci.system_id_id + "','" + Wlasciwosci.wozek + "','" + operac_id_global + "','1');");

                    }



                }
                else
                {
                    MessageBox.Show("Pełna ilość z etykiety jest wczytana");
                    ZacznijSkanowanie();
                    return;
                }



            }
                else
                {
                    MessageBox.Show("Nie istnieje w systemie etykieta " + etykieta + ".");
                        textBox1.Text = "";
                        ZacznijSkanowanie();
                        return;
                }


            

    /*

            //MessageBox.Show("4");
            int not_insert = 0;
            int ilosc_pozycji_dl = 0;

            if ((tabela).Rows.Count > 0)
            {



                for (int w = 0; w < tabela.Rows.Count; w++)
                {
                    ilosc_pozycji_dl++;
                    //MessageBox.Show("5");
                    //MessageBox.Show(tabela.Rows[w]["kod"].ToString());
                    //MessageBox.Show(tabela.Rows[w]["dataprod"].ToString());
                    //MessageBox.Show(tabela.Rows[w]["lot"].ToString());
                    //MessageBox.Show(tabela.Rows[w]["blloc"].ToString());
                    //MessageBox.Show("select sum(ilosc) as ilosc from dlcollect d left join etykiety ee on d.nr_et=ee.id  where nr_dl='" + nr_dl + "'  and kod='" + tabela.Rows[w]["kod"].ToString() + "' and dataprod='" + tabela.Rows[w]["dataprod"].ToString() + "' and lot='" + tabela.Rows[w]["lot"].ToString() + "' and blloc='" + tabela.Rows[w]["blloc"].ToString() + "' ");

                    string aa = "";
                        aa = (string)BazaDanychExternal.Wyczytaj_Jedna_Wartosc("select sum(ilosc) as ilosc from dlcollect d left join etykiety e on d.nr_et=e.id left join kody k on k.id=e.kod_id where d.nr_dl='" + nr_dl + "'  and k.kod='" + tabela.Rows[w]["kod"].ToString() + "'  "); //and e.dataprod='" + tabela.Rows[w]["dataprod"].ToString() + "' and status_prism='" + tabela.Rows[w]["status_prism"].ToString() + "' and blloc='" + tabela.Rows[w]["blloc"].ToString() + "'
                    

                    if (aa == "") aa = "0";
                    double ilosc_dlcollect = Math.Round(Convert.ToDouble(aa), 3);
                    double ilosc_dostepna = Math.Round(Convert.ToDouble(tabela.Rows[w]["ilosc_zamawiana"].ToString()), 3) - ilosc_dlcollect;
                    //MessageBox.Show("6");

                    zapytanie = "select k.kod,e.ilosc,d.nr_dl,e.id,e.active,dd.docout_type,dd.docout_nr, s.nazwa,s.funkcja_stat,e.magazyn,IFNULL(delivery_nr,0) as delivery_nr,IFNULL((select nr_dl from dlcollect d where e.id=d.nr_et limit 1),0) as dl_skan from  etykiety e left join dlcollect d on e.id=d.nr_et left join docout dd on dd.id=e.docout_id left join kody k on e.kod_id=k.id left join status_system s on e.status_id=s.id left join delivery_et de on de.etykieta_id=e.id left join delivery ddd on de.delivery_id=ddd.id  where (e.id='" + etykieta + "' or e.etykieta_klient='" + etykieta + "') order by e.id desc limit 1;";
                    object obj3 = BazaDanychExternal.Wyczytaj_Tabele(zapytanie);
                    DataTable tabela2 = (DataTable)obj3;
                    int jest = 0;
                    //MessageBox.Show("7");
                    if (tabela2.Rows.Count == 0)
                    {
                        MessageBox.Show("Nie istnieje w systemie etykieta " + etykieta + ".");
                        textBox1.Text = "";
                        return;
                    }
                    for (int zz = 0; zz < tabela2.Rows.Count; zz++)
                    {


                        if (zgodnosc == "1")
                        {
                            if (tabela.Rows[w]["kod"].ToString() == tabela2.Rows[0]["kod"].ToString()) jest++;
                        }
                        else
                        {
                            if (tabela.Rows[w]["kod"].ToString() == tabela2.Rows[0]["kod"].ToString()) jest++;
                            if (tabela.Rows[w]["id"].ToString() == tabela2.Rows[0]["id"].ToString()) jest++;
                        }

                        //if (tabela.Rows[w]["dataprod"].ToString() == tabela2.Rows[0]["dataprod"].ToString()) jest++;
                        //if (tabela.Rows[w]["status_prism"].ToString() == tabela2.Rows[0]["status_prism"].ToString()) jest++;
                        //if (tabela.Rows[w]["blloc"].ToString() == tabela2.Rows[0]["blloc"].ToString()) jest++;
                        //MessageBox.Show("88");


                        /*
                        if (tabela2.Rows[0]["magazyn"].ToString() != tabela.Rows[w]["magazyn"].ToString())
                        {
                            MessageBox.Show("Etykieta " + etykieta + " nie z tego z magazynu. Jest z " + tabela2.Rows[0]["magazyn"].ToString());
                            textBox1.Text = "";
                            return;
                        }
                        if (tabela2.Rows[0]["funkcja_stat"].ToString() == "blokada_wyd")
                        {
                            MessageBox.Show("Etykieta " + etykieta + " ma status " + tabela2.Rows[0]["nazwa"].ToString());
                            textBox1.Text = "";
                            return;
                        }

                        if (tabela2.Rows[0]["active"].ToString() == "0")
                        {
                            MessageBox.Show("Etykieta " + etykieta + " jest nieaktywna " + tabela2.Rows[0]["docout_type"].ToString() + " / " + tabela2.Rows[0]["docout_nr"].ToString());
                            textBox1.Text = "";
                            return;
                        }

                        if (tabela2.Rows[0]["nr_dl"].ToString() == nr_dl)
                        {
                            MessageBox.Show("Etykieta " + etykieta + " jest już wczytana na tą DL ");
                            textBox1.Text = "";
                            return;
                        }
                        else if ((Convert.ToInt32(tabela2.Rows[0]["dl_skan"].ToString()) > 0))  //tabela2.Rows[0]["delivery_nr"].ToString() != nr_dl && (Convert.ToInt32(tabela2.Rows[0]["delivery_nr"].ToString()) > 0 ||
                        {
                            MessageBox.Show("Etykiety nie można wczytać. " + tabela2.Rows[0]["id"].ToString() + " jest już zarezerwowana na DL " + tabela2.Rows[0]["delivery_nr"].ToString() + " lub " + tabela2.Rows[0]["dl_skan"].ToString());
                            textBox1.Text = "";
                            return;
                        }
                        else

                            if ((jest == 1 && zgodnosc == "1") || (jest == 2 && zgodnosc == "2")) //bbo 4 warunki spełnione
                            {
                                if (Math.Round(Convert.ToDouble(tabela2.Rows[0]["ilosc"].ToString()), 3) <= (ilosc_dostepna + 3))
                                {
                                    //MessageBox.Show("9");
                                    //MessageBox.Show("Można dodać");
                                    DateTime ts = DateTime.Now;
                                    BazaDanychExternal.DokonajUpdate("insert into dlcollect (nr_dl,nr_et,system_id) VALUES('" + nr_dl + "', '" + tabela2.Rows[0]["id"].ToString() + "','" + Wlasciwosci.system_id_id + "')");
                                    BazaDanychExternal.DokonajUpdate("insert into operacje(etykieta_id, doc_type, doc_nr, imie_nazwisko, typ_operacji,system_id,wozek,operac_id,ilosc) values('" + tabela2.Rows[0]["id"].ToString() + "','DL','" + nr_dl + "','" + Wlasciwosci.imie_nazwisko + "','DL_SZ','" + Wlasciwosci.system_id_id + "', '" + Wlasciwosci.wozek + "','" + operac_id_global + "','1');");

                                    
                                    aktualizuj_etykiete(etykieta, tabela2.Rows[0]["kod"].ToString(), tabela2.Rows[0]["ilosc"].ToString());
                                    ilosc_etykiet.Text = delivery_etykiety(_dl[comboBox1.SelectedIndex].ToString()) + "/" + wczytane_licznik(_dl[comboBox1.SelectedIndex].ToString());
                                    textBox1.Text = "";
                                    return;
                                }
                                else
                                {
                                    MessageBox.Show("Etykieta przekracza ilosc o:" + (Convert.ToDouble(tabela2.Rows[0]["ilosc"].ToString()) - ilosc_dostepna));

                                }
                            }
                            else
                            {
                                not_insert++;
                            }


                        textBox1.Text = "";
                    }



                }
                if (ilosc_pozycji_dl == not_insert)
                {
                    MessageBox.Show("Etykieta " + etykieta + " nie spełnia wymagań DL");
                    textBox1.Text = "";
                }
            }
*/
            aktualizuj_etykiete(etykieta, tabela.Rows[0]["kod"].ToString(), tabela.Rows[0]["ilosc_zamawiana"].ToString());
            ilosc_etykiet.Text = delivery_etykiety(_dl[comboBox1.SelectedIndex].ToString()) + "/" + wczytane_licznik(_dl[comboBox1.SelectedIndex].ToString());
                                    
            textBox1.Text = "";
            ZacznijSkanowanie();
        }

        private string DajDate(string gg)
        {

            gg = gg.Replace("'", "");
            //MessageBox.Show(gg);
            if (gg == "null")
            {
                return "null";
            }

            DateTime gh = DateTime.ParseExact(gg, "dd-MM-yyyy", null);
            return "'" + gh.ToString("yyyy-MM-dd") + "'";


        }


        private string sprawdz_czy_null(string bb)
        {
            if (bb == "")
            {

                return "null";
            }
            else
            {

                try
                {
                    Convert.ToDouble(bb);
                    return bb;
                }
                catch
                {
                    return "'" + bb + "'";
                }
            }

        }


        private string wczytane_licznik(string nr_dl)
        {
            return (string)BazaDanychExternal.Wyczytaj_Jedna_Wartosc("select count(1) from dlcollect d where nr_dl='" + nr_dl + "' and d.system_id='" + Wlasciwosci.system_id_id + "';");
        }

        private string delivery_etykiety(string nr_dl)
        {
            return (string)BazaDanychExternal.Wyczytaj_Jedna_Wartosc("select count(1) from etykiety e left join delivery_et de on de.etykieta_id=e.id left join delivery d on de.delivery_id=d.id where d.id='" + nr_dl + "' and e.system_id='" + Wlasciwosci.system_id_id + "' ");

        }

        private void kasuj_ostatnio_wczytane(string nr_dl)
        {
            string aa = (string)BazaDanychExternal.Wyczytaj_Jedna_Wartosc("select id from dlcollect d where nr_dl='" + nr_dl + "' and d.system_id='" + Wlasciwosci.system_id_id + "' order by d.id desc limit 1;");
            BazaDanychExternal.DokonajUpdate("delete from dlcollect where id='" + aa + "' limit 1;");
        }




































        private void aktualizuj_etykiete(string etykieta, string kod, string ilosc)
        {
            //if(nr_etykiety)
            try
            {
                //ok = myParent.conn;
                //MySqlCommand cmdSel = new MySqlCommand("update etykiety set nrsap='" + Convert.ToInt64(nrsap) + "',podkod='" + podkod + "' where id=" + nr_etykiety + " limit 1;", ok);
                //cmdSel.CommandText = "update etykiety set nrsap='" + Convert.ToInt64(nrsap) + "',podkod='" + podkod + "' where id=" + nr_etykiety + " limit 1;";
                //cmdSel.ExecuteNonQuery();
                result_label.Text = "Wczytano :" + etykieta + "\n" +
                    "Kod:" + kod + "   " +
                    "Ilość:" + ilosc;
                //etykieta_text.Text = "";

            }
            catch (MySqlException ex)
            {
                MessageBox.Show(ex.ToString());
            }

            /*
             *            DialogResult result3 = MessageBox.Show("Napewno chcesz zakończyć pracę na deklaracji?",
                "Eksport danych",
                MessageBoxButtons.YesNo,
                MessageBoxIcon.Question,
                MessageBoxDefaultButton.Button2);
            if (result3 == DialogResult.Yes)
            {}
             */

        }
        private void czysc()
        {



        }



        private void timer1_Tick(object sender, EventArgs e)
        {
            this.Close();
            myParent.Show();
        }




        private void label2_ParentChanged(object sender, EventArgs e)
        {

        }

        /*
        private void FERRERO_TworzenieDL_Load(object sender, EventArgs e)
        {
             try
            {
            //fullscreenmode();
            EventHandler MyReadNotifyHandler = new EventHandler(MyReader_ReadNotify);
            EventHandler MyStatusNotifyHandler = new EventHandler(MyReader_StatusNotify);
            this.MyReader.StatusNotify += MyStatusNotifyHandler;
            this.MyReader.ReadNotify += MyReadNotifyHandler;
            }
             catch (Exception ex)
             {
                 MessageBox.Show(ex.Message.ToString());
             }
        }*/

        #region fullscreen
        private void fullscreenmode()
        {
            this.Width = Screen.PrimaryScreen.Bounds.Width;
            this.Height = Screen.PrimaryScreen.Bounds.Height;
            this.Left = 0;
            this.Top = 0;
        }
        #endregion

        private void button3_Click(object sender, EventArgs e)
        {
            kasuj_ostatnio_wczytane(_dl[comboBox1.SelectedIndex].ToString());
            ilosc_etykiet.Text = delivery_etykiety(_dl[comboBox1.SelectedIndex].ToString()) + "/" + wczytane_licznik(_dl[comboBox1.SelectedIndex].ToString());
        }

        private void button2_Click(object sender, EventArgs e)
        {
            dodawanie(textBox1.Text);
            textBox1.Text = "";
        }

        private void checkBox1_CheckStateChanged(object sender, EventArgs e)
        {
            this.Skanowanie.Abort();
            Skaner.Przewij_Skanowanie();
            this.ZacznijSkanowanie();


            
                comboBox1.Visible = true;
                button1.Visible = true;
                button3.Visible = true;


        }

        

        




    }
}