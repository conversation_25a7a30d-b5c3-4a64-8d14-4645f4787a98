﻿using System;
using System.Linq;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using System.Threading;
using System.Text.RegularExpressions;
using System.Xml;

namespace Tarczyn__Magazyn
{



    public partial class Zad_DL_Pelna_Pobr : Form, IZad_DL
    {
        //MainMenu myParent = null;
        Zad_Main myParent = null;
        TextBox[] TextBoxArray = null;
        XmlNode node = null;
        TextBox AktualnyTextBox = null;


        List<string> _paleta_wybor = new List<string>();
        int[] _paleta = new int[100];


        //string ilosc_w_opakowaniu = "1";
        string ean_jednostki = "";
        string ean = "";
        string ilosc_w_opakowaniu = "";
        string ean_opakowanie_zbiorcze = "";
        string ilosc_szt_w_zbiorczym = "";
        string nosnik_numer = "";




        public Zad_DL_Pelna_Pobr(Zad_Main c, XmlNode node2)
        {

            FullScreenMode.OknoOFF(this);
            InitializeComponent();
            TextBoxArray = new TextBox[] { ETYKIETA };
            myParent = c;
            Etykieta.Inicjalizacja();
            wms_wybrany.Text = Wlasciwosci.system_id;
            imie_nazwisk_label.Text = Wlasciwosci.wozek + ":" + Wlasciwosci.imie_nazwisko;
            //Wlasciwosci.system_id_id = "";
            wypelnij_dane(node2);
        }

        void wypelnij_dane(XmlNode node2)
        {
            if (node2["zadan"].InnerText == "0")
            {
                myParent.Show();
                myParent.ZacznijNasluchiwanie("2", "");
                this.Close();
                return;
            }
            node = node2;
            label7.Text = node["doc_type_nazwa"].InnerText + " " + node["doc_id"].InnerText;
            label12.Text = node["etykieta_id"].InnerText + " DS" + node["paleta_id"].InnerText;
            textBox4.Text = node["stare_m_nazwa"].InnerText;
            kod.Text = node["kod"].InnerText;
            textBox3.Text = node["kod_nazwa"].InnerText;
            LOT.Text = node["lot"].InnerText;
            zam_szt.Text = node["ilosc"].InnerText;
            //ilosc_w_opakowaniu = node["ilosc_w_opakowaniu"].InnerText;
            ean = node["ean"].InnerText;
            ilosc_w_opakowaniu = node["ilosc_w_opakowaniu"].InnerText;
            ean_jednostki = node["ean_jednostki"].InnerText;
            ean_opakowanie_zbiorcze = node["ean_opakowanie_zbiorcze"].InnerText;
            ilosc_szt_w_zbiorczym = node["ilosc_szt_w_zbiorczym"].InnerText;
            licznik_label.Text = "" + node["ile_wszystkich"].InnerText + "/" + node["ile_gotowe"].InnerText + " ; " + node["ile_wszystkich_paleta"].InnerText + "/" + node["ile_gotowe_paleta"].InnerText;
            zam_szt.Focus();
            ETYKIETA.Focus();
            ChangeColorWhite(ETYKIETA);
            message_label.Text = "";
        }


        private void TworzenieDL_Load(object sender, EventArgs e)
        {
            //ETYKIETA.Focus();
        }



        private void GotFocus_TextBox(object sender, EventArgs e)
        {
            Skaner.Przewij_Skanowanie();
            TextBox Pole_Tekstowe = (TextBox)sender;
            AktualnyTextBox = Pole_Tekstowe;
            ZacznijSkanowanie();
        }

        private void LostFocus_TextBox(object sender, EventArgs e)
        {
            AktualnyTextBox = null;
            Skaner.Przewij_Skanowanie();

            TextBox Pole_Tekstowe = (TextBox)sender;

            if (ETYKIETA == Pole_Tekstowe)
            {


            }
        }

        private void ChangeColorLime(TextBox textBox)
        {
            textBox.BackColor = System.Drawing.Color.Lime;
        }
        private void ChangeColorRed(TextBox textBox)
        {
            textBox.BackColor = System.Drawing.Color.Red;
        }
        private void ChangeColorWhite(TextBox textBox)
        {
            textBox.BackColor = System.Drawing.Color.White;
        }






        #region Skanowanie

        Thread Skanowanie = null;



        private void ZacznijSkanowanie()
        {
            StringBuilder login = new StringBuilder();
            Skaner.UstawTryb_String(login);
            Skaner.Zacznij_Skanowanie();
            Skanowanie = new Thread(() => CheckString(login));
            Skanowanie.IsBackground = true;
            Skanowanie.Start();
        }

        private void CheckString(StringBuilder cc)
        {
            while (true)
            {
                if (cc.ToString() != "")
                {
                    this.Invoke(new EventHandler(delegate
                    {
                        dodawanie(cc.ToString());
                    }));
                    return;
                }
                Thread.Sleep(200);
            }
        }


        private void Zakoncz_Skanowanie()
        {
            {
                Skanowanie.Abort();
                Skaner.Przewij_Skanowanie();
            }
        }

        private void dodawanie(string ops)
        {
            //MessageBox.Show("TrybSkanu:" + TrybSkanu + "   ,   ops.Length:" + ops.Length);


            AktualnyTextBox.Text = ops;
            if (ops == "")
            {
                MessageBox.Show("Nie wypełniono etykiety");
                return;
            }



            if (AktualnyTextBox == ETYKIETA)
            {

                    XmlNode node_etykieta= Serwer_Konwersacja(node,null, ops,2);


                    if (node_etykieta["komunikat"].InnerText != "OK")
                    {
                        ETYKIETA.Text = "";
                        ChangeColorRed(ETYKIETA);
                        zam_szt.Focus();
                        ETYKIETA.Focus();
                        if (node_etykieta["komunikat"].InnerText.Length > 40)
                        {
                            MessageBox.Show(node_etykieta["komunikat"].InnerText);
                        }
                        else
                        {
                            message_label.Text = node_etykieta["komunikat"].InnerText;
                        }
                    }                    
                    else
                    {
                        ETYKIETA.Text = "";
                        ChangeColorLime(ETYKIETA);
                        //licznik_label.Text = "" + node_etykieta["ile_wszystkich"].InnerText + " / " + node_etykieta["ile_gotowe"].InnerText;
                        if (node_etykieta["czy_koniec_kompletacji"].InnerText == "NIE")
                        {
                            node_etykieta = myParent.PobieranieZadaniaZserwera("1", "");
                            //MessageBox.Show(node_etykieta.InnerXml);
                            this.wypelnij_dane(node_etykieta);
                            
                        }
                        else
                        {
                            myParent.Show();
                            myParent.ZacznijNasluchiwanie("2", "");
                            this.Close();
                        }
                    }
                }
            //this.ZacznijSkanowanie();
            }

        private XmlNode Serwer_Konwersacja(XmlNode node_src, XmlNode node_etykieta, string etykieta_scan, int proba)
        {
            //MessageBox.Show("Serwer_Konwersacja:"+proba +",  etykieta_id_realizowana:" + etykieta_id_realizowana);
            //MessageBox.Show("etykieta_id:" + node_etykieta["etykieta_id"].InnerText + ", ");
            //MessageBox.Show("nosnik_numer=" + nosnik_numer + "");


            XmlDocument doc1_etykieta = WebService.Pobierz_XmlDocument("delivery_realizacja_pelnych.php?akcja=realizacja_zadania&zadanie_dane_id=" + node_src["id"].InnerText + "&imie_nazwisko=" + Uri.EscapeUriString(Wlasciwosci.imie_nazwisko) + "&etykieta_scan=" + etykieta_scan);
            node_etykieta = WebService.Pobierz_XmlNode(doc1_etykieta, "dane");

            if (node_etykieta["komunikat"].InnerText != "OK")
            {
                //MessageBox.Show("0");
                return node_etykieta;
                //MessageBox.Show(node_etykieta["komunikat"].InnerText);
            }
            else
            {
                if (node_etykieta["ilosc_pozycji_etykiety"].InnerText != "1")
                {
                    //MessageBox.Show("1");
                    XmlNodeList xmlnode2 = WebService.Pobierz_XmlNodeList(doc1_etykieta, "etykiety");
                    DataTable dt = Konwersja_XmlNodeList_DataTable(xmlnode2);
                    //MessageBox.Show("2");
                    PoleWyborListBox XA = new PoleWyborListBox(dt, 12F, "NIE");

                    if (XA.ShowDialog() == DialogResult.OK)
                    {
                        if (XA.wartosc_wybrana != "")
                        {
                            //MessageBox.Show("3");
                            if (proba > 0)
                            {
                                node_etykieta = Serwer_Konwersacja(node_src,node_etykieta, XA.wartosc_wybrana, proba -= 1);
                            }
                        }
                    }
                }
            }
            //MessageBox.Show("4");
            return node_etykieta;
        }

        private static DataTable Konwersja_XmlNodeList_DataTable(XmlNodeList xmlnode2)
        {
            DataTable dt = new DataTable();
            dt.Columns.Add("id", typeof(string));
            dt.Columns.Add("nazwa_wyswietlana", typeof(string));
            foreach (XmlNode wynik in xmlnode2)
            {
                DataRow dtrow = dt.NewRow();
                dtrow["id"] = wynik["id"].InnerText;
                dtrow["nazwa_wyswietlana"] = wynik["id"].InnerText + " ; " + wynik["kod"].InnerText + " ; " + wynik["ilosc"].InnerText;
                dt.Rows.Add(dtrow);
            }
            return dt;
        }

        #endregion

        private void button3_Click(object sender, EventArgs e)
        {

        }







        private void QT_KeyDown(object sender, KeyEventArgs e)
        {

        }





        private void powrot_Click(object sender, EventArgs e)
        {
            Skaner.Przewij_Skanowanie();
            myParent.Show();
            this.Close();
        }

        private void odkladanie_Click(object sender, EventArgs e)
        {
            //myParent.delivery_odkladanie("TAK");
            myParent.Show();
            myParent.ZacznijNasluchiwanie("2", "");

            this.Close();
        }

        private void podglad_button_Click(object sender, EventArgs e)
        {
            Zad_DL_Podglad okno_nowy_nosnik = new Zad_DL_Podglad(this, node);
            okno_nowy_nosnik.Show();
            this.Hide();
        }

        private void Etykieta_KeyDown(object sender, KeyEventArgs e)
        {
            if (e.KeyCode == Keys.Enter)
            {
                this.dodawanie(ETYKIETA.Text);
                //odkladanie_Click(this, new EventArgs());
            }
        }






    }
}