<?php

include_once 'Db.class.php';
include_once 'funkcje.inc';
$db = new Db();



$komunikat = "OK";
$akcja = $_GET['akcja'];
$zadanie_head_id = $_GET['zadanie_head_id'];
$imie_nazwisko = plznaki($_GET['imie_nazwisko']);
$skan = $_GET["skan"];
$czy_koniec_zadania = "NIE";
$czy_koniec_kompletacji_zadania = "NIE";


$ile_kompletowanych_palet = 0;
$start_time = microtime(true);


//header('Content-type: text/xml');
/* echo '<?xml version="1.0" encoding="utf-8" ?><dane>'; */

if (empty($akcja) || empty($zadanie_head_id) || empty($imie_nazwisko) || empty($skan)) {
    $komunikat = "Brak wszystkich parametrow wejsciwych. Przerywam operacje";
    //echo '<komunikat>', htmlentities($komunikat), '</komunikat></dane>';
    return show_komunikat_xml($komunikat);
}


if ($akcja == "realizacja_zadania") {

    if (empty($ilosc)) {
        $ilosc = "0";
    }

    $sql = 'SELECT  zh.baza_danych AS baza_danych
 FROM zadania_head  zh
            where zh.id=' . $zadanie_head_id . '  limit 1';

    $result10 = $db->mGetResultAsXML($sql);

    foreach ($result10 as $key => $value) {
        $baza_danych = $value["baza_danych"];
    }



    $sql = 'SELECT zd.id AS id,k.kod,
TRIM(TRAILING "." FROM TRIM(TRAILING "0" from zd.ilosc)) as ilosc ,
ifnull((select ee.ilosc from etykiety ee
where ee.paleta_id=zd.paleta_id and ee.kod_id=zd.kod_id and ee.active=1 limit 1 ),0)  as ilosc_rzeczywista,
if(zd.ilosc < ifnull((select ee.ilosc from etykiety ee
where ee.paleta_id=zd.paleta_id and ee.kod_id=zd.kod_id and ee.active=1 limit 1 ),0),"TAK","NIE") as czy_kompletowana,
if(zd.ilosc > ifnull((select ee.ilosc from etykiety ee
where ee.paleta_id=zd.paleta_id and ee.kod_id=zd.kod_id and ee.active=1 limit 1 ),0),"TAK","NIE") as czy_brak,

k.kod_nazwa,k.ean,k.ean_jednostki, k.ilosc_w_opakowaniu, 
(k.ilosc_w_opakowaniu*TRIM(TRAILING "." FROM TRIM(TRAILING "0" from zd.ilosc))) as ilosc_opakowan,
zd.zadanie_head_id AS zadanie_head_id,
zd.status AS status,
zs.status_nazwa AS status_nazwa,

zd.stare_m AS stare_m, zd.nowe_m AS nowe_m,
zd.nowe_m_realizowane AS nowe_m_realizowane,
zd.paleta_id AS paleta_id, zd.etykieta_id AS etykieta_id,
zd.kod_id AS kod_id, zd.lot AS lot,
zh.baza_danych AS baza_danych, zh.system_id AS system_id, zh.typ AS typ,
zh.doc_id AS doc_id, zh.doc_type AS doc_type,
zdt.nazwa AS doc_type_nazwa,
zh.zgodnosc_miejsca AS zgodnosc_miejsca, zh.zgodnosc_towaru AS zgodnosc_towaru,
zt.nazwa AS zadanie_typ_nazwa,
zd.kompletowana_paleta_id,
concat("H: ",m1.hala," ",m1.regal,"-",m1.miejsce,"-",m1.poziom ) AS stare_m_nazwa,
concat("H: ",m2.hala," ",m2.regal,"-",m2.miejsce,"-",m2.poziom ) AS nowe_m_nazwa,
m2.hala as nowe_hala,
m2.regal as nowe_regal,
m2.miejsce as nowe_miejsce,
m2.poziom as nowe_poziom,
s.nazwa AS system_id_nazwa,

         if(zh.baza_danych="obrama", (SELECT if(w.rampa is null,"",concat("Rampa ",w.rampa,"; ",w.nr_ciagnik,"/", w.nr_naczepa))
         FROM obrama.wjazdy w WHERE w.id=zh.doc_id limit 1),"") AS info,

  zd.czas_przydzielenia AS czas_przydzielenia,
zd.przydzielenie_pracownik_id AS przydzielenie_pracownik_id,
zd.start AS start, zd.stop AS stop,
zd.realizacja_pracownik_id AS realizacja_pracownik_id,
p2.imie_nazwisko AS realizacja_imie_nazwisko,

 zd.stanowisko_id AS stanowisko_id, zd.kierunek AS kierunek,zd.kompletacja,
 zh.priorytet AS priorytet,
 p.imie_nazwisko AS przydzielenie_imie_nazwisko,

        concat(if(zh.baza_danych="obrama",
        (SELECT w.numer_karty FROM obrama.wjazdy w WHERE w.id=zh.doc_id limit 1),"")) AS numer_karty,
         if(zh.baza_danych="obrama", (SELECT w.ilosc_palet FROM obrama.wjazdy w WHERE w.id=zh.doc_id limit 1),"") AS ilosc_jednostek


 FROM zadania_dane zd LEFT JOIN zadania_head AS zh ON zh.id=zd.zadanie_head_id LEFT JOIN zadania_doc_type AS zdt ON zh.doc_type=zdt.id
         LEFT JOIN zadania_typ AS zt ON zh.typ=zt.id 
LEFT JOIN ' . $baza_danych . '.miejsca AS m1 ON m1.id=zd.stare_m
LEFT JOIN ' . $baza_danych . '.miejsca AS m2 ON m2.id=zd.nowe_m
LEFT JOIN ' . $baza_danych . '.pracownicy AS p ON zd.przydzielenie_pracownik_id=p.id
LEFT JOIN ' . $baza_danych . '.pracownicy AS p2 ON zd.realizacja_pracownik_id=p2.id
LEFT JOIN ' . $baza_danych . '.systemy AS s ON s.wartosc=zh.system_id
LEFT JOIN ' . $baza_danych . '.kody k ON zd.kod_id=k.id
LEFT JOIN wmsgg.zadania_statusy  AS zs  ON zd.status=zs.id

            where zh.id=' . $zadanie_head_id . ' and zd.status=2 and p.imie_nazwisko="' . $imie_nazwisko . '"  order by zd.start';
    $result = $db->mGetResultAsXML($sql);
    //$ile = mysql_num_rows($result);
    //echo "<br>" . $sql . "<br>";


    $wymagania_etykieta_skanowana = wymagania_etykieta_skanowana($skan, $zadanie_head_id, $db);
    if (!empty($wymagania_etykieta_skanowana)) {
        $komunikat = $wymagania_etykieta_skanowana;
        return show_komunikat_xml($komunikat);
    }


    // sprawdz czy było coś zaczęte
    $sprawdzanie_czy_jest_odlozone = sprawdzanie_czy_jest_odlozone($zadanie_head_id, $db);


    $sql = 'SELECT id FROM ' . $baza_danych . '.pracownicy p WHERE p.imie_nazwisko="' . $imie_nazwisko . '" limit 1; ';
    $result2 = $db->mGetResultAsXML($sql);
    //echo "<br>" . $sql;
    foreach ($result2 as $index => $aRowp) {
        $pracownik_id = $aRowp['id'];
    }
    if (empty($pracownik_id)) {
        $komunikat = "Nie znaleziono pracownika: " . $imie_nazwisko . ". Przerywam operacje";
        return show_komunikat_xml($komunikat);
    }


    $delivery_id = 0;


    if (!empty($result)) {
        $zadanie_dane_id = 0;
        foreach ($result as $index => $aRowZadanie) {
            $baza_danych = $aRowZadanie['baza_danych'];
            $zadanie_dane_id = $aRowZadanie['id'];
            $delivery_id = $aRowZadanie["doc_id"];

            if ($aRowZadanie['status'] != "2") {
                $komunikat = "Zadanie zostalo zrealizowane . Przerywam operacje";
                return show_komunikat_xml($komunikat);
            }














            // najpotrzebniejsze etykieta ta sama i ilosc realizowana
            //$skan = "MP-1-RMP-1-A";
            $arr = explode("-", $skan);




            if ($arr[0] == "MP") {


                if ($arr[2] == $aRowZadanie["nowe_regal"]) {

                    // sprawdz czy było coś zaczęte
                    if ($sprawdzanie_czy_jest_odlozone == "TAK") {
                        $komunikat = "Wymagane nosnik skompletowany";
                        return show_komunikat_xml($komunikat);
                    }


                    if ($aRowZadanie["nowe_hala"] != $arr[1] || $aRowZadanie["nowe_regal"] != $arr[2] || $aRowZadanie["nowe_miejsce"] != $arr[3]) {
                        $komunikat = "Wczytano \nH:" . $arr[1] . " " . " " . $arr[2] . "-" . $arr[3] .
                                " \nEtykieta moze byc skompletowana tylko na:\nH:" . $aRowZadanie["nowe_hala"] . " " . $aRowZadanie["nowe_regal"] . "-" . $aRowZadanie["nowe_miejsce"] . "";
                        return show_komunikat_xml($komunikat);
                    }
                } else {
                    // generuj porzucenie, pytanie dla jednej pozycji czy wielu/ chyba jednej
                    // chwilowa blokada porzucen
                    $komunikat = "Niepoprawne miejsce";
                    return show_komunikat_xml($komunikat);



//                    $nowe_miejsce = sprawdz_czy_istnieje_miejsce($arr[1], $arr[2], $arr[3], "A", $baza_danych, $db);
//                    if (empty($nowe_miejsce)) {
//                        $komunikat = "Brak w bazie tego miejsca";
//                        return show_komunikat_xml($komunikat);
//                    }
//
////                    echo "<pre>";
////                    print_r($result);
////                    echo "</pre>";
//                    realizuj_porzucenie($result, $komunikat, $baza_danych, $pracownik_id, $nowe_miejsce, $zadanie_dane_id, $db);
//
//                    $sprawdz = sprawdz_czy_wszystkie_kompletowane_sa_skompletowane($zadanie_head_id, $db);
//                    if ($sprawdz == "TAK") {
//                        zmien_status_rozpocznij_pelne_po_kompletacji($zadanie_head_id, $db);
//                    }
//
//                    $sprawdz2 = sprawdz_czy_wszystkie_zadanie_dokumentu_sa_wykonane($zadanie_head_id, $db);
//                    if ($sprawdz2 == "TAK") {
//                        $czy_koniec_zadania = "TAK";
//                        $sql = 'update zadania_head  set status_dokumentu=3 where id=' . $zadanie_head_id . ' and status_dokumentu!=3 limit 1';
//                        $result5 = $db->mGetResultAsXML($sql);
//                        $ile_kompletowanych_palet = ile_kompletowanych_palet($zadanie_head_id, $db);
//                    }
////                    echo "<pre>";
////                    print_r($result);
////                    echo "</pre>";
////                    return false;
//
//
//                    xml_from_indexed_array(array(
//                        'komunikat' => $komunikat,
//                        'ile_kompletowanych_palet'=>$ile_kompletowanych_palet,
//                        'czy_koniec_zadania' => $czy_koniec_zadania,
//                        'czy_koniec_kompletacji_zadania'=>$czy_koniec_kompletacji_zadania)
//                    );
                }

                // czy miejsce jest dodane w bazie danych
            }


            if (substr($arr[0], 0, 2) == "DS") {


//                if ($aRowZadanie["kompletowana_paleta_id"] == str_replace("DS", "", $skan)) {
//                    $komunikat = "Ta etykieta nie zostala odlozona na miejsce kompletacji";
//                    return show_komunikat_xml($komunikat);
//                }


                if ($sprawdzanie_czy_jest_odlozone == "NIE") {
                    $komunikat = "Wymagane miejsce kompletowane";
                    return show_komunikat_xml($komunikat);
                }


                $sql = 'select dl.nr_dl,hala,regal,miejsce,poziom
                            from ' . $baza_danych . '.etykiety e
                            left join ' . $baza_danych . '.miejsca m on e.miejscep=m.id
                            left join ' . $baza_danych . '.dlcollect dl  on dl.nr_et=e.id
                            where e.paleta_id=' . str_replace("DS", "", $skan) . " order by nr_dl desc limit 1 ";
                $result2 = $db->mGetResultAsXML($sql);
                //echo "<br>" . $sql;

                if (count($result2) == 0) {
                    $komunikat = "Nie rozpoznano etykiety";
                    return show_komunikat_xml($komunikat);
                }
                foreach ($result2 as $index => $aRowEtWms) {



                    //select m.id from wmsgg.miejsca m where m.hala='3' and regal='RMP' and miejsce='3' and poziom='A' limit 1


                    if ($aRowEtWms['nr_dl'] != $aRowZadanie["doc_id"]) {
                        $komunikat = "Etykieta jest z innej DL bo jest z " . $aRowEtWms['nr_dl'] . ". Przerywam operacje";
                        //echo "<br>" . $komunikat;
                        //return;
                        return show_komunikat_xml($komunikat);
                    }
                }
            }
//            else {
//                // gdy etykieta WMS
//
//                $sql = 'select dl.nr_dl,hala,regal,miejsce,poziom
//                            from ' . $baza_danych . '.etykiety e
//                            left join ' . $baza_danych . '.miejsca m on e.miejscep=m.id
//                            left join ' . $baza_danych . '.dlcollect dl  on dl.nr_et=e.id
//                            where (e.id=' . $skan . ' or e.etykieta_klient="' . $skan . '") order by nr_dl desc limit 1 ';
//                $result2 = $db->mGetResultAsXML($sql);
//                //echo "<br>" . $sql;
//                if (count($result2) == 0) {
//                    $komunikat = "Nie rozpoznano etykiety";
//                    return show_komunikat_xml($komunikat);
//                }
//                foreach ($result2 as $index => $aRowEtWms) {
//                    //while ($aRowEtWms = mysql_fetch_assoc($result2)) {
//                    //select m.id from wmsgg.miejsca m where m.hala='3' and regal='RMP' and miejsce='3' and poziom='A' limit 1
//
//
//                    if ($aRowEtWms['nr_dl'] != $aRowZadanie["doc_id"]) {
//                        $komunikat = "Etykieta jest z innej DL bo jest z " . $aRowEtWms['nr_dl'] . ". Przerywam operacje";
//                        //echo "<br>" . $komunikat;
//                        //return;
//                        return show_komunikat_xml($komunikat);
//                    }
//                }
//            }












            if ($komunikat == "OK") {

                $sql = "insert into $baza_danych.zmianym 
                        (typ, doc_nr, pracownik_id, data, etykieta, system_id, stare_m, nowe_m, doc_internal, stat, tszm)
                        values('ZM',0, '$pracownik_id',CURDATE(), " . $aRowZadanie['etykieta_id'] . "," . $aRowZadanie['system_id'] . "," . $aRowZadanie['stare_m'] . ", " . $aRowZadanie['nowe_m'] . ", 'Z', 1, NOW())";
                //echo "<br>" . $sql;
                $result2 = $db->mGetResultAsXML($sql);

                $sql = "update $baza_danych.etykiety set miejscep=" . $aRowZadanie['nowe_m'] . " where id=" . $aRowZadanie['etykieta_id'] . " limit 1";

                //echo "<br>" . $sql;
                $result2 = $db->mGetResultAsXML($sql);

                //zamykanie statusu dokumentu


                $sql = ' update  zadania_dane z set z.realizacja_pracownik_id=' . $pracownik_id . ',status=3 WHERE z.id=' . $zadanie_dane_id;
                //echo "<br>" . $sql;
                $result = $db->mGetResultAsXML($sql);
            }
            $komunikat .= $db->errors;
        }




        //pozpoczynanie_pelnych po skompletowaniu wszystkich
        $sprawdz = sprawdz_czy_wszystkie_kompletowane_sa_skompletowane($zadanie_head_id, $db);
        if ($sprawdz == "TAK") {
            zmien_status_rozpocznij_pelne_po_kompletacji($zadanie_head_id, $db);
            $czy_koniec_kompletacji_zadania = "TAK";
        }

        $sprawdz2 = sprawdz_czy_wszystkie_zadanie_dokumentu_sa_wykonane($zadanie_head_id, $db);
        if ($sprawdz2 == "TAK") {
            $czy_koniec_zadania = "TAK";
            $sql = 'update zadania_head  set status_dokumentu=4 where id=' . $zadanie_head_id . ' and status_dokumentu!=4 limit 1';
            $result5 = $db->mGetResultAsXML($sql);
            delivery_insert_palety_docin_docout($aRowZadanie["doc_id"], $db);
            $ile_kompletowanych_palet = ile_kompletowanych_palet($zadanie_head_id, $db);
            sprawdzanie_dl_akceptacja(array('delivery_id' => $aRowZadanie["doc_id"]), $db);
            $sql = 'update zlecenia_head set status=5 WHERE delivery_id=' . $aRowZadanie["doc_id"] . ' limit 1';
            $result15 = $db->mGetResultAsXML($sql);
            if ($aRowZadanie["system_id"] == "41") {
                exec('/usr/bin/php -f /var/www/wmsgg/public/skrypty/raporty_zawartosci_dl.php ' . $aRowZadanie["doc_id"]);
            }
        }






        // by ostatnia etykieta miała czas rampy rampę
        $sql = "update  zadania_dane z set z.stop=NOW()  WHERE z.id=" . $zadanie_dane_id . " limit 1";
        $result7 = $db->mGetResultAsXML($sql);

        //$start_time = microtime(true);
        $end_time = microtime(true);
        $execution_time = intval($end_time - $start_time);
        if ($execution_time > 0) {
            $sql = "INSERT INTO czas_wykonywania(nazwa_funkcji, ts, sekund)
                        values
                        ('delivery_kompletacja_odkladanie',NOW(),'" . $execution_time . "');";
            $result7 = $db->mGetResultAsXML($sql);
        }


//$komunikat=$sql;
    } else {
        $komunikat = "Brak zadan";
    }
} else {
    $komunikat = "Brak akcji realizacji!";
}
//show_komunikat_xml($komunikat);
xml_from_indexed_array(array(
    'komunikat' => $komunikat,
    'ile_kompletowanych_palet' => $ile_kompletowanych_palet,
    'czy_koniec_zadania' => $czy_koniec_zadania,
    'czy_koniec_kompletacji_zadania' => $czy_koniec_kompletacji_zadania)
);

function sprawdzanie_dl_akceptacja($params, $db) {

    $sql = "SELECT d.zgodnosc FROM delivery d   WHERE d.id='" . $params['delivery_id'] . "' limit 1;";
    //echo "<br>" . $sql;
    $result = $db->mGetResultAsXML($sql);
    if (!empty($result)) {
        if ($result[0]['zgodnosc'] == 1) {

             $sql = "SELECT z.id FROM zlecenia_head z WHERE z.delivery_id='" . $params['delivery_id'] . "' limit 1;";
            //echo "<br>" . $sql;
            $result3 = $db->mGetResult($sql);
            

            $sql = "select cc.*,if( cc.ilosc_zam=cc.ilosc_dl,'OK', 'NO OK') as wynik, if(cc.ilosc_dl=cc.ilosc_skaner ,'OK', 'NO OK') as wynik2 from ( select bb.kod,sum(ilosc_zam) as ilosc_zam, sum(ilosc_dl) as ilosc_dl, sum(ilosc_skaner) as ilosc_skaner from ( SELECT a.kod, TRIM(TRAILING '.' FROM TRIM(TRAILING '0' from ROUND(sum(a.ilosc),3))) as ilosc_zam, 0 as ilosc_dl, 0 AS ilosc_skaner FROM zlecenia_dane a left join zlecenia_head ah on ah.id=a.zlecenie_id WHERE ah.delivery_id='" . $params['delivery_id'] . "' GROUP BY a.kod union all SELECT k.kod, 0 as ilosc_zam, TRIM(TRAILING '.' FROM TRIM(TRAILING '0' FROM ROUND(sum(de.ilosc_zamawiana), 3))) AS ilosc_dl, (0 ) AS ilosc_skaner FROM etykiety e LEFT JOIN delivery_et de ON e.id=de.etykieta_id LEFT JOIN kody k ON e.kod_id=k.id WHERE de.delivery_id='" . $params['delivery_id'] . "' GROUP BY e.kod_id union all SELECT k.kod, 0 as ilosc_zam, (0) AS ilosc_dl, TRIM(TRAILING '.' FROM TRIM(TRAILING '0' FROM ROUND(sum(e.ilosc), 3))) AS ilosc_skaner FROM dlcollect dc LEFT JOIN etykiety e ON e.id=dc.nr_et LEFT JOIN kody k ON e.kod_id=k.id WHERE dc.nr_dl='" . $params['delivery_id'] . "' GROUP BY e.kod_id) as bb group by bb.kod ) as cc;";


            $no_ok = 0;
            $result2 = $db->mGetResultAsXML($sql);

            foreach ($result2 as $key => $value) {

//                echo "<PRE>";
//                print_r($value);
//                echo "</PRE>";

                if ($value['wynik2'] == "NO OK") {
                    $no_ok++;
                }
                if (!empty($result3)) {
                    if ($value['wynik'] == "NO OK") {
                        $no_ok++;
                    }
                }
            }

            $sql = "SELECT z.id FROM zlecenia_head z WHERE z.delivery_id='" . $params['delivery_id'] . "' limit 1;";
            //echo "<br>" . $sql;
            $result3 = $db->mGetResultAsXML($sql);

//            echo "<PRE>";
//            print_r($result3);
//            echo "</PRE>";
//            return;


            //if (!empty($result3) && !empty($result2) && empty($no_ok)) {
            if (!empty($result2) && empty($no_ok)) {
                $sql = "update delivery d set dl_status=3  WHERE d.id='" . $params['delivery_id'] . "' limit 1;";
                //echo "<br>" . $sql;
                $db->mGetResultAsXML($sql);
            }
        }
    }
}

function delivery_insert_palety_docin_docout($delivery_id, $db) {
    $sql_ins_zad = "SELECT p.typypalet_id,count(distinct paleta_id) as ilosc FROM delivery d
                    left join dlcollect dc on d.id=dc.nr_dl
                    left join etykiety e on e.id=dc.nr_et
                    left join palety p on e.paleta_id=p.id
                    where d.id=$delivery_id
                    group by typypalet_id";
    //echo "<br>" . $sql_ins_zad;
    $result = $db->mGetResultAsXML($sql_ins_zad);
    foreach ($result as $key => $value) {
        if (!empty($result[$key]['ilosc'])) {
            $sql_ins_zad = "replace into palety_docin_docout(doc_id, doc_typ, typypalet_id, ilosc, wlasnosc_id)
                        values ('" . $delivery_id . "', '3', '" . $result[$key]['typypalet_id'] . "', '" . $result[$key]['ilosc'] . "','1' );";
            //echo "<br>" . $sql_ins_zad;
            $result2 = $db->mGetResultAsXML($sql_ins_zad);
        }
    }
}

function realizuj_porzucenie($result, $komunikat, $baza_danych, $pracownik_id, $nowe_miejsce, $zadanie_dane_id, $db) {

    $paleta_id_prev = "";
    $paleta_id = "";
    foreach ($result as $index => $aRowZadanie) {

        if ($komunikat == "OK") {

            // 
            if (empty($aRowZadanie['kompletowana_paleta_id'])) {
                $paleta_id = $aRowZadanie['paleta_id'];
            } else {
                $paleta_id = $aRowZadanie['kompletowana_paleta_id'];
            }
            if ($paleta_id != $paleta_id_prev) {
                $sql_ins_zad = "insert into wmsgg.zadania_dane(zadanie_head_id,status, stare_m, nowe_m, paleta_id, etykieta_id, kod_id, lot, ilosc,kompletacja,wysokie,zadanie_dane_rodzic_id,kompletowana_paleta_id) values "
                        . "('" . $aRowZadanie['zadanie_head_id'] . "','8','" . $nowe_miejsce . "','" . $aRowZadanie['nowe_m'] . "','" . $paleta_id . "','0','0','','0','0','0','" . $aRowZadanie['id'] . "','" . $aRowZadanie['kompletowana_paleta_id'] . "')";
                //echo "<br>" . $sql_ins_zad;
                $result_zadanie4 = $db->mGetResultAsXML($sql_ins_zad);
                $paleta_id_prev = $paleta_id;
            }

            $sql = "insert into $baza_danych.zmianym 
                        (typ, doc_nr, pracownik_id, data, etykieta, system_id, stare_m, nowe_m, doc_internal, stat, tszm)
                        values('ZM',0, '$pracownik_id',CURDATE(), " . $aRowZadanie['etykieta_id'] . "," . $aRowZadanie['system_id'] . "," . $aRowZadanie['stare_m'] . ", " . $nowe_miejsce . ", 'Z', 1, NOW())";
            //echo "<br>" . $sql;
            $result2 = $db->mGetResultAsXML($sql);

            $sql = "update $baza_danych.etykiety set miejscep=" . $nowe_miejsce . " where id=" . $aRowZadanie['etykieta_id'] . " limit 1";

            //echo "<br>" . $sql;
            $result2 = $db->mGetResultAsXML($sql);

            //zamykanie statusu dokumentu


            $sql = ' update  zadania_dane z set z.realizacja_pracownik_id=' . $pracownik_id . ', z.stop=NOW(),status=7 WHERE z.id=' . $aRowZadanie['id'];
            //echo "<br>" . $sql;
            $result = $db->mGetResultAsXML($sql);
        }
    }
}

function sprawdz_czy_wszystkie_zadanie_dokumentu_sa_wykonane($zadanie_head_id, $db) {
    $wynik = "NIE";
    $sql_ins_zad = "SELECT sum(if(zs.finish=1,1,0)) as gotowe, sum(1) as wszystkie FROM zadania_dane z
left join zadania_statusy zs on zs.id=z.status
WHERE  zadanie_head_id=  " . $zadanie_head_id;
    //echo "<br>" . $sql_ins_zad;
    $result_zadanie4 = $db->mGetResult($sql_ins_zad);
    if (!empty($result_zadanie4)) {
        if ($result_zadanie4[0]['gotowe'] == $result_zadanie4[0]['wszystkie']) {
            $wynik = "TAK";
        }
    }
    return $wynik;
}

function wymagania_etykieta_skanowana($skan, $zadanie_head_id, $db) {
    $komunikat = "";

    if (!(substr($skan, 0, 2) == "MP" || substr($skan, 0, 2) == "DS")) {
        $komunikat = "Dozwolone: paleta/miejsce kompletowane";
        return $komunikat;
    }

//    $sprawdzanie_czy_jest_odlozone = sprawdzanie_czy_jest_odlozone($zadanie_head_id, $db);
//    if ($sprawdzanie_czy_jest_odlozone == "NIE" && substr($skan, 0, 2) != "MP") {
//        $komunikat = "Wymagane miejsce kompletowane";
//    }
//    if ($sprawdzanie_czy_jest_odlozone == "TAK" && substr($skan, 0, 2) != "DS") {
//        $komunikat = "Wymagane nosnik skompletowany";
//    }
    return $komunikat;
}

function sprawdzanie_czy_jest_odlozone($zadanie_head_id, $db) {
    $wynik = "NIE";
    $sql = "SELECT 1 as ile FROM zadania_dane z where status=3 and zadanie_head_id=" . $zadanie_head_id;
    $result_zadanie4 = $db->mGetResult($sql);
    if (!empty($result_zadanie4)) {
        $wynik = "TAK";
    }
    return $wynik;
}

function ile_kompletowanych_palet($zadanie_head_id, $db) {
    $sql = "SELECT count(distinct z.kompletowana_paleta_id) as ile_palet FROM zadania_dane z
WHERE z.zadanie_head_id=" . $zadanie_head_id . " AND z.status=3 AND z.kompletowana_paleta_id!=0";
    $result = $db->mGetResult($sql);

    $ilosc = 0;

    foreach ($result as $index => $aRow) {
        $ilosc = $aRow['ile_palet'];
    }
    return $ilosc;
}

function zmien_status_rozpocznij_pelne_po_kompletacji($zadanie_head_id, $db) {

    $sql_ins_zad = "update zadania_dane z set status=1,wysokie=0
where zadanie_head_id=$zadanie_head_id
and z.status=6 and kompletacja=0 ";
    //echo "<br>" . $sql_ins_zad;
    $result_zadanie4 = $db->mGetResult($sql_ins_zad);
}

function sprawdz_czy_wszystkie_kompletowane_sa_skompletowane($zadanie_head_id, $db) {
    $wynik = "NIE";
    $sql_ins_zad = "SELECT sum(if(z.status=3,1,0)) as kompletacja, sum(1) as wszystkie FROM zadania_dane z WHERE kompletacja=1 and zadanie_head_id=" . $zadanie_head_id;
    //echo "<br>" . $sql_ins_zad;
    $result_zadanie4 = $db->mGetResult($sql_ins_zad);
    if (!empty($result_zadanie4)) {
        if ($result_zadanie4[0]['kompletacja'] == $result_zadanie4[0]['wszystkie']) {
            $wynik = "TAK";
        }
    }
    //print_r($result_zadanie4);
//    $wynik = empty($result_zadanie4) ? 0 : count($result_zadanie4[0]);
    return $wynik;
}

//echo '<komunikat>', $komunikat, '</komunikat></dane>'; //htmlentities(    mb_convert_encoding($komunikat, "UTF-8","ISO-8859-2")
//
//echo $komunikat;
//echo '<ilosc_pozostala>', htmlentities($ilosc_pozostala), '</ilosc_pozostala>';
//echo '<nowa_etykieta>', htmlentities($nowa_etykieta), '</nowa_etykieta>';
//echo '<kod>', htmlentities($kod), '</kod>';
//echo '</dane>';
?>