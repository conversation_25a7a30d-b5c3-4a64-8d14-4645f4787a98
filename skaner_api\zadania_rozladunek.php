<?php

include_once 'Db.class.php';
include_once 'funkcje.inc';
$db = new Db();

$imie_nazwisko = plznaki($_GET['imie_nazwisko']);
$akcja = $_GET['akcja'];
$pracownik_id = $_GET['pracownik_id'];

$komunikat = "OK";
$start_time = microtime(true);


$zadania_dane_id = 0;
if (!empty($_GET['zadania_dane_id'])) {
    $zadania_dane_id = $_GET['zadania_dane_id'];
}

$wysokie = 0;

if (!empty($_GET['wysokie'])) {
    $wysokie = $_GET['wysokie'];
}
$kompletacja = 0;

if (!empty($_GET['kompletacja'])) {
    $kompletacja = $_GET['kompletacja'];
}

if(empty($_GET['stanowisko_id'])) {
    $arr = array('komunikat' => "Wybierz stanowisko pracy");
    xml_from_indexed_array($arr);
    exit()   ;
}


//header('Content-type: text/xml');
//echo '<dane>';


if ($akcja == "wyszukiwanie") {
    if (!empty($_GET['stanowisko_id'])) {
        $stanowisko_id = $_GET['stanowisko_id'];
    }

    $zadania_dane_id_local = 0;
    $baza_danych_local = "";
    $status_wymagany = "";
    if (!empty($_GET['status_wymagany'])) {
        $status_wymagany = " and zd.status=" . $_GET['status_wymagany'];
        if ($_GET['status_wymagany'] != 2) {
            $status_wymagany .= "  and zd.wysokie=" . $wysokie . "  "; //and zd.kompletacja=" . $kompletacja . "
        }
    } else {

        $status_wymagany = "  and zd.wysokie=" . $wysokie . "  "; //and zd.kompletacja=" . $kompletacja . "
    }

    if (!empty($_GET['zadanie_head_id_wymagany'])) {
        $zadanie_head_id_wymagany = " and zh.id=" . $_GET['zadanie_head_id_wymagany'];
    } else {
        $zadanie_head_id_wymagany = "";
    }

    //sprawdzaj_przydzielaj_zadanie($pracownik_id, $wysokie, $db);
    sprawdzaj_przydzielaj_zadanie($imie_nazwisko, $wysokie, $kompletacja, $stanowisko_id, $db);

    if ($stanowisko_id == "7" || $stanowisko_id == "8") {
        $stanowisko_id = "5";
    }
    //    CASE
    //              WHEN zd.stanowisko_id = ' . $stanowisko_id . ' THEN 1
    //              ELSE 0
    //            END DeSC,

    $sql = 'SELECT  zh.baza_danych,zd.id,zh.system_id,zh.docin_id_wew,zh.docout_id_wew,zh.typ,zd.zadanie_head_id
            FROM wmsgg.zadania_dane zd
            LEFT JOIN zadania_head AS zh ON zh.id=zd.zadanie_head_id
            LEFT JOIN zadania_doc_type AS zdt ON zh.doc_type=zdt.id
            LEFT JOIN zadania_typ AS zt ON zh.typ=zt.id
            LEFT JOIN pracownicy AS p ON zd.przydzielenie_pracownik_id=p.id
            LEFT JOIN pracownicy AS p2 ON zd.realizacja_pracownik_id=p2.id
            LEFT JOIN miejsca AS m ON zd.stare_m=m.id
            LEFT JOIN kody  AS k ON k.id=zd.kod_id


            WHERE p.imie_nazwisko="' . $imie_nazwisko . '"  
              AND (zd.status=1 or zd.status=2 or zd.status=5 or zd.status=8 or zd.status=21) and zd.stanowisko_id = ' . $stanowisko_id . ' ' . $status_wymagany . ' ' . $zadanie_head_id_wymagany . '
              AND zh.status_dokumentu>0
            GROUP BY zd.id
            ORDER BY IF(zd.status=2 or zd.status=21,1,0) desc,zh.rozpoczete DESC, zh.priorytet DESC,
            
            if(zh.planowany_czas_realizacji is null,DATE_ADD(zh.ts, INTERVAL +24 HOUR),
            zh.planowany_czas_realizacji) ASC, 
            zh.zadania_head_rodzic_id ASC,
            zd.pominiecie ASC,
            zd.kompletacja desc,
            
            
            
            
            floor(m.regal/2) desc, m.miejsce ASC,m.poziom  LIMIT 1 '; // if(k.ilosc_szt_palecie>30,5000,k.ilosc_szt_palecie) ASC,
    //floor(m.regal/2) desc,if(((floor(m.regal/2)%2)="1"),(300-m.miejsce),m.miejsce) desc,
    //echo $sql;


    //    CASE
    //              WHEN ((zd.ilosc/ilosc_szt_palecie)*100>70) THEN 2
    //              ELSE 1
    //            END DESC,            
    //            CASE
    //              WHEN (k.waga_szt_kg>30) THEN 2
    //              ELSE 1
    //            END desc,            
    //            CASE
    //              WHEN ((zd.ilosc/ilosc_szt_palecie)*100>70) THEN 3
    //              WHEN ((zd.ilosc/ilosc_szt_palecie)*100 between 30 and 70) THEN 2
    //              ELSE 1
    //            END DESC,
    $result = $db->mGetResultAsXML($sql);

    $ile = count($result);

    if ($ile > 0) {



        foreach ($result as $key => $value) {
            $baza_danych_local = $value["baza_danych"];
            $zadania_dane_id_local = $value["id"];
            $system_id = $value["system_id"];





            if ((empty($value["docin_id_wew"]) || empty($value["docout_id_wew"])) && $value["typ"] == "4") {
                $kontrah_wew_id = get_kontrah_wew($baza_danych_local, $system_id, $db);
                $pracownik_id = get_pracownik($baza_danych_local, $imie_nazwisko, $db);
                $numer = docnumber_increment($baza_danych_local, "PP", $db);
                $docout_id = tworz_dokument_docout($baza_danych_local, "PP-", $numer, $pracownik_id, $kontrah_wew_id, $db);
                $docin_id = tworz_dokument_docin($baza_danych_local, "PP", $numer, $pracownik_id, $kontrah_wew_id, $db);
                $sql = "update zadania_head set docin_id_wew=" . $docin_id . ",docout_id_wew=" . $docout_id . " where id=" . $value["zadanie_head_id"];
                //echo $sql;
                $result = $db->mGetResultAsXML($sql);
            }
        }

        if (!empty($zadania_dane_id)) {
            $zadania_dane_id_local = $zadania_dane_id;
        }




        $result = pobierz_zadanie_global($zadania_dane_id_local, $baza_danych_local, $db);


        if (count($result) > 0 && empty($result[0]['stop'])) {
            $sql = ' update  zadania_dane z set  z.start=NOW() WHERE z.status!=2 and z.id=' . $result[0]['id'];
            //echo "<br>" . $sql;
            $result8 = $db->mGetResultAsXML($sql);
        }
    }
    $komunikat .= $db->errors;
    //    echo '<komunikat>', htmlentities($komunikat), '</komunikat>';
    //    echo '<zadan>', htmlentities($ile), '</zadan>';
    $sql = 'select now() as czas_aktualny';
    //echo "<br>" . $sql;
    $czas_aktualny = $db->mGetResultAsXML($sql);
    $arr = array(
        'komunikat' => $komunikat,
        'zadan' => $ile,
        'czas_aktualny' => $czas_aktualny[0]['czas_aktualny']
    );

    foreach ($result as $index => $aRow) {
        //$typ = $aRow['typ'];
        //echo "aaa".$aRow['id'];
        $arr = array_merge($arr, $aRow);

        //echo "aaa";
    }


    if (!empty($arr['typ'])) {
        if ($arr['typ'] == "4") {
            $sql = 'SELECT logo FROM ' . $arr['baza_danych'] . '.delivery d
left join ' . $arr['baza_danych'] . '.kontrah k on d.dl_kontrah_id=k.id
where d.id=' . $arr['doc_id'] . '';
            $result2 = $db->mGetResultAsXML($sql);
            foreach ($result2 as $index => $aRow2) {
                //$typ = $aRow['typ'];
                //echo "aaa".$aRow['id'];
                $arr = array_merge($arr, $aRow2);
                //echo "aaa";
            }
        }

        //blokada by nie wymagał EAN
        //        $arr["ean"] = "";
        //        $arr["ean_jednostki"] = "";
        //        $arr["ean_opakowanie_zbiorcze"] = "";
    }




    //    if (!empty($arr['typ'])) {
    //        if ($arr['typ'] == "7") {
    //            $arr['baza_danych'] = "wmsgg";
    //            $sql = 'SELECT concat(doc_type," ",doc_nr) as dokument,doc_nr FROM ' . $arr['baza_danych'] . '.docin d
    //where d.id=' . $arr['doc_id'] . ' limit 1;';
    //            //echo $sql;
    ////            $tmpp = array('komunikat' => $sql);
    ////        return xml_from_indexed_array($tmpp);
    //
    //            $result2 = $db->mGetResultAsXML($sql);
    //            foreach ($result2 as $index => $aRow2) {
    //                $arr = array_merge($arr, $aRow2);
    //            }
    //        }
    //        if ($arr['typ'] == "6") {
    //            $arr = array_merge($arr, array('dokument' => $arr['doc_type_nazwa'] . " " . $arr['doc_id']));
    //        }
    //    }


    if (!empty($arr['status'])) {
        if ($arr['status'] == '1' || $arr['status'] == '2' || $arr['status'] == '8') {
            $arr["rodzaj_wymaganej_etykiety_odkladczej"] = "miejsce";
            $sql = 'SELECT sum(if(status=3,1,0)) as ile_zrealizowanych FROM zadania_dane z WHERE z.zadanie_head_id=' . $arr['zadanie_head_id'];
            //echo sql;
            $result2 = $db->mGetResultAsXML($sql);
            //print_r($result2);
            if ($result2[0]['ile_zrealizowanych'] != "0") {
                $arr["rodzaj_wymaganej_etykiety_odkladczej"] = "poprzednia_etykieta";
            }
        }
    }

    // End clock time in seconds 
    // Calculate script execution time 
    //$start_time = microtime(true);
    $end_time = microtime(true);
    $execution_time = intval($end_time - $start_time);
    if ($execution_time > 0) {
        $sql = "INSERT INTO czas_wykonywania(nazwa_funkcji, ts, sekund)
                        values
                        ('zadania_rozladunek',NOW(),'" . $execution_time . "');";
        $result7 = $db->mGetResultAsXML($sql);
    }




    xml_from_indexed_array($arr);
}





//
//echo $komunikat;
//echo '<ilosc_pozostala>', htmlentities($ilosc_pozostala), '</ilosc_pozostala>';
//echo '<nowa_etykieta>', htmlentities($nowa_etykieta), '</nowa_etykieta>';
//echo '<kod>', htmlentities($kod), '</kod>';
