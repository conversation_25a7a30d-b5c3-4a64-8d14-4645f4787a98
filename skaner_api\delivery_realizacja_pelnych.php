<?php

include_once 'Db.class.php';
include_once 'funkcje.inc';
//error_reporting(E_ALL);
//ini_set('display_errors', 1);

$db = new Db();

// todo do zablokowania statusy



$imie_nazwisko = plznaki($_GET['imie_nazwisko']);
$akcja = $_GET['akcja'];
$zadanie_dane_id = $_GET['zadanie_dane_id'];

$komunikat = "OK";
$etykieta_scan = $_GET['etykieta_scan'];

$czy_koniec_dokumentu = "NIE"; // dosłownie zadania nie kompletacji
//header('Content-type: text/xml');
//echo '<dane>';
//http://25.56.91.22/wmsrawa/public/skaner_api/delivery_realizacja_kompletacji.php?akcja=realizacja_zadania&zadanie_dane_id=1760&baza_danych=wmsgg&etykieta_id_realizowana=9969437&imie_nazwisko=Lukasz%20Domanski&ilosc_pobierana=3


if ($akcja == "realizacja_zadania") {



    //echo "<br>" . $sql . "<br>";

    $result = pobierz_zadanie_global($zadanie_dane_id, "", $db);

    if (count($result) == 0) {
        $komunikat = "Brak informacji o zadaniu";
        return show_komunikat_xml($komunikat);
    }

    $aRowZadanie = $result[0];
    $baza_danych = $aRowZadanie['baza_danych'];

    // najpotrzebniejsze etykieta ta sama i ilość realizowana



    if ($aRowZadanie['status'] != "1") {
        $komunikat = "Zadanie zostalo zrealizowane . Przerywam operacje";
        //echo $komunikat;
        return show_komunikat_xml($komunikat);
    }

    $pracownik_id = szukaj_pracownik($imie_nazwisko, $baza_danych, $komunikat, $db);


    $tmp_arr = sprawdz_szukana_etykiete_WMS($etykieta_scan, $aRowZadanie, $komunikat, $baza_danych, $db);


    if (empty($tmp_arr['aRowEtWms'])) {
        $komunikat = "Nie znaleziono etykiety " . $etykieta_scan;
        return show_komunikat_xml($komunikat);
    }
    if ($tmp_arr['ilosc_pozycji_etykiety'] > 1) {

        return xml_from_indexed_array(array('komunikat' => $komunikat, 'ilosc_pozycji_etykiety' => $tmp_arr['ilosc_pozycji_etykiety'], 'etykiety' => $tmp_arr['aRowEtWms']));
    }
//echo "Koniec";
//    return;
//    echo "Koniec2";
    $aRowEtWms = $tmp_arr['aRowEtWms'];
    $komunikat = $tmp_arr['komunikat'];
    $etykieta_id_realizowana = $aRowEtWms['id'];

    if ($komunikat != "OK") {
        return show_komunikat_xml($komunikat);
    }



    if ($aRowZadanie['etykieta_id'] != $etykieta_id_realizowana) { //czy jest odpowiednia ilość na etykiecie
        $zadanie_szukana_etykieta = szukaj_etykiete_zadan_dl($etykieta_id_realizowana, $baza_danych, $db);





        if (count($zadanie_szukana_etykieta) == 0) { // sprawdź zgodność  towaru
            $sprawdz_zgodnosc_etykieta_zadanie_komunikat = sprawdz_zgodnosc_etykieta_zadanie($aRowZadanie, $aRowEtWms);

            if ($sprawdz_zgodnosc_etykieta_zadanie_komunikat != "") {
                return show_komunikat_xml($sprawdz_zgodnosc_etykieta_zadanie_komunikat);
            } else {

                podmiana_etykiety_zadania($aRowZadanie, $aRowEtWms['id'], $db);
            }
            //return "KOniec";
        } else {
            $zadanie_szukana_etykieta = $zadanie_szukana_etykieta[0];
            if ($zadanie_szukana_etykieta['status'] != "1") {
                $komunikat = "Etykieta zostala juz zrealizowana innym zadaniem. Przerywam operacje";
                //echo $komunikat;
                return show_komunikat_xml($komunikat);
            }



            if ($zadanie_szukana_etykieta['zadanie_head_id'] != $aRowZadanie['zadanie_head_id']) {
                $komunikat = "Etykieta jest zaplanowane przez inna DL " . $zadanie_szukana_etykieta['doc_id'] . " . Przerywam operacje"; //echo "<br>" . $komunikat;
                return show_komunikat_xml($komunikat);
            } else {

                $result = pobierz_zadanie_global($zadanie_szukana_etykieta['id'], $baza_danych, $db);

                foreach ($result as $index => $wynik) {
//                    echo "<pre>";
//                    print_r($wynik);
//                    echo "</pre>";
//                    echo "<pre>";
//                    print_r($aRowZadanie);
//                    echo "</pre>";
                    podmiana_rozpoczecia_zadania($wynik, $aRowZadanie, $db);
                    $aRowZadanie = array();
                    $aRowZadanie = $wynik;
                    $baza_danych = $aRowZadanie['baza_danych'];
                }
            }
        }
    }





    // tutaj będzie realizacja etykiety
    if ($aRowZadanie['status'] != "1") {
        $komunikat = "Zadanie zostalo zrealizowane . Przerywam operacje";
        //echo $komunikat;
        return show_komunikat_xml($komunikat);
    }


//    //zmienić czasy start przy zmienianych etykietach
//    //                echo "<br>".$aRowZadanie['etykieta_id'];
//    //                $komunikat = "-----PRZERYWNIK-------";
//    //                //echo "<br>" . $komunikat;
//    //                return;
//    //return;




    //$komunikat .= //mysql_error();
    if ($komunikat == "OK") {
        $sql = "insert into $baza_danych.dlcollect(nr_dl,nr_et,system_id) values('" . $aRowZadanie['doc_id'] . "','" . $aRowEtWms['id'] . "','" . $aRowZadanie['system_id'] . "') ";
        $result3 = $db->mGetResultAsXML($sql);

        $sql = ' update  zadania_dane z set z.realizacja_pracownik_id=' . $pracownik_id . ', z.stop=NOW(),status=2,kompletowana_paleta_id=' . $aRowEtWms['paleta_id'] . ' WHERE z.id=' . $aRowZadanie['id'];
        //echo "<br>" . $sql;
        $result8 = $db->mGetResultAsXML($sql);


        $sql = 'SELECT count(1)  as ile_wszystkich, sum(if(z.status!=1,1,0)) as ile_gotowe FROM zadania_dane z WHERE z.zadanie_head_id=' . $aRowZadanie['zadanie_head_id'] . '  ';
        $result6 = $db->mGetResultAsXML($sql);
        $ile_wszystkich = 0;
        $ile_gotowe = 0;
        //$komunikat = $sql;
//            echo "<pre>";
//        echo print_r($result6);
//        echo "</pre>";
        foreach ($result6 as $index => $aRowZadanie6) {
            if ($aRowZadanie6["ile_gotowe"] == $aRowZadanie6["ile_wszystkich"]) { //to jest koniec kompletacji
                $czy_koniec_dokumentu = "TAK";
            }
            $ile_wszystkich = $aRowZadanie6["ile_wszystkich"];
            $ile_gotowe = $aRowZadanie6["ile_gotowe"];
        }

        return xml_from_indexed_array(array('komunikat' => $komunikat, 'ilosc_pozycji_etykiety' => $tmp_arr['ilosc_pozycji_etykiety'], 'czy_koniec_kompletacji' => $czy_koniec_dokumentu, 'ile_wszystkich' => $ile_wszystkich, 'ile_gotowe' => $ile_gotowe));
    }
}

function podmiana_rozpoczecia_zadania($aRowZadanieNew, $aRowZadanieOld, $db) {
    $sql = 'update  zadania_dane z set z.start="' . $aRowZadanieOld['start'] . '", z.przydzielenie_pracownik_id="' . $aRowZadanieOld['przydzielenie_pracownik_id'] . '" where z.id=' . $aRowZadanieNew['id'] . ' limit 1';
    //echo "<br>" . $sql;
    $result8 = $db->mGetResultAsXML($sql);

    $sql = 'update  zadania_dane z set z.start=null where z.id=' . $aRowZadanieOld['id'] . ' limit 1';
    //echo "<br>" . $sql;
    $result8 = $db->mGetResultAsXML($sql);
}

function podmiana_etykiety_zadania($aRowZadanie, $etykieta_id_realizowana, $db) {
    $sql = "update " . $aRowZadanie['baza_danych'] . ".delivery_et d set d.etykieta_id=$etykieta_id_realizowana WHERE d.delivery_id=" . $aRowZadanie['doc_id'] . " AND d.etykieta_id=" . $aRowZadanie['etykieta_id'] . " and ilosc_zamawiana=" . $aRowZadanie['ilosc'] . " limit 1";
    //echo "<br>" . $sql;
    $result7 = $db->mGetResultAsXML($sql);

    $sql = 'update  zadania_dane z set z.etykieta_id=' . $etykieta_id_realizowana . ' where z.id=' . $aRowZadanie['id'] . ' limit 1';
    //echo "<br>" . $sql;
    $result8 = $db->mGetResultAsXML($sql);
}

function sprawdz_szukana_etykiete_WMS($etykieta_id_realizowana, $aRowZadanie, $komunikat, $baza_danych, $db) {

    if (substr($etykieta_id_realizowana, 0, 2) == "DS") {
        $sql = 'select e.id,e.etykieta_klient,k.kod,e.lot,e.active,TRIM(TRAILING "." FROM TRIM(TRAILING "0" from e.ilosc)) as ilosc,dl.nr_dl,e.paleta_id,ss.nazwa as status_system_nazwa, ss.funkcja_stat from ' . $baza_danych . '.etykiety e left join ' . $baza_danych . '.kody k on e.kod_id=k.id left join ' . $baza_danych . '.dlcollect dl ' . ' on dl.nr_et=e.id left join ' . $baza_danych . '.status_system ss  on e.status_id=ss.id  where e.active=1 and  e.paleta_id=' . str_replace("DS", "", $etykieta_id_realizowana) . ' and e.system_id=' . $aRowZadanie['system_id'];
    } else {
        $sql = 'select e.id,e.etykieta_klient,k.kod,e.lot,e.active,TRIM(TRAILING "." FROM TRIM(TRAILING "0" from e.ilosc)) as ilosc,dl.nr_dl,e.paleta_id,ss.nazwa as status_system_nazwa, ss.funkcja_stat from ' . $baza_danych . '.etykiety e left join ' . $baza_danych . '.kody k on e.kod_id=k.id left join ' . $baza_danych . '.dlcollect dl ' . ' on dl.nr_et=e.id left join ' . $baza_danych . '.status_system ss  on e.status_id=ss.id  where e.active=1 and  (e.id="' . $etykieta_id_realizowana . '" or e.etykieta_klient="' . $etykieta_id_realizowana . '") and e.system_id=' . $aRowZadanie['system_id'];
    }

    $result2 = $db->mGetResultAsXML($sql);
    $aRowEtWms = array();

    if (count($result2) > 1) {
        return array('komunikat' => $komunikat, 'aRowEtWms' => $result2, 'ilosc_pozycji_etykiety' => count($result2));
    }
    //echo "<br>" . $sql;
    foreach ($result2 as $index => $aRowEtWms) {
        if ($aRowEtWms['active'] != "1") {
            $komunikat = "Etykieta jest nieaktywna. Przerywam operacje";
            //echo "<br>" . $komunikat;
            //return show_komunikat_xml($komunikat);
        }

        if (!empty($aRowEtWms['nr_dl'])) {
            $komunikat = "Etykieta jest wczytana na DL" . $aRowEtWms['nr_dl'] . "";
            //echo "<br>" . $komunikat;
            //return show_komunikat_xml($komunikat);
        }
        if (!empty($aRowEtWms['funkcja_stat'])) {
            $komunikat = "Etykieta jest w statusie: " . $aRowEtWms['status_system_nazwa'] . ". Przerywam operacje";
            //echo "<br>" . $komunikat;
            //return show_komunikat_xml($komunikat);
        }

        if ($aRowZadanie['ilosc'] != $aRowEtWms['ilosc']) { //czy jest odpowiednia ilość na etykiecie
            $komunikat = "Ilosc na etykiecie " . $aRowEtWms['ilosc'] . " jest inna niz zamawiana " . $aRowZadanie['ilosc'] . ". Przerywam operacje";
            //echo "<br>" . $komunikat;
            //return show_komunikat_xml($komunikat);
        }
    }
    return array('komunikat' => $komunikat, 'aRowEtWms' => $aRowEtWms, 'ilosc_pozycji_etykiety' => count($result2));
}

function szukaj_etykiete_zadan_dl($etykieta_id, $baza_danych, $db) {

    if (strpos($etykieta_id, 'DS') !== false) {
        $conditionlabel = '  ( zd.paleta_id=' . str_replace("DS", "", $etykieta_id) . ' ) ';
    } else {
        $conditionlabel = '  (zd.etykieta_id=' . $etykieta_id . ' or zd.etykieta_klient="' . $etykieta_id . '") ';
    }

    $sql = 'SELECT zd.id AS id,k.kod,zd.etykieta_klient,
TRIM(TRAILING "." FROM TRIM(TRAILING "0" from zd.ilosc)) as ilosc ,

k.kod_nazwa,k.ean,k.ean_jednostki, k.ilosc_w_opakowaniu, 
(k.ilosc_w_opakowaniu*TRIM(TRAILING "." FROM TRIM(TRAILING "0" from zd.ilosc))) as ilosc_opakowan,
zd.zadanie_head_id AS zadanie_head_id,
zd.status AS status,
zd.stare_m AS stare_m, zd.nowe_m AS nowe_m,
zd.nowe_m_realizowane AS nowe_m_realizowane,
zd.paleta_id AS paleta_id, zd.etykieta_id AS etykieta_id,
zd.kod_id AS kod_id, zd.lot AS lot,
zh.baza_danych AS baza_danych, zh.system_id AS system_id, zh.typ AS typ,
zh.doc_id AS doc_id, zh.doc_type AS doc_type,
zdt.nazwa AS doc_type_nazwa,
zh.zgodnosc_miejsca AS zgodnosc_miejsca, zh.zgodnosc_towaru AS zgodnosc_towaru,
zt.nazwa AS zadanie_typ_nazwa,
concat("H: ",m1.hala," ",m1.regal,"-",m1.miejsce,"-",m1.poziom ) AS stare_m_nazwa,
concat("H: ",m2.hala," ",m2.regal,"-",m2.miejsce,"-",m2.poziom ) AS nowe_m_nazwa,
s.nazwa AS system_id_nazwa,


  zd.czas_przydzielenia AS czas_przydzielenia,
zd.przydzielenie_pracownik_id AS przydzielenie_pracownik_id,
zd.start AS start, zd.stop AS stop,
zd.realizacja_pracownik_id AS realizacja_pracownik_id,
p2.imie_nazwisko AS realizacja_imie_nazwisko,

 zd.stanowisko_id AS stanowisko_id, zd.kierunek AS kierunek,zd.kompletacja,
 zh.priorytet AS priorytet,
 p.imie_nazwisko AS przydzielenie_imie_nazwisko,
 (SELECT count(1) AS ile_wszystkich
   FROM zadania_dane z
   WHERE z.zadanie_head_id=zh.id) AS ile_wszystkich,
  (SELECT count(1) AS ile_gotowe
   FROM zadania_dane z
   WHERE z.zadanie_head_id=zh.id
     AND status!=1) AS ile_gotowe
 FROM zadania_dane zd 
 LEFT JOIN zadania_head AS zh ON zh.id=zd.zadanie_head_id 
 LEFT JOIN zadania_doc_type AS zdt ON zh.doc_type=zdt.id
         LEFT JOIN zadania_typ AS zt ON zh.typ=zt.id 
         LEFT JOIN ' . $baza_danych . '.miejsca AS m1 ON m1.id=zd.stare_m 
         LEFT JOIN ' . $baza_danych . '.miejsca AS m2 ON m2.id=zd.nowe_m
         LEFT JOIN ' . $baza_danych . '.pracownicy AS p ON zd.przydzielenie_pracownik_id=p.id 
         LEFT JOIN ' . $baza_danych . '.pracownicy AS p2 ON zd.realizacja_pracownik_id=p2.id
         LEFT JOIN ' . $baza_danych . '.systemy AS s ON s.wartosc=zh.system_id
left join ' . $baza_danych . '.kody k on zd.kod_id=k.id
            where ' . $conditionlabel . ' and zh.typ=4 and zh.baza_danych="' . $baza_danych . '" order by zd.id desc limit 1';
    $result = $db->mGetResultAsXML($sql);
    //echo $sql;
    if (count($result) > 0) {
        $sql = ' update  zadania_dane z set  z.start=NOW() WHERE z.id=' . $result[0]['id'];
        //echo "<br>" . $sql;
        $result8 = $db->mGetResultAsXML($sql);
    }

    return $result;
}

function sprawdz_zgodnosc_etykieta_zadanie($zadanie_arr, $etykieta_arr) {
    $wynik = "";
    if ($zadanie_arr['ilosc'] != $etykieta_arr['ilosc']) {
        $wynik.="Niezgodna ilosc zamawiana:" . $zadanie_arr['ilosc'] . " a realizowana: " . $etykieta_arr['ilosc'];
    }

    if (empty($zadanie_arr['zgodnosc_towaru'])) {
        $zadanie_arr['zgodnosc_towaru'] = 4;
    }

    if ($zadanie_arr['zgodnosc_towaru'] == '1') {
        if ($zadanie_arr['kod'] != $etykieta_arr['kod']) {
            $wynik.="Niezgodny kod zamawiany:" . $zadanie_arr['kod'] . " a realizowany: " . $etykieta_arr['kod'];
        }
    }
    if ($zadanie_arr['zgodnosc_towaru'] == '2') {
        if ($zadanie_arr['etykieta_klient'] != $etykieta_arr['etykieta_klient']) {
            $wynik.="Niezgodny etykieta_klient zamawiany:" . $zadanie_arr['etykieta_klient'] . " a realizowany: " . $etykieta_arr['etykieta_klient'];
        }
    }
    if ($zadanie_arr['zgodnosc_towaru'] == '3') {

        if ($zadanie_arr['kod'] != $etykieta_arr['kod']) {
            $wynik.="Niezgodny kod zamawiany:" . $zadanie_arr['kod'] . " a realizowany: " . $etykieta_arr['kod'];
        }
        if ($zadanie_arr['lot'] != $etykieta_arr['lot']) {
            $wynik.="Niezgodny lot zamawiany:" . $zadanie_arr['lot'] . " a realizowany: " . $etykieta_arr['lot'];
        }
    }
    if ($zadanie_arr['zgodnosc_towaru'] == '4') {
        //echo  "Niezgoda etykieta zamawiany:" . $zadanie_arr['etykieta_id'] . " a realizowany: " . $etykieta_arr['id'];
        if ($zadanie_arr['kod'] != $etykieta_arr['kod']) {
            $wynik.="Niezgodny kod zamawiany:" . $zadanie_arr['kod'] . " a realizowany: " . $etykieta_arr['kod'];
        }
        if ($zadanie_arr['lot'] != $etykieta_arr['lot']) {
            $wynik.="Niezgodny lot zamawiany:" . $zadanie_arr['lot'] . " a realizowany: " . $etykieta_arr['lot'];
        }
        if ($zadanie_arr['etykieta_id'] != $etykieta_arr['id']) {
            $wynik.="Niezgodna etykieta zamawiany:" . $zadanie_arr['etykieta_id'] . " a realizowany: " . $etykieta_arr['id'];
        }
    }
    return $wynik;
}

function szukaj_pracownik($imie_nazwisko, $baza_danych, $komunikat, $db) {
    $sql = 'SELECT id FROM ' . $baza_danych . '.pracownicy p WHERE p.imie_nazwisko="' . $imie_nazwisko . '" limit 1; ';
    $result2 = $db->mGetResultAsXML($sql);
    //echo "<br>" . $sql;
    foreach ($result2 as $index => $aRowp) {
        $pracownik_id = $aRowp['id'];
    }
    if (empty($pracownik_id)) {
        $komunikat = "Nie znaleziono pracownika: " . $imie_nazwisko . ". Przerywam operacje";
        ////echo "<br>" . $komunikat;
        return $komunikat;
    }
    return $pracownik_id;
}

?>