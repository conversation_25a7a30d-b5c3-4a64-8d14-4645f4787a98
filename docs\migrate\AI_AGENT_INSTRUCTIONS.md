# Instrukcja dla Agenta AI - Migracja Systemu WMS

## Cel Misji
Twoim zadaniem jest przeprowadzenie kompleksowej migracji systemu WMS (Warehouse Management System) z obecnego stosu technologicznego (aplikacja C# Windows Mobile + backend PHP) na nowoczesną platformę .NET z wykorzystaniem:
- **Frontend**: .NET MAUI (Multi-platform App UI)
- **Backend**: .NET Core/ASP.NET Core Web API
- **Baza danych**: MySQL 8.0 (bez zmian)

## Kontekst Biznesowy

### Obecny System
System WMS obsługuje procesy magazynowe w kilku lokalizacjach, wykorzystując:
- Skanery Zebra z systemem Android i DataWedge
- Aplikację mobilną napisaną w C# dla Windows Mobile
- Backend PHP z wieloma endpointami API
- Bazę danych MySQL 8.0
- Integrację z systemami zewnętrznymi (ERP, drukarki etykiet)

### Kluczowe Procesy Biznesowe
1. **Przyjęcia magazynowe** - a<PERSON><PERSON><PERSON>, rozładunek, etykietowanie
2. **Składowanie** - alokacja miejsc, przenoszenia, inwentaryzacja
3. **Kompletacja zamówień** - picking, packing, konsolidacja
4. **Wydania magazynowe** - załadunek, dokumenty WZ
5. **Zarządzanie etykietami** - generowanie, drukowanie, śledzenie

## Architektura Docelowa

### Backend (.NET Core)
```
/WMS.Api
├── /src
│   ├── /WMS.Domain           # Logika domenowa, encje
│   ├── /WMS.Application       # Warstwa aplikacyjna, CQRS
│   ├── /WMS.Infrastructure    # Implementacje, integracje
│   └── /WMS.Api              # Kontrolery REST API
├── /tests
└── /docs
```

#### Wymagania Architektury Backend:
1. **Clean Architecture/Onion Architecture**
   - Separacja warstw (Domain, Application, Infrastructure, API)
   - Dependency Injection
   - Repository Pattern + Unit of Work

2. **CQRS Pattern** (Command Query Responsibility Segregation)
   - Osobne modele dla odczytu i zapisu
   - MediatR do obsługi komend i zapytań

3. **Modularność** (Bounded Contexts)
   - Moduł Przyjęć (Receiving)
   - Moduł Składowania (Storage)
   - Moduł Kompletacji (Picking)
   - Moduł Wydań (Shipping)
   - Moduł Etykiet (Labels)
   - Moduł Inwentaryzacji (Inventory)

4. **API RESTful**
   - Wersjonowanie API (api/v1/, api/v2/)
   - Swagger/OpenAPI dokumentacja
   - JWT Authentication
   - Rate limiting i throttling

### Frontend (.NET MAUI)
```
/WMS.Mobile
├── /src
│   ├── /WMS.Mobile            # Główny projekt MAUI
│   │   ├── /Views             # Widoki (Pages)
│   │   ├── /ViewModels        # MVVM ViewModels
│   │   ├── /Models            # Modele danych
│   │   ├── /Services          # Serwisy (API, Scanner)
│   │   ├── /Controls          # Kontrolki custom
│   │   └── /Platforms         # Kod specyficzny dla platform
│   │       └── /Android       # Integracja DataWedge
├── /tests
└── /docs
```

#### Wymagania Architektury Frontend:
1. **MVVM Pattern**
   - Data Binding
   - Commands
   - Dependency Injection (Microsoft.Extensions.DependencyInjection)

2. **Integracja ze Skanerem**
   - DataWedge API dla Zebra Android
   - Broadcast Receivers dla skanów
   - Profile DataWedge dla różnych ekranów

3. **Offline First**
   - SQLite lokalna baza
   - Synchronizacja z API
   - Queue dla operacji offline

4. **Nawigacja**
   - Shell Navigation
   - Deep linking
   - Tabs i Flyout menu

## Mapowanie Funkcjonalności

### Moduły do Migracji (Priorytet)

#### Faza 1: Podstawowe Operacje
1. **Logowanie i Autoryzacja**
   - Źródło: `skaner_api/login.php`
   - Cel: `WMS.Api/Controllers/AuthController.cs`
   - JWT tokens, role-based access

2. **Przyjęcia Magazynowe**
   - Źródło: `skaner_api/awizacja_dostawy_ukladanie.php`
   - Cel: `WMS.Application/Receiving/`
   - Skanowanie, alokacja miejsc, drukowanie etykiet

3. **Zarządzanie Miejscami**
   - Źródło: `skaner_api/wolne_miejsca.php`, `zmiana_miejsca_*.php`
   - Cel: `WMS.Application/Storage/`
   - Wolne miejsca, przenoszenia, blokady

#### Faza 2: Kompletacja i Wydania
1. **Kompletacja Zamówień**
   - Źródło: `skaner_api/delivery_kompletacja_*.php`
   - Cel: `WMS.Application/Picking/`
   - Zadania kompletacji, potwierdzenia, konsolidacja

2. **Wydania i Załadunek**
   - Źródło: `skaner_api/delivery_realizacja_*.php`
   - Cel: `WMS.Application/Shipping/`
   - Realizacja, SSCC, dokumenty

#### Faza 3: Funkcje Zaawansowane
1. **Inwentaryzacja**
   - Źródło: `skaner_api/inwentaryzacja_*.php`
   - Cel: `WMS.Application/Inventory/`

2. **Raporty i Wydruki**
   - Źródło: `skaner_api/wydruki/`
   - Cel: `WMS.Application/Reports/`

## Proces Migracji - Krok po Kroku

### Etap 1: Analiza i Dokumentacja
1. **Przeanalizuj każdy plik PHP w `skaner_api/`**
   - Zidentyfikuj endpointy
   - Udokumentuj parametry wejściowe/wyjściowe
   - Zapisz logikę biznesową

2. **Przeanalizuj każdy formularz w `teresin_Magazyn/`**
   - Mapuj ekrany na Views w MAUI
   - Zidentyfikuj flow nawigacji
   - Udokumentuj walidacje

3. **Stwórz dokumentację workflow**
   - Dla każdego procesu utwórz plik w `docs/migrate/workflow/`
   - Format: `PROCES_nazwa.md`
   - Zawartość: kroki procesu, regułfunkcionalnosci,ow biznesowe, wyjątki

### Etap 2: Implementacja Backend

#### Struktura Projektu
```csharp
// WMS.Domain/Entities/Etykieta.cs
public class Etykieta
{
    public int Id { get; set; }
    public string SystemId { get; set; }
    public string KodId { get; set; }
    public decimal Ilosc { get; set; }
    public string Lot { get; set; }
    public DateTime? DataProdukcji { get; set; }
    public DateTime? DataWaznosci { get; set; }
    public int? MiejsceId { get; set; }
    public bool Active { get; set; }
    // ... pozostałe właściwości
}

// WMS.Application/Labels/Commands/CreateLabelCommand.cs
public class CreateLabelCommand : IRequest<int>
{
    public string SystemId { get; set; }
    public string KodId { get; set; }
    public decimal Ilosc { get; set; }
    // ... parametry
}

// WMS.Api/Controllers/LabelsController.cs
[ApiController]
[Route("api/v1/[controller]")]
public class LabelsController : ControllerBase
{
    [HttpPost]
    public async Task<ActionResult<int>> CreateLabel(CreateLabelCommand command)
    {
        // implementacja
    }
}
```

### Etap 3: Implementacja Frontend

#### Struktura MAUI
```csharp
// WMS.Mobile/Views/Receiving/ReceivingPage.xaml
<ContentPage xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
             x:Class="WMS.Mobile.Views.Receiving.ReceivingPage">
    <VerticalStackLayout>
        <Label Text="Skanuj etykietę" />
        <Entry x:Name="BarcodeEntry" 
               Text="{Binding Barcode}" />
        <Button Text="Potwierdź" 
                Command="{Binding ConfirmCommand}" />
    </VerticalStackLayout>
</ContentPage>

// WMS.Mobile/ViewModels/ReceivingViewModel.cs
public class ReceivingViewModel : BaseViewModel
{
    private readonly IReceivingService _receivingService;
    private readonly IScannerService _scannerService;
    
    public ICommand ConfirmCommand { get; }
    public string Barcode { get; set; }
    
    public ReceivingViewModel(IReceivingService receivingService, 
                             IScannerService scannerService)
    {
        _receivingService = receivingService;
        _scannerService = scannerService;
        ConfirmCommand = new Command(async () => await ConfirmReceiving());
    }
}

// WMS.Mobile/Platforms/Android/Services/DataWedgeService.cs
public class DataWedgeService : IScannerService
{
    public void ConfigureDataWedge()
    {
        // Konfiguracja profilu DataWedge
        // Ustawienie Intent Action i Category
        // Mapowanie klawiszy skanera
    }
    
    public void EnableScanning()
    {
        // Włączenie skanowania dla aktywnego widoku
    }
}
```

## Kluczowe Wyzwania i Rozwiązania

### 1. Integracja DataWedge
**Problem**: Komunikacja z aplikacją ze skanera Zebra
**Rozwiązanie**: 
- Użyj Intent Broadcast Receivers w Android
- Stwórz profile DataWedge dla każdego ekranu
- Implementuj `IScannerService` z metodami Enable/Disable

### 2. Synchronizacja Offline
**Problem**: Praca bez połączenia z serwerem
**Rozwiązanie**:
- SQLite dla lokalnego cache
- Queue operacji do synchronizacji
- Conflict resolution strategy

### 3. Migracja Danych
**Problem**: Zachowanie ciągłości pracy podczas migracji
**Rozwiązanie**:
- API Gateway przekierowujący ruch
- Stopniowa migracja moduł po module
- Dual-write podczas okresu przejściowego

## Standardy Kodowania

### Backend (.NET Core)
1. **Nazewnictwo**
   - PascalCase dla klas i metod publicznych
   - camelCase dla zmiennych prywatnych
   - Prefiksy: I dla interfejsów

2. **Struktura**
   - Jedna klasa per plik
   - Foldery odpowiadające namespace
   - Testy w osobnym projekcie

3. **Async/Await**
   - Wszystkie operacje I/O asynchroniczne
   - Suffix "Async" dla metod async

### Frontend (MAUI)
1. **XAML**
   - x:Name w PascalCase
   - Binding z użyciem {Binding}
   - Styles w Resources

2. **ViewModels**
   - Implementacja INotifyPropertyChanged
   - Commands jako ICommand
   - Dependency Injection przez konstruktor

## Testowanie

### Testy Jednostkowe
- xUnit dla testów jednostkowych
- Moq dla mockowania
- FluentAssertions dla asercji

### Testy Integracyjne
- TestServer dla API
- SQLite in-memory dla testów z bazą
- WireMock dla zewnętrznych API

### Testy E2E
- Appium dla testów UI
- Postman/Newman dla API

## Dokumentacja

### Każdy moduł musi zawierać:
1. **README.md** - opis modułu, zależności
2. **API.md** - dokumentacja endpointów
3. **WORKFLOW.md** - procesy biznesowe
4. **MIGRATION.md** - mapowanie starego kodu na nowy

### Format dokumentacji workflow:
```markdown
# Proces: [Nazwa Procesu]

## Opis
[Krótki opis procesu]

## Kroki
1. [Krok 1]
   - Input: [dane wejściowe]
   - Akcja: [co się dzieje]
   - Output: [dane wyjściowe]
   - Walidacja: [reguły]
   
## Reguły Biznesowe
- [Reguła 1]
- [Reguła 2]

## Wyjątki
- [Wyjątek 1]: [jak obsłużyć]

## Mapowanie
- Stary endpoint: `skaner_api/xxx.php`
- Nowy endpoint: `api/v1/xxx`
- Zmiany: [lista zmian]
```

## Metryki Sukcesu

1. **Funkcjonalność**: 100% pokrycie istniejących funkcji
2. **Wydajność**: Response time < 200ms dla 95% requestów
3. **Dostępność**: 99.9% uptime
4. **Skalowalność**: Obsługa 10x obecnego ruchu
5. **Testy**: >80% code coverage
6. **Dokumentacja**: 100% API udokumentowane

## Harmonogram

### Miesiąc 1-2: Analiza i Przygotowanie
- Dokumentacja wszystkich procesów
- Setup środowiska developerskiego
- Proof of Concept dla krytycznych modułów

### Miesiąc 3-4: Implementacja Fazy 1
- Backend: Auth, Receiving, Storage
- Frontend: Podstawowe ekrany
- Integracja DataWedge

### Miesiąc 5-6: Implementacja Fazy 2
- Backend: Picking, Shipping
- Frontend: Kompletacja, wydania
- Testy integracyjne

### Miesiąc 7-8: Implementacja Fazy 3
- Pozostałe moduły
- Migracja danych
- Testy E2E

### Miesiąc 9-10: Stabilizacja
- Bug fixing
- Optymalizacja
- Szkolenia użytkowników

### Miesiąc 11-12: Wdrożenie
- Pilotaż w jednej lokalizacji
- Rollout do wszystkich lokalizacji
- Wsparcie powdrożeniowe

## Ważne Uwagi

1. **Zachowaj kompatybilność wsteczną** podczas okresu przejściowego
2. **Dokumentuj każdą decyzję architektoniczną** w Architecture Decision Records (ADR)
3. **Przeprowadzaj code review** dla każdego PR
4. **Automatyzuj wszystko** co możliwe (CI/CD, testy, deployments)
5. **Komunikuj postępy** regularnie ze stakeholderami

## Kontakt i Wsparcie

W razie pytań lub wątpliwości:
1. Sprawdź dokumentację w `docs/migrate/workflow/`
2. Przejrzyj istniejący kod w `skaner_api/` i `teresin_Magazyn/`
3. Konsultuj się z zespołem przed podjęciem kluczowych decyzji architektonicznych

---

**PAMIĘTAJ**: Celem jest nie tylko przepisanie kodu, ale modernizacja i ulepszenie systemu z zachowaniem wszystkich funkcjonalności biznesowych.
