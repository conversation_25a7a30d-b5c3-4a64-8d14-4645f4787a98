<?php

include_once 'Db.class.php';
include_once 'funkcje.inc';
//error_reporting(E_ALL);
//ini_set('display_errors', 1);

$db = new Db();

// todo do zablokowania statusy



$imie_nazwisko = $_GET['imie_nazwisko'];
$typ_operacji = $_GET['typ_operacji'];
$system_id = $_GET['system_id'];
$wozek = $_GET['wozek'];
$operac_id = $_GET['operac_id'];
$ilosc = $_GET['ilosc'];
$jm = $_GET['jm'];
$akcja = $_GET['akcja'];
$zadanie_dane_id = $_GET['zadanie_dane_id'];

$komunikat = "OK";
$etykieta_id_realizowana = $_GET['etykieta_id_realizowana'];
$paleta_id = $_GET['paleta_id'];


//header('Content-type: text/xml');
//echo '<dane>';
//http://25.56.91.22/wmsrawa/public/skaner_api/delivery_realizacja_kompletacji.php?akcja=realizacja_zadania&zadanie_dane_id=1760&baza_danych=wmsrawa&etykieta_id_realizowana=9969437&imie_nazwisko=Lukasz%20Domanski&ilosc_pobierana=3


if ($akcja == "realizacja_zadania") {

    if (empty($ilosc)) {
        $ilosc = "0";
    }


    //echo "<br>" . $sql . "<br>";

    $result = pobierz_zadanie($zadanie_dane_id, $db);




    if (count($result) > 0) {

        foreach ($result as $index => $aRowZadanie) {
            $baza_danych = $aRowZadanie['baza_danych'];

            // najpotrzebniejsze etykieta ta sama i ilość realizowana









            if ($aRowZadanie['status'] != "1") {
                $komunikat = "Zadanie zostalo zrealizowane . Przerywam operacje";
                //echo $komunikat;
                return show_komunikat_xml($komunikat);
            }


            $sql = 'SELECT id FROM ' . $baza_danych . '.pracownicy p WHERE p.imie_nazwisko="' . $imie_nazwisko . '" limit 1; ';
            $result2 = $db->mGetResult($sql);
            //echo "<br>" . $sql;
            foreach ($result2 as $index => $aRowp) {
                $pracownik_id = $aRowp['id'];
            }
            if (empty($pracownik_id)) {
                $komunikat = "Nie znaleziono pracownika: " . $imie_nazwisko . ". Przerywam operacje";
                ////echo "<br>" . $komunikat;
                return show_komunikat_xml($komunikat);
            }



            $sql = 'select kod,lot,e.active,TRIM(TRAILING "." FROM TRIM(TRAILING "0" from ilosc)) as ilosc,dl.nr_dl,e.paleta_id,ss.nazwa as status_system_nazwa, ss.funkcja_stat from ' . $baza_danych . '.etykiety e left join ' . $baza_danych . '.kody k on e.kod_id=k.id left join ' . $baza_danych . '.dlcollect dl '
                    . ' on dl.nr_et=e.id left join ' . $baza_danych . '.status_system ss  on e.status_id=ss.id  where e.id=' . $etykieta_id_realizowana;
            $result2 = $db->mGetResult($sql);
            //echo "<br>" . $sql;
            foreach ($result2 as $index => $aRowEtWms) {
                if ($aRowEtWms['active'] != "1") {
                    $komunikat = "Etykieta jest nieaktywna. Przerywam operacje";
                    //echo "<br>" . $komunikat;
                    return show_komunikat_xml($komunikat);
                }

                if (!empty($aRowEtWms['nr_dl'])) {
                    $komunikat = "Etykieta jest wczytana na DL" . $aRowEtWms['nr_dl'] . ". Przerywam operacje";
                    //echo "<br>" . $komunikat;
                    return show_komunikat_xml($komunikat);
                }
                if (!empty($aRowEtWms['funkcja_stat'])) {
                    $komunikat = "Etykieta jest w statusie: " . $aRowEtWms['status_system_nazwa'] . ". Przerywam operacje";
                    //echo "<br>" . $komunikat;
                    return show_komunikat_xml($komunikat);
                }

                if ($aRowZadanie['ilosc'] != $aRowEtWms['ilosc']) {  //czy jest odpowiednia ilość na etykiecie
                    $komunikat = "Ilosc na etykiecie " . $aRowEtWms['ilosc'] . " jest inna niz zamawiana " . $aRowZadanie['ilosc'] . ". Przerywam operacje";
                    //echo "<br>" . $komunikat;
                    return show_komunikat_xml($komunikat);
                }


                if ($aRowZadanie['etykieta_id'] != $etykieta_id_realizowana) {  //czy jest odpowiednia ilość na etykiecie
                    if ($aRowZadanie['zgodnosc_towaru'] == "2" || $aRowZadanie['zgodnosc_towaru'] == "4") {
                        $komunikat = "Etykieta realizowana jest rozna niz planowana. Przerywam operacje";                        //echo "<br>" . $komunikat;
                        return show_komunikat_xml($komunikat);
                    }
                    if ($aRowZadanie['zgodnosc_towaru'] == "1" && ($aRowZadanie['kod'] != $aRowEtWms['kod'] || $aRowZadanie['ilosc'] != $aRowEtWms['ilosc'])) {
                        $komunikat = "Realizowany jest rozny niz planowany w zakresie:kod,ilosc. Przerywam operacje";                        //echo "<br>" . $komunikat;
                        return show_komunikat_xml($komunikat);
                    }


                    if ($aRowZadanie['zgodnosc_towaru'] == "3" && ($aRowZadanie['kod'] != $aRowEtWms['kod'] || $aRowZadanie['lot'] != $aRowEtWms['lot'] || $aRowZadanie['ilosc'] != $aRowEtWms['ilosc'])) {
                        $komunikat = "Realizowany jest rozny niz planowany w zakresie:kod,lot,ilosc. Przerywam operacje";                        //echo "<br>" . $komunikat;
                        return show_komunikat_xml($komunikat);
                    }

                    $nowe_zadanie = szukaj_zadanie($etykieta_id_realizowana, $db);
                    if (count($nowe_zadanie) == 0) {
                        // realizuj podmianę
                        $sql = "update $baza_danych.delivery_et d set d.etykieta_id=$etykieta_id_realizowana WHERE d.delivery_id=" . $aRowZadanie['doc_id'] . " AND d.etykieta_id=" . $aRowZadanie['etykieta_id'] . " and ilosc_zamawiana=" . $aRowZadanie['ilosc'] . " limit 1";
                        //echo "<br>" . $sql;
                        $result7 = $db->mGetResult($sql);

                        $sql = 'update  zadania_dane z set z.etykieta_id=' . $etykieta_id_realizowana . ' where z.id=' . $zadanie_dane_id . ' limit 1';
                        //echo "<br>" . $sql;
                        $result8 = $db->mGetResult($sql);
                    } else {
                        foreach ($nowe_zadanie as $key => $value) {
                            if ($nowe_zadanie['zadanie_head_id'] != $aRowZadanie['zadanie_head_id']) {
                                $komunikat = "Etykieta jest zaplanowane przez inną DL " . $nowe_zadanie['doc_id'] . " . Przerywam operacje";                        //echo "<br>" . $komunikat;
                                return show_komunikat_xml($komunikat);
                            } else {
                                $sql = "update $baza_danych.delivery_et d set d.etykieta_id=$etykieta_id_realizowana WHERE d.delivery_id=" . $aRowZadanie['doc_id'] . " AND d.etykieta_id=" . $aRowZadanie['etykieta_id'] . " and ilosc_zamawiana=" . $aRowZadanie['ilosc'] . " limit 1";
                                //echo "<br>" . $sql;
                                $result7 = $db->mGetResult($sql);

                                $sql = 'update  zadania_dane z set z.etykieta_id=' . $etykieta_id_realizowana . ' where z.id=' . $zadanie_dane_id . ' limit 1';
                                //echo "<br>" . $sql;
                                $result8 = $db->mGetResult($sql);
                            }
                        }
                    }
                }


//                echo "<br>".$aRowZadanie['etykieta_id'];
//                $komunikat = "-----PRZERYWNIK-------";
//                //echo "<br>" . $komunikat;
//                return;
                //return;

                $sql = "insert into $baza_danych.dlcollect(nr_dl,nr_et,system_id) values('" . $aRowZadanie['doc_id'] . "','" . $etykieta_id_realizowana . "','" . $aRowZadanie['system_id'] . "') ";
                $result3 = $db->mGetResult($sql);


                $komunikat.=//mysql_error();
                if ($komunikat == "OK") {
                    $sql = ' update  zadania_dane z set z.realizacja_pracownik_id=' . $pracownik_id . ', z.start=NOW(),status=2,kompletowana_paleta_id=' . $aRowEtWms['paleta_id'] . ' WHERE z.id=' . $zadanie_dane_id;
                    //echo "<br>" . $sql;
                    $result8 = $db->mGetResult($sql);
                }
            }
        }


//$komunikat=$sql;
    } else {
        $komunikat = "Zadanie zostalo juz zrealizowane";
    }
    return show_komunikat_xml($komunikat);
}

function szukaj_zadanie($etykieta_id, $db) {
    $sql = 'SELECT zd.id AS id,k.kod,
TRIM(TRAILING "." FROM TRIM(TRAILING "0" from zd.ilosc)) as ilosc ,

k.kod_nazwa,k.ean,k.ean_jednostki, k.ilosc_w_opakowaniu, 
(k.ilosc_w_opakowaniu*TRIM(TRAILING "." FROM TRIM(TRAILING "0" from zd.ilosc))) as ilosc_opakowan,
zd.zadanie_head_id AS zadanie_head_id,
zd.status AS status,
zd.stare_m AS stare_m, zd.nowe_m AS nowe_m,
zd.nowe_m_realizowane AS nowe_m_realizowane,
zd.paleta_id AS paleta_id, zd.etykieta_id AS etykieta_id,
zd.kod_id AS kod_id, zd.lot AS lot,
zh.baza_danych AS baza_danych, zh.system_id AS system_id, zh.typ AS typ,
zh.doc_id AS doc_id, zh.doc_type AS doc_type,
zdt.nazwa AS doc_type_nazwa,
zh.zgodnosc_miejsca AS zgodnosc_miejsca, zh.zgodnosc_towaru AS zgodnosc_towaru,
zt.nazwa AS zadanie_typ_nazwa,
concat("H: ",m1.hala," ",m1.regal,"-",m1.miejsce,"-",m1.poziom ) AS stare_m_nazwa,
concat("H: ",m2.hala," ",m2.regal,"-",m2.miejsce,"-",m2.poziom ) AS nowe_m_nazwa,
s.nazwa AS system_id_nazwa,


  zd.czas_przydzielenia AS czas_przydzielenia,
zd.przydzielenie_pracownik_id AS przydzielenie_pracownik_id,
zd.start AS start, zd.stop AS stop,
zd.realizacja_pracownik_id AS realizacja_pracownik_id,
p2.imie_nazwisko AS realizacja_imie_nazwisko,

 zd.stanowisko_id AS stanowisko_id, zd.kierunek AS kierunek,zd.kompletacja,
 zh.priorytet AS priorytet,
 p.imie_nazwisko AS przydzielenie_imie_nazwisko
 FROM zadania_dane zd 
 LEFT JOIN zadania_head AS zh ON zh.id=zd.zadanie_head_id 
 LEFT JOIN zadania_doc_type AS zdt ON zh.doc_type=zdt.id
         LEFT JOIN zadania_typ AS zt ON zh.typ=zt.id 
         LEFT JOIN wmsrawa.miejsca AS m1 ON m1.id=zd.stare_m 
         LEFT JOIN wmsrawa.miejsca AS m2 ON m2.id=zd.nowe_m
         LEFT JOIN wmsrawa.pracownicy AS p ON zd.przydzielenie_pracownik_id=p.id 
         LEFT JOIN wmsrawa.pracownicy AS p2 ON zd.realizacja_pracownik_id=p2.id
         LEFT JOIN wmsrawa.systemy AS s ON s.wartosc=zh.system_id
left join wmsrawa.kody k on zd.kod_id=k.id
            where zd.etykieta_id=' . $etykieta_id . ' and zh.typ=4 order by zd.id desc limit 1';
    $result = $db->mGetResult($sql);
    return $result;
}

function pobierz_zadanie($zadanie_dane_id, $db) {
    $sql = 'SELECT zd.id AS id,k.kod,
TRIM(TRAILING "." FROM TRIM(TRAILING "0" from zd.ilosc)) as ilosc ,
ifnull((select ee.ilosc from etykiety ee
where ee.paleta_id=zd.paleta_id and ee.kod_id=zd.kod_id and ee.active=1 limit 1 ),0)  as ilosc_rzeczywista,
if(zd.ilosc<ifnull((select ee.ilosc from etykiety ee
where ee.paleta_id=zd.paleta_id and ee.kod_id=zd.kod_id and ee.active=1 limit 1 ),0),"TAK","NIE") as czy_kompletowana,
if(zd.ilosc>ifnull((select ee.ilosc from etykiety ee
where ee.paleta_id=zd.paleta_id and ee.kod_id=zd.kod_id and ee.active=1 limit 1 ),0),"TAK","NIE") as czy_brak,

k.kod_nazwa,k.ean,k.ean_jednostki, k.ilosc_w_opakowaniu, 
(k.ilosc_w_opakowaniu*TRIM(TRAILING "." FROM TRIM(TRAILING "0" from zd.ilosc))) as ilosc_opakowan,
zd.zadanie_head_id AS zadanie_head_id,
zd.status AS status,
(case when max(zd.status)=1 then "Aktywne" when max(zd.status)=0 then "Zamkniete" when max(zd.status)=2 then "Do aktywacji" end) AS status_nazwa,
zd.stare_m AS stare_m, zd.nowe_m AS nowe_m,
zd.nowe_m_realizowane AS nowe_m_realizowane,
zd.paleta_id AS paleta_id, zd.etykieta_id AS etykieta_id,
zd.kod_id AS kod_id, zd.lot AS lot,
zh.baza_danych AS baza_danych, zh.system_id AS system_id, zh.typ AS typ,
zh.doc_id AS doc_id, zh.doc_type AS doc_type,
zdt.nazwa AS doc_type_nazwa,
zh.zgodnosc_miejsca AS zgodnosc_miejsca, zh.zgodnosc_towaru AS zgodnosc_towaru,
zt.nazwa AS zadanie_typ_nazwa,
concat("H: ",m1.hala," ",m1.regal,"-",m1.miejsce,"-",m1.poziom ) AS stare_m_nazwa,
concat("H: ",m2.hala," ",m2.regal,"-",m2.miejsce,"-",m2.poziom ) AS nowe_m_nazwa,
s.nazwa AS system_id_nazwa,

         if(zh.baza_danych="rbrama", (SELECT if(w.rampa is null,"",concat("Rampa ",w.rampa,"; ",w.nr_ciagnik,"/", w.nr_naczepa))
         FROM rbrama.wjazdy w WHERE w.id=zh.doc_id limit 1),"") AS info,

  zd.czas_przydzielenia AS czas_przydzielenia,
zd.przydzielenie_pracownik_id AS przydzielenie_pracownik_id,
zd.start AS start, zd.stop AS stop,
zd.realizacja_pracownik_id AS realizacja_pracownik_id,
p2.imie_nazwisko AS realizacja_imie_nazwisko,

 zd.stanowisko_id AS stanowisko_id, zd.kierunek AS kierunek,zd.kompletacja,
 zh.priorytet AS priorytet,
 p.imie_nazwisko AS przydzielenie_imie_nazwisko,

        concat(if(zh.baza_danych="rbrama",
        (SELECT w.numer_karty FROM rbrama.wjazdy w WHERE w.id=zh.doc_id limit 1),"")) AS numer_karty,
         if(zh.baza_danych="rbrama", (SELECT w.ilosc_palet FROM rbrama.wjazdy w WHERE w.id=zh.doc_id limit 1),"") AS ilosc_jednostek


 FROM zadania_dane zd LEFT JOIN zadania_head AS zh ON zh.id=zd.zadanie_head_id LEFT JOIN zadania_doc_type AS zdt ON zh.doc_type=zdt.id
         LEFT JOIN zadania_typ AS zt ON zh.typ=zt.id LEFT JOIN wmsrawa.miejsca AS m1 ON m1.id=zd.stare_m LEFT JOIN wmsrawa.miejsca AS m2 ON m2.id=zd.nowe_m
         LEFT JOIN wmsrawa.pracownicy AS p ON zd.przydzielenie_pracownik_id=p.id LEFT JOIN wmsrawa.pracownicy AS p2 ON zd.realizacja_pracownik_id=p2.id
         LEFT JOIN wmsrawa.systemy AS s ON s.wartosc=zh.system_id
left join wmsrawa.kody k on zd.kod_id=k.id
            where zd.id=' . $zadanie_dane_id . '  limit 1';
    $result = $db->mGetResult($sql);
    return $result;
}

?>