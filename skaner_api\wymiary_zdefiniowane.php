<?php

//include_once 'db_pdo.php';
include_once 'Db.class.php';
include_once 'funkcje.inc';
//error_reporting(E_ALL);
//ini_set('display_errors', 1);

$db = new Db();
//
//if (empty($argv)) {
//    //$argv = array();
//    $argv[1] = $_GET['db'];
//    $argv[2] = $_GET['akcja'];
//}
//
$akcja = $_GET['akcja'];




$komunikat = "OK";

if ($akcja == "pobierz") {

    


    $wymiary = wyswietl_wymiary("wmsgg", $db);

    return xml_from_indexed_array(array('komunikat' => $komunikat, 'dodatkowa_akcja' => 'wybierz_wymiar', 'wymiary' => $wymiary));
}

function wyswietl_wymiary($baza_danych, $db) {
        $sql = 'SELECT if(w.nazwa!="",concat(w.nazwa,"-<PERSON><PERSON>"),"") as opis, w.dlugosc, w.szerokos<PERSON>, w.wysokosc,w.kolejnosc as kolejnosc,

concat(if(w.nazwa!="",concat(w.nazwa,"-Karton"),"")," ", if(szerokosc="","",concat(w.wysokosc,"x", w.szerokosc,"x", w.dlugosc))  ) as wyswietlana_nazwa
 FROM ' . $baza_danych . '.wymiary w
WHERE w.aktywne=1
union all
SELECT t.opis,pal_dlugosc as dlugosc, pal_szerokosc as szerokosc, "" as wysokosc,(t.kolejnosc_pal+10) as kolejnosc,
concat(t.opis) as wyswietlana_nazwa

 FROM ' . $baza_danych . '.typypalet t
    order by kolejnosc asc
    
    
;

';
        //echo $sql;
        $result = $db->mGetResultAsXML($sql);
        return $result;
    }




