<?php
include_once 'Dab.class.php';
include_once 'funkcje.inc';

// show errors
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

header('Content-Type: text/xml');

// Inicjalizacja
$Dab = new Dab();
$akcja = $_GET['akcja'] ?? 'dodawanie';
$komunikat = "OK";

switch ($akcja) {
    case "dodawanie":
        dodaj_osobe_do_linii($Dab, $_GET);
        break;
    default:
        show_komunikat_xml('Nieznana akcja');
        break;
}

/**
 * Dodaje osobę do linii produkcyjnej (deklaracji).
 *
 * @param Dab $db
 * @param array $data
 * @return void
 */
function dodaj_osobe_do_linii(Dab $db, array $data): void {
    // Sprawdzenie wymaganych parametrów
    if (!isset($data['typ_operacji']) || !isset($data['godzina']) || !isset($data['nr_karty']) || !isset($data['wyrob_id'])) {
        show_komunikat_xml('Brak wymaganych parametrów (typ_operacji, godzina, nr_karty, wyrob_id)');
    }

    $typ_operacji = $data['typ_operacji'];
    $godzina = $data['godzina'];
    $nr_karty = $data['nr_karty'];
    $wyrob_id = $data['wyrob_id'];
    $zadaniem = $data['zadaniem'] ?? '';
    $utworzyl = $data['utworzyl'] ?? 'api';
    
    // Walidacja formatu godziny
    if (!waliduj_format_czasu($godzina)) {
        show_komunikat_xml('Nieprawidłowy format czasu. Wymagany format: HH:MM lub H:MM');
    }

    // Sprawdź poprawność typu operacji
    if ($typ_operacji !== 'start' && $typ_operacji !== 'stop') {
        show_komunikat_xml('Nieprawidłowy typ operacji. Dozwolone: start, stop');
    }

    // Sprawdź dane osoby na podstawie numeru karty
    $result_osoba = sprawdz_osobe($nr_karty, $db);
    if (empty($result_osoba)) {
        show_komunikat_xml("Nie znaleziono osoby o numerze karty $nr_karty");
    }

    $osoba_id = $result_osoba[0]['id'];
    $imie_nazwisko = $result_osoba[0]['imie_nazwisko'];

    // Sprawdź czy istnieje deklaracja z danym wyrobem na dzisiejszy dzień
    $dzisiaj = date('Y-m-d');
    $aktualna_data_czas = $dzisiaj . ' ' . $godzina;
    
    $deklaracja_id = znajdz_lub_utworz_deklaracje($wyrob_id, $dzisiaj, $utworzyl, $db);
    
    // Sprawdź czy osoba jest już w deklaracji
    $osoba_w_deklaracji = sprawdz_czy_osoba_w_deklaracji($osoba_id, $dzisiaj, $db);
    
    if ($typ_operacji === 'start') {
        if (!empty($osoba_w_deklaracji)) {
            // Jeśli osoba jest już w deklaracji, sprawdź czy jest to ta sama deklaracja
            if ($osoba_w_deklaracji[0]['deklaracja_id'] == $deklaracja_id) {
                show_komunikat_xml('Osoba jest już w tej deklaracji');
            } else {
                // Informuj użytkownika o możliwości przeniesienia osoby z innej deklaracji
                if (isset($data['przenies_osobe']) && $data['przenies_osobe'] === 'tak') {
                    // Użytkownik potwierdził przeniesienie - zakończ pracę na poprzedniej deklaracji
                    $do_id_poprzednia = $osoba_w_deklaracji[0]['id'];
                    $result_zakonczenie = zakoncz_prace_osoby($do_id_poprzednia, $aktualna_data_czas, $db);
                    
                    if ($result_zakonczenie['status'] === 'error') {
                        xml_from_indexed_array($result_zakonczenie);
                    }
                    
                    // Po zakończeniu poprzedniej, dodaj osobę do nowej deklaracji
                    $result = dodaj_osobe_do_deklaracji($osoba_id, $deklaracja_id, $aktualna_data_czas, $db);
                    
                    if ($result) {
                        $response = [
                            'status' => 'success',
                            'message' => 'Przeniesiono osobę ' . $imie_nazwisko . ' z deklaracji ' . $osoba_w_deklaracji[0]['deklaracja_numer'] . ' do nowej deklaracji',
                            'deklaracja_id' => $deklaracja_id
                        ];
                        xml_from_indexed_array($response);
                    } else {
                        show_komunikat_xml('Błąd podczas dodawania osoby do nowej deklaracji');
                    }
                } else {
                    // Informuj użytkownika o możliwości przeniesienia
                    $response = [
                        'status' => 'confirm',
                        'message' => 'Osoba ' . $imie_nazwisko . ' jest już na deklaracji: ' . $osoba_w_deklaracji[0]['deklaracja_numer'] . '. Czy chcesz przenieść osobę do nowej deklaracji?',
                        'osoba_id' => $osoba_id,
                        'deklaracja_id' => $deklaracja_id
                    ];
                    xml_from_indexed_array($response);
                }
            }
        }
        
        // Dodaj osobę do deklaracji
        $result = dodaj_osobe_do_deklaracji($osoba_id, $deklaracja_id, $aktualna_data_czas, $db);
        
        if ($result) {
            $response = [
                'status' => 'success',
                'message' => 'Rozpoczęto pracę dla ' . $imie_nazwisko,
                'deklaracja_id' => $deklaracja_id
            ];
            xml_from_indexed_array($response);
        } else {
            show_komunikat_xml('Błąd podczas dodawania osoby do deklaracji');
        }
        
    } else { // koniec
        if (empty($osoba_w_deklaracji)) {
            show_komunikat_xml('Nie znaleziono rozpoczętej pracy dla tej osoby w danym dniu');
        }
        
        $do_id = $osoba_w_deklaracji[0]['id'];
        $result = zakoncz_prace_osoby($do_id, $aktualna_data_czas, $db);
        
        if ($result['status'] === 'error') {
            xml_from_indexed_array($result);
        }
        
        // Dopiero po pomyślnym zakończeniu pracy aktualizuj czasy w deklaracji
        update_time_in_deklaracja_wyroby($osoba_w_deklaracji[0]['deklaracja_id'], $db);
        
        $response = [
            'status' => 'success',
            'message' => 'Zakończono pracę dla ' . $imie_nazwisko,
            'deklaracja_id' => $osoba_w_deklaracji[0]['deklaracja_id']
        ];
        xml_from_indexed_array($response);
    }
}

/**
 * Sprawdza dane osoby na podstawie numeru karty.
 *
 * @param string $nr_karty
 * @param Dab $db
 * @return array
 */
function sprawdz_osobe(string $nr_karty, Dab $db): array {
    $sql = "SELECT id, concat(imie, ' ', nazwisko) as imie_nazwisko FROM osoby WHERE numer_karty = '$nr_karty'";
    return $db->mGetResult($sql);
}

/**
 * Sprawdza czy osoba jest już w deklaracji dzisiaj.
 *
 * @param int $osoba_id
 * @param string $dzisiaj
 * @param Dab $db
 * @return array
 */
function sprawdz_czy_osoba_w_deklaracji(int $osoba_id, string $dzisiaj, Dab $db): array {
    $sql = "SELECT do.id, do.deklaracja_id, d.numer as deklaracja_numer 
            FROM deklaracja_osoby do
            JOIN deklaracje d ON d.id = do.deklaracja_id
            WHERE do.osoba_id = $osoba_id 
            AND DATE(do.start) = '$dzisiaj' 
            AND do.stop IS NULL";
    return $db->mGetResult($sql);
}

/**
 * Znajduje istniejącą deklarację dla danego wyrobu i daty lub tworzy nową.
 *
 * @param int $wyrob_id
 * @param string $data
 * @param string $utworzyl
 * @param Dab $db
 * @return int
 */
function znajdz_lub_utworz_deklaracje(int $wyrob_id, string $data, string $utworzyl, Dab $db): int {
    // Sprawdź czy istnieje deklaracja na dany dzień dla wyrobu
    $sql = "SELECT d.id FROM deklaracje d 
            JOIN deklaracja_wyroby dw ON d.id = dw.deklaracja_id 
            WHERE dw.wyrob_id = $wyrob_id AND DATE(d.data_produkcji) = '$data'";
    $deklaracja_result = $db->mGetResult($sql);
    
    if (!empty($deklaracja_result)) {
        return $deklaracja_result[0]['id'];
    }
    
    // Nie ma deklaracji, tworzę nową
    $numer = get_max_nr_deklaracji_from_setup_and_increment($db);
    $klient_id = '81';
    $stol = '51';
    $jednostka_id = '11';
    $status_id = '1';
    $rok = date('Y');
    $zmiana = '1';
    
    $sql = "INSERT INTO deklaracje 
            (numer, rok, data_utworzenia, zmiana, utworzyl, klient_id, stol, jednostka_id, status_id, data_produkcji, deklaracja_typ) 
            VALUES 
            ('$numer', '$rok', NOW(), '$zmiana', '$utworzyl', '$klient_id', '$stol', '$jednostka_id', '$status_id', '$data', 'Nie dotyczy')";
    $db->mGetResult($sql);
    
    // Pobierz ID nowo utworzonej deklaracji
    $sql = "SELECT LAST_INSERT_ID() as id";
    $last_id = $db->mGetResult($sql);
    $deklaracja_id = $last_id[0]['id'];
    
    // Dodaj wyrób do deklaracji
    $sql = "INSERT INTO deklaracja_wyroby (deklaracja_id, wyrob_id, start) 
            VALUES ($deklaracja_id, $wyrob_id, NOW())";
    $db->mGetResult($sql);
    
    return $deklaracja_id;
}

/**
 * Dodaje osobę do deklaracji.
 *
 * @param int $osoba_id
 * @param int $deklaracja_id
 * @param string $start_time
 * @param Dab $db
 * @return bool
 */
function dodaj_osobe_do_deklaracji(int $osoba_id, int $deklaracja_id, string $start_time, Dab $db): bool {
    $sql = "INSERT INTO deklaracja_osoby (deklaracja_id, osoba_id, start) 
            VALUES ($deklaracja_id, $osoba_id, '$start_time')";
    $result = $db->mGetResult($sql);
    return ($result !== false);
}

/**
 * Kończy pracę osoby w deklaracji.
 *
 * @param int $deklaracja_osoby_id
 * @param string $stop_time
 * @param Dab $db
 * @return array
 */
function zakoncz_prace_osoby(int $deklaracja_osoby_id, string $stop_time, Dab $db): array {
    // Pobierz czas rozpoczęcia pracy
    $sql = "SELECT start, deklaracja_id, osoba_id FROM deklaracja_osoby WHERE id = $deklaracja_osoby_id";
    $result = $db->mGetResult($sql);
    
    if (empty($result)) {
        return ['status' => 'error', 'message' => 'Nie znaleziono rekordu pracy do zakończenia'];
    }
    
    $start_time = $result[0]['start'];
    $deklaracja_id = $result[0]['deklaracja_id'];
    $osoba_id = $result[0]['osoba_id'];
    
    // Sprawdź czy czas zakończenia jest większy od czasu rozpoczęcia
    if (strtotime($stop_time) <= strtotime($start_time)) {
        return [
            'status' => 'error', 
            'message' => 'Czas zakończenia musi być późniejszy niż czas rozpoczęcia',
            'start_time' => $start_time,
            'stop_time' => $stop_time,
            'deklaracja_id' => $deklaracja_id,
            'osoba_id' => $osoba_id
        ];
    }
    
    $sql = "UPDATE deklaracja_osoby SET stop = '$stop_time' WHERE id = $deklaracja_osoby_id";
    $result = $db->mGetResult($sql);
    
    if ($result === false) {
        return ['status' => 'error', 'message' => 'Błąd podczas aktualizacji czasu zakończenia pracy'];
    }
    
    return ['status' => 'success', 'message' => 'Praca została zakończona pomyślnie'];
}

/**
 * Pobiera i zwiększa numer deklaracji z tabeli setup.
 *
 * @param Dab $db
 * @return int
 */
function get_max_nr_deklaracji_from_setup_and_increment(Dab $db): int {
    $sql = "SELECT wartosc FROM setup WHERE nazwa = 'max_nr_deklaracji'";
    $result = $db->mGetResult($sql);
    $max_nr_deklaracji = (int)$result[0]['wartosc'];
    $max_nr_deklaracji++;
    $sql = "UPDATE setup SET wartosc = '$max_nr_deklaracji' WHERE nazwa = 'max_nr_deklaracji'";
    $db->mGetResult($sql);
    return $max_nr_deklaracji;
}

/**
 * Waliduje format czasu.
 *
 * @param string $czas Format HH:MM lub H:MM
 * @return bool True jeśli format jest poprawny, false w przeciwnym razie
 */
function waliduj_format_czasu(string $czas): bool {
    // Sprawdź format HH:MM (godzina dwucyfrowa)
    if (preg_match('/^([0-1][0-9]|2[0-3]):([0-5][0-9])$/', $czas, $matches)) {
        $godzina = (int)$matches[1];
        $minuta = (int)$matches[2];
        
        // Sprawdź zakres wartości
        if ($godzina >= 0 && $godzina <= 23 && $minuta >= 0 && $minuta <= 59) {
            return true;
        }
    }
    
    // Sprawdź format H:MM (godzina jednocyfrowa)
    if (preg_match('/^([0-9]):([0-5][0-9])$/', $czas, $matches)) {
        $godzina = (int)$matches[1];
        $minuta = (int)$matches[2];
        
        // Sprawdź zakres wartości
        if ($godzina >= 0 && $godzina <= 9 && $minuta >= 0 && $minuta <= 59) {
            return true;
        }
    }
    
    return false;
}

function update_time_in_deklaracja_wyroby(int $deklaracja_id, Dab $db){
    $sql = "UPDATE deklaracja_wyroby dw
JOIN (
    SELECT 
        deklaracja_id,
        MIN(start) AS min_start,
        MAX(stop) AS max_stop
    FROM deklaracja_osoby
    WHERE deklaracja_id = $deklaracja_id
    GROUP BY deklaracja_id
) AS sub ON dw.deklaracja_id = sub.deklaracja_id
SET 
    dw.start = sub.min_start,
    dw.stop = sub.max_stop
WHERE dw.deklaracja_id = $deklaracja_id;
";
//echo $sql;
    $db->mGetResult($sql);
}