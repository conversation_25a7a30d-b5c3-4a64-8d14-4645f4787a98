﻿using System;
using System.Linq;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using System.Threading;
using System.Text.RegularExpressions;
using System.Xml;

namespace Tarczyn__Magazyn
{



    public partial class Zad_DL_Zdejmowanie : Form, IZad_DL
    {
        //MainMenu myParent = null;
        Zad_Main myParent = null;
        TextBox[] TextBoxArray = null;
        XmlNode node = null;
        TextBox AktualnyTextBox = null;


        List<string> _paleta_wybor = new List<string>();
        int[] _paleta = new int[100];


        //string ilosc_w_opakowaniu = "1";
        string ean_jednostki = "";
        string ean = "";
        string ilosc_w_opakowaniu = "";
        string ean_opakowanie_zbiorcze = "";
        string ilosc_szt_w_zbiorczym = "";
        string nosnik_numer = "";




        public Zad_DL_Zdejmowanie(Zad_Main c, XmlNode node2)
        {

            FullScreenMode.OknoOFF(this);
            InitializeComponent();
            TextBoxArray = new TextBox[] { ETYKIETA };
            myParent = c;
            Etykieta.Inicjalizacja();
            wms_wybrany.Text = Wlasciwosci.system_id;
            imie_nazwisk_label.Text = Wlasciwosci.wozek + ":" + Wlasciwosci.imie_nazwisko;
            //Wlasciwosci.system_id_id = "";
            wypelnij_dane(node2);
        }

        void wypelnij_dane(XmlNode node2)
        {

            node = node2;
            label7.Text = node["doc_type_nazwa"].InnerText + " " + node["doc_id"].InnerText;
            label12.Text = "DS" + node["paleta_id"].InnerText;
            label11.Text = "" + node["kod"].InnerText;
            label4.Text = "" + node["kod_nazwa"].InnerText;
            textBox4.Text = node["stare_m_nazwa"].InnerText;
            //licznik_label.Text = "" + node["ile_wszystkich"].InnerText + "/" + node["ile_gotowe"].InnerText + " ; " + node["ile_wszystkich_paleta"].InnerText + "/" + node["ile_gotowe_paleta"].InnerText;


            if (node["przenaczenie_palety"].InnerText == "KOMPLETOWANA")
            {
                miejsce_docelowe_textBox1.Text = "PICKING";
            }
            else
            {
                miejsce_docelowe_textBox1.Text = "PEŁNA:"+node["nowe_m_nazwa"].InnerText;
            }
            
            textBox4.Focus();
            ETYKIETA.Focus();
            message_label.Text = "";
        }


        private void TworzenieDL_Load(object sender, EventArgs e)
        {
            //ETYKIETA.Focus();
        }



        private void GotFocus_TextBox(object sender, EventArgs e)
        {
            Skaner.Przewij_Skanowanie();
            TextBox Pole_Tekstowe = (TextBox)sender;
            AktualnyTextBox = Pole_Tekstowe;
            ZacznijSkanowanie();
        }

        private void LostFocus_TextBox(object sender, EventArgs e)
        {
            AktualnyTextBox = null;
            Skaner.Przewij_Skanowanie();

            TextBox Pole_Tekstowe = (TextBox)sender;

            if (ETYKIETA == Pole_Tekstowe)
            {


            }
        }







        #region Skanowanie

        Thread Skanowanie = null;



        private void ZacznijSkanowanie()
        {
            StringBuilder login = new StringBuilder();
            Skaner.UstawTryb_String(login);
            Skaner.Zacznij_Skanowanie();
            Skanowanie = new Thread(() => CheckString(login));
            Skanowanie.IsBackground = true;
            Skanowanie.Start();
        }

        private void CheckString(StringBuilder cc)
        {
            while (true)
            {
                if (cc.ToString() != "")
                {
                    this.Invoke(new EventHandler(delegate
                    {
                        dodawanie(cc.ToString());
                    }));
                    return;
                }
                Thread.Sleep(200);
            }
        }


        private void Zakoncz_Skanowanie()
        {
            {
                Skanowanie.Abort();
                Skaner.Przewij_Skanowanie();
            }
        }

        private void dodawanie(string ops)
        {
            //MessageBox.Show("TrybSkanu:" + TrybSkanu + "   ,   ops.Length:" + ops.Length);


            AktualnyTextBox.Text = ops;
            if (ops == "")
            {
                MessageBox.Show("Nie wypełniono etykiety");
                return;
            }



            if (AktualnyTextBox == MIEJSCE)
            {
                //XmlNode node_etykieta = KomunikacjaSerwer(node, ops, 2);

                XmlDocument doc1_etykieta = WebService.Pobierz_XmlDocument("zdejmowanie_wysokich.php?akcja=realizacja_zadania&zadanie_dane_id=" + node["id"].InnerText + "&imie_nazwisko=" + Uri.EscapeUriString(Wlasciwosci.imie_nazwisko) + "&skan=" + ops + "&pracownik_id=" + Wlasciwosci.id_Pracownika);
                XmlNode node_etykieta = WebService.Pobierz_XmlNode(doc1_etykieta, "dane");


                if (node_etykieta["komunikat"].InnerText != "OK")
                {
                    //ETYKIETA.Text = "";
                    message_label.BackColor = System.Drawing.Color.Red;

                    ETYKIETA.Focus();
                    MIEJSCE.Focus();
                    MIEJSCE.Text = "";

                    if (node_etykieta["komunikat"].InnerText.Length > 40)
                    {
                        MessageBox.Show(node_etykieta["komunikat"].InnerText);
                    }
                    else
                    {
                        message_label.Text = node_etykieta["komunikat"].InnerText;
                    }
                }
                else
                {
                    int proba = 2;
                    if (node_etykieta["dodatkowa_akcja"].InnerText == "wybierz_poziom") //&& miejsce_id==""
                    {
                        //MessageBox.Show("1");
                        XmlNodeList xmlnode2 = WebService.Pobierz_XmlNodeList(doc1_etykieta, "poziomy");
                        DataTable dt = Konwersja_XmlNodeList_DataTable(xmlnode2);
                        //MessageBox.Show("2");
                        PoleWyborListBox XA = new PoleWyborListBox(dt, 22F, "TAK");

                        if (XA.ShowDialog() == DialogResult.OK)
                        {
                            if (XA.wartosc_wybrana != "")
                            {
                                //MessageBox.Show("3");

                                node_etykieta = Poziom_Wybrany(node, XA.wartosc_wybrana, ops, proba -= 1);
                                if (node_etykieta["komunikat"].InnerText != "OK")
                                {

                                    message_label.BackColor = System.Drawing.Color.Red;

                                    ETYKIETA.Focus();
                                    MIEJSCE.Focus();
                                    MIEJSCE.Text = "";

                                    if (node_etykieta["komunikat"].InnerText.Length > 40)
                                    {
                                        MessageBox.Show(node_etykieta["komunikat"].InnerText);
                                    }
                                    else
                                    {
                                        message_label.Text = node_etykieta["komunikat"].InnerText;
                                    }

                                }
                                else
                                {
                                    myParent.Show();
                                    myParent.ZacznijNasluchiwanie("", "");
                                    this.Close();
                                }


                            }
                        }
                    }
                    else if (node_etykieta["dodatkowa_akcja"].InnerText == "zrealizowane") //&& miejsce_id==""
                    {
                        myParent.Show();
                        myParent.ZacznijNasluchiwanie("", "");
                        this.Close();
                    }                



                }
            }


            if (AktualnyTextBox == ETYKIETA)
            {
                if (ETYKIETA.Text.Replace("DS", "") != node["paleta_id"].InnerText)
                {
                    MessageBox.Show("Niepoprawna etykieta");
                    ETYKIETA.Text = "";
                    //return;
                }
                else
                {
                    MIEJSCE.Focus();
                }                
            }

            


            //this.ZacznijSkanowanie();
        }


        private XmlNode Poziom_Wybrany(XmlNode node_src, string miejsce_id, string etykieta_scan, int proba)
        {
            XmlDocument doc1_etykieta = WebService.Pobierz_XmlDocument("zdejmowanie_wysokich.php?akcja=realizacja_zadania&zadanie_dane_id=" + node_src["id"].InnerText + "&imie_nazwisko=" + Uri.EscapeUriString(Wlasciwosci.imie_nazwisko) + "&skan=" + etykieta_scan + "&pracownik_id=" + Wlasciwosci.id_Pracownika + "&miejsce_id=" + miejsce_id);
            XmlNode node_etykieta = WebService.Pobierz_XmlNode(doc1_etykieta, "dane");
            return node_etykieta;
        }

        private XmlNode KomunikacjaSerwer(XmlNode node_src, string etykieta_scan, int proba)
        {
            XmlDocument doc1_etykieta = WebService.Pobierz_XmlDocument("zdejmowanie_wysokich.php?akcja=realizacja_zadania&zadanie_dane_id=" + node_src["id"].InnerText + "&imie_nazwisko=" + Uri.EscapeUriString(Wlasciwosci.imie_nazwisko) + "&skan=" + etykieta_scan + "&pracownik_id=" + Wlasciwosci.id_Pracownika );
            XmlNode node_etykieta = WebService.Pobierz_XmlNode(doc1_etykieta, "dane");
            return node_etykieta;
        }

        private static DataTable Konwersja_XmlNodeList_DataTable(XmlNodeList xmlnode2)
        {
            DataTable dt = new DataTable();
            dt.Columns.Add("id", typeof(string));
            dt.Columns.Add("nazwa_wyswietlana", typeof(string));
            foreach (XmlNode wynik in xmlnode2)
            {
                DataRow dtrow = dt.NewRow();
                dtrow["id"] = wynik["id"].InnerText;
                dtrow["nazwa_wyswietlana"] = wynik["nazwa_wyswietlana"].InnerText;
                dt.Rows.Add(dtrow);
            }
            return dt;
        }




        #endregion






        private void QT_KeyDown(object sender, KeyEventArgs e)
        {

        }





        private void powrot_Click(object sender, EventArgs e)
        {
            Skaner.Przewij_Skanowanie();
            myParent.Show();
            this.Close();
        }

        private void odkladanie_Click(object sender, EventArgs e)
        {
            //myParent.delivery_odkladanie("TAK");
            myParent.Show();
            myParent.ZacznijNasluchiwanie("11", "");

            this.Close();
        }

        

        private void Etykieta_KeyDown(object sender, KeyEventArgs e)
        {
            if (e.KeyCode == Keys.Enter)
            {
                this.dodawanie(ETYKIETA.Text);
                //odkladanie_Click(this, new EventArgs());
            }
        }

        private void pomin_button_Click(object sender, EventArgs e)
        {
            XmlDocument doc1_etykieta = WebService.Pobierz_XmlDocument("delivery_pominiecie_zadania_zdejmowania.php?zadanie_dane_id=" + node["id"].InnerText);
            XmlNode node_etykieta = WebService.Pobierz_XmlNode(doc1_etykieta, "dane");


            if (node_etykieta["komunikat"].InnerText != "OK")
            {
                MessageBox.Show(node_etykieta["komunikat"].InnerText);
            }
            else
            {

                myParent.Show();
                myParent.ZacznijNasluchiwanie("", "");
                this.Close();

            }
        }






    }
}