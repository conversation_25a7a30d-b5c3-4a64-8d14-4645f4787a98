<?php

include_once 'Db.class.php';
include_once 'funkcje.inc';

$db = new Db();
$akcja = $_GET['akcja'];
$sql = $_GET['sql'];

if ($akcja == "sprawdz") 
{
    $komunikat = "OK";
    $dane = wyswietl_wersje($db, $wozek, $pracownik);

    return xml_from_indexed_array(array('komunikat' => $komunikat, 'dane' => $dane));
}
else if($akcja == "zaktualizuj")
{
    $result = $db->mGetResult($sql);//result zwraca id w przypadku jednego insertu

    echo $result;

}
else if($akcja == "WyczytajWartosc")
{
    $komunikat = "OK";
    $dane = $db->mGetResultAsXML($sql);

    foreach($dane[0] as $element => $value)
    {
        echo $value;
    }

    //return xml_from_indexed_array(array('komunikat' => $komunikat, 'dane' => $dane));
}

else if($akcja == "WyczytajWartosci")
{
    $komunikat = "OK";
    $dane = $db->mGetResultAsXML($sql);

    return xml_from_indexed_array(array('komunikat' => $komunikat, 'dane' => $dane));

    //return xml_from_indexed_array(array('komunikat' => $komunikat, 'dane' => $dane));
}

function wyswietl_wersje($db, $wozek, $pracownik) 
{
    $sql = "SELECT 1 as ile  FROM wozki_historia w WHERE w.wozek_id='". $wozek ."' and w.pracownik_id='". $pracownik ."' AND typ_operacji='logowanie' AND w.ts like concat(CURDATE(),'%') ORDER BY w.id desc limit 1;";
    $result = $db->mGetResultAsXML($sql);
    
    return $result;
}