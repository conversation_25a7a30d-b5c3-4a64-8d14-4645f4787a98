﻿using System;
using System.Linq;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using MySql.Data.MySqlClient;
using System.Text.RegularExpressions;
using System.Threading;
using System.Net;


using System.Collections;

using System.IO;
using System.Xml;


namespace Tarczyn__Magazyn
{
    public partial class Zad_ZM_Odkl : Form
    {
        public Zad_Main myParent = null;
        List<string> _hala = new List<string>();
        List<string> _regal = new List<string>();
        List<string> _poziom = new List<string>();
        List<string> Etykiety_dodane = new List<string>();

        public string hala = "";
        public string regal = "";
        public string miejsce = "";
        string zgodnosc_miejsca = "";
        string kompletowana_paleta_id = "";




        private Thread Skanowanie;
        private Thread Nasluchiwanie;

        XmlNode node = null;
        XmlNode node_myParent = null;


        Dictionary<string, string> rec = new Dictionary<string, string>();


        public Zad_ZM_Odkl(Zad_Main MyParent, XmlNode node2)
        {
            //
            InitializeComponent();
            node = node2;
            //this.etykieta_textbox.KeyPress += new KeyPressEventHandler(etykieta_textbox_KeyPress);
            this.myParent = MyParent;
            wms_wybrany.Text = Wlasciwosci.system_id;
            imie_nazwisk_label.Text = Wlasciwosci.wozek + ":" + Wlasciwosci.imie_nazwisko;
            //FERRERO_Synchronizacja.SprawdzWolnaNazwePliku();
            timer1.Enabled = false;
            //kompletowana_paleta_id = nr_nosnika_kompletowany;
            myParent.nr_nosnika_kompletowany = "";


            //Lokalizacja_label.Text = "H:" + myParent.hala + " " + myParent.regal + "-" + myParent.miejsce + "-" + myParent.poziom;
            //zawartosc_label.Text = myParent.kod + " ; " + myParent.lot + " ; " + myParent.ilosc;


            wypelnij_dane();

            //nosnik_text.Text = myParent.paleta_id;
            textBox1.Focus();




            this.ZacznijSkanowanie();
        }




        void wypelnij_dane()
        {
            //Lokalizacja_label.Text = node["nowe_m_nazwa"].InnerText;
            zgodnosc_miejsca = node["zgodnosc_miejsca"].InnerText;
            nosnik_text.Text = node["system_id_nazwa"].InnerText+"   DS" + node["paleta_id"].InnerText ;

        }






        private void etykieta_textbox_KeyPress(object sender, KeyPressEventArgs e)
        {
            if (e.KeyChar == (char)Keys.Back)
            {
                e.Handled = true;
                //etykieta_textbox.Text = "";
            }
            else
            {
                e.Handled = false;
                return;
            }
        }






        private void Zakoncz_Skanowanie(object sender, EventArgs e)
        {
            this.Skanowanie.Abort();
            Skaner.Przewij_Skanowanie();
        }

        private void powrot_Click(object sender, EventArgs e)
        {



            //this.myParent.tryb_pracy = "pobieranie";
            this.myParent.ZacznijNasluchiwanie("","");
            this.Skanowanie.Abort();
            Skaner.Przewij_Skanowanie();
            Wlasciwosci.CurrentOperacja = "0";
            //myParent.wczytwanieEtykiet = false;
            this.myParent.Show();
            timer1.Enabled = false;
            this.Close();



        }



        private void button1_Anuluj(object sender, EventArgs e)
        {

        }



        public void ZacznijSkanowanie()
        {
            StringBuilder login = new StringBuilder();
            Skaner.UstawTryb_String(login);
            Skaner.Zacznij_Skanowanie();
            this.Skanowanie = new Thread(() => this.CheckString(login));
            this.Skanowanie.IsBackground = true;
            this.Skanowanie.Start();
        }



        private void CheckString(StringBuilder cc)
        {
            EventHandler method = null; //to jest delegat

            while (true)
            {
                if (cc.ToString() != "")
                {
                    if (method == null)
                    {
                        method = delegate
                        {
                            this.dodawanie(cc.ToString());
                        };
                    }
                    this.Invoke(method);
                    return;
                }
                Thread.Sleep(200);
            }
        }


        // 'Internal' - wyszukuje w bazie etykiete i wypełnia pola
        private void dodawanie(string ops)
        {

            Skaner.Przewij_Skanowanie();


            XmlNode node_etykieta3 = Serwer_Uzgadnianie_Poziomu(node, null, ops, 2);
            if (node_etykieta3["komunikat"].InnerText != "OK")
            {
                //ETYKIETA.Text = "";
                nosnik_text.Focus();
                textBox1.Focus();

                if (node_etykieta3["komunikat"].InnerText.Length > 40)
                {
                    MessageBox.Show(node_etykieta3["komunikat"].InnerText);
                }
                else
                {
                    message_label.Text = node_etykieta3["komunikat"].InnerText;
                    message_label.BackColor = System.Drawing.Color.Red;
                }
                this.ZacznijSkanowanie();
            }
            else
            {
                myParent.Show();
                myParent.ZacznijNasluchiwanie("","");
                this.Close();
            }



        }








        private XmlNode Serwer_Uzgadnianie_Poziomu(XmlNode node_src, string miejsce_id, string etykieta_scan, int proba)
        {
            //MessageBox.Show("Serwer_Uzgadnianie_Poziomu:" + proba + ",  etykieta_scan:" + etykieta_scan);
            //MessageBox.Show("miejsce_id:" + miejsce_id + ", ");
            //MessageBox.Show("nosnik_numer=" + nosnik_numer + "");


            XmlDocument doc1_etykieta = WebService.Pobierz_XmlDocument("zmiana_miejsca_realizacja2.php?akcja=pobranie_miejsca&zadanie_dane_id=" + node_src["id"].InnerText + "&db=" + node_src["baza_danych"].InnerText + "&imie_nazwisko=" + Uri.EscapeUriString(Wlasciwosci.imie_nazwisko) + "&miejsce_id=" + miejsce_id + "&etykieta_scan=" + etykieta_scan);
            XmlNode node_etykieta = WebService.Pobierz_XmlNode(doc1_etykieta, "dane");

            if (node_etykieta["komunikat"].InnerText != "OK")
            {
                //MessageBox.Show("0");
                return node_etykieta;
                //MessageBox.Show(node_etykieta["komunikat"].InnerText);
            }
            else
            {
                //MessageBox.Show(node_etykieta.InnerXml);
                if (node_etykieta["ilosc_pozycji_poziomow"].InnerText != "1") //&& miejsce_id==""
                {
                    //MessageBox.Show("1");
                    XmlNodeList xmlnode2 = WebService.Pobierz_XmlNodeList(doc1_etykieta, "poziomy");
                    DataTable dt = Konwersja_XmlNodeList_DataTable(xmlnode2);
                    //MessageBox.Show("2");
                    PoleWyborListBox XA = new PoleWyborListBox(dt, 22F,"NIE");

                    if (XA.ShowDialog() == DialogResult.OK)
                    {
                        if (XA.wartosc_wybrana != "")
                        {
                            //MessageBox.Show("3");

                            node_etykieta = Poziom_Wybrany(node_src, XA.wartosc_wybrana, etykieta_scan, proba -= 1);

                        }
                    }
                }
            }
            //MessageBox.Show("4");
            return node_etykieta;
        }

        private XmlNode Poziom_Wybrany(XmlNode node_src, string miejsce_id, string etykieta_scan, int proba)
        {
            XmlDocument doc1_etykieta = WebService.Pobierz_XmlDocument("zmiana_miejsca_realizacja2.php?akcja=pobranie_miejsca&zadanie_dane_id=" + node_src["id"].InnerText + "&db=" + node_src["baza_danych"].InnerText + "&imie_nazwisko=" + Uri.EscapeUriString(Wlasciwosci.imie_nazwisko) + "&miejsce_id=" + miejsce_id + "&etykieta_scan=" + etykieta_scan);
            XmlNode node_etykieta = WebService.Pobierz_XmlNode(doc1_etykieta, "dane");
            return node_etykieta;
        }




        private static DataTable Konwersja_XmlNodeList_DataTable(XmlNodeList xmlnode2)
        {
            DataTable dt = new DataTable();
            dt.Columns.Add("id", typeof(string));
            dt.Columns.Add("nazwa_wyswietlana", typeof(string));
            foreach (XmlNode wynik in xmlnode2)
            {
                DataRow dtrow = dt.NewRow();
                dtrow["id"] = wynik["id"].InnerText;
                dtrow["nazwa_wyswietlana"] = wynik["nazwa_wyswietlana"].InnerText;
                dt.Rows.Add(dtrow);
            }
            return dt;
        }




        private void timer1_Tick(object sender, EventArgs e)
        {
            this.Skanowanie.Abort();
            Skaner.Przewij_Skanowanie();

            //myParent.wczytwanieEtykiet = false;
            this.myParent.Show();
            timer1.Enabled = false;
            this.Close();


        }



        #region fullscreen
        private void fullscreenmode()
        {
            this.Width = Screen.PrimaryScreen.Bounds.Width;
            this.Height = Screen.PrimaryScreen.Bounds.Height;
            this.Left = 0;
            this.Top = 0;
        }
        #endregion

        private void button1_Click(object sender, EventArgs e)
        {

        }











    }
}