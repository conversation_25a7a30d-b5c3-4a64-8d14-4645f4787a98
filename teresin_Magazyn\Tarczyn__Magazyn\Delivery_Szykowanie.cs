﻿using System;
using System.Linq;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using MySql.Data.MySqlClient;
using System.Threading;
using System.Net;
using System.IO;
using System.Xml;

namespace Tarczyn__Magazyn
{
    public partial class Delivery_Szykowanie : Form
    {
        ActionMenu myParent = null;
        string nr_etykiety = null;
        string aktywna = null;
        private Thread Skanowanie;
        string operac_id_global = "";
        TextBox AktualnyTextBox = null;

        public Delivery_Szykowanie(ActionMenu MyParent)
        {
            
            InitializeComponent();
            this.myParent = MyParent;
            wms_wybrany.Text = Wlasciwosci.system_id;
            imie_nazwisk_label.Text = Wlasciwosci.wozek + ":" + Wlasciwosci.imie_nazwisko;
            Wlasciwosci.CurrentOperacja = "3";
            this.ZacznijSkanowanie();



        }

        private void czysc()
        {
            nr_etykiety_opis.Text = "";
            
        }

        private void CheckString(StringBuilder cc)
        {
            EventHandler method = null;
            while (true)
            {
                if (cc.ToString() != "")
                {
                    if (method == null)
                    {
                        method = delegate
                        {
                            this.wyszukaj_etykiete(cc.ToString());
                        };
                    }
                    this.Invoke(method);
                    return;
                }
                Thread.Sleep(200);
            }
        }

        private void wyszukaj_etykiete(string ops)
        {
            
            //AktualnyTextBox.Text = ops;
            etykieta_text.Text = ops;

            if (etykieta_text.Text.Substring(0, 2) == "DL")
            {

                if (etykieta_text.Text != "")
                {
                    XmlDocument doc1_etykieta = WebService.Pobierz_XmlDocument("delivery_szykowanie_skanowanie.php?delivery_id=" + etykieta_text.Text.Replace("DL","") + "&akcja=szykowanie&pracownik_id=" + Wlasciwosci.id_Pracownika);
                    XmlNode node_etykieta = WebService.Pobierz_XmlNode(doc1_etykieta, "dane");
                    if (node_etykieta["komunikat"].InnerText != "OK")
                    {
                        etykieta_text.Text = "";
                        kod.Text = "";
                        MessageBox.Show(node_etykieta["komunikat"].InnerText);
                    }
                    else
                    {
                        kod.Text = node_etykieta["delivery_id"].InnerText;
                        
                        //etykieta_text.Focus();
                    }
                }
            }
            else
            {
                etykieta_text.Text = "";
                kod.Text = "";
                MessageBox.Show("To nie jest numer DL, bo " + ops);
            }
            etykieta_text.Text = "";
            ZacznijSkanowanie();

        }



        private void powrot_Click(object sender, EventArgs e)
        {
            this.Skanowanie.Abort();
            Skaner.Przewij_Skanowanie();
            //BazaDanychExternal.DokonajUpdate("insert into operacje(etykieta_id, doc_type, doc_nr, imie_nazwisko, typ_operacji,system_id,wozek,operac_id,ilosc) values('0','','','" + Wlasciwosci.imie_nazwisko + "','PR_AR','" + Wlasciwosci.system_id_id + "','" + Wlasciwosci.wozek + "','" + operac_id_global + "','0');");
            
            this.myParent.Show();
            this.Close();
            
        }




        private void ZacznijSkanowanie()
        {
            StringBuilder login = new StringBuilder();
            Skaner.UstawTryb_String(login);
            Skaner.Zacznij_Skanowanie();
            this.Skanowanie = new Thread(() => this.CheckString(login));
            this.Skanowanie.IsBackground = true;
            this.Skanowanie.Start();
        }





    }
}