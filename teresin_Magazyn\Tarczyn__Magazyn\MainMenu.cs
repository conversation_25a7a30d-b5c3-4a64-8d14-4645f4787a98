﻿using System;
using System.Linq;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using System.Threading;
using System.Diagnostics;
using System.Reflection;
using System.IO;
using System.Text.RegularExpressions;
using System.Xml;

namespace Tarczyn__Magazyn
{
    public partial class MainMenu : Form
    {

        public string skanowanie_nr = "1";
        #region Zmienne
        Thread ok;
        #endregion


        public WLAN_Status myWlan = null;
        public MainMenu()
        {
            FullScreenMode.OknoOFF(this);
            //WLAN_Status.Inicjalizacja(this);
            myWlan = new WLAN_Status(this);
            InitializeComponent();
            BazaDanychExternal.Inicjalizacja(Wlasciwosci.Varbaza, Wlasciwosci.Varhost, Wlasciwosci.VarLoginMYSQL, Wlasciwosci.VarHasloMYSQL, this);
            if (BazaDanychExternal.SprawdzCzyIstniejePolaczenie() == false)
            {
                MessageBox.Show("Brak połączenia , wyłączam aplikację.");
                button1_Click(null, null);
            }

            //MessageBox.Show("D");


        }

        private void button1_Click(object sender, EventArgs e)
        {
            //Wyczysc();
            WyjscieProgram.Wyjdz("WMS.exe", this);
        }

        private void Wyczysc()
        {
            BazaDanychExternal.Wyczysc();
            myWlan.Wyczysc();
            myWlan.Wyczysc();
        }


        public void Komunikat(string Next)
        {
            MessageBox.Show(Next);
        }

        

        private void Ustawienia_Click(object sender, EventArgs e)
        {
            //Wlasciwosci.CurrentOperacja = "7";
            
            //return;
            HasloPrompt X = new HasloPrompt(Wlasciwosci.Varhasloadmina, 2, 3);

            if (X.ShowDialog() == DialogResult.OK)
            {
                UstawieniaForm New = new UstawieniaForm(this);
                New.ShowDialog();
            }
        }

        private void button2_Click(object sender, EventArgs e)
        {
            if (myWlan.get_Signal_int() < 2)
            {
                MessageBox.Show("Trwa łączenie. Spróbuj ponownie...");
                return;
            }
            Skaner.Przewij_Skanowanie();
            object t = BazaDanychExternal.Wyczytaj_Jedna_Wartosc("Select Wersja from wersjamobile group by wersja order by wersja desc limit 1;");
            //MessageBox.Show(t.ToString());
            if (Wlasciwosci.Varwersja < Convert.ToDouble(t))
            {
                MessageBox.Show("Dostępna jest aktualizacja systemu.Zaczynam pobieranie.");
                ProcessStartInfo ok1 = new ProcessStartInfo();
                //Console.WriteLine(Path.GetDirectoryName(Assembly.GetExecutingAssembly().GetName().CodeBase) + "/ppgCE.exe");
                ok1.FileName = Path.GetDirectoryName(Assembly.GetExecutingAssembly().GetName().CodeBase) + "/UpdatePPGCE.exe";
                Process.Start(ok1);
                Wyczysc();
                WyjscieProgram.Wyjdz("WMS.exe", this);
            }



           


            button2.Text = "Anuluj";
            Wlasciwosci.CurrentOperacja = "7";
            this.button2.Click -= new System.EventHandler(this.button2_Click);
            this.button2.Click += new System.EventHandler(this.button22_Click);
            odblokuj(false);
            StringBuilder login = new StringBuilder();
            Skaner.UstawTryb_String(login);
            Skaner.Zacznij_Skanowanie();
            ok = new Thread(() => CheckString(login));
            ok.IsBackground = true;
            ok.Start();
            //SprawdzIdentyfikator("10297");
        }

        private void button22_Click(object sender, EventArgs e)
        {
            button2.Text = "Zaloguj";
            vvv();
            ok.Abort();
            Skaner.Przewij_Skanowanie();
            Wlasciwosci.CurrentOperacja = "7";
        }
        private void odblokuj(bool f)
        {
            Wyjscie.Enabled = f;
            Ustawienia.Enabled = f;

        }


        private void vvv()
        {

            this.button2.Click -= new System.EventHandler(this.button22_Click);
            this.button2.Click += new System.EventHandler(this.button2_Click);
            odblokuj(true);
        }

        private void CheckString(StringBuilder cc)
        {
            while (true)
            {

                if (cc.ToString() != "")
                {
                    this.Invoke(new EventHandler(delegate
           {
               vvv();
               button2.Text = "Zaloguj";

               Skaner.Przewij_Skanowanie();
               if (skanowanie_nr == "2")
               {
                   if (cc.ToString().Substring(0, 2) == "WK")
                   {
                       Wlasciwosci.wozek = cc.ToString().Substring(2, cc.ToString().Length - 2);
                       label6.Text = Wlasciwosci.wozek;


                       string zap = "SELECT 1 as ile  FROM wozki_historia w WHERE w.wozek_id='" + Wlasciwosci.wozek + "' and w.imie_nazwisko='" + Wlasciwosci.imie_nazwisko + "' AND typ_operacji='logowanie' AND w.ts like concat(CURDATE(),'%') ORDER BY w.id desc limit 1; ";               
                       object obj3 = BazaDanychExternal.Wyczytaj_Tabele(zap);
                       DataTable tabela2 = (DataTable)obj3;
                       int jest = 0;
                       //MessageBox.Show("7");
                       if (tabela2.Rows.Count == 0)
                       {
                           string operac_id_global = "";

                           //string zapyt = "update docnumber d set last=last+1 WHERE d.name='operacja_id'; ";
                           //BazaDanychExternal.Wyczytaj_Jedna_Wartosc(zapyt);
                           //string zapytanie = "SELECT last FROM docnumber d WHERE d.name='operacja_id';";
                           //operac_id_global = (string)BazaDanychExternal.Wyczytaj_Jedna_Wartosc(zapytanie);
                           Wozek XB = new Wozek(-1, "Ilość motogodzin wózka:");
                           if (XB.ShowDialog() == DialogResult.OK)
                           {
                               //BazaDanychExternal.DokonajUpdate("insert into operacje(etykieta_id, doc_type, doc_nr, imie_nazwisko, typ_operacji,system_id,wozek,operac_id,ilosc) values('" + Wlasciwosci.wozek + "','MOTO','" + XB.ilosc_wpisana + "','" + Wlasciwosci.imie_nazwisko + "','MOTO_IN','0','" + Wlasciwosci.wozek + "','" + operac_id_global + "','0');");
                               BazaDanychExternal.DokonajUpdate("insert into wozki_historia(wozek_id,   motogodziny,typ_operacji,czy_sprawny_wozek,uwagi,imie_nazwisko) values('" + Wlasciwosci.wozek + "','" + XB.ilosc_wpisana + "','logowanie','" + XB.czy_wozek_sprawny + "','" + XB.uwagi + "','" + Wlasciwosci.imie_nazwisko + "');");
                           }
                       }
                       //BazaDanychExternal.ChangeHost(Wlasciwosci.Varbaza, Wlasciwosci.Varhost, Wlasciwosci.VarLoginMYSQL, Wlasciwosci.VarHasloMYSQL);
                   }
                   else
                   {
                       Wlasciwosci.wozek = "0";
                       label6.Text = Wlasciwosci.wozek;
                   }

                   WyborBazy XA = new WyborBazy();
                   if (XA.ShowDialog() == DialogResult.OK)
                   {
                       ActionMenu v = new ActionMenu(this);
                       v.Show();
                       this.Hide();
                   }
                   Wlasciwosci.CurrentOperacja = "0";

                   skanowanie_nr = "1";
                   label6.Text = "";
                   label5.Text = "";
                   return;

               }

               if (skanowanie_nr == "1")
               {
                   this.SprawdzIdentyfikator(cc.ToString());
                   label5.Text = Wlasciwosci.imie_nazwisko;
                   //Skaner.Zacznij_Skanowanie();
               }






           }));
                    return;
                }

                Thread.Sleep(200);
            }


        }

        private void SprawdzIdentyfikator(string x)
        {
            try
            {

                if (myWlan.get_Signal_int() < 2)
                {
                    MessageBox.Show("Brak połączenia");
                    return;
                }



                string MacAddress = "";
                MacAddress = myWlan.get_MacAddress();
                string skaner_nazwa = (string)BazaDanychExternal.Wyczytaj_Jedna_Wartosc("SELECT s.nazwa FROM skanery_lista s WHERE s.Serial='" + Wlasciwosci.SerialNumber + "' ;");

                if (skaner_nazwa == null || skaner_nazwa == "")
                {
                    BazaDanychExternal.DokonajUpdate("insert INTO skanery_lista (Serial,nazwa, mac) VALUES ('" + Wlasciwosci.SerialNumber + "','" + Wlasciwosci.SerialNumber + "', '" + MacAddress + "')");
                }
                else
                {
                    BazaDanychExternal.DokonajUpdate("update skanery_lista  set ts=NOW()   where Serial='" + Wlasciwosci.SerialNumber + "';");
                }






                if (BazaDanychExternal.SprawdzCzyIstniejePolaczenie() == false)
                {
                    MessageBox.Show("Brak połączenia");
                    return;
                }


                string str = (string)BazaDanychExternal.Wyczytaj_Jedna_Wartosc("SELECT p.imie_nazwisko FROM pracownicy p WHERE p.numer_Karty=" + x + " ;");
                string prac_id = (string)BazaDanychExternal.Wyczytaj_Jedna_Wartosc("SELECT p.id FROM pracownicy p WHERE p.numer_Karty=" + x + " ;");
                string debugowanie_skaner = (string)BazaDanychExternal.Wyczytaj_Jedna_Wartosc("SELECT s.wartosc FROM setup s WHERE s.nazwa='debugowanie_skaner'");
                Wlasciwosci.debugowanie_skaner = Convert.ToInt32(debugowanie_skaner);

                if (str == "error" || prac_id == "error") //if (BazaDanychExternal.SprawdzCzyIstniejePolaczenie()==false)
                {
                    //MessageBox.Show(Wlasciwosci.Message[5]);
                    MessageBox.Show("Brak połączenia");
                    return;
                }
                if (str != "" && str != null)
                {
                    Wlasciwosci.imie_nazwisko = str;
                    Wlasciwosci.id_Pracownika = prac_id;

                    skanowanie_nr = "2";

                    Skaner.Przewij_Skanowanie();
                    StringBuilder login = new StringBuilder();
                    Skaner.UstawTryb_String(login);
                    Skaner.Zacznij_Skanowanie();
                    this.ok = new Thread(() => this.CheckString(login));
                    this.ok.IsBackground = true;
                    this.ok.Start();
                }
                else
                {
                    MessageBox.Show(Wlasciwosci.Message[4]);
                    return;
                }

            }

            catch (Exception ex)
            {
                MessageBox.Show(ex.Message);
            }

        }

        private void button1_Click_1(object sender, EventArgs e)
        {
            //Wlasciwosci.ZapisXMLZobele("1", "2", "3", "4", "5", "6", "7");
            object Tabela = BazaDanychExternal.Wyczytaj_Tabele("select DATE_FORMAT(dataprod,'%d-%m-%Y') from etykiety where id =59931");
            if (Tabela != null)
            {
                DataTable fgg = (DataTable)Tabela;
                MessageBox.Show(fgg.Rows[0][0].ToString());
            }
        }

        private void MainMenu_Load(object sender, EventArgs e)
        {
            try 
            {

                //MessageBox.Show("1");
                object t = BazaDanychExternal.Wyczytaj_Jedna_Wartosc("Select Wersja from wersjamobile group by wersja order by wersja desc limit 1;");
                //MessageBox.Show(t.ToString());
                //MessageBox.Show("2");
                if (Wlasciwosci.Varwersja < Convert.ToDouble(t))
                {
                    //MessageBox.Show("3");
                    MessageBox.Show("Dostępna jest aktualizacja systemu.Zaczynam pobieranie.");
                    ProcessStartInfo ok1 = new ProcessStartInfo();
                    //Console.WriteLine(Path.GetDirectoryName(Assembly.GetExecutingAssembly().GetName().CodeBase) + "/ppgCE.exe");
                    ok1.FileName = Path.GetDirectoryName(Assembly.GetExecutingAssembly().GetName().CodeBase) + "/UpdatePPGCE.exe";
                    Process.Start(ok1);
                    Wyczysc();
                    //MessageBox.Show("4");
                    WyjscieProgram.Wyjdz("WMS.exe", this);

                }

                wersja.Text = Wlasciwosci.Varwersja.ToString();


            }

            catch (Exception ex)
            {
                MessageBox.Show(ex.Message);
            }

            //MessageBox.Show(Wlasciwosci.Varwersja.ToString());
        }

        private void button3_Click(object sender, EventArgs e)
        {
            if (this.myWlan.get_Signal_int() < 2)
            {
                MessageBox.Show("Trwa łączenie. Spróbuj ponownie...");
                return;
            }
            Info_EAN tt = new Info_EAN(this);
            tt.Show();
            this.Hide();
        }

        private void button1_Click_2(object sender, EventArgs e)
        {
            if (this.myWlan.get_Signal_int() < 2)
            {
                MessageBox.Show("Trwa łączenie. Spróbuj ponownie...");
                return;
            }
            Info_EAN_Uszk tt = new Info_EAN_Uszk(this);
            tt.Show();
            this.Hide();

        }

        private void button4_Click(object sender, EventArgs e)
        {
            if (this.myWlan.get_Signal_int() < 2)
            {
                MessageBox.Show("Trwa łączenie. Spróbuj ponownie...");
                return;
            }
            PIKR tt = new PIKR(this);
            tt.Show();
            this.Hide();
        }


    }
}