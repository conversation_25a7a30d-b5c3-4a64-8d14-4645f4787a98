<?php

//include_once 'db_pdo.php';
include_once 'Db.class.php';
include_once 'funkcje.inc';
//error_reporting(E_ALL);
//ini_set('display_errors', 1);

$db = new Db();
//
$akcja = $_GET['akcja'];
$komunikat = "OK";
if ($akcja == "pobierz_stanowiska_pracy") {
    $sql = 'SELECT id,nazwa as nazwa_wyswietlana FROM zadania_stanowiska z where aktywne=1';
    $result = $db->mGetResultAsXML($sql);
    if (!empty($db->errors))
        $komunikat = $db->errors;

    if (count($result) == 0) {
        $komunikat = "Brak ";
        return show_komunikat_xml($komunikat);
    } else {
        return xml_from_indexed_array(array('komunikat' => $komunikat, 'pozycje' => $result));
    }
}
?>