# Mapa API - System WMS

Ten dokument mapuje istniejące punkty końcowe API (backend PHP) na podstawie analizy kodu klienta C#. Służy jako podstawa do stworzenia nowego API w technologii .NET.

---

## Moduł: Rejestra<PERSON>ja Czasu Pracy (PIKR)

### Endpoint: `GET /pikr/api.php`

#### Akcja: `dodawanie`

*   **Opis**: Rejestruje rozpoczęcie lub zakończenie pracy przez pracownika na danej linii produkcyjnej. Obsługuje scenariusz, w którym pracownik jest już przypisany do innej deklaracji i wymaga potwierdzenia przeniesienia.
*   **Plik Klienta**: `PIKR.cs`
*   **Metoda Klienta**: `wyslij_zapytanie(string ops)`

#### Parametry Zapytania:

| Nazwa           | Typ    | Opis                                                                 | Przykład        | Uwagi                                                                |
|-----------------|--------|----------------------------------------------------------------------|-----------------|----------------------------------------------------------------------|
| `akcja`         | string | Nazwa wykonywanej operacji.                                          | `dodawanie`     | Wartość stała.                                                       |
| `typ_operacji`  | string | Określa, czy jest to początek (`start`) czy koniec (`stop`) pracy.     | `start`         | Pobierane z kontrolki `radioButton1_start`.                          |
| `godzina`       | string | Godzina operacji w formacie `HH:mm`.                                 | `14:30`         | Składane z dwóch pól tekstowych.                                     |
| `nr_karty`      | string | Numer karty pracownika, zazwyczaj ze skanera kodów.                  | `12345678`      |                                                                      |
| `wyrob_id`      | int    | ID wyrobu.                                                           | `1414`          | Wartość zahardkodowana w kliencie. Może wymagać dynamicznego ustawiania. |
| `utworzyl`      | string | Login użytkownika wykonującego operację.                             | `rklepacki`     | Wartość zahardkodowana. W nowym API powinna pochodzić z sesji/tokenu. |
| `przenies_osobe`| string | Flaga (`tak`/`nie`) informująca, czy przenieść pracownika z innej deklaracji. | `tak`           | Ustawiane na `tak` po potwierdzeniu przez użytkownika.                |

#### Struktura Odpowiedzi (XML):

**Sukces (`success`):**
```xml
<dane>
  <status>success</status>
  <message>Dodano do deklaracji nr 123</message>
  <deklaracja_id>123</deklaracja_id>
</dane>
```

**Błąd (`error`):**
```xml
<dane>
  <status>error</status>
  <message>Nie znaleziono pracownika o podanym numerze karty.</message>
</dane>
```

**Potwierdzenie (`confirm`):**
```xml
<dane>
  <status>confirm</status>
  <message>Osoba jest już na innej deklaracji. Czy chcesz ją przenieść?</message>
</dane>
```

**Starszy format (dla kompatybilności wstecznej):**
```xml
<dane>
  <komunikat>OK</komunikat>
  <doc_ref>...</doc_ref>
</dane>
```

---

## Moduł: Deklaracja Produkcji (DeklaracjaProdukcji)

### Endpoint: `GET /produkcja/deklaracja_wyrobu_skaner.php`

#### Akcja: `wyszukaj_dp`

*   **Opis**: Wyszukuje i zwraca listę dostępnych deklaracji produkcji do wyboru przez operatora.
*   **Plik Klienta**: `DeklaracjaProdukcji.cs`
*   **Metoda Klienta**: `wyszukaj_dp()`

**Parametry Zapytania:**

| Nazwa       | Typ    | Opis                               | Przykład | 
|-------------|--------|------------------------------------|----------|
| `akcja`     | string | Nazwa operacji.                    | `wyszukaj_dp` |
| `system_id` | int    | ID systemu, z którego pochodzi zapytanie. | `1`      |

**Struktura Odpowiedzi (XML):**

```xml
<dane>
  <komunikat>OK</komunikat>
  <lista_dp>
    <rekord>
      <id>1</id>
      <nazwa>Produkt A</nazwa>
      <doc_nr>DP/123</doc_nr>
      <lot>L001</lot>
      <status_prism>Aktywny</status_prism>
    </rekord>
    <!-- ... więcej rekordów ... -->
  </lista_dp>
</dane>
```

---

#### Akcja: `sprawdzanie_deklaracja`

*   **Opis**: Tworzy nową deklarację ilościową dla wybranego zlecenia produkcyjnego, generuje etykietę i zwraca jej numer.
*   **Plik Klienta**: `DeklaracjaProdukcji.cs`
*   **Metoda Klienta**: `Deklaruj()`

**Parametry Zapytania:**

| Nazwa               | Typ    | Opis                                      | Przykład                |
|---------------------|--------|-------------------------------------------|-------------------------|
| `akcja`             | string | Nazwa operacji.                           | `sprawdzanie_deklaracja`|
| `docin_id`          | int    | ID wybranej deklaracji produkcji.         | `123`                   |
| `ilosc_deklarowana` | string | Zadeklarowana ilość.                      | `100`                   |
| `data_waznosci`     | string | Data ważności w formacie `RR-MM-DD`.      | `25-12-31`              |
| `operacja_id`       | string | ID operacji (jeśli kontynuacja).          | `456`                   |
| `pracownik_id`      | int    | ID pracownika.                            | `789`                   |
| `wozek`             | string | Numer wózka.                              | `W1`                    |
| `system_id`         | int    | ID systemu.                               | `1`                     |
| `drukarka_ip`       | string | Adres IP drukarki do wydruku etykiety.    | `*************`         |
| `imie_nazwisko`     | string | Imię i nazwisko pracownika.               | `Jan Kowalski`          |

**Struktura Odpowiedzi (XML):**

```xml
<dane>
  <komunikat>OK</komunikat>
  <etykieta>E0012345</etykieta>
  <operacja_id>457</operacja_id>
  <licznik>5</licznik>
</dane>
```

---

#### Akcja: `drukowanie_etykiety`

*   **Opis**: Ponownie drukuje istniejącą etykietę.
*   **Plik Klienta**: `DeklaracjaProdukcji.cs`
*   **Metoda Klienta**: `drukowanie_etykiety()`

**Parametry Zapytania:**

| Nazwa         | Typ    | Opis                           | Przykład        |
|---------------|--------|--------------------------------|-----------------|
| `akcja`       | string | Nazwa operacji.                | `drukowanie_etykiety` |
| `etykieta_id` | string | Numer etykiety do wydruku.     | `E0012345`      |
| `drukarka_ip` | string | Adres IP wybranej drukarki.    | `*************` |

**Struktura Odpowiedzi (XML):**

```xml
<dane>
  <komunikat>OK</komunikat>
</dane>
```

---

### Endpoint: `GET /wydruki/drukarki_lista.php`

*   **Opis**: Pobiera listę dostępnych w systemie drukarek etykiet.
*   **Plik Klienta**: `DeklaracjaProdukcji.cs`
*   **Metoda Klienta**: `pobierz_drukarki()`

**Parametry Zapytania:** Brak

**Struktura Odpowiedzi (XML):**

```xml
<dane>
  <komunikat>OK</komunikat>
  <drukarki>
    <rekord>
      <adres_ip>*************</adres_ip>
    </rekord>
    <rekord>
      <adres_ip>*************</adres_ip>
    </rekord>
  </drukarki>
</dane>
```

---

## Moduł: Szykowanie Wysyłki (Delivery_Szykowanie)

### Endpoint: `GET /delivery_szykowanie_skanowanie.php`

#### Akcja: `szykowanie`

*   **Opis**: Weryfikuje i przetwarza zeskanowany numer dokumentu wysyłki (DL), przypisując go do pracownika.
*   **Plik Klienta**: `Delivery_Szykowanie.cs`
*   **Metoda Klienta**: `wyszukaj_etykiete(string ops)`

**Parametry Zapytania:**

| Nazwa          | Typ    | Opis                               | Przykład   |
|----------------|--------|------------------------------------|------------|
| `delivery_id`  | string | Numer dokumentu wysyłki (bez prefiksu 'DL'). | `12345`    |
| `akcja`        | string | Nazwa operacji.                    | `szykowanie` |
| `pracownik_id` | int    | ID pracownika.                     | `789`      |

**Struktura Odpowiedzi (XML):**

```xml
<dane>
  <komunikat>OK</komunikat>
  <delivery_id>DL12345</delivery_id>
</dane>
```

---

## Moduł: Inwentaryzacja

### Endpoint: `GET /inwentaryzacja_manual.php`

#### Akcja: `zapisz`

*   **Opis**: Zapisuje pojedynczy wpis inwentaryzacyjny dla zeskanowanego kodu produktu lub etykiety w określonej lokalizacji.
*   **Plik Klienta**: `Inwentaryzacja.cs`
*   **Metoda Klienta**: `KomunikacjaSerwerem()`

**Parametry Zapytania:**

| Nazwa             | Typ    | Opis                                      | Przykład                |
|-------------------|--------|-------------------------------------------|-------------------------|
| `akcja`           | string | Nazwa operacji.                           | `zapisz`                |
| `pracownik_id`    | int    | ID pracownika.                            | `789`                   |
| `imie_nazwisko`   | string | Imię i nazwisko pracownika.               | `Jan Kowalski`          |
| `operac_id`       | string | ID operacji (jeśli istnieje).             | `op123`                 |
| `wozek`           | string | Numer wózka.                              | `W1`                    |
| `system_id`       | int    | ID systemu.                               | `1`                     |
| `inw_data`        | string | Data inwentaryzacji (`YYYY-MM-DD`).       | `2023-10-27`            |
| `inw_opis`        | string | Opis inwentaryzacji.                      | `Inwentaryzacja roczna` |
| `ilosc`           | string | Zliczona ilość.                           | `50`                    |
| `kod`             | string | Kod produktu.                             | `PROD-001`              |
| `inwentaryzacja_id`| int    | ID aktywnej sesji inwentaryzacyjnej.     | `42`                    |
| `hala`            | string | Nazwa hali.                               | `H1`                    |
| `regal`           | string | Numer regału.                             | `R10`                   |
| `miejsce`         | string | Numer miejsca.                            | `M5`                    |
| `poziom`          | string | Poziom regału.                            | `P2`                    |
| `inw_status`      | string | Status etykiety (np. `REPR`, `USZK`).     | `REPR`                  |
| `proba`           | string | Flaga próby.                              | `1`                     |
| `nrwspolny`       | string | Numer wspólny inwentaryzacji.             | `INW/2023/1`            |
| `etykieta_id`     | string | Zeskanowany numer etykiety.               | `E0012345`              |
| `etykieta_klient` | string | Numer SAP etykiety.                       | `S123456789`            |

**Struktura Odpowiedzi (XML):**

```xml
<dane>
  <komunikat>OK</komunikat>
  <ilosc_zliczona>150</ilosc_zliczona>
  <stan>200</stan>
</dane>
```
---
