﻿using System;
using System.Linq;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using System.Threading;
using System.Text.RegularExpressions;
using System.Net;
using System.IO;
using System.Xml;

namespace Tarczyn__Magazyn
{
    public partial class PIKR : Form
    {
        //MainMenu myParent = null;
        MainMenu myParent = null;
        TextBox[] TextBoxArray = null;

        TextBox AktualnyTextBox = null;

        string operac_id_global = "";
        string przenies_osobe = "NIE";
        string typ_operacji = "";
        






        public PIKR(MainMenu c)
        {
            Wlasciwosci.id_Pracownika = "1";
            FullScreenMode.OknoOFF(this);
            InitializeComponent();
            TextBoxArray = new TextBox[] { nr_karty,textBox1_godzina,textBox3_minuty};
            myParent = c;
            Etykieta.Inicjalizacja();
            nr_karty.Focus();




        }





        public void RemoveText(object sender, EventArgs e)
        {
            TextBox Pole_Tekstowe = (TextBox)sender;
            if (Pole_Tekstowe.Text == "RRMMDD")
            {
                Pole_Tekstowe.Text = "";
            }

        }

        public void AddText(object sender, EventArgs e)
        {

        }

        private void button1_Click(object sender, EventArgs e)
        {
            Skaner.Przewij_Skanowanie();
            myParent.Show();
            this.Close();
        }


        int TrybSkanu = 0;

        private void GotFocus_TextBox(object sender, EventArgs e)
        {
            Skaner.Przewij_Skanowanie();
            TextBox Pole_Tekstowe = (TextBox)sender;

            AktualnyTextBox = Pole_Tekstowe;

            //if (DW == Pole_Tekstowe)
            //{

            //    if (!(dataprod.Text == "" || dataprod.Text == "RRMMDD"))
            //    {
            //        // sprawdź poprawność daty

            //        try
            //        {
            //            DateTime gg = DateTime.ParseExact("20" + dataprod.Text, "yyyyMMdd", null);
            //            if (ilosc_dni_przydatnosci != "0")
            //            {
            //                DW.Text = gg.AddDays(Convert.ToDouble(ilosc_dni_przydatnosci)).ToString("yyMMdd"); 
            //                //MessageBox.Show("DW:" + DW.Text);
            //                opak_real.Focus();
            //            }
            //        }
            //        catch (Exception ex)
            //        {
            //            MessageBox.Show("Błędna data produkcji.");
            //            dataprod.Text = "";
            //        }
            //    }

            //}
           


            ZacznijSkanowanie();
        }

        private void LostFocus_TextBox(object sender, EventArgs e)
        {
            AktualnyTextBox = null;
            Zakoncz_Skanowanie();

            TextBox Pole_Tekstowe = (TextBox)sender;



           

        }





        private void button2_Click(object sender, EventArgs e)
        {



        }



        #region Skanowanie

        Thread Skanowanie = null;



        private void ZacznijSkanowanie()
        {
            StringBuilder login = new StringBuilder();
            Skaner.UstawTryb_String(login);
            Skaner.Zacznij_Skanowanie();
            Skanowanie = new Thread(() => CheckString(login));
            Skanowanie.IsBackground = true;
            Skanowanie.Start();
        }

        private void CheckString(StringBuilder cc)
        {
            while (true)
            {
                if (cc.ToString() != "")
                {
                    this.Invoke(new EventHandler(delegate
                    {
                        dodawanie(cc.ToString());
                    }));
                    return;
                }
                Thread.Sleep(200);
            }
        }


        private void Zakoncz_Skanowanie()
        {
            {
                Skanowanie.Abort();
                Skaner.Przewij_Skanowanie();
            }
        }


        private void wyslij_zapytanie(string ops)
        {
            label4.Text = "";
            if (ops == "")
            {
                label4.Text = "Etykieta nie może być pusta";
                return;
            }


            if (radioButton1_start.Checked)
            {
                typ_operacji = "start";
            }
            else
            {
                typ_operacji = "stop";
            }


            // Przygotowanie URL z parametrami zgodnie ze strukturą api.php
            string godzina = textBox1_godzina.Text + ":" + textBox3_minuty.Text;
            string url = "pikr/api.php?akcja=dodawanie&typ_operacji=" + typ_operacji + 
                         "&godzina=" + godzina + 
                         "&nr_karty=" + ops + 
                         "&wyrob_id=1414" + 
                         "&utworzyl=rklepacki" + 
                         "&przenies_osobe=" + przenies_osobe;
            
            XmlDocument doc1_etykieta = WebService.Pobierz_XmlDocument(url);
            
            // Analiza odpowiedzi XML zgodnie z nową strukturą API
            if (doc1_etykieta.DocumentElement.Name == "dane")
            {
                XmlNode statusNode = doc1_etykieta.SelectSingleNode("//status");
                XmlNode messageNode = doc1_etykieta.SelectSingleNode("//message");
                
                if (statusNode != null && messageNode != null)
                {
                    string status = statusNode.InnerText;
                    string message = messageNode.InnerText;
                    
                    if (status == "error")
                    {
                        nr_karty.Text = "";
                        label4.Text = message;
                        return;
                    }
                    else if (status == "confirm")
                    {
                        // Obsługa pytania o przeniesienie osoby do innej deklaracji
                        DialogResult result = MessageBox.Show(message, "Przeniesienie osoby",
                                                             MessageBoxButtons.YesNo, MessageBoxIcon.Question, MessageBoxDefaultButton.Button1);
                        
                        if (result == DialogResult.Yes)
                        {
                            przenies_osobe = "tak";
                            // Ponowne wywołanie zapytania z potwierdzeniem
                            wyslij_zapytanie(ops);
                            return;
                        }
                        else
                        {
                            nr_karty.Text = "";
                            return;
                        }
                    }
                    else if (status == "success")
                    {
                        // Pomyślna operacja
                        label4.Text = message;
                        
                        // Pobranie dodatkowych informacji z XML, jeśli są dostępne
                        XmlNode deklaracjaIdNode = doc1_etykieta.SelectSingleNode("//deklaracja_id");
                        if (deklaracjaIdNode != null)
                        {
                            // Opcjonalnie można zapisać ID deklaracji do wykorzystania później
                            string deklaracjaId = deklaracjaIdNode.InnerText;
                        }
                    }
                }
                else
                {
                    // Obsługa starego formatu odpowiedzi, jeśli taki wystąpi
                    XmlNode node_etykieta = WebService.Pobierz_XmlNode(doc1_etykieta, "dane");
                    if (node_etykieta != null && node_etykieta["komunikat"] != null)
                    {
                        if (node_etykieta["komunikat"].InnerText != "OK")
                        {
                            nr_karty.Text = "";
                            label4.Text = node_etykieta["komunikat"].InnerText;
                            return;
                        }
                        else if (node_etykieta["doc_ref"] != null)
                        {
                            label4.Text = node_etykieta["doc_ref"].InnerText;
                        }
                    }
                }
            }
            else
            {
                nr_karty.Text = "";
                label4.Text = "Nieprawidłowa odpowiedź z serwera";
                return;
            }
        }

        private void dodawanie(string ops)
        {
            Skaner.Przewij_Skanowanie();
            if (AktualnyTextBox == nr_karty)
            {
                
                wyslij_zapytanie(ops);
                    ZacznijSkanowanie();   
            }
        }

        #endregion





        private void button6_Click(object sender, EventArgs e)
        {


        }








        private void QT_KeyDown(object sender, KeyEventArgs e)
        {
            if (e.KeyCode == Keys.Enter)
            {
                button2_Click(this, new EventArgs());
            }
        }

        private void opak_real_KeyDown(object sender, KeyEventArgs e)
        {
            if (e.KeyCode == Keys.Enter)
            {
                button2_Click(this, new EventArgs());
            }
        }












    }
}