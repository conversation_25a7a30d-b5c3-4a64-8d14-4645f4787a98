# System Inwentaryzacji - Dokumentacja Techniczna

## Przegląd

Kompleksowa dokumentacja systemu inwentaryzacji dla aplikacji WMS, obejmująca analizę istniejącej aplikacji desktop oraz specyfikację dla nowej aplikacji MAUI.

## Struktura Dokumentacji

### 📊 [Schemat Bazy Danych](SCHEMAT_BAZY_DANYCH.md)
Szczegółowy opis struktury tabel, relacji i indeksów używanych w systemie inwentaryzacji.

**Główne tabele:**
- `inwentaryzacja` - Główna tabela z danymi inwentaryzacji
- `etykiety` - Informacje o etykietach produktów
- `miejsca` - Struktura lokalizacji magazynowych
- `kody` - Kartoteka produktów
- `operacje` - Historia operacji
- `zmianym` - Rejestr zmian lokalizacji

### 🔄 [Workflow Procesów](WORKFLOW_PROCESOW.md)
Opisuje przepływy procesów biznesowych dla różnych typów inwentaryzacji.

**Typy inwentaryzacji:**
- **Ogólna** (`Inwentaryzacja.cs`) - Kompleksowa inwentaryzacja z pełną kontrolą
- **Produktowa** (`InwentaryzacjaProd.cs`) - Skupiona na produktach/kodach
- **GG** (`InwentaryzacjaGG.cs`) - Według grup/kategorii
- **Miejsc** (`InwentaryzacjaMiejsca.cs`) - Według lokalizacji magazynowych

### 🌐 [API Endpoints MAUI](API_ENDPOINTS_MAUI.md)
Specyfikacja API potrzebnego dla aplikacji MAUI z przykładami zapytań i odpowiedzi.

**Główne endpoints:**
- `GET /inwentaryzacja_lista.php` - Lista aktywnych inwentaryzacji
- `GET /inwentaryzacja_szukaj.php` - Wyszukiwanie etykiet/kodów
- `POST /inwentaryzacja_zapis.php` - Zapisywanie pozycji
- `GET /inwentaryzacja_stan.php` - Stan inwentaryzacji
- `POST /inwentaryzacja_sync.php` - Synchronizacja offline

### 📱 [Mapowanie Desktop → MAUI](MAPOWANIE_DESKTOP_MAUI.md)
Szczegółowe mapowanie funkcjonalności z aplikacji desktop na architekturę MAUI.

**Kluczowe komponenty:**
- ViewModels (MVVM pattern)
- Services (API, Database, Scanner)
- Models (Data structures)
- Views (XAML interfaces)

### 📈 [Diagramy Przepływów](DIAGRAMY_PRZEPLYWY.md)
Wizualne reprezentacje procesów w formacie Mermaid.

**Główne diagramy:**
- Główny przepływ inwentaryzacji
- Proces skanowania i walidacji
- Synchronizacja offline
- Obsługa błędów
- Dekodowanie GS1-128

## Kluczowe Funkcjonalności

### 1. Typy Inwentaryzacji

#### Inwentaryzacja Ogólna
- **Operacja:** `CurrentOperacja = "19"`
- **Specyfika:** Obsługuje pola `nr_wspolny` i `proba`, może obsługiwać palety
- **Workflow:** Kompleksowa kontrola z pełną walidacją

#### Inwentaryzacja Produktowa
- **Operacja:** `CurrentOperacja = "21"`
- **Specyfika:** Automatyczne dodawanie nowych rekordów, tryb offline
- **Workflow:** Skupiona na kodach produktów z auto-dodawaniem

#### Inwentaryzacja GG
- **Operacja:** `CurrentOperacja = "20"`
- **Specyfika:** Brak wymogu kodu produktu, skupienie na etykietach
- **Workflow:** Uproszczona dla grup/kategorii

#### Inwentaryzacja Miejsc
- **Specyfika:** Możliwość tworzenia nowej inwentaryzacji, prostszy interfejs
- **Workflow:** Skupiona na lokalizacjach magazynowych

### 2. Obsługa Kodów

#### Standardowe Kody
- Wyszukiwanie po kodzie produktu
- Walidacja w kartotece `kody`
- Obsługa EAN i kodów wewnętrznych

#### Etykiety
- Wyszukiwanie po `etykieta_id` lub `nrsap`
- Powiązanie z lokalizacjami
- Historia zmian

#### Palety
- Prefix "DS" + ID palety
- Grupowanie etykiet
- Kontrola zawartości

#### GS1-128
- Dekodowanie identyfikatorów aplikacji (AI)
- Automatyczne wypełnianie pól
- Walidacja formatów

### 3. Lokalizacje Magazynowe

#### Struktura
- **Hala** - Główny obszar magazynu
- **Regał** - Identyfikator regału
- **Miejsce** - Pozycja w regale
- **Poziom** - Wysokość w regale

#### Zarządzanie
- Walidacja istnienia lokalizacji
- Automatyczne wykrywanie zmian
- Rejestracja przemieszczeń

### 4. Synchronizacja Danych

#### Tryb Online
- Bezpośrednia komunikacja z API
- Walidacja w czasie rzeczywistym
- Natychmiastowa aktualizacja stanu

#### Tryb Offline
- Lokalna baza SQLite
- Kolejka synchronizacji
- Automatyczne przywracanie połączenia

#### Mechanizmy Odzyskiwania
- Ponowne próby połączenia
- Rozwiązywanie konfliktów
- Backup lokalny

## Architektura Techniczna

### Desktop (Istniejąca)
```
Windows CE/Mobile
├── C# Windows Forms
├── MySQL Direct Connection
├── .NET Compact Framework
└── Hardware Scanner Integration
```

### MAUI (Docelowa)
```
Cross-Platform MAUI
├── XAML + MVVM Pattern
├── REST API Communication
├── SQLite Local Database
├── Camera + ML.NET Scanner
└── Background Synchronization
```

## Bezpieczeństwo

### Autoryzacja
- Token-based authentication
- Sprawdzanie uprawnień pracownika
- Walidacja system_id

### Walidacja Danych
- Escape SQL injection
- Walidacja XSS
- Kontrola limitów ilościowych
- Sprawdzenie wymaganych pól

### Audyt
- Rejestracja wszystkich operacji
- Śledzenie zmian lokalizacji
- Historia modyfikacji
- Logi błędów

## Wydajność

### Optymalizacja Bazy Danych
- Indeksy na kluczowych polach
- Partycjonowanie dużych tabel
- Optymalizacja zapytań

### Cache i Offline
- Lokalne cache dla często używanych danych
- Inteligentna synchronizacja
- Kompresja danych

### Sieć
- Gzip compression
- Paginacja wyników
- Rate limiting

## Implementacja MAUI

### Wymagania Techniczne
- .NET 8.0+
- MAUI Workload
- SQLite-net-pcl
- Microsoft.Extensions.Http
- CommunityToolkit.Mvvm

### Struktura Projektu
```
WMS.MAUI/
├── Models/           # Modele danych
├── ViewModels/       # Logika prezentacji
├── Views/           # Interfejsy XAML
├── Services/        # Usługi (API, DB, Scanner)
├── Converters/      # Konwertery XAML
├── Resources/       # Zasoby (style, obrazy)
└── Platforms/       # Kod specyficzny dla platform
```

### Kluczowe Serwisy
- **ApiService** - Komunikacja z backend API
- **DatabaseService** - Lokalna baza SQLite
- **ScannerService** - Obsługa skanowania kodów
- **SyncService** - Synchronizacja offline/online
- **NavigationService** - Nawigacja między stronami

## Migracja z Desktop

### Faza 1: Analiza i Przygotowanie
- ✅ Analiza istniejącego kodu
- ✅ Dokumentacja procesów biznesowych
- ✅ Specyfikacja API
- ✅ Projekt architektury MAUI

### Faza 2: Implementacja Core
- [ ] Modele danych i ViewModels
- [ ] Podstawowe serwisy
- [ ] API endpoints
- [ ] Lokalna baza danych

### Faza 3: Interfejs Użytkownika
- [ ] Główne ekrany inwentaryzacji
- [ ] Formularze wprowadzania danych
- [ ] Obsługa skanowania
- [ ] Nawigacja i menu

### Faza 4: Funkcjonalności Zaawansowane
- [ ] Synchronizacja offline
- [ ] Obsługa błędów
- [ ] Raportowanie
- [ ] Optymalizacja wydajności

### Faza 5: Testowanie i Wdrożenie
- [ ] Testy jednostkowe
- [ ] Testy integracyjne
- [ ] Testy użytkowników
- [ ] Wdrożenie produkcyjne

## Kontakt i Wsparcie

### Zespół Rozwoju
- **Backend API:** Rozszerzenie istniejącego PHP API
- **MAUI App:** Nowa aplikacja mobilna
- **Database:** Wykorzystanie istniejącej struktury MySQL

### Zasoby
- Kod źródłowy desktop: `teresin_Magazyn/Tarczyn__Magazyn/`
- API PHP: `skaner_api/`
- Dokumentacja: `docs/inwentaryzacja/`

---

**Ostatnia aktualizacja:** 2024-01-15  
**Wersja dokumentacji:** 1.0  
**Status:** Kompletna analiza systemu inwentaryzacji
