<?php

//include_once 'db_pdo.php';
include_once 'Db.class.php';
include_once 'funkcje.inc';
//error_reporting(E_ALL);
//ini_set('display_errors', 1);

$db = new Db();

if (empty($argv)) {
    //$argv = array();
    $argv[1] = $_GET['db'];
    $argv[2] = $_GET['akcja'];
}

$baza_danych = $_GET['db'];
$akcja = $argv[2];
$system_id = $_GET['system_id'];
$delivery_id = $_GET["delivery_id"];

//http://25.56.91.22/wmsgg/public/skaner_api/delivery_skanowanie_etykiety.php?db=wmsgg&akcja=szukaj&system_id=6&paleta_id=547144


$komunikat = "OK";


    if ($akcja == "podglad") {


        // gdy etykieta WMS
        $sql = 'SELECT e.id,k.kod,concat("H:", m.hala," ", m.regal,"-", m.miejsce,"-", m.poziom) AS adres FROM ' . $baza_danych . '.delivery d
left join ' . $baza_danych . '.delivery_et de on de.delivery_id=d.id
left join ' . $baza_danych . '.etykiety e on e.id=de.etykieta_id
left join ' . $baza_danych . '.miejsca m on m.id=e.miejscep
left join ' . $baza_danych . '.kody k on k.id=e.kod_id
left join ' . $baza_danych . '.dlcollect dc on dc.nr_et=e.id
where d.id=' . $delivery_id . ' and dc.nr_dl is null and e.id is not null
order by de.id asc';
        
        

        $result = $db->mGetResultAsXML($sql);
//        echo $sql;
//           echo "<pre>";
//    echo print_r($result);
//    echo "</pre>";
//    return false;
        //echo "<br>" . $sql;
        if (!empty($db->errors))
            $komunikat = $db->errors;

        if (count($result) == 0) {
            $komunikat = "Brak pozycji do zrealizowania";
            return show_komunikat_xml($komunikat);
        }
        header('Content-type: text/xml');
        echo '<dane>';
        echo '<komunikat>', htmlentities($komunikat), '</komunikat>';



        foreach ($result as $index => $row) {
            echo "<etykiety>";
            echo "<id>" . $row['id'] . "</id>";
            echo "<kod>" . $row['kod'] . "</kod>";
            echo "<adres>" . $row['adres'] . "</adres>";
            echo "</etykiety>";
        }
        echo '</dane>';
    }

?>