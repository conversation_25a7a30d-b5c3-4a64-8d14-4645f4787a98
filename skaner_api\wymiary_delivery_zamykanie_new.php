<?php

//include_once 'db_pdo.php';
include_once 'Db.class.php';
include_once 'funkcje.inc';
//error_reporting(E_ALL);
//ini_set('display_errors', 1);

$db = new Db();
//
//if (empty($argv)) {
//    //$argv = array();
//    $argv[1] = $_GET['db'];
//    $argv[2] = $_GET['akcja'];
//}
//
$baza_danych = $_GET['db'];
$akcja = $_GET['akcja'];
$delivery_wymiary_head_id = $_GET['delivery_wymiary_head_id'];
$oddzial = "Gnatowice";

// sprawdź z jakiego IP przyszło żądanie
$ip = $_SERVER['REMOTE_ADDR'];
// jeśli z zaczynającego sie z 172.7.1 ro jest to oddział w Rawie
if (substr($ip, 0, 7) == "172.7.1") {
    $oddzial = "Rawa Mazowiecka";
}



$komunikat = "OK";

if ($akcja == "zamykanie") {

    function zamykanie($baza_danych, $delivery_wymiary_head_id, $oddzial, $db)
    {
        $sql = "update " . $baza_danych . ".delivery_wymiary_head  set zamkniete=1,oddzial='" . $oddzial . "' where id=" . $delivery_wymiary_head_id . " ";
        //echo $sql;
        $result = $db->mGetResultAsXML($sql);
    }

    zamykanie("wmsgg", $delivery_wymiary_head_id, $oddzial, $db);
    return xml_from_indexed_array(array('komunikat' => $komunikat));
}
