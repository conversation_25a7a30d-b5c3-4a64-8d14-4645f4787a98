# Przewodnik po Stylu Kodowania (Style Guide)

Ten dokument określa zasady i konwencje kodowania, które należy stosować w projekcie WMS, aby z<PERSON><PERSON><PERSON><PERSON> spójnoś<PERSON>, c<PERSON><PERSON><PERSON><PERSON><PERSON> i łatwość utrzymania kodu.

## 1. Ogólne Zasady

- **Język:** Komentarze, nazwy zmiennych i dokumentacja powinny być pisane w języku polskim, aby zachować spójność z istniejącym kodem.
- **Formatowanie:** Używaj konsekwentnego formatowania (wcięcia, spacje, nawiasy klamrowe), najlepiej z pomocą automatycznych narzędzi formatujących w IDE.
- **Komentarze:** Komentuj kod, który jest skomplikowany lub którego działanie nie jest oczywiste. Unikaj komentowania kodu, kt<PERSON>ry jest prosty do zrozumienia.

## 2. Konwencje dla C# (.NET Compact Framework)

- **Nazewnictwo:**
  - **<PERSON><PERSON><PERSON>, interfejsy, enumeny, metody, właściwości:** Używaj notacji `PascalCase` (np. `MojaKlasa`, `ZrobCos`).
  - **Zmienne lokalne i parametry metod:** Używaj notacji `camelCase` (np. `mojParametr`).
  - **Pola prywatne:** Rozważ użycie prefiksu `_` (np. `_prywatnePole`).
  - **Kontrolki UI:** Używaj opisowych nazw z sufiksem wskazującym na typ kontrolki (np. `loginTextBox`, `okButton`).
- **Struktura Pliku:**
  - Pola (fields)
  - Właściwości (properties)
  - Konstruktory (constructors)
  - Metody publiczne (public methods)
  - Metody prywatne (private methods)

## 3. Konwencje dla PHP

- **Nazewnictwo:**
  - **Zmienne:** Używaj notacji `snake_case` (np. `$nazwa_zmiennej`), aby zachować spójność z popularnymi standardami PHP (PSR).
  - **Funkcje:** Używaj notacji `camelCase` (np. `mojaFunkcja()`).
  - **Nazwy plików:** Używaj małych liter i notacji `snake_case` (np. `obsluga_zamowien.php`).
- **Tagi PHP:** Zawsze używaj pełnych tagów `<?php ... ?>`. Unikaj krótkich tagów `<? ... ?>`.
- **Obsługa Błędów:** Unikaj używania operatora `@` do tłumienia błędów. Zamiast tego stosuj bloki `try...catch` lub odpowiednie mechanizmy raportowania błędów.

## 4. Baza Danych (MySQL)

- **Nazewnictwo:**
  - **Tabele:** Używaj małych liter i notacji `snake_case` w liczbie mnogiej (np. `zamowienia`, `produkty`, `deklaracje`).
  - **Kolumny:** Używaj małych liter i notacji `snake_case` (np. `id_uzytkownika`, `data_utworzenia`).
- **Klucze Główne:** Każda tabela powinna mieć klucz główny, najczęściej o nazwie `id`, będący autoinkrementującą liczbą całkowitą.
- **Tabele relacji:** Nazwy tabel łączących dwie encje nazywaj według wzorca `encja1_encja2` (np. `deklaracja_osoby`, `deklaracja_wyroby`).
- **Kolumny statusowe:** Dla pól przechowujących status używaj predefiniowanych wartości liczbowych lub ciągów znakowych z dokumentacją w kodzie.

---
*Uwaga: Ten przewodnik jest dokumentem żywym i może być aktualizowany w miarę rozwoju projektu.*
