<?php

//include_once 'db_pdo.php';
include_once 'Db.class.php';
include_once 'funkcje.inc';
//error_reporting(E_ALL);
//ini_set('display_errors', 1);

$db = new Db();
//
//if (empty($argv)) {
//    //$argv = array();
//    $argv[1] = $_GET['db'];
//    $argv[2] = $_GET['akcja'];
//}
//
$baza_danych = $_GET['db'];
$akcja = $_GET['akcja'];
$delivery_id = $_GET['delivery_id'];




$komunikat = "OK";

if ($akcja == "usun_ostatni") {

    function usun($baza_danych, $delivery_id, $db) {
        $sql = "delete  FROM " . $baza_danych . ".delivery_wymiary where delivery_id=" . $delivery_id." order by id desc limit 1;";
        //echo $sql;
        $result = $db->mGetResultAsXML($sql);
    }

    usun("wmsgg",$delivery_id, $db);
    return xml_from_indexed_array(array('komunikat' => $komunikat));
}



