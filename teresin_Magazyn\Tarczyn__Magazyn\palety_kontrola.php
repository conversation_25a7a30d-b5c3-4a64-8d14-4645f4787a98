<?php

include_once '../Db.class.php';
include_once '../funkcje.inc';

$baza_danych = "wmsgg";
$db = new Db("localhost", $baza_danych);

// Pobieranie parametrów
$delivery_id = $_GET['delivery_id'];
$paleta_id = $_GET['paleta_id'];
$imie_nazwisko = $_GET['imie_nazwisko'];
$typ_palety_old_id = $_GET['typ_palety_old_id'];
$typ_palety_new_id = $_GET['typ_palety_new_id'];

// Sprawdzenie pracownika
$pracownik_id = get_pracownik($baza_danych, $imie_nazwisko, $db);
if (empty($pracownik_id)) {
    return xml_from_indexed_array(
        array('komunikat' => "Brak pracownika w bazie. Przerywam operację")
    );
}

// Sprawdzenie wymaganych parametrów
if (empty($delivery_id) || empty($typ_palety_old_id) || empty($typ_palety_new_id)) {
    return xml_from_indexed_array(
        array('komunikat' => "Brak wymaganych parametrów")
    );
}

// Przygotowanie zapytania SQL
$sql = "INSERT INTO palety_kontrola 
        (delivery_id, paleta_id, data, ts, pracownik_id, typ_palety_old_id, typ_palety_new_id) 
        VALUES 
        (" . $delivery_id . ", 
         " . $paleta_id . ", 
         CURDATE(), 
         NOW(), 
         " . $pracownik_id . ", 
         " . $typ_palety_old_id . ", 
         " . $typ_palety_new_id . ")";

// Wykonanie zapytania
try {
    $db->mGetResultAsXML($sql);
    return xml_from_indexed_array(
        array('komunikat' => "OK")
    );
} catch (Exception $e) {
    return xml_from_indexed_array(
        array('komunikat' => "Błąd podczas zapisu: " . $e->getMessage())
    );
}
