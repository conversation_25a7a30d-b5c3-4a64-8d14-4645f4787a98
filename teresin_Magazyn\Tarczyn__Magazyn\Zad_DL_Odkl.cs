﻿using System;
using System.Linq;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using MySql.Data.MySqlClient;
using System.Text.RegularExpressions;
using System.Threading;
using System.Net;


using System.Collections;

using System.IO;
using System.Xml;


namespace Tarczyn__Magazyn
{
    public partial class Zad_DL_Odkl : Form
    {
        public Zad_Main myParent = null;
        List<string> _hala = new List<string>();
        List<string> _regal = new List<string>();
        List<string> _poziom = new List<string>();
        List<string> Etykiety_dodane = new List<string>();

        public string hala = "";
        public string regal = "";
        public string miejsce = "";
        string zgodnosc_miejsca = "";
        string kompletowana_paleta_id = "";




        private Thread Skanowanie;
        private Thread Nasluchiwanie;

        XmlNode node = null;
        XmlNode node_myParent = null;


        Dictionary<string, string> rec = new Dictionary<string, string>();


        public Zad_DL_Odkl(Zad_Main MyParent, XmlNode node2, string nr_nosnika_kompletowany)
        {
            //
            InitializeComponent();
            node = node2;
            //this.etykieta_textbox.KeyPress += new KeyPressEventHandler(etykieta_textbox_KeyPress);
            this.myParent = MyParent;
            wms_wybrany.Text = Wlasciwosci.system_id;
            imie_nazwisk_label.Text = Wlasciwosci.wozek + ":" + Wlasciwosci.imie_nazwisko;
            //FERRERO_Synchronizacja.SprawdzWolnaNazwePliku();
            timer1.Enabled = false;
            kompletowana_paleta_id = nr_nosnika_kompletowany;
            myParent.nr_nosnika_kompletowany = "";


            //Lokalizacja_label.Text = "H:" + myParent.hala + " " + myParent.regal + "-" + myParent.miejsce + "-" + myParent.poziom;
            //zawartosc_label.Text = myParent.kod + " ; " + myParent.lot + " ; " + myParent.ilosc;


            wypelnij_dane();

            //if (node["kompletacja"].InnerText == "1" && node["ile_zostalo_zadan"].InnerText != "0")
            //{


            //    myParent.zadanie_head_id = node["zadanie_head_id"].InnerText;
            //    myParent.Show();
            //    myParent.ZacznijNasluchiwanie("1", "");
            //    this.Close();
            //    //kontynuacja_button.Visible = true;
            //}

            //nosnik_text.Text = myParent.paleta_id;
            textBox1.Focus();




            this.ZacznijSkanowanie();
        }




        void wypelnij_dane()
        {

            //MessageBox.Show("aa");


            Lokalizacja_label.Text = node["nowe_m_nazwa"].InnerText;
            //MessageBox.Show("bb");
            zgodnosc_miejsca = node["zgodnosc_miejsca"].InnerText;
            //MessageBox.Show("cc");
            nosnik_text.Text = "DS" + node["kompletowana_paleta_id"].InnerText + " Nr et:" + node["etykieta_id"].InnerText;
            //MessageBox.Show("bb");
            //MessageBox.Show("dd");
            label_doc_nr.Text = "DL" + node["doc_id"].InnerText;
            if (node["rodzaj_wymaganej_etykiety_odkladczej"].InnerText == "poprzednia_etykieta")
            {
                //MessageBox.Show("cc");
                label2.Text = "Wczyt. etykietę palety:";
            }
            //MessageBox.Show("ee");
            //MessageBox.Show("dd");

        }






        private void etykieta_textbox_KeyPress(object sender, KeyPressEventArgs e)
        {
            if (e.KeyChar == (char)Keys.Back)
            {
                e.Handled = true;
                //etykieta_textbox.Text = "";
            }
            else
            {
                e.Handled = false;
                return;
            }
        }






        private void Zakoncz_Skanowanie(object sender, EventArgs e)
        {
            this.Skanowanie.Abort();
            Skaner.Przewij_Skanowanie();
        }

        private void powrot_Click(object sender, EventArgs e)
        {



            //this.myParent.tryb_pracy = "pobieranie";
            this.myParent.ZacznijNasluchiwanie("", "");
            this.Skanowanie.Abort();
            Skaner.Przewij_Skanowanie();
            Wlasciwosci.CurrentOperacja = "0";
            //myParent.wczytwanieEtykiet = false;
            this.myParent.Show();
            timer1.Enabled = false;
            this.Close();



        }



        private void button1_Anuluj(object sender, EventArgs e)
        {

        }



        public void ZacznijSkanowanie()
        {
            StringBuilder login = new StringBuilder();
            Skaner.UstawTryb_String(login);
            Skaner.Zacznij_Skanowanie();
            this.Skanowanie = new Thread(() => this.CheckString(login));
            this.Skanowanie.IsBackground = true;
            this.Skanowanie.Start();
        }



        private void CheckString(StringBuilder cc)
        {
            EventHandler method = null; //to jest delegat

            while (true)
            {
                if (cc.ToString() != "")
                {
                    if (method == null)
                    {
                        method = delegate
                        {
                            this.dodawanie(cc.ToString());
                        };
                    }
                    this.Invoke(method);
                    return;
                }
                Thread.Sleep(200);
            }
        }

        public string adres_ip_drukarki()
        {
            for (int i = 0; i < 3; i++)
            {
                PoleTekstowe XB = new PoleTekstowe("Wczytaj drukarkę do " + Environment.NewLine + "etykiet wysyłkowych", "");
                if (XB.ShowDialog() == DialogResult.OK)
                {
                    if (XB.wartosc_wpisana == "") return "";

                    if (XB.wartosc_wpisana.Substring(0, 2) == "IP")
                    {

                        return XB.wartosc_wpisana.Substring(2, XB.wartosc_wpisana.Length - 2);
                    }
                    else
                    {
                        MessageBox.Show("To nie jest etykieta drukarki");
                        return "";
                    }

                }
                else
                {
                    return "";
                }

            }
            return "";
        }


        // 'Internal' - wyszukuje w bazie etykiete i wypełnia pola
        private void dodawanie(string ops)
        {

            Skaner.Przewij_Skanowanie();
            //MessageBox.Show("dodawanie:" + node["zadanie_head_id"].InnerText);
            //MessageBox.Show("dodawanie:" + kompletowana_paleta_id);



            XmlDocument doc1 = WebService.Pobierz_XmlDocument("delivery_kompletacja_odkladanie.php?akcja=realizacja_zadania&zadanie_head_id=" + node["zadanie_head_id"].InnerText + "&imie_nazwisko=" + Uri.EscapeUriString(Wlasciwosci.imie_nazwisko) + "&skan=" + ops);
            //MessageBox.Show("3:");
            XmlNodeList xmlnode = null;
            XmlNode node_local = null;
            xmlnode = doc1.GetElementsByTagName("dane");

            foreach (XmlNode wynik in xmlnode)
            {
                node_local = wynik;
            }

            if (node_local["komunikat"].InnerText == "OK")
            {
                if (node_local["czy_koniec_zadania"].InnerText == "TAK")
                {

                    string adres_ip_print = adres_ip_drukarki();
                    //MessageBox.Show("" + aa);
                    //
                    if (adres_ip_print != "")
                    {
                        PoleTekstowe XC = new PoleTekstowe("Podaj ilość " + Environment.NewLine + "etykiet wysyłkowych", node_local["ile_kompletowanych_palet"].InnerText);
                        if (XC.ShowDialog() == DialogResult.OK)
                        {
                            if (XC.wartosc_wpisana != "")
                            {
                                XmlDocument doc2 = WebService.Pobierz_XmlDocument("delivery_wydruk_wysylka.php?baza_danych=" + node["baza_danych"].InnerText + "&delivery_id=" + node["doc_id"].InnerText + "&adres_ip_drukarki=" + adres_ip_print + "&ilosc_etykiet=" + XC.wartosc_wpisana);
                            }
                        }
                    }

                    //if (adres_ip_print != "")
                    //{
                    //    PoleTekstowe XC = new PoleTekstowe("Podaj ilość " + Environment.NewLine + "etykiet wysyłkowych", );
                    //    if (XC.ShowDialog() == DialogResult.OK)
                    //    {
                    //        if (XC.wartosc_wpisana != "")
                    //        {
                    //            //MessageBox.Show("XC:"+XC.wartosc_wpisana);
                    //            //MessageBox.Show("XC:" + Regex.IsMatch(XC.wartosc_wpisana, @"^\d+$"));
                    //            //return;
                    //            if (Regex.IsMatch(XC.wartosc_wpisana, @"^\d+$") == true && Convert.ToInt32(XC.wartosc_wpisana) < 100)
                    //            {
                    //                //MessageBox.Show("delivery_wydruk_wysylka.php?baza_danych=wmsgg&delivery_id=45581&adres_ip_drukarki=***********&ilosc_etykiet=" + XC.wartosc_wpisana);
                    //                XmlDocument doc2 = WebService.Pobierz_XmlDocument("delivery_wydruk_wysylka.php?baza_danych=wmsgg&delivery_id=" + node_local["doc_id"].InnerText + "&adres_ip_drukarki=" + adres_ip_print + "&ilosc_etykiet=" + XC.wartosc_wpisana);
                    //            }
                    //            else
                    //            {

                    //                MessageBox.Show("Można tylko liczby od 1-100");
                    //                PoleTekstowe XD = new PoleTekstowe("Podaj ilość " + Environment.NewLine + "etykiet wysyłkowych", "2");
                    //                if (XD.ShowDialog() == DialogResult.OK)
                    //                {
                    //                    //MessageBox.Show("XD:"+XD.wartosc_wpisana);
                    //                    //MessageBox.Show("XD:" + Regex.IsMatch(XD.wartosc_wpisana, @"^\d+$"));
                    //                    //MessageBox.Show("delivery_wydruk_wysylka.php?baza_danych=wmsgg&delivery_id=45581&adres_ip_drukarki=172.6.1.249249&ilosc_etykiet=" + XD.wartosc_wpisana);
                    //                    XmlDocument doc2 = WebService.Pobierz_XmlDocument("delivery_wydruk_wysylka.php?baza_danych=wmsgg&delivery_id=" + node_local["doc_id"].InnerText + "&adres_ip_drukarki=" + adres_ip_print + "&ilosc_etykiet=" + XD.wartosc_wpisana);
                    //                }
                    //            }
                    //        } 
                    //    }

                    //}



                    myParent.zadanie_head_id = "";
                }
                if (node_local["czy_koniec_kompletacji_zadania"].InnerText == "TAK")
                {
                    //myParent.zadanie_head_id = "";
                    //myParent.zadanie_head_id = "";
                }




                myParent.Show();
                if (node_local["czy_koniec_zadania"].InnerText == "NIE")
                {
                    //myParent.zadanie_head_id = "";
                    myParent.ZacznijNasluchiwanie("", "");
                }
                this.Close();
            }
            else
            {
                MessageBox.Show(node_local["komunikat"].InnerText);
                this.ZacznijSkanowanie();
            }
        }













        private void timer1_Tick(object sender, EventArgs e)
        {
            this.Skanowanie.Abort();
            Skaner.Przewij_Skanowanie();

            //myParent.wczytwanieEtykiet = false;
            this.myParent.Show();
            timer1.Enabled = false;
            this.Close();


        }



        #region fullscreen
        private void fullscreenmode()
        {
            this.Width = Screen.PrimaryScreen.Bounds.Width;
            this.Height = Screen.PrimaryScreen.Bounds.Height;
            this.Left = 0;
            this.Top = 0;
        }
        #endregion

        private void button1_Click(object sender, EventArgs e)
        {

        }

        private void kontynuacja_button_Click(object sender, EventArgs e)
        {

        }











    }
}