<?php

include_once 'Db.class.php';
//include_once 'funkcje.inc';
//error_reporting(E_ALL);
//ini_set('display_errors', 1);

$db = new Db();

if (empty($argv)) {
    //$argv = array();
    $argv[1] = $_GET['db'];
    $argv[2] = $_GET['tb'];
    $argv[3] = $_GET['id'];
}
//
//if ($argv[1] == '1') {
//    $baza_danych = 'wmsgg';
//}
//if ($argv[1] == '2') {
//    $baza_danych = 'wmsftl';
//}
//if ($argv[1] == '3') {
//    $baza_danych = 'prod';
//}

$baza_danych = $argv[1];


$tabela = $argv[2];

if (!empty($argv[3])) {
    $id = $argv[3];
}



//if ($_GET['db'] == '1') {
//    $baza_danych = 'wmsgg';
//}
//
//$tabela = $_GET['tb'];
//$id = $_GET['id'];

echo "<br>aa,$baza_danych";
if ($baza_danych == "wmsgg" || $baza_danych == "wmsftl") {


    echo "<br>bb";

    if ($tabela == "bolton") {
        echo "<br>cc";
        $typ = "6";
        $doc_type = "6";
        
        $sql = "select *,
(SELECT count(1) FROM zadania_dane zd 
left join zadania_head zh on zh.id=zd.zadanie_head_id 
where zd.paleta_id=aa.paleta_id  and zh.doc_id=0 and typ=6 and zd.status=1) as ile from (SELECT e.* FROM wmsgg.miejsca m
left join etykiety e on e.miejscep=m.id
left join zadania_dane zd on zd.paleta_id=e.paleta_id
left join zadania_head zh on zh.id=zd.zadanie_head_id and zh.doc_id=0 and typ=6
left join delivery_et de on de.etykieta_id=e.id
where 
(
(((m.regal='109' and m.miejsce between '41' and '56' and m.poziom='A') or (m.regal='116' and m.miejsce between '26' and '40' and m.poziom='A')) and  e.system_id=49) 
)
and e.active=1  and de.etykieta_id is null and zh.id is null group by e.paleta_id) as aa
having (ile is null or ile<1) ";

        echo "<br>" . $sql;
        $system_id_id=49;
        $id=0;
        
//        or
//(((m.regal='112' and m.miejsce between '32' and '40' and m.poziom='A') or (m.regal='114' and m.miejsce between '41' and '48' and m.poziom='A')) and  e.system_id=34) 
        
        $result = $db->mGetResult($sql);
        
        if (!empty($result)) {
            $sql_ins_zad = "insert into wmsgg.zadania_head(system_id,baza_danych,typ,doc_id,doc_type,ts,status_dokumentu,active) values"
                    . " ('" . $system_id_id . "','$baza_danych','$typ','" . $id . "','$doc_type',NOW(),1,1)";
            

            $zadanie_head_id = $db->mGetResult($sql_ins_zad);

            


            $query_zap1 = "UPDATE wmsgg.zadania_head z set zadania_head_rodzic_id=$zadanie_head_id where id='$zadanie_head_id' limit 1";
            echo "<br>" . $query_zap1;
            //$result_zadanie2 = $db->mGetResult($query_zap1);
        }

        foreach ($result as $key => $aRow4) {
            $aRow4['stanowisko_pracy_id'] = 3;
            $sql_ins_zad = "insert into wmsgg.zadania_dane(zadanie_head_id,status, stare_m, nowe_m, paleta_id, etykieta_id, kod_id, lot, ilosc,kompletacja,wysokie,stanowisko_id) values "
                    . "('" . $zadanie_head_id . "','1','" . $aRow4['miejscep'] . "','0','" . $aRow4['paleta_id'] . "','0','" . $aRow4['kod_id'] . "','','" . $aRow4['ilosc'] . "','0','1','" . $aRow4['stanowisko_pracy_id'] . "')";
            echo "<br>" . $sql_ins_zad;
            
            
           
        
            $result_zadanie4 = $db->mGetResult($sql_ins_zad);
            //exit();
        }
    }






















    if ($tabela == "list_control") {
        echo "<br>cc";
        $typ = "6";
        $doc_type = "6";
        $sql = "SELECT listcontrol_system_id as system_id,l.miejsce_id as miejscep,l.id, e.kod_id,e.ilosc FROM $baza_danych.list_control l
left join $baza_danych.etykiety e on e.listcontrol_id=l.id and e.active=1
where l.id=$id limit 1";

        echo "<br>" . $sql;
        $result = $db->mGetResult($sql);

        foreach ($result as $key => $aRow) {


            $system_id_id = $aRow['system_id'];

            $doc_id = $aRow['id'];
            $miejsce_kompletacji = $aRow['miejscep'];

            $sql = "SELECT zh.id from wmsgg.zadania_head zh left join wmsgg.zadania_dane zd on  zd.zadanie_head_id=zh.id"
                    . " where zh.baza_danych='$baza_danych' and system_id=$system_id_id and zh.typ='$typ' and doc_id=$doc_id and zd.id is not null limit 1";
            echo "<br>" . $sql;
            $result24 = $db->mGetResult($sql);


            if (empty($result24)) {
                echo "bbbbbbb";


                //echo "asdasd" . $aRow2;
                $sql_ins_zad = "insert into wmsgg.zadania_head(system_id,baza_danych,typ,doc_id,doc_type,ts,status_dokumentu) values"
                        . " ('" . $system_id_id . "','$baza_danych','$typ','" . $id . "','$doc_type',NOW(),0)";
                echo "<br>" . $sql_ins_zad;

                $zadanie_head_id = $db->mGetResult($sql_ins_zad);



                $query_zap1 = "UPDATE wmsgg.zadania_head z set zadania_head_rodzic_id=$zadanie_head_id where id='$zadanie_head_id' limit 1";
                echo "<br>" . $query_zap1;
                $result_zadanie2 = $db->mGetResult($query_zap1);


                /// dodawanie pozycji dl

                $sql4 = "SELECT listcontrol_system_id as system_id,l.miejsce_id,l.id, e.kod_id,e.ilosc,e.paleta_id,m.stanowisko_pracy_id
FROM $baza_danych.list_control l
left join $baza_danych.listcontrol_palety lp on lp.listcontrol_id=l.id
    
                        left join $baza_danych.etykiety e on e.paleta_id=lp.paleta_id and e.active=1 
                        left join $baza_danych.miejsca m on e.miejscep=m.id where l.id=$id and e.id is not null  group by e.paleta_id order by e.ts asc"; //and m.regal='RMP'
//                if ($system_id_id == "31") {
//                    $sql4.=" ,k.kod asc,m.hala asc,m.regal asc,m.miejsce asc,m.poziom asc";
//                } else {
//                    $sql4.=" , m.hala asc,m.regal asc,m.miejsce asc,m.poziom asc";
//                }

                echo "<br>" . $sql4;
                $result4 = $db->mGetResult($sql4); //mysql_query($sql4, $conn);

                foreach ($result4 as $aRow4) {

                    //$kompletacja = 1;
                    $sql_ins_zad = "insert into wmsgg.zadania_dane(zadanie_head_id,status, stare_m, nowe_m, paleta_id, etykieta_id, kod_id, lot, ilosc,kompletacja,wysokie,stanowisko_id) values "
                            . "('" . $zadanie_head_id . "','0','" . $miejsce_kompletacji . "','0','" . $aRow4['paleta_id'] . "','0','" . $aRow4['kod_id'] . "','','" . $aRow4['ilosc'] . "','0','1','" . $aRow4['stanowisko_pracy_id'] . "')";
                    echo "<br>" . $sql_ins_zad;
                    $result_zadanie4 = $db->mGetResult($sql_ins_zad);
                }
            }
        }
    }





    if ($tabela == "docin") {
        echo "<br>cc";
        $typ = "7";
        $doc_type = "9";
//        $sql = "SELECT e.system_id,e.miejscep,l.id, e.kod_id,e.ilosc FROM $baza_danych.list_control l
//left join $baza_danych.etykiety e on e.listcontrol_id=l.id and e.active=1
//where l.id=$id";
        $sql = "SELECT e.system_id,e.miejscep,d.id, e.kod_id,e.ilosc from
                 $baza_danych.etykiety e
                left join $baza_danych.docin d on e.docin_id=d.id
                left join $baza_danych.systemy s on s.wartosc=e.system_id
                where d.id=$id and e.active=1 and s.zadania_automat_docin=1 limit 1";

        echo "<br>" . $sql;
        // chwilowa blokada
        //exit();

        $result = $db->mGetResult($sql);
        foreach ($result as $aRow) {


            $system_id_id = $aRow['system_id'];

            $doc_id = $aRow['id'];
            $miejsce_kompletacji = $aRow['miejscep'];

            $sql = "SELECT zh.id from wmsgg.zadania_head zh left join wmsgg.zadania_dane zd on  zd.zadanie_head_id=zh.id"
                    . " where zh.baza_danych='$baza_danych' and system_id=$system_id_id and zh.typ='$typ' and doc_id=$doc_id and zd.id is not null limit 1";
            echo "<br>" . $sql;
            $result24 = $db->mGetResult($sql);

            if (empty($result24)) {
                echo "bbbbbbb";


                //echo "asdasd" . $aRow2;
                $sql_ins_zad = "insert into wmsgg.zadania_head(system_id,baza_danych,typ,doc_id,doc_type,ts,status_dokumentu) values"
                        . " ('" . $system_id_id . "','$baza_danych','$typ','" . $id . "','$doc_type',NOW(),0)";
                echo "<br>" . $sql_ins_zad;
                $zadanie_head_id = $db->mGetResult($sql_ins_zad);




                $query_zap1 = "UPDATE wmsgg.zadania_head z set zadania_head_rodzic_id=$zadanie_head_id where id='$zadanie_head_id' limit 1";
                echo "<br>" . $query_zap1;
                $result_zadanie2 = $db->mGetResult($query_zap1);


                /// dodawanie pozycji dl

                $sql4 = "SELECT e.system_id,e.miejscep,d.id, e.kod_id,e.ilosc,e.paleta_id,m.stanowisko_pracy_id  "
                        . " from $baza_danych.etykiety e left join $baza_danych.docin d on e.docin_id=d.id
                            left join $baza_danych.miejsca m on e.miejscep=m.id 
                where d.id=$id and e.active=1 and m.regal='RMP' group  by e.paleta_id order by e.ts asc";
//                if ($system_id_id == "31") {
//                    $sql4.=" ,k.kod asc,m.hala asc,m.regal asc,m.miejsce asc,m.poziom asc";
//                } else {
//                    $sql4.=" , m.hala asc,m.regal asc,m.miejsce asc,m.poziom asc";
//                }

                echo "<br>" . $sql;
                $result4 = $db->mGetResult($sql4); //mysql_query($sql4, $conn);

                foreach ($result4 as $aRow4) {


                    //$kompletacja = 1;
                    $sql_ins_zad = "insert into wmsgg.zadania_dane(zadanie_head_id,status, stare_m, nowe_m, paleta_id, etykieta_id, kod_id, lot, ilosc,kompletacja,wysokie,stanowisko_id) values "
                            . "('" . $zadanie_head_id . "','0','" . $aRow4['miejscep'] . "','0','" . $aRow4['paleta_id'] . "','0','" . $aRow4['kod_id'] . "','','" . $aRow4['ilosc'] . "','0','1','" . $aRow4['stanowisko_pracy_id'] . "')";
                    echo "<br>" . $sql_ins_zad;
                    $result_zadanie4 = $db->mGetResult($sql_ins_zad); //mysql_query($sql4, $conn);
                }
            }
        }
    }
}


//
//$komunikat.=//mysql_error();
////echo $komunikat;
//header('Content-type: text/xml');
//echo '<dane>';
//echo '<komunikat>', htmlentities($komunikat), '</komunikat>';
////echo '<ilosc_pozostala>', htmlentities($ilosc_pozostala), '</ilosc_pozostala>';
////echo '<nowa_etykieta>', htmlentities($nowa_etykieta), '</nowa_etykieta>';
////echo '<kod>', htmlentities($kod), '</kod>';
//echo '</dane>';
?>