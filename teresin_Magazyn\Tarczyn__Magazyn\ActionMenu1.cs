﻿using System;
using System.Linq;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using System.Threading;

namespace Tarczyn__Magazyn
{
    public partial class ActionMenu : Form, IZad_Parent,IGlobal
    {

        public MainMenu myParent;
        public WLAN_Status myWlan = null;
        private Thread Skanowanie;

        public ActionMenu(MainMenu parent)
        {
            FullScreenMode.OknoOFF(this);
            InitializeComponent();

            myParent = parent;
            myWlan = myParent.myWlan;

            wms_wybrany.Text = Wlasciwosci.system_id;
            imie_nazwisk_label.Text = Wlasciwosci.wozek + ":" + Wlasciwosci.imie_nazwisko;
        }

        public int Get_Signal_Int()
        {
            return myParent.myWlan.get_Signal_int();
        }

        public void ZacznijSkanowanie()
        {
            StringBuilder login = new StringBuilder();
            Skaner.UstawTryb_String(login);
            Skaner.Zacznij_Skanowanie();
            this.Skanowanie = new Thread(() => this.CheckString(login));
            this.Skanowanie.IsBackground = true;
            this.Skanowanie.Start();
        }

        private void CheckString(StringBuilder cc)
        {
            EventHandler method = null; //to jest delegat

            while (true)
            {
                if (cc.ToString() != "")
                {
                    if (method == null)
                    {
                        method = delegate
                        {
                            this.dodawanie(cc.ToString());
                        };
                    }
                    this.Invoke(method);
                    return;
                }
                Thread.Sleep(200);
            }
        }
        private void dodawanie(string ops)
        {

        }

        

        private void Wyjscie_Click(object sender, EventArgs e)
        {
            if (Wlasciwosci.wozek != "0")
            {


                //string zap = "SELECT 1 as ile FROM operacje o WHERE o.wozek='" + Wlasciwosci.wozek + "' AND o.typ_operacji='MOTO_OUT' and etykieta_id='" + Wlasciwosci.wozek + "' and imie_nazwisko='" + Wlasciwosci.imie_nazwisko + "' AND o.ts_ins like concat(CURDATE(),'%') ORDER BY o.id desc limit 1;";
                string zap = "SELECT 1 as ile  FROM wozki_historia w WHERE w.wozek_id='" + Wlasciwosci.wozek + "' and w.imie_nazwisko='" + Wlasciwosci.imie_nazwisko + "' AND typ_operacji='wylogowanie' AND w.ts like concat(CURDATE(),'%') ORDER BY w.id desc limit 1; ";               
                
                object obj3 = BazaDanychExternal.Wyczytaj_Tabele(zap);
                DataTable tabela2 = (DataTable)obj3;
                
                if (tabela2.Rows.Count == 0)
                {

                    DialogResult result3 = MessageBox.Show("Czy chcesz wprowadzić motogodziny?",
                                        "Czy chcesz wprowadzić motogodziny?",
                                        MessageBoxButtons.YesNo,
                                        MessageBoxIcon.Question,
                                        MessageBoxDefaultButton.Button2);
                    if (result3 == DialogResult.Yes)
                    {
                        string operac_id_global = "";

                        //string zapyt = "update docnumber d set last=last+1 WHERE d.name='operacja_id'; ";
                        //BazaDanychExternal.Wyczytaj_Jedna_Wartosc(zapyt);
                        //string zapytanie = "SELECT last FROM docnumber d WHERE d.name='operacja_id';";
                        //operac_id_global = (string)BazaDanychExternal.Wyczytaj_Jedna_Wartosc(zapytanie);

                        Wozek XB = new Wozek(-1, "Ilość motogodzin wózka:");
                        if (XB.ShowDialog() == DialogResult.OK)
                        {
                            //BazaDanychExternal.DokonajUpdate("insert into operacje(etykieta_id, doc_type, doc_nr, imie_nazwisko, typ_operacji,system_id,wozek,operac_id,ilosc) values('" + Wlasciwosci.wozek + "','MOTO','" + XB.ilosc_wpisana + "','" + Wlasciwosci.imie_nazwisko + "','MOTO_IN','0','" + Wlasciwosci.wozek + "','" + operac_id_global + "','0');");
                            BazaDanychExternal.DokonajUpdate("insert into wozki_historia(wozek_id,   motogodziny,typ_operacji,czy_sprawny_wozek,uwagi,imie_nazwisko) values('" + Wlasciwosci.wozek + "','" + XB.ilosc_wpisana + "','wylogowanie','" + XB.czy_wozek_sprawny + "','" + XB.uwagi + "','" + Wlasciwosci.imie_nazwisko + "');");
                        }

                    }


                    
                }

            }


            BazaDanychExternal.ChangeHost(Wlasciwosci.Varbaza, Wlasciwosci.Varhost, Wlasciwosci.VarLoginMYSQL, Wlasciwosci.VarHasloMYSQL);
            myParent.Show();
            this.Close();
            Wlasciwosci.id_Pracownika = "0";
        }


        


        private void button4_Click(object sender, EventArgs e)
        {
            if (myParent.myWlan.get_Signal_int() < 2)
            {
                MessageBox.Show("Trwa łączenie. Spróbuj ponownie...");
                return;
            }
            //GnG1 nowy = new GnG1(this);
            //nowy.Show();
            this.Hide();
        }

        private void button5_Click(object sender, EventArgs e)
        {
            if (myParent.myWlan.get_Signal_int() < 2)
            {
                MessageBox.Show("Trwa łączenie. Spróbuj ponownie...");
                return;
            }
            //GnG2 nowy = new GnG2(this);
            //nowy.Show();
            this.Hide();
        }

        private void button6_Click(object sender, EventArgs e)
        {
            if (myParent.myWlan.get_Signal_int() < 2)
            {
                MessageBox.Show("Trwa łączenie. Spróbuj ponownie...");
                return;
            }
            GnGNew nowy = new GnGNew(this);
            nowy.Show();
            this.Hide();

        }

        private void button7_Click(object sender, EventArgs e)
        {
            
        }

        private void przelacz_system_id()
        {
            /*
            switch (Wlasciwosci.TrybActionMenu)
            {
                case 0:
                    {
                        //prod
                        button2.Visible = true;
                        button1.Visible = true;
                        button3.Visible = true;
                        button7.Visible = false;
                        button6.Visible = false;
                        button11.Visible = false;
                        button21.Visible = true;
                        BazaDanychExternal.ChangeHost(Wlasciwosci.Varbaza, Wlasciwosci.Varhost, Wlasciwosci.VarLoginMYSQL, Wlasciwosci.VarHasloMYSQL);
                        comboBox1.Visible = false;
                        break;
                    }
                case 1:
                    {
                        // gg
                        button2.Visible = true;
                        comboBox1.Visible = true;
                        comboBox1.SelectedIndex = 0;
                        button1.Visible = false;
                        button3.Visible = false;
                        button7.Visible = true;
                        button6.Visible = true;
                        button11.Visible = true;
                        button21.Visible = false;
                        BazaDanychExternal.ChangeHost(Wlasciwosci.GNG, Wlasciwosci.Varhost, Wlasciwosci.VarLoginMYSQL, Wlasciwosci.VarHasloMYSQL);
                        break;
                    }
            }
            */
        }

        private void ActionMenu_Load(object sender, EventArgs e)
        {
            przelacz_system_id();
        }

        private void button8_Click(object sender, EventArgs e)
        {
            

        }

        private void button9_Click(object sender, EventArgs e)
        {
            /*
            if (myParent.myWlan.get_Signal_int() < 2)
            {
                MessageBox.Show("Trwa łączenie. Spróbuj ponownie...");
                return;
            }
            InwentaryzacjaGG tt = new InwentaryzacjaGG(this);
            tt.Show();
            this.Hide();
             */
        }

        
        private void button11_Click(object sender, EventArgs e)
        {
            
        }

        private void button12_Click(object sender, EventArgs e)
        {

            if (myParent.myWlan.get_Signal_int() < 2)
            {
                MessageBox.Show("Trwa łączenie. Spróbuj ponownie...");
                return;
            }
            ZmianaMiejscRegalNoActive tt = new ZmianaMiejscRegalNoActive(this);
            tt.Show();
            this.Hide();
        }

        private void button13_Click(object sender, EventArgs e)
        {
            if (myParent.myWlan.get_Signal_int() < 2)
            {
                MessageBox.Show("Trwa łączenie. Spróbuj ponownie...");
                return;
            }
            //SegregacjaGNG gg = new SegregacjaGNG(this);
            //gg.Show();
            this.Hide();
        }

        private void button14_Click(object sender, EventArgs e)
        {
            if (myParent.myWlan.get_Signal_int() < 2)
            {
                MessageBox.Show("Trwa łączenie. Spróbuj ponownie...");
                return;
            }
            ZmianaMiejscaKartonu gg = new ZmianaMiejscaKartonu(this);
            gg.Show();
            this.Hide();
        }

        

        private void button16_Click(object sender, EventArgs e)
        {
            /*
            if (myParent.myWlan.get_Signal_int() < 2)
            {
                MessageBox.Show("Trwa łączenie. Spróbuj ponownie...");
                return;
            }
            UpdateEtykietaMenu nowy = new UpdateEtykietaMenu(this);
            nowy.Show();
            this.Hide();
             */
        }


        private void button18_Click(object sender, EventArgs e)
        {
            if (myParent.myWlan.get_Signal_int() < 2)
            {
                MessageBox.Show("Trwa łączenie. Spróbuj ponownie...");
                return;
            }
            PrzepakowanieEtykiet nowy = new PrzepakowanieEtykiet(this);
            nowy.Show();
            this.Hide();

        }

        private void button20_Click(object sender, EventArgs e)
        {
            WyborBazy XA = new WyborBazy();
            if (XA.ShowDialog() == DialogResult.OK)
            {
                //ActionMenu v = new ActionMenu(myParent);
                //v.Show();
                //this.Close();
                przelacz_system_id();
                wms_wybrany.Text = Wlasciwosci.system_id;
                imie_nazwisk_label.Text = Wlasciwosci.wozek + ":" + Wlasciwosci.imie_nazwisko;
            }
            Wlasciwosci.CurrentOperacja = "0";
        }

        private void button21_Click(object sender, EventArgs e)
        {
            

        }

        private void button3_Click(object sender, EventArgs e)
        {
            if (myParent.myWlan.get_Signal_int() < 2)
            {
                MessageBox.Show("Trwa łączenie. Spróbuj ponownie...");
                return;
            }
            ProdukcjaMenu tt = new ProdukcjaMenu(this);
            tt.Show();
            this.Hide();
        }

        private void button19_Click(object sender, EventArgs e)
        {

        }

        private void button1_Click_1(object sender, EventArgs e)
        {
            if (myParent.myWlan.get_Signal_int() < 2)
            {
                MessageBox.Show("Trwa łączenie. Spróbuj ponownie...");
                return;
            }
            MenuDostawy tt = new MenuDostawy(this);
            tt.Show();
            this.Hide();
        }



        private void button23_Click(object sender, EventArgs e)
        {
            if (myParent.myWlan.get_Signal_int() < 2)
            {
                MessageBox.Show("Trwa łączenie. Spróbuj ponownie...");
                return;
            }
            Info_Paleta tt = new Info_Paleta(this);
            tt.Show();
            this.Hide();
        }

        private void button19_Click_1(object sender, EventArgs e)
        {
            
        }


        private void button27_Click(object sender, EventArgs e)
        {
            if (myParent.myWlan.get_Signal_int() < 2)
            {
                MessageBox.Show("Trwa łączenie. Spróbuj ponownie...");
                return;
            }
            Kolektor tt = new Kolektor(this);
            tt.Show();
            this.Hide();
        }

        private void button28_Click(object sender, EventArgs e)
        {
            if (myParent.myWlan.get_Signal_int() < 2)
            {
                MessageBox.Show("Trwa łączenie. Spróbuj ponownie...");
                return;
            }
            //ZmianaMiejscaPaletowego gg = new ZmianaMiejscaPaletowego(this);
            ZmianaMiejscWysokie gg = new ZmianaMiejscWysokie(this);
            gg.Show();
            this.Hide();


        }

        private void button29_Click(object sender, EventArgs e)
        {
            Operacje gg = new Operacje(this);
            gg.Show();
            this.Hide();
        }

        


        private void button7_Click_1(object sender, EventArgs e)
        {
            if (myParent.myWlan.get_Signal_int() < 2)
            {
                MessageBox.Show("Trwa łączenie. Spróbuj ponownie...");
                return;
            }
            InfoDostawa nowy = new InfoDostawa(this);
            nowy.Show();
            this.Hide(); 
        }

        private void button31_Click(object sender, EventArgs e)
        {
            if (myParent.myWlan.get_Signal_int() < 2)
            {
                MessageBox.Show("Trwa łączenie. Spróbuj ponownie...");
                return;
            }
            InwentaryzacjaGG tt = new InwentaryzacjaGG(this);
            tt.Show();
            this.Hide();
        }

        private void button11_Click_1(object sender, EventArgs e)
        {
            if (myParent.myWlan.get_Signal_int() < 2)
            {
                MessageBox.Show("Trwa łączenie. Spróbuj ponownie...");
                return;
            }
            Inwentaryzacja tt = new Inwentaryzacja(this);
            tt.Show();
            this.Hide();
        }

        private void button30_Click(object sender, EventArgs e)
        {
            if (myParent.myWlan.get_Signal_int() < 2)
            {
                MessageBox.Show("Trwa łączenie. Spróbuj ponownie...");
                return;
            }
            Wolne_Miejsca tt = new Wolne_Miejsca(this);
            tt.Show();
            this.Hide();
        }

        private void button32_Click(object sender, EventArgs e)
        {
            if (myParent.myWlan.get_Signal_int() < 2)
            {
                MessageBox.Show("Trwa łączenie. Spróbuj ponownie...");
                return;
            }
            Wolne_Poziomy tt = new Wolne_Poziomy(this);
            tt.Show();
            this.Hide();
        }

        private void button33_Click(object sender, EventArgs e)
        {
            if (myParent.myWlan.get_Signal_int() < 2)
            {
                MessageBox.Show("Trwa łączenie. Spróbuj ponownie...");
                return;
            }

            //if (Wlasciwosci.wozek_ilosc_nosnikow == 0)
            //{
            //    MessageBox.Show("Obecny pojazd może maksymalnie nosników:0");
            //    return;
            //}
            Zad_Main tt = new Zad_Main(this);
            tt.Show();
            this.Hide();

        }

        private void button34_Click(object sender, EventArgs e)
        {
            if (myParent.myWlan.get_Signal_int() < 2)
            {
                MessageBox.Show("Trwa łączenie. Spróbuj ponownie...");
                return;
            }
            InwentaryzacjaMiejsca tt = new InwentaryzacjaMiejsca(this);
            tt.Show();
            this.Hide();

        }

        private void button35_Click(object sender, EventArgs e)
        {
            if (myParent.myWlan.get_Signal_int() < 2)
            {
                MessageBox.Show("Trwa łączenie. Spróbuj ponownie...");
                return;
            }
            TworzenieDL_najnowszeV2 tt = new TworzenieDL_najnowszeV2(this);
            tt.Show();
            this.Hide();
        }

        private void button36_Click(object sender, EventArgs e)
        {
            if (myParent.myWlan.get_Signal_int() < 2)
            {
                MessageBox.Show("Trwa łączenie. Spróbuj ponownie...");
                return;
            }
            PrzeklejanieKartonow tt = new PrzeklejanieKartonow(this);
            tt.Show();
            this.Hide();

        }

        private void button21_Click_1(object sender, EventArgs e)
        {
            if (myParent.myWlan.get_Signal_int() < 2)
            {
                MessageBox.Show("Trwa łączenie. Spróbuj ponownie...");
                return;
            }
            Delivery_kontrola tt = new Delivery_kontrola(this);
            tt.Show();
            this.Hide();
        }
        private void button37_Click(object sender, EventArgs e)
        {
            
        }

        private void button38_Click(object sender, EventArgs e)
        {
            if (myParent.myWlan.get_Signal_int() < 2)
            {
                MessageBox.Show("Trwa łączenie. Spróbuj ponownie...");
                return;
            }
            KolektorWysylka tt = new KolektorWysylka(this);
            tt.Show();
            this.Hide();
        }
        private void button37_Click_1(object sender, EventArgs e)
        {
            if (myParent.myWlan.get_Signal_int() < 2)
            {
                MessageBox.Show("Trwa łączenie. Spróbuj ponownie...");
                return;
            }
            UpdateEtykietaMenu tt = new UpdateEtykietaMenu(this);
            tt.Show();
            this.Hide();
        }

        private void button1_Click(object sender, EventArgs e)
        {
            if (myParent.myWlan.get_Signal_int() < 2)
            {
                MessageBox.Show("Trwa łączenie. Spróbuj ponownie...");
                return;
            }
            ZmianaMiejscRegalNoActiveApiOne tt = new ZmianaMiejscRegalNoActiveApiOne(this);
            tt.Show();
            this.Hide();
        }

        private void button4_Click_1(object sender, EventArgs e)
        {
            if (myParent.myWlan.get_Signal_int() < 2)
            {
                MessageBox.Show("Trwa łączenie. Spróbuj ponownie...");
                return;
            }
            SzukanieKodu tt = new SzukanieKodu(this,"");
            tt.Show();
            this.Hide();

        }

        private void button5_Click_1(object sender, EventArgs e)
        {
            if (myParent.myWlan.get_Signal_int() < 2)
            {
                MessageBox.Show("Trwa łączenie. Spróbuj ponownie...");
                return;
            }
            KonsolidacjaEtykiet tt = new KonsolidacjaEtykiet(this);
            tt.Show();
            this.Hide();

        }

        private void button8_Click_1(object sender, EventArgs e)
        {
            if (myParent.myWlan.get_Signal_int() < 2)
            {
                MessageBox.Show("Trwa łączenie. Spróbuj ponownie...");
                return;
            }
            AwizacjaDostawRozkladanie nowy = new AwizacjaDostawRozkladanie(this);
            nowy.Show();
            this.Hide();
        }

        private void button9_Click_1(object sender, EventArgs e)
        {
            if (myParent.myWlan.get_Signal_int() < 2)
            {
                MessageBox.Show("Trwa łączenie. Spróbuj ponownie...");
                return;
            }
            ArtsanaWyszukiwanieEtykiety nowy = new ArtsanaWyszukiwanieEtykiety(this);
            nowy.Show();
            this.Hide();

        }

        private void button24_Click(object sender, EventArgs e)
        {
            if (myParent.myWlan.get_Signal_int() < 2)
            {
                MessageBox.Show("Trwa łączenie. Spróbuj ponownie...");
                return;
            }
            Delivery_Szykowanie nowy = new Delivery_Szykowanie(this);
            nowy.Show();
            this.Hide();

        }

        private void button26_Click(object sender, EventArgs e)
        {
            if (myParent.myWlan.get_Signal_int() < 2)
            {
                MessageBox.Show("Trwa łączenie. Spróbuj ponownie...");
                return;
            }
            Info_Miejsce nowy = new Info_Miejsce(this);
            nowy.Show();
            this.Hide();
        }

        private void button39_Click(object sender, EventArgs e)
        {
            //if (myParent.myWlan.get_Signal_int() < 2)
            //{
            //    MessageBox.Show("Trwa łączenie. Spróbuj ponownie...");
            //    return;
            //}
            //Delivery_Wymiary nowy = new Delivery_Wymiary(this);
            //nowy.Show();
            //this.Hide();

            if (myParent.myWlan.get_Signal_int() < 2)
            {
                MessageBox.Show("Trwa łączenie. Spróbuj ponownie...");
                return;
            }
            EtykietyLaczenie nowy = new EtykietyLaczenie(this);
            nowy.Show();
            this.Hide();
        }

        private void button40_Click(object sender, EventArgs e)
        {
            
                 if (myParent.myWlan.get_Signal_int() < 2)
            {
                MessageBox.Show("Trwa łączenie. Spróbuj ponownie...");
                return;
            }
                 Sofidel_wydruk nowy = new Sofidel_wydruk(this);
            nowy.Show();
            this.Hide();
        }

        private void button31_Click_1(object sender, EventArgs e)
        {
            if (myParent.myWlan.get_Signal_int() < 2)
            {
                MessageBox.Show("Trwa łączenie. Spróbuj ponownie...");
                return;
            }
            GS1_Test nowy = new GS1_Test(this);
            nowy.Show();
            this.Hide();
        }

        private void button41_Click(object sender, EventArgs e)
        {
            if (myParent.myWlan.get_Signal_int() < 2)
            {
                MessageBox.Show("Trwa łączenie. Spróbuj ponownie...");
                return;
            }
            Delivery_WymiaryNew nowy = new Delivery_WymiaryNew(this);
            nowy.Show();
            this.Hide();
        }

        private void button42_Click(object sender, EventArgs e)
        {
            if (myParent.myWlan.get_Signal_int() < 2)
            {
                MessageBox.Show("Trwa łączenie. Spróbuj ponownie...");
                return;
            }
            SortowanieGNG nowy = new SortowanieGNG(this);
            nowy.Show();
            this.Hide();
        }

        private void button43_Click(object sender, EventArgs e)
        {
            if (myParent.myWlan.get_Signal_int() < 2)
            {
                MessageBox.Show("Trwa łączenie. Spróbuj ponownie...");
                return;
            }
            KurierDrukowanie nowy = new KurierDrukowanie(this);
            nowy.Show();
            this.Hide();
        }

        private void button25_Click(object sender, EventArgs e)
        {
            if (myParent.myWlan.get_Signal_int() < 2)
            {
                MessageBox.Show("Trwa łączenie. Spróbuj ponownie...");
                return;
            }
            WydrukMiejsce_Form nowy = new WydrukMiejsce_Form(this);
            nowy.Show();
            this.Hide();
        }

        private void button44_Click(object sender, EventArgs e)
        {
            if (myParent.myWlan.get_Signal_int() < 2)
            {
                MessageBox.Show("Trwa łączenie. Spróbuj ponownie...");
                return;
            }
            KontrolaPaletNew nowy = new KontrolaPaletNew(this);
            nowy.Show();
            this.Hide();
        }

        private void button45_Click(object sender, EventArgs e)
        {
            if (myParent.myWlan.get_Signal_int() < 2)
            {
                MessageBox.Show("Trwa łączenie. Spróbuj ponownie...");
                return;
            }
            Delivery_kontrola_new tt = new Delivery_kontrola_new(this);
            tt.Show();
            this.Hide();
        }






    }
}