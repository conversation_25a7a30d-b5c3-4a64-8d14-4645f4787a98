# Dokumentacja funkcjonalności przesunięć magazynowych (WMS) - Część 1

## 1. STRUKTURA BAZY DANYCH

### Tabele używane w systemie

| Tabela | Opis |
|--------|------|
| etykiety | Główna tabela przechowująca informacje o etykietach produktów |
| miejsca | Tabela zawierająca informacje o miejscach magazynowych |
| miejsca_wolne | Tabela z wolnymi miejscami magazynowymi |
| miejsca_grupy | Tabela łącząca grupy produktów z miejscami |
| docnumber | Tabela z numeracją dokumentów |
| kody | Tabela kodów produktów |
| kody_grupy | Tabela grup kodów produktów |
| zmiany_miejsca_niezrealizowane | Tabela z niezrealizowanymi zmianami miejsc |
| historia_skanowania | Logi skanowania |
| docout | Dokumenty wydań |
| docin | Dokumenty przyjęć |
| delivery | Tabela dostaw |
| status_system | Tabela statusów w systemie |
| dlcollect | Kolekcja dostawy/wydania |

### Kluczowe kolumny i ich typy danych

#### Tabela `etykiety`
- `id` - VARCHAR/INT, identyfikator etykiety
- `etykieta_klient` - VARCHAR, identyfikator etykiety klienta
- `paleta_id` - VARCHAR, identyfikator palety
- `miejscep` - INT, identyfikator miejsca (klucz obcy do tabeli miejsca)
- `kod_id` - INT, identyfikator kodu (klucz obcy do tabeli kody)
- `docin_id` - VARCHAR, identyfikator dokumentu przyjęcia
- `docout_id` - VARCHAR, identyfikator dokumentu wydania
- `delivery_id` - VARCHAR, identyfikator dostawy
- `system_id` - VARCHAR, identyfikator systemu
- `status_id` - INT, identyfikator statusu
- `active` - TINYINT, flaga aktywności (1 = aktywna, NULL = aktywna, 0 = nieaktywna)

#### Tabela `miejsca`
- `id` - INT, identyfikator miejsca
- `hala` - VARCHAR, kod hali
- `regal` - VARCHAR, numer regału
- `miejsce` - VARCHAR, numer miejsca
- `poziom` - VARCHAR, poziom regału
- `widoczne` - TINYINT, flaga widoczności (1 = widoczne, 0 = niewidoczne)
- `zbiorka` - TINYINT, flaga czy miejsce zbiórki (1 = zbiorka)

#### Tabela `kody_grupy`
- `id` - INT, identyfikator grupy
- `nazwa` - VARCHAR, nazwa grupy
- `zakres_opis` - VARCHAR, opis zakresu
- `system_id` - VARCHAR, identyfikator systemu

### Relacje między tabelami

- **etykiety.miejscep** -> **miejsca.id** (relacja jeden do wielu, etykieta przypisana do miejsca)
- **etykiety.kod_id** -> **kody.id** (relacja jeden do wielu, etykieta ma określony kod produktu)
- **etykiety.status_id** -> **status_system.id** (relacja jeden do wielu, etykieta ma określony status)
- **etykiety.delivery_id** -> **delivery.id** (relacja jeden do wielu, etykieta powiązana z dostawą)
- **kody.kody_grupy_id** -> **kody_grupy.id** (relacja jeden do wielu, kod produktu należy do grupy)
- **miejsca_grupy.miejsce_id** -> **miejsca.id** (relacja wiele do wielu między miejscami a grupami)
- **miejsca_grupy.kody_grupy_id** -> **kody_grupy.id** (relacja wiele do wielu między miejscami a grupami)
- **miejsca_wolne.miejsce_id** -> **miejsca.id** (relacja jeden do jednego, wolne miejsce)
