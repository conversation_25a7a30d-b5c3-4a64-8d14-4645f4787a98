﻿using System;
using System.Linq;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using System.Threading;
using System.Text.RegularExpressions;
using System.Reflection;

namespace Tarczyn__Magazyn
{
    public partial class SzukanieKodu : Form
    {

        IGlobal parent = null;

        static string miejsce="";
        static string poziom="";
        static string ostatnia_paleta = "";


        string NR_DOK = "";

        string operac_id_global = "";


        List<string> _regal = new List<string>();

        static int miejsce_id = 0;

        string kod_id_local = "";

        public SzukanieKodu(IGlobal myParent, string kod_id)
        {
            
            FullScreenMode.OknoOFF(this);
            InitializeComponent();
            parent = myParent;
            textBox1.Focus();
            kod_id_local = kod_id;
            if (kod_id != "")
            {
                pokaz_kody("", kod_id);
            }
            else
            {
                ZacznijSkanowanie();
            }


            //

        }
        
        List<string> Etykiety = new List<string>();
        Thread Skanowanie = null;


        private void ZacznijSkanowanie()
        {

            StringBuilder login = new StringBuilder();
            Skaner.UstawTryb_String(login);
            Skaner.Zacznij_Skanowanie();
            Skanowanie = new Thread(() => CheckString(login));
            Skanowanie.IsBackground = true;
            Skanowanie.Start();
        }
        private void CheckString(StringBuilder cc)
        {
            while (true)
            {
                if (cc.ToString() != "")
                {
                    this.Invoke(new EventHandler(delegate
                    {
                        
                        dodawanie(cc.ToString());
                    }));
                    return;
                }
                Thread.Sleep(200);
            }
        }
        private void Zakoncz_Skanowanie()
        {
            try
            {
                Skanowanie.Abort();
                Skaner.Przewij_Skanowanie();
            }
            catch
            {
            }
        }

        private void Exit_Click_1(object sender, EventArgs e)
        {
            Zakoncz_Skanowanie();
            //parent.ZacznijSkanowanie();
            parent.Show();
            this.Close();
        }


        private void button2_Click(object sender, EventArgs e)
        {
          

        }

        private void pokaz_kody(string wartosc, string kod_id)
        {
            string sql = "";
            if (kod_id != "")
            {
                sql = "SELECT cast(concat(regal,'-',miejsce,'-',poziom) as char) as adres, cast(TRIM(TRAILING '.' FROM TRIM(TRAILING '0' from e.ilosc)) as char) as ilosc,s.nazwa as stat,ifnull(e.lot,'') as lot, k.kod,   k.kod_nazwa, ifnull((SELECT d.delivery_id FROM delivery_et d WHERE d.etykieta_id=e.id limit 1),'') as dl,kg.nazwa as kod_grupa_nazwa FROM etykiety e left join palety p on e.paleta_id=p.id left join kody k on e.kod_id=k.id left join miejsca m on e.miejscep=m.id left join status_system s on e.status_id=s.id left join kody_grupy kg on kg.id=k.kody_grupy_id where e.kod_id=\"" + kod_id + "\"  and k.active=1 and e.active=1 and e.system_id=" + Wlasciwosci.system_id_id + "  GROUP BY e.id Having dl='' order by m.zbiorka desc,e.ilosc asc,hala,regal,miejsce,poziom,e.id asc  ";
                kod_id_local = "";
            }
            else
            {
                sql = "SELECT cast(concat(regal,'-',miejsce,'-',poziom) as char) as adres, cast(TRIM(TRAILING '.' FROM TRIM(TRAILING '0' from e.ilosc)) as char) as ilosc,s.nazwa as stat,ifnull(e.lot,'') as lot, k.kod,   k.kod_nazwa, ifnull((SELECT d.delivery_id FROM delivery_et d WHERE d.etykieta_id=e.id limit 1),'') as dl,kg.nazwa as kod_grupa_nazwa FROM etykiety e left join palety p on e.paleta_id=p.id left join kody k on e.kod_id=k.id left join miejsca m on e.miejscep=m.id left join status_system s on e.status_id=s.id left join kody_grupy kg on kg.id=k.kody_grupy_id where ( TRIM(LEADING '0' FROM ean)=\"" + wartosc.TrimStart('0') + "\"  or TRIM(LEADING '0' FROM ean_opakowanie_zbiorcze)=\"" + wartosc.TrimStart('0') + "\"  or TRIM(LEADING '0' FROM ean_jednostki)=\"" + wartosc.TrimStart('0') + "\" or TRIM(LEADING '0' FROM kod)=\"" + wartosc.TrimStart('0') + "\" or TRIM(LEADING '0' FROM kod)=(SELECT TRIM(LEADING '0' FROM k.kod) FROM etykiety e left join kody k on e.kod_id=k.id where e.paleta_id=\"" + wartosc.Replace("DS", "") + "\" and e.system_id=" + Wlasciwosci.system_id_id + " and e.active=1 limit 1)  ) and k.active=1 and e.active=1 and e.system_id=" + Wlasciwosci.system_id_id + "  GROUP BY e.id Having dl='' order by m.zbiorka desc,e.ilosc asc,hala,regal,miejsce,poziom,e.id asc  ";
            }
            object test1 = BazaDanychExternal.Wyczytaj_Tabele(sql);
            //SELECT cast(concat (concat(count(1),'---',i.typ_cd_marchio),'__', (SELECT group_concat(ee.etykieta_klient) FROM etykiety ee left join importRICIdomi ii on ee.etykieta_klient=ii.CD_CONTENITORE where ee.paleta_id=e.paleta_id and ii.typ_cd_marchio=i.typ_cd_marchio GROUP BY i.typ_cd_marchio)) as char) as aa FROM etykiety e left join importRICIdomi i on e.etykieta_klient=i.CD_CONTENITORE where e.paleta_id='" + gg.Replace("DS", "") + " GROUP BY i.typ_cd_marchio  ");
            //MessageBox.Show("2");
            DataTable Wynik = (DataTable)test1;
            if (test1 == null)
            {

                sql = "SELECT  k.kod,k.kod_nazwa,kg.nazwa as kod_grupa_nazwa from kody k  left join kody_grupy kg on kg.id=k.kody_grupy_id where  ( TRIM(LEADING '0' FROM ean)=\"" + wartosc.TrimStart('0') + "\"  or TRIM(LEADING '0' FROM ean_opakowanie_zbiorcze)=\"" + wartosc.TrimStart('0') + "\"  or TRIM(LEADING '0' FROM ean_jednostki)=\"" + wartosc.TrimStart('0') + "\" or TRIM(LEADING '0' FROM kod)=\"" + wartosc.TrimStart('0') + "\" or TRIM(LEADING '0' FROM kod)=(SELECT TRIM(LEADING '0' FROM k.kod) FROM etykiety e left join kody k on e.kod_id=k.id where e.paleta_id=\"" + wartosc.Replace("DS", "") + "\" and e.system_id=" + Wlasciwosci.system_id_id + " and e.active=1 limit 1)  ) and k.active=1 and k.system_id=" + Wlasciwosci.system_id_id + "  GROUP BY k.id order by k.id desc ";
            object test2 = BazaDanychExternal.Wyczytaj_Tabele(sql);
            DataTable Wynik2 = (DataTable)test2;
            if (Wynik.Rows.Count > 0)
            {
                label2.Text = "Brak w bazie" + Environment.NewLine + Wynik2.Rows[0]["kod_grupa_nazwa"].ToString() + " - " + Wynik2.Rows[0]["kod"].ToString() + Environment.NewLine + Wynik2.Rows[0]["kod_nazwa"];
            }

                //label2.Text = "Brak w bazie";

                ZacznijSkanowanie();


                return;
            }
            else if (Wynik.Rows.Count < 1)
            {

                sql = "SELECT  k.kod,k.kod_nazwa,kg.nazwa as kod_grupa_nazwa from kody k  left join kody_grupy kg on kg.id=k.kody_grupy_id where  ( TRIM(LEADING '0' FROM ean)=\"" + wartosc.TrimStart('0') + "\"  or TRIM(LEADING '0' FROM ean_opakowanie_zbiorcze)=\"" + wartosc.TrimStart('0') + "\"  or TRIM(LEADING '0' FROM ean_jednostki)=\"" + wartosc.TrimStart('0') + "\" or TRIM(LEADING '0' FROM kod)=\"" + wartosc.TrimStart('0') + "\" or TRIM(LEADING '0' FROM kod)=(SELECT TRIM(LEADING '0' FROM k.kod) FROM etykiety e left join kody k on e.kod_id=k.id where e.paleta_id=\"" + wartosc.Replace("DS", "") + "\" and e.system_id=" + Wlasciwosci.system_id_id + " and e.active=1 limit 1)  ) and k.active=1 and k.system_id=" + Wlasciwosci.system_id_id + "  GROUP BY k.id order by k.id desc ";
                object test2 = BazaDanychExternal.Wyczytaj_Tabele(sql);
                DataTable Wynik2 = (DataTable)test2;
                if (Wynik.Rows.Count > 0)
                {
                    label2.Text = "Brak w bazie" + Environment.NewLine + Wynik2.Rows[0]["kod_grupa_nazwa"].ToString() + " - " + Wynik2.Rows[0]["kod"].ToString() + Environment.NewLine + Wynik2.Rows[0]["kod_nazwa"];
                }

                dataGrid1.DataSource = null;
                dataGrid1.Refresh();

                ZacznijSkanowanie();
                return;
            }
            else
            {
                //MessageBox.Show("5");


                
                //MessageBox.Show("6");

                label2.Text =  Wynik.Rows[0]["kod_grupa_nazwa"].ToString() +Environment.NewLine +"" + Wynik.Rows[0]["kod"].ToString() + Environment.NewLine + Wynik.Rows[0]["kod_nazwa"];


                dataGrid1.DataSource = Wynik;
                dataGrid1.TableStyles.Clear();
                DataGridTableStyle tableStyle = new DataGridTableStyle();
                tableStyle.MappingName = Wynik.TableName;


                DataGridTextBoxColumn tbcName0 = new DataGridTextBoxColumn();
                tbcName0.Width = 80;
                tbcName0.MappingName = Wynik.Columns[0].ColumnName;
                tbcName0.HeaderText = Wynik.Columns[0].ColumnName;
                tableStyle.GridColumnStyles.Add(tbcName0);


                DataGridTextBoxColumn tbcName1 = new DataGridTextBoxColumn();
                tbcName1.Width = 45;
                tbcName1.MappingName = Wynik.Columns[1].ColumnName;
                tbcName1.HeaderText = Wynik.Columns[1].ColumnName;
                tableStyle.GridColumnStyles.Add(tbcName1);

                DataGridTextBoxColumn tbcName2 = new DataGridTextBoxColumn();
                tbcName2.Width = 30;
                tbcName2.MappingName = Wynik.Columns[2].ColumnName;
                tbcName2.HeaderText = Wynik.Columns[2].ColumnName;
                tableStyle.GridColumnStyles.Add(tbcName2);

                DataGridTextBoxColumn tbcName3 = new DataGridTextBoxColumn();
                tbcName3.Width = 70;
                tbcName3.MappingName = Wynik.Columns[3].ColumnName;
                tbcName3.HeaderText = Wynik.Columns[3].ColumnName;
                tableStyle.GridColumnStyles.Add(tbcName3);

                DataGridTextBoxColumn tbcName4 = new DataGridTextBoxColumn();
                tbcName4.Width = 40;
                tbcName4.MappingName = Wynik.Columns[6].ColumnName;
                tbcName4.HeaderText = Wynik.Columns[6].ColumnName;
                tableStyle.GridColumnStyles.Add(tbcName4); 






                dataGrid1.TableStyles.Add(tableStyle);
                ZacznijSkanowanie();

                /*
                for (int t = 0; t < Wynik.Rows.Count; t++)
                {
                    string.Format(@"" + aa, Environment.NewLine);
                }
                
                 */
                //ZacznijSkanowanie();
                return;


            }


        }


        private void dodawanie(string wartosc)
        {



            if (parent.Get_Signal_Int() < 2)
            {
                MessageBox.Show("Trwa łączenie. Spróbuj ponownie...");
                return;
            }

            //if (parent.myParent.myWlan.get_Signal_int() < 2)
            //{
            //    MessageBox.Show("Trwa łączenie. Spróbuj ponownie...");
            //    return;
            //}

            if (BazaDanychExternal.SprawdzCzyIstniejePolaczenie() == false)
            {
                MessageBox.Show("Brak Wifi. Poczekaj chwilę na ponowne połączenie.");
                return;
            }


            Zakoncz_Skanowanie();

            pokaz_kody(wartosc,"");



            //ZacznijSkanowanie();
        }

        private void WczytywanieNaLinie_Load_1(object sender, EventArgs e)
        {
            //ZacznijSkanowanie();
        }

        private void button1_Click(object sender, EventArgs e)
        {
            if (textBox1.Text != "")
            {
                dodawanie(textBox1.Text);
                textBox1.Text = "";
            }

            
        }

        private void button2_Click_2(object sender, EventArgs e)
        {
            if (ostatnia_paleta.Trim() == "" || ostatnia_paleta == null)
            {
                MessageBox.Show("Brak palet!");
                return;
            }

            BazaDanychExternal.DokonajUpdate("update zlecenia_palety z set z.active1=1 WHERE z.paleta_id=" + ostatnia_paleta.Replace("DS", ""));
            BazaDanychExternal.DokonajUpdate("update zlecenia_palety z set z.active1=0 WHERE z.paleta_id="+ostatnia_paleta.Replace("DS", ""));
        }
    }
}
