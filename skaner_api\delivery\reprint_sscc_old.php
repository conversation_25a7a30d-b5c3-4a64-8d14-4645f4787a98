<?php

include_once '../Db.class.php';
include_once '../funkcje.inc';

// show errors  
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

$db = new Db();

$baza_danych = "wmsgg";
$komunikat = "OK";

$response = array();

// Pobierz dane z $_GET
$sscc = $_GET['sscc']; // wymagany
$system_id = $_GET['system_id'];  
$adres_ip = $_GET['adres_ip'];


$tmp_arr1 = sprawdz_szukana_etykiete_WMS($sscc, $system_id, $komunikat, $baza_danych, $db);




if(empty($tmp_arr1['aRowEtWms'])) {
    $komunikat = "Nie znaleziono aktywnej etykietyty $sscc !!!! ";
    return show_komunikat_xml($komunikat);
}

if(empty($adres_ip)) {
    $komunikat = " Nie podano adresu IP drukarki !!!";
    return show_komunikat_xml($komunikat);
}


$aRowEtWms = $tmp_arr1['aRowEtWms'];    
// echo "<pre>";
// print_r($aRowEtWms);
// echo "</pre>";
reprint_label($aRowEtWms[0],$adres_ip)  ;  
// dodaj opóżnienie sekundowe i wydrukuj ponownie

sleep(2);
reprint_label($aRowEtWms[0],$adres_ip)  ;



return show_komunikat_xml($komunikat);

//exit();

function reprint_label($dane,$adres_ip)
{
    $zpl_layout = "
^XA
^PW1012
^LL1200

^FO100,50^A0N,30,30^FDZleceniodawca:^FS
^FO100,90^A0N,30,30^FD" . zamianapolskich($dane['zlec_logo']) . "^FS
^FO100,130^A0N,30,30^FD " .  zamianapolskich( $dane['zlec_ulica'] . " ". $dane['zlec_lokal'] ).  "^FS ^FS
^FO100,170^A0N,30,30^FD " .  zamianapolskich($dane['zlec_kod'] . " ". $dane['zlec_miasto'] ).  "^FS

^FO400,50^A0N,30,30^FDNr zam. Zlecen.: " . zamianapolskich( $dane['docout_ref']) . "^FS
^FO400,90^A0N,30,30^FDNr zam. odb.: " . zamianapolskich( $dane['docout_ref_klient']) . "^FS

^FO100,250^A0N,30,30^FD Odbiorca:^FS
^FO100,290^A0N,30,30^FD" . zamianapolskich( $dane['koutlogo'] ). "^FS
^FO100,330^A0N,30,30^FD" . zamianapolskich( $dane['koutulica'] ). "^FS
^FO100,370^A0N,30,30^FD" . zamianapolskich( $dane['koutmiasto']) . "^FS
^FO100,410^A0N,30,30^FD" .  zamianapolskich($dane['kod_nazwa'] ). "^FS
^FO600,370^A0N,30,30^FD " .  $dane['ilosc_w_opakowaniu'] . "x" .  $dane['ilosc_opak'] . "=" .  $dane['ilosc_opak']*$dane['ilosc_w_opakowaniu'] . " ^FS
^FO600,410^A0N,30,30^FDSzt. x jedn.^FS

^FO100,490^GB700,3,3^FS

^FO100,520^A0N,30,30^FDCONTENT:^FS
^FO250,520^A0N,30,30^FD".$dane['ean']."^FS
^FO100,560^A0N,30,30^FDBATCH/LOT:^FS
^FO250,560^A0N,30,30^FD".$dane['lot']."^FS
^FO100,600^A0N,30,30^FDCOUNT:^FS
^FO250,600^A0N,30,30^FD".$dane['ilosc_opak']."^FS
^FO100,640^A0N,30,30^FDBEST BEFORE:^FS
^FO300,640^A0N,30,30^FD".date("Y.m.d", strtotime($dane['data_waznosci']))."^FS
^FO450,640^A0N,30,30^FD(YYYY.MM.DD)^FS

^FO100,700^A0N,30,30^FDSSCC:^FS
^FO200,700^A0N,30,30^FD".$dane['etykieta_klient']."^FS

^FO100,750^GB700,3,3^FS

^FO100,800^BY3^BCN,120,Y,N,N^FD>:02".str_pad($dane['ean'], 14, '0', STR_PAD_LEFT).">:37".$dane['ilosc_opak']."^FS
^FO100,1000^BY3^BCN,120,Y,N,N^FD>:15".date("ymd", strtotime($dane['data_waznosci'])).">:10".$dane['lot']."^FS
^FO100,1180^BY3^BCN,120,Y,N,N^FD>:00".$dane['etykieta_klient']."^FS

^XZ


";


//echo $zpl_layout;

$file = fopen("/tmp/reprint_sscc.zbr", "w");
        fputs($file, $zpl_layout);
        fclose($file);
        //$adres_ip = "**********";
        //flush();
        //sleep(1);

        $socket = socket_create(AF_INET, SOCK_STREAM, SOL_TCP);
        $result = socket_connect($socket, $adres_ip, "9100");
        socket_write($socket, $zpl_layout, strlen($zpl_layout));
        socket_close($socket);


}


function sprawdz_szukana_etykiete_WMS($etykieta_scan, $system_id, $komunikat, $baza_danych, $db)
{

        // if (substr($etykieta_scan, 0, 2) == "DS") {
        //         $sql = 'select e.id,e.etykieta_klient,k.kod,k.ilosc_w_opakowaniu,k.kod_nazwa,e.lot,e.active,TRIM(TRAILING "." FROM TRIM(TRAILING "0" from e.ilosc)) as ilosc,de.delivery_id,e.paleta_id,ss.nazwa as status_system_nazwa, ss.funkcja_stat , TRIM(TRAILING "." FROM TRIM(TRAILING "0" from de.ilosc_zamawiana))  as ilosc_zamawiana, d.dl_docin_id_wew, d.dl_docout_id_wew, d.miejsce_kompletacji,e.miejscep from ' . $baza_danych . '.etykiety e left join ' . $baza_danych . '.kody k on e.kod_id=k.id left join ' . $baza_danych . '.dlcollect dl ' . ' on dl.nr_et=e.id left join ' . $baza_danych . '.status_system ss  on e.status_id=ss.id left join ' . $baza_danych . '.delivery_et de ' . ' on de.etykieta_id=e.id left join ' . $baza_danych . '.delivery d ' . ' on de.delivery_id=d.id  where e.active=1  and e.paleta_id=' . str_replace("DS", "", $etykieta_scan) . ' and e.system_id=' . $system_id . '  order by nr_dl asc,e.ilosc asc limit 1'; //having nr_dl is null
        // } else {
        //         $sql = 'select e.id,e.etykieta_klient,k.kod,k.ilosc_w_opakowaniu,k.kod_nazwa,e.lot,e.active,TRIM(TRAILING "." FROM TRIM(TRAILING "0" from e.ilosc)) as ilosc,de.delivery_id,e.paleta_id,ss.nazwa as status_system_nazwa, ss.funkcja_stat , TRIM(TRAILING "." FROM TRIM(TRAILING "0" from de.ilosc_zamawiana))  as ilosc_zamawiana , d.dl_docin_id_wew, d.dl_docout_id_wew,d.miejsce_kompletacji,e.miejscep from ' . $baza_danych . '.etykiety e left join ' . $baza_danych . '.kody k on e.kod_id=k.id left join ' . $baza_danych . '.dlcollect dl ' . ' on dl.nr_et=e.id left join ' . $baza_danych . '.status_system ss  on e.status_id=ss.id left join ' . $baza_danych . '.delivery_et de ' . ' on de.etykieta_id=e.id left join ' . $baza_danych . '.delivery d ' . ' on de.delivery_id=d.id  where e.active=1  and  (e.id="' . $etykieta_scan . '" or e.etykieta_klient="' . $etykieta_scan . '")  and e.system_id=' . $system_id . '  order by nr_dl asc,e.ilosc asc limit 1'; //having nr_dl is null
        // }

        if (substr($etykieta_scan, 0, 2) == "DS") {
            $warunek = ' e.system_id = ' . $system_id . ' and e.paleta_id="' . substr($etykieta_scan, 0, 2) == "DS" . '"';
        } else {
            $warunek = ' e.system_id = ' . $system_id . ' and (e.id="' . $etykieta_scan . '" or e.etykieta_klient="' . $etykieta_scan . '")';
        }


        $sql = "SELECT 
    e.id,
    e.system_id,
    e.etykieta_klient,
    e.magazyn,
    e.active,
    e.miejscep,
    e.przeznaczenie_id,
    e.kod_id,
    e.paleta_id,
    e.dataprod,
    e.data_waznosci,
    e.status,
    e.blloc,
    e.akcja_id AS akcjanr_id,
    e.status_prism,
    e.stat,
    e.status_id,
    st.nazwa AS status_nazwa,
    st.funkcja_stat,
    e.kartony,
    ed3.kolor,
    ed3.plec,
    ed3.rozmiar_nr,
    ed3.uszkodzenie,
    e.lot,
    e.sscc,
    e.gtin,
    e.edycja_et,
    e.ilosc,
    e.ts,
    e.nretykiety,
    e.docin_id,
    e.docout_id,
    e.delivery_id,
    e.listcontrol_id,
    CONCAT('Hala ', m.hala, ' ', m.regal, '-', m.miejsce, '-', m.poziom) AS adres,
    CONCAT(m.regal, '-', m.miejsce, '-', m.poziom) AS adres2,
    k.kod_nazwa,
    k.jm,
    k.kod,
    k.kod2,
    TRIM(TRAILING '.' FROM TRIM(TRAILING '0' FROM (IF(e.ilosc IS NULL, 1, e.ilosc) / IF(k.ilosc_w_opakowaniu=0, 1, ilosc_w_opakowaniu)))) AS ilosc_opak,
    k.opakowanie_jm,
    k.ean,
    k.ean_jednostki,
    k.ilosc_w_opakowaniu,
    js.nazwa AS j_skladowania_nazwa,
    CONCAT(p.ilosc, ' ', tp.opis) AS paletanazwa,
    p.pal_klient,
    tp.kod AS tpkod,
    kin.logo AS kinlogo,
    dout.kontrah_id AS kontrah_id_docout,
    dout.docout_type,
    dout.docout_nr,
    dout.docout_date,
    dout.docout_ts,
    dd.dl_doc_ref as docout_ref,
    dd.dl_doc_ref_klient as docout_ref_klient,
    dout.docout_uwagi,
    dout.pracownik_id_kier,
    dout.docout_internal,
    dout.docout_date_req,
    ROUND(SUM(e.ilosc) * k.waga_szt_kg, 1) AS waga_suma,
    s.skrot,
    m.hala,
    din.doc_nr,
    din.doc_date,
    din.doc_ts,
    din.doc_internal,
    din.doc_type,
    din.kontrah_id AS kontrah_id_docin,
    din.doc_ref,
    din.doc_uwagi,
    din.dostawa_typ,
    din1.nr_doc_dost,
    din1.data_wystawienia,
    din1.nr_zam_mpg,
    din1.numeroavviso,
    kout.logo AS koutlogo,
    CONCAT(kout.ulica, ' ', kout.lokal) AS koutulica,
    CONCAT(kout.kod, ' ', kout.miasto) AS koutmiasto,
    a.nazwa AS akcja_nazwa,
    kodg.nazwa AS kody_grupa_nazwa,
    kwew.logo as zlec_logo,
    kwew.firma as zlec_firma,
    kwew.miasto as zlec_miasto,
    kwew.ulica as zlec_ulica,
    kwew.lokal as zlec_lokal,
    kwew.kod as zlec_kod,
    kwew.gln_kontrah as zlec_gln_kontrah
FROM 
    etykiety e
left JOIN 
    status_system st ON e.status_id = st.id
left JOIN 
    etykiety_dod3 ed3 ON e.id = ed3.id AND e.system_id = ed3.system_id
left JOIN 
    miejsca m ON e.miejscep = m.id
left JOIN 
    kody k ON k.id = e.kod_id
left JOIN 
    palety p ON e.paleta_id = p.id
left JOIN 
    typypalet tp ON p.typypalet_id = tp.id
left JOIN 
    jednostka_skladowania js ON p.j_skladowania_id = js.id
left JOIN 
    docin din ON e.docin_id = din.id
left JOIN 
    docin_dod1 din1 ON din.id = din1.docin_id
left JOIN 
    docout dout ON e.docout_id = dout.id
left JOIN 
    delivery dl ON e.delivery_id = dl.id
left JOIN 
    delivery_et de ON de.etykieta_id=e.id
    left JOIN 
    delivery dd ON de.delivery_id=dd.id
left JOIN 
    kontrah kin ON din.kontrah_id = kin.id
left JOIN 
    kontrah kout ON dd.dl_kontrah_id = kout.id
left JOIN 
    pracownicy pdocin ON din.pracownik_id = pdocin.id
left JOIN 
    pracownicy pdout ON dout.pracownik_id = pdout.id
left JOIN 
    systemy s ON e.system_id = s.wartosc
left JOIN 
    kontrah kwew ON s.kontrah_wew_id=kwew.id
left JOIN 
    akcja a ON a.id = e.akcja_id
left JOIN 
    kody_grupy kodg ON k.kody_grupy_id = kodg.id
WHERE 


  $warunek  
  
  order by de.delivery_id  asc,e.ilosc asc limit 1

;
";
        //echo $sql;
        $result2 = $db->mGetResultAsXML($sql);
        $aRowEtWms = array();

        // //    if (count($result2) > 1) {
        // //        return array('komunikat' => $komunikat, 'aRowEtWms' => $result2, 'ilosc_pozycji_etykiety' => count($result2));
        // //    }
        // //echo "<br>" . $sql;
        // foreach ($result2 as $index => $aRowEtWms) {
        //         if ($aRowEtWms['active'] != "1") {
        //                 $komunikat = "Etykieta jest nieaktywna. Przerywam operacje";
        //                 //echo "<br>" . $komunikat;
        //                 return show_komunikat_xml($komunikat);
        //         }

        //         if (!empty($aRowEtWms['funkcja_stat'])) {
        //                 $komunikat = "Etykieta jest w statusie: " . $aRowEtWms['status_system_nazwa'] . ". Przerywam operacje";
        //                 //echo "<br>" . $komunikat;
        //                 return show_komunikat_xml($komunikat);
        //         }

        //         // jeśli ilosc_zamawiana jest rózna od ilosc
        //         if ($aRowEtWms['ilosc_zamawiana'] != $aRowEtWms['ilosc']) {
        //                 $komunikat = "Etykieta zamawiana " . $aRowEtWms['ilosc_zamawiana'] . " jest różna od ilości etykiety: " . $aRowEtWms['ilosc'] . ". Przerywam operacje";
        //                 //echo "<br>" . $komunikat;
        //                 return show_komunikat_xml($komunikat);
        //         }
        // }
        return array('komunikat' => $komunikat, 'aRowEtWms' => $result2);
}




function template_biedronka_vershold($dane, $html)
    {

        if (!empty($dane['data_waznosci'])) {
            $data_waznosci_bar = "15" . date("ymd", strtotime($dane['data_waznosci']));
            $data_waznosci_html = "(15)" . date("ymd", strtotime($dane['data_waznosci']));
            $data_waznosci = date("Y.m.d", strtotime($dane['data_waznosci']));
        } else {
            $data_waznosci_bar = "";
            $data_waznosci_html = "";
            $data_waznosci = "";
        }
        if (empty($dane['ilosc_w_opakowaniu'])) {
            $dane['ilosc_w_opakowaniu'] = 1;
        }

        $ilosc_opak = $dane['ilosc'] / $dane['ilosc_w_opakowaniu'];
        $ilosc_opak = $dane['ilosc_opak'];
        $ilosc_opak_bar = "37" . $ilosc_opak;
        $ilosc_opak_html = "(37)" . $ilosc_opak;
        //$ilosc_opak = 3;
        //$dane['ean'] = "5609288138990";

        if (!empty($dane['dataprod'])) {
            $dataprod_bar = "11" . date("ymd", strtotime($dane['dataprod']));
            $dataprod_html = "(11)" . date("ymd", strtotime($dane['dataprod']));
            $dataprod_srodkowa = "" . date("d/m/Y", strtotime($dane['dataprod'])) . "          (DD/MM/YYYY)";
        } else {
            $dataprod_bar = "";
            $dataprod_html = "";
            $dataprod_srodkowa = "";
        }

        if (!empty($dane['waga_suma'])) {
            $dane['waga_suma'] = $dane['waga_suma'] + 22;
            $dane['waga_suma'] = number_format(round((float) $dane['waga_suma'], 1), 1);
            $waga_bar = "3301" . str_pad(str_replace('.', '', $dane['waga_suma']), 6, '0', STR_PAD_LEFT);
            $waga_html = "(3301)" . str_pad(str_replace('.', '', $dane['waga_suma']), 6, '0', STR_PAD_LEFT);
            //$waga_bar = "33010000130" ;
            //$waga_html = "(3301)000130";
            $waga_srodkowa = "" . $dane['waga_suma'] . " KG";
        } else {
            $waga_bar = "";
            $waga_html = "";
            $waga_srodkowa = "";
        }
        if (empty($dane['status_prism'])) {
            $dane['status_prism'] = $dane['docout_ref'];
        }

        $nr_zam_bar = chr(29) . "400" . $dane['status_prism'];
        $nr_zam_gora = $dane['status_prism'];
        $nr_zam_html = "(400)" . $dane['status_prism'];

        $html .= '<head><style type="text/css">     
        div { font-size: 13px; }</style></head>';

        $html .= '';
        $html .= '<center>Zleceniodawca:<br> ' . $dane['zlec_logo'] . ' &nbsp;&nbsp;&nbsp;&nbsp; &nbsp;&nbsp;&nbsp;&nbsp; <br> ' . $dane['zlec_ulica'] . ' ' . $dane['zlec_lokal'] . '<BR> ' . $dane['zlec_kod'] . ' ' . $dane['zlec_miasto'] . ' &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;   ' . $nr_zam_gora . "</center>"; //$dane['docout_ref_klient']
        //$html .= '<br><br><u>Odbiorca:</u><br> '.$dane['koutlogo'].'<br> '.$dane['koutulica'].'  <br> '.$dane['koutmiasto'].'           ';
        //$html .= '<br><br><u>Odbiorca:</u><br> JERONIMO MARTINS POLSKA S.A.<br> UL. ŻNIWNA 5  <br> 62-025 KOSTRZYN           ';
        //        $html .= '<div id="kod" style="font-size: 35px; text-align: center; ">' . $dane['kod'] . '</div>';
        //        $html .= '<br><div id="kod_nazwa" style="font-size: 18px; text-align: center;"> ' . $dane['kod_nazwa'] . "</div>";
        $html .= '';
        //$html .= '<br>';
        //        $html .= '<br><div id="ilosc" >Finished product code: </div>';
        //        $html .= '<br><div id="ilosc" style="font-size: 22px;" >' . $dane['kod'] . " </div>";
        $html .= '<br><span id="magazyn" style=""><br> ';
        $html .= '<span id="magazyn" style="font-size: 18px;">' . $dane['kod_nazwa'] . "</span>";

        $html .= '<hr>';
        $html .= '<span  >SSCC:  </span>';
        $html .= '<span style="font-size: 14px; font-weight:bold;  "> &nbsp;' . $dane['etykieta_klient'] . "</span>";
        $html .= '<br><span >Zawartość/GTIN: &nbsp; </span>';
        $html .= '<span style="font-size: 14px; "> &nbsp;' . $dane['ean'] . '  </span>';

        $html .= '<br><span  >SERIA/BATCH:  </span>';
        $html .= '<span style="font-size: 14px; font-weight:bold;  "> &nbsp;' . $dane['lot'] . "</span>";

        $html .= '<br><span  >Kartonów:  </span>';
        $html .= '<span style="font-size: 14px; font-weight:bold;  "> &nbsp;' . $ilosc_opak . "</span>";

        $html .= '<br><span  >Szt w Kartonie:  </span>';
        $html .= '<span style="font-size: 14px; font-weight:bold;  "> &nbsp;' . $dane['ilosc_w_opakowaniu'] . "</span>";

        //$html .= '<span  style="font-size: 14px;>Best before (yyyy.mm.dd):  ' . $data_waznosci . '</span>';
        //$html .= '<div style="font-size: 14px; "> &nbsp; </div>'; //' . date("d.m.Y", strtotime($dane['data_waznosci'])) . '
        //        $html .= '<br><span >PROD. DATE:  </span>';
        //        $html .= '<span style="font-size: 14px; "> &nbsp;' . $dataprod_srodkowa . '    </span>'; //' . date("d.m.Y", strtotime($dane['data_waznosci'])) . '

        $html .= '<br><span >PRODUCT CODE:  </span>';
        $html .= '<span style="font-size: 14px; "> <b>&nbsp;' . $dane['kod'] . '  <b>  </span>';

        //$html .= '<div style="text-align:right; margin-top: -140px;">Count:<br> <span style="font-size: 14px;">' . $ilosc_opak . ' </span></div>';
        //$html .= '<div style="text-align:right; margin-top: 0px; margin-bottom: 40px;">Batch:<br> <span style="font-size: 14px;"> &nbsp;' . $dane['lot'] . "</span> </div>";
        //$html .= '<br>';
        //        $html .= '<br><div style=" margin-top: 20px;">Purchase from/ Kupiono od: &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; Nr zam:</div>';
        //        $html .= '<br><div style="font-size: 27px; ">' . $dane['gln'] . '  &nbsp;&nbsp; &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;' . $dane['doc_ref'] . '  </div>';


        $html .= '<hr>';

        if (!empty($dane['gtin'])) {
            $gtin_bar = "02" . $dane['gtin'];
            $gtin_html = "(02)" . $dane['gtin'];
        } else {

            if (!empty($dane['ean'])) {
                $gtin_bar = "02" . str_pad(str_replace('.', '', $dane['ean']), 14, '0', STR_PAD_LEFT);
                $gtin_html = "(02)" . str_pad(str_replace('.', '', $dane['ean']), 14, '0', STR_PAD_LEFT);
            } else {
                $gtin_bar = "";
                $gtin_html = "";
            }
        }


        if (!empty($dane['gln'])) {
            $purchase_bar = "412" . $dane['gln'];
            $purchase_html = "(412)" . $dane['gln'];
        } else {
            $purchase_bar = "";
            $purchase_html = "";
        }
        if (!empty($dane['lot'])) {
            $lot_bar = "10" . $dane['lot'];
            $lot_html = "(10)" . $dane['lot'];
        } else {
            $lot_bar = "10Brak";
            $lot_html = "(10)Brak";
        }



        //        if (!empty($dane['gln'])) {
        //            $gln_bar = "15" . date("ymd", strtotime($dane['data_waznosci']));
        //            $gln_html = "(15)" . date("ymd", strtotime($dane['data_waznosci']));
        //        } else {
        //            $gln_bar = "";
        //            $gln_html = "";
        //        }
        //'.$dane['gln'].'
        //$dane['purchase']
        //. $purchase_bar 
        //
        //C128B  EAN128C //'.$dane['docout_ref_klient'].' // //$ilosc_opak  chr(29).'400762147'.chr(29). 'chr(106)



        $gora_barcode = preg_replace("/[^A-Za-z0-9 ]/", '', $gtin_bar . $data_waznosci_bar . $ilosc_opak_bar);
        $gora_html = $gtin_html . $data_waznosci_html . $ilosc_opak_html;

        $srodek_barcode = preg_replace("/[^A-Za-z0-9 ]/", '', $lot_bar . $nr_zam_bar);
        $srodek_html = $lot_html . $nr_zam_html;

        //"' .$gtin_bar .'37' .$ilosc_opak.  chr(29).'400'. $dane['docout_ref_klient'] .'" type="EAN128A" 
        //
        //        $html .= '<br><br><div align="center"><barcode code="' . $gora_barcode . '" type="EAN128A"  size="0.8" height="2" class="barcode" /> </div>';
        //        $html .= '<div id="gtin" style=" text-align: center;">' . $gora_html . '</div>'; //(02)
        //        //$html = '<div id="gtin" style=" text-align: center;>(412)5600000000014(10)' . $dane['lot']."</div>";
        //        $html .= '<br><div align="center"><barcode code="' . $srodek_barcode . '" type="EAN128A"  size="0.8" height="2" class="barcode" /> </div>';
        //        $html .= '<div id="gtin" style=" text-align: center;">' . $srodek_html . "</div>";
        //C128B  EAN128C //'.$dane['docout_ref_klient'].' // //$ilosc_opak  chr(29).'400762147'.chr(29). 'chr(106)
        //        $html .= '<br><br><div align="center"><barcode code="' . $gtin_bar . '37' . $ilosc_opak . '" type="EAN128A"  size="0.8" height="2" class="barcode" class="barcode"/> </div>';
        //        $html .= '<div id="gtin" style=" text-align: center;">' . "    " . $gtin_html . "(37)" . $ilosc_opak . "</div>"; //(02)
        //$sscc = '<div id="gtin" style=" text-align: center;>(412)5600000000014(10)' . $dane['lot']."</div>";
        //
        //$html .= '<br><div align="center"><barcode code="' . $dataprod_bar . $lot_bar .chr(29)."240".$dane['kod']. '" type="EAN128A"  size="0.8" height="2" class="barcode" class="barcode"/> </div>';
        //$html .= '<div id="gtin" style=" text-align: center;">' . $dataprod_html .  $lot_html ."(240)".$dane['kod']. "</div>";
        //$sscc = "(00)" . $dane['etykieta_klient'] . "";
        $html .= '<br><div align="center"><barcode code="00' . $dane['etykieta_klient'] . '" type="EAN128C"  size="1" height="2" class="barcode" class="barcode"/> </div>';

        //        $html .= '<br><div id="ts" style="font-size: 10px;"> czas ' . $dane['doc_ts'] . "</div>";
        //
        //        $html .= '<br><div align="center"><barcode code="' . $dane['id'] . '" type="C128B"  size="1.5" height="1" class="barcode" class="barcode"/> </div>';
        $html .= '<div id="id_etykiety" style=" text-align: center;"> (00)' . $dane['etykieta_klient'] . '</div>';

        return $html;
    }


    function zamianapolskich($tekst)
    {
        $tabela = array(
            //WIN
            "\xb9" => "a",
            "\xa5" => "A",
            "\xe6" => "c",
            "\xc6" => "C",
            "\xea" => "e",
            "\xca" => "E",
            "\xb3" => "l",
            "\xa3" => "L",
            "\xf3" => "o",
            "\xd3" => "O",
            "\x9c" => "s",
            "\x8c" => "S",
            "\x9f" => "z",
            "\xaf" => "Z",
            "\xbf" => "z",
            "\xac" => "Z",
            "\xf1" => "n",
            "\xd1" => "N",
            //UTF
            "\xc4\x85" => "a",
            "\xc4\x84" => "A",
            "\xc4\x87" => "c",
            "\xc4\x86" => "C",
            "\xc4\x99" => "e",
            "\xc4\x98" => "E",
            "\xc5\x82" => "l",
            "\xc5\x81" => "L",
            "\xc3\xb3" => "o",
            "\xc3\x93" => "O",
            "\xc5\x9b" => "s",
            "\xc5\x9a" => "S",
            "\xc5\xbc" => "z",
            "\xc5\xbb" => "Z",
            "\xc5\xba" => "z",
            "\xc5\xb9" => "Z",
            "\xc5\x84" => "n",
            "\xc5\x83" => "N",
            //ISO
            "\xb1" => "a",
            "\xa1" => "A",
            "\xe6" => "c",
            "\xc6" => "C",
            "\xea" => "e",
            "\xca" => "E",
            "\xb3" => "l",
            "\xa3" => "L",
            "\xf3" => "o",
            "\xd3" => "O",
            "\xb6" => "s",
            "\xa6" => "S",
            "\xbc" => "z",
            "\xac" => "Z",
            "\xbf" => "z",
            "\xaf" => "Z",
            "\xf1" => "n",
            "\xd1" => "N"
        );

        return strtr($tekst, $tabela);
    }
