<?php

include_once 'Db.class.php';
include_once 'funkcje.inc';
$db = new Db();

$komunikat = "OK";
$delivery_id = $_GET['delivery_id'];
$baza_danych = $_GET['baza_danych'];
$adres_ip_drukarki = $_GET['adres_ip_drukarki'];
$ilosc_etykiet_fizyczna = $_GET['ilosc_etykiet'];

$sql = 'SELECT d.id,d.dl_doc_ref,d.dl_doc_ref_klient , logo,firma,miasto,ulica, k.lokal,paleta_id,kkg.wydruk_etykiety_zamowienia_klietna FROM ' . $baza_danych . '.delivery d
left join ' . $baza_danych . '.kontrah k on d.dl_kontrah_id=k.id
    left join ' . $baza_danych . '.kontrah_grupa kkg on kkg.id=k.kontrah_grupa_id
left join ' . $baza_danych . '.delivery_et de on d.id=de.delivery_id
left join ' . $baza_danych . '.etykiety e on e.id=de.etykieta_id
    left join ' . $baza_danych . '.dlcollect dc on dc.nr_et=e.id
WHERE d.id=' . $delivery_id . ' 
group by paleta_id
order by e.paleta_id ASC,dc.czas asc
';

$result = $db->mGetResult($sql);
//$ile = mysql_num_rows($result);
//echo "<br>" . $sql . "<br>";
//exit();

if (empty($ilosc_etykiet_fizyczna)) {
    $ilosc_etykiet_fizyczna = count($result);
}

if (!is_numeric($ilosc_etykiet_fizyczna)) {
    exit();
}


if (count($result) > 0) {
    //$ilosc_etykiet = count($result);

    $k = 0;

    for ($i = 0; $i < $ilosc_etykiet_fizyczna; $i++) {
        //echo "<br>".$ilosc_etykiet."<br>";
        //$dl_doc_ref = $aRow['dl_doc_ref'];
        if (!empty($result[$i]['paleta_id'])) {
            $k = $i;
        }
        if ($i > 100) {
            exit();
        }



        $dl_doc_ref_klient = $result[$k]['dl_doc_ref_klient'];
        $logo = $result[$k]['logo'];
        //$dl_doc_ref = $aRow['dl_doc_ref'];
        if (($k % 3) == 0) { // zabezpieczenie, bo nie drukowało wszystkich etykiet
            sleep(1);
        }
        $opis = array(
            'wiersz1' => zamianapolskich_zpl($result[$k]['dl_doc_ref']),
            'wiersz2' => zamianapolskich_zpl($result[$k]['dl_doc_ref_klient']),
            'wiersz3' => zamianapolskich_zpl($result[$k]['firma']),
            'wiersz4' => zamianapolskich_zpl($result[$k]['miasto'] . " " . $result[$k]['ulica'] . " " . $result[$k]['lokal']),
            'wiersz5' => $result[$k]['id'],
            'wiersz6' => $result[$k]['paleta_id'],
            'licznik' => ($i + 1) . " z " . $ilosc_etykiet_fizyczna,
        );
//        echo "<pre>";
//        print_r($opis);
//        echo "</pre>";
        printlabelsmall_opis($opis, $adres_ip_drukarki);

        if ($result[$k]['wydruk_etykiety_zamowienia_klietna'] == 'T') {
            printlabelsmall_opis_dl_doc_ref_klient($opis, $adres_ip_drukarki);
        }

        $komunikat .= $db->errors;
    }





//$komunikat=$sql;
} else {
    $komunikat = "Brak ilości etykiet";
}

show_komunikat_xml($komunikat);

function printlabelsmall_opis($szukaj_etykiete, $adres_ip) {

//        $layout = '^XA'
//            //. '^FO80,80	^ADN,30,20	^FD NAKLEIC NA LISTE KONTROLNA: ^FS  '
//            . '^FO3,20	^ADN,50,30	^FD ' . $szukaj_etykiete['wiersz1'] . ' ^FS  '
//            . '^FO3,120	^ADN,50,30	^FD ' . $szukaj_etykiete['wiersz2'] . ' ^FS  '
//            . '^FO3,200	^A0N,50,30	^FD ' . $szukaj_etykiete['wiersz3'] . ' ^FS  '
//            . '^FO3,300	^A0N,50,30	^FD ' . $szukaj_etykiete['wiersz4'] . ' ^FS  '
//            . '^FO333,65^ADN,50,30^FD DS' . $szukaj_etykiete['wiersz6'] . '^FS  '
//    ;

    $layout = '^XA'
            //. '^FO80,80	^ADN,30,20	^FD NAKLEIC NA LISTE KONTROLNA: ^FS  '
            . '^FO3,20 ^A0N,60,60 ^FD  ' . $szukaj_etykiete['wiersz1'] . ' ^FS  '
            . '^FO3,120 ^A0N,50,50 ^FD ' . $szukaj_etykiete['wiersz2'] . ' ^FS  '
            . '^FO3,200 ^A0N,50,30 ^FD ' . $szukaj_etykiete['wiersz3'] . ' ^FS  '
            . '^FO3,300	^A0N,50,30	^FD ' . $szukaj_etykiete['wiersz4'] . ' ^FS  '
            . '^FO333,85^ADN,40,30^FD DS' . $szukaj_etykiete['wiersz6'] . '^FS  '
    ;
//    echo $layout;
//    exit();
    if (!empty($szukaj_etykiete['wiersz5'])) {
        $layout .= '^BY2,2,40 ^A2N,34,20 ^FO500,400^BC^FDDL' . $szukaj_etykiete['wiersz5'] . '^FS   ';
    }

    $layout .= '^FO40,400	^ADN,50,40	^FD ' . $szukaj_etykiete['licznik'] . ' ^FS  '

            //. '^FO140,180	^A0B,60,30	^FDLISTA KONT:^FS '
            //. '^FO120,250^BY3	 ^BCN,100,Y,N,N ^A0,60 ^FD' . $szukaj_etykiete['idetykiety'] . '^FS'
            //       .'^FO40,60 ^FB 400,,,C ^A0B,100,100 ^FD '.$params['nr_palety'].'/'.$params['nr_kartonu'].' ^FS' 
            //.'^FO180,145 ^FB 980,,,C ^A0B,120,180 ^FD '.$params['kod'].' ^FS' 
            . ' ^XZ';
    $file = fopen("/tmp/delivery_skaner_wysylka.zbr", "w");
    fputs($file, $layout);
    fclose($file);
    //flush();
    //sleep(1);
    //echo "<br>" . $layout . "<br>";

    $socket = socket_create(AF_INET, SOCK_STREAM, SOL_TCP);
    $result = socket_connect($socket, $adres_ip, "9100");
    socket_write($socket, $layout, strlen($layout));
    socket_close($socket);
}

function printlabelsmall_opis_dl_doc_ref_klient($szukaj_etykiete, $adres_ip) {


    $layout = '^XA^FO73,20 ^A0N,70,60 ^FD ' . $szukaj_etykiete['wiersz2'] . ' ^FS
                ^BY3,2,140 ^A2N,44,20 ^FO100,140^BC^FD' . $szukaj_etykiete['wiersz2'] . '^FS 
                ^FO40,400 ^ADN,50,40 ^FD ' . $szukaj_etykiete['licznik'] . ' ^FS ^XZ
                ';

//    echo $layout;
//    exit();
    $file = fopen("/tmp/delivery_skaner_wysylka3.zbr", "w");
    fputs($file, $layout);
    fclose($file);
    //flush();
    //sleep(1);
    //echo "<br>" . $layout . "<br>";

    $socket = socket_create(AF_INET, SOCK_STREAM, SOL_TCP);
    $result = socket_connect($socket, $adres_ip, "9100");
    socket_write($socket, $layout, strlen($layout));
    socket_close($socket);
}

?>