<?php

include_once 'Db.class.php';
include_once 'funkcje.inc';
$db = new Db();



$komunikat = "OK";
$akcja = $_GET['akcja'];
$zadanie_dane_id = $_GET['zadanie_dane_id'];
$imie_nazwisko = plznaki($_GET['imie_nazwisko']);
$skan = $_GET["skan"];
$czy_koniec_zadania = "NIE";
$miejsce_id = 0;
if (!empty($_GET["miejsce_id"])) {
    $miejsce_id = $_GET["miejsce_id"];
}





//header('Content-type: text/xml');
/* echo '<?xml version="1.0" encoding="utf-8" ?><dane>'; */

if (empty($akcja) || empty($zadanie_dane_id) || empty($imie_nazwisko) || empty($skan)) {
    $komunikat = "Brak wszystkich parametrow wejsciwych. Przerywam operacje";
    //echo '<komunikat>', htmlentities($komunikat), '</komunikat></dane>';
    return show_komunikat_xml($komunikat);
}


if ($akcja == "realizacja_zadania") {

    if (empty($ilosc)) {
        $ilosc = "0";
    }

    $sql = 'SELECT  zh.baza_danych AS baza_danych FROM zadania_dane zd
left join zadania_head  zh on zh.id=zd.zadanie_head_id
            where zd.id=' . $zadanie_dane_id . '  limit 1';

    $result10 = $db->mGetResultAsXML($sql);

    foreach ($result10 as $key => $value) {
        $baza_danych_local = $value["baza_danych"];
    }



    $sql = 'SELECT zd.id AS id,k.kod,
TRIM(TRAILING "." FROM TRIM(TRAILING "0" from zd.ilosc)) as ilosc ,
ifnull((select ee.ilosc from etykiety ee
where ee.paleta_id=zd.paleta_id and ee.kod_id=zd.kod_id and ee.active=1 limit 1 ),0)  as ilosc_rzeczywista,
if(zd.ilosc < ifnull((select ee.ilosc from etykiety ee
where ee.paleta_id=zd.paleta_id and ee.kod_id=zd.kod_id and ee.active=1 limit 1 ),0),"TAK","NIE") as czy_kompletowana,
if(zd.ilosc > ifnull((select ee.ilosc from etykiety ee
where ee.paleta_id=zd.paleta_id and ee.kod_id=zd.kod_id and ee.active=1 limit 1 ),0),"TAK","NIE") as czy_brak,

k.kod_nazwa,k.ean,k.ean_jednostki, k.ilosc_w_opakowaniu, 
(k.ilosc_w_opakowaniu*TRIM(TRAILING "." FROM TRIM(TRAILING "0" from zd.ilosc))) as ilosc_opakowan,
zd.zadanie_head_id AS zadanie_head_id,
zd.status AS status,
zs.status_nazwa AS status_nazwa,

zd.stare_m AS stare_m, zd.nowe_m AS nowe_m,
zd.nowe_m_realizowane AS nowe_m_realizowane,
zd.paleta_id AS paleta_id, zd.etykieta_id AS etykieta_id,
zd.kod_id AS kod_id, zd.lot AS lot,
zh.baza_danych AS baza_danych, zh.system_id AS system_id, zh.typ AS typ,
zh.doc_id AS doc_id, zh.doc_type AS doc_type,
zdt.nazwa AS doc_type_nazwa,
zh.zgodnosc_miejsca AS zgodnosc_miejsca, zh.zgodnosc_towaru AS zgodnosc_towaru,
zt.nazwa AS zadanie_typ_nazwa,
concat("H: ",m1.hala," ",m1.regal,"-",m1.miejsce,"-",m1.poziom ) AS stare_m_nazwa,
concat("H: ",m2.hala," ",m2.regal,"-",m2.miejsce,"-",m2.poziom ) AS nowe_m_nazwa,
m2.hala as nowe_hala,
m2.regal as nowe_regal,
m2.miejsce as nowe_miejsce,
m2.poziom as nowe_poziom,
s.nazwa AS system_id_nazwa,

         if(zh.baza_danych="obrama", (SELECT if(w.rampa is null,"",concat("Rampa ",w.rampa,"; ",w.nr_ciagnik,"/", w.nr_naczepa))
         FROM obrama.wjazdy w WHERE w.id=zh.doc_id limit 1),"") AS info,

  zd.czas_przydzielenia AS czas_przydzielenia,
zd.przydzielenie_pracownik_id AS przydzielenie_pracownik_id,
zd.start AS start, zd.stop AS stop,
zd.realizacja_pracownik_id AS realizacja_pracownik_id,
p2.imie_nazwisko AS realizacja_imie_nazwisko,

 zd.stanowisko_id AS stanowisko_id, zd.kierunek AS kierunek,zd.kompletacja,
 zh.priorytet AS priorytet,
 p.imie_nazwisko AS przydzielenie_imie_nazwisko,

        concat(if(zh.baza_danych="obrama",
        (SELECT w.numer_karty FROM obrama.wjazdy w WHERE w.id=zh.doc_id limit 1),"")) AS numer_karty,
         if(zh.baza_danych="obrama", (SELECT w.ilosc_palet FROM obrama.wjazdy w WHERE w.id=zh.doc_id limit 1),"") AS ilosc_jednostek


 FROM zadania_dane zd LEFT JOIN zadania_head AS zh ON zh.id=zd.zadanie_head_id LEFT JOIN zadania_doc_type AS zdt ON zh.doc_type=zdt.id
         LEFT JOIN zadania_typ AS zt ON zh.typ=zt.id 
LEFT JOIN ' . $baza_danych_local . '.miejsca AS m1 ON m1.id=zd.stare_m
LEFT JOIN ' . $baza_danych_local . '.miejsca AS m2 ON m2.id=zd.nowe_m
LEFT JOIN ' . $baza_danych_local . '.pracownicy AS p ON zd.przydzielenie_pracownik_id=p.id
LEFT JOIN ' . $baza_danych_local . '.pracownicy AS p2 ON zd.realizacja_pracownik_id=p2.id
LEFT JOIN ' . $baza_danych_local . '.systemy AS s ON s.wartosc=zh.system_id
LEFT JOIN ' . $baza_danych_local . '.kody k ON zd.kod_id=k.id
LEFT JOIN wmsgg.zadania_statusy  AS zs  ON zd.status=zs.id

            where zd.id=' . $zadanie_dane_id . ' and zd.status=5  ';
    $result = $db->mGetResultAsXML($sql);
    //$ile = mysql_num_rows($result);
    //echo "<br>" . $sql . "<br>";


    if (count($result) > 0) {
        foreach ($result as $index => $aRowZadanie) {
            $baza_danych = $aRowZadanie['baza_danych'];
            $system_id = $aRowZadanie['system_id'];

            if ($aRowZadanie['status'] != "5") {
                $komunikat = "Zadanie zostalo zrealizowane . Przerywam operacje";
                return show_komunikat_xml($komunikat);
            }






            $sql = 'SELECT id FROM ' . $baza_danych . '.pracownicy p WHERE p.imie_nazwisko="' . $imie_nazwisko . '" limit 1; ';
            $result2 = $db->mGetResultAsXML($sql);
            //echo "<br>" . $sql;
            foreach ($result2 as $index => $aRowp) {
                $pracownik_id = $aRowp['id'];
            }
            if (empty($pracownik_id)) {
                $komunikat = "Nie znaleziono pracownika: " . $imie_nazwisko . ". Przerywam operacje";
                return show_komunikat_xml($komunikat);
            }

            // najpotrzebniejsze etykieta ta sama i ilosc realizowana
            //$skan = "MP-1-RMP-1-A";
            $arr = explode("-", $skan);
            //
            if (empty($miejsce_id)) {
                if (substr($skan, 0, 2) == "MP") {


                    //echo "22";
                    $hala = $arr[1];
                    $regal = $arr[2];
                    $miejsce = $arr[3];

                    $poziomy = wyswietl_poziomy($baza_danych, $hala, $regal, $miejsce, $db);

////            echo "asd:" . count($poziomy);
////            xml_from_indexed_array($poziomy); 
//            return false;
                    if (count($poziomy) == 0) {
                        $komunikat = "Brak miejsc";
                        return show_komunikat_xml($komunikat);
                    }
                    echo "<pre>";
            print_r($poziomy);
            echo "</pre>";

                    $ile_zbiorek = 0;
                    foreach ($poziomy as $key => $value3) {
                        if ($value3['zbiorka'] == "1") {
                            $ile_zbiorek++;
                        }
                    }
                                echo "<pre>";
            print_r($ile_zbiorek);
            echo "</pre>";
            



                    if (count($poziomy) > 1 && $ile_zbiorek > 1) {
                        return xml_from_indexed_array(array('komunikat' => $komunikat, 'dodatkowa_akcja' => 'wybierz_poziom', 'poziomy' => $poziomy));
                    } else {
                        $miejsce_id = $poziomy[0]['id'];
                    }
                    print_r($miejsce_id);
                    exit();
                    
                } else {
                    $komunikat = "To nie jest miejsce";
                    return show_komunikat_xml($komunikat);
                }
            } else {



                // return;
//            echo "<pre>";
//            print_r($miejsce_id);
//            echo "</pre>";
//            return false;

                if ($komunikat == "OK") {

                    $sql = 'SELECT e.id,e.miejscep FROM ' . $baza_danych . '.etykiety e WHERE e.active=1 AND e.paleta_id=' . $aRowZadanie['paleta_id'];
                    $result7 = $db->mGetResultAsXML($sql);
                    foreach ($result7 as $index => $aRowEtykietaWMS) {

                        $sql = "insert into $baza_danych.zmianym 
                        (typ, doc_nr, pracownik_id, data, etykieta, system_id, stare_m, nowe_m, doc_internal, stat, tszm)
                        values('ZM',0, '$pracownik_id',CURDATE(), " . $aRowEtykietaWMS['id'] . "," . $aRowZadanie['system_id'] . "," . $aRowEtykietaWMS['miejscep'] . ", " . $miejsce_id . ", 'Z', 1, NOW())";
                        //echo "<br>" . $sql;
                        $result2 = $db->mGetResultAsXML($sql);

                        $sql = "update $baza_danych.etykiety set miejscep=" . $miejsce_id . " where id=" . $aRowEtykietaWMS['id'] . " and active =1 ";
                        $result2 = $db->mGetResultAsXML($sql);
                    }

                    $sql = ' update  zadania_dane z set z.realizacja_pracownik_id=' . $pracownik_id . ', z.stop=NOW(),status=9, nowe_m=' . $miejsce_id . ' WHERE z.id=' . $zadanie_dane_id;
                    //echo "<br>" . $sql;
                    $result = $db->mGetResultAsXML($sql);

                    $zadania_palety = sprawdz_w_jakich_zadaniach_jest_paleta($aRowZadanie['paleta_id'], $db);
                    foreach ($zadania_palety as $key => $value) {
                        zmien_status_zadaniom_palety($value['zadanie_head_id'], $value['paleta_id'], $miejsce_id, $db);
                        $sprawdzenie = sprawdz_czy_wszystkie_kompletowane_sa_dostepne_na_dole($value['zadanie_head_id'], $db);
                        if ($sprawdzenie == "TAK") {
                            mozna_rozpoczac_kompletacje($value['zadanie_head_id'], $db);
                        }
                    }

                    // dodawanie operaji
                    $operac_id = docnumber_increment($baza_danych, "operacja_id", $db);
                    $sql = "insert into operacje(ts_ins,etykieta_id, doc_type, doc_nr, imie_nazwisko, typ_operacji,system_id,wozek,operac_id,ilosc) values('" . $aRowZadanie['start'] . "','" . $aRowZadanie['etykieta_id'] . "','DL','" . $aRowZadanie['doc_id'] . "','" . $imie_nazwisko . "','DL_ZAD_ZDEJ','" . $aRowZadanie['system_id'] . "', '0','" . $operac_id . "','0');";
                    $result8 = $db->mGetResultAsXML($sql);

                    $sql = "insert into operacje(ts_ins,etykieta_id, doc_type, doc_nr, imie_nazwisko, typ_operacji,system_id,wozek,operac_id,ilosc) values(NOW(),'" . $aRowZadanie['etykieta_id'] . "','DL','" . $aRowZadanie['doc_id'] . "','" . $imie_nazwisko . "','DL_ZAD_ZDEJ','" . $aRowZadanie['system_id'] . "', '0','" . $operac_id . "','1');";
                    $result8 = $db->mGetResultAsXML($sql);

                    //sprawdzanie_wszystkich_zadan zwiazanych z tą paletą, czy_moga_rozpoczac_kompletacje($paleta_id);
                //

            }
            }

            $komunikat .= $db->errors;
        }


        // by ostatnia etykieta miała czas rampy rampę
//$komunikat=$sql;
    } else {
        $komunikat = "Brak zadan";
    }
} else {
    $komunikat = "Brak akcji realizacji!";
}
//show_komunikat_xml($komunikat);
xml_from_indexed_array(array(
    'komunikat' => $komunikat,
    'dodatkowa_akcja' => 'zrealizowane',
    'czy_koniec_zadania' => $czy_koniec_zadania)
);

function mozna_rozpoczac_kompletacje($zadanie_head_id, $db) {
    $sql_ins_zad = "update zadania_dane z set z.status=1,wysokie=0 WHERE status=17 and zadanie_head_id=" . $zadanie_head_id;
    //echo "<br>" . $sql_ins_zad;
    $result_zadanie4 = $db->mGetResult($sql_ins_zad);
}

function sprawdz_czy_wszystkie_kompletowane_sa_dostepne_na_dole($zadanie_head_id, $db) {
    $wynik = "NIE";
    $sql_ins_zad = "SELECT sum(if(z.status=17,1,0)) as kompletacja, sum(1) as wszystkie FROM zadania_dane z WHERE kompletacja=1 and z.stop is null and zadanie_head_id=" . $zadanie_head_id;
    //echo "<br>" . $sql_ins_zad;
    $result_zadanie4 = $db->mGetResult($sql_ins_zad);
    if (!empty($result_zadanie4)) {
        if ($result_zadanie4[0]['kompletacja'] == $result_zadanie4[0]['wszystkie']) {
            $wynik = "TAK";
        }
    }
    //print_r($result_zadanie4);
//    $wynik = empty($result_zadanie4) ? 0 : count($result_zadanie4[0]);
    return $wynik;
}

function zmien_status_zadaniom_palety($zadanie_head_id, $paleta_id, $nowe_m, $db) {

    $sql_ins_zad = "update zadania_dane z set status=17, stare_m=$nowe_m,wysokie=0
where paleta_id=$paleta_id and zadanie_head_id=$zadanie_head_id and z.stop is null
and z.status=10 ";
    //echo "<br>" . $sql_ins_zad;
    $result_zadanie4 = $db->mGetResult($sql_ins_zad);
}

function wyswietl_poziomy($baza_danych, $hala, $regal, $miejsce, $db) {
    $sql = 'SELECT m.id,m.poziom as nazwa_wyswietlana,m.zbiorka FROM ' . $baza_danych . '.miejsca m WHERE m.hala="' . $hala . '" AND m.regal="' . $regal . '" AND m.miejsce="' . $miejsce . '" AND m.widoczne=1 
ORDER BY m.poziom ASC;  ';
    //echo $sql;
    $result = $db->mGetResultAsXML($sql);
    return $result;
}

function sprawdz_w_jakich_zadaniach_jest_paleta($paleta_id, $db) {

    $sql_ins_zad = "SELECT zadanie_head_id,paleta_id FROM zadania_dane z
where paleta_id=$paleta_id
and status=10
group by paleta_id,zadanie_head_id ;";
    //echo "<br>" . $sql_ins_zad;
    $result_zadanie4 = $db->mGetResult($sql_ins_zad);
    return $result_zadanie4;
}

//echo sprawdz_czy_wszystkie_kompletowane_sa_dostepne_na_dole(7, $db);
//return;
//echo '<komunikat>', $komunikat, '</komunikat></dane>'; //htmlentities(    mb_convert_encoding($komunikat, "UTF-8","ISO-8859-2")
//
//echo $komunikat;
//echo '<ilosc_pozostala>', htmlentities($ilosc_pozostala), '</ilosc_pozostala>';
//echo '<nowa_etykieta>', htmlentities($nowa_etykieta), '</nowa_etykieta>';
//echo '<kod>', htmlentities($kod), '</kod>';
//echo '</dane>';
?>