# Lista zadań (TODO)

## Zadania związane z API

### Zak<PERSON>ńczone (100%)
- [x] Podstawowa implementacja API dla zarządzania osobami na liniach produkcyjnych
- [x] Zmiana formatu odpowiedzi z JSON na XML
- [x] Implementacja walidacji formatu czasu (HH:MM lub H:MM)
- [x] Stworzenie dokumentacji technicznej API

### Do wykonania (0%)
- [ ] Dodanie obsługi większej liczby akcji w API (np. podgląd deklaracji, edycja danych)
- [ ] Rozbudowa walidacji danych wejściowych (np. weryfikacja poprawności identyfikatora wyrobu)
- [ ] Implementacja autentykacji dla API
- [ ] Dodanie logowania błędów i zdarzeń
- [ ] Dodanie testów jednostkowych dla funkcji API
- [ ] Optymalizacja zapytań bazodanowych dla lepszej wydajności
- [ ] Rozszerzenie API o funkcje raportowania i statystyk

## Planowane usprawnienia

### Do wykonania (0%)
- [ ] Refaktoryzacja kodu w kierunku bardziej obiektowego modelu
- [ ] Wprowadzenie wstrzykiwania zależności (dependency injection)
- [ ] Dodanie obsługi wyjątków (try-catch) zamiast prostych warunków
- [ ] Utworzenie modelu warstwy DAO (Data Access Object) dla operacji bazodanowych
- [ ] Utworzenie oddzielnych kontrolerów dla różnych akcji API
- [ ] Poprawa bezpieczeństwa API (sanityzacja danych wejściowych, ochrona przed SQL Injection)

## Podsumowanie postępu
- Zadania związane z API: 40% (4/10 zadań)
- Planowane usprawnienia: 0% (0/6 zadań)
- Całkowity postęp: 25% (4/16 zadań)
